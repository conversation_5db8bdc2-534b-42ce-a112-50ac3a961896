{"groups": [{"name": "aes.encrypt", "type": "com.uino.x.gatewayx.config.SecretKeyConfig", "sourceType": "com.uino.x.gatewayx.config.SecretKeyConfig"}, {"name": "xhr.filter", "type": "com.uino.x.gatewayx.config.XHRtokenWhiteListConfig", "sourceType": "com.uino.x.gatewayx.config.XHRtokenWhiteListConfig"}], "properties": [{"name": "aes.encrypt.charset", "type": "java.lang.String", "sourceType": "com.uino.x.gatewayx.config.SecretKeyConfig"}, {"name": "aes.encrypt.double-check-public-key", "type": "java.lang.String", "sourceType": "com.uino.x.gatewayx.config.SecretKeyConfig"}, {"name": "aes.encrypt.double-check-time", "type": "java.lang.Long", "sourceType": "com.uino.x.gatewayx.config.SecretKeyConfig"}, {"name": "aes.encrypt.fresh-token", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.uino.x.gatewayx.config.SecretKeyConfig"}, {"name": "aes.encrypt.open", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.uino.x.gatewayx.config.SecretKeyConfig"}, {"name": "aes.encrypt.public-key", "type": "java.lang.String", "sourceType": "com.uino.x.gatewayx.config.SecretKeyConfig"}, {"name": "aes.encrypt.show-log", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.uino.x.gatewayx.config.SecretKeyConfig"}, {"name": "xhr.filter.token-white-list", "type": "java.util.List<java.lang.String>", "sourceType": "com.uino.x.gatewayx.config.XHRtokenWhiteListConfig"}], "hints": [], "ignored": {"properties": []}}