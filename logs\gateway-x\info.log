[1;35m11:14:54.801[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m11:14:55.052[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m11:14:55.055[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m11:14:55.159[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m11:14:55.176[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStarting,53][0;39m - Starting GatewayXApplication using Java 17.0.14 with PID 37952 (D:\x\gateway-x\gateway-x-server\target\classes started by Administrator in D:\x)
[1;35m11:14:55.183[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal-x"
[1;35m11:14:55.317[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m11:14:55.319[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m11:14:55.323[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=gateway-x.yml, group=DEFAULT_GROUP] success
[1;35m11:14:59.465[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'sentinelGatewayFilter' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=gatewayConfig; factoryMethodName=sentinelGatewayFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/gatewayx/config/GatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.sentinel.gateway.scg.SentinelSCGAutoConfiguration; factoryMethodName=sentinelGatewayFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/sentinel/gateway/scg/SentinelSCGAutoConfiguration.class]]
[1;35m11:14:59.977[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m11:14:59.986[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m11:15:00.149[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 119 ms. Found 0 Redis repository interfaces.
[1;35m11:15:00.973[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=a682fabe-27a3-3ecc-8e94-3197e1d58eef
[1;35m11:15:02.623[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m11:15:03.256[0;39m [32m[redisson-netty-3-5][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m11:15:03.284[0;39m [32m[redisson-netty-3-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m11:15:03.642[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m11:15:03.711[0;39m [32m[redisson-netty-6-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m11:15:03.790[0;39m [32m[redisson-netty-6-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m11:15:04.408[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayFilter,143][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
[1;35m11:15:06.594[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [After]
[1;35m11:15:06.595[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Before]
[1;35m11:15:06.596[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Between]
[1;35m11:15:06.596[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Cookie]
[1;35m11:15:06.596[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Header]
[1;35m11:15:06.597[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Host]
[1;35m11:15:06.597[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Method]
[1;35m11:15:06.597[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Path]
[1;35m11:15:06.597[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Query]
[1;35m11:15:06.598[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [ReadBody]
[1;35m11:15:06.598[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [RemoteAddr]
[1;35m11:15:06.598[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[1;35m11:15:06.599[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Weight]
[1;35m11:15:06.599[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [CloudFoundryRouteService]
[1;35m11:15:07.267[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 1 endpoint beneath base path '/actuator'
[1;35m11:15:07.688[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayBlockExceptionHandler,133][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
[1;35m11:15:07.883[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m11:15:07.935[0;39m [32m[redisson-netty-11-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m11:15:07.971[0;39m [32m[redisson-netty-11-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m11:15:08.727[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.NacosConfigService[0;39m - [36m[<init>,78][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal-x
	username=nacos
	password=Xq*******os

[1;35m11:15:08.730[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,48][0;39m - DefaultLabelsCollectorManager get labels.....
[1;35m11:15:08.730[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,62][0;39m - Process LabelsCollector with [name:defaultNacosLabelsCollector]
[1;35m11:15:08.731[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,64][0;39m - default nacos collect properties raw labels: null
[1;35m11:15:08.731[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,71][0;39m - default nacos collect properties labels: {}
[1;35m11:15:08.731[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,74][0;39m - default nacos collect jvm raw labels: null
[1;35m11:15:08.732[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,80][0;39m - default nacos collect jvm labels: {}
[1;35m11:15:08.732[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,83][0;39m - default nacos collect env raw labels: null
[1;35m11:15:08.732[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,91][0;39m - default nacos collect env labels: {}
[1;35m11:15:08.732[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,50][0;39m - DefaultLabelsCollectorManager get labels finished,labels :{}
[1;35m11:15:08.733[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m11:15:08.733[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m11:15:09.043[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[initNotifyWarnTimeout,72][0;39m - config listener notify warn timeout millis use default 60000 millis 
[1;35m11:15:09.044[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[<clinit>,99][0;39m - nacos.cache.data.init.snapshot = true 
[1;35m11:15:09.061[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-x-***********_8848] [subscribe] gateway-x-sentinel.json+DEFAULT_GROUP+pedestal-x
[1;35m11:15:09.080[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-x-***********_8848] [add-listener] ok, tenant=pedestal-x, dataId=gateway-x-sentinel.json, group=DEFAULT_GROUP, cnt=1
[1;35m11:15:09.081[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of 5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0
[1;35m11:15:09.081[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$340/0x000002d8e62d6ac0
[1;35m11:15:09.082[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$341/0x000002d8e62d6ef0
[1;35m11:15:09.082[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
[1;35m11:15:09.083[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
[1;35m11:15:09.083[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m11:15:09.084[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m11:15:09.103[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0] Success to connect to server [***********:8848] on start up, connectionId = 1756177677351_**********_59336
[1;35m11:15:09.104[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m11:15:09.104[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0] Notify connected event to listeners.
[1;35m11:15:09.104[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$359/0x000002d8e6418450
[1;35m11:15:09.104[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onConnected,713][0;39m - [5eeb95be-5851-4f10-9ce4-f3461996fce5_config-0] Connected,notify listen context...
[1;35m11:15:09.236[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.n.NettyWebServer[0;39m - [36m[start,126][0;39m - Netty started on port 10001 (http)
[1;35m11:15:09.601[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[init,102][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal
	username=nacos
	password=Xq*******os

[1;35m11:15:09.602[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[initNamespaceForNaming,62][0;39m - initializer namespace from ans.namespace attribute : null
[1;35m11:15:09.603[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$0,66][0;39m - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[1;35m11:15:09.603[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$1,73][0;39m - initializer namespace from namespace attribute :null
[1;35m11:15:09.612[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,75][0;39m - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[1;35m11:15:09.618[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m11:15:09.618[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m11:15:09.738[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of bc8dcfeb-4d7c-47b8-8928-c995590e09e7
[1;35m11:15:09.742[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,109][0;39m - Create naming rpc client for uuid->bc8dcfeb-4d7c-47b8-8928-c995590e09e7
[1;35m11:15:09.742[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [bc8dcfeb-4d7c-47b8-8928-c995590e09e7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[1;35m11:15:09.742[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [bc8dcfeb-4d7c-47b8-8928-c995590e09e7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[1;35m11:15:09.743[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [bc8dcfeb-4d7c-47b8-8928-c995590e09e7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[1;35m11:15:09.744[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [bc8dcfeb-4d7c-47b8-8928-c995590e09e7] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m11:15:09.745[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m11:15:09.762[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [bc8dcfeb-4d7c-47b8-8928-c995590e09e7] Success to connect to server [***********:8848] on start up, connectionId = 1756177678013_**********_59341
[1;35m11:15:09.763[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [bc8dcfeb-4d7c-47b8-8928-c995590e09e7] Notify connected event to listeners.
[1;35m11:15:09.763[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [bc8dcfeb-4d7c-47b8-8928-c995590e09e7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m11:15:09.763[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m11:15:09.763[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [bc8dcfeb-4d7c-47b8-8928-c995590e09e7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$359/0x000002d8e6418450
[1;35m11:15:09.765[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[registerService,133][0;39m - [REGISTER-SERVICE] pedestal registering service gateway-x with instance Instance{instanceId='null', ip='**********', port=10001, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[1;35m11:15:09.777[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[register,76][0;39m - nacos registry, DEFAULT_GROUP gateway-x **********:10001 register finished
[1;35m11:15:09.887[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:gateway-x, group:DEFAULT_GROUP, clusters: 
[1;35m11:15:09.887[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:gateway-x, group:DEFAULT_GROUP, clusters: 
[1;35m11:15:09.887[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:gateway-x, group:DEFAULT_GROUP, cluster: 
[1;35m11:15:09.887[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:gateway-x, group:DEFAULT_GROUP, cluster: 
[1;35m11:15:09.906[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"**********#10001##DEFAULT_GROUP@@gateway-x","ip":"**********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m11:15:09.906[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"**********#10001##DEFAULT_GROUP@@gateway-x","ip":"**********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m11:15:09.907[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"**********#10001##DEFAULT_GROUP@@gateway-x","ip":"**********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m11:15:09.907[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"**********#10001##DEFAULT_GROUP@@gateway-x","ip":"**********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m11:15:10.061[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStarted,59][0;39m - Started GatewayXApplication in 19.288 seconds (process running for 21.277)
[1;35m11:15:10.089[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-x-***********_8848] [subscribe] gateway-x.yml+DEFAULT_GROUP+pedestal-x
[1;35m11:15:10.089[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-x-***********_8848] [add-listener] ok, tenant=pedestal-x, dataId=gateway-x.yml, group=DEFAULT_GROUP, cnt=1
[1;35m11:15:10.090[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=gateway-x.yml, group=DEFAULT_GROUP
[1;35m11:15:10.105[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-x-***********_8848] [subscribe] server-config.yml+DEFAULT_GROUP+pedestal-x
[1;35m11:15:10.106[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-x-***********_8848] [add-listener] ok, tenant=pedestal-x, dataId=server-config.yml, group=DEFAULT_GROUP, cnt=1
[1;35m11:15:10.106[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-config.yml, group=DEFAULT_GROUP
[1;35m11:15:10.121[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-x-***********_8848] [subscribe] server-info.yml+DEFAULT_GROUP+pedestal-x
[1;35m11:15:10.122[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-x-***********_8848] [add-listener] ok, tenant=pedestal-x, dataId=server-info.yml, group=DEFAULT_GROUP, cnt=1
[1;35m11:15:10.122[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-info.yml, group=DEFAULT_GROUP
[1;35m11:15:10.336[0;39m [32m[nacos-grpc-client-executor-***********-10][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [bc8dcfeb-4d7c-47b8-8928-c995590e09e7] Receive server push request, request = NotifySubscriberRequest, requestId = 6514
[1;35m11:15:10.337[0;39m [32m[nacos-grpc-client-executor-***********-10][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [bc8dcfeb-4d7c-47b8-8928-c995590e09e7] Ack server push request, request = NotifySubscriberRequest, requestId = 6514
