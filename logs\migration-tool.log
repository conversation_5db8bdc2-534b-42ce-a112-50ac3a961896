2025-08-26 09:24:54.295 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.301 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-26 09:24:54.542 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.547 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-26 09:24:54.661 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.662 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-26 09:24:54.722 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-26 09:24:54.772 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 10 transformation strategies in engine [MySQL-to-达梦]: [CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, BacktickQuoteTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, DataTypeTransformationStrategy, AlterTableCommentTransformationStrategy]
2025-08-26 09:24:54.773 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 10 strategies
2025-08-26 09:24:54.774 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-26 09:24:54.774 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-26 09:24:54.834 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.867 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-26 09:24:54.901 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.902 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-26 09:24:54.902 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-26 09:24:54.904 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.905 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-26 09:24:54.906 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.906 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 3 个配置
2025-08-26 09:24:54.908 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.908 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-26 09:24:54.909 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.911 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.912 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-26 09:24:54.923 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.925 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.932 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.934 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:24:54.935 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-26 09:24:56.772 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-26 09:25:02.181 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 40mysql-31达梦
2025-08-26 09:25:02.185 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:25:02.190 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 源数据库配置已设置: host=***********, database=sys
2025-08-26 09:25:02.195 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 目标数据库配置已设置: host=***********, database=SYSDBA
2025-08-26 09:25:02.195 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已应用数据库配置: 40mysql-31达梦
2025-08-26 09:25:13.095 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:25:13.635 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 成功加载 290 个源数据库Schema
2025-08-26 09:25:15.034 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 成功加载源数据库Schema，共290个
2025-08-26 09:25:25.306 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 09:25:25.306 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.ui.MigrationPanel - 使用源数据库配置: host=***********, port=3307, database=bjdyk_scenex, username=root
2025-08-26 09:25:25.354 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 开始导出Schema: bjdyk_scenex (不转换Schema) 到路径: C:\Users\<USER>\Desktop\data-export
2025-08-26 09:25:25.354 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 导出所有表和视图
2025-08-26 09:25:25.357 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 一键迁移过滤表信息: 排除表 [flyway_schema_history] 不进行常规迁移
2025-08-26 09:25:25.370 [SwingWorker-pool-2-thread-2] INFO  c.u.x.common.core.factory.ExecutorServiceFactory - sql-utils executor is create!
2025-08-26 09:25:26.344 [SwingWorker-pool-2-thread-2] INFO  c.u.x.common.core.factory.ExecutorServiceFactory - async-utils executor is create!
2025-08-26 09:25:26.371 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=effect_package, length=818, columns=16, hasMetadata=true, sql=CREATE TABLE `effect_package` (
  `id` BIGINT UNSIGNED NOT NULL COMMENT '主键',
  `name` VARCHAR(32) NOT NULL COMMENT '名称',
  `code` VARCHAR(64) COMMENT '编码',
  `parent_id` BIGINT COMMENT '父级id',
  `kind` TINYINT NOT NULL COMMENT '类型',
  `version` INT COMMENT '版本',
  `enable` TINYINT COMMENT '是否使用',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `remark` VARCHAR(128) COMMENT '备注',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人',
  `snap_shot` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '缩略图名称',
  `is_bundle` TINYINT,
  `default_type` TINYINT COMMENT '是否默认'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='效果包表'
2025-08-26 09:25:26.372 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=effect_package_use, length=513, columns=7, hasMetadata=true, sql=CREATE TABLE `effect_package_use` (
  `effect_package_code` VARCHAR(64) NOT NULL COMMENT '效果包编码',
  `scene_code` VARCHAR(50) NOT NULL COMMENT '场景编码',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`scene_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='效果包使用表'
2025-08-26 09:25:26.373 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=map_level, length=843, columns=12, hasMetadata=true, sql=CREATE TABLE `map_level` (
  `id` BIGINT NOT NULL COMMENT 'ID',
  `name` VARCHAR(50) NOT NULL COMMENT '名称',
  `code` VARCHAR(100) NOT NULL COMMENT '唯一编码',
  `pid` BIGINT NOT NULL COMMENT '父级ID',
  `pids` VARCHAR(255) NOT NULL COMMENT '父级ID列表',
  `sort` INT COMMENT '排序',
  `remark` TEXT COMMENT '备注',
  `status` INT NOT NULL COMMENT '状态',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地图层级表';
CREATE INDEX `map_level_code` ON `map_level` (`code`);
CREATE INDEX `map_level_pids` ON `map_level` (`pids`);
CREATE INDEX `map_level_pid` ON `map_level` (`pid`);
CREATE INDEX `map_level_name` ON `map_level` (`name`)
2025-08-26 09:25:26.373 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=map_source, length=726, columns=12, hasMetadata=true, sql=CREATE TABLE `map_source` (
  `id` BIGINT NOT NULL COMMENT '地图ID',
  `name` VARCHAR(255) COMMENT '地图源名称',
  `coords` VARCHAR(255) COMMENT '坐标系标识 GCJ02 为 0 WGS84 为 1',
  `type` TINYINT COMMENT '地图类型（0-底图；1-正射影像）',
  `tiles_url` VARCHAR(255) COMMENT '地图源URL',
  `is_default` TINYINT NOT NULL DEFAULT 0 COMMENT '是否默认地图源',
  `default_cam_info` VARCHAR(255) COMMENT '地图源默认视角',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地图来源表'
2025-08-26 09:25:26.375 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=projection_layer, length=627, columns=10, hasMetadata=true, sql=CREATE TABLE `projection_layer` (
  `id` BIGINT UNSIGNED NOT NULL COMMENT '主键',
  `name` VARCHAR(64) COMMENT '图层名称',
  `coords` VARCHAR(32) COMMENT '坐标系标识 GCJ02 WGS84',
  `url` VARCHAR(255) COMMENT '地图源URL',
  `height` DECIMAL(20,2) NOT NULL DEFAULT 0.00 COMMENT '离地高度',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='倾斜射影图层表'
2025-08-26 09:25:26.376 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=scene_data, length=1248, columns=24, hasMetadata=true, sql=CREATE TABLE `scene_data` (
  `uuid` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `campus_builder_id` VARCHAR(128) COMMENT '场景文件里面的id',
  `name` VARCHAR(50) COMMENT '场景文件里面的name',
  `setting_name` VARCHAR(50) COMMENT '用户设置的name',
  `user_id` VARCHAR(128) COMMENT '场景文件里面的user_id',
  `properties` JSON,
  `parent_scene_record_uuid` BIGINT COMMENT '所属场景UUID，外键',
  `default_camInfo` VARCHAR(255) COMMENT '默认视角',
  `config_camInfo` VARCHAR(255) COMMENT '用户设置视角',
  `data_type` VARCHAR(30),
  `belong_room` VARCHAR(50) COMMENT 'placements所属房间的campusbuilderID',
  `holescorners` TEXT COMMENT '洞的角落点',
  `position` LONGTEXT COMMENT '点位所在位置',
  `corners` LONGTEXT COMMENT '角落',
  `parent_cbid` VARCHAR(50),
  `children_scene_record_uuid` BIGINT COMMENT '子场景uuid',
  `extra_properties` VARCHAR(255) COMMENT '扩展属性',
  `facades` TEXT COMMENT '外立面',
  `facades_camInfo` TEXT COMMENT '外立面视角',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场景数据表'
2025-08-26 09:25:26.376 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=scene_model, length=540, columns=8, hasMetadata=true, sql=CREATE TABLE `scene_model` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `scene_code` VARCHAR(50) NOT NULL COMMENT '所属场景code',
  `model_id` VARCHAR(100) NOT NULL COMMENT '模型id',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场景&模型中间关联表'
2025-08-26 09:25:26.377 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=scene_placements_twin, length=730, columns=11, hasMetadata=true, sql=CREATE TABLE `scene_placements_twin` (
  `scene_data_id` BIGINT NOT NULL COMMENT '场景数据id',
  `twin_class_code` VARCHAR(255) NOT NULL COMMENT '孪生分类code',
  `scene_uuid` BIGINT COMMENT '物体所属场景',
  `properties_mapping` JSON NOT NULL COMMENT '物体属性和孪生体对象属性映射',
  `properties_where` JSON COMMENT '前端查询条件',
  `twin_data_uuid` BIGINT NOT NULL COMMENT '孪生对象数据uuid',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`scene_data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场景物体和孪生体映射关系表'
2025-08-26 09:25:26.377 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=scene_record, length=1097, columns=20, hasMetadata=true, sql=CREATE TABLE `scene_record` (
  `uuid` BIGINT UNSIGNED NOT NULL COMMENT '主键',
  `cb_scene_id` VARCHAR(50) COMMENT 'scene.json中的sceneID',
  `scene_code` VARCHAR(50) COMMENT '场景编码',
  `project_id` INT COMMENT '主场景',
  `name` VARCHAR(50) COMMENT '场景名，用户上传时指定的场景名',
  `default_name` VARCHAR(50) COMMENT '默认的场景名，也就是scene.json中的文件名',
  `version` INT COMMENT '版本',
  `default_camInfo` VARCHAR(255) COMMENT '默认视角',
  `config_camInfo` VARCHAR(255) COMMENT '用户设置视角',
  `extra_properties` VARCHAR(255) COMMENT '扩展属性',
  `enable` TINYINT COMMENT '1->使用中，2->未使用',
  `map_info` VARCHAR(255) COMMENT '点位信息',
  `parent_uuid` BIGINT COMMENT '子场景归属主场景id，当为主场景时，本栏位未空',
  `tjs_version` VARCHAR(50) COMMENT 'tjs版本号',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人',
  `translate_position` TEXT COMMENT '位置校准'
,  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场景记录表'
2025-08-26 09:25:26.377 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=scene_texture, length=633, columns=8, hasMetadata=true, sql=CREATE TABLE `scene_texture` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `scene_code` VARCHAR(50) NOT NULL COMMENT '场景code',
  `texture_id` VARCHAR(100) NOT NULL COMMENT '贴图Id',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场景关联贴图资源表';
CREATE UNIQUE INDEX `IDX_SCENE_TEXTURE` ON `scene_texture` (`scene_code`, `texture_id`)
2025-08-26 09:25:26.378 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=texture_resource, length=641, columns=10, hasMetadata=true, sql=CREATE TABLE `texture_resource` (
  `texture_id` VARCHAR(100) NOT NULL COMMENT '贴图Id',
  `extend_name` VARCHAR(10) COMMENT '文件扩展名',
  `file_exist` TINYINT COMMENT '资源是否存在(不存在：1；存在：2)',
  `file_size` BIGINT DEFAULT 0 COMMENT '文件大小（单位：字节）',
  `upload_time` DATETIME COMMENT '上传时间',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`texture_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='贴图资源'
2025-08-26 09:25:26.378 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=things_model, length=1017, columns=17, hasMetadata=true, sql=CREATE TABLE `things_model` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '编号',
  `model_id` VARCHAR(100) COMMENT '模型编号',
  `model_type` TINYINT NOT NULL COMMENT '模型分类（基础模型：1；用户模型：2）',
  `type` VARCHAR(30) COMMENT '类型',
  `title` VARCHAR(50) COMMENT '模型名',
  `version` VARCHAR(20) COMMENT '版本',
  `size` VARCHAR(128) COMMENT '尺寸',
  `tags` TEXT COMMENT '标签',
  `dir_flag` TINYINT DEFAULT 1 COMMENT '是否为目录节点(1：默认叶子，2：目录)',
  `file_exist` TINYINT DEFAULT 1 COMMENT '模型是否存在(不存在：1；存在：2)',
  `file_size` BIGINT DEFAULT 0 COMMENT '文件大小（单位：字节）',
  `classify` VARCHAR(200) COMMENT '模型分类全路径(以斜杠进行分隔)',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物模型表';
CREATE UNIQUE INDEX `model_id` ON `things_model` (`model_id`)
2025-08-26 09:25:26.378 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=topographic_data, length=625, columns=10, hasMetadata=true, sql=CREATE TABLE `topographic_data` (
  `id` BIGINT UNSIGNED NOT NULL COMMENT '主键',
  `name` VARCHAR(64) COMMENT '地图名称',
  `coords` VARCHAR(32) COMMENT '坐标系标识 GCJ02 WGS84',
  `url` VARCHAR(255) COMMENT '地图源URL',
  `is_default` TINYINT NOT NULL DEFAULT 0 COMMENT '是否默认地图源',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地形数据管理表'
2025-08-26 09:25:26.378 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_all, length=499, columns=7, hasMetadata=true, sql=CREATE TABLE `twin_all` (
  `twin_uuid` BIGINT NOT NULL COMMENT '孪生体唯一id',
  `twin_code` VARCHAR(255) NOT NULL COMMENT '孪生体所属code',
  `UniqueCode` VARCHAR(255) COMMENT '孪生体数据唯一编码',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` VARCHAR(127) COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` VARCHAR(127) COMMENT '更新人'
,  PRIMARY KEY (`twin_code`, `twin_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生体总表'
2025-08-26 09:25:26.378 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_building, length=549, columns=9, hasMetadata=true, sql=CREATE TABLE `twin_building` (
  `uuid` BIGINT NOT NULL COMMENT '唯一标识',
  `scene_code` VARCHAR(127) COMMENT '场景编码',
  `UniqueCode` VARCHAR(255) COMMENT '孪生体编码',
  `user_id` VARCHAR(127) COMMENT '场景点位userId',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` VARCHAR(127) COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` VARCHAR(127) COMMENT '更新人',
  `parent_cbid` VARCHAR(50) COMMENT '父级id'
,  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='建筑'
2025-08-26 09:25:26.379 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_class, length=1354, columns=18, hasMetadata=true, sql=CREATE TABLE `twin_class` (
  `id` BIGINT NOT NULL COMMENT 'ID',
  `name` VARCHAR(255) NOT NULL COMMENT '名称',
  `code` VARCHAR(255) NOT NULL COMMENT '编码',
  `group_id` BIGINT NOT NULL COMMENT '分组ID',
  `level` VARCHAR(255) NOT NULL COMMENT '所在层级',
  `data_type` VARCHAR(255) NOT NULL COMMENT '数据类型',
  `custom_color` VARCHAR(255) COMMENT '自定义颜色',
  `bubble_info_id` BIGINT COMMENT '气泡ID',
  `things_model_id` BIGINT COMMENT '模型ID',
  `things_model_uuid` VARCHAR(255) COMMENT '模型UUID',
  `status` INT COMMENT '状态',
  `form` LONGTEXT COMMENT '表单信息(json数据)',
  `structure` LONGTEXT COMMENT '表结构',
  `pure` INT DEFAULT 0 COMMENT '单独管理的孪生对象(1是 0否, 默认0)',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生对象表';
CREATE INDEX `twin_class_things_model_uuid` ON `twin_class` (`things_model_uuid`);
CREATE INDEX `twin_class_bubble_info_id` ON `twin_class` (`bubble_info_id`);
CREATE INDEX `twin_class_group_id` ON `twin_class` (`group_id`);
CREATE INDEX `twin_class_code` ON `twin_class` (`code`);
CREATE INDEX `twin_class_name` ON `twin_class` (`name`);
CREATE INDEX `twin_class_things_model_id` ON `twin_class` (`things_model_id`)
2025-08-26 09:25:26.379 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_class_group, length=763, columns=10, hasMetadata=true, sql=CREATE TABLE `twin_class_group` (
  `id` BIGINT NOT NULL COMMENT 'ID',
  `name` VARCHAR(255) NOT NULL COMMENT '名称',
  `pid` BIGINT NOT NULL COMMENT '父级ID',
  `pids` VARCHAR(512) NOT NULL COMMENT '父级ID列表',
  `status` INT NOT NULL COMMENT '状态',
  `sort` INT COMMENT '排序',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生对象分组表';
CREATE INDEX `twin_class_group_name` ON `twin_class_group` (`name`);
CREATE INDEX `twin_class_group_pid` ON `twin_class_group` (`pid`);
CREATE INDEX `twin_class_group_sort` ON `twin_class_group` (`sort`)
2025-08-26 09:25:26.379 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_class_model_mapping, length=890, columns=15, hasMetadata=true, sql=CREATE TABLE `twin_class_model_mapping` (
  `id` BIGINT NOT NULL COMMENT 'ID',
  `twin_class_id` BIGINT NOT NULL COMMENT '孪生体分类ID',
  `twin_class_code` VARCHAR(255) NOT NULL COMMENT '孪生体分类编码',
  `field_name` VARCHAR(100) NOT NULL COMMENT '字段名',
  `condition` VARCHAR(100) NOT NULL COMMENT '条件',
  `content` VARCHAR(255) COMMENT '内容',
  `order_num` BIGINT NOT NULL COMMENT '排序',
  `things_model_uuid` VARCHAR(255) NOT NULL COMMENT '模型UUID',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人',
  `bubble_info_id` BIGINT COMMENT '气泡ID',
  `bubble_info_name` VARCHAR(255) COMMENT '气泡名称'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生对象模型映射表'
2025-08-26 09:25:26.380 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_class_relation, length=733, columns=12, hasMetadata=true, sql=CREATE TABLE `twin_class_relation` (
  `id` BIGINT NOT NULL COMMENT 'ID',
  `name` VARCHAR(255) NOT NULL COMMENT '规则名称',
  `source_id` BIGINT NOT NULL COMMENT '源主键',
  `target_id` BIGINT NOT NULL COMMENT '目标主键',
  `source_field_code` VARCHAR(100) NOT NULL COMMENT '源字段code',
  `target_field_code` VARCHAR(100) NOT NULL COMMENT '目标字段code',
  `rule` VARCHAR(100) COMMENT '规则',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关联孪生体的条件表'
2025-08-26 09:25:26.380 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_coalesce, length=582, columns=10, hasMetadata=true, sql=CREATE TABLE `twin_coalesce` (
  `id` BIGINT NOT NULL COMMENT '主键',
  `name` VARCHAR(100) COMMENT '孪生融合视图名称',
  `code` VARCHAR(255) COMMENT '孪生融合视图编码',
  `remark` LONGTEXT COMMENT '孪生融合视图备注',
  `form` LONGTEXT COMMENT '表单json',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1冻结 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生融合表'
2025-08-26 09:25:26.380 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_coalesce_map, length=626, columns=10, hasMetadata=true, sql=CREATE TABLE `twin_coalesce_map` (
  `id` BIGINT NOT NULL COMMENT '主键',
  `twin_coalesce_id` BIGINT COMMENT '孪生融合视图id',
  `twin_code` VARCHAR(255) COMMENT '孪生体编码',
  `twin_coalesce_model` VARCHAR(255) COMMENT '孪生融合model',
  `twin_model` VARCHAR(255) COMMENT '孪生体Model',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1冻结 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生融合映射表'
2025-08-26 09:25:26.380 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_external_server, length=845, columns=15, hasMetadata=true, sql=CREATE TABLE `twin_external_server` (
  `id` BIGINT NOT NULL COMMENT 'ID',
  `name` VARCHAR(255) COMMENT '服务名称',
  `http_method` VARCHAR(10) COMMENT '请求方式（GET，POST）',
  `request_param` VARCHAR(2048) COMMENT '请求参数',
  `request_body` VARCHAR(2048) COMMENT '请求体',
  `request_header` VARCHAR(2048) COMMENT '请求头',
  `request_url` VARCHAR(2048) COMMENT '请求地址',
  `response_param_example` VARCHAR(2048) COMMENT '响应参数示例',
  `response_code_explain` VARCHAR(2048) COMMENT '响应代码解释',
  `response_param` VARCHAR(2048) COMMENT '响应参数',
  `status` INT NOT NULL COMMENT '状态',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生服务表'
2025-08-26 09:25:26.380 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_external_server_log, length=350, columns=5, hasMetadata=true, sql=CREATE TABLE `twin_external_server_log` (
  `id` BIGINT NOT NULL COMMENT 'ID',
  `server_id` BIGINT COMMENT '关联服务id',
  `status` INT NOT NULL COMMENT '状态（0 成功 1失败）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生服务表日志表'
2025-08-26 09:25:26.380 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_floor, length=546, columns=9, hasMetadata=true, sql=CREATE TABLE `twin_floor` (
  `uuid` BIGINT NOT NULL COMMENT '唯一标识',
  `scene_code` VARCHAR(255) COMMENT '场景编码',
  `UniqueCode` VARCHAR(255) COMMENT '孪生体编码',
  `user_id` VARCHAR(127) COMMENT '场景点位userId',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` VARCHAR(127) COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` VARCHAR(127) COMMENT '更新人',
  `parent_cbid` VARCHAR(50) COMMENT '父级id'
,  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='楼层'
2025-08-26 09:25:26.381 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_legend, length=449, columns=7, hasMetadata=true, sql=CREATE TABLE `twin_legend` (
  `uuid` BIGINT NOT NULL COMMENT '唯一标识',
  `legend_id` BIGINT COMMENT '图例ID',
  `UniqueCode` VARCHAR(255) COMMENT '孪生体编码',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` VARCHAR(127) COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` VARCHAR(127) COMMENT '更新人'
,  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图例'
2025-08-26 09:25:26.381 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_map_level, length=639, columns=12, hasMetadata=true, sql=CREATE TABLE `twin_map_level` (
  `uuid` BIGINT NOT NULL COMMENT '唯一标识',
  `map_level_id` BIGINT COMMENT '地图层级ID',
  `UniqueCode` VARCHAR(255) COMMENT '孪生体编码',
  `level` VARCHAR(255) COMMENT 'level',
  `name` VARCHAR(255) COMMENT '名称',
  `view` VARCHAR(255) COMMENT '视角',
  `json` TEXT COMMENT 'Json',
  `height` VARCHAR(255) COMMENT '高度',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` VARCHAR(127) COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` VARCHAR(127) COMMENT '更新人'
,  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地图层级'
2025-08-26 09:25:26.381 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_map_vector_data, length=573, columns=10, hasMetadata=true, sql=CREATE TABLE `twin_map_vector_data` (
  `uuid` BIGINT NOT NULL COMMENT '唯一标识',
  `map_level_id` BIGINT COMMENT '地图层级ID',
  `UniqueCode` VARCHAR(255) COMMENT '孪生体编码',
  `name` VARCHAR(255) COMMENT '名称',
  `level` VARCHAR(255) COMMENT 'level',
  `json` TEXT COMMENT 'Json文件',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` VARCHAR(127) COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` VARCHAR(127) COMMENT '更新人'
,  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地图层级'
2025-08-26 09:25:26.382 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_menu, length=481, columns=8, hasMetadata=true, sql=CREATE TABLE `twin_menu` (
  `uuid` BIGINT NOT NULL COMMENT '唯一标识',
  `menu_id` BIGINT COMMENT '菜单ID',
  `UniqueCode` VARCHAR(255) COMMENT '孪生体编码',
  `view` VARCHAR(255) COMMENT '视角',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` VARCHAR(127) COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` VARCHAR(127) COMMENT '更新人'
,  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单'
2025-08-26 09:25:26.382 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_park, length=545, columns=9, hasMetadata=true, sql=CREATE TABLE `twin_park` (
  `uuid` BIGINT NOT NULL COMMENT '唯一标识',
  `scene_code` VARCHAR(255) COMMENT '场景编码',
  `UniqueCode` VARCHAR(255) COMMENT '孪生体编码',
  `user_id` VARCHAR(127) COMMENT '场景点位userId',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` VARCHAR(127) COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` VARCHAR(127) COMMENT '更新人',
  `parent_cbid` VARCHAR(50) COMMENT '父级id'
,  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='园区'
2025-08-26 09:25:26.382 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_permission, length=642, columns=13, hasMetadata=true, sql=CREATE TABLE `twin_permission` (
  `id` BIGINT UNSIGNED NOT NULL,
  `title` VARCHAR(255) COMMENT '孪生体名称',
  `role_id` BIGINT COMMENT '角色id',
  `twin_code` VARCHAR(255) COMMENT '孪生体code',
  `permission` VARCHAR(255) COMMENT '权限内容',
  `status` INT COMMENT '状态',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人',
  `read_column` TEXT COMMENT '可读字段',
  `add_column` TEXT COMMENT '可增加字段',
  `edit_column` TEXT COMMENT '可编辑字段'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
2025-08-26 09:25:26.382 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_property, length=1217, columns=18, hasMetadata=true, sql=CREATE TABLE `twin_property` (
  `id` BIGINT NOT NULL COMMENT 'ID',
  `twin_class_id` BIGINT NOT NULL COMMENT '孪生对象ID',
  `twin_class_code` VARCHAR(255) NOT NULL COMMENT '孪生对象编码',
  `query_url` VARCHAR(2048) NOT NULL COMMENT '请求地址',
  `query_http_method` VARCHAR(10) NOT NULL COMMENT '请求方式（GET，POST）',
  `query_request_body` VARCHAR(2048) COMMENT '请求体',
  `query_request_header` VARCHAR(2048) COMMENT '请求头',
  `query_result` LONGTEXT NOT NULL COMMENT '请求数据结果',
  `task_name` VARCHAR(100) NOT NULL COMMENT '任务名称',
  `update_method` VARCHAR(20) NOT NULL COMMENT '更新方式（增量更新，全量更新）',
  `execution_cycle` VARCHAR(50) NOT NULL COMMENT '执行周期',
  `execution_cron` VARCHAR(255) COMMENT '执行cron',
  `enable` INT NOT NULL COMMENT '是否启用（1启用，0禁用）',
  `status` INT NOT NULL COMMENT '状态',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生体属性映射任务';
CREATE INDEX `twin_property_task_name` ON `twin_property` (`task_name`);
CREATE INDEX `twin_property_twin_class_id` ON `twin_property` (`twin_class_id`)
2025-08-26 09:25:26.383 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_property_map, length=1014, columns=14, hasMetadata=true, sql=CREATE TABLE `twin_property_map` (
  `id` BIGINT NOT NULL COMMENT 'ID',
  `twin_property_id` BIGINT NOT NULL COMMENT '孪生体属性映射任务ID',
  `twin_field_name` VARCHAR(255) NOT NULL COMMENT '孪生体字段名称',
  `twin_field_comment` VARCHAR(255) NOT NULL COMMENT '孪生体字段说明',
  `twin_field_type` VARCHAR(127) NOT NULL COMMENT '孪生体字段类型',
  `twin_field_primary_key` INT NOT NULL COMMENT '孪生体字段主键（1为主键，0为非主键）',
  `external_field_name` VARCHAR(255) COMMENT '外部字段名称',
  `external_field_comment` VARCHAR(255) COMMENT '外部字段说明',
  `external_field_json_path` VARCHAR(255) NOT NULL COMMENT '外部字段Json路径（使用JsonPath语法）',
  `status` INT NOT NULL COMMENT '状态',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生体属性映射字段映射';
CREATE INDEX `twin_property_map_twin_property_id` ON `twin_property_map` (`twin_property_id`)
2025-08-26 09:25:26.383 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_room, length=545, columns=9, hasMetadata=true, sql=CREATE TABLE `twin_room` (
  `uuid` BIGINT NOT NULL COMMENT '唯一标识',
  `scene_code` VARCHAR(255) COMMENT '场景编码',
  `UniqueCode` VARCHAR(255) COMMENT '孪生体编码',
  `user_id` VARCHAR(127) COMMENT '场景点位userId',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` VARCHAR(127) COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` VARCHAR(127) COMMENT '更新人',
  `parent_cbid` VARCHAR(50) COMMENT '父级id'
,  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房间'
2025-08-26 09:25:26.384 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=twin_xxv, length=561, columns=8, hasMetadata=true, sql=CREATE TABLE `twin_xxv` (
  `id` BIGINT NOT NULL COMMENT 'ID',
  `twin_id` BIGINT NOT NULL COMMENT '孪生体id',
  `xxv_code` VARCHAR(100) COMMENT '菜单编号',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` BIGINT COMMENT '创建人',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user` BIGINT COMMENT '更新人'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='孪生对象xxv关联表';
CREATE INDEX `xxv_code` ON `twin_xxv` (`xxv_code`)
2025-08-26 09:25:26.384 [sql-utils1] INFO  com.uino.x.common.sql.maker.mysql.MysqlTableSql - 基于元数据生成CREATE语句: table=undo_log, length=697, columns=8, hasMetadata=true, sql=CREATE TABLE `undo_log` (
  `id` INT NOT NULL COMMENT 'id',
  `branch_id` BIGINT NOT NULL COMMENT 'branch transaction id',
  `xid` VARCHAR(128) NOT NULL COMMENT 'global transaction id',
  `context` VARCHAR(128) NOT NULL COMMENT 'undo_log context,such as serialization',
  `rollback_info` LONGBLOB NOT NULL COMMENT 'rollback info',
  `log_status` INT NOT NULL COMMENT '0:normal status,1:defense status',
  `log_created` DATETIME NOT NULL COMMENT 'create datetime',
  `log_modified` DATETIME NOT NULL COMMENT 'modify datetime'
,  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AT transaction mode undo table';
CREATE UNIQUE INDEX `ux_undo_log` ON `undo_log` (`xid`, `branch_id`)
2025-08-26 09:25:27.029 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.common.sql.maker.AbstractSchemaSql - 开始关闭Schema SQL制造者: bjdyk_scenex
2025-08-26 09:25:27.029 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.common.sql.maker.AbstractSchemaSql - Schema SQL制造者关闭完成: bjdyk_scenex
2025-08-26 09:25:27.029 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 生成的DROP语句长度: 39
2025-08-26 09:25:27.029 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - 生成的CREATE语句长度: 31
2025-08-26 09:25:27.030 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.common.sql.maker.AbstractSchemaSql - 开始关闭Schema SQL制造者: bjdyk_scenex
2025-08-26 09:25:27.030 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.common.sql.maker.AbstractSchemaSql - Schema SQL制造者关闭完成: bjdyk_scenex
2025-08-26 09:25:27.030 [SwingWorker-pool-2-thread-2] INFO  com.uino.x.migration.service.DatabaseService - Schema导出完成: C:\Users\<USER>\Desktop\data-export
2025-08-26 11:36:43.386 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.389 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已加载
2025-08-26 11:36:43.564 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.567 [AWT-EventQueue-0] INFO  c.uino.x.migration.ui.CombinedDatabaseConfigPanel - 已刷新数据库配置列表
2025-08-26 11:36:43.701 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.702 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-26 11:36:43.763 [AWT-EventQueue-0] INFO  c.u.x.c.s.t.s.SqlTransformationStrategyFactory - DataSource not set for ReplaceIntoTransformationStrategy, metadata-based column detection will not work, lazy settings using spring context
2025-08-26 11:36:43.793 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - Registered 10 transformation strategies in engine [MySQL-to-达梦]: [CreateDatabaseTransformationStrategy, DropDatabaseTransformationStrategy, BacktickQuoteTransformationStrategy, JsonOperatorTransformationStrategy, OnDuplicateKeyUpdateTransformationStrategy, ReplaceIntoTransformationStrategy, InformationSchemaTransformationStrategy, GroupConcatTransformationStrategy, DataTypeTransformationStrategy, AlterTableCommentTransformationStrategy]
2025-08-26 11:36:43.794 [AWT-EventQueue-0] INFO  c.u.x.c.sql.transformation.SqlTransformationEngine - SQL transformation engine [MySQL-to-达梦] initialized with 10 strategies
2025-08-26 11:36:43.794 [AWT-EventQueue-0] DEBUG com.uino.x.migration.ui.SqlTransformationPanel - 转换引擎已更新: MySQL-to-达梦
2025-08-26 11:36:43.794 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.SqlTransformationPanel - SQL转换引擎初始化成功
2025-08-26 11:36:43.846 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.878 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-26 11:36:43.915 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.915 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-26 11:36:43.916 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-26 11:36:43.919 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.919 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-26 11:36:43.921 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.921 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 3 个配置
2025-08-26 11:36:43.925 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.925 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-26 11:36:43.927 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.929 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.930 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-26 11:36:43.944 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.946 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.964 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.966 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 11:36:43.967 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-26 11:36:46.231 [AWT-EventQueue-0] INFO  c.u.x.migration.DatabaseMigrationToolApplication - 数据库迁移工具启动成功
2025-08-26 11:36:50.266 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:05:02.212 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.095 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.142 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已保存到: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.154 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.ConfigFilePanel - 配置文件内容已加载
2025-08-26 14:13:21.155 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.156 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.156 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: null
2025-08-26 14:13:21.156 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 数据库配置选择变化: 请选择数据库配置
2025-08-26 14:13:21.156 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MigrationPanel - 已刷新数据库配置列表
2025-08-26 14:13:21.157 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.157 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.OneClickMigrationPanel - 已刷新数据库配置列表
2025-08-26 14:13:21.158 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.159 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.FlywayValidationPanel - 已刷新数据库配置列表，共 3 个配置
2025-08-26 14:13:21.160 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.160 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.TimerPathFixPanel - 已刷新数据库配置列表
2025-08-26 14:13:21.160 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.162 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.162 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 开始应用配置到组件
2025-08-26 14:13:21.171 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.171 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.177 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.178 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.181 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.182 [AWT-EventQueue-0] INFO  com.uino.x.migration.config.ConfigManager - 配置已从文件加载: C:\Users\<USER>\.database-migration-tool\config.json
2025-08-26 14:13:21.183 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已应用到组件
2025-08-26 14:13:21.183 [AWT-EventQueue-0] INFO  com.uino.x.migration.ui.MainFrame - 配置已保存
