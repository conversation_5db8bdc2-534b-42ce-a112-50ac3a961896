package com.uino.x.pedestal.placement.impl.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.uino.x.common.tool.base.DateUtils;
import com.uino.x.pedestal.placement.common.enums.SceneTypeEnum;
import com.uino.x.pedestal.placement.common.utils.SceneUtil;
import com.uino.x.pedestal.placement.common.utils.ThingUtils;
import com.uino.x.pedestal.placement.pojo.entity.SceneData;
import com.uino.x.pedestal.placement.pojo.entity.SceneRecord;
import com.uino.x.pedestal.twin.pojo.param.DefaultTwinMapParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 构建封装场景树的专用工具类（非通用）
 * <p>
 * 本类虽然是工具类，但是内部定义了必备的成员属性，以及固有的执行顺序，所以，请把它当成普通对象使用，不准直接以类方法的方式引用
 *
 * <AUTHOR>
 * @version 0.0.1
 * @date 2021/9/27 10:01
 */
public class BuildSceneTreeUtil {

    /**
     * 主场景uuid
     */
    private final long UUID;

    /**
     * 是否不显示房间（true：不显示，false：显示）
     */
    private final boolean ROOM_FLAG;

    /**
     * 是否显示孪生体（true：显示，false：不显示）
     */
    private final boolean TWIN_FLAG;

    /**
     * 本场景关联的主+子场景数据
     */
    private final List<SceneRecord> ALL_SCENE_RECORD;

    /**
     * 本场景依赖的所有物体数据
     */
    private final List<SceneData> ALL_SCENE_DATA;

    /**
     * 主场景
     */
    private final SceneRecord MAIN_SCENE_RECORD;

    /**
     * 场景效果包（新版）
     */
    private Map<String,String> EFFECT_PACKAGE_NEW;

    /**
     * 默认孪生体信息
     */
    private final Map<String, Map<String, Object>> BUILDING_TWIN_MAP;

    private final Map<String, Map<String, Object>> FLOOR_TWIN_MAP;

    // private final Map<String, Map<String, Object>> ROOM_TWIN_MAP;

    private final Map<String, Map<String, Object>> PARK_TWIN_MAP;


    public BuildSceneTreeUtil(SceneRecord mainSceneRecord, boolean roomFlag, boolean twinFlag, DefaultTwinMapParam twinData, List<SceneRecord> allSceneRecord, List<SceneData> allSceneData) {
        if (Objects.isNull(mainSceneRecord) || CollectionUtils.isEmpty(allSceneRecord) || CollectionUtils.isEmpty(allSceneData)) {
            throw new NullPointerException("场景或者场景数据为空");
        }
        this.MAIN_SCENE_RECORD = mainSceneRecord;
        this.UUID = mainSceneRecord.getUuid();
        this.ROOM_FLAG = roomFlag;
        this.ALL_SCENE_RECORD = allSceneRecord;
        this.ALL_SCENE_DATA = allSceneData;
        this.TWIN_FLAG = twinFlag;
        if (twinFlag && !Objects.isNull(twinData)) {
            BUILDING_TWIN_MAP = twinData.getBuildingMap();
            FLOOR_TWIN_MAP = twinData.getFloorMap();
            //ROOM_TWIN_MAP = twinData.getRoomMap();
            PARK_TWIN_MAP = twinData.getParkMap();
        } else {
            BUILDING_TWIN_MAP = new HashMap<>();
            FLOOR_TWIN_MAP = new HashMap<>();
            //ROOM_TWIN_MAP = new HashMap<>();
            PARK_TWIN_MAP = new HashMap<>();
        }
        EFFECT_PACKAGE_NEW = new HashMap<>();
    }

    public void setEffectPackageNew(Map<String, String> effectPackage) {
        this.EFFECT_PACKAGE_NEW = CollectionUtils.isEmpty(effectPackage)?new HashMap<>():effectPackage;
    }

    /**
     * 构造场景一级
     *
     * @return 单个场景json对象
     */
    public JSONObject generateScene() {
        JSONObject sceneJson = (JSONObject) JSON.toJSON(MAIN_SCENE_RECORD);
        List<JSONObject> buildAndPark = generateBuilding(sceneJson);
        // 对结果进行一次排序(按照campusBuilderId来，如果是字符串则按照字符串的hashcode)
        Collections.sort(buildAndPark, (o1, o2) -> {
            Long campusBuilderId1 = null;
            String campusBuilderIdStr1 = o1.getString("campusBuilderId");
            try {
                if (StringUtils.isEmpty(campusBuilderIdStr1)) {
                    campusBuilderId1 = null;
                } else {
                    campusBuilderId1 = Long.valueOf(campusBuilderIdStr1);
                }
            } catch (Exception e) {
                campusBuilderId1 = (long) campusBuilderIdStr1.hashCode();
            }
            Long campusBuilderId2 = null;
            String campusBuilderIdStr2 = o2.getString("campusBuilderId");
            try {
                if (StringUtils.isEmpty(campusBuilderIdStr2)) {
                    campusBuilderId2 = null;
                } else {
                    campusBuilderId2 = Long.valueOf(campusBuilderIdStr2);
                }
            } catch (Exception e) {
                campusBuilderId2 = (long) campusBuilderIdStr2.hashCode();
            }
            if (Objects.isNull(campusBuilderId1)) {
                return 1;
            }
            if (Objects.isNull(campusBuilderId2)) {
                return -1;
            }
            return campusBuilderId1.compareTo(campusBuilderId2);
        });
        sceneJson.put("children", buildAndPark);
        sceneJson.put("dataType", "scene");
        sceneJson.put("url", MAIN_SCENE_RECORD.getSceneCode() + "/" + MAIN_SCENE_RECORD.getVersion());
        sceneJson.put("tjsOrigin", ThingUtils.getSceneSource(MAIN_SCENE_RECORD.getTjsVersion()));
        sceneJson.put("effectPackageNew", EFFECT_PACKAGE_NEW.get(MAIN_SCENE_RECORD.getSceneCode()));
        // 园区挂上孪生体
        if (this.TWIN_FLAG) {
            Map thisSceneTwinMap = PARK_TWIN_MAP.getOrDefault(MAIN_SCENE_RECORD.getSceneCode(), Collections.EMPTY_MAP);
            Object twinData = thisSceneTwinMap.get(SceneUtil.OUTDOOR_NODE);
            sceneJson.put("twinData", twinData);
        }
        return sceneJson;
    }

    /**
     * 构造建筑或者园区二级
     *
     * @param sceneJson 主场景
     * @return 本场景下的建筑或者园区数组对象
     */
    public List<JSONObject> generateBuilding(JSONObject sceneJson) {
        // 查询该场景(主+子)下的所有建筑及室外信息。2021-12-07 15:47:56，修改为只查询主场景的
        List<SceneData> buildingAndParkDatas = ALL_SCENE_DATA.stream().filter(e -> ("0".equals(e.getParentCBID()) && (this.MAIN_SCENE_RECORD.getUuid()).equals(e.getParentSceneUUID()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(buildingAndParkDatas)) {
            // 没有任何主+子场景数据
            return null;
        }
        // 逐一处理每个建筑或者室外(单个)
        List<JSONObject> buildingsJson = new ArrayList<>();
        Set<Long> cache = new HashSet<>();
        // buildingAndParkDatas含有场景点位和傀儡节点
        for (SceneData dataItem : buildingAndParkDatas) {
            if (cache.contains(dataItem.getUuid())) {
                //考虑到傀儡节点的干扰，为了防止重复加入，这里做了一次防重判断
                continue;
            }
            // 这里需要考虑有子场景的情况
            if (Objects.isNull(dataItem.getChildrenSceneUUID())) {
                // 无子场景
                String mainSceneCode = MAIN_SCENE_RECORD.getSceneCode();
                generateThings(dataItem, UUID, null, sceneJson, buildingsJson, mainSceneCode);
                cache.add(dataItem.getUuid());
            } else {
                // 有子场景，通过childrenSceneUUID在scene_record唯一且为启用的条件查询一条场景
                Optional<SceneRecord> childSceneDataOp = ALL_SCENE_RECORD.stream().filter(e -> 1 == e.getEnable() && Objects.equals(e.getUuid(), dataItem.getChildrenSceneUUID())).findFirst();
                // 关联的场景为空，或者未启用，则这颗树不予展示
                if (!childSceneDataOp.isPresent()) {
                    continue;
                }
                SceneRecord childSceneRecord = childSceneDataOp.get();
                // 然后通过场景的uuid，在scene_data查询，条件是parent_scene_record_uuid = uuid，为建筑的数据。注意：子场景的下只有一个建筑
                Optional<SceneData> childBuildingAndParkOp = ALL_SCENE_DATA.stream().filter(e -> ("0".equals(e.getParentCBID()) && Objects.equals(dataItem.getChildrenSceneUUID(), e.getParentSceneUUID()))).findFirst();

                if (!childBuildingAndParkOp.isPresent()) {
                    continue;
                }
                SceneData sceneData = childBuildingAndParkOp.get();
                // 将傀儡节点里面保存的外立面返回至这个建筑里面
                sceneData.setFacades(dataItem.getFacades());
                sceneData.setUserid(dataItem.getUserid());
                String childSceneCode = childSceneRecord.getSceneCode();
                generateThings(sceneData, dataItem.getChildrenSceneUUID(), childSceneRecord, sceneJson, buildingsJson, childSceneCode);
                cache.add(sceneData.getUuid());
            }
        }
        // 下面的代码别删！TODO 这里的户外建筑信息来源于下一级的数据挂载
        // if (sceneJSON.containsKey(SceneTypeEnum.OUTDOOR.getVal())) {
        //     // 将园区的层级保持与建筑物是同一级
        //     buildingsJSON.add(sceneJSON.getJSONObject(SceneTypeEnum.OUTDOOR.getVal()));
        //     sceneJSON.remove(SceneTypeEnum.OUTDOOR.getVal());
        // }
        return buildingsJson;
    }


    /**
     * 构建本建筑下的所有楼层及物体信息
     *
     * @param buildInfoItem    本建筑基本信息
     * @param sceneUuid        物体归属的场景uuid
     * @param childSceneRecord 子场景数据信息
     * @param sceneJson        主场景信息
     * @param buildingsJson    用于装数据返回的结果集
     * @param sceneCode        场景code(主场景或者子场景)
     */
    private void generateThings(SceneData buildInfoItem, Long sceneUuid, SceneRecord childSceneRecord, JSONObject sceneJson, List<JSONObject> buildingsJson, String sceneCode) {
        // 查询本建筑下的楼层或者室外数据
        List<SceneData> plansAndOutDoor = ALL_SCENE_DATA.stream().filter(e -> Objects.equals(e.getParentSceneUUID(), sceneUuid) && Objects.equals(e.getParentCBID(), buildInfoItem.getCampusBuilderId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(plansAndOutDoor)) {
            JSONObject empty = (JSONObject) JSON.toJSON(buildInfoItem);
            if ((SceneUtil.OUTDOOR_NODE).equals(buildInfoItem.getDataType())) {
                // 园区下，没有任何数据时，此时返回的是一个空数据
                buildingsJson.add(0, empty);
            } else {
                // 建筑下面没有楼层的情况
                empty.put("underSceneId", Objects.isNull(childSceneRecord) ? buildInfoItem.getParentSceneUUID() : sceneJson.getLong("uuid"));
                // 楼层挂上孪生体
                if (this.TWIN_FLAG) {
                    Map thisSceneTwinMap = BUILDING_TWIN_MAP.getOrDefault(sceneCode, Collections.EMPTY_MAP);
                    Object twinData = thisSceneTwinMap.get(buildInfoItem.getUserid());
                    empty.put("twinData", twinData);
                }
                buildingsJson.add(empty);
            }
            return;
        }
        // 处理楼栋数据
        if ((SceneTypeEnum.BUILDING.getVal()).equals(buildInfoItem.getDataType())) {
            // 每一栋下的数据
            JSONObject buildingJson = (JSONObject) JSON.toJSON(buildInfoItem);
            // 建筑挂上孪生体
            if (this.TWIN_FLAG) {
                Map thisSceneTwinMap = BUILDING_TWIN_MAP.getOrDefault(sceneCode, Collections.EMPTY_MAP);
                Object twinData = thisSceneTwinMap.get(buildInfoItem.getUserid());
                buildingJson.put("twinData", twinData);
            }
            buildingJson.put("createTime", DateFormatUtils.format(buildInfoItem.getCreateTime(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            JSONArray plansJson = new JSONArray();
            // 楼层数据
            for (SceneData planItem : plansAndOutDoor) {
                JSONObject planJson = (JSONObject) JSON.toJSON(planItem);
                // 楼层挂上孪生体
                if (this.TWIN_FLAG) {
                    Map thisSceneTwinMap = FLOOR_TWIN_MAP.getOrDefault(sceneCode, Collections.EMPTY_MAP);
                    Object twinData = thisSceneTwinMap.get(planItem.getUserid());
                    planJson.put("twinData", twinData);
                }
                // 本楼层下的数据信息集合
                JSONArray planJsons = new JSONArray();
                List<SceneData> basic = new ArrayList<>();
                // 根据flag控制物体是否显示
                if (ROOM_FLAG) {
                    // 不显示房间
                    //basic = ALL_SCENE_DATA.stream().filter(e -> Objects.equals(e.getParentSceneUUID(), sceneUuid) && Objects.equals(e.getParentCBID(), planItem.getCampusBuilderId())).collect(Collectors.toList());
                } else {
                    // 只查询最底层房间数据
                    basic = ALL_SCENE_DATA.stream().filter(e -> (SceneTypeEnum.ROOM.getVal()).equals(e.getDataType()) && Objects.equals(e.getParentSceneUUID(), sceneUuid) && Objects.equals(e.getParentCBID(), planItem.getCampusBuilderId())).collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(basic)) {
                    plansJson.add(planJson);
                    continue;
                }
                // 房间的孪生体一概不返回，临时注释下面的代码
                //if (this.TWIN_FLAG) {
                //    for (SceneData layer : basic) {
                //        JSONObject layerJson = (JSONObject) JSON.toJSON(layer);
                //        // 房间挂上孪生体
                //        if ((SceneUtil.ROOM_NODE).equals(layer.getDataType())) {
                //            Map thisSceneTwinMap = ROOM_TWIN_MAP.getOrDefault(sceneCode, Collections.EMPTY_MAP);
                //            Object twinData = thisSceneTwinMap.get(layer.getCampusBuilderId());
                //            layerJson.put("twinData", twinData);
                //        }
                //        planJsons.add(layerJson);
                //    }
                //} else {
                //    planJsons.addAll(basic);
                //}
                // TODO:为了修改返回的创建时间格式
                JSONArray basicJsonArray = basic.stream().collect(JSONArray::new, (jsonArray, item) -> jsonArray.add(JSON.toJSON(item)), JSONArray::addAll);
                planJsons.addAll(basicJsonArray);
                // 楼层下的物体
                planJson.put("children", planJsons);
                if (Objects.isNull(childSceneRecord)) {
                    planJson.put("sceneCode", MAIN_SCENE_RECORD.getSceneCode());
                } else {
                    planJson.put("sceneCode", childSceneRecord.getSceneCode());
                }
                // 每一个楼层的数据
                plansJson.add(planJson);
            }
            // 本栋楼下的场景
            buildingJson.put("children", plansJson);
            if (Objects.isNull(childSceneRecord)) {
                // 本建筑附属于哪个主场景节点
                buildingJson.put("underSceneId", buildInfoItem.getParentSceneUUID());
                buildingJson.put("sceneCode", MAIN_SCENE_RECORD.getSceneCode());
                if (StringUtils.isBlank(buildingJson.getString("childrenSceneUUID"))) {
                    // 前端需要一个为null的字段判断使用
                    buildingJson.putIfAbsent("childrenSceneUUID", null);
                }
            } else {
                buildingJson.put("translatePosition", childSceneRecord.getTranslatePosition());
                // 对于子场景的情况进行特殊处理
                buildingJson.put("childrenSceneUUID", buildInfoItem.getParentSceneUUID());
                buildingJson.put("version", childSceneRecord.getVersion());
                buildingJson.put("url", childSceneRecord.getSceneCode() + "/" + childSceneRecord.getVersion());
                buildingJson.put("sceneCode", childSceneRecord.getSceneCode());
                buildingJson.put("underSceneId", sceneJson.getLong("uuid"));
                buildingJson.put("source", "normal");
                buildingJson.put("tjsOrigin", ThingUtils.getSceneSource(childSceneRecord.getTjsVersion()));
                buildingJson.put("effectPackageNew", EFFECT_PACKAGE_NEW.get(childSceneRecord.getSceneCode()));
            }
            // 处理完毕每栋建筑
            buildingsJson.add(buildingJson);
        }
        // 处理园区数据
        if ((SceneUtil.OUTDOOR_NODE).equals(buildInfoItem.getDataType())) {
            JSONObject outDoorJson = (JSONObject) JSON.toJSON(buildInfoItem);
            JSONArray parkJsons = new JSONArray();
            parkJsons.addAll(plansAndOutDoor);
            outDoorJson.remove("createTime");
            // flag控制底层房间物体显示
            if (ROOM_FLAG) {
                outDoorJson.put("children", null);
                //outDoorJson.put("children", parkJsons);
            } else {
                // 园区一级下面只显示房间
                List<SceneData> onlyDisplayRooms = plansAndOutDoor.stream().filter(e -> (SceneTypeEnum.ROOM.getVal()).equals(e.getDataType())).collect(Collectors.toList());
                // 房间的孪生体一概不返回，临时注释下面的代码
                //if (this.TWIN_FLAG) {
                //    JSONArray roomsJsons = new JSONArray();
                //    for (SceneData room : onlyDisplayRooms) {
                //        JSONObject roomJson = (JSONObject) JSON.toJSON(room);
                //        // 房间挂上孪生体
                //        if ((SceneUtil.ROOM_NODE).equals(room.getDataType())) {
                //            Map thisSceneTwinMap = ROOM_TWIN_MAP.getOrDefault(sceneCode, Collections.EMPTY_MAP);
                //            Object twinData = thisSceneTwinMap.get(room.getCampusBuilderId());
                //            roomJson.put("twinData", twinData);
                //        }
                //        roomsJsons.add(roomJson);
                //    }
                //    outDoorJson.put("children", roomsJsons);
                //} else {
                //    outDoorJson.put("children", onlyDisplayRooms);
                //}
//                onlyDisplayRooms.forEach(onlyDisplayRoom->{
//                    onlyDisplayRoom.getCreateTime()
//                });


                JSONArray onlyDisplayRoomJsons = new JSONArray();
                //  为了修改返回的创建时间格式
                JSONArray basicJsonArray = onlyDisplayRooms.stream().collect(JSONArray::new, (jsonArray, item) -> jsonArray.add(JSON.toJSON(item)), JSONArray::addAll);
                onlyDisplayRoomJsons.addAll(basicJsonArray);
                outDoorJson.put("children", onlyDisplayRoomJsons);
            }
            // 下面的代码别删！ 将场景下的户外信息挂载到场景下，作用：在这里挂载后，封装场景时方便设置它与建筑同级
            // sceneJSON.put(SceneTypeEnum.OUTDOOR.getVal(), outDoorJson);
            /// 需求是：把园区放到与建筑下级
            buildingsJson.add(0, outDoorJson);
        }
    }

}
