package com.uino.x.pedestal.tenant.migrate.impl;


import com.uino.x.common.tool.base.ThrowUtils;
import com.uino.x.common.tool.spring.SpringIocUtils;
import com.uino.x.pedestal.tenant.migrate.*;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.FlywayException;
import org.flywaydb.core.api.output.RepairResult;

import javax.sql.DataSource;

/**
 * 初始化数据的租户迁移
 *
 * <AUTHOR>
 * @version 0.0.5
 * @date 2021/11/26 10:38
 */
@Slf4j
public class InitTenantMigration extends AbstractTenantMigration implements TenantMigration {


    public InitTenantMigration(DataSource dataSource, TenantMigrationInfo tenantMigrationInfo) {
        super(dataSource, tenantMigrationInfo);
    }


    @Override
    public void migrate() {

        final FlywayOption flywayOption = SpringIocUtils.getBeanOp(FlywayOption.class).orElseThrow();
        final Flyway flyway = flywayOption.build(dataSource, new FlywayReplaceSchemaCallback(tenantMigrationInfo.getTargetTenant()));
        try {
            // 进行数据迁移
            flyway.migrate();
        } catch (FlywayException e) {
            // 失败后进行修复
            final RepairResult repair = flyway.repair();
            log.error("repairActions: \n{}", String.join("\n", repair.repairActions));
            throw ThrowUtils.getThrow().internalServerError(String.format("初始化租户对应的数据迁移失败, %s", tenantMigrationInfo.getTargetTenant()), e);
        }
    }
}
