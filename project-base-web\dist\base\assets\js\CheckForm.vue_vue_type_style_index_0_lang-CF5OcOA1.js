import{F as e,c as a,I as l,p as s,B as r,M as u}from"./ant-design-vue-DW0D0Hn-.js";import{d,r as t,a as o,V as n,U as m,bJ as b,c as v,G as f,n as p}from"./@vue-DgI1lw0Y.js";const i=d({__name:"CheckForm",setup(d,{expose:i}){const c=t(!1),h=t(),_=o({id:"",name:"",url:"",reqMethod:"",browser:"",param:"",result:""}),w=()=>{h.value.resetFields(),c.value=!1};return i({init:e=>{c.value=!0,p((()=>{h.value.resetFields(),_.id=e.id,_.name=e.name,_.url=e.url,_.reqMethod=e.reqMethod,_.browser=e.browser,_.param=e.param,_.result=e.result}))}}),(d,t)=>{const o=l,p=a,i=s,k=e,M=r,U=u;return m(),n(U,{width:678,title:"日志详情","wrap-class-name":"cus-modal",open:c.value,"mask-closable":!1,onCancel:w},{footer:b((()=>[v(M,{onClick:w},{default:b((()=>t[6]||(t[6]=[f("关闭")]))),_:1})])),default:b((()=>[v(k,{ref_key:"formRef",ref:h,model:_,"label-align":"left",class:"checkForm"},{default:b((()=>[v(p,{name:"name",label:"日志名称","has-feedback":""},{default:b((()=>[v(o,{value:_.name,"onUpdate:value":t[0]||(t[0]=e=>_.name=e),disabled:!0},null,8,["value"])])),_:1}),v(p,{name:"url",label:"请求地址","has-feedback":""},{default:b((()=>[v(o,{value:_.url,"onUpdate:value":t[1]||(t[1]=e=>_.url=e),disabled:!0},null,8,["value"])])),_:1}),v(p,{name:"reqMethod",label:"请求方式","has-feedback":""},{default:b((()=>[v(o,{value:_.reqMethod,"onUpdate:value":t[2]||(t[2]=e=>_.reqMethod=e),disabled:!0},null,8,["value"])])),_:1}),v(p,{name:"browser",label:"浏览器","has-feedback":""},{default:b((()=>[v(o,{value:_.browser,"onUpdate:value":t[3]||(t[3]=e=>_.browser=e),disabled:!0},null,8,["value"])])),_:1}),v(p,{name:"param",label:"请求参数"},{default:b((()=>[v(i,{value:_.param,"onUpdate:value":t[4]||(t[4]=e=>_.param=e),rows:4,disabled:!0},null,8,["value"])])),_:1}),v(p,{name:"result",label:"返回结果"},{default:b((()=>[v(i,{value:_.result,"onUpdate:value":t[5]||(t[5]=e=>_.result=e),rows:4,disabled:!0},null,8,["value"])])),_:1})])),_:1},8,["model"])])),_:1},8,["open"])}}});export{i as _};
