var e=Object.defineProperty,t=(t,r,s)=>((t,r,s)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[r]=s)(t,"symbol"!=typeof r?r+"":r,s);import{d as r,r as s,o as i,w as h,S as l,U as a,am as n,F as u,b7 as d,bk as g}from"./@vue-DgI1lw0Y.js";class o{constructor(e){t(this,"ruleArr"),t(this,"dateArr"),t(this,"resultList"),t(this,"dayRuleSup"),t(this,"dayRule"),this.ruleArr=e.ruleArr,this.dateArr=[],this.resultList=[],this.dayRuleSup=[],this.dayRule="",this.expressionChange()}expressionChange(){const e=this.ruleArr;let t=0;const r=[],s=new Date,i=s.getFullYear();let h=s.getMonth()+1,l=s.getDate(),a=s.getHours(),n=s.getMinutes(),u=s.getSeconds();this.getSecondArr(e[0]),this.getMinArr(e[1]),this.getHourArr(e[2]),this.getDayArr(e[3]),this.getMouthArr(e[4]),this.getWeekArr(e[5]),this.getYearArr(e[6],i);const d=this.dateArr[0],g=this.dateArr[1],o=this.dateArr[2],A=this.dateArr[3],c=this.dateArr[4],f=this.dateArr[5];let y=this.getIndex(d,u),p=this.getIndex(g,n),$=this.getIndex(o,a),R=this.getIndex(A,l),S=this.getIndex(c,h);const D=this.getIndex(f,i),k=()=>{y=0,u=d[y]},x=()=>{p=0,n=g[p],k()},m=()=>{$=0,a=o[$],x()},O=()=>{R=0,l=A[R],m()},v=()=>{S=0,h=c[S],O()};i!==f[D]&&v(),h!==c[S]&&O(),l!==A[R]&&m(),a!==o[$]&&x(),n!==g[p]&&k();e:for(let w=D;w<f.length;w++){const e=f[w];if(h>c[c.length-1])v();else t:for(let s=S;s<c.length;s++){let i=c[s];if(i=i<10?`0${i}`:i,l>A[A.length-1]){if(O(),s===c.length-1){v();continue e}}else r:for(let h=R;h<A.length;h++){let l=A[h],f=l<10?`0${l}`:l;if(a>o[o.length-1]){if(m(),h===A.length-1){if(O(),s===c.length-1){v();continue e}continue t}}else{if(!0!==this.checkDate(`${e}-${i}-${f} 00:00:00`)&&"workDay"!==this.dayRule&&"lastWeek"!==this.dayRule&&"lastDay"!==this.dayRule){O();continue t}if("lastDay"===this.dayRule){if(!0!==this.checkDate(`${e}-${i}-${f} 00:00:00`))for(;l>0&&!0!==this.checkDate(`${e}-${i}-${f} 00:00:00`);)l--,f=l<10?`0${l}`:l}else if("workDay"===this.dayRule){if(!0!==this.checkDate(`${e}-${i}-${f} 00:00:00`))for(;l>0&&!0!==this.checkDate(`${e}-${i}-${f} 00:00:00`);)l--,f=l<10?`0${l}`:l;const t=this.formatDate(new Date(`${e}-${i}-${f} 00:00:00`),"week");0===t?(l++,f=l<10?`0${l}`:l,!0!==this.checkDate(`${e}-${i}-${f} 00:00:00`)&&(l-=3)):6===t&&(1!==this.dayRuleSup?l--:l+=2)}else if("weekDay"===this.dayRule){const t=this.formatDate(new Date(`${e}-${i}-${l} 00:00:00`),"week");if(Array.prototype.indexOf(this.dayRuleSup,t)<0){if(h===A.length-1){if(O(),s===c.length-1){v();continue e}continue t}continue}}else if("assWeek"===this.dayRule){const t=this.formatDate(new Date(`${e}-${i}-${l} 00:00:00`),"week");l=this.dayRuleSup[1]>=t?7*(this.dayRuleSup[0]-1)+this.dayRuleSup[1]-t+1:7*this.dayRuleSup[0]+this.dayRuleSup[1]-t+1}else if("lastWeek"===this.dayRule){if(!0!==this.checkDate(`${e}-${i}-${f} 00:00:00`))for(;l>0&&!0!==this.checkDate(`${e}-${i}-${f} 00:00:00`);)l--,f=l<10?`0${l}`:l;const t=this.formatDate(new Date(`${e}-${i}-${f} 00:00:00`),"week");this.dayRuleSup<t?l-=t-this.dayRuleSup:this.dayRuleSup>t&&(l-=7-(this.dayRuleSup-t))}l=l<10?`0${l}`:l;s:for(let a=$;a<o.length;a++){const f=o[a]<10?`0${o[a]}`:o[a];if(n>g[g.length-1]){if(x(),a===o.length-1){if(m(),h===A.length-1){if(O(),s===c.length-1){v();continue e}continue t}continue r}}else i:for(let n=p;n<g.length;n++){const p=g[n]<10?`0${g[n]}`:g[n];if(u>d[d.length-1]){if(k(),n===g.length-1){if(x(),a===o.length-1){if(m(),h===A.length-1){if(O(),s===c.length-1){v();continue e}continue t}continue r}continue s}}else for(let u=y;u<=d.length-1;u++){const y=d[u]<10?`0${d[u]}`:d[u];if("00"!==i&&"00"!==l&&(r.push(`${e}-${i}-${l} ${f}:${p}:${y}`),t++),5===t)break e;if(u===d.length-1){if(k(),n===g.length-1){if(x(),a===o.length-1){if(m(),h===A.length-1){if(O(),s===c.length-1){v();continue e}continue t}continue r}continue s}continue i}}}}}}}}0===r.length?this.resultList=["没有达到条件的结果！"]:(this.resultList=r,5!==r.length&&this.resultList.push(`最近100年内只有上面${r.length}条结果！`))}getSecondArr(e){this.dateArr[0]=this.getOrderArr(0,59),e.indexOf("-")>=0?this.dateArr[0]=this.getCycleArr(e,60,!0):e.indexOf("/")>=0?this.dateArr[0]=this.getAverageArr(e,59):"*"!==e&&(this.dateArr[0]=this.getAssignArr(e))}getMinArr(e){this.dateArr[1]=this.getOrderArr(0,59),e.indexOf("-")>=0?this.dateArr[1]=this.getCycleArr(e,60,!0):e.indexOf("/")>=0?this.dateArr[1]=this.getAverageArr(e,59):"*"!==e&&(this.dateArr[1]=this.getAssignArr(e))}getHourArr(e){this.dateArr[2]=this.getOrderArr(0,23),e.indexOf("-")>=0?this.dateArr[2]=this.getCycleArr(e,24,!0):e.indexOf("/")>=0?this.dateArr[2]=this.getAverageArr(e,23):"*"!==e&&(this.dateArr[2]=this.getAssignArr(e))}getDayArr(e){var t;if(this.dateArr[3]=this.getOrderArr(1,31),this.dayRule="",this.dayRuleSup="",e.indexOf("-")>=0)this.dateArr[3]=this.getCycleArr(e,31,!1),this.dayRuleSup="null";else if(e.indexOf("/")>=0)this.dateArr[3]=this.getAverageArr(e,31),this.dayRuleSup="null";else if(e.indexOf("W")>=0){this.dayRule="workDay";const r=(null==(t=e.match(/[0-9]{1,2}/g))?void 0:t[0])||0;this.dayRuleSup=Number(r[0]),this.dateArr[3]=[this.dayRuleSup]}else e.indexOf("L")>=0?(this.dayRule="lastDay",this.dayRuleSup="null",this.dateArr[3]=[31]):"*"!==e&&"?"!==e?(this.dateArr[3]=this.getAssignArr(e),this.dayRuleSup="null"):"*"===e&&(this.dayRuleSup="null")}getWeekArr(e){var t;if(""===this.dayRule&&""===this.dayRuleSup){if(e.indexOf("-")>=0)this.dayRule="weekDay",this.dayRuleSup=this.getCycleArr(e,7,!1);else if(e.indexOf("#")>=0){this.dayRule="assWeek";const t=e.match(/[0-9]{1}/g);this.dayRuleSup=[Number(null==t?void 0:t[0]),Number(null==t?void 0:t[1])],this.dateArr[3]=[1],7===this.dayRuleSup[1]&&(this.dayRuleSup[1]=0)}else e.indexOf("L")>=0?(this.dayRule="lastWeek",this.dayRuleSup=Number(null==(t=e.match(/[0-9]{1,2}/g))?void 0:t[0]),this.dateArr[3]=[31],7===this.dayRuleSup&&(this.dayRuleSup=0)):"*"!==e&&"?"!==e&&(this.dayRule="weekDay",this.dayRuleSup=this.getAssignArr(e));if("weekDay"===this.dayRule)for(let e=0;e<this.dayRuleSup.length;e++)7===this.dayRuleSup[e]&&(this.dayRuleSup[e]=0)}}getMouthArr(e){this.dateArr[4]=this.getOrderArr(1,12),e.indexOf("-")>=0?this.dateArr[4]=this.getCycleArr(e,12,!1):e.indexOf("/")>=0?this.dateArr[4]=this.getAverageArr(e,12):"*"!==e&&(this.dateArr[4]=this.getAssignArr(e))}getYearArr(e,t){this.dateArr[5]=this.getOrderArr(t,t+100),void 0!==e&&(e.indexOf("-")>=0?this.dateArr[5]=this.getCycleArr(e,t+100,!1):e.indexOf("/")>=0?this.dateArr[5]=this.getAverageArr(e,t+100):"*"!==e&&(this.dateArr[5]=this.getAssignArr(e)))}getOrderArr(e,t){const r=[];for(let s=e;s<=t;s++)r.push(s);return r}getCycleArr(e,t,r){const s=[],i=e.split("-"),h=Number(i[0]);let l=Number(i[1]);h>l&&(l+=t);for(let a=h;a<=l;a++){let e=0;!1===r&&a%t==0&&(e=t),s.push(Math.round(a%t+e))}return s.sort(this.compare),s}getAverageArr(e,t){const r=[],s=e.split("/");let i=Number(s[0]);const h=Number(s[1]);for(;i<=t;)r.push(i),i+=h;return r}getAssignArr(e){const t=[],r=e.split(",");for(let s=0;s<r.length;s++)t[s]=Number(r[s]);return t.sort(this.compare),t}getIndex(e,t){if(t<=e[0]||t>e[e.length-1])return 0;for(let r=0;r<e.length-1;r++)if(t>e[r]&&t<=e[r+1])return r+1;return-1}checkDate(e){const t=new Date(e);return e===this.formatDate(t)}formatDate(e,t){const r="number"==typeof e?new Date(e):e,s=r.getFullYear(),i=r.getMonth()+1,h=r.getDate(),l=r.getHours(),a=r.getMinutes(),n=r.getSeconds(),u=r.getDay();return void 0===t?`${s}-${i<10?`0${i}`:i}-${h<10?`0${h}`:h} ${l<10?`0${l}`:l}:${a<10?`0${a}`:a}:${n<10?`0${n}`:n}`:"week"===t?u:void 0}compare(e,t){return t-e>0?-1:1}}const A={class:"popup-result"},c={class:"popup-result-scroll"},f={key:1},y=r({__name:"Crontab-Result",props:{ex:{default:""}},setup(e){const t=e,r=s([]),y=s(!1),p=()=>{y.value=!1;const e=t.ex.split(" "),{resultList:s}=new o({ruleArr:e});r.value=s,y.value=!0};return i((()=>{p()})),h((()=>t.ex),p),(e,t)=>(a(),l("div",A,[t[0]||(t[0]=n("p",{class:"title"},"最近5次运行时间",-1)),n("ul",c,[y.value?(a(!0),l(u,{key:0},d(r.value,(e=>(a(),l("li",{key:e},g(e),1)))),128)):(a(),l("li",f,"计算结果中..."))])]))}});export{y as _};
