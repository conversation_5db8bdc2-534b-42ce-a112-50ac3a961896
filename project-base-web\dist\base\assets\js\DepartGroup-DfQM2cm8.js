import{d as e,r as a,o as l,w as t,b9 as s,S as o,U as r,am as i,c as n,V as u,bJ as d,u as p,al as m,A as c,bk as v}from"./@vue-DgI1lw0Y.js";import{a as f,b as j,c as h,e as b}from"./bubble-CENITBfd.js";import{u as g}from"./main-DE7o6g98.js";import{S as y}from"./@ant-design-tBRGNTkq.js";import{I as k,r as _,i as w,s as x,F as C,n as O,d as S,c as E,M as P}from"./ant-design-vue-DW0D0Hn-.js";import{_ as U}from"./vue-qr-6l_NUpj8.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const F={class:"tree-wrap"},I={class:"tree-contain"},J={class:"tree-search"},K={key:1,class:"tree-content"},N=["title"],D={key:0,class:"tab-bar"},G={key:1,class:"tab-bar"},M={class:"title"},R=U(e({__name:"DepartGroup",emits:["handleClick","onBack"],setup(e,{expose:U,emit:R}){const T=R,q={name:[{required:!0,message:"请输入分类名称"}]},z=a(),A=a({children:"children",title:"title",key:"id"}),L=a(""),B=a([]);let H=[];const V=a([]),W=a([]),$=a(!1),Q=a(!1),X=a({form:"",name:"",parents:""}),Y=a({id:"",pid:"",sort:100,name:"",parents:"",form:""});l((()=>{ae("")}));const Z=()=>{z.value.validateFields().then((e=>{const a={name:X.value.name,pid:X.value.parents,sort:100};h(a).then((e=>{200===e.code?(g("success","新增分类成功！"),ae(""),$.value=!1):400101===e.code&&g("error","分类名称重复")}),(e=>{g("error","添加分类失败")}))}),(()=>{g("error","添加分类失败")}))},ee=()=>{b({name:Y.value.name,pid:Y.value.pid,id:Y.value.id,sort:Y.value.sort}).then((e=>{200===e.code?(Q.value=!1,ae(""),g("success","编辑分类成功")):g("error","编辑分类失败")}))},ae=e=>{f({flag:!0}).then((a=>{if(!a.success)return;const l=[];l.push({children:a.data,id:"0",pid:"0",rootTarget:!0,title:"全部"}),B.value=l,H=l,V.value=e?[e]:[B.value[0].id]}))},le=(e,a)=>{e.length?V.value=e:V.value=[B.value[0].id],T("handleClick",e,a)};t(L,(e=>{B.value=te(H,e)}));const te=(e,a)=>{const l=JSON.parse(JSON.stringify(e));if(!l||!l.length)return[];const t=[];for(const s of l){const e=te(s.children,a);s.title.indexOf(a)>-1?t.push(s):e&&e.length&&(s.children=e,t.push(s))}return t.length?t:[]};return U({init:e=>{ae(e)}}),(e,a)=>{const l=k,t=s("plus-outlined"),f=s("FormOutlined"),h=s("DeleteOutlined"),b=w,U=x,R=E,T=S,H=O,te=C,se=P;return r(),o("div",F,[i("div",I,[i("div",J,[n(l,{value:L.value,"onUpdate:value":a[0]||(a[0]=e=>L.value=e),class:"filter",placeholder:"请输入搜索内容","allow-clear":!0},{prefix:d((()=>[n(p(y))])),_:1},8,["value"])]),0===B.value.length?(r(),u(p(_),{key:0,image:p(_).PRESENTED_IMAGE_SIMPLE},null,8,["image"])):(r(),o("div",K,[n(U,{selectedKeys:V.value,"onUpdate:selectedKeys":a[2]||(a[2]=e=>V.value=e),"show-icon":"","tree-data":B.value,class:"cus-tree","default-expand-all":!0,"default-expanded-keys":W.value,"field-names":A.value,onSelect:le},{title:d((l=>[i("span",{class:"root-tree-item",title:l.title},[l.rootTarget?(r(),o("div",D,[e.hasPerm("sys-bubble-group:add")?(r(),u(t,{key:0,title:"添加",onClick:c((e=>(e=>{X.value.parents=e.id,X.value.name="",$.value=!0})(l)),["stop"])},null,8,["onClick"])):m("",!0)])):(r(),o("div",G,[e.hasPerm("sys-bubble-group:edit")?(r(),u(f,{key:0,title:"编辑",onClick:c((e=>(e=>{Y.value.name=e.title,Y.value.id=e.id,Y.value.pid=e.pid,Y.value.sort=100,Q.value=!0})(l)),["stop"])},null,8,["onClick"])):m("",!0),i("span",null,[e.hasPerm("sys-bubble-group:delete")?(r(),u(b,{key:0,title:"确认删除当前分类？",placement:"topRight",onConfirm:c((e=>(e=>{const a={id:e.id};j(a).then((e=>{200===e.code?(g("success","图标分类删除成功"),ae("")):g("error",e.message)}))})(l)),["stop"])},{default:d((()=>[i("span",{type:"delete",title:"删除",onClick:a[1]||(a[1]=c((()=>{}),["stop"]))},[n(h,{style:{"padding-left":"10px"}})])])),_:2},1032,["onConfirm"])):m("",!0),a[7]||(a[7]=i("span",null,null,-1))])])),i("span",M,v(l.title),1)],8,N)])),_:1},8,["selectedKeys","tree-data","default-expanded-keys","field-names"])]))]),n(se,{title:"新增分类",width:460,"wrap-class-name":"cus-modal",open:$.value,"mask-closable":!1,onOk:Z,onCancel:a[4]||(a[4]=e=>$.value=!1)},{default:d((()=>[n(te,{ref_key:"categoryFormRef",ref:z,rules:q,model:X.value,"label-align":"left"},{default:d((()=>[n(H,{gutter:24},{default:d((()=>[n(T,{md:20,sm:24},{default:d((()=>[n(R,{name:"name",label:"分类名称："},{default:d((()=>[n(l,{value:X.value.name,"onUpdate:value":a[3]||(a[3]=e=>X.value.name=e),placeholder:"请输入分类名称",maxlength:30},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["open"]),n(se,{title:"编辑分类",width:460,"wrap-class-name":"cus-modal",open:Q.value,"mask-closable":!1,onOk:ee,onCancel:a[6]||(a[6]=e=>Q.value=!1)},{default:d((()=>[n(te,{model:Y.value,rules:q,"label-align":"left"},{default:d((()=>[n(H,{gutter:24},{default:d((()=>[n(T,{md:24,sm:24},{default:d((()=>[n(R,{name:"name",label:"分类名称："},{default:d((()=>[n(l,{value:Y.value.name,"onUpdate:value":a[5]||(a[5]=e=>Y.value.name=e),placeholder:"请输入分类名称",maxlength:30},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["open"])])}}}),[["__scopeId","data-v-f8ee6d2e"]]);export{R as default};
