import{d as e,o as a,l,j as s,r as i,b9 as t,S as o,U as n,am as r,bk as u,c as p,u as v,Z as d,bJ as c,G as m,q as y,F as g,b7 as j,B as b}from"./@vue-DgI1lw0Y.js";import{k as w}from"./model-BUBDdBL2.js";import{a as f,U as h,u as x}from"./main-DE7o6g98.js";import{c as _}from"./clipboard-Dv7Qpqbb.js";import{R as k}from"./vue-pick-colors-CjUSS-xa.js";import{f as T,r as C,aa as I,h as N,g as z}from"./ant-design-vue-DW0D0Hn-.js";import{_ as B}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./@popperjs-CFrBDmKk.js";import"./js-binary-schema-parser-G48GG52R.js";const G={class:"model-detail keep-px"},H={class:"left"},A={class:"describe"},F={class:"handel"},$={class:"right"},S={class:"obj-box"},P={class:"col"},U={class:"val"},E={class:"col"},L={class:"val"},R={class:"col"},M={class:"val"},V={class:"col"},O={class:"val"},q={class:"anm-tab"},D={key:0,class:"tab-content"},J={class:"col1"},K={class:"col2"},Z=["onClick"],W={key:1,class:"no-data"},Q={class:"marker-btn"},X={class:"marker-btn-item"},Y={class:"marker-btn-item"},ee={class:"marker-btn-item"},ae=B(e({__name:"Detail",props:{previewId:{type:String,default:""},previewType:{type:String,default:""},fileSize:{type:String,default:""}},setup(e){const B=f(),ae=e;a((()=>{ue()})),l((()=>{le=null,se=null}));let le=null,se=null;const ie=s((()=>B.modeName)),te=i("#000"),oe=i(""),ne=i([]),re=i({}),ue=()=>{w({modelId:ae.previewId}).then((async e=>{if(200===e.code){re.value=e.data,se=null,le=null,1===h().thingjsVersion?(1===h().thingjsNoAuth&&(window.THING.__auth_server_URL__=`${window.baseConfig.appApi}/licensex/license-info/thing-js-api`,window.THING.__force_check_auth_result__=!0),THING.disableLoginRequested=!0,se=new window.THING.App({container:"div3dModal"})):(await THING.Utils.login({method:"POST",url:`${window.baseConfig.appApi}/licensex/license-info/thing-js-2-api`,wasmRootPath:"/base/libs/wasm"}),se=new THING.App({container:document.getElementById("div3dModal")||void 0,compatibleOptions:{rendering:!0},isEditor:!1})),se.background=te.value,window.app=se;const a=1===h().thingjsVersion?"Thing":"Entity";oe.value=`${window.baseConfig.previewResourceUrl}${e.data.viewUrl}`,le=se.create({type:"Particles"===ae.previewType?"ParticleSystem":a,id:"exp",name:"expName",url:`${window.baseConfig.previewResourceUrl}${e.data.viewUrl}`,position:[0,0,0],complete:({object:e})=>{"Particles"===ae.previewType?se.camera.flyTo({target:e.position,time:0}):1===h().thingjsVersion?se.camera.flyTo({object:e,time:0}):se.camera.flyTo({target:e,time:0}),ne.value=e.animationNames||[]}})}}))},pe=i(""),ve=i(1),de=i("no"),ce=i(!1),me=e=>{ve.value=e,pe.value&&le.playAnimation({name:pe.value,speed:ve.value,loopType:window.THING.LoopType[de.value],reverse:ce.value})},ye=e=>{de.value=e,pe.value&&le.playAnimation({speed:ve.value,name:pe.value,loopType:window.THING.LoopType[de.value],reverse:ce.value})},ge=e=>{ce.value=e,pe.value&&le.playAnimation({speed:ve.value,name:pe.value,loopType:window.THING.LoopType[de.value],reverse:ce.value})},je=e=>{const a=Number(e);let l="";l=a<102.4?`${a.toFixed(2)}B`:a<104857.6?`${(a/1024).toFixed(2)}KB`:`${(a/1048576).toFixed(2)}MB`;const s=`${l}`,i=s.indexOf(".");return"00"===s.substr(i+1,2)?s.substring(0,i)+s.substr(i+3,2):s},be=i(!1),we=()=>{be.value=!be.value,le.style.wireframe=be.value},fe=()=>{_.copy(oe.value),x("success","复制成功")},he=()=>{se.camera.fit(le)},xe=i(!1),_e=()=>{xe.value=!xe.value,1===h().thingjsVersion?xe.value?ke.value?(Te(),le.style.boundingBox=!0,le.style.boundingBoxColor="#F6782C",Te()):(le.style.boundingBox=!0,le.style.boundingBoxColor="#F6782C"):le.style.boundingBox=!1:xe.value?(le.helper.boundingBox.visible=!0,le.helper.boundingBox.color="#F6782C"):le.helper.boundingBox.visible=!1},ke=i(!1),Te=()=>{ke.value=!ke.value,ke.value?1===h().thingjsVersion?le.style.axisHelper=!0:le.helper.axes=!0:1===h().thingjsVersion?le.style.axisHelper=!1:le.helper.axes=!1};return(e,a)=>{const l=t("IconFont"),s=T,i=I,w=z,f=N;return n(),o("div",G,[r("div",H,[a[3]||(a[3]=r("div",{id:"div3dModal",class:"div3d"},null,-1)),r("div",A,[r("div",null,"长："+u(re.value&&re.value.size?re.value.size[0].toFixed(2):"未知")+"m",1),r("div",null,"宽："+u(re.value&&re.value.size?re.value.size[1].toFixed(2):"未知")+"m",1),r("div",null,"高："+u(re.value&&re.value.size?re.value.size[2].toFixed(2):"未知")+"m",1)]),r("div",F,[p(v(k),{value:te.value,"onUpdate:value":a[0]||(a[0]=e=>te.value=e),class:"check-color",style:{"margin-right":"10px"},theme:v(ie),format:"hex",size:20,title:"切换背景色",onChange:a[1]||(a[1]=e=>{return a=e,te.value=a,void(se&&(se.background=a));var a})},null,8,["value","theme"]),p(l,{title:"复制地址",type:"icon-copy",style:{"margin-right":"10px"},onClick:fe}),p(l,{title:"定位",type:"icon-jujiao",style:{"margin-right":"10px"},onClick:he}),p(l,{title:"线框模式",class:d(be.value?"active":""),type:"icon-area",style:{"margin-right":"10px"},onClick:we},null,8,["class"]),p(l,{title:"包围盒",class:d(xe.value?"active":""),type:"icon-liti",style:{"margin-right":"10px"},onClick:_e},null,8,["class"]),p(l,{title:"坐标轴",class:d(ke.value?"active":""),type:"icon-sanwei",onClick:Te},null,8,["class"])])]),r("div",$,[r("div",S,u(re.value.title),1),a[18]||(a[18]=r("div",{class:"line"},null,-1)),a[19]||(a[19]=r("div",{class:"title"},"基本信息",-1)),r("div",P,[a[4]||(a[4]=r("span",{class:"lab"},"ID",-1)),p(s,null,{title:c((()=>[m(u(ae.previewId),1)])),default:c((()=>[r("span",U,u(ae.previewId),1)])),_:1})]),r("div",E,[a[5]||(a[5]=r("span",{class:"lab"},"文件大小",-1)),r("span",L,u(je(ae.fileSize)),1)]),r("div",R,[a[6]||(a[6]=r("span",{class:"lab"},"面数",-1)),r("span",M,u(re.value&&re.value.faces?re.value.faces:"未知"),1)]),r("div",V,[a[7]||(a[7]=r("span",{class:"lab"},"更新时间",-1)),r("span",O,u(re.value.updateTime),1)]),a[20]||(a[20]=r("div",{class:"line"},null,-1)),r("div",null,[a[17]||(a[17]=r("div",{class:"title"},"动画信息",-1)),r("div",q,[a[8]||(a[8]=r("div",{class:"tab-row tab-row-header"},[r("span",{class:"col1"},"动画名称"),r("span",{class:"col2"},"操作")],-1)),ne.value.length?(n(),o("div",D,[(n(!0),o(g,null,j(ne.value,((e,l)=>(n(),o("div",{key:l,class:"tab-row"},[r("div",J,u(e),1),r("div",K,[pe.value===e&&"no"!==de.value?(n(),o("div",{key:0,class:"stop",title:"播放动画",onClick:a[2]||(a[2]=e=>(pe.value="",void le.stopAnimation()))})):(n(),o("div",{key:1,class:"paly",title:"播放动画",onClick:a=>{return l=e,pe.value=l,void le.playAnimation({name:pe.value,speed:ve.value,loopType:window.THING.LoopType[de.value],reverse:ce.value});var l}},null,8,Z))])])))),128))])):(n(),o("div",W,[p(v(C),{image:v(C).PRESENTED_IMAGE_SIMPLE},null,8,["image"])]))]),y(r("div",Q,[r("div",X,[a[9]||(a[9]=m("播放速度：")),p(i,{class:"animation_speed","default-value":1,onChange:me})]),r("div",Y,[a[13]||(a[13]=m(" 循环类型： ")),p(f,{size:"small",class:"marker-select","default-value":"no",style:{width:"160px"},onChange:ye},{default:c((()=>[p(w,{value:"no"},{default:c((()=>a[10]||(a[10]=[m(" 不循环 ")]))),_:1}),p(w,{value:"Repeat"},{default:c((()=>a[11]||(a[11]=[m(" 重复 ")]))),_:1}),p(w,{value:"PingPong"},{default:c((()=>a[12]||(a[12]=[m(" 往复 ")]))),_:1})])),_:1})]),r("div",ee,[a[16]||(a[16]=m(" 倒播动画： ")),p(f,{size:"small",class:"marker-select","default-value":"false",style:{width:"160px"},onChange:ge},{default:c((()=>[p(w,{value:"false"},{default:c((()=>a[14]||(a[14]=[m(" 不倒播 ")]))),_:1}),p(w,{value:"true"},{default:c((()=>a[15]||(a[15]=[m(" 倒播 ")]))),_:1})])),_:1})])],512),[[b,ne.value.length]])])])])}}}),[["__scopeId","data-v-f67ea512"]]);export{ae as default};
