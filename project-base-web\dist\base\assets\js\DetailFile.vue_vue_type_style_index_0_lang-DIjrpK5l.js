import{F as e,c as i,M as l}from"./ant-design-vue-DW0D0Hn-.js";import{d as a,r as f,a as s,V as t,U as n,bJ as d,c as u,am as o,bk as m,n as r}from"./@vue-DgI1lw0Y.js";const c={style:{"line-height":"38px"}},h={style:{"line-height":"38px"}},b={style:{"line-height":"38px"}},p={style:{"line-height":"38px"}},k={style:{"line-height":"38px"}},g=a({__name:"DetailFile",setup(a,{expose:g}){const x=f(!1),v=f(),_=s({id:"",fileBucket:"",fileOriginName:"",fileSuffix:"",fileSizeInfo:""}),S=()=>{v.value.resetFields(),x.value=!1};return g({init:e=>{x.value=!0,r((()=>{v.value.resetFields(),_.id=e.id,_.fileBucket=e.fileBucket,_.fileOriginName=e.fileOriginName,_.fileSuffix=e.fileSuffix,_.fileSizeInfo=e.fileSizeInfo}))}}),(a,f)=>{const s=i,r=e,g=l;return n(),t(g,{width:676,title:"文件信息详情","wrap-class-name":"cus-modal",footer:null,open:x.value,"mask-closable":!1,onCancel:S},{default:d((()=>[u(r,{ref_key:"formRef",ref:v,model:_,"label-align":"left",class:"checkForm"},{default:d((()=>[u(s,{name:"fileBucket",label:"文件仓库","has-feedback":""},{default:d((()=>[o("div",c,m(_.fileBucket),1)])),_:1}),u(s,{name:"fileOriginName",label:"文件名称","has-feedback":""},{default:d((()=>[o("div",h,m(_.fileOriginName),1)])),_:1}),u(s,{name:"fileSuffix",label:"文件后缀","has-feedback":""},{default:d((()=>[o("div",b,m(_.fileSuffix),1)])),_:1}),u(s,{name:"fileSizeInfo",label:"文件大小","has-feedback":""},{default:d((()=>[o("div",p,m(_.fileSizeInfo),1)])),_:1}),u(s,{name:"id",label:"唯一标识","has-feedback":"",style:{"padding-bottom":"24px"}},{default:d((()=>[o("div",k,m(_.id),1)])),_:1})])),_:1},8,["model"])])),_:1},8,["open"])}}});export{g as _};
