import e from"./ACron-Cdy8z9jI.js";import{u as a}from"./main-DE7o6g98.js";import{e as s}from"./scheduledTask-D-uirQox.js";import{S as t,F as r,n as o,d as l,c as i,I as m,f as n,o as p,p as u,M as d}from"./ant-design-vue-DW0D0Hn-.js";import{Q as c}from"./@ant-design-tBRGNTkq.js";import{d as j,a as v,r as f,V as b,U as k,bJ as g,c as h,S as y,F as _,b7 as C,u as N,am as x,n as A}from"./@vue-DgI1lw0Y.js";import{_ as w}from"./vue-qr-6l_NUpj8.js";import"./Crontab-CnHNK4l5.js";import"./Crontab-Second-Cb5dnRiU.js";import"./@babel-B4rXMRun.js";import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./dayjs-D9wJ8dSB.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./Crontab-Min-CvbMKa3m.js";import"./Crontab-Hour-944bNSow.js";import"./Crontab-Day-DUlTjVdV.js";import"./Crontab-Mouth-8HJdgSDk.js";import"./Crontab-Week-D6Gd-d6p.js";import"./Crontab-Year-Df77Ipqs.js";import"./Crontab-Result.vue_vue_type_script_setup_true_lang-DpqA3H9M.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";/* empty css                      */const S={class:"icon_open"},F=w(j({__name:"EditForm",emits:["ok"],setup(j,{expose:w,emit:F}){const U=F;w({init:e=>{V.value=!0,A((()=>{if(J.value.resetFields(),e){O.timerName=e.timerName,O.id=e.id,O.jobStatus=e.jobStatus,O.cron=e.cron,O.remark=e.remark,O.actionClass=e.actionClass;try{O.taskArgs=JSON.parse(e.taskArgs),H=JSON.parse(e.taskForm),H.forEach((e=>{O[e.model]=O.taskArgs[e.model]||e.options.defaultValue}))}catch(a){}}}))}});const O=v({id:"",jobStatus:"",timerName:"",cron:"",remark:"",taskArgs:"",actionClass:""}),J=f(),q={timerName:[{required:!0,message:"请输入任务名称！"}],cron:[{required:!0,message:"请输入正确的cron表达式"}]};let H=v([]);const M=e=>{void 0!==e&&A((()=>{O.cron=e}))},V=f(!1),$=f(!1),z=()=>{$.value=!0,J.value.validate().then((async e=>{const t={id:O.id,cron:O.cron,jobStatus:O.jobStatus,remark:O.remark,timerName:O.timerName};Object.assign(t,{taskArgs:JSON.stringify(O.taskArgs)});try{const e=await s(t);$.value=!1,e.success?(a("success","编辑成功"),V.value=!1,U("ok",t),J.value.resetFields()):a("error",`编辑失败：${e.message}`)}catch(r){a("error",`编辑失败：${r.message}`)}finally{$.value=!1}}))},E=()=>{$.value||(J.value.resetFields(),V.value=!1,$.value=!1)};return(a,s)=>{const j=m,v=i,f=l,A=n,w=p,F=u,U=o,I=r,R=t,W=d;return k(),b(W,{width:676,title:"编辑定时任务","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:V.value,"confirm-loading":$.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:z,onCancel:E},{default:g((()=>[h(R,{spinning:$.value},{default:g((()=>[h(I,{ref_key:"formRef",ref:J,model:O,rules:q,"label-align":"left"},{default:g((()=>[h(U,{gutter:24},{default:g((()=>[h(f,{md:12,sm:24},{default:g((()=>[h(v,{name:"timerName",label:"任务名称","has-feedback":""},{default:g((()=>[h(j,{value:O.timerName,"onUpdate:value":s[0]||(s[0]=e=>O.timerName=e),placeholder:"请输入任务名称",disabled:"",maxlength:30},null,8,["value"])])),_:1})])),_:1}),h(f,{md:12,sm:24},{default:g((()=>[h(v,{name:"cron",label:"任务表达式","has-feedback":""},{default:g((()=>[h(e,{ref:"innerVueCron",cron:O.cron,onChange:M},null,8,["cron"])])),_:1})])),_:1}),h(f,{md:12,sm:24},{default:g((()=>[(k(!0),y(_,null,C(N(H),((e,a)=>(k(),b(v,{key:a,"has-feedback":"",label:e.label,name:N(H).map((e=>e.model))},{default:g((()=>[x("span",S,[h(A,{title:e.help,placement:"left"},{default:g((()=>[h(N(c),{style:{color:"#ef7b1a"}})])),_:2},1032,["title"])]),"number"===e.type?(k(),b(w,{key:0,value:O.taskArgs[e.model],"onUpdate:value":a=>O.taskArgs[e.model]=a,style:{width:"100%"},min:0,max:e.options.max,placeholder:e.options.placeholder},null,8,["value","onUpdate:value","max","placeholder"])):(k(),b(j,{key:1,value:O.taskArgs[e.model],"onUpdate:value":a=>O.taskArgs[e.model]=a,type:e.type,placeholder:e.options.placeholder},null,8,["value","onUpdate:value","type","placeholder"]))])),_:2},1032,["label","name"])))),128))])),_:1}),h(f,{md:24,sm:24},{default:g((()=>[h(v,{name:"remark",label:"备注"},{default:g((()=>[h(F,{value:O.remark,"onUpdate:value":s[1]||(s[1]=e=>O.remark=e),disabled:"",rows:4,placeholder:"请输入备注",maxlength:80},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-d9ccf9c1"]]);export{F as default};
