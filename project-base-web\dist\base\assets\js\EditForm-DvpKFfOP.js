import{d as e,r as a,j as t,w as s,o as l,n,l as i,S as o,am as r,bk as c,c as u,Z as p,bJ as d,q as m,B as v,u as w,al as f,b9 as g,V as h,G as y,A as $,U as j}from"./@vue-DgI1lw0Y.js";import{v as b}from"./@ti-cli-Z3vwStYr.js";import{c as T}from"./coordtransform-CEi8OirC.js";import{_ as I}from"./SelectColor.vue_vue_type_script_setup_true_lang-BdCAiBbd.js";import{u as _}from"./map-DByF_6EZ.js";import C from"./AssociatedTwins-BINKmP0O.js";import{U as P,x as O,b as k,u as N,a1 as S,w as D,a2 as E,ab as U}from"./main-DE7o6g98.js";import{d as x}from"./attachmentManage-CasMLJka.js";import{_ as V}from"./lodash-BZHY5H3K.js";import{D as H}from"./dragMove-COHxzxUH.js";import{a as A}from"./axios-ChCdAMPF.js";import{u as J}from"./vue3-cookies-ll55Epyr.js";import{B as F,F as G,c as z,o as L,f as M,a3 as W,I as q}from"./ant-design-vue-DW0D0Hn-.js";import{_ as B}from"./vue-qr-6l_NUpj8.js";import"./@babel-B4rXMRun.js";import"./vue-CqsM5HEV.js";import"./vue-pick-colors-CjUSS-xa.js";import"./@popperjs-CFrBDmKk.js";import"./pinia-iScrtxv6.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./mitt-CNZ6avp8.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const R={class:"map-twin keep-px"},X={id:"map-edit-twin-head",class:"add-twin-top"},Z={class:"title"},K={class:"btn-wrap"},Y={class:"add-twin-bottom"},Q={class:"tab-wrap"},ee={class:"tab-content"},ae={class:"twin-prop-content"},te={class:"special-input-wrap"},se={class:"special-input-item"},le={class:"special-input-item"},ne={class:"special-input-item"},ie={class:"special-input-wrap"},oe={class:"special-input-item"},re={class:"special-input-item"},ce={class:"special-input-item"},ue={class:"special-input-wrap"},pe={class:"special-input-item"},de={class:"special-input-item"},me={class:"special-input-item"},ve={class:"special-input-wrap"},we={class:"special-input-item"},fe={class:"special-input-item"},ge={class:"twin-custom-prop"},he={class:"tab-btn"},ye={key:0,class:"notice-icon"},$e=B(e({__name:"EditForm",props:{currentTab:{type:Object,default:()=>{}}},emits:["update"],setup(e,{emit:B}){const $e=A.defaults.headers.common.Tenant,{cookies:je}=J(),be=P(),Te=e,Ie=B,_e=a(1),Ce=()=>{_e.value=1===_e.value?2:1};a(!0);const Pe=a(""),Oe=a(""),ke=()=>{window.mapPlacementIns.twinObject.userData.eye="",window.mapPlacementIns.twinObject.userData.target="",window.mapPlacementIns.twinObject.userData.distance=0,Pe.value="",Oe.value="",N("success","清除视角成功，保存后生效"),Ye.value=!0},Ne=()=>{Pe.value=JSON.stringify(window.app.camera.position),Oe.value=JSON.stringify(window.app.camera.target),window.mapPlacementIns.twinObject.userData.eye=JSON.stringify(window.app.camera.position),window.mapPlacementIns.twinObject.userData.target=JSON.stringify(window.app.camera.target),window.mapPlacementIns.twinObject.userData.distance=window.app.camera.distance,N("success","点位视角设置成功，保存后生效"),Ye.value=!0},Se=async e=>{try{await x({ids:e})}catch(a){}},De=async e=>{const a=new FormData;a.append("file",e),a.append("bucketName","twinfile");return(await A.post(`${window.baseConfig.appApi}/systemx/sys-file-info/upload`,a,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${je.get(`ACCESS_TOKEN_${$e}`)}`,Tenant:$e,"X-Security-FreshToken":O()}})).data},Ee=a({action:`${window.baseConfig.appApi}/systemx/sys-file-info/upload`,headers:{Authorization:`Bearer ${je.get(`ACCESS_TOKEN_${$e}`)}`,Tenant:$e,"X-Security-FreshToken":O()},generateRequestId:O,data:{bucketName:"twinfile"},delayUpload:!0}),Ue=a(""),xe=k(),Ve=a(),He=_(),Ae=t((()=>He.twinProp)),Je=t((()=>He.defaultData)),Fe=t((()=>He.twinData));Ue.value=He.twinProp.customColor;const Ge=a({});(()=>{const e=JSON.parse(Fe.value.form);if("{}"!==JSON.stringify(Je.value)&&Je.value){const a=["input","number","textarea","radio","date","editor"];e.list.forEach((e=>{if(a.includes(e.type)&&void 0!==Je.value[e.model]&&(e.options.defaultValue=Je.value[e.model]),"number"===e.type&&Je.value[e.model]&&(e.options.defaultValue=Je.value[e.model]),"checkbox"===e.type)try{Je.value[e.model]&&(e.options.defaultValue=JSON.parse(Je.value[e.model]))}catch(t){e.options.defaultValue=[]}if("select"===e.type&&Je.value[e.model])if(e.options.multiple)try{e.options.defaultValue=JSON.parse(Je.value[e.model])}catch(t){e.options.defaultValue=[]}else e.options.defaultValue=Je.value[e.model];if("uploadFile"===e.type||"releative"===e.type)try{e.options.defaultValue=JSON.parse(Je.value[e.model])}catch(t){e.options.defaultValue=[]}})),Ge.value=e}else Ge.value=e})();const ze=a(2),Le=e=>{ze.value=e},Me=()=>{window.mapPlacementIns.reset(!0),window.mapDrawInstance.reset(),He.updateShowEditPanel(!1),Ie("update")};a(330);const We=()=>{window.mapPlacementIns.flyToCurrentThing()},qe=(e,a)=>{Be(e,a)},Be=V.debounce(((e,a)=>{if("offsetHeight"===a){const e={type:a,data:Ae.value.offsetHeight};window.mapPlacementIns.updateThing(e)}else if("angles"===a){const e={type:a,data:Ae.value.angles};window.mapPlacementIns.updateThing(e)}else if("scale"===a){1===_e.value&&(Ae.value.scale=[Ae.value.scale[0],Ae.value.scale[0],Ae.value.scale[0]]);const e={type:a,data:Ae.value.scale};window.mapPlacementIns.updateThing(e)}else if("customColor"===a){const t={type:a,data:e};window.mapPlacementIns.updateThing(t)}}),500),Re=()=>{const e=Fe.value.code;Ve.value.getData().then((async a=>{var t,s,l;let n=[];try{let i;n=await(async e=>{var a;if(!(null==(a=Ge.value)?void 0:a.list))return[];const t=[];try{for(const a of Ge.value.list)if("uploadFile"===a.type&&e[a.model]){const l=e[a.model];for(let e=0;e<l.length;e++){const a=l[e];if(a.response&&a.response.realFile)try{const e=await De(a.response.realFile);t.push(e.data),a.response={data:e.data,status:"done"},a.status="done"}catch(s){throw a.status="error",t.length>0&&await Se(t),new Error(`文件 ${a.name} 上传失败`)}}}return t}catch(s){throw t.length>0&&await Se(t),s}})(a),Ge.value.list.forEach((e=>{if("uploadFile"===e.type){const t=[];a[e.model].forEach((e=>{var a;const s=e.name.split("."),l=s.length?s[s.length-1]:"";t.push({type:"file",name:e.name,status:e.status,uid:e.uid,url:e.url||"",dataId:(null==(a=e.response)?void 0:a.data)||e.data,suffix:l})})),a[e.model]=t}}));let o=0,r=[0,0,0],c=[1,1,1],u={};const p=window.mapPlacementIns.twinObject;if(1===be.thingjsVersion){const[e,a,t]=CMAP.Util.convertWorldToLonlat(p.position);if("POINT"===Fe.value.dataType)i=[e,a];else{const e=window.mapPlacementIns.originTwinParam.position,a=p.position,t=THING.Math.subVector(a,e),s=p.coordinates,l=[];s.forEach((e=>{const a=CMAP.Util.convertLonlatToWorld(e,0),s=THING.Math.addVector(a,t),[n,i,o]=CMAP.Util.convertWorldToLonlat(s);l.push([n,i])})),i=l}o=t}else{const[e,a,t]=THING.EARTH.Utils.convertWorldToLonlat(p.position);if("POINT"===Fe.value.dataType)i=[e,a];else{const e=window.mapPlacementIns.originTwinParam.position,a=p.position,t=THING.MathUtils.subVector(a,e),s=p.coordinates,l=[];s.forEach((e=>{const a=THING.EARTH.Utils.convertLonlatToWorld(e,0),s=THING.MathUtils.addVector(a,t),[n,i,o]=THING.EARTH.Utils.convertWorldToLonlat(s);l.push([n,i])})),i=l}o=t}r=p.angles,c=p.scale,u=p.userData;let d=[];d="POINT"===Fe.value.dataType?[i]:(Fe.value.dataType,i);const m={gis_height:o.toFixed(2)},v={gis_height:o.toFixed(2),angles:r,scale:c,eye:(null==(t=p.userData)?void 0:t.eye)||null,target:(null==(s=p.userData)?void 0:s.target)||null,distance:(null==(l=p.userData)?void 0:l.distance)||null,engine:be.thingjsVersion},w=[],f=[];"GCJ02"===Te.currentTab.coords?d.forEach((e=>{f.push(T.gcj02towgs84(e[0],e[1])),w.push(e)})):"WGS84"===Te.currentTab.coords&&d.forEach((e=>{w.push(T.wgs84togcj02(e[0],e[1])),f.push(e)})),"POINT"===Fe.value.dataType?(m.wgs84_position=JSON.stringify(f[0]),m.gcj02_position=JSON.stringify(w[0])):(Fe.value.dataType,m.wgs84_position=JSON.stringify(f),m.gcj02_position=JSON.stringify(w)),v.customColor=Ue.value,u.asset&&(m.data_source="ASSET");const g={...a,...m,position_body:JSON.stringify(v)};Je.value.uuid?S({uuid:Je.value.uuid,twinClassCode:e,formData:{...g}}).then((()=>{D({[e]:{uuid:Je.value.uuid,...g,update_user:xe.userInfo.id}}).then((async e=>{200===e.code?(N("success","点位修改成功"),window.mapPlacementIns.reset(),window.mapDrawInstance.reset(),He.updateShowEditPanel(!1),Ie("update")):(N("error",`修改失败${e.msg}`),n.length>0&&await Se(n))})).catch((async e=>{N("error","修改失败"),n.length>0&&await Se(n)}))})).catch((async e=>{N("error","修改失败"),n.length>0&&await Se(n)})):S({twinClassCode:e,formData:{...g}}).then((()=>{E({[e]:{...g,data_source:"TWIN",create_user:xe.userInfo.id}}).then((async e=>{200===e.code?(N("success","新增成功"),window.mapPlacementIns.reset(),window.mapDrawInstance.reset(),He.updateShowEditPanel(!1),Ie("update")):(N("error",`新增失败${e.msg}`),n.length>0&&await Se(n))})).catch((async e=>{N("error","新增失败"),n.length>0&&await Se(n)}))})).catch((async e=>{N("error","新增失败"),n.length>0&&await Se(n)}))}catch(i){N("error","文件上传失败，请重试")}}))},Xe=()=>{U({twinClassCode:Fe.value.code,updateData:{gis_height:Ae.value.offsetHeight,engine:be.thingjsVersion}}).then((e=>{200===e.code&&(N("success","离地高度同步成功"),e.data.forEach((e=>{const a=window.app.query(`#${e}`)[0];a&&window.mapPlacementIns.twinObject.id!==a.id&&(a.offsetHeight=Ae.value.offsetHeight)})),window.mapPlacementIns.updateOriginTwinParam(),Ie("update"))}))},Ze=()=>{U({twinClassCode:Fe.value.code,updateData:{angles:Ae.value.angles,engine:be.thingjsVersion}}).then((e=>{200===e.code&&(N("success","角度同步成功"),e.data.forEach((e=>{const a=window.app.query(`#${e}`)[0];a&&window.mapPlacementIns.twinObject.id!==a.id&&(a.angles=Ae.value.angles)})),window.mapPlacementIns.updateOriginTwinParam(),Ie("update"))}))},Ke=()=>{U({twinClassCode:Fe.value.code,updateData:{scale:Ae.value.scale,engine:be.thingjsVersion}}).then((e=>{200===e.code&&(N("success","缩放同步成功"),e.data.forEach((e=>{const a=window.app.query(`#${e}`)[0];a&&(a.scale=Ae.value.scale,2===be.thingjsVersion&&window.mapPlacementIns.twinObject.id!==a.id&&window.mapPlacementIns.setMarker(a))})),window.mapPlacementIns.updateOriginTwinParam(),Ie("update"))}))},Ye=a(!1);s((()=>Ae.value),(()=>{var e,a;Pe.value=null==(e=window.mapPlacementIns.twinObject)?void 0:e.userData.eye,Oe.value=null==(a=window.mapPlacementIns.twinObject)?void 0:a.userData.target,Ye.value=!0}),{deep:!0});const Qe=e=>{27===e.keyCode&&Me()};let ea=null;return l((()=>{const e=document.querySelector("#map-place-twin-modal"),a=document.querySelector("#map-edit-twin-head"),t=document.body;ea=new H(a,t,e,1),document.addEventListener("keyup",Qe),n((()=>{var e,a;Pe.value=null==(e=window.mapPlacementIns.twinObject)?void 0:e.userData.eye,Oe.value=null==(a=window.mapPlacementIns.twinObject)?void 0:a.userData.target}))})),i((()=>{document.removeEventListener("keyup",Qe),ea&&ea.destroy()})),(e,a)=>{const t=g("IconFont"),s=F,l=g("unlock-outlined"),n=g("lock-outlined"),i=L,T=g("interaction-outlined"),_=M,P=z,O=W,k=q,N=g("eye-invisible-outlined"),S=G;return j(),o("div",R,[r("div",X,[r("span",Z,c(Je.value.uuid?"修改":"新增")+c(Fe.value.name),1),r("div",K,[u(s,{class:p(["btn",{active:Pe.value&&Oe.value}]),title:"设置点位视角",onClick:Ne},{icon:d((()=>[u(t,{title:"设置点位视角",class:"func-icon",type:"icon-view"})])),_:1},8,["class"]),u(s,{class:"btn",title:"定位",onClick:We},{icon:d((()=>[u(t,{class:"func-icon",type:"icon-location"})])),_:1}),u(s,{class:"btn",title:"关闭",onClick:Me},{icon:d((()=>[u(t,{title:"关闭",class:"func-icon",type:"icon-close"})])),_:1})])]),r("div",Y,[r("div",Q,[r("div",{class:p(["tab-item",{active:2===ze.value}]),onClick:a[0]||(a[0]=e=>Le(2))},"字段属性",2),r("div",{class:p(["tab-item",{active:1===ze.value}]),onClick:a[1]||(a[1]=e=>Le(1))},"设置",2),a[24]||(a[24]=r("div",{class:"tab-item"},null,-1))]),r("div",ee,[m(r("div",ae,[u(S,{"label-align":"left",ref:"twinPropRef",model:Ae.value,class:"twin-prop-form keep-px"},{default:d((()=>[u(P,{class:"special-form-item"},{label:d((()=>[a[25]||(a[25]=y(" 放大倍数")),2===_e.value?(j(),h(l,{key:0,title:"非等比缩放",style:{"margin-left":"3px","font-size":"12px",cursor:"pointer"},onClick:$(Ce,["stop"])})):f("",!0),1===_e.value?(j(),h(n,{key:1,title:"等比缩放",style:{"margin-left":"3px","font-size":"12px",cursor:"pointer"},onClick:$(Ce,["stop"])})):f("",!0)])),default:d((()=>[r("div",te,[r("div",se,[u(i,{value:Ae.value.scale[0],"onUpdate:value":a[2]||(a[2]=e=>Ae.value.scale[0]=e),formatter:e=>`${Number(e)}`.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),parser:e=>e.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),step:"0.1",class:"special-input",min:.01,max:1e3,onChange:a[3]||(a[3]=e=>qe(e,"scale"))},null,8,["value","formatter","parser"]),a[26]||(a[26]=r("span",{class:"input-notice"},"X",-1))]),r("div",le,[u(i,{value:Ae.value.scale[1],"onUpdate:value":a[4]||(a[4]=e=>Ae.value.scale[1]=e),formatter:e=>`${e}`.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),parser:e=>e.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),step:"0.1",min:.01,disabled:1===_e.value,max:1e3,class:"special-input",onChange:a[5]||(a[5]=e=>qe(e,"scale"))},null,8,["value","formatter","parser","disabled"]),a[27]||(a[27]=r("span",{class:"input-notice"},"Y",-1))]),r("div",ne,[u(i,{value:Ae.value.scale[2],"onUpdate:value":a[6]||(a[6]=e=>Ae.value.scale[2]=e),disabled:1===_e.value,min:.01,max:1e3,formatter:e=>`${e}`.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),parser:e=>e.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),step:"0.1",class:"special-input",onChange:a[7]||(a[7]=e=>qe(e,"scale"))},null,8,["value","disabled","formatter","parser"]),a[28]||(a[28]=r("span",{class:"input-notice"},"Z",-1))]),u(_,{title:"将该值同步到当前层级所有同类孪生体"},{default:d((()=>[u(s,{class:"sync-btn",onClick:Ke},{icon:d((()=>[u(T)])),_:1})])),_:1})])])),_:1}),u(P,{label:"离地高度(m)"},{default:d((()=>[u(O,{compact:"",class:"normal-group"},{default:d((()=>[u(i,{value:Ae.value.offsetHeight,"onUpdate:value":a[8]||(a[8]=e=>Ae.value.offsetHeight=e),formatter:e=>`${e}`.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),parser:e=>e.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),step:"0.1",min:0,class:"normal-input",onChange:a[9]||(a[9]=e=>qe(e,"offsetHeight"))},null,8,["value","formatter","parser"]),u(_,{title:"将该值同步到当前层级所有同类孪生体"},{default:d((()=>[u(s,{class:"sync-btn",onClick:Xe},{icon:d((()=>[u(T)])),_:1})])),_:1})])),_:1})])),_:1}),u(P,{label:"旋转角度",class:"special-form-item"},{default:d((()=>[r("div",ie,[r("div",oe,[u(i,{value:Ae.value.angles[0],"onUpdate:value":a[10]||(a[10]=e=>Ae.value.angles[0]=e),min:-180,max:180,formatter:e=>`${e}`.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),parser:e=>e.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),step:"0.1",class:"special-input",onChange:a[11]||(a[11]=e=>qe(e,"angles"))},null,8,["value","formatter","parser"]),a[29]||(a[29]=r("span",{class:"input-notice"},"X",-1))]),r("div",re,[u(i,{value:Ae.value.angles[1],"onUpdate:value":a[12]||(a[12]=e=>Ae.value.angles[1]=e),formatter:e=>`${e}`.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),parser:e=>e.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),step:"0.1",class:"special-input",onChange:a[13]||(a[13]=e=>qe(e,"angles"))},null,8,["value","formatter","parser"]),a[30]||(a[30]=r("span",{class:"input-notice"},"Y",-1))]),r("div",ce,[u(i,{value:Ae.value.angles[2],"onUpdate:value":a[14]||(a[14]=e=>Ae.value.angles[2]=e),formatter:e=>`${e}`.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),parser:e=>e.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),step:"0.1",class:"special-input",onChange:a[15]||(a[15]=e=>qe(e,"angles"))},null,8,["value","formatter","parser"]),a[31]||(a[31]=r("span",{class:"input-notice"},"Z",-1))]),u(_,{title:"将该值同步到当前层级所有同类孪生体"},{default:d((()=>[u(s,{class:"sync-btn",onClick:Ze},{icon:d((()=>[u(T)])),_:1})])),_:1})])])),_:1}),u(P,{label:"模型大小",class:"special-form-item"},{default:d((()=>[r("div",ue,[r("div",pe,[u(i,{value:Ae.value.size[0],"onUpdate:value":a[16]||(a[16]=e=>Ae.value.size[0]=e),style:{width:"68px"},min:.01,formatter:e=>`${e}`.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),parser:e=>e.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),step:"0.1",class:"special-input",disabled:!0},null,8,["value","formatter","parser"]),a[32]||(a[32]=r("span",{class:"input-notice"},"长",-1))]),r("div",de,[u(i,{value:Ae.value.size[2],"onUpdate:value":a[17]||(a[17]=e=>Ae.value.size[2]=e),min:.01,style:{width:"68px"},formatter:e=>`${e}`.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),parser:e=>e.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),step:"0.1",disabled:!0,class:"special-input"},null,8,["value","formatter","parser"]),a[33]||(a[33]=r("span",{class:"input-notice"},"宽",-1))]),r("div",me,[u(i,{value:Ae.value.size[1],"onUpdate:value":a[18]||(a[18]=e=>Ae.value.size[1]=e),min:.01,style:{width:"68px"},formatter:e=>`${e}`.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),parser:e=>e.replace(/^(-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),step:"0.1",disabled:!0,class:"special-input"},null,8,["value","formatter","parser"]),a[34]||(a[34]=r("span",{class:"input-notice"},"高",-1))])])])),_:1}),"LINE"===Fe.value.dataType||"SURFACE"===Fe.value.dataType?(j(),h(P,{key:0,label:"选择颜色"},{default:d((()=>[u(I,{value:Ue.value,"onUpdate:value":a[19]||(a[19]=e=>Ue.value=e),onChange:a[20]||(a[20]=e=>qe(e,"customColor"))},null,8,["value"])])),_:1})):f("",!0),u(P,{label:"当前视角",class:"special-form-item"},{default:d((()=>[r("div",ve,[r("div",we,[u(k,{value:Pe.value,"onUpdate:value":a[21]||(a[21]=e=>Pe.value=e),class:"special-input-eye"},null,8,["value"]),a[35]||(a[35]=r("span",{class:"input-notice"},"P",-1))]),r("div",fe,[u(k,{value:Oe.value,"onUpdate:value":a[22]||(a[22]=e=>Oe.value=e),class:"special-input-eye"},null,8,["value"]),a[36]||(a[36]=r("span",{class:"input-notice"},"T",-1))]),u(_,{title:"清除视角"},{default:d((()=>[u(s,{class:"sync-btn",onClick:ke},{icon:d((()=>[u(N)])),_:1})])),_:1})])])),_:1})])),_:1},8,["model"])],512),[[v,1===ze.value]]),m(r("div",ge,[u(w(b.AntGenerateForm),{ref_key:"antGenerateFormRef",ref:Ve,data:Ge.value,"upload-config":Ee.value},{extendContent:d((({element:e,data:a,disable:t,onBack:s})=>[u(C,{element:e,data:a,disable:t,onOnBack:e=>s(e)},null,8,["element","data","disable","onOnBack"])])),_:1},8,["data","upload-config"])],512),[[v,2===ze.value]])]),r("div",he,[u(s,{type:"primary",onClick:a[23]||(a[23]=e=>Re())},{default:d((()=>a[37]||(a[37]=[y("保存")]))),_:1}),Ye.value?(j(),o("div",ye,"*")):f("",!0)])])])}}}),[["__scopeId","data-v-d56719bd"]]);export{$e as default};
