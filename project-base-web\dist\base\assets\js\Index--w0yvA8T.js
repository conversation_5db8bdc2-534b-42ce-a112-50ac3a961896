var t=Object.defineProperty,e=(e,i,s)=>((e,i,s)=>i in e?t(e,i,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[i]=s)(e,"symbol"!=typeof i?i+"":i,s);import{d as i,r as s,a as o,w as n,o as r,S as a,am as l,c as p,q as c,al as h,bk as d,bJ as u,G as m,b9 as g,B as A,u as y,F as b,b7 as w,V as f,U as v}from"./@vue-DgI1lw0Y.js";import{b as j}from"./vue-router-DmgHFAdP.js";import{c as C}from"./coordtransform-CEi8OirC.js";import O from"./Index-D06D2a3q.js";import{i as T,M as L}from"./index-jUuC6oCe.js";import{l as E,u as I}from"./mapManag-wSwfWE2D.js";import{ac as S,Z as k,u as P,ad as M,ae as D,U as H,a3 as B,af as N,Y as G}from"./main-DE7o6g98.js";import{u as R}from"./map-DByF_6EZ.js";import{M as U,T as F,B as x,y as V}from"./ant-design-vue-DW0D0Hn-.js";import{_ as J}from"./vue3-ts-jsoneditor-C_C2tCrf.js";import{a as Q}from"./axios-ChCdAMPF.js";import Z from"./AddMapServe-BHXzsI7m.js";import Y from"./MapLayer-CEyrLa67.js";import W from"./TableContent-BJszrZxP.js";import z from"./Loading-Bd2lTA-M.js";import q from"./EditForm-DvpKFfOP.js";import{h as K}from"./header-logo-BFspEaUW.js";import{_ as X}from"./vue-qr-6l_NUpj8.js";import"./@babel-B4rXMRun.js";import"./useTableScrollY-9oHU_oJI.js";import"./AddEditForm-CmkoLo8T.js";import"./@ti-cli-Z3vwStYr.js";import"./vue-CqsM5HEV.js";import"./attachmentManage-CasMLJka.js";import"./vue3-cookies-ll55Epyr.js";import"./AssociatedTwins-BINKmP0O.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./upload-CsXIzxCg.js";import"./PreviewAndDownload.vue_vue_type_script_setup_true_lang-C0tVYshs.js";import"./PreviewForm.vue_vue_type_script_setup_true_lang-BPt1qaC1.js";import"./ConditionBox-r2AXqrZY.js";import"./no-data-DWJyvDuH.js";import"./Reletive-Bnq70Jw7.js";import"./scene-DnZsitgt.js";import"./qs-Cgg8q2iR.js";import"./side-channel-C0354c6F.js";import"./es-errors-Bq3kx8t5.js";import"./object-inspect-7jlLeo80.js";import"./side-channel-list-9pNTKFEK.js";import"./side-channel-map-qN4s3zsW.js";import"./get-intrinsic-BQNEepf0.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-z0bYe-bF.js";import"./gopd-uUVnxxv0.js";import"./es-define-property-lN4EXf1s.js";import"./has-symbols-CRskrF1U.js";import"./get-proto-nTH32ZvN.js";import"./dunder-proto-W0bZB-Yu.js";import"./call-bind-apply-helpers-CohWVzbO.js";import"./function-bind-Ckw9YnhN.js";import"./hasown-Dpzr2-IR.js";import"./call-bound-BVGeUV4S.js";import"./side-channel-weakmap-dRphfAXb.js";import"./lodash-BZHY5H3K.js";import"./earth_bg-CoeJTRun.js";import"./effectsPack-CMixYyev.js";import"./loading-D1_5edTl.js";import"./SelectColor.vue_vue_type_script_setup_true_lang-BdCAiBbd.js";import"./vue-pick-colors-CjUSS-xa.js";import"./@popperjs-CFrBDmKk.js";import"./dragMove-COHxzxUH.js";const _=class t{constructor(){e(this,"twinObject"),e(this,"mouseOffsetV3"),e(this,"app"),e(this,"prePos"),e(this,"prePosV3"),e(this,"preAngle"),e(this,"pickGround"),e(this,"angleBtnDir"),e(this,"shiftRotateStep",45),this.twinObject=null}onAdd(t){this.twinObject=t,this.app=t.app,this.createPickGround(this.app),this.updatePickGroundPosition(t)}onUpdate(){const e=this.calcBtnPos();return this.twinObject.trigger(t.TRANSFORM_CONTROL_UPDATE,{data:e}),!0}onRemove(){this.twinObject&&this.twinObject.style&&(this.twinObject.style.orientedBoundingBox=!1),this.destroyPickGround()}updatePickGroundPosition(t){this.pickGround&&t&&(this.pickGround.position=t.position)}calcBtnPos(){let t=null;const e=this.twinObject.orientedBoundingBox,i=this.getTargetSize(this.twinObject,e)*(this.twinObject.scale[1]>0?1:-1);if(t=this.app.camera.worldToScreen([this.twinObject.position[0],this.twinObject.position[1]+i/3,this.twinObject.position[2]]),this.angleBtnDir){const e=this.app.camera.worldToScreen(this.twinObject.position),i=THING.Utils.parseVector3([e[0]+45,e[1]-15,0]);i.add(this.angleBtnDir.clone().multiplyScalar(60)),t=i.toArray()}return t}update(t){const e=this,{mode:i,shift:s,x:o,y:n}=t;switch(i){case"translateY":e.moveY(o,n);break;case"translateXZ":e.moveXZ(o,n);break;case"mirror":e.mirror();break;case"rotate":s?e.rotateYStep():e.rotateY()}}moveY(t,e){if(this.prePosV3=null,this.prePos){const i=CMAP.Util.convertWorldToLonlat(this.twinObject.position),s=THING.Math.getDistance(this.twinObject.position,this.app.camera.position),o=(this.prePos[1]-e)*s*5e-4,n=i[2]+o,r=CMAP.Util.convertLonlatToWorld([i[0],i[1]],n);this.twinObject.position=r,this.prePos=[t,e]}else this.prePos=[t,e,0]}moveXZ(t,e){this.prePos||(this.prePos=[t,e]);const i=this.getWorldPos(t,e),s=window.CMAP.Util.convertWorldToLonlat(i),o=CMAP.Util.convertWorldToLonlat(this.twinObject.position),n=CMAP.Util.convertLonlatToWorld([s[0],s[1]],o[2]);this.twinObject.position=n,this.prePos=[t,e]}getTargetSize(t,e){if("GeoLine"===t.type||"GeoPolygon"===t.type)return 0;const i=e.initSize[1],s=t.size[1];let o=null;t.children&&t.children[0]&&(o=t.children[0].size[1]);let n=[];return n=o?[i,s,o]:[i,s],n.sort(((t,e)=>t-e)),n[0]}rotateY(){this.prePosV3=null;const t=this.twinObject.position,e=this.twinObject.orientedBoundingBox,i=this.getTargetSize(this.twinObject,e)/this.twinObject.scale[1],s=this.twinObject.app.camera.worldToScreen([t[0],t[1]+i/2,t[2]]),o=window.THING.Utils.parseVector3(this.app.mousePosition[0],this.app.mousePosition[1],0).sub(window.THING.Utils.parseVector3(s[0],s[1],0)).normalize(),n=window.THING.Utils.parseVector3([0,1,0]),r=this.angleTwoVectorsY(n,o);this.preAngle?(this.twinObject.rotateY(this.preAngle-r),this.preAngle=r):this.preAngle=r,this.angleBtnDir=o}rotateYStep(){const t=this.twinObject.position,e=this.twinObject.orientedBoundingBox.size[1]/this.twinObject.scale[1],i=this.app.camera.worldToScreen([t[0],t[1]+e/2,t[2]]),s=THING.Utils.parseVector3([this.app.mousePosition[0],this.app.mousePosition[1],0]).sub(THING.Utils.parseVector3([i[0],i[1],0])).normalize(),o=THING.Utils.parseVector3([0,1,0]);let n=this.angleTwoVectorsY(o,s);this.preAngle?(n=Math.floor(n/this.shiftRotateStep)*this.shiftRotateStep,this.twinObject.rotateY(this.preAngle-n),this.preAngle=n):this.preAngle=n,this.angleBtnDir=s}mirror(){this.twinObject.angleY+=180}angleTwoVectorsY(t,e){const i=t.angleTo(e),s=(new window.THREE.Vector3).crossVectors(t,e),o=THING.Math.radToDeg(i);return s.z>0?o:360-o}createPickGround(t){this.pickGround=t.create({type:"Plane",width:100,height:100,visible:!1,angles:[90,0,0],localPosition:[0,0,0],parent:this.twinObject}),this.pickGround.visible=!1,this.pickGround.addPickableGround({size:1e6})}destroyPickGround(){this.pickGround&&(this.pickGround.destroy(),this.pickGround=null)}getWorldPos(t,e,i=!1){var s;if(i){const i=this.app.getClientPosition({clientX:t,clientY:e});t=i.x,e=i.y}let o=null==(s=this.pickGround)?void 0:s.pickGroundWorldPosition(t,e);return o||(o=this.app.camera.screenToWorld(t,e)),o}};e(_,"TRANSFORM_CONTROL_UPDATE","transform_control_update");let $=_;const tt=R();let et=null,it=class{constructor(t){e(this,"app"),e(this,"twinObject"),e(this,"transformCtrl"),e(this,"activeMode"),e(this,"updateCb"),e(this,"disableBtns",[]),e(this,"dragDownFlag",!1),e(this,"dragMoveFlag",!1),e(this,"originTwinParam"),e(this,"type",""),this.app=t,this.twinObject=null,this.transformCtrl=null,this.activeMode=""}init(t){this.reset(!0),this.twinObject=t.thing,this.disableBtns=t.disableBtns||[],this.updateCb=t.updateCb,this.type=t.type,this.transformCtrl=this.twinObject.getControl("TransformControl"),this.transformCtrl||(this.transformCtrl=this.twinObject.addControl(new $,"TransformControl")),this.twinObject.on($.TRANSFORM_CONTROL_UPDATE,(t=>{this.setBtnsPos(t)})),this.twinObject.style.outlineColor="#135EBF",this.app.on("mousemove",this.mouseMove.bind(this),"campusPlacement"),this.app.on("mouseup",this.mouseUp.bind(this),"campusPlacement"),et=this.docMouseUp.bind(this),document.addEventListener("mouseup",et),this.resetClickBtns(),this.disableClickBtns(),this.initDragListener();const e={scale:this.twinObject.scale,offsetHeight:CMAP.Util.convertWorldToLonlat(this.twinObject.position)[2],position:this.twinObject.position,size:this.twinObject.size,angles:this.twinObject.angles,customColor:this.twinObject.userData.position_body?JSON.parse(this.twinObject.userData.position_body).customColor:null};this.originTwinParam=JSON.parse(JSON.stringify(e)),tt.updateTwinProp(e),tt.updateDefaultData(this.twinObject.userData),tt.updateShowEditPanel(!0)}updateOriginTwinParam(){const t={scale:this.twinObject.scale,position:this.twinObject.position,offsetHeight:CMAP.Util.convertWorldToLonlat(this.twinObject.position)[2],size:this.twinObject.scale,angles:this.twinObject.angles,customColor:this.twinObject.userData.position_body?JSON.parse(this.twinObject.userData.position_body).customColor:null};this.originTwinParam=JSON.parse(JSON.stringify(t))}resetClickBtns(){if(document.getElementById("placement-btns")){const t=document.getElementsByClassName("placement-btn");for(let e=t.length-1;e>=0;e--){const i=t[e];i.style.visibility="visible",i.style.cursor="pointer",i.style.background="#FFFFFF"}}}initDragListener(){this.app.on("mousedown",(()=>{this.dragDownFlag=!0,this.app.on("mousemove",(()=>{!this.activeMode&&this.dragDownFlag?this.dragMoveFlag=!0:this.dragDownFlag=!1}),"dragListener")}),"dragListener")}removeDragListener(){this.dragDownFlag=!1,this.dragMoveFlag=!1,this.app.off("mousedown",null,"dragListener"),this.app.off("mousemove",null,"dragListener")}docMouseUp(t){t.stopPropagation(),this.activeMode&&this.mouseUp(t)}updateTwinProp(){const t={scale:this.twinObject.scale,offsetHeight:CMAP.Util.convertWorldToLonlat(this.twinObject.position)[2],size:this.twinObject.size,angles:this.twinObject.angles};tt.updateTwinProp(t)}updateThing(t){if(this.activeMode||!this.twinObject)return;let e={};switch(t.type){case"scale":this.twinObject.scale=[t.data[0],t.data[1],t.data[2]];break;case"angles":this.twinObject.angles=[t.data[0],t.data[1],t.data[2]];break;case"offsetHeight":const i=CMAP.Util.convertWorldToLonlat(this.twinObject.position);this.twinObject.position=CMAP.Util.convertLonlatToWorld([i[0],i[1]],t.data);break;case"customColor":e=S(t.data),this.twinObject.renderer.color=e.color,this.twinObject.renderer.opacity=e.opacity}}btnDown(t){this.activeMode=t.srcElement.getAttribute("mode"),this.app.camera.inputEnabled=!1,"rotate"!==this.activeMode&&"translateY"!==this.activeMode&&"translateXZ"!==this.activeMode||this.hideOtherBtns(),this.transformCtrl&&(this.transformCtrl.prePos=null,this.transformCtrl.preAngle=null,this.transformCtrl.angleBtnDir=null)}mouseUp(t){if(this.twinObject){if(this.dragDownFlag=!1,!this.dragMoveFlag)return this.disableBtns.includes(this.activeMode)?(this.activeMode="",void(this.app.camera.inputEnabled=!0)):"rotate"===this.activeMode||"translateY"===this.activeMode||"translateXZ"===this.activeMode?(this.activeMode="",this.app.camera.inputEnabled=!0,this.showPlaceBtns(),void(this.transformCtrl&&(this.transformCtrl.prePos=null,this.transformCtrl.preAngle=null,this.transformCtrl.angleBtnDir=null))):void("delete"!==this.activeMode||this.deleteThing());this.dragMoveFlag=!1}}deleteThing(){this.activeMode="",U.confirm({title:"确定删除当前摆点吗？",okText:"确定",cancelText:"取消",onOk:()=>{const t=this.twinObject.userData.uuid;t?k({[tt.twinData.code]:{"uuid{}":[t]}}).then((e=>{if(200===e.code){this.reset();const e=window.app.query(`#${t}`)[0];e&&e.destroy(),tt.updateDeleteThing(+new Date)}else P("error",e.message)})):(this.reset(!0),tt.updateDeleteThing(+new Date))},onCancel:()=>{this.activeMode="",this.app.camera.inputEnabled=!0}})}disableClickBtns(){if(!this.disableBtns.length)return;const t=document.getElementsByClassName("placement-btn");for(let e=t.length-1;e>=0;e--){const i=t[e].getAttribute("mode");this.disableBtns.find((t=>i===t))&&(t[e].style.cursor="not-allowed",t[e].style.background="#999999")}}mouseMove(t){if(this.activeMode&&this.transformCtrl&&("rotate"===this.activeMode||"translateY"===this.activeMode||"translateXZ"===this.activeMode)){const e={x:t.x,y:t.y,shift:t.shiftKey,mode:this.activeMode};this.transformCtrl.update(e),this.updateCb&&("rotate"===this.activeMode?this.updateCb({type:"angles",data:this.disposeUpdateData()}):"translateY"===this.activeMode&&this.updateCb({type:"offsetHeight",data:this.disposeUpdateData()}))}}disposeUpdateData(){if(!this.activeMode||!this.twinObject)return null;switch(this.activeMode){case"translateY":return CMAP.Util.convertWorldToLonlat(this.twinObject.position)[2];case"rotate":case"mirror":return this.twinObject.angles;default:return null}}hideOtherBtns(){if("translateY"===this.activeMode||"translateXZ"===this.activeMode||"rotate"===this.activeMode){const t=document.getElementsByClassName("placement-btn");for(let e=t.length-1;e>=0;e--){const i=t[e];i.id.indexOf(this.activeMode)<0&&(i.style.visibility="hidden")}}}hidePlaceBtns(){const t=document.getElementById("placement-btns");t&&(t.style.visibility="hidden")}showPlaceBtns(){const t=document.getElementById("placement-btns");if(t){t.style.visibility="visible";const e=document.getElementsByClassName("placement-btn");for(let t=e.length-1;t>=0;t--){e[t].style.visibility="visible"}}}reset(t){if(!this.twinObject)return;tt.updateShowEditPanel(!1);const e=window.app.query(`#GeoPointMarker${this.twinObject.id}`)[0];if(e&&(e.visible=!0),this.activeMode="",this.removeDragListener(),this.twinObject.removeControl("TransformControl"),this.app.off("mousemove",null,"campusPlacement"),this.app.off("mouseup",null,"campusPlacement"),et&&(document.removeEventListener("mouseup",et),et=null),this.app.camera.inputEnabled=!0,this.twinObject.style.outlineColor=null,t){const t=this.twinObject.userData;this.twinObject.destroy(),"new"!==this.type&&window.mapDrawInstance.creatThing(t,t.coords)}this.twinObject=null,this.disableBtns=[];const i=document.getElementById("placement-btns");if(i){const t=document.getElementsByClassName("placement-btn");for(let e=t.length-1;e>=0;e--){const i=t[e];i.style.visibility="visible",i.style.cursor="pointer",i.style.background="#FFFFFF"}i.style.display="none"}}setBtnsPos(t){const{data:e}=t,i=document.getElementById("placement-btns");if(i)i.style.display="block",i.style.top=e[1]-35+"px",i.style.left=e[0]-80+"px";else{const t=this.createPalcementBtns();t.style.top=e[1]-35+"px",t.style.left=e[0]-80+"px",this.app.domElement.append(t),this.disableClickBtns()}}createPalcementBtns(){const t=document.createElement("div");t.style.pointerEvents="all",t.style.userSelect="none",t.id="placement-btns",t.style.position="absolute",t.style.width="160px",t.style.height="70px";return[{mode:"rotate",title:"旋转",left:20,bottom:0,img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABUJJREFUWEfFl39M1GUcx9/PcT8AOX6rl4TGgQZTBCSUOSRkIJWLQCVBHaYiTVNz5TLLVWu2qTMjQpyiS8vUFLXJQEkJPfGCFBSbHAKC8wc7wXnGIXjA9572fL1j9/tIbTx/fbfn83w/r+fz+yEY4qKUegCYDSARQAQAOQBPw/EuAK0A6gH8AaCUENI9lF8TZ0KU0hAA6/s5LquqrXWE8lYrrqvVuKPRQKt7wh+XSlwR6OODiTIZpr8iR1yQ/LHIxeUQgC2EkBZHOuwCUEolAL7W6nRrf7pUIz54pRaPenud8fL73m5uWBAVjeyYaX1SiSQPwBeEEJ2twzYBKKUyACfKG1Wxm86W42FPz5AUWwr5urtjY1IKUkLDqgGkE0LUljJWAJTScEppaf6F84G7q5XPpNjyUG7sdKyZ8fodQshsQsjfpvtmAOzmlNK/1pX8Fni6UfVClBt/8kZoGLa9ncYgpppaYhDA4PNz3yvOxb6om9uyxIfxCcwdCcaYMAXYUt6o+uSjkyec3jzI1w8zguQI9veHm0iMnr4+NHSooWxrw91/Hjk8vz01ncXEVkLIeibIA7BU0+p0198q2il2FHBho0ZjfWISYsaO45V0dnfzqejp6gr/ER7QU4rK5iZsraywC8ICs2z5CpYdE1mKGgGKCqoUOTuVVWb0MqknAr29cfnuHcybHImNybP4VNxbU40y1XU8ePx4UD7A0wvvTArHotdiIBS4YEPpSVQ0N9m0xsrpcfggLn4PIWQ5YRWun+PUCYX5IyzzfOnUWHyckIhNZ8qxMTkFlS1N2FBaAq3OZkrzykZ5SPFtahrCXxqDNSeKoWi9aQXB6sS5lWtYsZIxgPmVLc2HVx0/aiVoBBjQ66Fsa8Wq48XgqN5pjLiLRPgxcyHGeHkjde9uaHqt60jBnAzMDBmfyQB2fXO2PPdgXa1dALZxqK4Wqg41jl1j5d75kvv64dh7y3D4Si22VFZYHVg4JRqfJaXsZgDVCw7sn1bffs8hANu81n4PWQf283Lslmnhk/k+cErVgNuPNFbnN89ORbw8GAmF+ejjOLP9iDEBOLhocQ0D6JjxQ97IhzbMZO+eEhchfs1eghB/f3CUop/jkPXzPjQ/6DQ7EuDlBZHABbc0D61+5evmjgur13YygCeR2zZL+vXOfWv8S/KEV5GXNhfvHz2MhvtqlCzLxZkbN/DV76ec+8YgIRIIcHXdp7rnBmCtuSQnF2efA8CuC4QCAZivWHywTDAuiVCII9lLEOzHXKDHAKdH1oF9aOo0d4Ejc5i6wG4QsgDaOW8+n8+WRcVdLEb6JBaEEpQ1NuC25mkQyqRSVKxYbaY7ZVehVWU0DUK7aSggBKdzV0DT08vfkJVaZ8tDLGaDCGICx2KqoWTbAlgwJRqfG9LQbiFiymZNCMV3aXOwS3kR+VXnnenn91n0H8leCg+JBMyNtgBMC5HdUmzU9uWsN/FuZBT2VCuRX6UA5yBjWMPaMTcDYhchfqm7jFVx8VYAZqWYKaGUFu2oUuQUWjQjIwDrWKwXZEZF40ZHB/bW/InzN1vQ3fe0J7D90FGjkRERiXkRUbjfrcXK4iP8KCf388O19nbouIFB65k1IwPAkNrxzODxWBufgJCRI3krqLVd0A0MgHVNFpTsu7j+KgouKtD15OnEbLlstmMDxJAHkikBLyMmcBzG+vhALBSio1vLW4Z1S0edkumxOZAYANgYPnwjmQFi+IZSo6+GdSw3gRi+h4kJxODTbP+lGvGh//g0y4qKxuJnfZqZps6wPU4t8/f/ep7/C/g92Wd29OLrAAAAAElFTkSuQmCC",id:"btn-rotate"},{mode:"translateXZ",title:"平移",left:64,bottom:0,img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA45JREFUWEfFl2tIk1EYx//HmGsznTNK0aScRpmUlZQi3grByqwMwnLS+pBCN7OI7vQhullSpktLu2CYBkZ2YZGkZZuaJl2sbCPFjCisQCNrNmWeOGuTpStf3xnbx73P85zfOc+dgOOPUjoOQDyAhQCCAcgAuJnVvwFoA9AE4D4AFSHkOxfTZDghSmkAgF19RuOamrdtLnXtbWju6MD7ri50G36a1F2FY+ErlSLIywvhU2SI8JP9EIwZUwogkxDS+q8z/gpAKRUCONhtMGRcbmxwLnn2BF97eobjNX13F4mQPCcEa+eF9roKhdkADhBCDLaUbQJQSr0AlFfotGGHKivQqddzOniwkIdYjP2xcYibHlgPIJEQ0jFYZggApXQmpVSVo3noW1Bfx+vgwUppYeFIj4x+TwiJJ4S8tP7+BwC7OaX08Y7bN3zv6rSjcrjFyKLpgchKWMEg5lu/xACA2efVp9XVYaN1c1svsTUqhrkjxhIT1gCZFTrtzu23yjnffLJUikkSd9S2v+Wsc3JZIouJ44SQXUzJBMBSrdtgaF5SmO/MNeBkHuNxabUcUrEYe1S3odI2c4JggXkndQPLjiCWohaAQmWNen1+XQ0nI0zoxNLlWBw4A4QQdOn1iFRmg3LU3hgegU0RUecJIamEVbg+o7EjJi/HhWueW3L9YlIyfCTuSCu7iqaPHzge/7tOVG9MZ8XKiwEkPWhtubr5ehlnAxZBVgHZk77r6hyxrnLlKiwImLqaAZw7XFmRVvL0yYiN2KMgnxuCvbFxBQygPrm4KHQkT2jPwRbdYG8flKQoGhjA58jc7AmdPbbLLYv2o/EJOFJ1b4ifo2T+8HR1w41XL9BnNA5wMR+fWr4S15qeQaV9bZPXQySGZkvGFwbwc3bWMWFff/8QQW83CUpTFBjv4oIfvb1QlBZD9/mTSW6WtzdK5ApTFuRqHuLso1rT/yKBAFfkazFtoieM/f3YdvM6qlreDLEtcHLC8x27DbwBghlAyjqTYaVGjfxHv1OYDwBvF0T7B8BznCvK7XSBw4PQIWmYPDcE+8xpyLsQuQnHmnqBvYWIVymWikS4kCSHj0RiXyk2d8PCMzXq9XmOaEZ827GfuR2zXmB3OzZD8BpIWDess3cgMQOwMdxxI5kZwnFDqaVYO3Qst4Jw3GJiBTGwmhU1NjiXjnA1WzMnBAq+q5l173TYcjq4gf+v9fwXlF0zZ1GZitAAAAAASUVORK5CYII=",id:"btn-translateXZ"},{mode:"translateY",title:"上下平移",left:108,bottom:0,img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAyFJREFUWEfFl3tIU1Ecx79H0eV8zgiHJtVcoEiZSiniqxAUpcggUifZHypkZRKSvegPK8iQMDU1lcIIDYKUZNFASrc5NbGykgmJClJIgUYrdck8ccYmyzTv3S3u/ff+Hp9zzu9JwPGjlHoByACwD0AEAAUAH5v6NwDjAIYBPAegJoR852KarCdEKVUCKFu0WLL1E+OehslxjExPY2p2FibzglXdW7IBwTIZwuVyxG1VIH6b4oebq2sbgApCyNjffKwJQCmVACg3mc0l9wcH3FtfD+Hr/Px6vNb/fh4eyImMxtHdMT+9JZIqAJcJIebVlFcFoJTKAbRrRo2xV7s0mJmb4+R4pZC/VIpLKalIDQ3rB5BJCJleKfMHAKV0B6VUXa3rCW7sNzjleKVSYWwcihOSpgghGYSQd47/fwNgJ6eUvizt7Ah+Nmr8J87tRtJCw1C5/yCD2ON4E8sAtjfvvqXtjv1XJ1/tJk4nJrPnSLbHhCNAhWbUePbMk3bOJ98ik2Gzrx96Jyc469w8kMli4gYhpIwpWQFYqpnM5pH0pnp3rgGn8N+Ie1kqyKRSnFd3Qm0c4QTBAvNpwXGWHeEsRe0ATbV6bX69Qc/JCBMqT0vHXuV2q7xpwYyM5gZQjtpFcfE4EZ/YTAgpIKzCLVos08l11Z5c85yjnzXFWJ3oLipmxUrOAI68GPvw8OTjR0Lt8tKvPXSY3WAWA7hzrUtT2PpqiJcBocKqqGhcSEltZAD9OQ9aYoY/fRRqk5d+RGAQWnPzBhjA54Saqk0z8/zLbaIiBAHePuh4/xaLFgsvAH8PKXSnSr4wgIVdldcli0tLvAzsDAxEqyoPhBDU6HrQ0NfLS9/NxQVvSs+ZnQaIYAC5x6xOa3Va1PdxT2Gm4wjg9BMkhSgR4OWNdoFPIHoQipKGOVHRuGhLQ9ELkbil2NYNm27rtfl1PJrRlbQMJCvZvCqwGQlpx3ezVGDtVXA7tkE4NZAE+frBIHQgsQGwMVy8kcwGId5Qai/koo7lDhDiLSYOEMurWcvggHsbz9UsOzIaec6uZo59VbTldGVz/1/r+S/+aeVYDYwhNQAAAABJRU5ErkJggg==",id:"btn-translateY"},{mode:"delete",title:"删除",left:64,top:0,img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAAD20lEQVRYCcVXW0hUURTdk4OWOoNG6iRqffTwERWlZSKDEz2saMIgKCqqjwIjSOujD8uPoqAgM4iC+qgoKQgyjR4UaIlEZUQvHz1+KvOREJmmKNq01p07l6vdaa4l44Y958w5++y17nnss49FTIrH4wmFqQvqhqZC41VFIS2qNqCshFZbLJZ+lAHFEsgCwA7YFHtENj5v/myvev9O6tta5Wt3t3R0dynDYyJtEhsZKWmOybJ4+gyZl5D4A47L0HkQRNr+huGXAIDDMLCof3Bwz6VnTyMu1D2Rbz09f/Ol9U0MD5etGQtlc/qCn6EhISXoOAwifZqBrmJIQP3q8ntvmzKPVt2Xti7vl+rGmao6bDbZt3ipLJuZ/BgD8oxm4w8CAJ8N41ulNQ8Szj1+ZAookNH2zCwpcOY0w24lSLzW2w8hoH553d7K8oS7TY16u/+u5yanyHF3Hklk6GdinM+zuubl/PLRBicGfZ6Eb1TLVSwF2qr8en+KuOZmpn3dnLmyKiVNNxRr1lgv116+GNI2/M9ZLGlKnCMTe6IIfcXsV2aAU8/dzg0XSJKioqVoyXKZFBGpmbK+f2muJEVHa23+KsQglrrcXgIwLuZRM7PbsZnoQLZcuSxbr5YpynrfwIAUOl3+cLV2YhCLmGy0gEkogkyH81Spffg5P79+oyxImqIN/pfK008fZRuI6oVxomZXAYNVDPeAixFuODgH3Hj9SurgYHXaLImaEC5vv7Yj4tnEGqLtXb1fGRj8hQjZJTNj4+R7b4/crH8jXzo7h9jwD7GIOT8h0UUCboZXI6mo9x5ZzkI7po5TbkYuYOYopx/V+jUnJgi4+SmpjO3BFhUzlQTiebGMRKKxHOmJSRJmtSrKOttGIipmvELAd6uZdbBo6lS5uGGTOGx2RVln20hExVQI4CxgPwZbvJgezkBLjC6oBIuHitmqEGAyEWxRMVtIoIGZTLBFxWxgHKhEGrWTGY9Zud3YIFSfpB074quaLpm6QSo5A9XM4RgegyXEIiaxrUgO+nEflCGHyy95WG3IAXeF2MePlwycdzNig21nb69fU+aLOHdlxFbOn3odf1hx9rThjZiflS27sp1+HRp1nKqtkTMGoZh54p0dO5msTmNmpAUAkDiIhORAYcV1I3+SjAvGFsZEObB09fVJEy4uIzmxZi2T1EMA917HPiMQoPcHSMlMZUW+cSMpdyA53e3MYYacAwJKms5NqIjakMfslQnkaAt9ArwZfpmea28EbQl8gJiJsUvLdST4HBubh4mOxNg9zXwkWPKIogj+41RPQiUSitIFdUNH7Xn+G1lBsUgUlgnSAAAAAElFTkSuQmCC",id:"btn-delete"}].forEach((e=>{const i=document.createElement("div");i.id=e.id,i.classList.add("placement-btn"),i.setAttribute("title",e.title),i.setAttribute("mode",e.mode),i.style.position="absolute",e.top&&(i.style.top=`${e.top}px`),e.hasOwnProperty("left")&&(i.style.left=`${e.left}px`),e.hasOwnProperty("bottom")&&(i.style.bottom=`${e.bottom}px`),i.style.width="32px",i.style.height="32px",i.style.textAlign="center",i.style.lineHeight="32px",i.style.zIndex="99",i.style.cursor="pointer",i.style.borderRadius="50%",i.style.background="#FFFFFF",i.innerHTML=`<img src=${e.img} style='-webkit-user-drag:none;user-drag:none;' mode='${e.mode}'></img>`,i.addEventListener("mousedown",this.btnDown.bind(this)),t.append(i)})),t}flyToCurrentThing(){var t,e,i;if(this.twinObject){const s=null==(t=this.twinObject)?void 0:t.userData.eye,o=null==(e=this.twinObject)?void 0:e.userData.target,n=null==(i=this.twinObject)?void 0:i.userData.distance;s&&o?window.app.camera.flyTo({position:JSON.parse(s),target:JSON.parse(o),radius:n,isEarth:!0,time:1e3,duration:1e3}):this.app.camera.earthFlyTo({object:this.twinObject,height:600})}}setMarker(t){const e=tt.twinData.bubbleInfoVo.imgUrl;let i=2;const s=t.query(".Thing")[0];s&&(i=s.boundingBox.initSize[1]/2),this.app.create({type:"GeoPoint",id:`GeoPointMarker${t.id}`,name:`marker${t.id}`,coordinates:"GeoPoint"===t.type?t.coordinates:"GeoLine"===t.type?t.coordinates[0]:t.centerCoordinates,renderer:{type:"image",size:80,url:e,alwaysOnTop:!0,keepSize:!1},inheritScale:!1,inheritStyle:!1,inheritTheme:!1,parent:t,localPosition:[0,i,0],pivot:[.5,1],visible:!0,complete:e=>{e.object.customPicker=!0,e.object.on("click",(i=>{tt.markFlag||0===i.button&&(tt.markFlag=!1,this.init({thing:t,updateCb:t=>{switch(t.type){case"angles":tt.twinProp.angles=t.data;break;case"offsetHeight":tt.twinProp.offsetHeight=t.data}}}),e.object.visible=!1)}))}})}endChoose(t){"creatUuid"!==(t||this.twinObject).id&&document.addEventListener("mouseup",this.listenDocument)}listenDocument(t){(t.path||t.composedPath&&t.composedPath()).find((t=>t.id&&"earthOutContainer"===t.id))&&(t.srcElement.getAttribute("mode")||window.mapPlacementIns.reset(!0))}};const st=R();let ot=null,nt=class{constructor(t){e(this,"app"),e(this,"twinObject"),e(this,"transformCtrl"),e(this,"activeMode"),e(this,"updateCb"),e(this,"disableBtns",[]),e(this,"dragDownFlag",!1),e(this,"dragMoveFlag",!1),e(this,"originTwinParam"),e(this,"type",""),e(this,"TransformControl"),J((()=>import("./TransformControl-DyfYd0RF.js")),[]).then((e=>{this.TransformControl=e.TransformControl,this.app=t,this.twinObject=null,this.transformCtrl=null,this.activeMode=""}))}init(t){this.reset(!0),this.twinObject=t.thing,this.disableBtns=t.disableBtns||[],this.updateCb=t.updateCb,this.type=t.type,this.transformCtrl=this.twinObject.getComponentByName("TransformControl"),this.transformCtrl||(this.transformCtrl=this.twinObject.addComponent(this.TransformControl,"TransformControl")),this.twinObject.on(this.TransformControl.TRANSFORM_CONTROL_UPDATE,(t=>{this.setBtnsPos(t)})),this.twinObject.style.outlineColor="#135EBF",this.app.on("mousemove",this.mouseMove.bind(this),"campusPlacement"),this.app.on("mouseup",this.mouseUp.bind(this),"campusPlacement"),ot=this.docMouseUp.bind(this),document.addEventListener("mouseup",ot),this.resetClickBtns(),this.disableClickBtns(),this.initDragListener();const e={scale:this.twinObject.scale,position:this.twinObject.position,offsetHeight:THING.EARTH.Utils.convertWorldToLonlat(this.twinObject.position)[2],size:this.twinObject.scale,angles:this.twinObject.angles,customColor:this.twinObject.userData.position_body?JSON.parse(this.twinObject.userData.position_body).customColor:null};this.originTwinParam=JSON.parse(JSON.stringify(e)),st.updateTwinProp(e),st.updateDefaultData(this.twinObject.userData),st.updateShowEditPanel(!0)}updateOriginTwinParam(){const t={scale:this.twinObject.scale,position:this.twinObject.position,offsetHeight:THING.EARTH.Utils.convertWorldToLonlat(this.twinObject.position)[2],size:this.twinObject.scale,angles:this.twinObject.angles,customColor:this.twinObject.userData.position_body?JSON.parse(this.twinObject.userData.position_body).customColor:null};this.originTwinParam=JSON.parse(JSON.stringify(t))}resetClickBtns(){if(document.getElementById("placement-btns")){const t=document.getElementsByClassName("placement-btn");for(let e=t.length-1;e>=0;e--){const i=t[e];i.style.visibility="visible",i.style.cursor="pointer",i.style.background="#FFFFFF"}}}initDragListener(){this.app.on("mousedown",(()=>{this.dragDownFlag=!0,this.app.on("mousemove",(()=>{!this.activeMode&&this.dragDownFlag?this.dragMoveFlag=!0:this.dragDownFlag=!1}),"dragListener")}),"dragListener")}removeDragListener(){this.dragDownFlag=!1,this.dragMoveFlag=!1,this.app.off("mousedown",null,"dragListener"),this.app.off("mousemove",null,"dragListener")}docMouseUp(t){t.stopPropagation(),this.activeMode&&this.mouseUp(t)}updateTwinProp(){const t={scale:this.twinObject.scale,offsetHeight:THING.EARTH.Utils.convertWorldToLonlat(this.twinObject.position)[2],size:this.twinObject.size,angles:this.twinObject.angles};st.updateTwinProp(t)}updateThing(t){if(this.activeMode||!this.twinObject)return;let e={};switch(t.type){case"scale":this.twinObject.scale=[t.data[0],t.data[1],t.data[2]];break;case"angles":this.twinObject.angles=[t.data[0],t.data[1],t.data[2]];break;case"offsetHeight":const i=THING.EARTH.Utils.convertWorldToLonlat(this.twinObject.position);this.twinObject.position=THING.EARTH.Utils.convertLonlatToWorld([i[0],i[1]],t.data);break;case"customColor":e=S(t.data),this.twinObject.style.color="rgb("+e.color.toString()+")",this.twinObject.style.opacity=e.opacity}}btnDown(t){this.activeMode=t.srcElement.getAttribute("mode"),this.app.camera.enable=!1,"rotate"!==this.activeMode&&"translateY"!==this.activeMode&&"translateXZ"!==this.activeMode||this.hideOtherBtns(),this.transformCtrl&&this.twinObject.resetCtrlData()}mouseUp(t){if(this.twinObject){if(this.dragDownFlag=!1,!this.dragMoveFlag)return this.disableBtns.includes(this.activeMode)?(this.activeMode="",void(this.app.camera.enable=!0)):"rotate"===this.activeMode||"translateY"===this.activeMode||"translateXZ"===this.activeMode?(this.activeMode="",this.app.camera.enable=!0,this.showPlaceBtns(),void(this.transformCtrl&&this.twinObject.resetCtrlData())):void("delete"!==this.activeMode||this.deleteThing());this.dragMoveFlag=!1}}deleteThing(){this.activeMode="",U.confirm({title:"确定删除当前摆点吗？",okText:"确定",cancelText:"取消",onOk:()=>{const t=this.twinObject.userData.uuid;t?k({[st.twinData.code]:{"uuid{}":[t]}}).then((e=>{if(200===e.code){this.reset();const e=window.app.query(`#${t}`)[0];e&&e.destroy(),st.updateDeleteThing(+new Date)}else P("error",e.message)})):(this.reset(!0),st.updateDeleteThing(+new Date))},onCancel:()=>{this.activeMode="",this.app.camera.enable=!0}})}disableClickBtns(){if(!this.disableBtns.length)return;const t=document.getElementsByClassName("placement-btn");for(let e=t.length-1;e>=0;e--){const i=t[e].getAttribute("mode");this.disableBtns.find((t=>i===t))&&(t[e].style.cursor="not-allowed",t[e].style.background="#999999")}}mouseMove(t){if(this.activeMode&&this.transformCtrl&&("rotate"===this.activeMode||"translateY"===this.activeMode||"translateXZ"===this.activeMode)){const e={x:t.x,y:t.y,shift:t.shiftKey,mode:this.activeMode};this.twinObject.updateCtrlObj(e),this.updateCb&&("rotate"===this.activeMode?this.updateCb({type:"angles",data:this.disposeUpdateData()}):"translateY"===this.activeMode&&this.updateCb({type:"offsetHeight",data:this.disposeUpdateData()}))}}disposeUpdateData(){if(!this.activeMode||!this.twinObject)return null;switch(this.activeMode){case"translateY":return THING.EARTH.Utils.convertWorldToLonlat(this.twinObject.position)[2];case"rotate":case"mirror":return this.twinObject.angles;default:return null}}hideOtherBtns(){if("translateY"===this.activeMode||"translateXZ"===this.activeMode||"rotate"===this.activeMode){const t=document.getElementsByClassName("placement-btn");for(let e=t.length-1;e>=0;e--){const i=t[e];i.id.indexOf(this.activeMode)<0&&(i.style.visibility="hidden")}}}hidePlaceBtns(){const t=document.getElementById("placement-btns");t&&(t.style.visibility="hidden")}showPlaceBtns(){const t=document.getElementById("placement-btns");if(t){t.style.visibility="visible";const e=document.getElementsByClassName("placement-btn");for(let t=e.length-1;t>=0;t--){e[t].style.visibility="visible"}}}reset(t){if(!this.twinObject)return;st.updateShowEditPanel(!1);if(window.app.query(`#GeoPointMarker${this.twinObject.id}`)[0]&&this.setMarker(this.twinObject),this.activeMode="",this.removeDragListener(),this.twinObject.removeComponent("TransformControl"),this.app.off("mousemove",null,"campusPlacement"),this.app.off("mouseup",null,"campusPlacement"),ot&&(document.removeEventListener("mouseup",ot),ot=null),this.app.camera.enable=!0,this.twinObject.style.outlineColor=null,t){const t=this.twinObject.userData;this.twinObject.destroy(),"new"!==this.type&&window.mapDrawInstance.creatThing(t,t.coords)}this.twinObject=null,this.disableBtns=[];const e=document.getElementById("placement-btns");if(e){const t=document.getElementsByClassName("placement-btn");for(let e=t.length-1;e>=0;e--){const i=t[e];i.style.visibility="visible",i.style.cursor="pointer",i.style.background="#FFFFFF"}e.style.display="none"}}setBtnsPos(t){const{data:e}=t,i=document.getElementById("placement-btns");if(i)i.style.display="block",i.style.top=e[1]-35+"px",i.style.left=e[0]-80+"px";else{const t=this.createPalcementBtns();t.style.top=e[1]-35+"px",t.style.left=e[0]-80+"px",this.app.container.append(t),this.disableClickBtns()}}createPalcementBtns(){const t=document.createElement("div");t.style.pointerEvents="all",t.style.userSelect="none",t.id="placement-btns",t.style.position="absolute",t.style.width="160px",t.style.height="70px";return[{mode:"rotate",title:"旋转",left:20,bottom:0,img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABUJJREFUWEfFl39M1GUcx9/PcT8AOX6rl4TGgQZTBCSUOSRkIJWLQCVBHaYiTVNz5TLLVWu2qTMjQpyiS8vUFLXJQEkJPfGCFBSbHAKC8wc7wXnGIXjA9572fL1j9/tIbTx/fbfn83w/r+fz+yEY4qKUegCYDSARQAQAOQBPw/EuAK0A6gH8AaCUENI9lF8TZ0KU0hAA6/s5LquqrXWE8lYrrqvVuKPRQKt7wh+XSlwR6OODiTIZpr8iR1yQ/LHIxeUQgC2EkBZHOuwCUEolAL7W6nRrf7pUIz54pRaPenud8fL73m5uWBAVjeyYaX1SiSQPwBeEEJ2twzYBKKUyACfKG1Wxm86W42FPz5AUWwr5urtjY1IKUkLDqgGkE0LUljJWAJTScEppaf6F84G7q5XPpNjyUG7sdKyZ8fodQshsQsjfpvtmAOzmlNK/1pX8Fni6UfVClBt/8kZoGLa9ncYgpppaYhDA4PNz3yvOxb6om9uyxIfxCcwdCcaYMAXYUt6o+uSjkyec3jzI1w8zguQI9veHm0iMnr4+NHSooWxrw91/Hjk8vz01ncXEVkLIeibIA7BU0+p0198q2il2FHBho0ZjfWISYsaO45V0dnfzqejp6gr/ER7QU4rK5iZsraywC8ICs2z5CpYdE1mKGgGKCqoUOTuVVWb0MqknAr29cfnuHcybHImNybP4VNxbU40y1XU8ePx4UD7A0wvvTArHotdiIBS4YEPpSVQ0N9m0xsrpcfggLn4PIWQ5YRWun+PUCYX5IyzzfOnUWHyckIhNZ8qxMTkFlS1N2FBaAq3OZkrzykZ5SPFtahrCXxqDNSeKoWi9aQXB6sS5lWtYsZIxgPmVLc2HVx0/aiVoBBjQ66Fsa8Wq48XgqN5pjLiLRPgxcyHGeHkjde9uaHqt60jBnAzMDBmfyQB2fXO2PPdgXa1dALZxqK4Wqg41jl1j5d75kvv64dh7y3D4Si22VFZYHVg4JRqfJaXsZgDVCw7sn1bffs8hANu81n4PWQf283Lslmnhk/k+cErVgNuPNFbnN89ORbw8GAmF+ejjOLP9iDEBOLhocQ0D6JjxQ97IhzbMZO+eEhchfs1eghB/f3CUop/jkPXzPjQ/6DQ7EuDlBZHABbc0D61+5evmjgur13YygCeR2zZL+vXOfWv8S/KEV5GXNhfvHz2MhvtqlCzLxZkbN/DV76ec+8YgIRIIcHXdp7rnBmCtuSQnF2efA8CuC4QCAZivWHywTDAuiVCII9lLEOzHXKDHAKdH1oF9aOo0d4Ejc5i6wG4QsgDaOW8+n8+WRcVdLEb6JBaEEpQ1NuC25mkQyqRSVKxYbaY7ZVehVWU0DUK7aSggBKdzV0DT08vfkJVaZ8tDLGaDCGICx2KqoWTbAlgwJRqfG9LQbiFiymZNCMV3aXOwS3kR+VXnnenn91n0H8leCg+JBMyNtgBMC5HdUmzU9uWsN/FuZBT2VCuRX6UA5yBjWMPaMTcDYhchfqm7jFVx8VYAZqWYKaGUFu2oUuQUWjQjIwDrWKwXZEZF40ZHB/bW/InzN1vQ3fe0J7D90FGjkRERiXkRUbjfrcXK4iP8KCf388O19nbouIFB65k1IwPAkNrxzODxWBufgJCRI3krqLVd0A0MgHVNFpTsu7j+KgouKtD15OnEbLlstmMDxJAHkikBLyMmcBzG+vhALBSio1vLW4Z1S0edkumxOZAYANgYPnwjmQFi+IZSo6+GdSw3gRi+h4kJxODTbP+lGvGh//g0y4qKxuJnfZqZps6wPU4t8/f/ep7/C/g92Wd29OLrAAAAAElFTkSuQmCC",id:"btn-rotate"},{mode:"translateXZ",title:"平移",left:64,bottom:0,img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA45JREFUWEfFl2tIk1EYx//HmGsznTNK0aScRpmUlZQi3grByqwMwnLS+pBCN7OI7vQhullSpktLu2CYBkZ2YZGkZZuaJl2sbCPFjCisQCNrNmWeOGuTpStf3xnbx73P85zfOc+dgOOPUjoOQDyAhQCCAcgAuJnVvwFoA9AE4D4AFSHkOxfTZDghSmkAgF19RuOamrdtLnXtbWju6MD7ri50G36a1F2FY+ErlSLIywvhU2SI8JP9EIwZUwogkxDS+q8z/gpAKRUCONhtMGRcbmxwLnn2BF97eobjNX13F4mQPCcEa+eF9roKhdkADhBCDLaUbQJQSr0AlFfotGGHKivQqddzOniwkIdYjP2xcYibHlgPIJEQ0jFYZggApXQmpVSVo3noW1Bfx+vgwUppYeFIj4x+TwiJJ4S8tP7+BwC7OaX08Y7bN3zv6rSjcrjFyKLpgchKWMEg5lu/xACA2efVp9XVYaN1c1svsTUqhrkjxhIT1gCZFTrtzu23yjnffLJUikkSd9S2v+Wsc3JZIouJ44SQXUzJBMBSrdtgaF5SmO/MNeBkHuNxabUcUrEYe1S3odI2c4JggXkndQPLjiCWohaAQmWNen1+XQ0nI0zoxNLlWBw4A4QQdOn1iFRmg3LU3hgegU0RUecJIamEVbg+o7EjJi/HhWueW3L9YlIyfCTuSCu7iqaPHzge/7tOVG9MZ8XKiwEkPWhtubr5ehlnAxZBVgHZk77r6hyxrnLlKiwImLqaAZw7XFmRVvL0yYiN2KMgnxuCvbFxBQygPrm4KHQkT2jPwRbdYG8flKQoGhjA58jc7AmdPbbLLYv2o/EJOFJ1b4ifo2T+8HR1w41XL9BnNA5wMR+fWr4S15qeQaV9bZPXQySGZkvGFwbwc3bWMWFff/8QQW83CUpTFBjv4oIfvb1QlBZD9/mTSW6WtzdK5ApTFuRqHuLso1rT/yKBAFfkazFtoieM/f3YdvM6qlreDLEtcHLC8x27DbwBghlAyjqTYaVGjfxHv1OYDwBvF0T7B8BznCvK7XSBw4PQIWmYPDcE+8xpyLsQuQnHmnqBvYWIVymWikS4kCSHj0RiXyk2d8PCMzXq9XmOaEZ827GfuR2zXmB3OzZD8BpIWDess3cgMQOwMdxxI5kZwnFDqaVYO3Qst4Jw3GJiBTGwmhU1NjiXjnA1WzMnBAq+q5l173TYcjq4gf+v9fwXlF0zZ1GZitAAAAAASUVORK5CYII=",id:"btn-translateXZ"},{mode:"translateY",title:"上下平移",left:108,bottom:0,img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAyFJREFUWEfFl3tIU1Ecx79H0eV8zgiHJtVcoEiZSiniqxAUpcggUifZHypkZRKSvegPK8iQMDU1lcIIDYKUZNFASrc5NbGykgmJClJIgUYrdck8ccYmyzTv3S3u/ff+Hp9zzu9JwPGjlHoByACwD0AEAAUAH5v6NwDjAIYBPAegJoR852KarCdEKVUCKFu0WLL1E+OehslxjExPY2p2FibzglXdW7IBwTIZwuVyxG1VIH6b4oebq2sbgApCyNjffKwJQCmVACg3mc0l9wcH3FtfD+Hr/Px6vNb/fh4eyImMxtHdMT+9JZIqAJcJIebVlFcFoJTKAbRrRo2xV7s0mJmb4+R4pZC/VIpLKalIDQ3rB5BJCJleKfMHAKV0B6VUXa3rCW7sNzjleKVSYWwcihOSpgghGYSQd47/fwNgJ6eUvizt7Ah+Nmr8J87tRtJCw1C5/yCD2ON4E8sAtjfvvqXtjv1XJ1/tJk4nJrPnSLbHhCNAhWbUePbMk3bOJ98ik2Gzrx96Jyc469w8kMli4gYhpIwpWQFYqpnM5pH0pnp3rgGn8N+Ie1kqyKRSnFd3Qm0c4QTBAvNpwXGWHeEsRe0ATbV6bX69Qc/JCBMqT0vHXuV2q7xpwYyM5gZQjtpFcfE4EZ/YTAgpIKzCLVos08l11Z5c85yjnzXFWJ3oLipmxUrOAI68GPvw8OTjR0Lt8tKvPXSY3WAWA7hzrUtT2PpqiJcBocKqqGhcSEltZAD9OQ9aYoY/fRRqk5d+RGAQWnPzBhjA54Saqk0z8/zLbaIiBAHePuh4/xaLFgsvAH8PKXSnSr4wgIVdldcli0tLvAzsDAxEqyoPhBDU6HrQ0NfLS9/NxQVvSs+ZnQaIYAC5x6xOa3Va1PdxT2Gm4wjg9BMkhSgR4OWNdoFPIHoQipKGOVHRuGhLQ9ELkbil2NYNm27rtfl1PJrRlbQMJCvZvCqwGQlpx3ezVGDtVXA7tkE4NZAE+frBIHQgsQGwMVy8kcwGId5Qai/koo7lDhDiLSYOEMurWcvggHsbz9UsOzIaec6uZo59VbTldGVz/1/r+S/+aeVYDYwhNQAAAABJRU5ErkJggg==",id:"btn-translateY"},{mode:"delete",title:"删除",left:64,top:0,img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAAIAAAAACshmLzAAAD20lEQVRYCcVXW0hUURTdk4OWOoNG6iRqffTwERWlZSKDEz2saMIgKCqqjwIjSOujD8uPoqAgM4iC+qgoKQgyjR4UaIlEZUQvHz1+KvOREJmmKNq01p07l6vdaa4l44Y958w5++y17nnss49FTIrH4wmFqQvqhqZC41VFIS2qNqCshFZbLJZ+lAHFEsgCwA7YFHtENj5v/myvev9O6tta5Wt3t3R0dynDYyJtEhsZKWmOybJ4+gyZl5D4A47L0HkQRNr+huGXAIDDMLCof3Bwz6VnTyMu1D2Rbz09f/Ol9U0MD5etGQtlc/qCn6EhISXoOAwifZqBrmJIQP3q8ntvmzKPVt2Xti7vl+rGmao6bDbZt3ipLJuZ/BgD8oxm4w8CAJ8N41ulNQ8Szj1+ZAookNH2zCwpcOY0w24lSLzW2w8hoH553d7K8oS7TY16u/+u5yanyHF3Hklk6GdinM+zuubl/PLRBicGfZ6Eb1TLVSwF2qr8en+KuOZmpn3dnLmyKiVNNxRr1lgv116+GNI2/M9ZLGlKnCMTe6IIfcXsV2aAU8/dzg0XSJKioqVoyXKZFBGpmbK+f2muJEVHa23+KsQglrrcXgIwLuZRM7PbsZnoQLZcuSxbr5YpynrfwIAUOl3+cLV2YhCLmGy0gEkogkyH81Spffg5P79+oyxImqIN/pfK008fZRuI6oVxomZXAYNVDPeAixFuODgH3Hj9SurgYHXaLImaEC5vv7Yj4tnEGqLtXb1fGRj8hQjZJTNj4+R7b4/crH8jXzo7h9jwD7GIOT8h0UUCboZXI6mo9x5ZzkI7po5TbkYuYOYopx/V+jUnJgi4+SmpjO3BFhUzlQTiebGMRKKxHOmJSRJmtSrKOttGIipmvELAd6uZdbBo6lS5uGGTOGx2RVln20hExVQI4CxgPwZbvJgezkBLjC6oBIuHitmqEGAyEWxRMVtIoIGZTLBFxWxgHKhEGrWTGY9Zud3YIFSfpB074quaLpm6QSo5A9XM4RgegyXEIiaxrUgO+nEflCGHyy95WG3IAXeF2MePlwycdzNig21nb69fU+aLOHdlxFbOn3odf1hx9rThjZiflS27sp1+HRp1nKqtkTMGoZh54p0dO5msTmNmpAUAkDiIhORAYcV1I3+SjAvGFsZEObB09fVJEy4uIzmxZi2T1EMA917HPiMQoPcHSMlMZUW+cSMpdyA53e3MYYacAwJKms5NqIjakMfslQnkaAt9ArwZfpmea28EbQl8gJiJsUvLdST4HBubh4mOxNg9zXwkWPKIogj+41RPQiUSitIFdUNH7Xn+G1lBsUgUlgnSAAAAAElFTkSuQmCC",id:"btn-delete"}].forEach((e=>{const i=document.createElement("div");i.id=e.id,i.classList.add("placement-btn"),i.setAttribute("title",e.title),i.setAttribute("mode",e.mode),i.style.position="absolute",e.top&&(i.style.top=`${e.top}px`),e.hasOwnProperty("left")&&(i.style.left=`${e.left}px`),e.hasOwnProperty("bottom")&&(i.style.bottom=`${e.bottom}px`),i.style.width="32px",i.style.height="32px",i.style.textAlign="center",i.style.lineHeight="32px",i.style.zIndex="99",i.style.cursor="pointer",i.style.borderRadius="50%",i.style.background="#FFFFFF",i.innerHTML=`<img src=${e.img} style='-webkit-user-drag:none;user-drag:none;' mode='${e.mode}'></img>`,i.addEventListener("mousedown",this.btnDown.bind(this)),t.append(i)})),t}flyToCurrentThing(){var t,e,i;if(this.twinObject){const s=null==(t=this.twinObject)?void 0:t.userData.eye,o=null==(e=this.twinObject)?void 0:e.userData.target,n=null==(i=this.twinObject)?void 0:i.userData.distance;if(s&&o)window.app.camera.flyTo({position:JSON.parse(s),target:JSON.parse(o),radius:n,isEarth:!0,time:1e3,duration:1e3});else{const t=THING.EARTH.Utils.convertWorldToLonlat(this.twinObject.position),[e,i]=t;this.app.camera.earthFlyTo({lonlat:[e,i],height:600})}}}setMarker(t){const e=st.twinData.bubbleInfoVo.imgUrl,i=this.app.query(`#GeoPointMarker${t.id}`)[0];i&&i.destroy();let s=2;const o=t.query(".Entity")[0];o&&(s=o.orientedBox.size[1]/o.scale[1]/2),new THING.Marker({id:`GeoPointMarker${t.id}`,name:`marker${t.id}`,autoFitBodyScale:!0,scaleFactor:1.5,alwaysOnTop:!0,style:{image:e,alwaysOnTop:!0},keepSize:!0,keepSizeDistance:3e3,keepSizeDistanceLimited:[10,3e3],parent:t,pivot:[.5,0],localPosition:[0,s,0],visible:!0,complete:e=>{e.object.inherit.scale=!1,e.object.on("click",(i=>{st.markFlag||0===i.button&&(st.markFlag=!1,this.init({thing:t,updateCb:t=>{switch(t.type){case"angles":st.twinProp.angles=t.data;break;case"offsetHeight":st.twinProp.offsetHeight=t.data}}}),e.object.visible=!1)}))}})}endChoose(t){"creatUuid"!==(t||this.twinObject).id&&document.addEventListener("mouseup",this.listenDocument)}listenDocument(t){(t.path||t.composedPath&&t.composedPath()).find((t=>t.id&&"earthOutContainer"===t.id))&&(t.srcElement.getAttribute("mode")||window.mapPlacementIns.reset(!0))}},rt=class t{constructor(t,i){e(this,"app"),e(this,"twinObject",null),e(this,"positionArr",[]),e(this,"twinData",{}),e(this,"defalutColor","rgba(220,20,60,1)"),this.app=t,this.twinData=i}startDraw(e,i,s){this.positionArr=[],this.twinData=e,t.handleMouseStyle("crosshair"),this.app.on("singleclick",(t=>{if(t&&0===t.button){const e=t.pickedPosition?t.pickedPosition:this.app.camera.screenToWorld(t.x,t.y),o=CMAP.Util.convertWorldToLonlat(e);if(o){const[t,e,n]=o;this.positionArr.push(t,e,n),this.createTempPoint(i,s)}}else t&&2===t.button&&(this.reset(),i())}),"mapPointClickEvent"),this.app.on("mouseMove",(t=>{this.refreshCopyDom(t.x,t.y)}),"mapPointMouseMoveEvent")}refreshCopyDom(t,e){const i=document.getElementById("create-line-panel"),s="<span> 鼠标左键增加,右键单击取消绘制</span>";if(i)i.style.display="block",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.innerHTML=s;else{const i=document.createElement("div");i.style.pointerEvents="none",i.style.userSelect="none",i.id="create-line-panel",i.style.position="absolute",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.style.background="#F7F8FA",i.style.color="#1D1F24",i.style.padding="4px",i.style.borderRadius="4px",i.innerHTML=s,this.app.domElement.append(i)}}hideCopyDom(){const t=document.getElementById("create-line-panel");t&&(t.style.display="none")}createTempPoint(e,i){const s=Q.defaults.headers.common.Tenant||"master",o={customColor:this.defalutColor};let n={};n=i?{...i,asset:!0,position_body:JSON.stringify(o)}:{...i,position_body:JSON.stringify(o)},this.positionArr&&this.positionArr.length&&(this.twinObject=this.app.create({type:"GeoPoint",name:"temp",id:"creatUuid",coordinates:[this.positionArr[0],this.positionArr[1]],renderer:{type:"model",url:`${window.baseConfig.previewResourceUrl}${s}/model/${this.twinData.thingsModelUuid}/0/gltf/`,size:[1,1,1]},userData:n,offsetHeight:this.positionArr[this.positionArr.length-1][2]||0,complete:i=>{e&&e(i.object),this.removeListener(),t.handleMouseStyle("default")}}))}removeListener(){this.hideCopyDom(),this.app.off("singleclick",null,"mapPointClickEvent"),this.app.off("mouseMove",null,"mapPointMouseMoveEvent")}reset(){this.twinObject&&(this.twinObject.destroy(),this.twinObject=null),this.removeListener(),t.handleMouseStyle("default")}static handleMouseStyle(t){document.getElementsByTagName("body")[0].style.cursor=t}creatThing(t,e){const i=Q.defaults.headers.common.Tenant||"master",s=this.app.query(`#${t.uuid}`)[0];if(s)return s.userData=t,void(s.userData.coords=e);let o={};o=t.position_body?JSON.parse(t.position_body):{scale:[1,1,1],angles:[0,0,0]},this.app.create({type:"GeoPoint",name:"temp",id:t.uuid,coordinates:"GCJ02"===e?JSON.parse(t.gcj02_position):JSON.parse(t.wgs84_position),renderer:{type:"model",url:`${window.baseConfig.previewResourceUrl}${i}/model/${this.twinData.thingsModelUuid}/0/gltf/`,size:[1,1,1]},angles:this.getAngles(o),scale:o.scale,userData:{coords:e,...t},offsetHeight:Number(t.gis_height||0),complete:t=>{window.mapPlacementIns.setMarker(t.object)}})}getAngles(t){var e;const i=null==(e=null==THING?void 0:THING.VERSION)?void 0:e.substring(0,1);return i===(t.engine?`${t.engine}`:"1")?t.angles:"1"===i?M(t.angles):"2"===i?D(t.angles):t.angles}},at=class t{constructor(t,i){e(this,"app"),e(this,"twinObject",null),e(this,"positionArr",[]),e(this,"twinData",{}),e(this,"defalutColor","rgba(220,20,60,1)"),e(this,"thingLayer",null),this.app=t,this.twinData=i;const s=window.app.map.layers.find((t=>"thingLayer"==t.name));s?this.thingLayer=s:(this.thingLayer=new THING.EARTH.ThingLayer({name:"thingLayer"}),window.app.map.addLayer(this.thingLayer))}startDraw(e,i,s){this.positionArr=[],this.twinData=e,t.handleMouseStyle("crosshair"),this.app.on("click",(t=>{if(t&&0===t.button){const e=t.pickedPosition?t.pickedPosition:this.app.camera.screenToWorld([t.x,t.y]),o=THING.EARTH.Utils.convertWorldToLonlat(e);if(o){const[t,e,n]=o;this.positionArr.push(t,e,n),this.createTempPoint(i,s)}}else t&&2===t.button&&(this.reset(),i())}),"mapPointClickEvent"),this.app.on("mouseMove",(t=>{this.refreshCopyDom(t.x,t.y)}),"mapPointMouseMoveEvent")}refreshCopyDom(t,e){const i=document.getElementById("create-line-panel"),s="<span> 鼠标左键增加,右键单击取消绘制</span>";if(i)i.style.display="block",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.innerHTML=s;else{const i=document.createElement("div");i.style.pointerEvents="none",i.style.userSelect="none",i.id="create-line-panel",i.style.position="absolute",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.style.background="#F7F8FA",i.style.color="#1D1F24",i.style.padding="4px",i.style.borderRadius="4px",i.innerHTML=s,this.app.container.append(i)}}hideCopyDom(){const t=document.getElementById("create-line-panel");t&&(t.style.display="none")}createTempPoint(e,i){const s=Q.defaults.headers.common.Tenant||"master",o={customColor:this.defalutColor};let n={};n=i?{...i,asset:!0,position_body:JSON.stringify(o)}:{...i,position_body:JSON.stringify(o)},this.positionArr&&this.positionArr.length&&(this.twinObject=new THING.EARTH.GeoPoint({name:"temp",id:"creatUuid",coordinates:[this.positionArr[0],this.positionArr[1]],style:{pointType:THING.EARTH.SymbolType.Model,url:`${window.baseConfig.previewResourceUrl}${s}/model/${this.twinData.thingsModelUuid}/0/gltf/`,size:1},userData:n,offsetHeight:this.positionArr[this.positionArr.length-1][2]||0}),this.thingLayer.add(this.twinObject),e&&e(this.twinObject),this.removeListener(),t.handleMouseStyle("default"))}removeListener(){this.hideCopyDom(),this.app.off("click",null,"mapPointClickEvent"),this.app.off("mouseMove",null,"mapPointMouseMoveEvent")}reset(){this.twinObject&&(this.twinObject.destroy(),this.twinObject=null),this.removeListener(),t.handleMouseStyle("default")}static handleMouseStyle(t){document.getElementsByTagName("body")[0].style.cursor=t}creatThing(t,e){const i=Q.defaults.headers.common.Tenant||"master",s=this.app.query(`#${t.uuid}`)[0];if(s)return s.userData=t,void(s.userData.coords=e);let o={};o=t.position_body?JSON.parse(t.position_body):{scale:[1,1,1],angles:[0,0,0]};const n=new THING.EARTH.GeoPoint({name:"temp",id:t.uuid,coordinates:"GCJ02"===e?JSON.parse(t.gcj02_position):JSON.parse(t.wgs84_position),style:{pointType:THING.EARTH.SymbolType.Model,url:`${window.baseConfig.previewResourceUrl}${i}/model/${this.twinData.thingsModelUuid}/0/gltf/`,size:[1,1,1]},scale:o.scale,userData:{coords:e,...t},offsetHeight:Number(t.gis_height||0),complete:t=>{window.mapPlacementIns.setMarker(t.object);const e=this.getAngles(o);e&&(t.object.angles=e)}});this.thingLayer.add(n)}getAngles(t){var e;const i=null==(e=null==THING?void 0:THING.VERSION)?void 0:e.substring(0,1),s=t.engine?`${t.engine}`:"";return s?i===s?t.angles:"1"===i?M(t.angles):"2"===i?D(t.angles):t.angles:null}},lt=class{constructor(t){e(this,"app"),e(this,"twinData",null),e(this,"positionArr",[]),e(this,"pointHeight",[]),e(this,"refreshLine",null),e(this,"twinObject",null),e(this,"render",null),e(this,"defalutColor",""),this.app=t}startDraw(t,e,i){const s=this;this.twinData=t,this.positionArr=[],this.pointHeight=[],this.refreshLine=null,this.changeCursorStyle(),this.defalutColor=t.customColor||"rgba(220,20,60,1)";const o=S(this.defalutColor);this.render={type:"vector",lineType:"Route",color:o.color,width:5,growSpeed:0,growLoop:!1,effect:!1,opacity:o.opacity},this.app.on("singleclick",(t=>{if(t&&0===t.button){const e=t.pickedPosition?t.pickedPosition:this.app.camera.screenToWorld(t.x,t.y),i=CMAP.Util.convertWorldToLonlat(e),[s,o,n]=i;this.pointHeight.push(n<0?0:n),this.positionArr.push([s,o]),this.positionArr.length>=2&&this.createTempLine([this.positionArr[this.positionArr.length-2],[s,o]],[this.pointHeight[this.pointHeight.length-2],n])}else t&&2===t.button&&this.createRouteLineTemp(e,i)}),"mapLineClickEvent"),this.app.on("mouseMove",(t=>{if(s.refreshCopyDom(t.x,t.y),this.positionArr&&this.positionArr.length>=1){const e=t.pickedPosition?t.pickedPosition:this.app.camera.screenToWorld(t.x,t.y),i=CMAP.Util.convertWorldToLonlat(e),[s,o,n]=i;this.createRefreshLine([this.positionArr[this.positionArr.length-1],[s,o]],[this.pointHeight[this.pointHeight.length-1],n])}}),"mapLineMouseMoveEvent"),this.app.on("dblclick",(t=>{t&&2===t.button&&(this.reset(),e())}),"mapLineDblClickEvent")}createTempLine(t,e){this.app.create({type:"GeoLine",name:"temp_routeLine",coordinates:t,heightArray:e,renderer:this.render})}refreshCopyDom(t,e){const i=document.getElementById("create-line-panel"),s="<span> 鼠标左键增加,右键单击结束,右键双击取消绘制</span>";if(i)i.style.display="block",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.innerHTML=s;else{const i=document.createElement("div");i.style.pointerEvents="none",i.style.userSelect="none",i.id="create-line-panel",i.style.position="absolute",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.style.background="#F7F8FA",i.style.color="#1D1F24",i.style.padding="4px",i.style.borderRadius="4px",i.innerHTML=s,this.app.domElement.append(i)}}hideCopyDom(){const t=document.getElementById("create-line-panel");t&&(t.style.display="none")}createRefreshLine(t,e){this.refreshLine&&this.refreshLine.destroy(),this.refreshLine=this.app.create({type:"GeoLine",name:"refresh_routeLine",coordinates:t,heightArray:e,renderer:this.render})}createRouteLineTemp(t,e){const i={customColor:this.defalutColor};let s={};s=e?{...e,asset:!0,position_body:JSON.stringify(i)}:{position_body:JSON.stringify(i)},this.positionArr&&this.positionArr.length>1&&(this.app.query("refresh_routeLine").destroyAll(),this.app.query("temp_routeLine").destroyAll(),this.twinObject=this.app.create({id:"creatUuid",type:"GeoLine",name:"temp",coordinates:this.positionArr,heightArray:this.pointHeight,renderer:this.render,scale:[1,1,1],userData:s,offsetHeight:0,complete:e=>{t&&t(e.object),this.removeListener(),this.resetCursorStrle()}}))}reset(){this.twinObject&&(this.twinObject.destroy(),this.twinObject=null),this.app.query("refresh_routeLine").destroyAll(),this.app.query("temp_routeLine").destroyAll(),this.removeListener(),this.resetCursorStrle()}changeCursorStyle(){this.app.domElement.style.setProperty("cursor",'url("/base/img/pen.ico") 10 24,auto',"important")}resetCursorStrle(){this.app.domElement.style.cursor="default"}removeListener(){this.hideCopyDom(),this.app.off("mousemove",null,"mapLineMouseMoveEvent"),this.app.off("singleclick",null,"mapLineClickEvent"),this.app.off("dblclick",null,"mapLineDblClickEvent")}creatThing(t,e){const i=this.app.query(`#${t.uuid}`)[0];if(i)return i.userData=t,void(i.userData.coords=e);let s={};s=t.position_body?JSON.parse(t.position_body):{scale:[1,1,1],angles:[0,0,0],customColor:"rgba(220,20,60,1)"};const o=S(s.customColor),n={type:"vector",lineType:"Route",color:o.color,width:5,growSpeed:0,growLoop:!1,effect:!1,opacity:o.opacity};this.app.create({type:"GeoLine",name:"temp",id:t.uuid,coordinates:"GCJ02"===e?JSON.parse(t.gcj02_position):JSON.parse(t.wgs84_position),renderer:n,angles:this.getAngles(s),scale:s.scale,userData:{coords:e,...t},offsetHeight:Number(t.gis_height||0),complete:t=>{window.mapPlacementIns.setMarker(t.object)}})}getAngles(t){var e;const i=null==(e=null==THING?void 0:THING.VERSION)?void 0:e.substring(0,1);return i===(t.engine?`${t.engine}`:"1")?t.angles:"1"===i?M(t.angles):"2"===i?D(t.angles):t.angles}},pt=class{constructor(t){e(this,"app"),e(this,"twinData",null),e(this,"positionArr",[]),e(this,"pointHeight",[]),e(this,"refreshLine",null),e(this,"twinObject",null),e(this,"render",null),e(this,"defalutColor",""),e(this,"thingLayer",null),this.app=t;const i=window.app.map.layers.find((t=>"thingLayer"==t.name));i?this.thingLayer=i:(this.thingLayer=new THING.EARTH.ThingLayer({name:"thingLayer"}),window.app.map.addLayer(this.thingLayer))}startDraw(t,e,i){this.twinData=t,this.positionArr=[],this.pointHeight=[],this.refreshLine=null,this.changeCursorStyle(),this.defalutColor=t.customColor||"rgba(220,20,60,1)";const s=S(this.defalutColor);this.render={color:"rgb("+s.color.toString()+")",effect:!1,opacity:s.opacity,lineType:THING.EARTH.GeoLineType.Route,speed:0,width:5},this.app.on("click",(t=>{if(t&&0===t.button){const e=t.pickedPosition;if(e){const t=THING.EARTH.Utils.convertWorldToLonlat(e),[i,s,o]=t;this.pointHeight.push(o<0?0:o),this.positionArr.push([i,s]),this.positionArr.length>=2&&this.createTempLine([this.positionArr[this.positionArr.length-2],[i,s]],[this.pointHeight[this.pointHeight.length-2],o])}}else t&&2===t.button&&this.createRouteLineTemp(e,i)}),"mapLineClickEvent"),this.app.on("mouseMove",(t=>{if(this.refreshCopyDom(t.x,t.y),this.positionArr&&this.positionArr.length>=1){const e=t.pickedPosition;if(e){const t=THING.EARTH.Utils.convertWorldToLonlat(e),[i,s,o]=t,n=o<0?0:o;this.createRefreshLine([this.positionArr[this.positionArr.length-1],[i,s]],[this.pointHeight[this.pointHeight.length-1],n])}}}),"mapLineMouseMoveEvent"),this.app.on("dblclick",(t=>{t&&2===t.button&&(this.reset(),e())}),"mapLineDblClickEvent")}refreshCopyDom(t,e){const i=document.getElementById("create-line-panel"),s="<span> 鼠标左键增加,右键单击结束,右键双击取消绘制</span>";if(i)i.style.display="block",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.innerHTML=s;else{const i=document.createElement("div");i.style.pointerEvents="none",i.style.userSelect="none",i.id="create-line-panel",i.style.position="absolute",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.style.background="#F7F8FA",i.style.color="#1D1F24",i.style.padding="4px",i.style.borderRadius="4px",i.innerHTML=s,this.app.container.append(i)}}hideCopyDom(){const t=document.getElementById("create-line-panel");t&&(t.style.display="none")}createTempLine(t,e){const i=new THING.EARTH.GeoLine({name:"temp_routeLine",coordinates:t,heights:e,style:this.render});this.thingLayer.add(i)}createRefreshLine(t,e){this.refreshLine&&this.refreshLine.destroy(),this.refreshLine=new THING.EARTH.GeoLine({name:"refresh_routeLine",coordinates:t,heights:e,style:this.render}),this.thingLayer.add(this.refreshLine)}createRouteLineTemp(t,e){const i={customColor:this.defalutColor};let s={};if(s=e?{...e,asset:!0,position_body:JSON.stringify(i)}:{position_body:JSON.stringify(i)},this.positionArr&&this.positionArr.length>1){const e=this.app.query("refresh_routeLine");e&&e.length>0&&e.forEach((t=>{t.destroy()}));const i=this.app.query("temp_routeLine");i&&i.length>0&&i.forEach((t=>{t.destroy()})),this.twinObject=new THING.EARTH.GeoLine({id:"creatUuid",name:"temp",coordinates:this.positionArr,heights:this.pointHeight,style:this.render,userData:s,offsetHeight:0,complete:t=>{}}),this.thingLayer.add(this.twinObject),this.removeListener(),this.resetCursorStrle(),t&&t(this.twinObject)}}reset(){this.twinObject&&(this.twinObject.destroy(),this.twinObject=null);const t=this.app.query("refresh_routeLine");t&&t.length>0&&t.forEach((t=>{t.destroy()}));const e=this.app.query("temp_routeLine");e&&e.length>0&&e.forEach((t=>{t.destroy()})),this.removeListener(),this.resetCursorStrle()}changeCursorStyle(){this.app.container.style.setProperty("cursor",'url("/base/img/pen.ico") 10 24,auto',"important")}resetCursorStrle(){this.app.container.style.cursor="default"}removeListener(){this.hideCopyDom(),this.app.off("mousemove",null,"mapLineMouseMoveEvent"),this.app.off("click",null,"mapLineClickEvent"),this.app.off("dblclick",null,"mapLineDblClickEvent")}creatThing(t,e){const i=this.app.query(`#${t.uuid}`)[0];if(i)return i.userData=t,void(i.userData.coords=e);let s={};s=t.position_body?JSON.parse(t.position_body):{scale:[1,1,1],angles:[0,0,0],customColor:"rgba(220,20,60,1)"};const o=S(s.customColor),n={lineType:THING.EARTH.GeoLineType.Route,color:"rgb("+o.color.toString()+")",width:5,speed:0,effect:!1,opacity:o.opacity},r=new THING.EARTH.GeoLine({name:"temp",id:t.uuid,coordinates:"GCJ02"===e?JSON.parse(t.gcj02_position):JSON.parse(t.wgs84_position),style:n,angles:this.getAngles(s),scale:s.scale,userData:{coords:e,...t},offsetHeight:Number(t.gis_height||0),complete:t=>{window.mapPlacementIns.setMarker(t.object)}});this.thingLayer.add(r)}getAngles(t){var e;const i=null==(e=null==THING?void 0:THING.VERSION)?void 0:e.substring(0,1),s=t.engine?`${t.engine}`:"";return s?i===s?t.angles:"1"===i?M(t.angles):"2"===i?D(t.angles):t.angles:null}},ct=class{constructor(t){e(this,"app"),e(this,"twinData",null),e(this,"twinObject",null),e(this,"positionArr",[]),e(this,"refreshLine",null),e(this,"render",{}),e(this,"lineRender",{}),e(this,"defalutColor",""),this.app=t}startDraw(t,e,i){this.twinData=t,this.positionArr=[],this.changeCursorStyle(),this.defalutColor=t.customColor||"rgba(220,20,60,1)";const s=S(this.defalutColor);this.render={type:"vector",color:s.color,opacity:s.opacity,lights:!1},this.lineRender={type:"vector",lineType:"Route",color:s.color,width:2,effect:!1,opacity:s.opacity},this.app.on("singleclick",(t=>{if(t&&0===t.button){const e=t.pickedPosition?t.pickedPosition:this.app.camera.screenToWorld(t.x,t.y),i=CMAP.Util.convertWorldToLonlat(e),[s,o]=i;this.positionArr.push([s,o]),this.positionArr.length>=2&&this.createTempLine([this.positionArr[this.positionArr.length-2],[s,o]])}else t&&2===t.button&&this.createRegionTemp(e,i)}),"commonRegionClickEvent"),this.app.on("mouseMove",(t=>{if(this.refreshCopyDom(t.x,t.y),this.positionArr&&this.positionArr.length>=1){const e=t.pickedPosition?t.pickedPosition:this.app.camera.screenToWorld(t.x,t.y),i=CMAP.Util.convertWorldToLonlat(e),[s,o]=i;this.createRefreshLine([this.positionArr[this.positionArr.length-1],[s,o]])}}),"commonRegionMouseMoveEvent"),this.app.on("dblclick",(t=>{t&&2===t.button&&(this.reset(),e())}),"mapRegionDblClickEvent")}createTempLine(t){this.app.create({type:"GeoLine",name:"temp_routeLine",coordinates:t,renderer:this.lineRender})}createRefreshLine(t){this.refreshLine&&this.refreshLine.destroy(),this.refreshLine=this.app.create({type:"GeoLine",name:"refresh_routeLine",coordinates:t,renderer:this.lineRender})}createRegionTemp(t,e){const i={customColor:this.defalutColor};let s={};s=e?{...e,asset:!0,position_body:JSON.stringify(i)}:{position_body:JSON.stringify(i)},this.positionArr&&this.positionArr.length>1&&(this.app.query(".GeoLine").destroyAll(),this.twinObject=this.app.create({type:"GeoPolygon",name:"temp",id:"creatUuid",coordinates:this.positionArr,extrudeHeight:0,renderer:this.render,scale:[1,1,1],userData:s,offsetHeight:0,complete:e=>{t&&t(e.object),this.removeListener(),this.resetCursorStrle()}}))}reset(){this.twinObject&&(this.twinObject.destroy(),this.twinObject=null),this.app.query(".GeoLine").destroyAll(),this.removeListener(),this.resetCursorStrle()}refreshCopyDom(t,e){const i=document.getElementById("create-line-panel"),s="<span> 鼠标左键增加,右键单击结束,右键双击取消绘制</span>";if(i)i.style.display="block",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.innerHTML=s;else{const i=document.createElement("div");i.style.pointerEvents="none",i.style.userSelect="none",i.id="create-line-panel",i.style.position="absolute",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.style.background="#F7F8FA",i.style.color="#1D1F24",i.style.padding="4px",i.style.borderRadius="4px",i.innerHTML=s,this.app.domElement.append(i)}}hideCopyDom(){const t=document.getElementById("create-line-panel");t&&(t.style.display="none")}changeCursorStyle(){this.app.domElement.style.setProperty("cursor",'url("/base/img/pen.ico") 10 24,auto',"important")}resetCursorStrle(){this.app.domElement.style.cursor="default"}removeListener(){this.hideCopyDom(),this.app.off("mousemove",null,"commonRegionMouseMoveEvent"),this.app.off("singleclick",null,"commonRegionClickEvent"),this.app.off("dblclick",null,"mapRegionDblClickEvent")}creatThing(t,e){const i=this.app.query(`#${t.uuid}`)[0];if(i)return i.userData=t,void(i.userData.coords=e);let s={};s=t.position_body?JSON.parse(t.position_body):{scale:[1,1,1],angles:[0,0,0],customColor:"rgba(220,20,60,1)"};const o=S(s.customColor),n={type:"vector",color:o.color,opacity:o.opacity,lights:!1};this.app.create({type:"GeoPolygon",name:"temp",id:t.uuid,coordinates:"GCJ02"===e?JSON.parse(t.gcj02_position):JSON.parse(t.wgs84_position),renderer:n,angles:this.getAngles(s),scale:s.scale,userData:{coords:e,...t},offsetHeight:Number(t.gis_height||0),complete(t){window.mapPlacementIns.setMarker(t.object)}})}getAngles(t){var e;const i=null==(e=null==THING?void 0:THING.VERSION)?void 0:e.substring(0,1);return i===(t.engine?`${t.engine}`:"1")?t.angles:"1"===i?M(t.angles):"2"===i?D(t.angles):t.angles}},ht=class{constructor(t){e(this,"app"),e(this,"twinData",null),e(this,"twinObject",null),e(this,"positionArr",[]),e(this,"refreshLine",null),e(this,"render",{}),e(this,"lineRender",{}),e(this,"defalutColor",""),e(this,"thingLayer",null),this.app=t;const i=window.app.map.layers.find((t=>"thingLayer"==t.name));i?this.thingLayer=i:(this.thingLayer=new THING.EARTH.ThingLayer({name:"thingLayer"}),window.app.map.addLayer(this.thingLayer))}startDraw(t,e,i){this.twinData=t,this.positionArr=[],this.changeCursorStyle(),this.defalutColor=t.customColor||"rgba(220,20,60,1)";const s=S(this.defalutColor);this.render={type:"vector",color:"rgb("+s.color.toString()+")",opacity:s.opacity,lights:!1},this.lineRender={color:"rgb("+s.color.toString()+")",effect:!1,opacity:s.opacity,lineType:THING.EARTH.GeoLineType.Line,width:5},this.app.on("click",(t=>{if(t&&0===t.button){const e=t.pickedPosition;if(e){const t=THING.EARTH.Utils.convertWorldToLonlat(e),[i,s]=t;this.positionArr.push([i,s]),this.positionArr.length>=2&&this.createTempLine([this.positionArr[this.positionArr.length-2],[i,s]])}}else t&&2===t.button&&this.createRegionTemp(e,i)}),"commonRegionClickEvent"),this.app.on("mouseMove",(t=>{if(this.refreshCopyDom(t.x,t.y),this.positionArr&&this.positionArr.length>=1){const e=t.pickedPosition;if(e){const t=THING.EARTH.Utils.convertWorldToLonlat(e),[i,s]=t;this.createRefreshLine([this.positionArr[this.positionArr.length-1],[i,s]])}}}),"commonRegionMouseMoveEvent"),this.app.on("dblclick",(t=>{t&&2===t.button&&(this.reset(),e())}),"mapRegionDblClickEvent")}createTempLine(t){const e=new THING.EARTH.GeoLine({name:"temp_routeLine",coordinates:t,style:this.lineRender});this.thingLayer.add(e)}createRefreshLine(t){this.refreshLine&&this.refreshLine.destroy(),this.refreshLine=new THING.EARTH.GeoLine({name:"refresh_routeLine",coordinates:t,style:this.lineRender}),this.thingLayer.add(this.refreshLine)}createRegionTemp(t,e){const i={customColor:this.defalutColor};let s={};if(s=e?{...e,asset:!0,position_body:JSON.stringify(i)}:{position_body:JSON.stringify(i)},this.positionArr&&this.positionArr.length>1){const e=this.app.query(".GeoLine");e&&e.length>0&&e.forEach((t=>{t.destroy()})),this.twinObject=new THING.EARTH.GeoPolygon({name:"temp",id:"creatUuid",coordinates:this.positionArr,extrudeHeight:0,style:this.render,userData:s,offsetHeight:0,complete:t=>{}}),this.thingLayer.add(this.twinObject),this.removeListener(),this.resetCursorStrle(),t&&t(this.twinObject)}}reset(){this.twinObject&&(this.twinObject.destroy(),this.twinObject=null);const t=this.app.query(".GeoLine");t&&t.length>0&&t.forEach((t=>{t.destroy()})),this.removeListener(),this.resetCursorStrle()}refreshCopyDom(t,e){const i=document.getElementById("create-line-panel"),s="<span> 鼠标左键增加,右键单击结束,右键双击取消绘制</span>";if(i)i.style.display="block",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.innerHTML=s;else{const i=document.createElement("div");i.style.pointerEvents="none",i.style.userSelect="none",i.id="create-line-panel",i.style.position="absolute",i.style.left=`${t+20}px`,i.style.top=`${e}px`,i.style.background="#F7F8FA",i.style.color="#1D1F24",i.style.padding="4px",i.style.borderRadius="4px",i.innerHTML=s,this.app.container.append(i)}}hideCopyDom(){const t=document.getElementById("create-line-panel");t&&(t.style.display="none")}changeCursorStyle(){this.app.container.style.setProperty("cursor",'url("/base/img/pen.ico") 10 24,auto',"important")}resetCursorStrle(){this.app.container.style.cursor="default"}removeListener(){this.hideCopyDom(),this.app.off("mousemove",null,"commonRegionMouseMoveEvent"),this.app.off("click",null,"commonRegionClickEvent"),this.app.off("dblclick",null,"mapRegionDblClickEvent")}creatThing(t,e){const i=this.app.query(`#${t.uuid}`)[0];if(i)return i.userData=t,void(i.userData.coords=e);let s={};s=t.position_body?JSON.parse(t.position_body):{scale:[1,1,1],angles:[0,0,0],customColor:"rgba(220,20,60,1)"};const o=S(s.customColor),n={type:"vector",color:"rgb("+o.color.toString()+")",opacity:o.opacity,lights:!1},r="GCJ02"===e?JSON.parse(t.gcj02_position):JSON.parse(t.wgs84_position),a=new THING.EARTH.GeoPolygon({name:"temp",id:t.uuid,coordinates:r,style:n,angles:this.getAngles(s),scale:s.scale,userData:{coords:e,...t},offsetHeight:Number(t.gis_height||0),complete(t){window.mapPlacementIns.setMarker(t.object)}});this.thingLayer.add(a)}getAngles(t){var e;const i=null==(e=null==THING?void 0:THING.VERSION)?void 0:e.substring(0,1),s=t.engine?`${t.engine}`:"";return s?i===s?t.angles:"1"===i?M(t.angles):"2"===i?D(t.angles):t.angles:null}};const dt={class:"map-twin keep-px"},ut={class:"tabs-list"},mt={class:"campus-name"},gt=["src"],At={class:"header-title"},yt={class:"map-list"},bt={class:"btn-list"},wt={class:"map-container"},ft={class:"mouse-pos"},vt={class:"pos-wrappper"},jt={key:0,id:"map-place-twin-modal",class:"map-place-twin-modal"},Ct=X(i({__name:"Index",setup(t){const e=s(sessionStorage.getItem("XI_TONG_LOGO")||K),i=s(sessionStorage.getItem("XI_TONG_BIAO_TI")||"");let S=document.querySelector('link[rel="icon"]');S&&(S.href=e.value);const k=s(sessionStorage.getItem("PROJECT_NAME")||"");k.value?document.title=`${i.value} - ${k.value}`:document.title=`${i.value}`;const M=j(),D=R(),U=H(),J=s(),{query:Q}=j(),{name:X}=Q;J.value=X;let _=null;const $=s(!0),tt=s(0),et=s(),st=s([]),ot=s(0),Ct=s({}),Ot=o({lat:0,lng:0}),Tt=s({}),Lt=s(),Et=t=>{var e,i,s,o;if(ot.value=t,Ct.value.coords!==(null==(e=st.value[ot.value])?void 0:e.coords)){window.mapPlacementIns.reset(!0),window.mapDrawInstance.reset(),D.markFlag=!1;const t=window.app.query(".GeoPoint"),e=window.app.query(".GeoLine"),i=window.app.query(".GeoPolygon");1===U.thingjsVersion?(t.destroyAll(),e.destroyAll(),i.destroyAll()):2===U.thingjsVersion&&(t.forEach((t=>{t.destroy()})),e.forEach((t=>{t.destroy()})),i.forEach((t=>{t.destroy()}))),xt()}Ct.value=st.value[ot.value],1===U.thingjsVersion?window.mapManagerIns.changeTile({tileLayerUrl:null==(i=st.value[ot.value])?void 0:i.tilesUrl,maximumLevel:18}):2===U.thingjsVersion&&window.mapManagerIns.changeTile({url:null==(s=st.value[ot.value])?void 0:s.tilesUrl,name:null==(o=st.value[ot.value])?void 0:o.name,maximumLevel:18})},It=()=>{E({}).then((async t=>{if(t.success){if(!t.data||!t.data.length)return void P("error","请添加地图瓦片服务！");st.value=t.data.filter((t=>0===t.type)),ot.value=0,Ct.value=st.value[0],await(async()=>{const t=await N({id:M.query.id});200===t.code&&(t.data.bubbleInfoVo.imgUrl=`${window.baseConfig.previewResourceUrl}${t.data.bubbleInfoVo.imgUrl}`,Tt.value=t.data,D.updateTwinData(t.data),Lt.value.init(Tt.value))})(),St()}}))},St=()=>{kt((()=>{Pt()}))},kt=t=>{B((()=>{t&&t()}),["thingjs","uearth","thing.campus"])},Pt=async()=>{var t;Mt();const e=await T("earthOutContainer");window.app=e,window.mapManagerIns=L({app:e,tileLayerUrl:null==(t=st.value[ot.value])?void 0:t.tilesUrl,mapLoaded:t=>{t.attribution="none",Dt(),window.app.on(THING.EventType.MouseMove,(t=>{const e=t.pickedPosition?t.pickedPosition:1===U.thingjsVersion?window.app.camera.screenToWorld(t.x,t.y):window.app.camera.screenToWorld([t.x,t.y]),i=1===U.thingjsVersion?CMAP.Util.convertWorldToLonlat(e):THING.EARTH.Utils.convertWorldToLonlat(e),[s,o]=i;Ot.lat=o,Ot.lng=s})),_&&clearInterval(_),tt.value=100,setTimeout((()=>{$.value=!1}),50),Gt(),Ut()}})},Mt=()=>{_=setInterval((()=>{tt.value<99&&(tt.value+=1)}),5)},Dt=()=>{if(Ct.value.defaultCamInfo){const t=JSON.parse(Ct.value.defaultCamInfo);window.app.camera.flyTo({position:t.position,target:t.target,time:0})}else{let t=[116.397451,39.909187];"WGS84"===Ct.value.coords&&(t=C.gcj02towgs84(t[0],t[1])),window.app.camera.earthFlyTo({lonlat:t,time:3e3,height:1e3})}},Ht=()=>{const t={position:window.app.camera.position,target:window.app.camera.target};I(t).then((t=>{200===t.code&&P("success","默认视角保存成功")}))},Bt=s(),Nt=()=>{Bt.value.init()},Gt=()=>{window.mapPlacementIns=(t=>{const e=H();return 1===e.thingjsVersion?new it(t):2===e.thingjsVersion?new nt(t):void 0})(window.app),"POINT"===Tt.value.dataType?window.mapDrawInstance=((t,e)=>{const i=H();return 1===i.thingjsVersion?new rt(t,e):2===i.thingjsVersion?new at(t,e):void 0})(window.app,Tt.value):"LINE"===Tt.value.dataType?window.mapDrawInstance=(t=>{const e=H();return 1===e.thingjsVersion?new lt(t):2===e.thingjsVersion?new pt(t):void 0})(window.app):"SURFACE"===Tt.value.dataType&&(window.mapDrawInstance=(t=>{const e=H();return 1===e.thingjsVersion?new ct(t):2===e.thingjsVersion?new ht(t):void 0})(window.app))},Rt=()=>{Lt.value.getData(),xt(),D.markFlag&&et.value.handleMark(!0)},Ut=()=>{const t=Tt.value.code;G({"[]":{[t]:{"@order":"create_time-,uuid-"}},"total@":"/[]/total","info@":"/[]/info"}).then((e=>{(e["[]"]||[]).forEach((e=>{(e[t].wgs84_position||e[t].gcj02_position)&&window.mapDrawInstance.creatThing(e[t],Ct.value.coords)}))}))},Ft=t=>{et.value.handleMark(!0,t)},xt=()=>{Ut()};return n((()=>D.deleteThing),(()=>{Rt()})),r((()=>{It()})),(t,i)=>{const s=V,o=F,n=x,r=g("eye-outlined");return v(),a("div",dt,[l("div",ut,[l("div",mt,[l("img",{src:e.value,class:"header-logo",alt:"header-logo"},null,8,gt),l("div",At,d(J.value),1)]),l("div",yt,[p(o,{modelValue:ot.value,"onUpdate:modelValue":i[0]||(i[0]=t=>ot.value=t),onChange:Et},{default:u((()=>[(v(!0),a(b,null,w(st.value,((t,e)=>(v(),f(s,{key:e,tab:`${t.name}(${t.coords})`},null,8,["tab"])))),128))])),_:1},8,["modelValue"])]),l("div",bt,[p(n,{class:"addmap",type:"primary",onClick:Nt},{default:u((()=>i[1]||(i[1]=[m(" 新增地图服务 ")]))),_:1})])]),l("div",wt,[p(Y,{ref_key:"mapLayerRef",ref:et,"current-tab":Ct.value},null,8,["current-tab"]),p(W,null,{default:u((()=>[p(O,{ref_key:"twinDataManageRef",ref:Lt,onSetAssMapOpenEdit:Ft,onRefreshMap:xt},null,512)])),_:1}),l("div",ft,[l("div",vt,[l("span",null,[i[2]||(i[2]=l("label",null,"经度：",-1)),m(d((+Ot.lng).toFixed(6)),1)]),l("span",null,[i[3]||(i[3]=l("label",null,"纬度：",-1)),m(d((+Ot.lat).toFixed(6)),1)]),l("span",{title:"保存默认视角",onClick:Ht},[p(r)])])]),i[4]||(i[4]=l("div",{id:"earthOutContainer",class:"earth-container"},null,-1))]),p(Z,{ref_key:"addMapServeRef",ref:Bt},null,512),c(p(z,{"loading-percent":tt.value},null,8,["loading-percent"]),[[A,$.value]]),y(D).showEditPanel?(v(),a("div",jt,[p(q,{"current-tab":Ct.value,onUpdate:Rt},null,8,["current-tab"])])):h("",!0)])}}}),[["__scopeId","data-v-30d0c5d0"]]);export{Ct as default};
