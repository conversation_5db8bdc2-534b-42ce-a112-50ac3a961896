import s from"./Index-0BREZ92i.js";import i from"./Index-C1p7mWM5.js";import{d as t,r as o,S as e,U as r,am as p,F as m,b7 as a,Z as j,bk as d,V as n,al as l}from"./@vue-DgI1lw0Y.js";import{_ as u}from"./vue-qr-6l_NUpj8.js";import"./lodash-BZHY5H3K.js";import"./@babel-B4rXMRun.js";import"./DepartGroup-DfQM2cm8.js";import"./bubble-CENITBfd.js";import"./main-DE7o6g98.js";import"./ant-design-vue-DW0D0Hn-.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./dayjs-D9wJ8dSB.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";/* empty css                      */import"./AddEditForm-CHgRQNLk.js";import"./attachmentManage-CasMLJka.js";import"./delayedUpload-3Ol5fK1t.js";import"./legend-D6xDqzgJ.js";import"./model-BUBDdBL2.js";import"./Index-DliAELKp.js";import"./nodata-dark-DHz_m8dv.js";import"./Detail-4l1XJAkN.js";import"./clipboard-Dv7Qpqbb.js";import"./vue-pick-colors-CjUSS-xa.js";import"./@popperjs-CFrBDmKk.js";import"./js-binary-schema-parser-G48GG52R.js";import"./AddOrEdit.vue_vue_type_style_index_0_lang-B0JB0YoY.js";import"./basicModelTip4-CPNhDiAx.js";import"./UploadModal.vue_vue_type_script_setup_true_lang-BRboDosL.js";import"./Index-CPLrtVLz.js";import"./AddForm.vue_vue_type_style_index_0_lang-DCX-wfEb.js";import"./AddOrEdit.vue_vue_type_style_index_0_lang-Dur8dl0H.js";const c={class:"source-manage"},v={class:"third-menu"},_={class:"third-menu-wrap"},y={class:"menu-content"},b=["onClick"],g={class:"name"},k={class:"content"},x=u(t({__name:"Index",setup(t){const u=o([{id:1,name:"图标库"},{id:2,name:"模型库"}]),x=o(1);return(t,o)=>(r(),e("div",c,[p("div",v,[p("div",_,[p("div",y,[(r(!0),e(m,null,a(u.value,(s=>(r(),e("div",{key:s.id,class:j(["content-item",s.id===x.value?"active":""]),onClick:i=>(s=>{x.value!==s.id&&(x.value=s.id)})(s)},[p("span",g,d(s.name),1)],10,b)))),128))])])]),p("div",k,[1===x.value?(r(),n(s,{key:0})):l("",!0),2===x.value?(r(),n(i,{key:1})):l("",!0)])]))}}),[["__scopeId","data-v-a0c26840"]]);export{x as default};
