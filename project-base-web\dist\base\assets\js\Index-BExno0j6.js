import{u as e}from"./main-DE7o6g98.js";import{u as s}from"./useTableScrollY-9oHU_oJI.js";import{p as a,q as t,r as o}from"./mapManag-wSwfWE2D.js";import i from"./AddEditForm-DWt1JnIc.js";import{B as r,i as l,_ as n,b as p}from"./ant-design-vue-DW0D0Hn-.js";import{d,a as m,r as c,j as u,o as j,S as y,U as g,am as k,c as v,V as f,al as h,bJ as b,G as w,u as _,W as x}from"./@vue-DgI1lw0Y.js";import{_ as S}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const z={class:"map-tiles"},C={class:"table-wrap"},I={class:"table-handle"},P={key:0,class:"table-actions"},L=["onClick"],N={key:3,class:"disable"},R={class:"pagination"},D=S(d({__name:"Index",setup(d){const S=m({pageNo:1,pageSize:10}),D=c(0),J=m([{title:"地图名称",dataIndex:"name",key:"name",ellipsis:!0},{title:"坐标系",dataIndex:"coords",key:"coords",ellipsis:!0},{title:"类型",dataIndex:"typeStr",key:"typeStr",ellipsis:!0},{title:"瓦片地址",dataIndex:"tilesUrl",key:"tilesUrl"},{title:"操作",key:"action",width:240}]),T=u((()=>({current:S.pageNo,pageSize:S.pageSize,total:D.value,pageSizeOptions:["10","20","50","100"],showTotal:(e,s)=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),U=(e,s)=>{S.pageNo=e,S.pageSize=s,q()},A=c(),{scrollY:E}=s(A);let F=m([]);const O=m({selectedRowKeys:[],loading:!1,addLoading:!1,delLoading:!1}),W=()=>{S.pageNo=1,q()},q=()=>{O.loading=!0,a(S).then((e=>{var s;O.loading=!1,200===e.code&&(F=null==(s=e.data.rows)?void 0:s.map((e=>({...e,typeStr:0===e.type?"底图":1===e.type?"正射影像":""}))),D.value=e.data.totalRows)}),(()=>{O.loading=!1}))},G=c(),K=(e,s)=>{G.value.init(e,s)},Y=()=>{W()};return j((()=>{q()})),(s,a)=>{const d=r,m=l,c=n,u=p;return g(),y("div",z,[k("div",C,[k("div",I,[s.hasPerm("sys-map-source:add")?(g(),f(d,{key:0,type:"primary",class:"handle-btn",loading:O.addLoading,onClick:a[0]||(a[0]=e=>K("add",null))},{default:b((()=>a[1]||(a[1]=[w(" 新增地图配置 ")]))),_:1},8,["loading"])):h("",!0)]),k("div",{ref_key:"table",ref:A,class:"table-content"},[v(c,{class:"table",scroll:{y:_(E)},pagination:!1,"row-key":e=>e.id,size:"small",columns:J,loading:O.loading,"data-source":_(F)},{bodyCell:b((({column:i,record:r})=>["action"===i.key?(g(),y("div",P,[s.hasPerm("sys-map-source:edit")?(g(),y("a",{key:0,onClick:e=>K("edit",r)},"编辑",8,L)):h("",!0),s.hasPerm("sys-map-source:delete")?(g(),f(m,{key:1,placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:s=>(s=>{o(s).then((s=>{200===s.code?(e("success","地图瓦片删除成功"),W()):e("error","地图瓦片删除失败")}))})(r)},{title:b((()=>a[2]||(a[2]=[k("p",null,"确定要删除吗?",-1)]))),default:b((()=>[a[3]||(a[3]=k("a",null,"删除",-1))])),_:2},1032,["onConfirm"])):h("",!0),!r.isDefault&&0===r.type&&s.hasPerm("sys-map-source:change")?(g(),f(m,{key:2,placement:"topRight",title:"设置为默认瓦片？","ok-text":"是","cancel-text":"否",onConfirm:s=>(s=>{t({id:s.id}).then((s=>{200===s.code?(e("success","默认数据设置成功"),W()):e("error","默认数据设置失败")}))})(r)},{default:b((()=>a[4]||(a[4]=[k("a",null,"设为默认",-1)]))),_:2},1032,["onConfirm"])):!r.isDefault&&0!==r.type&&s.hasPerm("DTWP:DEFAULT")?(g(),y("a",N,"设为默认")):h("",!0)])):h("",!0)])),_:1},8,["scroll","row-key","columns","loading","data-source"]),k("div",R,[_(F).length>0?(g(),f(u,x({key:0},T.value,{onChange:U}),null,16)):h("",!0)])],512)]),v(i,{ref_key:"addEditFormRef",ref:G,onOk:Y},null,512)])}}}),[["__scopeId","data-v-81679744"]]);export{D as default};
