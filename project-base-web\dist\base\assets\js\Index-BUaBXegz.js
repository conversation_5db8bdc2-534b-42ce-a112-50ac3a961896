import{d as e,a as s,j as a,r as t,o as i,S as o,U as r,am as l,c as n,bL as d,bJ as c,G as p,al as m,F as u,bk as v,b7 as y,q as g,B as h,u as j,V as w,W as b}from"./@vue-DgI1lw0Y.js";import{s as f}from"./main-DE7o6g98.js";import{u as k}from"./useTableScrollY-9oHU_oJI.js";import{I,X as S,B as T,_ as P,b as L}from"./ant-design-vue-DW0D0Hn-.js";import{_ as x}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const R="/systemx/view-point-category/get-page-category-and-detail";const z={class:"view-animation"},_={class:"search-wrap"},C={class:"search-item"},N={class:"search-item"},O={class:"search-btns"},D={class:"table-wrap"},F={style:{display:"block"}},M={class:"pagination"},Y=x(e({__name:"Index",setup(e){const x=s({name:"",searchBeginTime:"",searchEndTime:"",sortField:"",sortRule:"",dates:[],pageNo:1,pageSize:10}),Y=a((()=>({current:x.pageNo,pageSize:x.pageSize,total:U.value,pageSizeOptions:["10","20","50","100"],showTotal:e=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),B=(e,s)=>{x.pageNo=e,x.pageSize=s,K()},J=()=>{x.pageNo=1,x.pageSize=10,K()},E=s({loading:!1}),H=[{key:"id",title:"步骤名称"},{key:"sort",title:"排序"},{key:"title",title:"标题"},{key:"coverImg",title:"封面"},{key:"video",title:"配音"},{key:"description",title:"描述"},{key:"camera",title:"跳转视角"},{key:"stayTime",title:"播放时长"},{key:"createTime",title:"生成时间"},{key:"json",title:"附加内容"}],U=t(0),q=s([{title:"动画名称",dataIndex:"name",key:"name",sorter:!0,ellipsis:!0},{title:"排序",dataIndex:"sort",key:"sort",sorter:!0,ellipsis:!0},{title:"是否循环播放",dataIndex:"isLoop",key:"isLoop",ellipsis:!0},{title:"生成时间",dataIndex:"createTime",key:"createTime",sorter:!0,ellipsis:!0}]),A=t([{cover:'/"\\"\\""',createTime:"2023-03-24 17:34:35",id:"1605113005850333186",isLoop:!0,name:"测试用面板",sort:"0",viewPointDetailList:[{id:"1605388073121714178",json:'{"tabIndex":2,"tabPath":"/sceneLoad/service","panelShow":true,"menuIndex":-1,"isMap":false,"camera":{"position":[-35.89773661197336,212.5398467611267,171.97984814631423],"target":[395.80853025403087,12.791032997816197,-166.86675024252696]},"showLt":false,"currentPage":1,"showRt":{"isShowPreview":false,"typeOfPreview":"","urlPreview":""}}',sort:0,categoryId:"1605387048184811522",video:"",title:"",coverImg:"/1605387070711910401",description:"初始状态",camera:"1",stayTime:"5",createTime:"2022-12-21 16:06:01"},{id:"1605388073121714178",json:'{"tabIndex":2,"tabPath":"/sceneLoad/service","panelShow":true,"menuIndex":-1,"isMap":false,"camera":{"position":[-35.89773661197336,212.5398467611267,171.97984814631423],"target":[395.80853025403087,12.791032997816197,-166.86675024252696]},"showLt":false,"currentPage":1,"showRt":{"isShowPreview":false,"typeOfPreview":"","urlPreview":""}}',sort:0,categoryId:"1605387048184811522",video:"",title:"",coverImg:"/1605387070711910401",description:"初始状态",camera:"1",stayTime:"5",createTime:"2022-12-21 16:06:01"},{id:"1605388073121714178",json:'{"tabIndex":2,"tabPath":"/sceneLoad/service","panelShow":true,"menuIndex":-1,"isMap":false,"camera":{"position":[-35.89773661197336,212.5398467611267,171.97984814631423],"target":[395.80853025403087,12.791032997816197,-166.86675024252696]},"showLt":false,"currentPage":1,"showRt":{"isShowPreview":false,"typeOfPreview":"","urlPreview":""}}',sort:0,categoryId:"1605387048184811522",video:"",title:"",coverImg:"/1605387070711910401",description:"初始状态",camera:"1",stayTime:"5",createTime:"2022-12-21 16:06:01"}]}]),G=(e,s,a)=>{x.sortField=a.field,a.order&&"ascend"===a.order?x.sortRule="ASC":a.order&&"descend"===a.order?x.sortRule="DESC":(x.sortField="",x.sortRule=""),K()},K=()=>{const e=(()=>{const e=JSON.parse(JSON.stringify(x));return e.dates&&e.dates.length>0?(e.searchBeginTime=e.dates[0],e.searchEndTime=e.dates[1]):(delete e.searchBeginTime,delete e.searchEndTime),e.sortField||delete e.sortField,e.sortRule||delete e.sortRule,delete e.dates,e})();var s;(s=e,f({url:R,method:"get",params:s})).then((e=>{200===e.code&&(U.value=e.data.totalRows,A.value=e.data.rows)}))},W=t(),{scrollY:$}=k(W);return i((()=>{K()})),(e,s)=>{const a=I,t=S,i=T,f=P,k=L;return r(),o("div",z,[l("div",_,[l("div",C,[s[5]||(s[5]=l("span",{class:"search-label"},"动画名称",-1)),n(a,{value:x.name,"onUpdate:value":s[0]||(s[0]=e=>x.name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入动画名称",class:"search-input",onKeyup:s[1]||(s[1]=d((e=>J()),["enter"]))},null,8,["value"])]),l("div",N,[s[6]||(s[6]=l("span",{class:"search-label"},"生成时间",-1)),n(t,{value:x.dates,"onUpdate:value":s[2]||(s[2]=e=>x.dates=e),placeholder:["开始时间","结束时间"],class:"search-date","show-time":{hideDisabledOptions:!0},"value-format":"YYYY-MM-DD HH:mm:ss",onChange:J},null,8,["value"])]),l("div",O,[n(i,{type:"primary",class:"search-btn",onClick:s[3]||(s[3]=e=>J())},{default:c((()=>s[7]||(s[7]=[p(" 查询 ")]))),_:1}),n(i,{class:"search-btn",onClick:s[4]||(s[4]=e=>(x.name="",x.dates=[],x.pageNo=1,x.pageSize=10,void J()))},{default:c((()=>s[8]||(s[8]=[p(" 重置 ")]))),_:1})])]),l("div",D,[l("div",{ref_key:"tableRef",ref:W,class:"table-content"},[n(f,{class:"table",scroll:{y:j($)},pagination:!1,"row-key":e=>e.id,size:"small",columns:q,loading:E.loading,"data-source":A.value,onChange:G},{expandedRowRender:c((({record:e})=>[l("p",null,[(r(!0),o(u,null,y(e.viewPointDetailList,((s,a)=>(r(),o("span",{key:a},[(r(),o(u,null,y(H,((e,a)=>l("span",{key:a},[l("span",F,v(e.title)+" ： "+v(s[e.key]),1)]))),64)),g(l("hr",null,null,512),[[h,a!==e.viewPointDetailList.length-1]])])))),128))])])),bodyCell:c((({column:e,record:s})=>["isLoop"===e.key?(r(),o(u,{key:0},[p(v(s.isLoop?"是":"否"),1)],64)):m("",!0)])),_:1},8,["scroll","row-key","columns","loading","data-source"]),l("div",M,[A.value.length>0?(r(),w(k,b({key:0},Y.value,{onChange:B}),null,16)):m("",!0)])],512)])])}}}),[["__scopeId","data-v-95b89fb3"]]);export{Y as default};
