import{d as e,r as s,a,o as l,S as i,U as t,am as o,c as r,bJ as n,F as c,b7 as d,V as u,G as p,bk as m,bL as f,al as v,q as y,B as h,Z as g,u as j,W as k}from"./@vue-DgI1lw0Y.js";import{g as b,a as w,b as _,c as C,p as x,d as R}from"./attachmentManage-CasMLJka.js";import{u as S}from"./useTableScrollY-9oHU_oJI.js";import{u as z,e as O}from"./main-DE7o6g98.js";import K from"./UploadFile-DVqyYPwS.js";import{_ as N}from"./DetailFile.vue_vue_type_style_index_0_lang-DIjrpK5l.js";import{_ as I}from"./PreviewImg.vue_vue_type_style_index_0_lang-D3nvZPcG.js";import{g as B,h as F,I as P,B as $,i as U,Q as J,$ as E,_ as G,b as L}from"./ant-design-vue-DW0D0Hn-.js";import{_ as T}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./developConfig-CkJLRJ2N.js";import"./js-binary-schema-parser-G48GG52R.js";const Y={class:"alarm-data"},q={class:"search-wrap"},A={class:"search-content"},D={class:"search-item"},H={class:"search-item"},Q={class:"search-btns"},V={class:"table-handle"},W={class:"table-wrap"},M={key:0},X={key:1,class:"table-actions"},Z=["onClick"],ee=["onClick"],se=["onClick"],ae=["onClick"],le={key:0},ie={class:"pagination"},te=T(e({__name:"Index",setup(e){const T=s(),te=s(),oe=s(),re=s([]),ne=s(!1),ce=s(-1),de=s(0),ue=a({fileBucket:null,selectedRowKeys:[],fileOriginName:""}),pe=[{title:"业务标识",dataIndex:"fileBucket",sorter:!0,ellipsis:!0},{title:"文件名称",dataIndex:"fileOriginName",sorter:!0,ellipsis:!0},{title:"文件后缀",dataIndex:"fileSuffix",sorter:!0,ellipsis:!0},{title:"文件大小",dataIndex:"fileSizeInfo",sorter:!0,ellipsis:!0},{title:"唯一标识id",dataIndex:"fileObjectName",sorter:!0,ellipsis:!0},{title:"操作",key:"action",width:300}],me=s({current:1,pageSize:10,total:0,pageSizeOptions:["10","20","50","100"],showTotal:e=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),fe=(e,s)=>{me.value=Object.assign(me.value,{current:e,pageSize:s}),be()},ve=s(),{scrollY:ye}=S(ve),he=s([]),ge=()=>{setTimeout((()=>{de.value-=1,de.value>0&&ge()}),1e3)};l((()=>{je(),be()}));const je=()=>{b().then((e=>{if(200===e.code){const s=[];e.data.forEach((e=>{s.push({code:e.file_bucket,value:e.file_bucketName})})),he.value=s}}))},ke=(e,s,a)=>{const l={sortField:"",sortRule:""};l.sortField=a.field,a.order&&"ascend"===a.order?l.sortRule="ASC":a.order&&"descend"===a.order?l.sortRule="DESC":(l.sortField="",l.sortRule=""),be(l)},be=(e={})=>{re.value=[];const s={pageNo:me.value.current,pageSize:me.value.pageSize,fileBucket:ue.fileBucket,fileOriginName:ue.fileOriginName,...e};ne.value=!0,w(s).then((e=>{if(200===e.code){const{rows:s,pageNo:a,pageSize:l,totalRows:i}=e.data;re.value=s,me.value.current=a,me.value.pageSize=l,me.value.total=i}})).finally((()=>{ne.value=!1})).catch((()=>{ne.value=!1}))},we=e=>{ue.selectedRowKeys=e},_e=()=>{const e={ids:ue.selectedRowKeys};R(e).then((e=>{ue.selectedRowKeys=[],200===e.code?(z("success","批量删除成功"),Ce()):z("error",e.message)})).catch((()=>{ue.selectedRowKeys=[],z("error","批量删除失败")}))},Ce=()=>{me.value.current=1,me.value.pageSize=10,be()},xe=()=>{Ce()},Re=s(!1),Se=s(),ze=(e,s)=>{x({id:s.id}).then((a=>{200===a.code&&(Se.value=`${window.baseConfig.previewResourceUrl}${a.data}`,s.visible=e)}))},Oe=e=>{T.value.init(e)};return(e,s)=>{const a=B,l=F,b=P,w=$,R=U,S=J,je=E,be=G,Ke=L;return t(),i("div",Y,[o("div",q,[o("div",A,[o("div",D,[s[6]||(s[6]=o("span",{class:"search-label"},"业务标识",-1)),r(l,{value:ue.fileBucket,"onUpdate:value":s[0]||(s[0]=e=>ue.fileBucket=e),"allow-clear":!0,placeholder:"请选择业务标识",class:"search-select",onChange:s[1]||(s[1]=e=>Ce())},{default:n((()=>[(t(!0),i(c,null,d(he.value,((e,s)=>(t(),u(a,{key:s,value:e.code},{default:n((()=>[p(m(e.value),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])]),o("div",H,[s[7]||(s[7]=o("span",{class:"search-label"},"文件名称",-1)),r(b,{value:ue.fileOriginName,"onUpdate:value":s[2]||(s[2]=e=>ue.fileOriginName=e),placeholder:"请输入文件名称(源文件名)",class:"search-input",onKeyup:s[3]||(s[3]=f((e=>Ce()),["enter"]))},null,8,["value"])]),o("div",Q,[r(w,{type:"primary",class:"search-btn",onClick:s[4]||(s[4]=e=>Ce())},{default:n((()=>s[8]||(s[8]=[p(" 查询 ")]))),_:1}),r(w,{class:"search-btn",onClick:s[5]||(s[5]=e=>(ue.fileBucket="",ue.fileOriginName="",ue.selectedRowKeys=[],void Ce()))},{default:n((()=>s[9]||(s[9]=[p(" 重置 ")]))),_:1})])]),o("div",V,[e.hasPerm("sys-file-info:delete")?(t(),u(R,{key:0,disabled:!ue.selectedRowKeys.length,"ok-text":"是","cancel-text":"否",onConfirm:_e},{title:n((()=>s[10]||(s[10]=[o("p",null,"删除后可能导致使用该资源的功能找不到资源，确认删除？",-1)]))),default:n((()=>[r(w,{type:"primary",class:"handle-btn",disabled:!ue.selectedRowKeys.length},{default:n((()=>s[11]||(s[11]=[p(" 批量删除 ")]))),_:1},8,["disabled"])])),_:1},8,["disabled"])):v("",!0),e.hasPerm("sys-file-info:upload")?(t(),u(w,{key:1,class:"handle-btn",type:"primary",onClick:Oe},{default:n((()=>s[12]||(s[12]=[p("上传文件 ")]))),_:1})):v("",!0)])]),o("div",W,[o("div",{ref_key:"table",ref:ve,class:"table-content"},[r(be,{class:"table",scroll:{y:j(ye)},pagination:!1,"row-key":e=>e.id,size:"small",columns:pe,"data-source":re.value,loading:ne.value,"row-selection":{selectedRowKeys:ue.selectedRowKeys,onChange:we},onChange:ke},{bodyCell:n((({column:a,record:l})=>{return["fileSuffix"===a.key?(t(),i("span",M,[r(S,{color:"green"},{default:n((()=>[p(m(l.fileSuffix),1)])),_:2},1024),y(o("span",null,"--",512),[[h,!l.fileSuffix]])])):v("",!0),"action"===a.key?(t(),i("div",X,[e.hasPerm("sys-file-info:download")?(t(),i("a",{key:0,class:g(ce.value===l.id&&0!==de.value?"disablecls":""),onClick:e=>(e=>{0===de.value&&(ce.value=e.id,de.value=3,ge(),Re.value=!0,C({id:e.id}).then((e=>{O(e),Re.value=!1})).catch((()=>{Re.value=!1})))})(l)},[y(o("span",null,m(0===de.value?"":`${de.value}s `),513),[[h,ce.value===l.id]]),s[13]||(s[13]=o("span",{class:"text"},"下载",-1))],10,Z)):v("",!0),e.hasPerm("sys-file-info:detail")?(t(),i("a",{key:1,onClick:e=>(e=>{te.value.init(e)})(l)},"详情",8,ee)):v("",!0),e.hasPerm("sys-file-info:preview")&&(c=l.fileSuffix,"png"===c||"jpg"===c||"jpeg"===c||"bmp"===c||"gif"===c||"tif"===c)?(t(),i("a",{key:2,onClick:e=>ze(!0,l)},"预览",8,se)):v("",!0),r(je,{width:0,style:{display:"none"},preview:{visible:!!l.visible,onVisibleChange:e=>{ze(e,l)}},src:Se.value},null,8,["preview","src"]),e.hasPerm("FJGL:COPY")?(t(),i("a",{key:3,onClick:e=>(e=>{x({id:e.id}).then((e=>{if(200===e.code){const s=`${window.baseConfig.previewResourceUrl}${e.data}`,a=document.createElement("input");a.value=s,document.body.appendChild(a),a.select(),document.execCommand("Copy"),z("success","已复制地址"),a.remove()}}))})(l)},"拷贝地址",8,ae)):v("",!0),r(R,{placement:"topRight",title:"删除后可能导致使用该资源的功能找不到资源，确认删除？","ok-text":"确认","cancel-text":"取消",onConfirm:e=>(e=>{const s={id:e.id};_(s).then((e=>{200===e.code?(z("success","删除成功"),Ce()):z("error",e.message)})).catch((()=>{z("error","删除失败")}))})(l)},{default:n((()=>[e.hasPerm("sys-file-info:delete")?(t(),i("a",le,"删除")):v("",!0)])),_:2},1032,["onConfirm"])])):v("",!0)];var c})),_:1},8,["scroll","row-key","data-source","loading","row-selection"]),o("div",ie,[re.value.length>0?(t(),u(Ke,k({key:0},me.value,{onChange:fe}),null,16)):v("",!0)])],512)]),r(K,{ref_key:"uploadFileRef",ref:T,onOk:xe},null,512),r(N,{ref_key:"detailFileRef",ref:te},null,512),r(I,{ref_key:"previewImgRef",ref:oe},null,512)])}}}),[["__scopeId","data-v-3c5f6847"]]);export{te as default};
