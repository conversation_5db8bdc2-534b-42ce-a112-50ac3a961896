import{d as e,r as a,a as s,j as t,V as i,U as l,bJ as o,am as r,c as n,bL as c,G as d,S as p,al as u,bk as m,W as v,n as j}from"./@vue-DgI1lw0Y.js";import{a as h,g,c as y,d as k}from"./addRelRule-nABHz7II.js";import{u as f}from"./main-DE7o6g98.js";import{I as w,B as b,_ as C,i as x,j as _,W as S,b as z,M as N}from"./ant-design-vue-DW0D0Hn-.js";import{_ as I}from"./vue-qr-6l_NUpj8.js";import"./indicatorData-7JRXzlPf.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const R={class:"rel-build"},J={class:"search-wrap"},O={class:"search-content"},F={class:"expand-content"},W={class:"search-item"},K={class:"search-btns"},U={class:"table-handle"},D={class:"table-wrap"},G={class:"table-content"},H={key:0},L={key:2},P={key:3,class:"table-actions"},$=["onClick"],q={class:"pagination"},A=I(e({__name:"Index",setup(e,{expose:I}){const A=a(!1),B=a(!1),E=a(),M=a([{id:"001",name:"规则名字",enable:!0,conditions:"教室.编号 = 喷淋.编号"}]),Q=s({keywords:"",pageNo:1,pageSize:10}),T=a(0),V=t((()=>({current:Q.pageNo,pageSize:Q.pageSize,total:T.value,pageSizeOptions:["10","20","50","100"],showTotal:e=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),X=a(null),Y=()=>{A.value=!1,B.value=!1},Z=[{title:"规则名称",dataIndex:"name",ellipsis:!0,width:180,align:"center"},{title:"启用状态",dataIndex:"status",ellipsis:!0,width:100,align:"center"},{title:"目标端",ellipsis:!0,width:180,dataIndex:"targetName"},{title:"构建条件",dataIndex:"conditions",ellipsis:!0,width:250,align:"center"},{title:"操作",key:"action",width:150,fixed:"right",align:"center"}],ee=(e,a)=>{Q.pageNo=e,Q.pageSize=a,te()},ae=(e,a,s)=>{let t={};t=s.order?{sortField:s.field,sortRule:"descend"===s.order?"DESC":"ASC"}:{sortField:"",sortRule:""},te(t)};a();const se=()=>{E.value.init("add",X.value)},te=(e={})=>{B.value=!0,M.value=[];const a=JSON.parse(JSON.stringify(Q)),s={sourceId:X.value.id,...a,...e};g(s).then((e=>{if(200===e.code){const{rows:a,pageNo:s,pageSize:t,totalRows:i}=e.data;M.value=a,V.value.current=s,V.value.pageSize=t,V.value.total=i}B.value=!1})).finally((()=>{B.value=!1})).catch((()=>{B.value=!1}))},ie=()=>{Q.pageNo=1,te()},le=()=>{ie()};return I({init:async e=>{X.value=e,A.value=!0,j((()=>{te()}))}}),(e,a)=>{const s=w,t=b,j=_,g=x,I=S,T=C,oe=z,re=N;return l(),i(re,{title:"关系构建",width:"100%","body-style":{maxHeight:"100%",overflow:"auto"},footer:null,"wrap-class-name":"cus-modal full-modal",open:A.value,"mask-closable":!1,onCancel:Y},{default:o((()=>[r("div",R,[r("div",J,[r("div",O,[r("div",F,[r("div",W,[a[4]||(a[4]=r("span",{class:"search-label"},"规则名称",-1)),n(s,{value:Q.keywords,"onUpdate:value":a[0]||(a[0]=e=>Q.keywords=e),"allow-clear":"",placeholder:"请输入规则名称",class:"search-input",onKeyup:a[1]||(a[1]=c((e=>ie()),["enter"]))},null,8,["value"])]),r("div",K,[n(t,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>ie())},{default:o((()=>a[5]||(a[5]=[d("搜索")]))),_:1}),n(t,{class:"search-btn",onClick:a[3]||(a[3]=e=>(Q.kewWord="",Q.pageNo=1,Q.pageSize=10,void te()))},{default:o((()=>a[6]||(a[6]=[d("重置")]))),_:1})])])]),r("div",U,[n(t,{class:"handle-btn",type:"primary",onClick:se},{default:o((()=>a[7]||(a[7]=[d(" 创建规则")]))),_:1})])]),r("div",D,[r("div",G,[n(T,{class:"table",scroll:{y:"calc(100% - 40px)"},pagination:!1,"row-key":e=>e.id,size:"small",columns:Z,"data-source":M.value,loading:B.value,onChange:ae},{bodyCell:o((({column:s,record:t})=>["name"===s.dataIndex?(l(),p("div",H,m(t.name),1)):u("",!0),"status"===s.dataIndex?(l(),i(g,{key:1,placement:"top",title:0===t.status?"确定停用该规则？":"确定启用该规则？",onConfirm:()=>(e=>{const a=0===e.status?1:0,s={id:e.id,status:a};y(s).then((e=>{200===e.code&&(f("success","状态修改成功"),te())}))})(t)},{default:o((()=>[n(j,{"checked-children":"启用","un-checked-children":"停用",checked:0===t.status},null,8,["checked"])])),_:2},1032,["title","onConfirm"])):u("",!0),"conditions"===s.dataIndex?(l(),p("span",L,m(t.sourceName+"."+t.sourceFieldComment+t.rule+t.targetName+"."+t.targetFieldComment),1)):u("",!0),"action"===s.key?(l(),p("div",P,[e.hasPerm("device-alert:process")?(l(),p("a",{key:0,onClick:e=>(e=>{E.value.init("edit",X.value,e)})(t)},"编辑",8,$)):u("",!0),n(I,{type:"vertical"}),e.hasPerm("device-alert:delete")?(l(),i(g,{key:1,placement:"topRight",title:"确定要删除该规则吗？","ok-text":"是","cancel-text":"否",onConfirm:e=>(async e=>{if(e.status)f("warning","规则已启动无法删除");else try{200===(await k(e.id)).code&&(f("success","删除成功"),te())}catch(a){f("error","删除失败")}})(t)},{default:o((()=>a[8]||(a[8]=[r("a",{class:"delete-btn"},"删除",-1)]))),_:2},1032,["onConfirm"])):u("",!0)])):u("",!0)])),_:1},8,["row-key","data-source","loading"]),r("div",q,[M.value.length>0?(l(),i(oe,v({key:0},V.value,{onChange:ee}),null,16)):u("",!0)])])]),n(h,{ref_key:"addRelRuleRef",ref:E,onOk:le},null,512)])])),_:1},8,["open"])}}}),[["__scopeId","data-v-09897315"]]);export{A as default};
