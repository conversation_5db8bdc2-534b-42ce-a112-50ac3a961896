import{d as e,r as a,a as l,V as s,U as t,bJ as o,c as n,S as i,F as r,b7 as d,G as c,bk as u,am as m,u as p,o as v,w as f,b9 as y,Z as h,al as g,A as k}from"./@vue-DgI1lw0Y.js";import{v as j,u as _,a3 as b}from"./main-DE7o6g98.js";import{s as x,g as C,d as L,a as O,e as S}from"./model-BUBDdBL2.js";import{M as w,S as T,F as E,c as M,k as I,R as D,I as U,p as A,f as R,B as F,r as G,i as P,s as q}from"./ant-design-vue-DW0D0Hn-.js";import{Q as J,S as B,ak as K,a8 as Z,T as N}from"./@ant-design-tBRGNTkq.js";import{_ as z}from"./vue-qr-6l_NUpj8.js";import Y from"./Index-DliAELKp.js";import H from"./Index-CPLrtVLz.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./nodata-dark-DHz_m8dv.js";import"./Detail-4l1XJAkN.js";import"./clipboard-Dv7Qpqbb.js";import"./vue-pick-colors-CjUSS-xa.js";import"./@popperjs-CFrBDmKk.js";import"./AddOrEdit.vue_vue_type_style_index_0_lang-B0JB0YoY.js";import"./basicModelTip4-CPNhDiAx.js";import"./UploadModal.vue_vue_type_script_setup_true_lang-BRboDosL.js";import"./AddForm.vue_vue_type_style_index_0_lang-DCX-wfEb.js";import"./AddOrEdit.vue_vue_type_style_index_0_lang-Dur8dl0H.js";const $=z(e({__name:"SyncClassify",emits:["ok"],setup(e,{expose:v,emit:f}){const y={xs:{span:24},sm:{span:15,offset:5}},h=[{code:0,value:"在线同步（需连接外网）"},{code:1,value:"离线同步"}],g=a(!1),k=a(),b=l({type:0,url:"http://model.3dmomoda.cn/librarys/basex",jsonText:"",importOp:!0}),C={type:[{required:!0,message:"请输入同步方式名称"}],url:[{required:!0,message:"请输入地址"},{validator:j}],jsonText:[{required:!0,message:"请输入内容！"}],importOp:[{required:!0,message:"请选择同步规则"}]},L=f,O=a(!1),S=a(!1),F=(e,a,l)=>{x({modelJson:e,modelJsonType:a,importOp:l}).then((e=>{var a;S.value=!1,e.success?(_("success","公共模型分类列表同步成功！"),O.value=!1,L("ok",b),null==(a=k.value)||a.resetFields()):_("error",`同步失败：${e.message}`),S.value=!1})).finally((()=>{S.value=!1}))},G=()=>{var e;S.value||(null==(e=k.value)||e.resetFields(),O.value=!1,S.value=!1)};return v({add:()=>{g.value=!1,O.value=!0}}),(e,a)=>{const l=D,v=I,f=M,j=U,_=A,x=R,L=E,P=T,q=w;return t(),s(q,{title:"同步分类列表","wrap-class-name":"cus-modal",width:528,open:O.value,"confirm-loading":S.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[4]||(a[4]=e=>{return S.value=!0,void(null==(a=k.value)||a.validateFields().then((()=>{0===b.type?F(b.url,!0,b.importOp):1===b.type&&F(b.jsonText,!1,b.importOp)})).catch((e=>{S.value=!1})));var a}),onCancel:G},{default:o((()=>[n(P,{spinning:S.value},{default:o((()=>[n(L,{ref_key:"formRef",ref:k,model:b,rules:C,"label-align":"left"},{default:o((()=>[n(f,{label:"同步方式",name:"type"},{default:o((()=>[n(v,{value:b.type,"onUpdate:value":a[0]||(a[0]=e=>b.type=e)},{default:o((()=>[(t(),i(r,null,d(h,((e,a)=>n(l,{key:a,value:e.code,onClick:a=>{return l=e.code,void(g.value=0!==l);var l}},{default:o((()=>[c(u(e.value),1)])),_:2},1032,["value","onClick"]))),64))])),_:1},8,["value"])])),_:1}),g.value?(t(),s(f,{key:1,"wrapper-col":y,name:"jsonText"},{default:o((()=>[n(_,{value:b.jsonText,"onUpdate:value":a[2]||(a[2]=e=>b.jsonText=e),placeholder:"请输入json格式内容",rows:8},null,8,["value"]),a[5]||(a[5]=m("span",null,[m("i",{style:{"margin-right":"10px","font-style":"normal",color:"red"}},"*"),c("请打开"),m("a",{href:"http://model.3dmomoda.cn/librarys/basex",target:"_blank"}," http://model.3dmomoda.cn/librarys/basex"),c(" 页面，然后复制页面上的全部内容")],-1))])),_:1})):(t(),s(f,{key:0,"has-feedback":"",label:"url",name:"url"},{default:o((()=>[n(j,{value:b.url,"onUpdate:value":a[1]||(a[1]=e=>b.url=e),placeholder:"请输入url"},null,8,["value"])])),_:1})),n(f,{name:"importOp"},{label:o((()=>[m("span",null,[a[6]||(a[6]=c(" 同步规则 ")),n(x,{placement:"left"},{title:o((()=>[m("div",{textContent:"替换：把已有的分类清空重新导入；增量：在已有的分类数据中，存在的做更新，不存在的做追加"})])),default:o((()=>[n(p(J),{style:{color:"#ef7b1a"}})])),_:1})])])),default:o((()=>[n(v,{value:b.importOp,"onUpdate:value":a[3]||(a[3]=e=>b.importOp=e)},{default:o((()=>[(t(),i(r,null,d([{code:!0,value:"增量"},{code:!1,value:"替换"}],((e,a)=>n(l,{key:a,value:e.code},{default:o((()=>[c(u(e.value),1)])),_:2},1032,["value"]))),64))])),_:1},8,["value"])])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-fc72394a"]]),Q={class:"tree-wrap"},V={class:"top-nav"},W={class:"tree-contain"},X={class:"tree-search"},ee={key:1,class:"tree-box"},ae=["title"],le={class:"title"},se={class:"btns"},te=["onClick"],oe=["onClick"],ne={key:2,class:"btn",title:"删除"},ie=z(e({__name:"ModelGroup",emits:["handleClick","onBack","changeMenu","syncClass"],setup(e,{expose:r,emit:d}){const c=d,j=e=>{I.value=e,c("changeMenu",I.value),H()},b=a(),x=()=>{b.value.add()},T=()=>{H(),c("syncClass")},I=a(1),D=a({children:"children",title:"title",key:"id"}),A=a(""),R=a([]);let J=[];const z=a([]),Y=a([]);v((()=>{H()}));const H=()=>{C({modelType:I.value}).then((e=>{if(!e.success)return;const a=e.data,l=de(a),s=ce([l]);R.value=s,J=s,z.value=[R.value[0].id]}))},ie=(e,a)=>{e.length?z.value=e:z.value=[R.value[0].id],c("handleClick",e,a)};f(A,(e=>{R.value=re(J,e)}));const re=(e,a)=>{const l=JSON.parse(JSON.stringify(e));if(!l||!l.length)return[];const s=[];for(const t of l){const e=re(t.children,a);t.title.indexOf(a)>-1?s.push(t):e&&e.length&&(t.children=e,s.push(t))}return s.length?s:[]},de=e=>{const a=e;return a.id=e.key,a.children.length&&a.children.forEach((e=>{de(e)})),a},ce=e=>(e.forEach((e=>{const a=e;let l=[];l=a.id.split("/"),l=l.filter((e=>e)),0===l.length?a.add=!0:1===l.length?(a.add=!0,a.edit=!0,a.delete=!0):2===l.length&&(a.edit=!0,a.delete=!0),a.children&&a.children.length&&(a.children=ce(e.children))})),e),ue=l({visible:!1,title:"",data:null,confirmLoading:!1}),me=a(),pe=l({name:""}),ve={name:[{required:!0,message:"请输入名称！",trigger:"blur"},{max:30,message:"名字长度不能超过30",trigger:"blur"}]},fe=()=>{ue.confirmLoading||(me.value.resetFields(),ue.visible=!1,ue.confirmLoading=!1,ue.data=null)},ye=()=>{ue.confirmLoading=!0,me.value.validate().then((()=>{if("新增分组"===ue.title){const e={modelType:I.value,title:pe.name,parentKey:ue.data.key};O(e).then((e=>{ue.confirmLoading=!1,200===e.code?(_("success","新增分类成功"),fe(),H(),c("onBack")):_("error",e.message)})).catch((()=>{ue.confirmLoading=!1})).finally((()=>{ue.confirmLoading=!1}))}else{const e={modelType:I.value,editTitle:pe.name,key:ue.data.key};S(e).then((e=>{ue.confirmLoading=!1,200===e.code?(_("success","编辑分类成功"),fe(),H(),c("onBack")):_("error",e.message)})).catch((()=>{ue.confirmLoading=!1})).finally((()=>{ue.confirmLoading=!1}))}})).catch((e=>{ue.confirmLoading=!1}))};return r({getTreeData:H}),(e,a)=>{const l=U,r=F,d=y("down-outlined"),v=y("plus-outlined"),f=P,C=q,O=M,S=E,J=w;return t(),i("div",Q,[m("div",V,[m("span",{class:h(["tab",{active:1===I.value}]),onClick:a[0]||(a[0]=e=>j(1))},"公共模型库",2),m("span",{class:h(["tab",{active:2===I.value}]),onClick:a[1]||(a[1]=e=>j(2))},"项目模型库",2)]),m("div",W,[m("div",X,[n(l,{value:A.value,"onUpdate:value":a[2]||(a[2]=e=>A.value=e),placeholder:"请输入内容过滤",class:"filter"},{suffix:o((()=>[n(p(B))])),_:1},8,["value"]),e.hasPerm("model-lib:sync-by-classify")&&1===I.value?(t(),s(r,{key:0,type:"primary",style:{width:"40px",padding:"0"},class:"synchronization",title:"同步模型分类",onClick:x},{icon:o((()=>[n(p(K))])),_:1})):g("",!0)]),0===R.value.length?(t(),s(p(G),{key:0,image:p(G).PRESENTED_IMAGE_SIMPLE},null,8,["image"])):(t(),i("div",ee,[n(C,{selectedKeys:z.value,"onUpdate:selectedKeys":a[4]||(a[4]=e=>z.value=e),"show-icon":"","tree-data":R.value,class:"cus-tree","default-expand-all":!0,"default-expanded-keys":Y.value,"field-names":D.value,onSelect:ie},{switcherIcon:o((({switcherCls:e})=>[n(d,{class:h(e)},null,8,["class"])])),title:o((l=>[m("span",{class:"root-tree-item",title:l.title},[m("span",le,u(l.title),1),m("s",se,[e.hasPerm("ZYGL:MODEL-CLASS-ADD")&&l.add?(t(),i("span",{key:0,class:"btn",title:"添加",onClick:k((e=>(e=>{ue.title="新增分组",ue.data=e,ue.visible=!0,pe.name=""})(l)),["stop"])},[n(v)],8,te)):g("",!0),e.hasPerm("ZYGL:MODEL-CLASS-EDIT")&&l.edit?(t(),i("span",{key:1,class:"btn",title:"编辑",onClick:k((e=>(e=>{ue.title="编辑分组",ue.data=e,ue.visible=!0,pe.name=e.title})(l)),["stop"])},[n(p(Z))],8,oe)):g("",!0),e.hasPerm("ZYGL:MODEL-CLASS-DELETE")&&l.delete?(t(),i("span",ne,[n(f,{placement:"topRight",title:"确认删除？","ok-text":"确认","cancel-text":"取消",onConfirm:e=>(e=>{const a={modelType:I.value,key:e.key};L(a).then((e=>{200===e.code?(_("success","删除分类成功"),H(),c("onBack")):_("error",e.message)}))})(l)},{default:o((()=>[n(p(N),{onClick:a[3]||(a[3]=k((()=>{}),["stop"]))})])),_:2},1032,["onConfirm"])])):g("",!0)])],8,ae)])),_:1},8,["selectedKeys","tree-data","default-expanded-keys","field-names"])]))]),n(J,{open:ue.visible,"onUpdate:open":a[6]||(a[6]=e=>ue.visible=e),"wrap-class-name":"cus-modal",width:528,title:ue.title,"mask-closable":!1,"confirm-loading":ue.confirmLoading,"mask-style":{background:" rgba(0,0,0,0.71)"},onOk:ye,onCancel:fe},{default:o((()=>[n(S,{ref_key:"formRef",ref:me,model:pe,rules:ve,"label-align":"left"},{default:o((()=>[n(O,{name:"name",label:"名称","has-feedback":""},{default:o((()=>[n(l,{value:pe.name,"onUpdate:value":a[5]||(a[5]=e=>pe.name=e),placeholder:"请输入名称","max-length":30},null,8,["value"])])),_:1})])),_:1},8,["model"])])),_:1},8,["open","title","confirm-loading"]),n($,{ref_key:"syncClassifyRef",ref:b,onOk:T},null,512)])}}}),[["__scopeId","data-v-10f68405"]]),re={class:"model-manege"},de={class:"left"},ce={class:"right"},ue=z(e({__name:"Index",setup(e){const l=a(1),o=e=>{l.value=e},n=a(),r=a(),d=(e,a)=>{let s="";const{key:t}=a.node.dataRef;s=2!==l.value||"/"!==t&&0!==e.length?t:"",1===l.value?n.value.classifySearch(s):r.value.classifySearch(s)},c=()=>{n.value.classifySearch("")};v((()=>{u()}));const u=e=>{b((()=>{}),["thingjs","uearth"])};return(e,a)=>(t(),i("div",re,[m("div",de,[e.hasPerm("model-lib:tree")?(t(),s(ie,{key:0,onHandleClick:d,onChangeMenu:o,onSyncClass:c})):g("",!0)]),m("div",ce,[1===p(l)?(t(),s(Y,{key:0,ref_key:"publicModelRef",ref:n},null,512)):g("",!0),2===p(l)?(t(),s(H,{key:1,ref_key:"projectModelRef",ref:r},null,512)):g("",!0)])]))}}),[["__scopeId","data-v-8e224bc5"]]);export{ue as default};
