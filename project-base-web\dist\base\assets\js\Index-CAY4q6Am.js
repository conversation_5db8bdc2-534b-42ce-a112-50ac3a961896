import{u as e}from"./main-DE7o6g98.js";import{u as a}from"./useTableScrollY-9oHU_oJI.js";import{x as o,y as s}from"./mapManag-wSwfWE2D.js";import t from"./AddEditForm-kWow-IaX.js";import{B as i,i as r,_ as l,b as n}from"./ant-design-vue-DW0D0Hn-.js";import{d,a as p,r as m,j as c,o as j,S as u,U as g,am as y,c as v,V as k,al as h,bJ as f,G as b,u as w,W as _}from"./@vue-DgI1lw0Y.js";import{_ as x}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const z={class:"madas"},S={class:"table-wrap"},C={class:"table-handle"},I={key:0,class:"table-actions"},N=["onClick"],J={class:"pagination"},L=x(d({__name:"Index",setup(d){const x=p({pageNo:1,pageSize:10}),L=m(),R=m();let O=p([]);const P=m(0),A=p([{title:"图层名称",dataIndex:"name",key:"name",ellipsis:!0},{title:"坐标系",dataIndex:"coords",key:"coords",ellipsis:!0},{title:"图层地址",dataIndex:"url",key:"url",ellipsis:!0},{title:"离地高度(米)",dataIndex:"height",key:"height",ellipsis:!0},{title:"操作",key:"action",width:240}]),E=c((()=>({current:x.pageNo,pageSize:x.pageSize,total:P.value,pageSizeOptions:["10","20","50","100"],showTotal:(e,a)=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),F=(e,a)=>{x.pageNo=e,x.pageSize=a,W()},{scrollY:G}=a(L),K=p({selectedRowKeys:[],loading:!1,addLoading:!1,delLoading:!1}),T=()=>{x.pageNo=1,W()},W=()=>{K.loading=!0,o(x).then((e=>{var a;K.loading=!1,200===e.code&&(O=null==(a=e.data.rows)?void 0:a.map((e=>({...e,typeStr:0===e.type?"底图":1===e.type?"正射影像":""}))),P.value=e.data.totalRows)}),(()=>{K.loading=!1}))},Y=(e,a)=>{R.value.init(e,a)},$=()=>{T()};return j((()=>{W()})),(a,o)=>{const d=i,p=r,m=l,c=n;return g(),u("div",z,[y("div",S,[y("div",C,[a.hasPerm("projection:add")?(g(),k(d,{key:0,type:"primary",class:"handle-btn",loading:K.addLoading,onClick:o[0]||(o[0]=e=>Y("add",null))},{default:f((()=>o[1]||(o[1]=[b(" 新增倾斜摄影图层 ")]))),_:1},8,["loading"])):h("",!0)]),y("div",{ref_key:"table",ref:L,class:"table-content"},[v(m,{class:"table",scroll:{y:w(G)},pagination:!1,"row-key":e=>e.id,size:"small",columns:A,loading:K.loading,"data-source":w(O)},{bodyCell:f((({column:t,record:i})=>["action"===t.key?(g(),u("div",I,[a.hasPerm("projection:edit")?(g(),u("a",{key:0,onClick:e=>Y("edit",i)},"编辑",8,N)):h("",!0),a.hasPerm("projection:delete")?(g(),k(p,{key:1,placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:a=>(a=>{s(a).then((a=>{200===a.code?(e("success","倾斜摄影删除成功"),T()):e("error","倾斜摄影删除失败")}))})(i)},{title:f((()=>o[2]||(o[2]=[y("p",null,"确定要删除吗?",-1)]))),default:f((()=>[o[3]||(o[3]=y("a",null,"删除",-1))])),_:2},1032,["onConfirm"])):h("",!0)])):h("",!0)])),_:1},8,["scroll","row-key","columns","loading","data-source"]),y("div",J,[w(O).length>0?(g(),k(c,_({key:0},E.value,{onChange:F}),null,16)):h("",!0)])],512)]),v(t,{ref_key:"addEditFormRef",ref:R,onOk:$},null,512)])}}}),[["__scopeId","data-v-ab0068c5"]]);export{L as default};
