import{d as e,o as a,r as s,S as t,U as r,am as o,c as i,bL as l,bJ as n,G as m,al as c,V as p,u,W as d}from"./@vue-DgI1lw0Y.js";import{u as v}from"./useTableScrollY-9oHU_oJI.js";import j from"./EditForm-BV3_IRyT.js";import y from"./ViewLog-BMpdlvXd.js";import{g as k,a as b,b as g}from"./scheduledTask-D-uirQox.js";import{u as h}from"./main-DE7o6g98.js";import{I as f,B as C,i as w,_,b as N}from"./ant-design-vue-DW0D0Hn-.js";import{_ as S}from"./vue-qr-6l_NUpj8.js";import"./ACron-Cdy8z9jI.js";import"./Crontab-CnHNK4l5.js";import"./Crontab-Second-Cb5dnRiU.js";import"./@babel-B4rXMRun.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./dayjs-D9wJ8dSB.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./Crontab-Min-CvbMKa3m.js";import"./Crontab-Hour-944bNSow.js";import"./Crontab-Day-DUlTjVdV.js";import"./Crontab-Mouth-8HJdgSDk.js";import"./Crontab-Week-D6Gd-d6p.js";import"./Crontab-Year-Df77Ipqs.js";import"./Crontab-Result.vue_vue_type_script_setup_true_lang-DpqA3H9M.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";/* empty css                      */const z={class:"scheduled-task"},I={class:"search-wrap"},x={class:"search-item"},R={class:"search-btns"},L={class:"table-wrap"},O={key:0,class:"table-actions"},T=["onClick"],A=["onClick"],E={class:"pagination"},W=S(e({__name:"Index",setup(e){a((()=>{S(),W()}));const S=async()=>{try{const e=await k({code:"RENWULEIXINGBIAOSHI"});B.value=e.data}catch(e){}},W=()=>{H.value=[],G.value=!0,b({...M.value,pageNo:U.value.current,pageSize:U.value.pageSize}).then((e=>{if(200===e.code){const{rows:a,pageNo:s,pageSize:t,totalRows:r}=e.data;H.value=a,U.value.current=s,U.value.pageSize=t,U.value.total=r}G.value=!1}),(()=>{G.value=!1}))},$=[{title:"任务名称",dataIndex:"timerName",key:"timerName",ellipsis:!0},{title:"任务类型",dataIndex:"taskType",key:"taskType",ellipsis:!0,customRender:e=>{var a;return null==(a=B.value.filter((a=>a.code===e.value.toString()))[0])?void 0:a.value}},{title:"定时任务表达式",dataIndex:"cron",key:"cron",ellipsis:!0},{title:"备注",dataIndex:"remark",key:"remark",ellipsis:!0},{title:"操作",key:"action",width:240}],B=s([]),G=s(!1),H=s([]),J=s(),{scrollY:P}=v(J),U=s({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),Y=(e,a)=>{U.value=Object.assign(U.value,{current:e,pageSize:a}),W()},q=s(),F=e=>{q.value.init(e)},K=s(),M=s({timerName:"",code:""}),V=()=>{M.value.timerName="",M.value.code="",D()},D=()=>{U.value.current=1,U.value.pageSize=10,W()};return(e,a)=>{const s=f,v=C,k=w,b=_,S=N;return r(),t("div",z,[o("div",I,[o("div",x,[a[3]||(a[3]=o("span",{class:"search-label"},"任务名称",-1)),i(s,{value:M.value.timerName,"onUpdate:value":a[0]||(a[0]=e=>M.value.timerName=e),"allow-clear":"",placeholder:"请输入任务名称",class:"search-input",onKeyup:a[1]||(a[1]=l((e=>D()),["enter"]))},null,8,["value"])]),o("div",R,[i(v,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>D())},{default:n((()=>a[4]||(a[4]=[m(" 查询 ")]))),_:1}),i(v,{class:"search-btn",onClick:V},{default:n((()=>a[5]||(a[5]=[m(" 重置 ")]))),_:1})])]),o("div",L,[o("div",{ref_key:"table",ref:J,class:"table-content"},[i(b,{class:"table",scroll:{y:u(P)},pagination:!1,"row-key":e=>e.id,size:"small",columns:$,loading:G.value,"data-source":H.value},{bodyCell:n((({column:s,record:i})=>["action"===s.key?(r(),t("div",O,[e.hasPerm("sys-timers:edit")?(r(),t("a",{key:0,onClick:e=>(e=>{K.value.init(e)})(i)},"编辑",8,T)):c("",!0),e.hasPerm("sys-timers:excute")?(r(),p(k,{key:1,title:"当前操作不可逆转，是否立即执行此定时任务？","ok-text":"是","cancel-text":"否",onConfirm:e=>(async e=>{try{await g({id:e.id,timerName:e.timerName,actionClass:e.actionClass,cron:e.cron}),h("success",`执行${e.timerName}成功`)}catch(a){h("error",`执行${e.timerName}失败`)}})(i)},{default:n((()=>a[6]||(a[6]=[o("a",null,"立即执行",-1)]))),_:2},1032,["onConfirm"])):c("",!0),e.hasPerm("sys-timers:page-logs")?(r(),t("a",{key:2,onClick:e=>F(i)},"查看日志",8,A)):c("",!0)])):c("",!0)])),_:1},8,["scroll","row-key","loading","data-source"]),o("div",E,[H.value.length>0?(r(),p(S,d({key:0},U.value,{onChange:Y}),null,16)):c("",!0)])],512),i(j,{ref_key:"addEditFormRef",ref:K,onOk:W},null,512),i(y,{ref_key:"viewLogRef",ref:q,onOk:F},null,512)])])}}}),[["__scopeId","data-v-dc8dc348"]]);export{W as default};
