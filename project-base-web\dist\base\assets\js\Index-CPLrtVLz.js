import{d as e,r as a,o as l,b9 as s,v as t,S as o,U as i,F as n,am as c,c as r,V as u,bL as d,bJ as p,G as m,al as v,bk as h,q as f,u as y,b7 as g,Z as k,n as j}from"./@vue-DgI1lw0Y.js";import{d as b}from"./dayjs-D9wJ8dSB.js";import{d as w,a as _}from"./nodata-dark-DHz_m8dv.js";import{_ as C}from"./AddForm.vue_vue_type_style_index_0_lang-DCX-wfEb.js";import{_ as x}from"./AddOrEdit.vue_vue_type_style_index_0_lang-Dur8dl0H.js";import E from"./Detail-4l1XJAkN.js";import{h as z,b as O,i as U,f as D,u as I,D as S,c as L,j as P}from"./model-BUBDdBL2.js";import{a as N,u as $}from"./main-DE7o6g98.js";import{a as M}from"./axios-ChCdAMPF.js";import{I as R,g as Y,h as G,B as A,i as T,q as Z,S as q,r as F,b as V,M as W,p as J}from"./ant-design-vue-DW0D0Hn-.js";import{_ as K}from"./vue-qr-6l_NUpj8.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./basicModelTip4-CPNhDiAx.js";import"./clipboard-Dv7Qpqbb.js";import"./vue-pick-colors-CjUSS-xa.js";import"./@popperjs-CFrBDmKk.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const B={class:"project-model"},H={class:"search-wrap"},Q={class:"search-content"},X={class:"search-item"},ee={class:"search-item"},ae={class:"search-btns"},le={class:"table-handle"},se={key:0,class:"checkinfo"},te={class:"number"},oe={key:0,class:"list",style:{"place-content":"center center"}},ie={key:1,class:"list"},ne={class:"contain"},ce=["src"],re={class:"cus-check"},ue={class:"hover-box"},de={class:"cus-check"},pe=["onClick"],me=["onClick"],ve=["onClick"],he={class:"item-top"},fe={class:"item-name"},ye={class:"item-file"},ge={class:"item-bottom"},ke={class:"item-type"},je={class:"pagination-box"},be={style:{"text-align":"right"}},we=K(e({__name:"Index",setup(e,{expose:K}){const we=N();a(window.baseConfig.previewResourceUrl);const _e=a(!1),Ce=()=>{const e=De.value.map((e=>({modelId:e.modelId,type:e.type})));_e.value=!0,O(e).then((e=>{_e.value=!1,e.success?(De.value=[],$("success","删除成功！"),Se()):$("error",e.message)})).catch((()=>{_e.value=!1}))},xe=e=>{const a=M.defaults.headers.common.Tenant||"master";let l=`${window.baseConfig.previewResourceUrl}${a}/model/${e.modelId}/0/gltf/screenshot.jpg`;return"模型"!==e.type&&(l=`${window.baseConfig.previewResourceUrl}${a}/texture/${e.modelId}.${e.extendName}`),l},Ee=e=>{const a=e.srcElement;a.src="light"===we.modeName?w:_,a.onerror=null},ze=a({name:"",fileExist:"",classify:"",modelType:2,pageNo:1,pageSize:12,belongProjectCode:""}),Oe=a(0),Ue=a(!1),De=a([]),Ie=a([]);l((()=>{Ne()}));const Se=()=>{ze.value.pageNo=1,ze.value.pageSize=12,Ne()},Le=()=>{De.value=Ie.value.filter((e=>!0===e.check))},Pe=()=>{const e=Ie.value.map((e=>({...e,check:!1})));De.value=[],Ie.value=e},Ne=()=>{Ie.value=[],Ue.value=!0,De.value=[],z(ze.value).then((e=>{if(200===e.code){const{rows:a,pageNo:l,pageSize:s,totalPage:t,totalRows:o}=e.data;Ie.value=[],a.forEach((e=>{Ie.value.push({...e,check:!1})})),ze.value.pageNo=l,ze.value.pageSize=s,Oe.value=o}Ue.value=!1})).catch((()=>{Ue.value=!1}))},$e=(e,a)=>{ze.value.pageNo=e,Ne()},Me=(e,a)=>{ze.value.pageNo=1,ze.value.pageSize=a,Ne()},Re=a(),Ye=a(),Ge=()=>{Ye.value.add()},Ae=()=>{Ne()},Te=a(!1),Ze=()=>{I({flag:Te.value}).then((e=>{e.success?$("success",e.message):$("error",e.message),Te.value=!1})).catch((()=>{Te.value=!1}))},qe=a(!1),Fe=()=>{if(De.value.length>0){let e=[];qe.value=!0,e=De.value.map((e=>e.modelId)),Ve(e)}else S({classify:ze.value.classify}).then((e=>{!0===e.success&&$("success",e.message)})).catch((()=>{qe.value=!1})).finally((()=>{qe.value=!1}))},Ve=e=>{L(e).then((e=>{!0===e.success&&$("success",e.message),Pe(),qe.value=!1})).catch((()=>{qe.value=!1}))},We=a(!1),Je=a(!1),Ke=a(""),Be=()=>{We.value=!0,Ke.value=""},He=async()=>{if(Je.value=!0,Ke.value){const a=Ke.value.split(",");try{const e=await P(a);e.success&&$("success",e.message),We.value=!1,Ke.value="",Je.value=!1}catch(e){$("error",e),Je.value=!1}}},Qe=()=>{We.value=!1,Ke.value="",Je.value=!1},Xe=a(""),ea=a(""),aa=a(""),la=a(!1),sa=()=>{la.value=!1};return K({classifySearch:e=>{ze.value.classify=e,ze.value.pageNo=1,ze.value.pageSize=12,Ne()}}),(e,a)=>{const l=R,w=Y,_=G,z=A,O=s("CloseCircleFilled"),I=T,S=Z,L=s("UploadOutlined"),P=s("PlusOutlined"),N=s("DownloadOutlined"),M=q,K=V,we=W,Ne=J,ta=t("loading");return i(),o(n,null,[c("div",B,[c("div",H,[c("div",Q,[c("div",X,[a[7]||(a[7]=c("span",{class:"search-label"},"关键字",-1)),c("div",null,[r(l,{value:ze.value.name,"onUpdate:value":a[0]||(a[0]=e=>ze.value.name=e),"allow-clear":"",placeholder:"模型名称",class:"search-input",onKeyup:a[1]||(a[1]=d((e=>Se()),["enter"]))},null,8,["value"])])]),c("div",ee,[a[11]||(a[11]=c("span",{class:"search-label"},"资源文件",-1)),c("div",null,[r(_,{value:ze.value.fileExist,"onUpdate:value":a[2]||(a[2]=e=>ze.value.fileExist=e),class:"search-select",placeholder:"请选择资源文件是否存在","allow-clear":"",style:{width:"100px"},onChange:a[3]||(a[3]=e=>Se())},{default:p((()=>[r(w,{value:""},{default:p((()=>a[8]||(a[8]=[m("全部")]))),_:1}),r(w,{value:"1"},{default:p((()=>a[9]||(a[9]=[m("不存在")]))),_:1}),r(w,{value:"2"},{default:p((()=>a[10]||(a[10]=[m("已存在")]))),_:1})])),_:1},8,["value"])])]),c("div",ae,[r(z,{type:"primary",class:"btn",onClick:a[4]||(a[4]=e=>Se())},{default:p((()=>a[12]||(a[12]=[m(" 查询 ")]))),_:1})]),c("div",le,[De.value.length>0?(i(),o("span",se,[a[13]||(a[13]=m(" 已选中:")),c("span",te,h(De.value.length),1),r(O,{class:"cancelCheck",style:{"font-size":"17px",color:"rgb(29 31 36 / 70%)","vertical-align":"middle"},onClick:Pe})])):v("",!0),e.hasPerm("ZYGL:MODEL-CLASS-DELETE")&&De.value.length>0?(i(),u(I,{key:1,"ok-text":"是","cancel-text":"否",onConfirm:Ce},{title:p((()=>a[14]||(a[14]=[c("p",null,"确认删除选中模型或贴图？",-1)]))),default:p((()=>[f((i(),u(z,{type:"primary",class:"handle-btn"},{default:p((()=>a[15]||(a[15]=[m(" 批量删除 ")]))),_:1})),[[ta,_e.value]])])),_:1})):v("",!0),e.hasPerm("model-lib:sync-by-classify")?(i(),u(I,{key:2,"ok-text":"是","cancel-text":"否",onConfirm:Ze},{title:p((()=>[a[17]||(a[17]=c("p",null,"系统将自动同步更新所有未存在的模型资源",-1)),c("p",null,[a[16]||(a[16]=m(" 是否强制同步全部资源 ")),r(S,{modelValue:Te.value,"onUpdate:modelValue":a[5]||(a[5]=e=>Te.value=e)},null,8,["modelValue"])])])),default:p((()=>[r(z,{type:"primary",class:"handle-btn"},{icon:p((()=>[r(L,{style:{"font-size":"16px"}})])),default:p((()=>[a[18]||(a[18]=m(" 同步全部模型 "))])),_:1})])),_:1})):v("",!0),e.hasPerm("model-lib:offline-project-upload")&&ze.value.classify&&ze.value.classify.split("/")[1]?(i(),u(z,{key:3,class:"handle-btn",onClick:Ge},{icon:p((()=>[r(P,{style:{"font-size":"16px"}})])),default:p((()=>[a[19]||(a[19]=m(" 导入资源包 "))])),_:1})):v("",!0),e.hasPerm("ZYGL:MODEL-DOWNLOAD")?(i(),u(I,{key:4,placement:"top","ok-text":"是","cancel-text":"否",onConfirm:Fe},{title:p((()=>a[20]||(a[20]=[c("p",null,"该操作将对选中的模型进行打包下载",-1),c("p",null,"若未选中，将下载全部，是否继续？",-1)]))),default:p((()=>[r(z,{class:"handle-btn",loading:qe.value},{icon:p((()=>[r(N,{style:{"font-size":"16px"}})])),default:p((()=>[a[21]||(a[21]=m(" 下载开发资源 "))])),_:1},8,["loading"])])),_:1})):v("",!0),e.hasPerm("model-lib:download-hard")?(i(),u(z,{key:5,class:"handle-btn",onClick:Be},{icon:p((()=>[r(N,{style:{"font-size":"16px"}})])),default:p((()=>[a[22]||(a[22]=m(" 下载Umodel模型 "))])),_:1})):v("",!0)])])]),Ue.value?(i(),u(M,{key:0,spinning:Ue.value,"wrapper-class-name":"spin"},{default:p((()=>a[23]||(a[23]=[c("div",{class:"list"},null,-1)]))),_:1},8,["spinning"])):(i(),o(n,{key:1},[0===Ie.value.length?(i(),o("div",oe,[r(y(F),{image:y(F).PRESENTED_IMAGE_SIMPLE},null,8,["image"])])):(i(),o("div",ie,[(i(!0),o(n,null,g(Ie.value,(l=>(i(),o("div",{key:l.id,class:"item"},[c("div",ne,[c("div",{class:k(["img-box",{active:l.check}])},[c("img",{src:xe(l),alt:"图片",onErrorOnce:Ee},null,40,ce),c("div",re,[r(S,{checked:l.check,"onUpdate:checked":e=>l.check=e,class:"chk",onChange:Le},null,8,["checked","onUpdate:checked"])]),c("div",ue,[c("div",de,[r(S,{checked:l.check,"onUpdate:checked":e=>l.check=e,class:"chk",onChange:Le},null,8,["checked","onUpdate:checked"])]),e.hasPerm("model-lib:detail")&&"模型"===l.type&&2===l.fileExist?(i(),o("div",{key:0,class:"btn",onClick:e=>(e=>{Xe.value=e.modelId,ea.value=e.type,aa.value=e.fileSize,la.value=!0})(l)},"详情",8,pe)):v("",!0),e.hasPerm("model-lib:sync-by-id")?(i(),u(I,{key:1,placement:"topRight",title:"更新资源方式","ok-text":"在线","cancel-text":"离线",onCancel:e=>{return a=l,void j((()=>{Re.value.add(a.modelId,a.type)}));var a},onConfirm:e=>{return a=l,Ue.value=!0,void("贴图"===a.type?U([a.modelId]).then((e=>{200===e.code?($("success","已向系统发起同步请求操作，请耐心等待"),Ue.value=!1):($("error",e.message),Ue.value=!1)})):D({id:a.id}).then((e=>{200===e.code?$("success","已向系统发起同步请求操作，请耐心等待"):$("error",e.message),Ue.value=!1})).finally((()=>{Ue.value=!1})));var a}},{default:p((()=>a[24]||(a[24]=[c("div",{class:"btn"},"更新",-1)]))),_:2},1032,["onCancel","onConfirm"])):v("",!0),e.hasPerm("ZYGL:MODEL-COPY")?(i(),o("div",{key:2,class:"btn",onClick:e=>(e=>{if(e.fileUrl&&""!==e.fileUrl){const a=document.createElement("input");a.value=`${window.baseConfig.downloadUrl}${e.fileUrl}`,document.body.appendChild(a),a.select(),document.execCommand("Copy"),$("success","模型地址拷贝成功"),document.body.removeChild(a)}else $("warning","模型资源不存在")})(l)},"拷贝地址",8,me)):v("",!0),e.hasPerm("ZYGL:MODEL-DOWNLOAD")?(i(),o("div",{key:3,class:"btn",onClick:e=>Ve([l.modelId])},"下载资源",8,ve)):v("",!0)])],2),c("div",he,[c("span",fe,h(l.name||l.modelId),1),c("span",ye,[c("span",{class:k(["circle",1===l.fileExist?"red":"green"])},null,2),m(h(1===l.fileExist?"不存在":"已存在"),1)])]),c("div",ge,[c("span",null,"上传时间："+h(y(b)(l.updateTime).format("YYYY-MM-DD")),1),c("span",ke,h(l.type),1)])])])))),128))]))],64)),c("div",je,[r(K,{total:Oe.value,size:"small",current:ze.value.pageNo,"page-size":ze.value.pageSize,"show-total":e=>`共 ${e} 条`,"show-size-changer":"","show-quick-jumper":"",onChange:$e,onShowSizeChange:Me},null,8,["total","current","page-size","show-total"])])]),r(we,{open:la.value,"wrap-class-name":"cus-modal1",width:"1000px",footer:null,title:"模型详情","mask-closable":!1,"mask-style":{background:" rgba(0,0,0,0.71)"},onCancel:sa},{default:p((()=>[la.value?(i(),u(E,{key:0,"preview-id":Xe.value,"preview-type":ea.value,"file-size":aa.value},null,8,["preview-id","preview-type","file-size"])):v("",!0)])),_:1},8,["open"]),r(we,{title:"下载模型","wrap-class-name":"cus-modal",footer:null,open:We.value,"confirm-loading":Je.value,onCancel:Qe},{default:p((()=>[r(Ne,{value:Ke.value,"onUpdate:value":a[6]||(a[6]=e=>Ke.value=e),placeholder:"请输入模型资源ID,多个模型资源ID以英文逗号分隔","auto-size":{minRows:2,maxRows:6}},null,8,["value"]),c("div",be,[r(z,{style:{margin:"8px"},type:"primary",disabled:!Ke.value,onClick:He},{default:p((()=>a[25]||(a[25]=[m("确认")]))),_:1},8,["disabled"])])])),_:1},8,["open","confirm-loading"]),r(C,{ref_key:"addFormRef",ref:Ye,classify:ze.value.classify,onOk:Se},null,8,["classify"]),r(x,{ref_key:"addOrEditRef",ref:Re,onOk:Ae},null,512)],64)}}}),[["__scopeId","data-v-6e7eab71"]]);export{we as default};
