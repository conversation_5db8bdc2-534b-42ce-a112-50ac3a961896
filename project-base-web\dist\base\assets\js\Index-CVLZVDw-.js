import{d as e,r as a,a as s,j as t,V as l,U as i,bJ as o,am as r,c as n,bL as c,G as d,S as p,al as u,bk as m,W as v,n as h}from"./@vue-DgI1lw0Y.js";import"./dayjs-D9wJ8dSB.js";import{g as y}from"./alarmData-tFrsDi_z.js";import{i as j,d as g,f}from"./alarmBuild-D9HFb3RP.js";import{s as b}from"./dictionaryManage-BgMHheIw.js";import k from"./AlarmRule-D0mtW6rQ.js";import{u as w}from"./main-DE7o6g98.js";import{I as x,B as C,_ as S,i as _,j as I,W as z,b as N,M as R}from"./ant-design-vue-DW0D0Hn-.js";import{_ as O}from"./vue-qr-6l_NUpj8.js";import"./@babel-B4rXMRun.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const J={class:"alarm-build"},M={class:"search-wrap"},T={class:"search-content"},U={class:"expand-content"},W={class:"search-item"},$={class:"search-btns"},A={class:"table-handle"},D={class:"table-wrap"},E={key:0},L={key:1},P={key:3},B={key:4},F={key:5},G={key:6,class:"table-actions"},H=["onClick"],K={class:"pagination"},q=O(e({__name:"Index",setup(e,{expose:O}){const q=a(!1),Q=a(!1);a([]);const V=a([]),X=a([]),Y=a([]),Z=a([]),ee=a();a();const ae=a([]),se=s({name:"",pageNo:1,pageSize:10}),te=a(0),le=t((()=>({current:se.pageNo,pageSize:se.pageSize,total:te.value,pageSizeOptions:["10","20","50","100"],showTotal:e=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),ie=a(null),oe=()=>{q.value=!1,Q.value=!1},re=[{title:"规则名称",dataIndex:"name",sorter:!0,ellipsis:!0,width:180,align:"center"},{title:"规则ID",dataIndex:"id",sorter:!0,ellipsis:!0,width:180,align:"center"},{title:"监控指标",dataIndex:"metricName",sorter:!0,ellipsis:!0,width:180,align:"center"},{title:"告警条件",dataIndex:"expression",ellipsis:!0,width:200,align:"center"},{title:"告警来源",dataIndex:"source",sorter:!0,ellipsis:!0,width:120,align:"center"},{title:"告警等级",dataIndex:"severity",ellipsis:!0,width:120,align:"center"},{title:"启用状态",dataIndex:"enable",sorter:!0,ellipsis:!0,width:100,align:"center"},{title:"操作",key:"action",width:150,fixed:"right",align:"center"}];a({label:"name",value:"code"});const ne=(e,a)=>{se.pageNo=e,se.pageSize=a,ue()},ce=(e,a,s)=>{s.order?(se.sortField=s.field,se.sortRule="descend"===s.order?"DESC":"ASC"):(se.sortField="",se.sortRule=""),ue()},de=a(),pe=()=>{ee.value.init("add",ie.value)},ue=()=>{var e;Q.value=!0,ae.value=[];const a=JSON.parse(JSON.stringify(se)),s={classCode:null==(e=ie.value)?void 0:e.code,...a};g(s).then((e=>{if(200===e.code){const{rows:a,pageNo:s,pageSize:t,totalRows:l}=e.data;ae.value=a,le.value.current=s,le.value.pageSize=t,le.value.total=l}Q.value=!1})).finally((()=>{Q.value=!1})).catch((()=>{Q.value=!1}))},me=()=>{se.pageNo=1,ue()},ve=()=>{me()},he=async()=>{try{const e=await y();200===e.code&&(X.value=e.data)}catch(e){}},ye=async()=>{try{const e=await b({code:"ALERT_SOURCE"});200===e.code&&(Z.value=e.data)}catch(e){}},je={Twin:"元模型属性",Performance:"指标管理"};return O({init:async e=>{ie.value=e,q.value=!0,h((()=>{ue(),he(),ye()}))}}),(e,a)=>{const s=x,t=C,h=I,y=_,g=z,b=S,O=N,Z=R;return i(),l(Z,{title:"告警构建",width:"100%","body-style":{maxHeight:"100%",overflow:"auto"},footer:null,"wrap-class-name":"cus-modal full-modal",open:q.value,"mask-closable":!1,onCancel:oe},{default:o((()=>[r("div",J,[r("div",M,[r("div",T,[r("div",U,[r("div",W,[a[4]||(a[4]=r("span",{class:"search-label"},"规则名称",-1)),n(s,{value:se.name,"onUpdate:value":a[0]||(a[0]=e=>se.name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入规则名称",class:"search-input",onKeyup:a[1]||(a[1]=c((e=>me()),["enter"]))},null,8,["value"])]),r("div",$,[n(t,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>me())},{default:o((()=>a[5]||(a[5]=[d("搜索")]))),_:1}),n(t,{class:"search-btn",onClick:a[3]||(a[3]=e=>(se.type=null,se.level="",se.processStatus="",se.kewWord="",se.dates=[],se.pageNo=1,se.pageSize=10,void ue()))},{default:o((()=>a[6]||(a[6]=[d("重置")]))),_:1})])])]),r("div",A,[n(t,{class:"handle-btn",type:"primary",onClick:pe},{default:o((()=>a[7]||(a[7]=[d(" 创建规则")]))),_:1})])]),r("div",D,[r("div",{ref_key:"table",ref:de,class:"table-content"},[n(b,{class:"table",scroll:{y:"calc(100% - 40px)"},pagination:!1,"row-key":e=>e.id,size:"small",columns:re,"data-source":ae.value,loading:Q.value,onChange:ce},{bodyCell:o((({column:s,record:t})=>{var c,d,v;return["mappingCode"===s.dataIndex?(i(),p("div",E,m(V.value.find((e=>e.code===t.mappingCode))?V.value.find((e=>e.code===t.mappingCode)).name:t.mappingCode),1)):u("",!0),"processStatus"===s.dataIndex?(i(),p("div",L,m(Y.value.find((e=>e.code===t.processStatus))?Y.value.find((e=>e.code===t.processStatus)).value:t.processStatus),1)):u("",!0),"enable"===s.dataIndex?(i(),l(y,{key:2,placement:"top",title:1===t.enable?"确定停用告警规则？":"确定启用该告警规则？",onConfirm:()=>(e=>{const a=1===e.enable?0:1,s={...e,enable:a};j(s).then((e=>{200===e.code&&(w("success","状态修改成功"),ue())}))})(t)},{default:o((()=>[n(h,{"checked-children":"启用","un-checked-children":"停用",checked:1===t.enable},null,8,["checked"])])),_:2},1032,["title","onConfirm"])):u("",!0),"severity"===s.dataIndex?(i(),p("span",P,m((null==(c=X.value.find((e=>e.levelCode===t.severity)))?void 0:c.levelName)||t.severity),1)):u("",!0),"source"===s.dataIndex?(i(),p("span",B,m(je[t.source]||t.source),1)):u("",!0),"expression"===s.dataIndex?(i(),p("span",F,m((d=t.expression,v=t.threshold,`${{">":"大于","<":"小于",">=":"大于等于","<=":"小于等于","==":"等于","!=":"不等于",contain:"包含","=":"等于",not_contain:"不包含"}[d]||d} ${v}`)),1)):u("",!0),"action"===s.key?(i(),p("div",G,[e.hasPerm("device-alert:process")?(i(),p("a",{key:0,onClick:e=>(e=>{ee.value.init("edit",ie.value,e)})(t)},"编辑",8,H)):u("",!0),n(g,{type:"vertical"}),e.hasPerm("device-alert:delete")?(i(),l(y,{key:1,placement:"topRight",title:"确定要删除该规则吗？","ok-text":"是","cancel-text":"否",onConfirm:e=>(async e=>{try{200===(await f(e.id)).code&&(w("success","删除成功"),ue())}catch(a){w("error","删除失败")}})(t)},{default:o((()=>a[8]||(a[8]=[r("a",{class:"delete-btn"},"删除",-1)]))),_:2},1032,["onConfirm"])):u("",!0)])):u("",!0)]})),_:1},8,["row-key","data-source","loading"]),r("div",K,[ae.value.length>0?(i(),l(O,v({key:0},le.value,{onChange:ne}),null,16)):u("",!0)])],512)]),n(k,{ref_key:"alarmRuleRef",ref:ee,onOk:ve},null,512)])])),_:1},8,["open"])}}}),[["__scopeId","data-v-444bb92d"]]);export{q as default};
