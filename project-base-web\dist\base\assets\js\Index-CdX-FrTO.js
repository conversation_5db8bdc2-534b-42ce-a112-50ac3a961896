import{d as e,r as a,o as l,w as t,a as s,b9 as i,S as o,U as n,am as r,c as d,V as c,bJ as u,u as p,bk as m,q as v,al as h,A as k,B as f,G as y,bL as g,F as _,b7 as C,W as j,n as w}from"./@vue-DgI1lw0Y.js";import{am as b,ax as P,u as R,ay as S,az as I,aA as x,aB as T,aC as M,B as A}from"./main-DE7o6g98.js";import{S as E,a8 as D,T as O}from"./@ant-design-tBRGNTkq.js";import{I as z,r as F,i as L,s as K,S as N,F as G,c as H,M as U,B,g as J,h as X,a2 as $,z as q,E as W,G as Q,_ as V,b as Y}from"./ant-design-vue-DW0D0Hn-.js";import{_ as Z}from"./vue-qr-6l_NUpj8.js";import ee from"./AddEditForm-DUvH5TB6.js";import ae from"./IndicatorTable-BGQqkXvA.js";import le from"./AlarmTable-C4BGtvlb.js";import te from"./PropertiesEdit-ByS3pPZi.js";import se from"./Index-D06D2a3q.js";import{_ as ie}from"./AlarmSetting.vue_vue_type_script_setup_true_lang-DMx0QMnn.js";import{_ as oe}from"./ModelMapping.vue_vue_type_script_setup_true_lang-BCo8jLTL.js";import{u as ne}from"./useTableScrollY-9oHU_oJI.js";import{a as re}from"./axios-ChCdAMPF.js";import de from"./Index-CVLZVDw-.js";import ce from"./Index-DAnS5DCe.js";import ue from"./Index-Bv2SzFE-.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./SelectBubble-DCDJhldK.js";import"./SelectModel-ClNHKDDy.js";import"./ti-B4l9Nygm.js";import"./SelectColor.vue_vue_type_script_setup_true_lang-BdCAiBbd.js";import"./vue-pick-colors-CjUSS-xa.js";import"./@popperjs-CFrBDmKk.js";import"./@ti-cli-Z3vwStYr.js";import"./vue-CqsM5HEV.js";import"./RemoteSet-Cp4Rkq2C.js";import"./dictManage-CTOLVV06.js";import"./AssociatedConfig.vue_vue_type_script_setup_true_lang-BE6hFNQe.js";import"./AssociatedTwins-BINKmP0O.js";import"./AddEditForm-CmkoLo8T.js";import"./attachmentManage-CasMLJka.js";import"./upload-CsXIzxCg.js";import"./PreviewAndDownload.vue_vue_type_script_setup_true_lang-C0tVYshs.js";import"./PreviewForm.vue_vue_type_script_setup_true_lang-BPt1qaC1.js";import"./ConditionBox-r2AXqrZY.js";import"./no-data-DWJyvDuH.js";import"./Reletive-Bnq70Jw7.js";import"./alarmData-tFrsDi_z.js";import"./alarmBuild-D9HFb3RP.js";import"./dictionaryManage-BgMHheIw.js";import"./AlarmRule-D0mtW6rQ.js";import"./addAttrMap-CQMumYiu.js";import"./codemirror-editor-vue3-CQ6xaWU-.js";import"./diff-match-patch-jtm_x_zs.js";import"./editReturnJson-DTys2U5n.js";import"./addRelRule-nABHz7II.js";import"./indicatorData-7JRXzlPf.js";const pe={class:"tree-wrap"},me={class:"tree-contain"},ve={class:"tree-search"},he={key:1,class:"tree-content"},ke=["title"],fe={class:"title"},ye={class:"btns"},ge=["onClick"],_e=["onClick"],Ce={key:2,class:"btn",title:"删除"},je=Z(e({__name:"TwinObjGroup",emits:["handleClick","changeGroup"],setup(e,{emit:y}){const g=y,_=a({children:"children",title:"title",key:"id"}),C=a(""),j=a([]);let w=[];const x=a([]);l((()=>{T()}));const T=e=>{b({}).then((e=>{if(!e.success)return;const a=[];a.push({children:e.data,id:"0",pid:"0",title:"全部"}),j.value=M(a),w=M(a),x.value=[j.value[0].id]}))},M=e=>(e.forEach((e=>{"0"===e.id?e.add=!0:"默认"!==e.title&&(e.add=!0,e.edit=!0,e.delete=!0),e.children&&e.children.length&&(e.children=M(e.children))})),e),A=(e,a)=>{e.length?x.value=e:x.value=[j.value[0].id],g("handleClick",e,a)};t(C,(e=>{j.value=B(w,e)}));const B=(e,a)=>{const l=JSON.parse(JSON.stringify(e));if(!l||!l.length)return[];const t=[];for(const s of l){const e=B(s.children,a);s.title.indexOf(a)>-1?t.push(s):e&&e.length&&(s.children=e,t.push(s))}return t.length?t:[]},J=a(!1),X=a(!1),$=a(),q=a(""),W=s({id:"",name:"",pid:"0",sort:100}),Q={name:[{required:!0,message:"请输入名称！",trigger:"blur"}]},V=()=>{X.value||($.value.resetFields(),J.value=!1,X.value=!1)};return(e,a)=>{const l=z,t=i("plus-outlined"),s=L,y=K,w=H,b=G,M=N,B=U;return n(),o("div",pe,[r("div",me,[r("div",ve,[d(l,{value:C.value,"onUpdate:value":a[0]||(a[0]=e=>C.value=e),class:"filter",placeholder:"请输入搜索内容","allow-clear":!0},{prefix:u((()=>[d(p(E))])),_:1},8,["value"])]),0===j.value.length?(n(),c(p(F),{key:0,image:p(F).PRESENTED_IMAGE_SIMPLE},null,8,["image"])):(n(),o("div",he,[d(y,{selectedKeys:x.value,"onUpdate:selectedKeys":a[2]||(a[2]=e=>x.value=e),"show-icon":"","tree-data":j.value,class:"cus-tree","default-expand-all":!0,"field-names":_.value,onSelect:A},{title:u((l=>[r("span",{class:"root-tree-item",title:l.title},[r("span",fe,m(l.title),1),r("div",ye,[l.add&&e.hasPerm("twin-class-group:add")?v((n(),o("span",{key:0,class:"btn",title:"添加",onClick:k((e=>(e=>{W.pid=e.id,W.sort=100,W.name="",J.value=!0,q.value="add"})(l)),["stop"])},[d(t)],8,ge)),[[f,5!==l.level&&!l.hasTwinClasses]]):h("",!0),l.edit&&e.hasPerm("twin-class-group:edit")?(n(),o("span",{key:1,class:"btn",title:"编辑",onClick:k((e=>(e=>{W.pid=e.pid,W.sort=100,W.id=e.data.id,W.name=e.data.title,J.value=!0,q.value="edit"})(l)),["stop"])},[d(p(D))],8,_e)):h("",!0),l.delete&&e.hasPerm("twin-class-group:delete")?(n(),o("span",Ce,[d(s,{placement:"topRight",title:"确认删除？","ok-text":"确认","cancel-text":"取消",onConfirm:e=>(e=>{P({id:e.data.id}).then((e=>{200===e.code&&(T(),R("success","删除成功"),g("changeGroup"))})).catch((e=>{R("error",`删除失败${e.message}`)}))})(l)},{default:u((()=>[d(p(O),{onClick:a[1]||(a[1]=k((()=>{}),["stop"]))})])),_:2},1032,["onConfirm"])])):h("",!0)])],8,ke)])),_:1},8,["selectedKeys","tree-data","field-names"])]))]),d(B,{width:500,title:"add"===q.value?"新增分组":"编辑分组","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:J.value,"confirm-loading":X.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[4]||(a[4]=e=>{X.value||(X.value=!0,$.value.validate().then((()=>{if("add"===q.value){const e={name:W.name,pid:W.pid,sort:W.sort};S(e).then((e=>{X.value=!1,200===e.code&&(R("success","新增成功"),V(),T(),g("changeGroup"))})).finally((()=>{X.value=!1}))}else{const e={name:W.name,pid:W.pid,sort:W.sort,id:W.id};I(e).then((e=>{X.value=!1,200===e.code&&(R("success","编辑成功"),V(),T(),g("changeGroup"))})).finally((()=>{X.value=!1}))}})).catch((e=>{X.value=!1})))}),onCancel:V},{default:u((()=>[d(M,{spinning:X.value},{default:u((()=>[d(b,{ref_key:"formRef",ref:$,model:W,rules:Q,"label-align":"left"},{default:u((()=>[d(w,{name:"name",label:"分组名称","has-feedback":""},{default:u((()=>[d(l,{value:W.name,"onUpdate:value":a[3]||(a[3]=e=>W.name=e),placeholder:"请输入分组名称",maxlength:30},null,8,["value"])])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])])}}}),[["__scopeId","data-v-2de3150d"]]),we={class:"twin-obj"},be={class:"left"},Pe={class:"right"},Re={class:"table-handle"},Se={class:"total"},Ie={class:"search-wrap"},xe={class:"search-item"},Te={class:"search-item"},Me={class:"search-item"},Ae={class:"search-item"},Ee={class:"search-btns"},De={class:"table-wrap"},Oe={key:0},ze={key:0,class:"table-actions"},Fe=["onClick"],Le=["onClick"],Ke=["onClick"],Ne=["onClick"],Ge={key:1,class:"table-actions"},He=["onClick"],Ue=["onClick"],Be=["onClick"],Je=["onClick"],Xe=["onClick"],$e=["onClick"],qe={class:"ant-dropdown-link"},We=["onClick"],Qe=["onClick"],Ve=["onClick"],Ye=["onClick"],Ze=["onClick"],ea=["onClick"],aa=["onClick"],la={key:1,class:"table-actions"},ta=["onClick"],sa=["onClick"],ia=["onClick"],oa=["onClick"],na={key:1},ra={key:2},da={class:"pagination"},ca="1476825472376508418",ua=Z(e({__name:"Index",setup(e){const t={PARK:"园区",MAP:"地图",OTHER:"其他"},s={POINT:"点",LINE:"线",SURFACE:"面",FORM:"表单"},v=a(),{scrollY:k}=ne(v),f=[{title:"所在层级",dataIndex:"level",sortDirections:["descend","ascend"],width:120,sorter:!0,ellipsis:!0},{title:"孪生对象名称",dataIndex:"name",sortDirections:["descend","ascend"],sorter:!0,ellipsis:!0},{title:"唯一编码",dataIndex:"code",sortDirections:["descend","ascend"],sorter:!0,ellipsis:!0},{title:"数据类型",dataIndex:"dataType",width:120,sortDirections:["descend","ascend"],sorter:!0},{title:"孪生体数量",width:120,dataIndex:"total"},{title:"操作",dataIndex:"action",width:360,minWidth:360}],b=a([{code:"MAP",value:"地图"},{code:"PARK",value:"园区"},{code:"OTHER",value:"其他"}]),P=a([{code:"POINT",value:"点"},{code:"LINE",value:"线"},{code:"SURFACE",value:"面"},{code:"FORM",value:"表单"}]),S=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),I=(e,a)=>{S.value=Object.assign(S.value,{current:e,pageSize:a}),pe()},E=(e,a,l)=>{let t={};l.order&&(t={sortField:l.field,sortRule:"descend"===l.order?"DESC":"ASC"}),pe(t)},D=a({keys:[],list:[]}),F={onChange:(e,a)=>{D.value.keys=e,D.value.list=a},getCheckboxProps:e=>({disabled:e.groupId===ca})},K=a({name:null,code:null,level:null,dataType:null,groupId:""}),N=a(!1),G=a([]),H=a(0);l((()=>{pe()}));const Z=()=>{S.value.current=1,S.value.pageSize=10,pe()},pe=(e={})=>{G.value=[],N.value=!0,x({...K.value,pageNo:S.value.current,pageSize:S.value.pageSize,...e}).then((e=>{if(200===e.code){const{rows:a,pageNo:l,pageSize:t,totalRows:s}=e.data;G.value=a||[],S.value.current=l,S.value.pageSize=t,S.value.total=s}N.value=!1})).catch((()=>{N.value=!1})),T({...K.value,pageNo:S.value.current,pageSize:S.value.pageSize,...e}).then((e=>{H.value=e.data}))},me=e=>{M({id:e.id}).then((e=>{e.success?(R("success","孪生对象删除成功"),Z()):R("error",e.message)})).finally((()=>{D.value.keys=[],D.value.list=[]}))},ve=()=>{U.confirm({title:"提示",content:"批量删除孪生对象将关联删除所有的历史告警、指标数据，确认删除？",okText:"确定",cancelText:"取消",onOk:()=>{he()}})},he=()=>{D.value.keys.forEach((e=>{M({id:e}).then((e=>{e.success?(R("success","孪生对象删除成功"),Z()):R("error",e.message)})).finally((()=>{D.value.keys=[],D.value.list=[]}))}))},ke=a(!1),fe=(e,a)=>{K.value.groupId=e.toString(),Z(),a.node.children.length&&"全部"!==a.node.title?ke.value=!0:ke.value=!1},ye=()=>{K.value.groupId=""},ge=a(),_e=(e,a,l)=>{ge.value.init(e,a,l)},Ce=()=>{Z()},ua=a(!1),pa=a(),ma=a(!1),va=a(),ha=(e,a)=>{ua.value=!!a,va.value=e,ma.value=!0,w((()=>{pa.value.init(e)}))},ka=a(),fa=e=>{ka.value.init(e)},ya=a(),ga=e=>{ya.value.init(e.code,e.name)},_a=a(),Ca=e=>{_a.value.init(e.code,e.name)},ja=a(),wa=a(),ba=a(),Pa=a(),Ra=a();return(e,a)=>{const l=B,w=z,R=J,x=X,T=L,M=$,ne=i("DownOutlined"),he=Q,Sa=W,Ia=q,xa=V,Ta=Y,Ma=U;return n(),o("div",we,[r("div",be,[e.hasPerm("twin-class-group:tree")?(n(),c(je,{key:0,class:"side-menu",onHandleClick:fe,onChangeGrou:ye})):h("",!0)]),r("div",Pe,[r("div",Re,[e.hasPerm("twin-class:delete")?(n(),c(l,{key:0,class:"handle-btn",disabled:D.value.keys.length<=0,onClick:ve},{icon:u((()=>[d(p(O))])),default:u((()=>[a[10]||(a[10]=y(" 批量删除 "))])),_:1},8,["disabled"])):h("",!0),r("div",null,[r("span",Se,"孪生体总数："+m(H.value)+"个",1),e.hasPerm("twin-class:add")?(n(),c(l,{key:0,type:"primary",disabled:K.value.groupId===ca||ke.value,class:"handle-btn",onClick:a[0]||(a[0]=e=>_e("add",null,K.value.groupId))},{default:u((()=>a[11]||(a[11]=[y(" 新增孪生对象 ")]))),_:1},8,["disabled"])):h("",!0)])]),r("div",Ie,[r("div",xe,[a[12]||(a[12]=r("span",{class:"search-label"},"对象名称",-1)),d(w,{value:K.value.name,"onUpdate:value":a[1]||(a[1]=e=>K.value.name=e),"allow-clear":"",placeholder:"请输入名称",class:"search-input",onKeyup:a[2]||(a[2]=g((e=>Z()),["enter"]))},null,8,["value"])]),r("div",Te,[a[13]||(a[13]=r("span",{class:"search-label"},"唯一编码",-1)),d(w,{value:K.value.code,"onUpdate:value":a[3]||(a[3]=e=>K.value.code=e),"allow-clear":"",placeholder:"请输入编码",class:"search-input",onKeyup:a[4]||(a[4]=g((e=>Z()),["enter"]))},null,8,["value"])]),r("div",Me,[a[14]||(a[14]=r("span",{class:"search-label"},"所在层级",-1)),d(x,{value:K.value.level,"onUpdate:value":a[5]||(a[5]=e=>K.value.level=e),class:"search-select","allow-clear":"",placeholder:"请选择层级",onChange:Z},{default:u((()=>[(n(!0),o(_,null,C(b.value,((e,a)=>(n(),c(R,{key:a,value:e.code},{default:u((()=>[y(m(e.value),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])]),r("div",Ae,[a[17]||(a[17]=r("span",{class:"search-label"},"数据类型",-1)),d(x,{value:K.value.dataType,"onUpdate:value":a[6]||(a[6]=e=>K.value.dataType=e),class:"search-select","allow-clear":"",placeholder:"请选择数据类型",onChange:Z},{default:u((()=>[(n(!0),o(_,null,C(P.value,((e,a)=>(n(),c(R,{key:a,value:e.code},{default:u((()=>[y(m(e.value),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"]),r("div",Ee,[d(l,{type:"primary",class:"search-btn",onClick:a[7]||(a[7]=e=>Z())},{default:u((()=>a[15]||(a[15]=[y(" 查询 ")]))),_:1}),d(l,{class:"search-btn",onClick:a[8]||(a[8]=e=>(K.value.name="",K.value.code="",K.value.level=null,K.value.dataType=null,void Z()))},{default:u((()=>a[16]||(a[16]=[y(" 重置 ")]))),_:1})])])]),r("div",De,[r("div",{ref_key:"table",ref:v,class:"table-content"},[d(xa,{class:"table",scroll:{y:p(k)},pagination:!1,size:"small",loading:N.value,"row-key":e=>e.id,"row-selection":F,columns:f,"data-source":G.value,onChange:E},{bodyCell:u((({column:l,record:i})=>["action"===l.dataIndex?(n(),o(_,{key:0},[i.groupId!==ca?(n(),o("span",Oe,["FORM"===i.dataType?(n(),o("div",ze,[e.hasPerm("twin-class:detail")?(n(),o("a",{key:0,onClick:e=>ha(i)},"对象数据",8,Fe)):h("",!0),e.hasPerm("twin-class:modify-form")?(n(),o("a",{key:1,onClick:e=>fa(i)},"属性编辑",8,Le)):h("",!0),e.hasPerm("twin-class:copy")?(n(),o("a",{key:2,onClick:e=>_e("copy",i,i.groupId)},"复制",8,Ke)):h("",!0),e.hasPerm("twin-class:edit")?(n(),o("a",{key:3,onClick:e=>_e("edit",i,i.groupId)},"编辑",8,Ne)):h("",!0),e.hasPerm("twin-class:delete")?(n(),c(T,{key:4,placement:"topRight",title:"确认删除？",onConfirm:()=>me(i)},{default:u((()=>a[18]||(a[18]=[r("a",null,"删除",-1)]))),_:2},1032,["onConfirm"])):h("",!0)])):(n(),o("div",Ge,[e.hasPerm("twin-class:detail")&&"MAP"===i.level?(n(),c(M,{key:0,trigger:"hover"},{content:u((()=>[r("div",null,[r("a",{onClick:e=>ha(i,!0)},"查看数据",8,He),a[19]||(a[19]=y(" | ")),r("a",{onClick:e=>(e=>{ua.value=!1;const a=re.defaults.headers.common.Tenant,{href:l}=A.resolve({path:`/${a}/preview/mapTwinData`,query:{id:e.id,name:e.name}});window.open("_blank").location=`${l}`})(i)},"地球摆点",8,Ue)])])),default:u((()=>[a[20]||(a[20]=r("a",null,"对象数据",-1))])),_:2},1024)):h("",!0),e.hasPerm("twin-class:detail")&&"MAP"!==i.level?(n(),o("a",{key:1,onClick:e=>ha(i)},"对象数据",8,Be)):h("",!0),e.hasPerm("LSDX:CHECK-PER")?(n(),o("a",{key:2,onClick:e=>ga(i)},"指标数据",8,Je)):h("",!0),e.hasPerm("LSDX:CHECK-ALARM")?(n(),o("a",{key:3,onClick:e=>Ca(i)},"告警数据",8,Xe)):h("",!0),e.hasPerm("twin-class:modify-form")?(n(),o("a",{key:4,onClick:e=>fa(i)},"属性编辑",8,$e)):h("",!0),d(Ia,null,{overlay:u((()=>[d(Sa,null,{default:u((()=>[d(he,null,{default:u((()=>[e.hasPerm("LSDX:ALARM-SETTING")?(n(),o("a",{key:0,onClick:e=>(e=>{ja.value.init(e)})(i)},"告警配置",8,We)):h("",!0)])),_:2},1024),e.hasPerm("twin-class-model-mapping:save")?(n(),c(he,{key:0},{default:u((()=>[r("a",{onClick:e=>(e=>{wa.value.init(e)})(i)},"模型映射",8,Qe)])),_:2},1024)):h("",!0),d(he,null,{default:u((()=>[r("a",{onClick:e=>(e=>{var a;null==(a=ba.value)||a.init(e)})(i)},"属性映射",8,Ve)])),_:2},1024),d(he,null,{default:u((()=>[r("a",{onClick:e=>(e=>{var a;null==(a=Pa.value)||a.init(e)})(i)},"关系构建",8,Ye)])),_:2},1024),d(he,null,{default:u((()=>[r("a",{onClick:e=>(e=>{var a;null==(a=Ra.value)||a.init(e)})(i)},"告警构建",8,Ze)])),_:2},1024),e.hasPerm("twin-class:copy")?(n(),c(he,{key:1},{default:u((()=>[r("a",{onClick:e=>_e("copy",i,i.groupId)},"复制",8,ea)])),_:2},1024)):h("",!0),e.hasPerm("twin-class:edit")?(n(),c(he,{key:2},{default:u((()=>[r("a",{onClick:e=>_e("edit",i,i.groupId)},"编辑",8,aa)])),_:2},1024)):h("",!0),e.hasPerm("twin-class:delete")?(n(),c(he,{key:3},{default:u((()=>[d(T,{placement:"topRight",title:"删除当前孪生对象将关联删除此孪生对象下所有的历史告警、指标数据，确认删除？",onConfirm:()=>me(i)},{default:u((()=>a[22]||(a[22]=[r("a",null,"删除",-1)]))),_:2},1032,["onConfirm"])])),_:2},1024)):h("",!0)])),_:2},1024)])),default:u((()=>[r("a",qe,[a[21]||(a[21]=y("更多 ")),d(ne)])])),_:2},1024)]))])):(n(),o("div",la,[e.hasPerm("twin-class:detail")?(n(),o("a",{key:0,onClick:e=>ha(i)},"对象数据",8,ta)):h("",!0),"FORM"!==i.dataType&&e.hasPerm("LSDX:CHECK-PER")?(n(),o("a",{key:1,onClick:e=>ga(i)},"指标数据",8,sa)):h("",!0),"FORM"!==i.dataType&&e.hasPerm("LSDX:CHECK-ALARM")?(n(),o("a",{key:2,onClick:e=>Ca(i)},"告警数据",8,ia)):h("",!0),e.hasPerm("twin-class:edit")?(n(),o("a",{key:3,onClick:e=>fa(i)},"属性编辑",8,oa)):h("",!0)]))],64)):h("",!0),"level"===l.dataIndex?(n(),o("span",na,m(t[i.level]),1)):h("",!0),"dataType"===l.dataIndex?(n(),o("span",ra,m(s[i.dataType]),1)):h("",!0)])),_:1},8,["scroll","loading","row-key","data-source"]),r("div",da,[G.value.length>0?(n(),c(Ta,j({key:0},S.value,{onChange:I}),null,16)):h("",!0)])],512)])]),d(ee,{ref_key:"addEditFormRef",ref:ge,onSuccess:Ce},null,512),d(ae,{ref_key:"indicatorTableRef",ref:ya},null,512),d(le,{ref_key:"alarmTableRef",ref:_a},null,512),d(te,{ref_key:"propertiesEditRef",ref:ka,onSuccess:pe},null,512),d(Ma,{width:"100%","body-style":{maxHeight:"100%",overflow:"auto",minHeight:"600px"},title:"孪生体数据【"+(va.value?va.value.name:"")+"】",footer:null,"wrap-class-name":"cus-modal full-modal",open:ma.value,"mask-closable":!1,onCancel:a[9]||(a[9]=e=>ma.value=!1)},{default:u((()=>[ma.value?(n(),c(se,{key:0,ref_key:"twinDataManageRef",ref:pa,onRefreshMap:pe,checkMapData:ua.value},null,8,["checkMapData"])):h("",!0)])),_:1},8,["title","open"]),d(ie,{ref_key:"alarmSettingRef",ref:ja},null,512),d(oe,{ref_key:"modelMappingRef",ref:wa},null,512),d(de,{ref_key:"alarmBuildIndexRef",ref:Ra},null,512),d(ce,{ref_key:"attrMapIndexRef",ref:ba},null,512),d(ue,{ref_key:"relBuildIndexRef",ref:Pa},null,512)])}}}),[["__scopeId","data-v-89885944"]]);export{ua as default};
