import{a as e,g as s}from"./systemAuthoriza-YtR9ek7A.js";import t from"./UpLoadData-E36UpUwv.js";import a from"./SelectVersion-CGjKimtj.js";import i from"./SelectFileExport-CyOD_f7W.js";import o from"./CheckField-DVimB6Jb.js";import{a as r}from"./axios-ChCdAMPF.js";import{Q as n,am as l,an as m}from"./@ant-design-tBRGNTkq.js";import{f as p,B as d}from"./ant-design-vue-DW0D0Hn-.js";import{d as c,r as u,w as j,a as f,o as h,S as v,U as y,al as b,c as T,am as k,bJ as g,u as x,G as C,V as _,bk as w}from"./@vue-DgI1lw0Y.js";import{_ as $}from"./vue-qr-6l_NUpj8.js";import"./qs-Cgg8q2iR.js";import"./@babel-B4rXMRun.js";import"./side-channel-C0354c6F.js";import"./es-errors-Bq3kx8t5.js";import"./object-inspect-7jlLeo80.js";import"./crypto-js-HwOCoVPb.js";import"./side-channel-list-9pNTKFEK.js";import"./side-channel-map-qN4s3zsW.js";import"./get-intrinsic-BQNEepf0.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-z0bYe-bF.js";import"./gopd-uUVnxxv0.js";import"./es-define-property-lN4EXf1s.js";import"./has-symbols-CRskrF1U.js";import"./get-proto-nTH32ZvN.js";import"./dunder-proto-W0bZB-Yu.js";import"./call-bind-apply-helpers-CohWVzbO.js";import"./function-bind-Ckw9YnhN.js";import"./hasown-Dpzr2-IR.js";import"./call-bound-BVGeUV4S.js";import"./side-channel-weakmap-dRphfAXb.js";import"./main-DE7o6g98.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const z={class:"system-data-migration"},D={key:0,class:"wrap",style:{"margin-top":"24px"}},S={class:"head"},E={class:"help",style:{"margin-left":"6px"}},M={class:"col1"},P={class:"val"},q={class:"col1"},R={class:"val"},U={style:{color:"var(--theme-color)"}},F=$(c({__name:"Index",props:{auth:{type:Boolean,default:!0}},setup(c){const $=c,F=u(!0);j((()=>$.auth),(e=>{F.value=e}),{immediate:!0});const B=f({createTime:"",totalTime:"",expiredTime:"",userCount:"",sceneCount:"",authStatus:"",qrText:"",tenant:"master",hidden:!0,downTime:0});h((()=>{const e=r.defaults.headers.common.Tenant||"master";B.tenant=e,L()}));const L=()=>{"master"!==B.tenant?(B.hidden=!0,Q()):(B.hidden=!1,V())},Q=()=>{e().then((e=>{if(200===e.code&&!0===e.success){const{licenseUpdateTime:s,licenseTotalTime:t,licenseExpiredTime:a,sceneCount:i,userCount:o,valid:r}=e.data;B.createTime=s,B.totalTime=A(t),B.expiredTime=a,B.userCount=o,B.sceneCount=i,B.authStatus=r?"normal":"expire"}}))},V=()=>{s().then((e=>{if(200===e.code&&!0===e.success){const{data:s}=e;B.createTime=s.licenseTime,B.totalTime=`${s.authUseDays}天`,s.authUseDay<0?B.expiredTime="无":B.expiredTime=G(s.authEndDate),B.qrText=s.machineCode,B.userCount=s.userCount,B.sceneCount=s.sceneCount,B.authStatus=s.valid?"normal":s.authEndDate?"expire":"noPermiss"}}))},A=e=>{const s=Math.floor(e/86400);return s>0?Math.floor(s/31)?Math.floor(s/365)?`${Math.floor(s/365)}年`:`${Math.floor(s/31)}月`:`${s}天`:`${e}秒`},G=e=>{const s=e;if(s&&8===s.length){return`${s.substr(0,4)}-${s.substr(4,2)}-${s.substr(6,2)}`}return""},I=u(),J=e=>{"data"===e?I.value.init("数据导入","data"):"file"===e&&I.value.init("文件导入","file")},O=u(),H=e=>{O.value.init(e)},K=u(),N=u();return(e,s)=>{const r=p,c=d;return y(),v("div",z,[F.value?(y(),v("div",D,[k("div",S,[s[5]||(s[5]=k("span",{class:"title"},"数据迁移",-1)),k("span",E,[T(r,{placement:"right","overlay-class-name":"model_tooltip_data"},{title:g((()=>s[4]||(s[4]=[k("div",null,[k("p",null,[C(" 支持版本同版本,不同项目之间的系统数据互相迁移。"),k("br"),C(" ① 各类孪生体数据和告警、性能指标等数据，请选择“数据导出”；"),k("br"),C(" ② 孪生体附件、图标，模型和贴图等资源，请选择“文件导出”；"),k("br"),C(" ③ 全部三维场景及相关数据，则请将“数据”和“文件”都进行导出"),k("br")]),k("p",{class:"font-small"},"数据导出：指备份包括告警、指标数据和部门、用户信息等在内的系统数据"),k("p",{class:"font-small"},"文件导出：指备份包括模型、贴图资源，孪生体图标和自定义孪生体附件在内的文件资源"),k("p",{style:{"font-weight":"bold",color:"red"}},"执行“导入”操作前请提前备份好当前环境数据")],-1)]))),default:g((()=>[T(x(n),{style:{color:"#ef7b1a"}})])),_:1})])]),k("div",M,[s[8]||(s[8]=k("div",{class:"lab"},"项目数据迁移",-1)),k("div",P,[e.hasPerm("data-migration:do-import-data")?(y(),_(c,{key:0,size:"small",class:"btn",onClick:s[0]||(s[0]=e=>J("data"))},{icon:g((()=>[T(x(l))])),default:g((()=>[s[6]||(s[6]=C("数据导入"))])),_:1})):b("",!0),e.hasPerm("data-migration:do-export-data")?(y(),_(c,{key:1,size:"small",class:"btn",onClick:s[1]||(s[1]=e=>{K.value.init(B.tenant)})},{icon:g((()=>[T(x(m))])),default:g((()=>[s[7]||(s[7]=C(" 数据导出 "))])),_:1})):b("",!0)])]),k("div",q,[s[11]||(s[11]=k("div",{class:"lab"},"项目文件迁移",-1)),k("div",R,[e.hasPerm("data-migration:do-import-file")?(y(),_(c,{key:0,size:"small",class:"btn",onClick:s[2]||(s[2]=e=>J("file"))},{icon:g((()=>[T(x(l))])),default:g((()=>[s[9]||(s[9]=C("文件导入"))])),_:1})):b("",!0),e.hasPerm("data-migration:do-export-file")?(y(),_(c,{key:1,size:"small",class:"btn",disabled:0!==B.downTime,onClick:s[3]||(s[3]=e=>{N.value.init(B.tenant)})},{icon:g((()=>[T(x(m))])),default:g((()=>[k("span",U,w(0===B.downTime?"":`${B.downTime}s `),1),s[10]||(s[10]=C("文件导出"))])),_:1},8,["disabled"])):b("",!0)])])])):b("",!0),T(t,{ref_key:"upLoadDataRef",ref:I,onOk:H},null,512),T(a,{ref_key:"selectVersionRef",ref:K},null,512),T(i,{ref_key:"selectFileExportRef",ref:N},null,512),T(o,{ref_key:"checkFieldRef",ref:O},null,512)])}}}),[["__scopeId","data-v-a8e94815"]]);export{F as default};
