var e=Object.defineProperty,a=(a,t,i)=>((a,t,i)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):a[t]=i)(a,"symbol"!=typeof t?t+"":t,i);import{d as t,a as i,r as o,S as d,F as l,am as s,c as n,bL as r,bJ as u,G as c,V as _,al as m,u as p,b9 as h,ar as v,b7 as g,bk as f,q as y,A as b,B as k,Y as O,W as I,U as w}from"./@vue-DgI1lw0Y.js";import{U as N,V as S,W as j,ag as x,Y as R,u as F,ah as $,e as M,ai as T,Z as B}from"./main-DE7o6g98.js";import{u as C}from"./useTableScrollY-9oHU_oJI.js";import L from"./AddEditForm-CmkoLo8T.js";import E from"./upload-CsXIzxCg.js";import{_ as A}from"./PreviewAndDownload.vue_vue_type_script_setup_true_lang-C0tVYshs.js";import D from"./ConditionBox-r2AXqrZY.js";import V from"./Reletive-Bnq70Jw7.js";import{I as z,B as P,i as G,_ as U,f as J,b as H,M as W}from"./ant-design-vue-DW0D0Hn-.js";import{_ as q}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./@ti-cli-Z3vwStYr.js";import"./vue-CqsM5HEV.js";import"./attachmentManage-CasMLJka.js";import"./AssociatedTwins-BINKmP0O.js";import"./js-binary-schema-parser-G48GG52R.js";import"./PreviewForm.vue_vue_type_script_setup_true_lang-BPt1qaC1.js";import"./no-data-DWJyvDuH.js";class K{constructor(e=2e3){a(this,"cache"),a(this,"maxSize"),this.cache=new Map,this.maxSize=e}set(e,a){if(this.cache.has(e)&&this.cache.delete(e),this.cache.size>=this.maxSize){const e=this.cache.keys().next().value;this.cache.delete(e)}this.cache.set(e,a)}get(e){if(!this.cache.has(e))return null;const a=this.cache.get(e);return this.cache.delete(e),this.cache.set(e,a),a}has(e){return this.cache.has(e)}setBatch(e){Object.entries(e).forEach((([e,a])=>{this.set(e,a)}))}clear(){this.cache.clear()}size(){return this.cache.size}getMaxSize(){return this.maxSize}setMaxSize(e){for(this.maxSize=e;this.cache.size>this.maxSize;){const e=this.cache.keys().next().value;this.cache.delete(e)}}getStats(){return{size:this.cache.size,maxSize:this.maxSize,usage:`${(this.cache.size/this.maxSize*100).toFixed(1)}%`}}}const X={class:"content"},Y={class:"search-wrap"},Q={class:"search-content"},Z={class:"search-item"},ee={class:"search-btns"},ae={class:"table-handle"},te={class:"table-wrap"},ie={key:0,class:"table-actions"},oe=["onClick"],de=["onClick"],le={key:0},se={key:1},ne=["onClick"],re=["onClick"],ue={class:"pagination"},ce=["innerHTML"],_e="1476825472376508418",me=q(t({__name:"Index",props:{checkMapData:{type:Boolean,default:!1}},emits:["setAssMapOpenEdit","refreshMap"],setup(e,{expose:a,emit:t}){const q=N(),me=t,pe=e,he=(e,a)=>{try{const t=e[a];return!!t&&JSON.parse(t).length}catch(t){return!1}},ve=i({searchValue:"",scene_id:"",data_source:"",Building_data:"",Floor_data:"",Room_data:"",scene_ids:[],Name_data:""}),ge={PARK:"园区",MAP:"地图",OTHER:"其他"},fe=o(!1),ye=o([]),be=o([]),ke=o([]),Oe=o([]);let Ie={},we={};const Ne=o({}),Se=o(!1),je=o([]),xe=o([]),Re=o([]),Fe=new K(2e3),$e=async e=>{if(!e)return"";const a=String(e),t=Fe.get(a);if(t)return t.name||"";try{const e=await T({id:Number(a),pageSize:10});if(200===e.code&&e.data){Fe.setBatch(e.data);const t=Fe.get(a);return(null==t?void 0:t.name)||""}}catch(i){}return""},Me=e=>{try{return JSON.parse(e).toString()}catch(a){return e}},Te=()=>{Pe(),me("refreshMap")},Be=()=>{Pe(),me("refreshMap")},Ce=e=>({onClick:()=>{Se.value&&!e.iconStatus&&"ASSET_IMPORT"===e.data_source_source&&me("setAssMapOpenEdit",e)}}),Le=()=>{const e=[];return Se.value||"FORM"===Ie.dataType||e.push({label:"所属场景",type:"select",fieldValue:ve.scene_id?ve.scene_id:"",fieldName:"scene_id",data:je.value,placeholder:"请选择孪生体点位所属场景"}),Ie.groupId!==_e&&"FORM"!==Ie.dataType&&e.push({label:"点位数据来源",type:"select",fieldValue:ve.data_source?ve.data_source:"",fieldName:"data_source",data:xe.value,placeholder:"请选择孪生体"}),"FORM"!==Ie.dataType&&"PARK"===Ie.level&&(Ie.groupId===_e?("BUILDING"!==Ie.code&&"FLOOR"!==Ie.code&&"ROOM"!==Ie.code||("BUILDING"===Ie.code?e.push({label:"名称",type:"input",fieldValue:ve.Name_data?ve.Name_data:"",fieldName:"Name_data",placeholder:"请输入建筑名称"}):e.push({label:"所属建筑",type:"input",fieldValue:ve.Building_data?ve.Building_data:"",fieldName:"Building_data",placeholder:"请输入所属建筑名称"})),"FLOOR"!==Ie.code&&"ROOM"!==Ie.code||e.push({label:"FLOOR"===Ie.code?"名称":"所属楼层",type:"input",fieldValue:ve.Floor_data?ve.Floor_data:"",fieldName:"Floor_data",placeholder:"FLOOR"===Ie.code?"请输入楼层名称":"请输入所属楼层名称"}),"ROOM"===Ie.code&&e.push({label:"名称",type:"input",fieldValue:ve.Name_data?ve.Name_data:"",fieldName:"Name_data",placeholder:"请输入名称"})):(e.push({label:"所属建筑",type:"input",fieldValue:ve.Building_data?ve.Building_data:"",fieldName:"Building_data",placeholder:"请输入所属建筑"}),e.push({label:"所属楼层",type:"input",fieldValue:ve.Floor_data?ve.Floor_data:"",fieldName:"Floor_data",placeholder:"请输入所属楼层"}),e.push({label:"所属房间",type:"input",fieldValue:ve.Room_data?ve.Room_data:"",fieldName:"Room_data",placeholder:"请输入所属房间"}))),Oe.value.forEach((a=>{const t=a.options.showTime?ve.RiQiXuanZeKuang:ve[a.model];e.push({label:a.label,type:a.options.showTime?"date":"input",fieldValue:t||"",fieldName:a.options.showTime?"RiQiXuanZeKuang":a.model,placeholder:a.options.showTime?"请选择时间":"请输入"})})),ke.value.forEach((a=>{e.push({label:a.label,type:a.type,fieldValue:ve[a.model]?ve[a.model]:"",fieldName:a.model,data:a.options.options,placeholder:"请选择"})})),e},Ee=e=>{let a=!1;e.forEach((e=>{ve[e.fieldName]=e.fieldValue,"所属场景"===e.label&&(a=!0)})),a?Ae():Pe(),Ue.value.list=[],Ue.value.keys=[]},Ae=()=>{if(ve.scene_id){Ie.groupId===_e?S({uuid:ve.scene_id}).then((e=>{ve.scene_ids=e.data,Pe()})):j({uuid:ve.scene_id}).then((e=>{ve.scene_ids=e.data,Pe()}))}else ve.scene_ids=[],Pe()},De=o();C(De);const Ve=()=>{ze.value.current=1,ze.value.pageSize=10,Pe()},ze=o({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),Pe=async(e={})=>{var a,t;const i=He.value;fe.value=!0;const o=Ie.code,d=Ie.groupId===_e,l="MAP"!==Ie.level&&"FORM"!==Ie.dataType,s={};d?"FORM"!==Ie.dataType&&(s["&/Scene_record"]={enable:"1"}):l&&(s["</Scene_record"]={});let n="";if(i.sortField&&i.sortRule){let{sortField:e}=i;"sceneName"===i.sortField?e=d?"scene_code":"scene_id":"sceneDataName"===i.sortField?e="user_id":"buildingName"===i.sortField?e="user_building_id":"floorName"===i.sortField?e="user_floor_id":"roomName"===i.sortField&&(e="user_room_id"),n=`${e}${"ASC"===i.sortRule?"+":"-"}`}else n="";const r={};if(ve.searchValue&&(we.forEach((e=>{"uploadFile"!==e.type&&(r[`${e.model}$`]=`%${ve.searchValue}%`)})),r.uuid$=`%${ve.searchValue}%`,Object.keys(r).length>1)){const e=[];Object.keys(r).forEach((a=>{e.push(a)})),r["@combine"]=e.join(",")}ve.data_source&&(r.data_source=ve.data_source),ve.scene_id&&(d?r["scene_code{}"]=ve.scene_ids:r["scene_id{}"]=ve.scene_ids),ke.value.forEach((e=>{ve[e.model]&&("checkbox"===e.type?0!==ve[e.model].length&&(r[e.model]=ve[e.model].toString()):r[e.model]=ve[e.model])})),Oe.value.forEach((e=>{ve[e.model]&&(r[`${e.model}$`]=`%${ve[e.model]}%`)})),ve.Name_data&&(r.user_id$=`%${ve.Name_data}%`),d||(ve.Building_data&&(r.user_building_id$=`%${ve.Building_data}%`),ve.Floor_data&&(r.user_floor_id$=`%${ve.Floor_data}%`),ve.Room_data&&(r.user_room_id$=`%${ve.Room_data}%`));const u={"[]":{join:s,[o]:{...r,"@order":`${n||"create_time-,uuid-"}`},page:ze.value.current-1,count:ze.value.pageSize,query:2},"total@":"/[]/total","info@":"/[]/info"};if(l){let e={Scene_record:{"uuid@":`/${o}/scene_id`,"@column":"uuid;name;scene_code"},Building_data:{"user_id@":`/${o}/user_building_id`,"parent_scene_record_uuid@":`/${o}/scene_id`,"@column":"uuid;user_id;name;setting_name"},Floor_data:{"user_id@":`/${o}/user_floor_id`,"parent_scene_record_uuid@":`/${o}/scene_id`,"@column":"uuid;user_id;name;setting_name"},Room_data:{"user_id@":`/${o}/user_room_id`,"parent_scene_record_uuid@":`/${o}/scene_id`,"@column":"uuid;user_id;name;setting_name"}};"BUILDING"!==Ie.code&&"PARK"!==Ie.code&&(e={FLOOR:{"@order":"create_time-,uuid-"},BUILDING:{"@order":"create_time-,uuid-"},Scene_record:{"uuid@":`/${o}/scene_id`,"@column":"uuid;name;scene_code"},Building_data:{"user_id@":`/${o}/user_building_id`,"parent_scene_record_uuid@":`/${o}/scene_id`,"@column":"uuid;user_id;name;setting_name"},Floor_data:{"user_id@":`/${o}/user_floor_id`,"parent_scene_record_uuid@":`/${o}/scene_id`,"@column":"uuid;user_id;name;setting_name"},Room_data:{"user_id@":`/${o}/user_room_id`,"parent_scene_record_uuid@":`/${o}/scene_id`,"@column":"uuid;user_id;name;setting_name"}}),u["[]"]=Object.assign(u["[]"],e)}if(d){let e={};if("FORM"!==Ie.dataType&&(e={Scene_record:{"scene_code@":`/${o}/scene_code`,enable:"1","status!":"2","@column":"uuid;name;scene_code"}}),"ROOM"===Ie.code){const a={Building_data:{"user_id@":"/FLOOR/parent_cbid","parent_scene_record_uuid@":"/Scene_record/uuid","@column":"uuid;user_id;name;setting_name;config_camInfo;parent_cbid"},Floor_data:{"user_id@":"/ROOM/parent_cbid","parent_scene_record_uuid@":"/Scene_record/uuid","@column":"uuid;user_id;name;setting_name;config_camInfo;parent_cbid"},Room_data:{"user_id@":"/ROOM/user_id","parent_scene_record_uuid@":"/Scene_record/uuid","@column":"uuid;user_id;name;setting_name;config_camInfo;parent_cbid"}};e=Object.assign(e,a)}if("FLOOR"===Ie.code){const a={Building_data:{"user_id@":"/FLOOR/parent_cbid","parent_scene_record_uuid@":"/Scene_record/uuid","@column":"uuid;user_id;name;setting_name;config_camInfo;parent_cbid"},Floor_data:{"user_id@":"/FLOOR/user_id","parent_scene_record_uuid@":"/Scene_record/uuid","@column":"uuid;user_id;name;setting_name;config_camInfo;parent_cbid"}};e=Object.assign(e,a)}if("BUILDING"===Ie.code){const a={Building_data:{"user_id@":"/BUILDING/user_id","parent_scene_record_uuid@":"/Scene_record/uuid","@column":"uuid;user_id;name;setting_name;config_camInfo;parent_cbid"}};e=Object.assign(e,a)}if("MENU"===Ie.code){const a={Sys_menu:{"id@":"/MENU/menu_id","@column":"id;name"}};e=Object.assign(e,a)}if("MAP_LEVEL"===Ie.code||"MAP_VECTOR_DATA"===Ie.code){const a={Map_level_data:{"id@":`/${Ie.code}/map_level_id`,"@column":"id;name"}};e=Object.assign(e,a)}u["[]"]=Object.assign(u["[]"],e)}if(d){let e=[],i=[];if(ve.Building_data){const t={"[]":{BUILDING:{user_id$:`%${ve.Building_data}%`,"@order":"create_time-,uuid-"}},"total@":"/[]/total","info@":"/[]/info"};if(e=null==(a=(await R(t))["[]"])?void 0:a.map((e=>e.BUILDING)).map((e=>e.user_id)),!e)return be.value=[],void(fe.value=!1);u["[]"].FLOOR["parent_cbid{}"]=e}if(ve.Floor_data){const a={"[]":{FLOOR:{user_id$:`%${ve.Floor_data}%`,"@order":"create_time-,uuid-"}},"total@":"/[]/total","info@":"/[]/info"};ve.Building_data&&(a["[]"].FLOOR["parent_cbid{}"]=e||[]);if(i=null==(t=(await R(a))["[]"])?void 0:t.map((e=>e.FLOOR)).map((e=>e.user_id)),!i)return be.value=[],void(fe.value=!1);u["[]"].ROOM["parent_cbid{}"]=i}}R(u).then((async e=>{var a;if(200===e.code)if(e["[]"]){ze.value.total=(null==(a=e.info)?void 0:a.total)||0;let t=e["[]"];t=await Promise.all(t.map((async e=>{let a=e[`${o}`].user_id,t=e[`${o}`].user_building_id,i="",l="";if(e.Building_data){const a=e.Building_data.setting_name?e.Building_data.setting_name:e.Building_data.name;t=d?`${e.Building_data.user_id}`:`${a}[${e.Building_data.user_id}]`,i=a,l=e.Building_data.uuid}else e.BUILDING&&(t=`[${e.BUILDING.user_id}]`,i="",l=e.BUILDING.uuid);let s,n,r=e[`${o}`].user_floor_id;if(e.Floor_data){const a=e.Floor_data.setting_name?e.Floor_data.setting_name:e.Floor_data.name;r=d?e.Floor_data.user_id:`${a}[${e.Floor_data.user_id}]`,i=a,l=e.Floor_data.uuid}else e.FLOOR&&(r=`[${e.FLOOR.user_id}]`,i="",l=e.FLOOR.uuid);e.Map_level_data&&(s=e.Map_level_data.name),e.Sys_menu&&(n=e.Sys_menu.name);let u=e[`${o}`].user_room_id;if(e.Room_data){const a=e.Room_data.setting_name?e.Room_data.setting_name:e.Room_data.name;u=`${a}[${e.Room_data.user_id}]`,i=a,l=e.Room_data.uuid}const c={...e[o],sceneName:e.Scene_record?e.Scene_record.name:e[`${o}`].scene_id,sceneDataName:a,buildingName:t,floorName:r,roomName:u,mapLevelName:s,menuName:n,originName:i,originId:l};return xe.value.some((e=>e.code===c.data_source&&(c.data_source_source=c.data_source,c.data_source=e.value,!0))),c.iconStatus=c.gcj02_position||c.wgs84_position,c.create_time=c.create_time.replace("T"," "),c.update_time=c.update_time.replace("T"," "),c.create_user=await $e(c.create_user),c.update_user=await $e(c.update_user),c.current_level=ge[c.current_level],c}))),be.value=t||[]}else be.value=[];else be.value=[],F("warning",e.msg);fe.value=!1}))},Ge=(e,a)=>{ze.value=Object.assign(ze.value,{current:e,pageSize:a}),Pe()},Ue=o({keys:[],list:[]}),Je={onChange:(e,a)=>{Ue.value.keys=e,Ue.value.list=a},getCheckboxProps:e=>({disabled:e.groupId===_e})},He=o({}),We=(e,a,t)=>{let i={};t.order&&(i={sortField:t.field,sortRule:"descend"===t.order?"DESC":"ASC"}),He.value=i,Pe(i)},qe=o(),Ke=o([]),Xe=o([]),Ye=o([]),Qe=o([]),Ze=()=>{let e=[{code:"uuid",value:"唯一标识"},{code:"data_source",value:"点位数据来源"},{code:"scene_id",value:"所属场景"},{code:"current_level",value:"所在层级"},{code:"user_building_id",value:"所属建筑"},{code:"user_floor_id",value:"所属楼层"},{code:"user_room_id",value:"所属房间"},{code:"wgs84_position",value:"WGS84坐标系"},{code:"gcj02_position",value:"GCJ02火星坐标系"},{code:"gis_height",value:"离地高度"},{code:"parent_user_id",value:"所在层级ID"},{code:"position_body",value:"点位数据信息"},{code:"position",value:"位置坐标"},{code:"create_time",value:"创建时间"},{code:"create_user",value:"创建人"},{code:"update_time",value:"更新时间"},{code:"update_user",value:"更新人"}].map((e=>({dataIndex:e.code,title:e.value})));const a=JSON.parse(Ie.readStructure?Ie.readStructure:"{}"),t=Ie.form?JSON.parse(Ie.form):{list:[]};we=t.list,ea(t.list);let i=["wgs84_position","gcj02_position","position_body","parent_user_id","position"];if("MAP"===Ie.level){const e=["scene_id","current_level","user_building_id","user_floor_id","user_room_id"];i=[...i,...e]}if(Ie.groupId===_e){const e=["data_source","scene_id","current_level","user_building_id","user_floor_id","user_room_id","gis_height"];i=[...i,...e]}if("FORM"===Ie.dataType){const e=["data_source","scene_id","current_level","user_building_id","user_floor_id","user_room_id","gis_height"];i=[...i,...e]}const o=e.map((e=>e.dataIndex)),d=(null==a?void 0:a.column)?null==a?void 0:a.column.filter((e=>!o.includes(e.name))).map((e=>({dataIndex:e.name,title:e.comment}))):[],l=e.findIndex((e=>"create_time"===e.dataIndex));-1!==l&&e.splice(l,0,...d),e=e.filter((e=>!i.includes(e.dataIndex))),"ROOM"===Ie.code&&e.splice(2,0,{dataIndex:"user_building_id",title:"所属建筑"},{dataIndex:"user_floor_id",title:"所属楼层"}),"FLOOR"===Ie.code&&e.splice(2,0,{dataIndex:"user_building_id",title:"所属建筑"}),"MAP_LEVEL"!==Ie.code&&"MAP_VECTOR_DATA"!==Ie.code||e.splice(1,0,{dataIndex:"cb_mapLevel_name",title:"地图层级名称"}),"MENU"===Ie.code&&e.splice(1,0,{dataIndex:"cb_menu_name",title:"菜单名称"});const s=Ie.groupId===_e;e.forEach((e=>{const a={scene_id:"sceneName",user_building_id:"buildingName",user_floor_id:"floorName",user_room_id:"roomName",scene_code:"sceneName",user_id:"sceneDataName",cb_mapLevel_name:"mapLevelName",cb_menu_name:"menuName"};"scene_code"===e.dataIndex&&(e.title="所属场景"),"user_id"===e.dataIndex&&(e.title="名称"),e.dataIndex=a[e.dataIndex]?a[e.dataIndex]:e.dataIndex;const i=s&&("buildingName"===e.dataIndex||"floorName"===e.dataIndex||"roomName"===e.dataIndex);e.sorter=!i,e.sortDirections=["descend","ascend"],e.width=180,e.ellipsis=!0,t.list.forEach((a=>{if(a.model===e.dataIndex){if("uploadFile"===a.type){const t=`fileDownload-${a.model}`;e.key=`fileDownload-${a.model}`,Ke.value.push(t)}if("releative"===a.type){const t=`releative-${a.model}`;e.key=`releative-${a.model}`,Xe.value.push(t)}if("editor"===a.type){const t=`editor-${a.model}`;e.key=t,Ye.value.push(t)}if("select"===a.type||"checkbox"===a.type){const t=`selectOrCheckbox-${a.model}`;e.key=t,Qe.value.push(t)}delete e.sorter,delete e.sortDirections}}))}));let n=160;n=s?60:Se.value?160:110,e.push({title:"操作",dataIndex:"action",key:"action",width:n,fixed:"right"}),Se.value&&!pe.checkMapData&&e.splice(1,0,{title:"摆点",dataIndex:"iconStatus",key:"iconStatus",width:160}),ye.value=e;const r=JSON.parse(Ie.form);"OTHER"!==Ie.level&&Ie.groupId!==_e&&r.list.splice(1,0,{type:"number",label:"离地高度(m)",options:{width:"100%",defaultValue:0,min:-1e4,max:1e4,precision:2,step:.05,hidden:!1,disabled:!1,placeholder:"请输入",unique:!1},model:"gis_height",key:"gis_height",help:"",rules:[{required:!1,message:"必填项"}]}),Ne.value=JSON.stringify(r)},ea=e=>{const a=[],t=[];e.forEach((e=>{"radio"!==e.type&&"checkbox"!==e.type&&"select"!==e.type||"1"!==e.options.isFilter||a.push(e),"input"!==e.type&&"date"!==e.type&&"number"!==e.type&&"textarea"!==e.type||"1"!==e.options.isFilter||t.push(e)})),ke.value=a,Oe.value=t},aa=e=>("OTHER"!==Ie.level&&Ie.groupId!==_e&&e.list.splice(1,0,{type:"number",label:"离地高度(m)",options:{width:"100%",defaultValue:0,min:-1e4,max:1e4,precision:2,step:.05,hidden:!1,disabled:!1,placeholder:"请输入",unique:!1},model:"gis_height",key:"gis_height",help:"",rules:[{required:!1,message:"必填项"}]}),JSON.stringify(e)),ta=o(),ia=()=>{if(!Ie.addForm)return void F("warning","没有权限新增数据，请联系管理员");const e=JSON.parse(Ie.addForm),a=aa(e);ta.value.init({},a,{table:Ie.code,dataType:Ie.dataType})},oa=()=>{W.confirm({title:"提示",content:"确定要删除吗 ?",okText:"确定",cancelText:"取消",onOk:()=>{const e=Ie.code;B({[e]:{"uuid{}":Ue.value.keys}}).then((e=>{200===e.code?(F("success","孪生体数据删除成功"),Pe(),me("refreshMap")):F("error",`删除失败：${e.msg}`)})),Ue.value.keys=[]}})},da=o(!1),la=()=>{da.value=!0;const e={};ve.data_source&&(e.data_source=ve.data_source),Oe.value.forEach((a=>{ve[a.model]&&(e[`${a.model}`]=`${ve[a.model]}`)})),ke.value.forEach((a=>{ve[a.model]&&(e[`${a.model}`]=`${ve[a.model]}`)})),$({id:Ie.id,dataIds:Ue.value.keys.join(","),idFlag:!0,keyword:ve.searchValue,sceneId:ve.scene_ids,matchJson:e,roomName:ve.Room_data,floorName:ve.Floor_data,buildingName:ve.Building_data}).then((e=>{ze.value.total>1e4&&0===Ue.value.keys.length?F("warning","需导出的数据量较大 系统正努力下载中"):(da.value=!1,M(e))})).finally((()=>{da.value=!1}))},sa=o(),na=()=>{sa.value.init(Ie)},ra=i({title:"",show:!1,content:""}),ua=o();return a({init:async e=>{Ie=e,Se.value="MAP"===e.level,await(async()=>{try{const e=await T({id:1,pageSize:100});e.data&&Fe.setBatch(e.data)}catch(e){}})(),Se.value?(xe.value=[{code:"TWIN",value:"孪生对象摆点"},{code:"ASSET_IMPORT",value:"资产数据导入"},{code:"API_PUSH",value:"接口数据推送"},{code:"ASSET",value:"资产数据摆点"}],Re.value=[{key:"wgs84_position",name:"WGS84坐标系"},{key:"gcj02_position",name:"GCJ02坐标系"},{key:"gis_height",name:"离地高度"},{key:"position_body",name:"点位数据信息"}]):(xe.value=[{code:"TWIN",value:"孪生对象摆点"},{code:"ASSET",value:"资产数据摆点"},{code:"ASSET_IMPORT",value:"资产数据导入"},{code:"CAD",value:"CAD点位转换"},{code:"MMD",value:"模模搭点位转换"},{code:"GIS",value:"GIS点位转换"}],Re.value=[{key:"current_level",name:"所在层级"},{key:"buildingName",name:"所属建筑"},{key:"floorName",name:"所属楼层"},{key:"roomName",name:"所属房间"},{key:"parent_user_id",name:"场景层级中的物体ID"},{key:"wgs84_position",name:"WGS84坐标系"},{key:"gcj02_position",name:"GCJ02坐标系"},{key:"gis_height",name:"离地高度"},{key:"position_body",name:"点位数据信息"},{key:"position",name:"位置坐标"}],x().then((e=>{je.value=e.data}))),"FORM"===e.dataType&&(Re.value=[]),Ze(),Pe()},getData:Pe}),(a,t)=>{const i=z,o=P,N=h("upload-outlined"),S=h("download-outlined"),j=G,x=h("DeleteOutlined"),R=J,$=U,M=H,T=W;return w(),d(l,null,[s("div",X,[s("div",Y,[s("div",Q,[s("div",Z,[t[6]||(t[6]=s("span",{class:"search-label"},"关键词",-1)),n(i,{value:ve.searchValue,"onUpdate:value":t[0]||(t[0]=e=>ve.searchValue=e),"allow-clear":"",placeholder:"请输入设备编码",class:"search-input",onKeyup:t[1]||(t[1]=r((e=>Ve()),["enter"]))},null,8,["value"])]),s("div",ee,[n(o,{type:"primary",class:"search-btn",onClick:t[2]||(t[2]=e=>Ve())},{default:u((()=>t[7]||(t[7]=[c("查询")]))),_:1}),n(o,{class:"search-btn",onClick:t[3]||(t[3]=e=>(ve.searchValue="",ze.value.current=1,ze.value.pageSize=10,void qe.value.clear()))},{default:u((()=>t[8]||(t[8]=[c("重置")]))),_:1}),n(D,{ref_key:"conditionBoxRef",ref:qe,"assembly-filter-group":Le,onCallBack:Ee},null,512)])]),s("div",ae,[!Se.value&&a.hasPerm("twin-class:add")?(w(),_(o,{key:0,type:"primary",disabled:p(Ie).groupId===_e,class:"handle-btn",onClick:ia},{default:u((()=>t[9]||(t[9]=[c(" 新增孪生体 ")]))),_:1},8,["disabled"])):m("",!0),s("div",null,[a.hasPerm("twin-body-data:import-excel")?(w(),_(o,{key:0,class:"handle-btn",onClick:na},{default:u((()=>[n(N),t[10]||(t[10]=c("导入"))])),_:1})):m("",!0),n(j,{placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:la},{title:u((()=>t[11]||(t[11]=[s("p",null,"该操作将对选中的孪生体数据进行下载",-1),s("p",null,"若未选中，将下载全部，是否继续？",-1)]))),default:u((()=>[a.hasPerm("twin-body-data:export-excel")?(w(),_(o,{key:0,class:"handle-btn",loading:da.value},{default:u((()=>[n(S),t[12]||(t[12]=c("导出"))])),_:1},8,["loading"])):m("",!0)])),_:1}),a.hasPerm("twin-body-data:delete-twin-body-point-info")?(w(),_(o,{key:1,class:"handle-btn",disabled:Ue.value.keys.length<=0,onClick:oa},{icon:u((()=>[n(x)])),default:u((()=>[t[13]||(t[13]=c(" 批量删除 "))])),_:1},8,["disabled"])):m("",!0)])])]),s("div",te,[s("div",{ref_key:"table",ref:De,class:"table-content"},[n($,{class:"table",scroll:{x:"max-content",y:"calc(100% - 40px)"},"custom-row":Ce,pagination:!1,size:"small",loading:fe.value,"row-selection":Je,"row-key":e=>e.uuid,columns:ye.value,"data-source":be.value,onChange:We},v({bodyCell:u((({text:i,column:o,record:r})=>["action"===o.dataIndex?(w(),d("div",ie,[a.hasPerm("twin-class:modify-form")?(w(),d("a",{key:0,onClick:b((e=>(e=>{if(!Ie.editForm)return void F("warning","没有权限操作该数据，请联系管理员");const a=JSON.parse(Ie.editForm),t=aa(a);ta.value.init(e,t,{table:Ie.code,dataType:Ie.dataType})})(r)),["stop"])},"更新",8,oe)):m("",!0),p(Ie).groupId!==_e||"FORM"===p(Ie).dataType?(w(),_(j,{key:1,placement:"topRight",title:"确认删除？",onConfirm:()=>(e=>{const a=Ie.code;B({[a]:{"uuid{}":e}}).then((a=>{var t,i;if(200===a.code){F("success","孪生体数据删除成功"),Pe(),me("refreshMap");const a=null==(t=window.app)?void 0:t.query(`#${e[0]}`)[0];a&&((null==(i=window.mapPlacementIns.twinObject)?void 0:i.id)===a.id&&window.mapPlacementIns.reset(),a.destroy())}else F("error",`删除失败：${a.msg}`)}))})([r.uuid])},{default:u((()=>[a.hasPerm("twin-body-data:delete-twin-body-point-info")?(w(),d("a",{key:0,onClick:t[4]||(t[4]=b((()=>{}),["stop"]))},"删除")):m("",!0)])),_:2},1032,["onConfirm"])):m("",!0),y(s("a",{onClick:b((e=>(e=>{var a,t,i;const o=null==(a=window.app)?void 0:a.query(`#${e.uuid}`)[0];if(o)if(1===q.thingjsVersion)null==(t=window.app)||t.camera.earthFlyTo({object:o,pitch:45,height:600});else if(2===q.thingjsVersion){const e=o.position?THING.EARTH.Utils.convertWorldToLonlat(o.position):o.coordinates[0][0],[a,t]=e;null==(i=window.app)||i.camera.earthFlyTo({lonlat:[a,t],height:600})}})(r)),["stop"])},[r.wgs84_position||r.gcj02_position?(w(),d("span",le," 定位 ")):(w(),d("span",se,[n(R,{placement:"topLeft",title:"请先在地图上对孪生体进行摆点，再进行定位操作"},{default:u((()=>t[14]||(t[14]=[c(" 定位 ")]))),_:1})]))],8,de),[[k,Se.value&&!e.checkMapData]])])):m("",!0),"iconStatus"===o.dataIndex?(w(),d(l,{key:1},[s("span",{style:O([{display:"inline-block",width:"10px",height:"10px","margin-right":"8px","border-radius":"5px"},{backgroundColor:i?"#1DBE53":"#C3C4C7"}])},null,4),s("span",null,f(i?"已标注":"未标注"),1)],64)):m("",!0),(w(!0),d(l,null,g(Qe.value,(e=>(w(),d(l,null,[e===o.key?(w(),d("span",{key:e},f(Me(i)),1)):m("",!0)],64)))),256)),(w(!0),d(l,null,g(Ye.value,(e=>(w(),d(l,null,[e===o.key?(w(),d("a",{key:e,onClick:e=>(e=>{ra.title="预览",ra.show=!0,ra.content=e})(i)},"详情",8,ne)):m("",!0)],64)))),256)),(w(!0),d(l,null,g(Ke.value,(e=>(w(),d(l,null,[e===o.key?(w(),_(A,{key:e,item:e,record:r,text:i},null,8,["item","record","text"])):m("",!0)],64)))),256)),(w(!0),d(l,null,g(Xe.value,(e=>(w(),d(l,null,[e===o.key?(w(),d(l,{key:0},[he(r,o.dataIndex)?(w(),d("a",{key:e,onClick:e=>((e,a)=>{try{JSON.parse(e[a.dataIndex]).length&&ua.value.init(JSON.parse(e[a.dataIndex]),a.dataIndex,Ie)}catch(t){}})(r,o)},"详情",8,re)):(w(),d("span",{key:e+"1"},"--"))],64)):m("",!0)],64)))),256))])),_:2},[p(Ie).groupId!==_e&&"FORM"!==p(Ie).dataType?{name:"expandedRowRender",fn:u((({record:e})=>[(w(!0),d(l,null,g(Re.value,((a,t)=>(w(),d("span",{key:t,style:{display:"block"}},f(a.name)+":"+f(e[a.key]),1)))),128))])),key:"0"}:void 0]),1032,["loading","row-key","columns","data-source"]),s("div",ue,[be.value.length>0?(w(),_(M,I({key:0},ze.value,{onChange:Ge}),null,16)):m("",!0)])],512)])]),n(L,{ref_key:"addEditFormRef",ref:ta,onSuccess:Te},null,512),n(E,{ref_key:"uploadRef",ref:sa,onOk:Be},null,512),n(T,{title:ra.title,"body-style":{maxHeight:"80vh",paddingBottom:"40px",overflowY:"auto",overflowX:"hidden"},"wrap-class-name":"cus-modal",footer:null,width:900,open:ra.show,"mask-closable":!1,onCancel:t[5]||(t[5]=e=>ra.show=!1)},{default:u((()=>[s("div",{class:"editor-html",innerHTML:ra.content},null,8,ce)])),_:1},8,["title","open"]),n(V,{ref_key:"reletiveRef",ref:ua},null,512)],64)}}}),[["__scopeId","data-v-bd588ef1"]]);export{me as default};
