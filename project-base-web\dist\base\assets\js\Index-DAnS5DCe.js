import{d as e,r as a,a as t,j as s,V as i,U as l,bJ as o,am as r,c as n,bL as d,G as c,S as p,al as u,bk as m,W as v,n as h}from"./@vue-DgI1lw0Y.js";import{a as j,g as y,u as g,d as k}from"./addAttrMap-CQMumYiu.js";import{u as b}from"./main-DE7o6g98.js";import{I as f,B as w,_ as x,i as _,j as C,W as I,b as S,M as z}from"./ant-design-vue-DW0D0Hn-.js";import{_ as N}from"./vue-qr-6l_NUpj8.js";import"./codemirror-editor-vue3-CQ6xaWU-.js";import"./@babel-B4rXMRun.js";import"./diff-match-patch-jtm_x_zs.js";import"./editReturnJson-DTys2U5n.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const E={class:"rel-build"},R={class:"search-wrap"},M={class:"search-content"},T={class:"expand-content"},U={class:"search-item"},A={class:"search-btns"},J={class:"table-handle"},O={class:"table-wrap"},L={class:"table-content"},D={key:0},V={key:2},W={key:3},Y={key:4},F={key:5},H={key:6,class:"table-actions"},P=["onClick"],X={class:"pagination"},G=N(e({__name:"Index",setup(e,{expose:N}){const G=a(!1),K=a(!1),Q=a(),$=a([{id:"001",task:"任务名称",enable:!0,updateMethod:"全量更新",creater:"XXX",createTime:"2025-07-10"}]),q=t({keyword:"",pageNo:1,pageSize:10}),B=a(0),Z={INCREMENTAL_UPDATE:"增量更新",FULL_UPDATE:"全量更新"},ee={EVERY_1_DAY:"每天一次",EVERY_1_HOUR:"每小时一次",EVERY_30_MINUTES:"每30分钟一次"},ae=s((()=>({current:q.pageNo,pageSize:q.pageSize,total:B.value,pageSizeOptions:["10","20","50","100"],showTotal:e=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),te=a(null),se=()=>{G.value=!1,K.value=!1},ie=[{title:"任务名称",dataIndex:"taskName",ellipsis:!0,width:180,align:"center"},{title:"启用状态",dataIndex:"enable",sorter:!0,ellipsis:!0,width:100,align:"center"},{title:"更新方式",dataIndex:"updateMethod",width:180,align:"center"},{title:"执行周期",width:180,dataIndex:"executionCycle"},{title:"创建人",dataIndex:"createUser",width:180,align:"center"},{title:"创建时间",dataIndex:"createTime",width:180,align:"center"},{title:"操作",key:"action",width:150,fixed:"right",align:"center"}],le=(e,a)=>{q.pageNo=e,q.pageSize=a,ne()},oe=(e,a,t)=>{let s={};s=t.order?{sortField:t.field,sortRule:"descend"===t.order?"DESC":"ASC"}:{sortField:"",sortRule:""},ne(s)},re=()=>{Q.value.init("add",te.value)},ne=(e={})=>{var a;K.value=!0,$.value=[];const t=JSON.parse(JSON.stringify(q)),s={twinClassId:null==(a=te.value)?void 0:a.id,...t,...e};y(s).then((e=>{if(200===e.code){const{rows:a,pageNo:t,pageSize:s,totalRows:i}=e.data;$.value=a,ae.value.current=t,ae.value.pageSize=s,ae.value.total=i}K.value=!1})).finally((()=>{K.value=!1})).catch((()=>{K.value=!1}))},de=()=>{q.pageNo=1,ne()},ce=()=>{de()};return N({init:async e=>{te.value=e,G.value=!0,h((()=>{ne()}))}}),(e,a)=>{const t=f,s=w,h=C,y=_,N=I,B=x,pe=S,ue=z;return l(),i(ue,{title:"属性映射",width:"100%","body-style":{maxHeight:"100%",overflow:"auto"},footer:null,"wrap-class-name":"cus-modal full-modal",open:G.value,"mask-closable":!1,onCancel:se},{default:o((()=>[r("div",E,[r("div",R,[r("div",M,[r("div",T,[r("div",U,[n(t,{value:q.keyword,"onUpdate:value":a[0]||(a[0]=e=>q.keyword=e),"allow-clear":"",placeholder:"请输入任务名称",class:"search-input",onKeyup:a[1]||(a[1]=d((e=>de()),["enter"]))},null,8,["value"])]),r("div",A,[n(s,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>de())},{default:o((()=>a[4]||(a[4]=[c("搜索")]))),_:1}),n(s,{class:"search-btn",onClick:a[3]||(a[3]=e=>(q.kewWord="",q.pageNo=1,q.pageSize=10,void ne()))},{default:o((()=>a[5]||(a[5]=[c("重置")]))),_:1})])])]),r("div",J,[n(s,{class:"handle-btn",type:"primary",onClick:re},{default:o((()=>a[6]||(a[6]=[c(" 新建")]))),_:1})])]),r("div",O,[r("div",L,[n(B,{class:"table",scroll:{y:"calc(100% - 40px)"},pagination:!1,"row-key":e=>e.id,size:"small",columns:ie,"data-source":$.value,loading:K.value,onChange:oe},{bodyCell:o((({column:e,record:t})=>["task"===e.dataIndex?(l(),p("div",D,m(t.task),1)):u("",!0),"enable"===e.dataIndex?(l(),i(y,{key:1,placement:"top",title:0===t.enable?"确定启用该规则？":"确定停用该规则？",onConfirm:()=>(e=>{const a=1===e.enable?0:1,t={id:e.id,enable:a};g(t).then((e=>{200===e.code&&(b("success","状态修改成功"),ne())}))})(t)},{default:o((()=>[n(h,{"checked-children":"启用","un-checked-children":"停用",checked:1===t.enable},null,8,["checked"])])),_:2},1032,["title","onConfirm"])):u("",!0),"updateMethod"===e.dataIndex?(l(),p("span",V,m(Z[t.updateMethod]),1)):u("",!0),"executionCycle"===e.dataIndex?(l(),p("span",W,m(ee[t.executionCycle]),1)):u("",!0),"creater"===e.dataIndex?(l(),p("span",Y,m(t.creater),1)):u("",!0),"createTime"===e.dataIndex?(l(),p("span",F,m(t.createTime),1)):u("",!0),"action"===e.key?(l(),p("div",H,[r("a",{onClick:e=>(e=>{Q.value.init("edit",te.value,e)})(t)},"编辑",8,P),n(N,{type:"vertical"}),n(y,{placement:"topRight",title:"确定要删除该规则吗？","ok-text":"是","cancel-text":"否",onConfirm:e=>(async e=>{try{200===(await k(e.id)).code&&(b("success","删除成功"),ne())}catch(a){b("error","删除失败")}})(t)},{default:o((()=>a[7]||(a[7]=[r("a",{class:"delete-btn"},"删除",-1)]))),_:2},1032,["onConfirm"])])):u("",!0)])),_:1},8,["row-key","data-source","loading"]),r("div",X,[$.value.length>0?(l(),i(pe,v({key:0},ae.value,{onChange:le}),null,16)):u("",!0)])])]),n(j,{ref_key:"addAttrMapRef",ref:Q,onOk:ce},null,512)])])),_:1},8,["open"])}}}),[["__scopeId","data-v-5470be95"]]);export{G as default};
