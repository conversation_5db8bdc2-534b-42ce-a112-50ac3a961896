import{u as a}from"./main-DE7o6g98.js";import{u as e}from"./useTableScrollY-9oHU_oJI.js";import{B as o,C as s,D as t}from"./mapManag-wSwfWE2D.js";import i from"./AddEditForm-BGMNd_s6.js";import{B as r,i as l,_ as n,b as d}from"./ant-design-vue-DW0D0Hn-.js";import{d as p,a as m,r as c,j as u,o as j,S as g,U as y,am as h,c as k,V as v,al as f,bJ as b,G as w,u as _,W as C}from"./@vue-DgI1lw0Y.js";import{_ as z}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const x={class:"terrain-data"},S={class:"table-wrap"},I={class:"table-handle"},N={key:0,class:"table-actions"},J=["onClick"],L=["onClick"],P={class:"pagination"},R=z(p({__name:"Index",setup(p){let z=m([]);const R=m({pageNo:1,pageSize:10}),O=c(),q=c(0),A=c(),{scrollY:B}=e(O),D=m([{title:"地形名称",dataIndex:"name",key:"name",ellipsis:!0},{title:"坐标系",dataIndex:"coords",key:"coords",ellipsis:!0},{title:"地形地址",dataIndex:"url",key:"url",ellipsis:!0},{title:"操作",key:"action",width:240}]),E=u((()=>({current:R.pageNo,pageSize:R.pageSize,total:q.value,pageSizeOptions:["10","20","50","100"],showTotal:(a,e)=>`共${a}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),F=(a,e)=>{R.pageNo=a,R.pageSize=e,T()},G=m({selectedRowKeys:[],loading:!1,addLoading:!1,delLoading:!1}),K=()=>{R.pageNo=1,T()},T=()=>{G.loading=!0,o(R).then((a=>{G.loading=!1,200===a.code&&(z=a.data.rows||[],q.value=a.data.totalRows)}),(()=>{G.loading=!1}))},W=(a,e)=>{A.value.init(a,e)},Y=()=>{K()};return j((()=>{T()})),(e,o)=>{const p=r,m=l,c=n,u=d;return y(),g("div",x,[h("div",S,[h("div",I,[e.hasPerm("topographic:data:add")?(y(),v(p,{key:0,type:"primary",class:"handle-btn",loading:G.addLoading,onClick:o[0]||(o[0]=a=>W("add",null))},{default:b((()=>o[1]||(o[1]=[w(" 新增地形数据 ")]))),_:1},8,["loading"])):f("",!0)]),h("div",{ref_key:"table",ref:O,class:"table-content"},[k(c,{class:"table",scroll:{y:_(B)},pagination:!1,"row-key":a=>a.id,size:"small",columns:D,loading:G.loading,"data-source":_(z)},{bodyCell:b((({column:i,record:r})=>["action"===i.key?(y(),g("div",N,[e.hasPerm("topographic:data:edit")?(y(),g("a",{key:0,onClick:a=>W("edit",r)},"编辑",8,J)):f("",!0),e.hasPerm("topographic:data:delete")?(y(),v(m,{key:1,placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:e=>(e=>{t(e).then((e=>{200===e.code?(a("success","地形数据删除成功"),K()):a("error","地形数据删除失败")}))})(r)},{title:b((()=>o[2]||(o[2]=[h("p",null,"确定要删除吗?",-1)]))),default:b((()=>[o[3]||(o[3]=h("a",null,"删除",-1))])),_:2},1032,["onConfirm"])):f("",!0),e.hasPerm("topographic:data:change")&&!r.isDefault?(y(),g("a",{key:2,onClick:e=>(e=>{s({id:e.id}).then((e=>{200===e.code?(a("success","默认数据设置成功"),K()):a("error","默认数据设置失败")}))})(r)},"设为默认",8,L)):f("",!0)])):f("",!0)])),_:1},8,["scroll","row-key","columns","loading","data-source"]),h("div",P,[_(z).length>0?(y(),v(u,C({key:0},E.value,{onChange:F}),null,16)):f("",!0)])],512)]),k(i,{ref_key:"addEditFormRef",ref:A,onOk:Y},null,512)])}}}),[["__scopeId","data-v-9dc03106"]]);export{R as default};
