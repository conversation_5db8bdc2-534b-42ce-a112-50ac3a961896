import s from"./Index-CZv0BOMp.js";import i from"./Index-hvctTD4G.js";import{d as o,r as t,S as e,U as r,am as p,F as m,b7 as a,Z as j,bk as n,V as d,al as c}from"./@vue-DgI1lw0Y.js";import{_ as l}from"./vue-qr-6l_NUpj8.js";import"./scene-DnZsitgt.js";import"./qs-Cgg8q2iR.js";import"./@babel-B4rXMRun.js";import"./side-channel-C0354c6F.js";import"./es-errors-Bq3kx8t5.js";import"./object-inspect-7jlLeo80.js";import"./crypto-js-HwOCoVPb.js";import"./side-channel-list-9pNTKFEK.js";import"./side-channel-map-qN4s3zsW.js";import"./get-intrinsic-BQNEepf0.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-z0bYe-bF.js";import"./gopd-uUVnxxv0.js";import"./es-define-property-lN4EXf1s.js";import"./has-symbols-CRskrF1U.js";import"./get-proto-nTH32ZvN.js";import"./dunder-proto-W0bZB-Yu.js";import"./call-bind-apply-helpers-CohWVzbO.js";import"./function-bind-Ckw9YnhN.js";import"./hasown-Dpzr2-IR.js";import"./call-bound-BVGeUV4S.js";import"./side-channel-weakmap-dRphfAXb.js";import"./main-DE7o6g98.js";import"./ant-design-vue-DW0D0Hn-.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./dayjs-D9wJ8dSB.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";/* empty css                      */import"./UploadMainScene-snQmlwnc.js";import"./RepeatUuid-GduIXTNN.js";import"./js-binary-schema-parser-G48GG52R.js";import"./SceneTree-D2nACbTQ.js";import"./MainSceneVersion-DcOjMBqv.js";import"./ChildSceneVersion-DjNq03DC.js";import"./UploadChildScene-BkRnjWLa.js";import"./CheckOtherScene-DlVFF30m.js";import"./CustomProperties-BVpIegYD.js";import"./@ti-cli-Z3vwStYr.js";import"./vue-CqsM5HEV.js";import"./effectsPack-CMixYyev.js";import"./AddAndEdit-C1hEz4Cs.js";import"./Version-l2p5rk3P.js";const u={class:"thingjs-campus"},v={class:"third-menu"},h={class:"third-menu-wrap"},y={class:"menu-content"},b=["onClick"],f={class:"name"},k={class:"content"},g=l(o({__name:"Index",setup(o){const l=t([{id:1,name:"场景包"},{id:2,name:"效果包"}]),g=t(1);return(o,t)=>(r(),e("div",u,[p("div",v,[p("div",h,[p("div",y,[(r(!0),e(m,null,a(l.value,(s=>(r(),e("div",{key:s.id,class:j(["content-item",s.id===g.value?"active":""]),onClick:i=>(s=>{g.value!==s.id&&(g.value=s.id)})(s)},[p("span",f,n(s.name),1)],10,b)))),128))])])]),p("div",k,[1===g.value?(r(),d(s,{key:0})):c("",!0),2===g.value?(r(),d(i,{key:1})):c("",!0)])]))}}),[["__scopeId","data-v-25388a62"]]);export{g as default};
