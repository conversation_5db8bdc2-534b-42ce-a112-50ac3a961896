import{d as e,r as a,a as s,o as t,S as o,U as i,am as n,c as l,bL as r,bJ as c,G as m,V as p,al as d,F as u,b7 as v,q as h,B as j,u as g,bk as b}from"./@vue-DgI1lw0Y.js";import{d as f}from"./dayjs-D9wJ8dSB.js";import{p as k,u as w}from"./useCookies-DaNNZ0x-.js";import y from"./Nodata-CooGNjR3.js";import{b as _}from"./developConfig-CkJLRJ2N.js";import{d as C,f as $,g as z}from"./visualScreen-ChSISTOu.js";import S from"./ChangeName-C9DzryPE.js";import{u as x}from"./main-DE7o6g98.js";import H from"./UploadFile-DqNDfknJ.js";import{I as N,B as P,i as Y,S as D,b as E}from"./ant-design-vue-DW0D0Hn-.js";import{_ as I}from"./vue-qr-6l_NUpj8.js";import"./@babel-B4rXMRun.js";import"./vue3-cookies-ll55Epyr.js";import"./axios-ChCdAMPF.js";import"./no-data-DWJyvDuH.js";import"./js-binary-schema-parser-G48GG52R.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";const R={class:"screen-templete"},U={class:"search-wrap"},M={class:"search-content"},F={class:"search-item"},G={class:"search-btns"},K={class:"table-handle"},O={class:"content-wrap"},T={class:"content-list"},q={key:0,class:"list"},B={class:"contain"},J={class:"img-box"},L=["src"],W={class:"bottom-wrapper"},V={class:"bottom-content"},A={key:0,class:"time"},Q={key:1,class:"time"},X={class:"hover-box"},Z=["onClick"],ee=["onClick"],ae={key:0,class:"btn"},se={class:"item-bottom"},te={class:"title"},oe={class:"name"},ie=["title"],ne={key:1,class:"list"},le={key:2,class:"pagination-box"},re=I(e({__name:"Index",setup(e){const I=a(!1);a([]);const re=a([]),ce=s({total:0,current:1,pageSize:12,name:""}),me=a("");_({code:"SCREEN_UI_IP"}).then((e=>{var a,s;const t=(null==(s=null==(a=e.data)?void 0:a.rows[0])?void 0:s.value)||"";me.value=t})).catch((()=>{me.value=""}));const pe=()=>{ce.current=1,ce.pageSize=12,ue()},de=(e,a)=>{ce.current=e,ce.pageSize=a,ue()},ue=()=>{I.value=!0;const e={pageNo:ce.current,pageSize:ce.pageSize,name:ce.name};C(e).then((e=>{I.value=!1,ce.total=e.totalRows||0,re.value=e.rows||[]})).catch((()=>{I.value=!1}))};t((()=>{ue()}));const ve=a(),he=()=>{ve.value.init()},je=async()=>{const e=await z();if(200!==e.code)return void x("error",e.message);x("success","大屏新建成功");const{id:a}=e.data;w(),window.open(`${me.value}/kunpeng/editor/${a}`,"_blank"),ue()},ge=a(),be=e=>me.value?`${window.baseConfig.hostUrl}${e.snapShot}`:`${window.baseConfig.hostUrl}${e.filePath}${e.snapShot}`,fe=()=>{ue()};return(e,a)=>{const s=N,t=P,_=Y,C=D,z=E;return i(),o("div",R,[n("div",U,[n("div",M,[n("div",F,[a[5]||(a[5]=n("span",{class:"search-label"},"大屏名称",-1)),l(s,{value:ce.name,"onUpdate:value":a[0]||(a[0]=e=>ce.name=e),"allow-clear":"",placeholder:"请输入大屏名称",class:"search-input",onKeyup:a[1]||(a[1]=r((e=>pe()),["enter"]))},null,8,["value"])]),n("div",G,[l(t,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>pe())},{default:c((()=>a[6]||(a[6]=[m(" 查询 ")]))),_:1}),l(t,{class:"search-btn",onClick:a[3]||(a[3]=e=>(ce.name="",void pe()))},{default:c((()=>a[7]||(a[7]=[m(" 重置 ")]))),_:1})])]),n("div",K,[e.hasPerm("annotate-component:create-base")&&me.value?(i(),p(t,{key:0,type:"primary",style:{"margin-right":"10px"},onClick:je},{default:c((()=>a[8]||(a[8]=[m("新建大屏")]))),_:1})):d("",!0),e.hasPerm("annotate-component:upload-base")?(i(),p(t,{key:1,type:"primary",onClick:he},{default:c((()=>a[9]||(a[9]=[m("导入大屏")]))),_:1})):d("",!0)])]),n("div",O,[n("div",T,[e.hasPerm("annotate-component:page-base")?(i(),o("div",q,[(i(!0),o(u,null,v(re.value,(s=>h((i(),o("div",{key:s.id,class:"item"},[n("div",B,[n("div",J,[n("img",{src:be(s),alt:"图片",class:"img",onError:a[4]||(a[4]=e=>e.target.src=g(k))},null,40,L),n("div",W,[n("div",V,[me.value?(i(),o("div",A,"创建时间："+b(g(f)(s.createTime).format("YYYY-MM-DD HH:mm:ss")),1)):(i(),o("div",Q,"上传时间："+b(g(f)(s.createTime).format("YYYY-MM-DD HH:mm:ss")),1))])]),n("div",X,[n("div",{class:"btn",onClick:e=>(e=>{if(me.value){w();const a=`${me.value}/kunpeng/preview/sandbox/${e.id}`;window.open(a,"_blank",`width=${window.innerWidth},height=${window.innerHeight-100},top=100,left=100,z-look=yes`)}else window.open("_blank").location=`/base/screen/index.html?path=${e.filePath}`})(s)},"预览",8,Z),e.hasPerm("KSHDP:SCREEN-EDIT")&&me.value?(i(),o("div",{key:0,class:"btn perview",onClick:e=>(e=>{w();const a=`${me.value}/kunpeng/editor/${e.id}`;window.open(a,"_blank")})(s)},"编辑",8,ee)):d("",!0),l(_,{placement:"topRight",title:"确认删除？",onConfirm:e=>(async e=>{200===(await $(e.id)).code&&(x("success","大屏删除成功"),ue())})(s)},{default:c((()=>[e.hasPerm("annotate-component:delete-base")?(i(),o("div",ae,"删除")):d("",!0)])),_:2},1032,["onConfirm"])])]),n("div",se,[n("div",te,[n("div",oe,[n("span",{class:"name-val",title:s.componentName},b(s.componentName),9,ie)])])])])])),[[j,!I.value&&re.value.length]]))),128)),I.value?(i(),p(C,{key:0,class:"loading-icon",spinning:I.value},null,8,["spinning"])):d("",!0),I.value||re.value.length?d("",!0):(i(),p(y,{key:1}))])):(i(),o("div",ne,[l(y,{title:"暂无权限"})])),re.value.length?(i(),o("div",le,[l(z,{total:ce.total,"page-size-options":["12","20","30","40"],current:ce.current,"page-size":ce.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:de},null,8,["total","current","page-size","show-total"])])):d("",!0)])]),l(H,{ref_key:"uploadFileRef",ref:ve,onOk:fe},null,512),l(S,{ref_key:"changeNameRef",ref:ge,onOk:fe},null,512)])}}}),[["__scopeId","data-v-4bfe2fb4"]]);export{re as default};
