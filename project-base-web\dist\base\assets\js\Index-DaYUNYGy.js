import{u as s}from"./main-DE7o6g98.js";import{A as e,g as a,d as t,c as o,r as i}from"./AddOrEdit-BKVaqXww.js";import{u as r}from"./useTableScrollY-9oHU_oJI.js";import{B as l,i as n,j as m,_ as c,b as d}from"./ant-design-vue-DW0D0Hn-.js";import{d as p,a as u,r as j,j as g,o as h,S as v,U as f,am as y,c as k,bJ as b,G as _,V as w,al as z,u as x,W as C}from"./@vue-DgI1lw0Y.js";import{_ as S}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const J={class:"tidixJar"},N={class:"table-wrap"},O={class:"table-handle"},A={key:1,class:"table-actions"},I={class:"pagination"},$=S(p({__name:"Index",setup(p){let S=u([]);const $=u({pageNo:1,pageSize:10}),R=j(),E=j(0),F=j(),{scrollY:G}=r(R),T=u([{title:"jar包名",dataIndex:"name",key:"name",ellipsis:!0},{title:"状态",dataIndex:"status",key:"status",ellipsis:!0},{title:"操作",key:"action",width:240}]),W=g((()=>({current:$.pageNo,pageSize:$.pageSize,total:E.value,pageSizeOptions:["10","20","50","100"],showTotal:(s,e)=>`共${s}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),Y=(s,e)=>{$.pageNo=s,$.pageSize=e,H()},q=u({loading:!1}),B=()=>{$.pageNo=1,H()},H=()=>{q.loading=!0,a($).then((s=>{q.loading=!1,200===s.code&&(S=s.data.records||[],E.value=s.data.totalRecord)}),(()=>{q.loading=!1}))},K=()=>{var s;null==(s=F.value)||s.init()},L=()=>{B()};h((()=>{H()}));const Q=()=>{i().then((e=>{200===e.code?(s("success","重启成功"),B()):s("error",e.message)}))};return(a,i)=>{const r=l,p=m,u=n,j=c,g=d;return f(),v("div",J,[y("div",N,[y("div",O,[k(r,{class:"handle-btn",onClick:Q},{default:b((()=>i[0]||(i[0]=[_(" 重启 ")]))),_:1}),k(r,{type:"primary",class:"handle-btn",onClick:K},{default:b((()=>i[1]||(i[1]=[_(" 新增Jar包 ")]))),_:1})]),y("div",{ref_key:"table",ref:R,class:"table-content"},[k(j,{class:"table",scroll:{y:x(G)},pagination:!1,"row-key":s=>s.name,size:"small",columns:T,loading:q.loading,"data-source":x(S)},{bodyCell:b((({column:e,record:a})=>["status"===e.dataIndex?(f(),w(u,{key:0,placement:"top",title:"已启用"===a.status?"确定停用该Jar包？":"确定启用该Jar包？",onConfirm:()=>(e=>{o({name:e.name,status:"已启用"===e.status?"OFF":"ON"}).then((a=>{200===a.code?(s("success",`${"已启用"==`${e.status}`?"启用":"禁用"}${e.name}成功`),B()):s("error",a.message)}))})(a)},{default:b((()=>[k(p,{"checked-children":"启用","un-checked-children":"停用",checked:"已启用"===a.status},null,8,["checked"])])),_:2},1032,["title","onConfirm"])):z("",!0),"action"===e.key?(f(),v("div",A,[k(u,{placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:e=>(e=>{"已启用"!==e.status?t({name:e.name}).then((e=>{200===e.code?(s("success","删除成功"),B()):s("error","删除失败")})):s("warning","已启用的jar包不能删除")})(a)},{title:b((()=>i[2]||(i[2]=[y("p",null,"确定要删除吗?",-1)]))),default:b((()=>[i[3]||(i[3]=y("a",null,"删除",-1))])),_:2},1032,["onConfirm"])])):z("",!0)])),_:1},8,["scroll","row-key","columns","loading","data-source"]),y("div",I,[x(S).length>0?(f(),w(g,C({key:0},W.value,{onChange:Y}),null,16)):z("",!0)])],512)]),k(e,{ref_key:"AddAndEditRef",ref:F,onOk:L},null,512)])}}}),[["__scopeId","data-v-4960a9fa"]]);export{$ as default};
