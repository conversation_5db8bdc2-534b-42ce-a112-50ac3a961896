import{d as e,r as a,o as s,b9 as l,v as t,S as i,U as o,F as c,am as n,c as r,V as u,bL as d,bJ as p,G as m,al as v,bk as h,q as f,u as y,b7 as g,Z as k}from"./@vue-DgI1lw0Y.js";import{d as j}from"./dayjs-D9wJ8dSB.js";import{d as b,a as _}from"./nodata-dark-DHz_m8dv.js";import w from"./Detail-4l1XJAkN.js";import{t as C,b as x,c as E,D as z,f as S}from"./model-BUBDdBL2.js";import{a as O,u as U}from"./main-DE7o6g98.js";import{_ as P}from"./AddOrEdit.vue_vue_type_style_index_0_lang-B0JB0YoY.js";import{_ as D}from"./UploadModal.vue_vue_type_script_setup_true_lang-BRboDosL.js";import{a as I}from"./axios-ChCdAMPF.js";import{I as L,g as N,h as M,B as $,i as R,S as Y,r as G,q as T,b as Z,M as q}from"./ant-design-vue-DW0D0Hn-.js";import{_ as A}from"./vue-qr-6l_NUpj8.js";import"./@babel-B4rXMRun.js";import"./clipboard-Dv7Qpqbb.js";import"./vue-pick-colors-CjUSS-xa.js";import"./@popperjs-CFrBDmKk.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./basicModelTip4-CPNhDiAx.js";import"./js-binary-schema-parser-G48GG52R.js";const F={class:"public-model"},J={class:"search-wrap"},K={class:"search-content"},W={class:"search-item"},B={class:"search-item"},H={class:"search-btns"},V={class:"table-handle"},Q={key:0,class:"checkinfo"},X={class:"number"},ee={key:0,class:"list",style:{"place-content":"center center"}},ae={key:1,class:"list"},se={class:"contain"},le=["src"],te={class:"cus-check"},ie={class:"hover-box"},oe={class:"cus-check"},ce=["disabled","onClick"],ne=["onClick"],re=["onClick"],ue={class:"item-top"},de={class:"item-name"},pe={class:"item-file"},me={class:"item-bottom"},ve={class:"item-type"},he={class:"pagination-box"},fe=A(e({__name:"Index",setup(e,{expose:A}){const fe=O(),ye=a({title:"",fileExist:"",classify:"/",modelType:1,pageNo:1,pageSize:12}),ge=a(0),ke=a(!1),je=a([]),be=a([]);s((()=>{Ue()}));const _e=e=>{const a=e.srcElement;a.src="light"===fe.modeName?b:_,a.onerror=null},we=a(!1),Ce=()=>{const e=je.value.map((e=>({modelId:e.modelId,type:"模型"})));we.value=!0,x(e).then((e=>{we.value=!1,e.success?(je.value=[],U("success",e.message)):U("error",e.message)})).catch((()=>{we.value=!1}))},xe=()=>{ye.value.pageNo=1,Ue()},Ee=()=>{Ue()},ze=()=>{je.value=be.value.filter((e=>!0===e.check))},Se=()=>{const e=be.value.map((e=>({...e,check:!1})));je.value=[],be.value=e},Oe=(e,a)=>{const s=I.defaults.headers.common.Tenant||"master";let l=`${window.baseConfig.previewResourceUrl}${s}/model/${e}/0/gltf/screenshot.jpg`;return"Particles"===a&&(l=`${window.baseConfig.previewResourceUrl}${s}/model/${e}/0/particles/screenshot.jpg`),l},Ue=()=>{be.value=[],ke.value=!0,je.value=[],C(ye.value).then((e=>{if(200===e.code){const{rows:a,pageNo:s,pageSize:l,totalPage:t,totalRows:i}=e.data;be.value=[],a.forEach((e=>{be.value.push({...e,check:!1})})),ye.value.pageNo=s,ye.value.pageSize=l,ge.value=i}ke.value=!1})).catch((()=>{ke.value=!1}))},Pe=(e,a)=>{ye.value.pageNo=e,Ue()},De=(e,a)=>{ye.value.pageNo=1,ye.value.pageSize=a,Ue()},Ie=a(""),Le=a(""),Ne=a(""),Me=a(!1),$e=()=>{Me.value=!1},Re=a(),Ye=()=>{Re.value.add("",ye.value.classify)},Ge=a(),Te=()=>{Ge.value.add(ye.value.classify)},Ze=a(!1),qe=()=>{Ze.value=!0;const e=je.value.map((e=>e.modelId));if(e.length)E(e).then((e=>{e.success&&U("success",e.message)})).finally((()=>{Ze.value=!1}));else{const e={classify:ye.value.classify};z(e).then((e=>{e.success&&U("success",e.message)})).finally((()=>{Ze.value=!1}))}};return A({classifySearch:e=>{ye.value.classify=e,ye.value.pageNo=1,ye.value.pageSize=12,Ue()}}),(e,a)=>{const s=L,b=N,_=M,C=$,x=l("CloseCircleFilled"),z=R,O=l("CloudSyncOutlined"),I=l("UploadOutlined"),A=l("DownloadOutlined"),fe=Y,Ue=T,Ae=Z,Fe=q,Je=t("loading");return o(),i(c,null,[n("div",F,[n("div",J,[n("div",K,[n("div",W,[a[5]||(a[5]=n("span",{class:"search-label"},"关键字",-1)),r(s,{value:ye.value.title,"onUpdate:value":a[0]||(a[0]=e=>ye.value.title=e),"allow-clear":"",placeholder:"模型名称",class:"search-input",onKeyup:a[1]||(a[1]=d((e=>xe()),["enter"]))},null,8,["value"])]),n("div",B,[a[9]||(a[9]=n("span",{class:"search-label"},"资源文件",-1)),r(_,{value:ye.value.fileExist,"onUpdate:value":a[2]||(a[2]=e=>ye.value.fileExist=e),class:"search-select",placeholder:"请选择资源文件是否存在","allow-clear":"",style:{width:"100px"},onChange:a[3]||(a[3]=e=>xe())},{default:p((()=>[r(b,{value:""},{default:p((()=>a[6]||(a[6]=[m("全部")]))),_:1}),r(b,{value:"1"},{default:p((()=>a[7]||(a[7]=[m("不存在")]))),_:1}),r(b,{value:"2"},{default:p((()=>a[8]||(a[8]=[m("已存在")]))),_:1})])),_:1},8,["value"])]),n("div",H,[r(C,{type:"primary",class:"btn",onClick:a[4]||(a[4]=e=>xe())},{default:p((()=>a[10]||(a[10]=[m(" 查询 ")]))),_:1})])]),n("div",V,[je.value.length>0?(o(),i("span",Q,[a[11]||(a[11]=m(" 已选中：")),n("span",X,h(je.value.length),1),r(x,{class:"cancelCheck",style:{"font-size":"17px",color:"rgb(29 31 36 / 70%)","vertical-align":"middle"},onClick:Se})])):v("",!0),e.hasPerm("ZYGL:MODEL-CLASS-DELETE")&&je.value.length>0?(o(),u(z,{key:1,"ok-text":"是","cancel-text":"否",onConfirm:Ce},{title:p((()=>a[12]||(a[12]=[n("p",null,"确认删除选中模型或贴图？",-1)]))),default:p((()=>[f((o(),u(C,{type:"primary",class:"handle-btn"},{default:p((()=>a[13]||(a[13]=[m(" 批量删除 ")]))),_:1})),[[Je,we.value]])])),_:1})):v("",!0),e.hasPerm("model-lib:sync-by-classify")?(o(),u(C,{key:2,type:"primary",class:"handle-btn",onClick:Ye},{icon:p((()=>[r(O,{style:{"font-size":"16px"}})])),default:p((()=>[a[14]||(a[14]=m(" 同步选中分类下模型 "))])),_:1})):v("",!0),ye.value.classify.split("/")[1]&&e.hasPerm("model-lib:offline-project-upload")?(o(),u(C,{key:3,class:"handle-btn",onClick:Te},{icon:p((()=>[r(I,{style:{"font-size":"16px"}})])),default:p((()=>[a[15]||(a[15]=m(" 上传模型 "))])),_:1})):v("",!0),e.hasPerm("ZYGL:MODEL-DOWNLOAD")?(o(),u(z,{key:4,placement:"bottomRight","ok-text":"是","cancel-text":"否",onConfirm:qe},{title:p((()=>a[16]||(a[16]=[n("p",null,"该操作将对选中的模型进行打包下载",-1),n("p",null,"若未选中，将下载全部，是否继续？",-1)]))),default:p((()=>[r(C,{class:"handle-btn",loading:Ze.value},{icon:p((()=>[r(A,{style:{"font-size":"16px"}})])),default:p((()=>[a[17]||(a[17]=m(" 下载开发资源 "))])),_:1},8,["loading"])])),_:1})):v("",!0)])]),ke.value?(o(),u(fe,{key:0,spinning:ke.value,"wrapper-class-name":"spin"},{default:p((()=>a[18]||(a[18]=[n("div",{class:"list"},null,-1)]))),_:1},8,["spinning"])):(o(),i(c,{key:1},[0===be.value.length?(o(),i("div",ee,[r(y(G),{image:y(G).PRESENTED_IMAGE_SIMPLE},null,8,["image"])])):(o(),i("div",ae,[(o(!0),i(c,null,g(be.value,(s=>(o(),i("div",{key:s.id,class:"item"},[n("div",se,[n("div",{class:k(["img-box",{active:s.check}])},[n("img",{src:Oe(s.modelId,s.type),alt:"图片",onErrorOnce:_e},null,40,le),n("div",te,[r(Ue,{checked:s.check,"onUpdate:checked":e=>s.check=e,class:"chk",onChange:ze},null,8,["checked","onUpdate:checked"])]),n("div",ie,[n("div",oe,[r(Ue,{checked:s.check,"onUpdate:checked":e=>s.check=e,class:"chk",onChange:ze},null,8,["checked","onUpdate:checked"])]),e.hasPerm("model-lib:detail")?(o(),i("div",{key:0,disabled:1===s.fileExist,class:"btn",onClick:e=>(e=>{Ie.value=e.modelId,Le.value=e.type,Ne.value=e.fileSize,Me.value=!0})(s)},"详情",8,ce)):v("",!0),e.hasPerm("model-lib:sync-by-id")?(o(),u(z,{key:1,placement:"topRight",title:"更新资源方式","ok-text":"在线","cancel-text":"离线",onCancel:e=>(e=>{Re.value.add(e.modelId,"")})(s),onConfirm:e=>{return a=s,ke.value=!0,void S({id:a.id}).then((e=>{200===e.code?U("success","已向系统发起同步请求操作，请耐心等待"):U("error",e.message),ke.value=!1})).finally((()=>{ke.value=!1}));var a}},{default:p((()=>a[19]||(a[19]=[n("div",{class:"btn"},"更新",-1)]))),_:2},1032,["onCancel","onConfirm"])):v("",!0),e.hasPerm("ZYGL:MODEL-COPY")?(o(),i("div",{key:2,class:"btn",onClick:e=>(e=>{if(e.fileUrl&&""!==e.fileUrl){const a=document.createElement("input");a.value=`${window.baseConfig.downloadUrl}${e.fileUrl}`,document.body.appendChild(a),a.select(),document.execCommand("Copy"),U("success","模型地址拷贝成功"),document.body.removeChild(a)}else U("warning","模型资源不存在")})(s)},"拷贝地址",8,ne)):v("",!0),e.hasPerm("model-lib:download")?(o(),i("div",{key:3,class:"btn",onClick:e=>(e=>{const a=[e.modelId];E(a).then((e=>{e.success&&U("success",e.message)}))})(s)},"下载资源",8,re)):v("",!0)])],2),n("div",ue,[n("span",de,h(s.title),1),n("span",pe,[n("span",{class:k(["circle",1===s.fileExist?"red":"green"])},null,2),m(h(1===s.fileExist?"不存在":"已存在"),1)])]),n("div",me,[n("span",null,"上传时间："+h(y(j)(s.updateTime).format("YYYY-MM-DD")),1),n("span",ve,h(s.type),1)])])])))),128))]))],64)),n("div",he,[r(Ae,{total:ge.value,size:"small",current:ye.value.pageNo,"page-size":ye.value.pageSize,"show-total":e=>`共 ${e} 条`,"show-size-changer":"","show-quick-jumper":"",onChange:Pe,onShowSizeChange:De},null,8,["total","current","page-size","show-total"])])]),r(Fe,{open:Me.value,"wrap-class-name":"cus-modal",width:"1000px",footer:null,title:"模型详情","mask-closable":!1,"mask-style":{background:" rgba(0,0,0,0.71)"},onCancel:$e},{default:p((()=>[Me.value?(o(),u(w,{key:0,"preview-id":Ie.value,"preview-type":Le.value,"file-size":Ne.value},null,8,["preview-id","preview-type","file-size"])):v("",!0)])),_:1},8,["open"]),r(P,{ref_key:"addOrEditRef",ref:Re,onOk:Ee},null,512),r(D,{ref_key:"uploadModalRef",ref:Ge,onOk:Ee},null,512)],64)}}}),[["__scopeId","data-v-cd2ec559"]]);export{fe as default};
