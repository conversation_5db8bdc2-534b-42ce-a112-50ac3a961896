import{d as e,a,r as s,j as l,o as t,S as o,U as i,am as r,c as n,bL as c,bJ as d,G as p,V as u,al as m,F as v,bk as h,u as y,W as g}from"./@vue-DgI1lw0Y.js";import{d as j}from"./dayjs-D9wJ8dSB.js";import{u as f}from"./useTableScrollY-9oHU_oJI.js";import{aI as k}from"./main-DE7o6g98.js";import{_}from"./CheckForm.vue_vue_type_style_index_0_lang-CF5OcOA1.js";import{I as b,g as T,h as w,X as N,B as C,_ as S,b as z}from"./ant-design-vue-DW0D0Hn-.js";import{_ as I}from"./vue-qr-6l_NUpj8.js";import"./@babel-B4rXMRun.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const x={class:"operate-log"},Y={class:"search-wrap"},D={class:"search-item"},E={class:"search-item"},O={class:"search-item"},B={class:"search-item"},F={class:"search-btns"},H={class:"table-wrap"},J={key:0},U={key:1},M={key:1,class:"table-actions"},R=["onClick"],K={key:2},P={class:"pagination"},A=I(e({__name:"Index",setup(e){const I=a({account:"",operationType:void 0,logName:"",searchBeginTime:"",searchEndTime:"",dates:[],pageNo:1,pageSize:10}),A=s(0),G=a([{title:"操作人账号",sortDirections:["descend","ascend"],sorter:!0,dataIndex:"account",key:"account",ellipsis:!0},{title:"操作人姓名",dataIndex:"username",sortDirections:["descend","ascend"],sorter:!0,key:"username",ellipsis:!0},{title:"日志名称",dataIndex:"name",key:"name",ellipsis:!0},{title:"日志级别",dataIndex:"level",key:"level",ellipsis:!0},{title:"结果",dataIndex:"success",key:"success",ellipsis:!0},{title:"IP地址",dataIndex:"ip",sortDirections:["descend","ascend"],sorter:!0,key:"ip",ellipsis:!0},{title:"操作时间",dataIndex:"opTime",sortDirections:["descend","ascend"],sorter:!0,key:"opTime",ellipsis:!0},{title:"操作",key:"action",width:140}]),L=e=>e&&e>j().endOf("day"),W=l((()=>({current:I.pageNo,pageSize:I.pageSize,total:A.value,pageSizeOptions:["10","20","50","100"],showTotal:e=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),$=(e,a)=>{I.pageNo=e,I.pageSize=a,se()},q=s(),{scrollY:Q}=f(q);let V=a([]);const X=a({loading:!1}),Z=()=>{const e=JSON.parse(JSON.stringify(I));return e.account||delete e.account,e.logName||delete e.logName,e.operationType||delete e.operationType,e.dates&&e.dates.length>0?(e.searchBeginTime=e.dates[0],e.searchEndTime=e.dates[1]):(delete e.searchBeginTime,delete e.searchEndTime),delete e.dates,e},ee=()=>{I.pageNo=1,I.pageSize=10,se()},ae=(e,a,s)=>{let l={};l=s.order?{sortField:s.field,sortRule:"descend"===s.order?"DESC":"ASC"}:{sortField:"",sortRule:""},se(l)},se=(e={})=>{X.loading=!0;const a={...Z(),...e};k(a).then((e=>{X.loading=!1,200===e.code&&(V=e.data.rows||[],A.value=e.data.totalRows)}),(()=>{X.loading=!1}))},le=s();return t((()=>{se()})),(e,a)=>{const s=b,l=T,t=w,f=N,k=C,A=S,Z=z;return i(),o("div",x,[r("div",Y,[r("div",D,[a[8]||(a[8]=r("span",{class:"search-label"},"操作人",-1)),n(s,{value:I.account,"onUpdate:value":a[0]||(a[0]=e=>I.account=e),"allow-clear":"",placeholder:"请输入账号或姓名",class:"search-input",onKeyup:a[1]||(a[1]=c((e=>ee()),["enter"]))},null,8,["value"])]),r("div",E,[a[9]||(a[9]=r("span",{class:"search-label"},"日志名称",-1)),n(s,{value:I.logName,"onUpdate:value":a[2]||(a[2]=e=>I.logName=e),"allow-clear":"",placeholder:"请输入日志名称",class:"search-input",onKeyup:a[3]||(a[3]=c((e=>ee()),["enter"]))},null,8,["value"])]),r("div",O,[a[24]||(a[24]=r("span",{class:"search-label"},"操作类型",-1)),n(t,{value:I.operationType,"onUpdate:value":a[4]||(a[4]=e=>I.operationType=e),"allow-clear":!0,placeholder:"请选择操作类型",class:"search-select",onChange:ee},{default:d((()=>[n(l,{value:"0"},{default:d((()=>a[10]||(a[10]=[p("其他")]))),_:1}),n(l,{value:"1"},{default:d((()=>a[11]||(a[11]=[p("增加")]))),_:1}),n(l,{value:"2"},{default:d((()=>a[12]||(a[12]=[p("删除")]))),_:1}),n(l,{value:"3"},{default:d((()=>a[13]||(a[13]=[p("编辑")]))),_:1}),n(l,{value:"4"},{default:d((()=>a[14]||(a[14]=[p("更新")]))),_:1}),n(l,{value:"5"},{default:d((()=>a[15]||(a[15]=[p("查询")]))),_:1}),n(l,{value:"6"},{default:d((()=>a[16]||(a[16]=[p("详情")]))),_:1}),n(l,{value:"7"},{default:d((()=>a[17]||(a[17]=[p("树")]))),_:1}),n(l,{value:"8"},{default:d((()=>a[18]||(a[18]=[p("导入")]))),_:1}),n(l,{value:"9"},{default:d((()=>a[19]||(a[19]=[p("导出")]))),_:1}),n(l,{value:"10"},{default:d((()=>a[20]||(a[20]=[p("强退")]))),_:1}),n(l,{value:"11"},{default:d((()=>a[21]||(a[21]=[p("清空")]))),_:1}),n(l,{value:"12"},{default:d((()=>a[22]||(a[22]=[p("修改状态")]))),_:1}),n(l,{value:"13"},{default:d((()=>a[23]||(a[23]=[p("客户端记录")]))),_:1})])),_:1},8,["value"])]),r("div",B,[a[27]||(a[27]=r("span",{class:"search-label"},"操作时间",-1)),n(f,{value:I.dates,"onUpdate:value":a[5]||(a[5]=e=>I.dates=e),placeholder:["开始时间","结束时间"],"disabled-date":L,class:"search-date","show-time":{hideDisabledOptions:!0},"value-format":"YYYY-MM-DD HH:mm:ss",onChange:ee},null,8,["value"]),r("div",F,[n(k,{type:"primary",class:"search-btn",onClick:a[6]||(a[6]=e=>ee())},{default:d((()=>a[25]||(a[25]=[p(" 查询 ")]))),_:1}),n(k,{class:"search-btn",onClick:a[7]||(a[7]=e=>(I.account="",I.operationType=void 0,I.searchBeginTime="",I.searchEndTime="",I.searchEndTime="",I.dates=[],I.logName="",void ee()))},{default:d((()=>a[26]||(a[26]=[p(" 重置 ")]))),_:1})])])]),r("div",H,[r("div",{ref_key:"table",ref:q,class:"table-content"},[e.hasPerm("sys-op-log:page")?(i(),u(A,{key:0,class:"table",scroll:{y:y(Q)},pagination:!1,"row-key":e=>e.id,size:"small",columns:G,loading:X.loading,"data-source":y(V),onChange:ae},{bodyCell:d((({column:s,record:l})=>["success"===s.key?(i(),o(v,{key:0},["Y"===l.success?(i(),o("span",J,a[28]||(a[28]=[r("span",{class:"yes-mark"},null,-1),p("成功")]))):m("",!0),"N"===l.success?(i(),o("span",U,a[29]||(a[29]=[r("span",{class:"no-mark"},null,-1),p("失败")]))):m("",!0)],64)):m("",!0),"action"===s.key?(i(),o("div",M,[e.hasPerm("sys-op-log:record")?(i(),o("a",{key:0,onClick:e=>(e=>{le.value.init(e)})(l)},"查看",8,R)):m("",!0)])):m("",!0),"opTime"===s.key?(i(),o("span",K,h(y(j)(l.opTime).format("YYYY-MM-DD HH:mm:ss")),1)):m("",!0)])),_:1},8,["scroll","row-key","columns","loading","data-source"])):m("",!0),r("div",P,[y(V).length>0?(i(),u(Z,g({key:0},W.value,{onChange:$}),null,16)):m("",!0)])],512)]),n(_,{ref_key:"checkFormRef",ref:le},null,512)])}}}),[["__scopeId","data-v-b4cfe746"]]);export{A as default};
