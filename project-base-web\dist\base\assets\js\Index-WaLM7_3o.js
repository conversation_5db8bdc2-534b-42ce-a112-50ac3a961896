import{d as e,r as a,o as s,S as t,U as r,am as i,c as o,bL as l,bJ as n,G as c,V as p,al as m,u,W as d}from"./@vue-DgI1lw0Y.js";import v from"./AddEditForm-DwcuJSic.js";import j from"./PreView-BJI9W2LC.js";import{u as h}from"./main-DE7o6g98.js";import{u as f}from"./useTableScrollY-9oHU_oJI.js";import{g as y,d as g}from"./twinFusion-CyqtDZS9.js";import{I as w,B as b,i as k,_,b as x}from"./ant-design-vue-DW0D0Hn-.js";import{_ as z}from"./vue-qr-6l_NUpj8.js";import"./Attr-HRzetqrZ.js";import"./@ti-cli-Z3vwStYr.js";import"./@babel-B4rXMRun.js";import"./vue-CqsM5HEV.js";import"./RemoteSet-Cp4Rkq2C.js";import"./dictManage-CTOLVV06.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./Step2-pmGlScfh.js";import"./PreOrDown-C0vmHv2Y.js";const C={class:"twin-fusion"},S={class:"search-wrap"},I={class:"search-content"},T={class:"search-item"},P={class:"search-btns"},V={class:"table-handle"},R={class:"table-wrap"},A={key:0,class:"table-actions"},O=["onClick"],E=["onClick"],F={class:"pagination"},J=z(e({__name:"Index",setup(e){const z=[{title:"视图名称",dataIndex:"name"},{title:"唯一编码",dataIndex:"code"},{title:"备注",dataIndex:"remark"},{title:"创建时间",dataIndex:"createTime"},{title:"操作",dataIndex:"action"}],J=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),N=(e,a)=>{J.value=Object.assign(J.value,{current:e,pageSize:a}),U()},q=a({searchValue:null}),B=a(!1),D=a([]),G=a(),{scrollY:K}=f(G);s((()=>{U()}));const L=()=>{J.value.current=1,J.value.pageSize=10,U()},U=()=>{D.value=[],B.value=!0,y({...q.value,pageNo:J.value.current,pageSize:J.value.pageSize}).then((e=>{if(200===e.code){const{rows:a,pageNo:s,pageSize:t,totalRows:r}=e.data;a.forEach((e=>{e.createTime=e.createTime?e.createTime.substring(0,19).replace("T"," "):""})),D.value=a,J.value.current=s,J.value.pageSize=t,J.value.total=r}B.value=!1})).catch((()=>{B.value=!1}))},W=a(),Y=(e,a)=>{W.value.init(e,a)},$=()=>{L()},H=a();return(e,a)=>{const s=w,f=b,y=k,U=_,M=x;return r(),t("div",C,[i("div",S,[i("div",I,[i("div",T,[a[5]||(a[5]=i("span",{class:"search-label"},"视图搜索",-1)),o(s,{value:q.value.searchValue,"onUpdate:value":a[0]||(a[0]=e=>q.value.searchValue=e),"allow-clear":"",placeholder:"请输入孪生视图名称",class:"search-input",onKeyup:a[1]||(a[1]=l((e=>L()),["enter"]))},null,8,["value"])]),i("div",P,[o(f,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>L())},{default:n((()=>a[6]||(a[6]=[c(" 查询 ")]))),_:1}),o(f,{class:"search-btn",onClick:a[3]||(a[3]=e=>(q.value.searchValue="",void L()))},{default:n((()=>a[7]||(a[7]=[c(" 重置 ")]))),_:1})])]),i("div",V,[e.hasPerm("twin-coalesce:add")?(r(),p(f,{key:0,type:"primary",class:"handle-btn",onClick:a[4]||(a[4]=e=>Y("add",null))},{default:n((()=>a[8]||(a[8]=[c(" 新增孪生视图 ")]))),_:1})):m("",!0)])]),i("div",R,[i("div",{ref_key:"table",ref:G,class:"table-content"},[o(U,{class:"table",scroll:{y:u(K)},pagination:!1,size:"small",loading:B.value,"row-key":e=>e.id,columns:z,"data-source":D.value},{bodyCell:n((({column:s,record:o})=>["action"===s.dataIndex?(r(),t("div",A,[e.hasPerm("twin-coalesce:edit")?(r(),t("a",{key:0,type:"text",onClick:e=>Y("edit",o)},"编辑",8,O)):m("",!0),e.hasPerm("twin-coalesce:delete")?(r(),p(y,{key:1,placement:"topRight",title:"确认删除？",onConfirm:()=>(e=>{B.value=!0,g(e).then((e=>{e.success?(h("success","孪生视图删除成功"),L()):h("error",e.message),B.value=!1}))})(o)},{default:n((()=>a[9]||(a[9]=[i("a",{type:"text"},"删除",-1)]))),_:2},1032,["onConfirm"])):m("",!0),e.hasPerm("twin-coalesce:detail")?(r(),t("a",{key:2,type:"text",onClick:e=>(e=>{H.value.init(e)})(o)},"预览",8,E)):m("",!0)])):m("",!0)])),_:1},8,["scroll","loading","row-key","data-source"]),i("div",F,[D.value.length>0?(r(),p(M,d({key:0},J.value,{onChange:N}),null,16)):m("",!0)])],512)]),o(v,{ref_key:"addEditFormRef",ref:W,onOk:$},null,512),o(j,{ref_key:"previewRef",ref:H},null,512)])}}}),[["__scopeId","data-v-76c614f0"]]);export{J as default};
