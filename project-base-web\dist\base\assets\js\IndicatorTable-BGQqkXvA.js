import{d as e,a,r as s,V as t,U as r,bJ as i,am as l,c as o,bL as n,G as d,bk as c,al as p,W as m}from"./@vue-DgI1lw0Y.js";import{d as u}from"./dayjs-D9wJ8dSB.js";import{aD as v}from"./main-DE7o6g98.js";import{I as j,X as h,B as w,_ as b,b as g,M as f}from"./ant-design-vue-DW0D0Hn-.js";import{_ as x}from"./vue-qr-6l_NUpj8.js";import"./@babel-B4rXMRun.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const y={class:"content"},D={class:"search-wrap"},C={class:"search-item"},I={class:"search-item"},k={class:"search-btns"},_={class:"table-wrap"},z={ref:"table",class:"table-content"},S={class:"pagination"},T=x(e({__name:"IndicatorTable",setup(e,{expose:x}){const T=[{title:"孪生体分类",dataIndex:"twinCode"},{title:"孪生体编码",dataIndex:"externalDeviceId",sortDirections:["descend","ascend"],sorter:!0},{title:"数据产生时间",dataIndex:"externalCreateTime",sortDirections:["descend","ascend"],sorter:!0},{title:"数据接收时间",dataIndex:"createTime",sortDirections:["descend","ascend"],sorter:!0}],N=a({externalDeviceId:"",twinCode:"",twinName:"",dates:[]}),O=s(!1),R=s(!1),Y=s([]),H=e=>e&&e>u().endOf("day"),J=()=>{M.value.current=1,M.value.pageSize=10,F()},M=s({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),F=(e={})=>{R.value=!0;let a={};N.dates&&N.dates.length&&(a={beginTime:N.dates[0],endTime:N.dates[1]}),v({externalDeviceId:N.externalDeviceId,twinCode:N.twinCode,pageNo:M.value.current,pageSize:M.value.pageSize,...e,...a}).then((e=>{M.value.total=e.data.totalRows,Y.value=e.data.rows})).finally((()=>{R.value=!1}))},G=(e,a)=>{M.value=Object.assign(M.value,{current:e,pageSize:a}),F()},K=(e,a,s)=>{let t={};s.order&&(t={sortField:s.field,sortRule:"descend"===s.order?"DESC":"ASC"}),F(t)},L=()=>{O.value=!1,N.externalDeviceId="",N.dates=[],M.value.current=1};return x({init:(e,a)=>{N.twinCode=e,N.twinName=a,O.value=!0,F()}}),(e,a)=>{const s=j,u=h,v=w,x=b,F=g,U=f;return r(),t(U,{width:"auto",title:"指标数据",footer:null,"wrap-class-name":"cus-modal full-modal",open:O.value,"mask-closable":!1,onCancel:L},{default:i((()=>[l("div",y,[l("div",D,[l("div",C,[a[4]||(a[4]=l("span",{class:"search-label"},"唯一编码",-1)),o(s,{value:N.externalDeviceId,"onUpdate:value":a[0]||(a[0]=e=>N.externalDeviceId=e),"allow-clear":"",placeholder:"请输入孪生体编码",class:"search-input",onKeyup:a[1]||(a[1]=n((e=>J()),["enter"]))},null,8,["value"])]),l("div",I,[a[5]||(a[5]=l("span",{class:"search-label"},"操作时间",-1)),o(u,{value:N.dates,placeholder:["开始时间","结束时间"],"disabled-date":H,class:"search-date","show-time":{hideDisabledOptions:!0},"value-format":"YYYY-MM-DD HH:mm:ss",onChange:J},null,8,["value"])]),l("div",k,[o(v,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>J())},{default:i((()=>a[6]||(a[6]=[d(" 查询 ")]))),_:1}),o(v,{class:"search-btn",onClick:a[3]||(a[3]=e=>(N.externalDeviceId="",N.dates=[],void J()))},{default:i((()=>a[7]||(a[7]=[d(" 重置 ")]))),_:1})])]),l("div",_,[l("div",z,[o(x,{class:"table",pagination:!1,size:"small",loading:R.value,"row-key":e=>e.uuid,columns:T,"data-source":Y.value,onChange:K},{expandedRowRender:i((({record:e})=>[l("div",null,[l("p",null,"指标内容："+c(e.originalFrame),1),l("p",null,"备注："+c(e.remark),1)])])),_:1},8,["loading","row-key","data-source"]),l("div",S,[Y.value.length>0?(r(),t(F,m({key:0},M.value,{onChange:G}),null,16)):p("",!0)])],512)])])])),_:1},8,["open"])}}}),[["__scopeId","data-v-568da8ee"]]);export{T as default};
