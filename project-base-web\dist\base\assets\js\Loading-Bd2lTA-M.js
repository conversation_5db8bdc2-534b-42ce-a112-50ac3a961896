import{_ as s}from"./loading-D1_5edTl.js";import{s as o}from"./pinia-iScrtxv6.js";import{a as r}from"./main-DE7o6g98.js";import{d as t,S as i,am as e,c as p,u as m,U as a}from"./@vue-DgI1lw0Y.js";import{e as j}from"./ant-design-vue-DW0D0Hn-.js";import{_ as n}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const l={class:"campus-loading keep-px"},c={class:"loading-wrap"},d=n(t({__name:"Loading",props:{loadingPercent:{type:Number,default:0}},setup(t){const n=r(),{themeColor:d}=o(n);return(o,r)=>{const n=j;return a(),i("div",l,[e("div",c,[r[0]||(r[0]=e("img",{class:"img",src:s,alt:"loading"},null,-1)),r[1]||(r[1]=e("p",{class:"title"},"地图加载中...",-1)),p(n,{"stroke-color":{from:"#108ee9",to:m(d)},percent:t.loadingPercent,status:"active"},null,8,["stroke-color","percent"])])])}}}),[["__scopeId","data-v-821ed5b3"]]);export{d as default};
