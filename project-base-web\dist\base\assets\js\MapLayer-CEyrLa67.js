import{d as e,r as a,j as l,o as i,S as t,am as n,c as s,bJ as o,al as r,q as d,bD as u,F as c,b7 as p,Z as v,u as m,b9 as g,B as f,Y as w,V as y,n as h,G as b,bk as j,U as k}from"./@vue-DgI1lw0Y.js";import{e as _}from"./earth_bg-CoeJTRun.js";import{c as L}from"./coordtransform-CEi8OirC.js";import{U as C,u as M,z as I,aa as A,a5 as S}from"./main-DE7o6g98.js";import{v as E}from"./mapManag-wSwfWE2D.js";import{u as $}from"./map-DByF_6EZ.js";import{b as x}from"./effectsPack-CMixYyev.js";import{a as D}from"./axios-ChCdAMPF.js";import{h as P,r as U,V as R,g as G,n as N,q as H,d as F,B as z}from"./ant-design-vue-DW0D0Hn-.js";import{_ as V}from"./vue-qr-6l_NUpj8.js";import"./@babel-B4rXMRun.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const J={class:"map-layer keep-px"},B={class:"map-toggle"},K={class:"map-search"},q={key:0,class:"search"},O={key:1,class:"search"},Y={key:0,class:"suggest-list"},W=["onClick"],Z={class:"name"},Q={class:"layer"},X={class:"layer-wrapper"},ee={class:"content-wrapper"},ae={class:"box"},le={class:"icon"},ie={class:"box-con"},te={class:"box"},ne={class:"icon"},se={class:"box-con"},oe={class:"box"},re={class:"icon"},de={class:"box-con"},ue={class:"effect-layer"},ce={key:0,class:"effect-layer-wrapper"},pe=["src"],ve={class:"name"},me={class:"btns"},ge={key:1,class:"effect-layer-wrapper no-data"},fe=V(e({__name:"MapLayer",props:{currentTab:{type:Object,default:()=>{}}},setup(e,{expose:V}){const fe=C(),we=$(),ye=e,he=a(!1);let be=null;const je=()=>{var e,a,l,i,t,n,s,o,r,d,u,c,p,v;Ie.value=!1,he.value=!he.value,be||(be={defaultRenderSettings:null,ambDefaultConfig:{},dirDefaultConfig:{}},be.defaultRenderSettings=window.app.renderSettings,be.ambDefaultConfig.intensity=null==(a=null==(e=window.app.scene)?void 0:e.ambientLight)?void 0:a.intensity,be.ambDefaultConfig.color=null==(i=null==(l=window.app.scene)?void 0:l.ambientLight)?void 0:i.color,be.dirDefaultConfig.intensity=null==(n=null==(t=window.app.scene)?void 0:t.mainLight)?void 0:n.intensity,be.dirDefaultConfig.color=null==(o=null==(s=window.app.scene)?void 0:s.mainLight)?void 0:o.color,be.dirDefaultConfig.horzAngle=null==(u=null==(d=null==(r=window.app.scene)?void 0:r.mainLight)?void 0:d.adapter)?void 0:u.horzAngle,be.dirDefaultConfig.vertAngle=null==(v=null==(p=null==(c=window.app.scene)?void 0:c.mainLight)?void 0:p.adapter)?void 0:v.vertAngle)},ke=a([]);x({name:"",kind:1,pageNo:1,pageSize:999}).then((e=>{200===e.code&&(ke.value=e.data.rows||[])})).catch((()=>{ke.value=[]}));const _e=e=>{const a=D.defaults.headers.common.Tenant;return`${window.baseConfig.previewResourceUrl}${a}/effect/${e.code}/${e.version}/preview.png`},Le=a(),Te=()=>{window.app.renderSettings=be.defaultRenderSettings,window.app.scene.ambientLight.intensity=be.ambDefaultConfig.intensity,window.app.scene.ambientLight.color=be.ambDefaultConfig.color,window.app.scene.mainLight.intensity=be.dirDefaultConfig.intensity,window.app.scene.mainLight.color=be.dirDefaultConfig.color,window.app.scene.mainLight.adapter.horzAngle=be.dirDefaultConfig.horzAngle,window.app.scene.mainLight.adapter.vertAngle=be.dirDefaultConfig.vertAngle},Ce=a(!1),Me=(e,a)=>{Ce.value=e,we.updateMarkFlag(Ce.value),Ce.value?window.mapDrawInstance.startDraw(we.twinData,(e=>{e?window.mapPlacementIns.init({thing:e,type:"new",updateCb:e=>{switch(e.type){case"angles":we.twinProp.angles=e.data;break;case"offsetHeight":we.twinProp.offsetHeight=e.data}}}):(Ce.value=!1,we.updateMarkFlag(Ce.value))}),a):(window.mapPlacementIns.reset(!0),window.mapDrawInstance.reset(),we.updateShowEditPanel(!1))},Ie=a(!1),Ae=l((()=>ye.currentTab.coords)),Se=()=>{he.value=!1,Ie.value=!Ie.value,Ie.value&&Je()},Ee=a([]),$e=a([]),xe=a(!1),De=e=>{Ve(Ee.value,e,"TileLayer")},Pe=a([]),Ue=a([]),Re=a(!1),Ge=e=>{Ve(Pe.value,e,"Tile3dLayer")},Ne=a([]),He=a([]),Fe=a(!1),ze=e=>{Ve(Ne.value,e,"TerrainLayer")},Ve=(e,a,l)=>{e.forEach((e=>{if("TerrainLayer"===l&&2===fe.thingjsVersion)a.includes(e.id)?window.mapManagerIns.map.terrain.url=e.url:window.mapManagerIns.map.terrain.url="";else{let i=window.mapManagerIns.map.getLayerByName(`refer_${e.id}`).objects[0];i&&!a.includes(e.id)?("TerrainLayer"===l&&(i.url=""),i.visible=!1):!i&&a.includes(e.id)?"Tile3dLayer"===l?1===fe.thingjsVersion?i=window.app.create({type:l,name:`refer_${e.id}`,url:e.url,offsetHeight:e.height,complete:({object:e})=>{window.mapManagerIns.map.addLayer(e),setTimeout((()=>{e.flyToLayer()}),1e3)}}):(i=new THING.EARTH.Tile3DLayer({name:`refer_${e.id}`,url:e.url,offsetHeight:e.height,complete:function(e){e.object.flyToLayer()}}),window.mapManagerIns.map.addLayer(i)):"TileLayer"===l?1===fe.thingjsVersion?i=window.app.create({type:"TileLayer",name:`refer_${e.id}`,url:e.url,complete:({object:e})=>{window.mapManagerIns.map.addLayer(e)}}):(i=new THING.EARTH.TileLayer({name:`refer_${e.id}`,url:e.url}),window.mapManagerIns.map.addLayer(i)):"TerrainLayer"===l&&(i=window.app.create({type:"TerrainLayer",name:`refer_${e.id}`,url:e.url,complete:({object:e})=>{window.mapManagerIns.map.addLayer(e)}})):i&&!1===i.visible&&a.includes(e.id)&&("TerrainLayer"===l&&(i.url=e.url),i.visible=!0,i.offsetHeight=e.height)}}))},Je=()=>{E().then((e=>{200===e.code?(Ee.value=e.data.filter((e=>0===e.source)),Pe.value=e.data.filter((e=>1===e.source)),Ne.value=e.data.filter((e=>2===e.source))):M("error","获取图层数据失败！")}))},Be=a(),Ke=a(),qe=()=>{Be.value.value?Ke.value.style.display="block":Ke.value.style.display="none"},Oe=()=>{Be.value.value="",Ke.value&&(Ke.value.style.display="none")},Ye=a("天地图"),We=e=>{Oe(),na.value=[],da.value="","高德"===e?ea():"天地图"===e&&ia()},Ze=()=>{da.value="",na.value=[]},Qe=a(),Xe=()=>{Qe.value||M("error","请在开发配置中配置高德地图1.4.15版本JSAPI密钥")},ea=()=>{document.getElementsByClassName("AMapScript").length||I(["AMAP_JSAPI_KEY"]).then((e=>{var a,l;e.success&&(null==(a=e.data)?void 0:a[0])&&"无"!==(null==(l=e.data)?void 0:l[0])&&(Qe.value=e.data[0],A(`https://webapi.amap.com/maps?v=1.4.15&key=${e.data[0]}&plugin=AMap.MapType,AMap.Autocomplete,AMap.PlaceSearch,AMap.Geocoder`,(()=>{h((()=>{ta()}))}),"AMapScript"))}))},aa=a(),la=()=>{aa.value||M("error","请在开发配置中配置天地地图密钥")},ia=()=>{document.getElementsByClassName("TMapScript").length||I(["TIAN_DI_TU_MI_YAO"]).then((e=>{var a,l;e.success&&(null==(a=e.data)?void 0:a[0])&&"无"!==(null==(l=e.data)?void 0:l[0])&&(aa.value=e.data[0],A(`http://api.tianditu.gov.cn/api?v=4.0&tk=${e.data[0]}`,(()=>{h((()=>{oa()}))}),"TMapScript"))}))},ta=()=>{const e=new window.AMap.Autocomplete({input:"suggestId"});window.AMap.event.addListener(e,"select",(e=>{if(Ke.value.style.display="block",e.poi.location){const{lng:a,lat:l}=e.poi.location;let i=[a,l];"WGS84"===Ae.value&&(i=L.gcj02towgs84(a,l)),window.app.camera.earthFlyTo({lonlat:i,time:1e3,duration:1e3,height:1e3})}else M("warning","未找到位置")}))},na=a([]);let sa=a(null);const oa=()=>{const e=new T.Map("map-div",{projection:"EPSG:4326"});e.centerAndZoom(new T.LngLat(116.40769,39.89945),12);const a={pageCapacity:10,onSearchComplete:ra};sa.value=new T.LocalSearch(e,a)},ra=e=>{na.value=e.suggests||[]},da=a(""),ua=a(),ca=e=>{da.value?(ua.value.style.display="block",sa.value.search(da.value,4)):(ua.value.style.display="none",na.value=[])};return i((()=>{ia()})),V({handleMark:Me}),(e,a)=>{const l=G,i=P,h=g("pushpin-outlined"),T=g("icon-font"),C=g("down-outlined"),M=H,I=F,A=N,E=R,$=z;return k(),t("div",J,[a[20]||(a[20]=n("div",{id:"map-div"},null,-1)),n("div",B,[s(i,{ref:"select",value:Ye.value,"onUpdate:value":a[0]||(a[0]=e=>Ye.value=e),style:{width:"90px"},onChange:We},{default:o((()=>[s(l,{value:"高德"},{default:o((()=>a[11]||(a[11]=[b("高德")]))),_:1}),s(l,{value:"天地图"},{default:o((()=>a[12]||(a[12]=[b("天地图")]))),_:1})])),_:1},8,["value"])]),n("div",K,["高德"===Ye.value?(k(),t("div",q,[n("input",{id:"suggestId",ref_key:"SearchText",ref:Be,type:"text",onFocus:Xe,placeholder:"请输入需要查询的地点（需联网）",autocomplete:"off",onKeyup:qe},null,544),n("div",{id:"search-clear",ref_key:"SearchClear",ref:Ke,class:"search-clear",title:"清空",onClick:Oe},null,512)])):r("",!0),"天地图"===Ye.value?(k(),t("div",O,[d(n("input",{ref_key:"SearchText",ref:Be,type:"text","onUpdate:modelValue":a[1]||(a[1]=e=>da.value=e),onFocus:la,placeholder:"请输入需要查询的地点（需联网）",autocomplete:"off",onKeyup:ca},null,544),[[u,da.value]]),na.value.length?(k(),t("div",Y,[(k(!0),t(c,null,p(na.value,((e,a)=>(k(),t("div",{class:"suggest-item",onClick:a=>(e=>{const a=e.lonlat.split(",");let l=a;"GCJ02"===Ae.value&&(l=L.wgs84togcj02(a[0],a[1])),window.app.camera.earthFlyTo({lonlat:l,time:1e3,duration:1e3,height:1e3})})(e),key:a},[n("div",Z,j(e.name),1),b(" "+j(e.address),1)],8,W)))),128))])):r("",!0),n("div",{id:"search-clear",ref_key:"SearchTClear",ref:ua,class:"search-clear",title:"清空",onClick:Ze},null,512)])):r("",!0)]),n("div",{class:v(["mark",m(we).markFlag?"active":""]),title:"开始标注",onClick:a[2]||(a[2]=e=>Me(!m(we).markFlag))},[s(h)],2),n("div",Q,[s(T,{type:"icon-tuceng",title:"地图图层",onClick:Se}),d(n("div",X,[a[16]||(a[16]=n("div",{class:"title"},"地图图层",-1)),a[17]||(a[17]=n("div",{class:"line"},null,-1)),n("div",ee,[n("div",ae,[n("div",{class:"box-title",onClick:a[3]||(a[3]=e=>xe.value=!xe.value)},[a[13]||(a[13]=n("div",{class:"text"},"正射影像",-1)),n("div",le,[s(C,{style:w({transform:`rotate(${xe.value?"180deg":"0deg"})`})},null,8,["style"])])]),d(n("div",ie,[Ee.value.length?(k(),y(E,{key:0,value:$e.value,"onUpdate:value":a[4]||(a[4]=e=>$e.value=e),onChange:De},{default:o((()=>[(k(!0),t(c,null,p(Ee.value,(e=>(k(),y(A,{key:e.id,class:"item"},{default:o((()=>[s(I,{span:24},{default:o((()=>[s(M,{value:e.id,disabled:Ae.value!==e.coords},{default:o((()=>[b(j(e.name),1)])),_:2},1032,["value","disabled"])])),_:2},1024)])),_:2},1024)))),128))])),_:1},8,["value"])):(k(),y(m(U),{key:1,image:m(U).PRESENTED_IMAGE_SIMPLE},null,8,["image"]))],512),[[f,xe.value]])]),n("div",te,[n("div",{class:"box-title",onClick:a[5]||(a[5]=e=>Re.value=!Re.value)},[a[14]||(a[14]=n("div",{class:"text"},"倾斜摄影图层",-1)),n("div",ne,[s(C,{style:w({transform:`rotate(${Re.value?"180deg":"0deg"})`})},null,8,["style"])])]),d(n("div",se,[Pe.value.length?(k(),y(E,{key:0,value:Ue.value,"onUpdate:value":a[6]||(a[6]=e=>Ue.value=e),onChange:Ge},{default:o((()=>[(k(!0),t(c,null,p(Pe.value,(e=>(k(),y(A,{key:e.id,class:"item"},{default:o((()=>[s(I,{span:24},{default:o((()=>[s(M,{value:e.id,disabled:Ae.value!==e.coords},{default:o((()=>[b(j(e.name),1)])),_:2},1032,["value","disabled"])])),_:2},1024)])),_:2},1024)))),128))])),_:1},8,["value"])):(k(),y(m(U),{key:1,image:m(U).PRESENTED_IMAGE_SIMPLE},null,8,["image"]))],512),[[f,Re.value]])]),n("div",oe,[n("div",{class:"box-title",onClick:a[7]||(a[7]=e=>Fe.value=!Fe.value)},[a[15]||(a[15]=n("div",{class:"text"},"地形数据",-1)),n("div",re,[s(C,{style:w({transform:`rotate(${Fe.value?"180deg":"0deg"})`})},null,8,["style"])])]),d(n("div",de,[Ne.value.length?(k(),y(E,{key:0,value:He.value,"onUpdate:value":a[8]||(a[8]=e=>He.value=e),onChange:ze},{default:o((()=>[(k(!0),t(c,null,p(Ne.value,(e=>(k(),y(A,{key:e.id,class:"item"},{default:o((()=>[s(I,{span:24},{default:o((()=>[s(M,{value:e.id,disabled:Ae.value!==e.coords},{default:o((()=>[b(j(e.name),1)])),_:2},1032,["value","disabled"])])),_:2},1024)])),_:2},1024)))),128))])),_:1},8,["value"])):(k(),y(m(U),{key:1,image:m(U).PRESENTED_IMAGE_SIMPLE},null,8,["image"]))],512),[[f,Fe.value]])])])],512),[[f,Ie.value]])]),n("div",ue,[s(T,{type:"icon-xiaoguo",title:"地图效果包",onClick:je}),ke.value.length?d((k(),t("div",ce,[(k(!0),t(c,null,p(ke.value,(e=>(k(),t("div",{class:v(["effect-item",Le.value===e.code?"checked":""]),key:e.id},[n("img",{src:`${_e(e)}`,alt:"",class:"img",onError:a[9]||(a[9]=e=>e.target.src=m(_))},null,40,pe),n("div",ve,j(e.name),1),n("div",me,[Le.value!==e.code?(k(),y($,{key:0,class:"btn",onClick:a=>(async e=>{var a;Le.value=e.code;const l=D.defaults.headers.common.Tenant||"master",i=`${window.baseConfig.previewResourceUrl}${l}/effect/${e.code}/${e.version}/map.json`,t=`${window.baseConfig.previewResourceUrl}${l}/effect/${e.code}/${e.version}/map.bundle.json`,n=await S(t)?t:i;null==(a=window.mapManagerIns)||a.changeEffect({url:n,resourcePrefix:`${window.baseConfig.previewResourceUrl}${l}/effect/${e.code}/${e.version}`})})(e)},{default:o((()=>a[18]||(a[18]=[b("预览")]))),_:2},1032,["onClick"])):(k(),y($,{key:1,class:"btn",onClick:a[10]||(a[10]=e=>{return Le.value="",window.mapManagerIns.map.baseLayers.removeAll(),window.mapManagerIns.map.layers.removeAll(),void(1===fe.thingjsVersion?window.mapManagerIns.changeTile({tileLayerUrl:null==(a=ye.currentTab)?void 0:a.tilesUrl,maximumLevel:18}):2===fe.thingjsVersion&&(window.mapManagerIns.changeTile({url:null==(l=ye.currentTab)?void 0:l.tilesUrl,name:null==(i=ye.currentTab)?void 0:i.name,maximumLevel:18}),Te()));var a,l,i})},{default:o((()=>a[19]||(a[19]=[b("取消预览")]))),_:1}))])],2)))),128))],512)),[[f,he.value]]):d((k(),t("div",ge,[s(m(U),{image:m(U).PRESENTED_IMAGE_SIMPLE},null,8,["image"])],512)),[[f,he.value]])])])}}}),[["__scopeId","data-v-e22d2533"]]);export{fe as default};
