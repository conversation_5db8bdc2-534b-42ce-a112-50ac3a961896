import{d as e,r as a,a as l,o as n,f as t,b9 as o,V as i,U as s,bJ as d,c as u,S as c,am as m,F as r,b7 as p,G as b,bk as f,q as v,B as g,al as h,A as y,Y as _}from"./@vue-DgI1lw0Y.js";import{S as k}from"./SelectModel-ClNHKDDy.js";import{S as V}from"./SelectBubble-DCDJhldK.js";import{aF as N,aG as M,aH as C,u as U}from"./main-DE7o6g98.js";import{n as I,d as E,F as w,c as x,h as T,g as F,I as q,B as j,M as L}from"./ant-design-vue-DW0D0Hn-.js";const S=e({__name:"ModelMapping",emits:["ok"],setup(e,{expose:S,emit:B}){const O=a(),$=a(!1),A=a(!1),G=a([]),J=a(""),P=a([]),H=B,R=l({domains:[]}),X=e=>{const a=P.value.find((a=>a.model===e.fieldName));a&&(e.attrType=a.type,"checkbox"===e.attrType&&(e.content=e.content.split(",")),e.optionalValues=a.optionalValues)},Y=async()=>{if(R.domains.length){A.value=!0;const e=[];for(const a of O.value){const l=await a.validate().then((e=>!0)).catch((e=>!1));e.push(l)}if(e.includes(!1))A.value=!1;else{R.domains.forEach((e=>{Array.isArray(e.content)&&(e.content=e.content.join(","))}));const e={twinClassId:J.value,modelMappings:R.domains};C(e).then((e=>{200===e.code?(H("ok"),U("success","模型映射保存成功"),D()):(A.value=!1,U("error",`保存失败：${e.message}`))}))}}else{const e={twinClassId:J.value,modelMappings:[]};C(e).then((e=>{200===e.code?(H("ok"),U("success","模型映射保存成功"),D()):(A.value=!1,U("error",`保存失败：${e.message}`))}))}},z=()=>{R.domains.push({fieldName:"",attrType:"input",condition:"",conditionList:[],content:"",optionalValues:[],thingsModelUuid:"",thingsModelName:"",bubbleInfoName:"",bubbleInfoId:"",modelVisible:!1,bubbleVisible:!1})},D=()=>{R.domains=[],$.value=!1,A.value=!1},K=e=>{(e.path||e.composedPath&&e.composedPath()).find((e=>e.dataset&&"selectTree"===e.dataset.type))||R.domains.forEach((e=>{e.modelVisible=!1,e.bubbleVisible=!1}))};return n((()=>{document.addEventListener("click",K)})),t((()=>{document.removeEventListener("click",K)})),S({init:async e=>{const a=JSON.parse(e.form).list;G.value=a.filter((e=>"input"===e.type||"checkbox"===e.type||"radio"===e.type)),J.value=e.id;const l=await N({twinClassId:e.id});P.value=l.data.legendXxvMenuTwinFiledVos;const n=await M({twinClassId:e.id});if(200===n.code)for(let t=0;t<n.data.length;t++){let e=P.value.find((e=>e.model===n.data[t].fieldName));e||(e={}),R.domains=n.data,R.domains[t].modelVisible=!1,R.domains[t].bubbleVisible=!1,R.domains[t].conditionList=e.conditions,X(R.domains[t])}$.value=!0}}),(e,a)=>{const l=E,n=I,t=F,N=T,M=x,C=q,U=o("down-outlined"),S=o("MinusCircleOutlined"),B=w,J=o("icon-font"),H=j,X=L;return s(),i(X,{title:"模型映射",width:1700,"wrap-class-name":"cus-modal",open:$.value,"mask-closable":!1,"confirm-loading":A.value,"ok-text":"确认","cancel-text":"取消",onOk:Y,onCancel:D},{default:d((()=>[u(n,{gutter:24},{default:d((()=>[u(l,{span:3},{default:d((()=>a[0]||(a[0]=[m("span",null,"属性字段",-1)]))),_:1}),u(l,{span:3},{default:d((()=>a[1]||(a[1]=[m("span",null,"条件",-1)]))),_:1}),u(l,{span:3},{default:d((()=>a[2]||(a[2]=[m("span",null,"属性值",-1)]))),_:1}),u(l,{span:7},{default:d((()=>a[3]||(a[3]=[m("span",null,"三维模型",-1)]))),_:1}),u(l,{span:7},{default:d((()=>a[4]||(a[4]=[m("span",null,"模型图标",-1)]))),_:1})])),_:1}),(s(!0),c(r,null,p(R.domains,((e,a)=>(s(),i(B,{key:a,ref_for:!0,ref_key:"modelMappingFormRef",ref:O,model:e,"wrapper-col":{xs:{span:24,offset:0}}},{default:d((()=>[u(n,{gutter:24},{default:d((()=>[u(l,{span:3},{default:d((()=>[u(M,{name:"fieldName",rules:{required:!0,message:"请选择属性字段",trigger:"change"}},{default:d((()=>[u(N,{value:e.fieldName,"onUpdate:value":a=>e.fieldName=a,placeholder:"请选择属性字段",onChange:a=>(e=>{const a=P.value.find((a=>a.model===e.fieldName));a&&(e.attrType=a.type,"checkbox"===e.attrType?e.content=[]:e.content="",e.conditionList=a.conditions,e.optionalValues=a.optionalValues)})(e)},{default:d((()=>[(s(!0),c(r,null,p(G.value,((e,a)=>(s(),i(t,{key:a,value:e.model},{default:d((()=>[b(f(e.label),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value","onChange"])])),_:2},1024)])),_:2},1024),u(l,{span:3},{default:d((()=>[u(M,{name:"condition",rules:{required:!0,message:"请选择条件",trigger:"change"}},{default:d((()=>[u(N,{value:e.condition,"onUpdate:value":a=>e.condition=a,placeholder:"请选择条件",disabled:!e.fieldName},{default:d((()=>[(s(!0),c(r,null,p(e.conditionList,((e,a)=>(s(),i(t,{key:a,value:e.code},{default:d((()=>[b(f(e.message),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value","disabled"])])),_:2},1024)])),_:2},1024),u(l,{span:3},{default:d((()=>["checkbox"===e.attrType?v((s(),i(M,{key:0,name:"content",rules:{required:"isEmpty"!==e.condition&&"notEmpty"!==e.condition,message:"请输入属性值",trigger:"change"}},{default:d((()=>[u(N,{value:e.content,"onUpdate:value":a=>e.content=a,disabled:!e.fieldName,mode:"multiple"},{default:d((()=>[(s(!0),c(r,null,p(e.optionalValues,((e,a)=>(s(),i(t,{key:a,value:e.value},{default:d((()=>[b(f(e.value),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value","disabled"])])),_:2},1032,["rules"])),[[g,"isEmpty"!==e.condition&&"notEmpty"!==e.condition]]):"radio"===e.attrType?v((s(),i(M,{key:1,name:"content",rules:{required:"isEmpty"!==e.condition&&"notEmpty"!==e.condition,message:"请选择",trigger:"change"}},{default:d((()=>[u(N,{value:e.content,"onUpdate:value":a=>e.content=a,placeholder:"请选择条件",disabled:!e.fieldName},{default:d((()=>[(s(!0),c(r,null,p(e.optionalValues,((e,a)=>(s(),i(t,{key:a,value:e.value},{default:d((()=>[b(f(e.value),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["value","onUpdate:value","disabled"])])),_:2},1032,["rules"])),[[g,"isEmpty"!==e.condition&&"notEmpty"!==e.condition]]):v((s(),i(M,{key:2,name:"content",rules:{required:"isEmpty"!==e.condition&&"notEmpty"!==e.condition,message:"请输入",trigger:"change"}},{default:d((()=>[u(C,{value:e.content,"onUpdate:value":a=>e.content=a,valueModifiers:{trim:!0},placeholder:"请输入查询的值",disabled:!e.fieldName},null,8,["value","onUpdate:value","disabled"])])),_:2},1032,["rules"])),[[g,"isEmpty"!==e.condition&&"notEmpty"!==e.condition]])])),_:2},1024),u(l,{span:7},{default:d((()=>[u(M,{name:"thingsModelName",rules:{required:!0,message:"请选择模型",trigger:"change"}},{default:d((()=>[u(C,{value:e.thingsModelName,"onUpdate:value":a=>e.thingsModelName=a,"data-type":"selectTree",readonly:"",placeholder:"请选择模型",onFocus:a=>e.modelVisible=!0},{suffix:d((()=>[u(U,{style:_({transform:`rotate(${e.modelVisible?"180deg":"0deg"})`}),onClick:y((a=>(e=>{e.modelVisible=!e.modelVisible})(e)),["stop"])},null,8,["style","onClick"])])),_:2},1032,["value","onUpdate:value","onFocus"]),e.modelVisible?(s(),i(k,{key:0,"things-model-uuid":e.thingsModelUuid,onCheckModel:a=>((e,a)=>{a.thingsModelName=e.title,a.thingsModelUuid=e.modelId,a.modelVisible=!1})(a,e)},null,8,["things-model-uuid","onCheckModel"])):h("",!0)])),_:2},1024)])),_:2},1024),u(l,{span:7},{default:d((()=>[u(M,{name:"bubbleInfoName",rules:{required:!0,message:"请选择图标",trigger:"change"}},{default:d((()=>[u(C,{value:e.bubbleInfoName,"onUpdate:value":a=>e.bubbleInfoName=a,"data-type":"selectTree",readonly:"",placeholder:"请选择图标",onFocus:a=>e.bubbleVisible=!0},{suffix:d((()=>[u(U,{style:_({transform:`rotate(${e.bubbleVisible?"180deg":"0deg"})`}),onClick:y((a=>(e=>{e.bubbleVisible=!e.bubbleVisible})(e)),["stop"])},null,8,["style","onClick"])])),_:2},1032,["value","onUpdate:value","onFocus"]),e.bubbleVisible?(s(),i(V,{key:0,"bubble-info-id":e.bubbleInfoId,onCheckBubble:a=>((e,a)=>{a.bubbleInfoName=e.name,a.bubbleInfoId=e.id,a.bubbleVisible=!1})(a,e)},null,8,["bubble-info-id","onCheckBubble"])):h("",!0)])),_:2},1024)])),_:2},1024),u(l,{span:1},{default:d((()=>[u(M,null,{default:d((()=>[u(S,{title:"删除",disabled:1===R.domains.length,onClick:a=>(e=>{const a=R.domains.indexOf(e);-1!==a&&R.domains.splice(a,1)})(e)},null,8,["disabled","onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["model"])))),128)),u(H,{type:"dashed",style:{width:"100%"},onClick:z},{default:d((()=>[u(J,{type:"icon-add",class:"icon-add"}),a[5]||(a[5]=b("添加配置"))])),_:1})])),_:1},8,["open","confirm-loading"])}}});export{S as _};
