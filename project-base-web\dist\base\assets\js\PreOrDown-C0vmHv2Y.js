import{b as e}from"./twinFusion-CyqtDZS9.js";import{e as t}from"./main-DE7o6g98.js";import{a}from"./axios-ChCdAMPF.js";import{a2 as s,B as i,M as l}from"./ant-design-vue-DW0D0Hn-.js";import{d as o,r,w as n,S as m,U as p,al as u,c as d,V as c,bJ as v,G as j,F as y,b7 as f,bk as b,am as g}from"./@vue-DgI1lw0Y.js";import{_ as w}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const k={class:"preOrDown"},h={key:0},_=["title","onClick"],C={key:1},O=["title"],x=["onClick"],z=["src"],I=w(o({__name:"PreOrDown",props:{data:{type:Object,default:null}},setup(o){const w=o,I=r([]),$=r([]),J=r(!1),q=r(""),D=r(!1);n((()=>w.data),(e=>{e&&(I.value=JSON.parse(e),$.value=I.value.filter((e=>["jpg","png"].includes(e.name.substring(e.name.lastIndexOf(".")+1)))))}),{immediate:!0});const F=a=>{D.value=!0,e({id:a.dataId}).then((e=>{t(e),D.value=!1})).catch((()=>{D.value=!1})).finally((()=>{D.value=!1}))},G=e=>{const t=a.defaults.headers.common.Tenant,s=e.name.substring(e.name.lastIndexOf(".")+1);q.value=`${window.baseConfig.previewResourceUrl}${t}/twinfile/${e.dataId}.${s}`,J.value=!0},H=()=>{J.value=!1};return(e,t)=>{const a=i,o=s,r=l;return p(),m("div",k,[I.value.length>0?(p(),m("div",h,[I.value.length>1?(p(),c(o,{key:0,title:"下载",trigger:"hover",placement:"bottom",width:260},{content:v((()=>[(p(!0),m(y,null,f(I.value,((e,t)=>(p(),m("div",{key:t,class:"item",title:e.name,onClick:t=>F(e)},b(e.name),9,_)))),128))])),default:v((()=>[d(a,{type:"primary",size:"small",class:"tab-btn down",loading:D.value},{default:v((()=>t[2]||(t[2]=[j("下载")]))),_:1},8,["loading"])])),_:1})):(p(),c(a,{key:1,type:"primary",size:"small",class:"tab-btn down",loading:D.value,onClick:t[0]||(t[0]=e=>F(I.value[0]))},{default:v((()=>t[3]||(t[3]=[j("下载")]))),_:1},8,["loading"]))])):u("",!0),$.value.length>0?(p(),m("div",C,[$.value.length>1?(p(),c(o,{key:0,title:"预览",trigger:"hover",placement:"bottom",width:260},{content:v((()=>[(p(!0),m(y,null,f($.value,((e,t)=>(p(),m("div",{key:t,class:"item",title:e.name},[g("div",{onClick:t=>G(e)},b(e.name),9,x)],8,O)))),128))])),default:v((()=>[d(a,{type:"primary",size:"small",class:"tab-btn preview"},{default:v((()=>t[4]||(t[4]=[j("预览")]))),_:1})])),_:1})):(p(),c(a,{key:1,type:"primary",size:"small",class:"tab-btn preview",onClick:t[1]||(t[1]=e=>G($.value[0]))},{default:v((()=>t[5]||(t[5]=[j("预览")]))),_:1}))])):u("",!0),d(r,{width:500,title:"预览图片",footer:null,"body-style":{maxHeight:"500px",overflow:"auto"},"wrap-class-name":"cus-modal",open:J.value,"mask-closable":!1,onCancel:H},{default:v((()=>[""!==q.value?(p(),m("img",{key:0,src:q.value,alt:"模型预览图",class:"prewimg"},null,8,z)):u("",!0)])),_:1},8,["open"])])}}}),[["__scopeId","data-v-6e6fc2da"]]);export{I as default};
