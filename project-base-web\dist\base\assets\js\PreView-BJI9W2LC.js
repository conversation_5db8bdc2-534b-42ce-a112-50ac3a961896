import{d as e,r as a,a as t,V as s,U as l,bJ as o,am as i,c as r,bL as n,G as u,al as p,S as c,W as d}from"./@vue-DgI1lw0Y.js";import{q as m}from"./twinFusion-CyqtDZS9.js";import v from"./PreOrDown-C0vmHv2Y.js";import{I as j,B as h,_ as y,b as f,M as w}from"./ant-design-vue-DW0D0Hn-.js";import{_ as b}from"./vue-qr-6l_NUpj8.js";import"./main-DE7o6g98.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const g={class:"preview"},C={class:"search-wrap"},x={class:"search-item"},k={class:"search-btns"},z={class:"table-wrap"},_={ref:"table",class:"table-content"},F=["onClick"],I={class:"pagination"},S=["innerHTML"],V=b(e({__name:"PreView",setup(e,{expose:b}){const V=a(!1),$=a(""),O=a(!1),R=a(""),H=a([{title:"孪生对象编码",dataIndex:"twinClassCode"},{title:"孪生对象名称",dataIndex:"twinClassName"}]),N=a([]),q=t({searchValue:"",sortField:"",sortRule:""}),E=a([]),J=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),L=(e,a,t)=>{"applicationName"===t.field?q.sortField="application":q.sortField=t.field,t.order&&"ascend"===t.order?q.sortRule="ASC":t.order&&"descend"===t.order?q.sortRule="DESC":(q.sortField="",q.sortRule=""),T()},M=e=>{H.value=[{title:"孪生对象编码",dataIndex:"twinClassCode"},{title:"孪生对象名称",dataIndex:"twinClassName"}],e.forEach((e=>{if("uploadFile"===e.type||"editor"===e.type){const a={title:e.label,dataIndex:`${e.model}`,type:e.type,ellipsis:!0,align:"center",resizable:!0};H.value.push(a)}else{const a={title:e.label,dataIndex:e.model,sorter:!0,type:e.type,ellipsis:!0,resizable:!0};E.value.push(e),H.value.push(a)}}))},T=()=>{N.value=[],O.value=!0;let e="";""!==q.sortField&&""!==q.sortRule&&(e=`${q.sortField}${"ASC"===q.sortRule?"+":"-"}`);const a={};if(q.searchValue&&(E.value.forEach((e=>{"uploadFile"!==e.type&&(a[`${e.model}$`]=`%${q.searchValue}%`)})),Object.keys(a).length>1)){const e=[];Object.keys(a).forEach((a=>{e.push(a)})),a["@combine"]=e.join(",")}const t={"[]":{[R.value]:{...a,"@order":e},page:J.value.current-1,count:J.value.pageSize,query:2},"total@":"/[]/total","info@":"/[]/info"};m(t).then((e=>{const{total:a,count:t}=e.info;if(e["[]"]){let t=e["[]"];t=t.map((e=>({...e[R.value]}))),N.value=t,J.value.total=a}else N.value=[],J.value.total=a;O.value=!1})).catch((()=>{O.value=!1}))},A=()=>{J.value.current=1,J.value.pageSize=10,T()},B=(e,a)=>{J.value=Object.assign(J.value,{current:e,pageSize:a}),T()},D=()=>{V.value=!1},G=t({title:"",show:!1,content:""});return b({init:e=>{V.value=!0,$.value=e.name,R.value=e.code;const a=e.form?JSON.parse(e.form):{list:[]};M(a.list),T()}}),(e,a)=>{const t=j,m=h,b=y,R=f,E=w;return l(),s(E,{width:1200,title:$.value,"body-style":{maxHeight:"700px",overflow:"auto"},"wrap-class-name":"cus-modal",open:V.value,footer:null,"mask-closable":!1,onCancel:D},{default:o((()=>[i("div",g,[i("div",C,[i("div",x,[a[5]||(a[5]=i("span",{class:"search-label"},"关键词",-1)),r(t,{value:q.searchValue,"onUpdate:value":a[0]||(a[0]=e=>q.searchValue=e),"allow-clear":"",placeholder:"孪生体自定义属性内容",class:"search-input",onKeyup:a[1]||(a[1]=n((e=>A()),["enter"]))},null,8,["value"])]),i("div",k,[r(m,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>A())},{default:o((()=>a[6]||(a[6]=[u(" 查询 ")]))),_:1}),r(m,{class:"search-btn",onClick:a[3]||(a[3]=e=>(q.searchValue="",void A()))},{default:o((()=>a[7]||(a[7]=[u(" 重置 ")]))),_:1})])]),i("div",z,[i("div",_,[r(b,{class:"table",scroll:{y:450,x:1100},pagination:!1,size:"small",loading:O.value,"row-key":e=>e.uuid,columns:H.value,"data-source":N.value,onChange:L},{bodyCell:o((({column:e,record:a,index:t})=>["uploadFile"===e.type?(l(),s(v,{key:t,data:a[e.dataIndex]},null,8,["data"])):p("",!0),"editor"===e.type?(l(),c("a",{key:1,onClick:t=>((e,a)=>{G.show=!0,G.content=e[a.dataIndex],G.title=a.title})(a,e)}," 详情",8,F)):p("",!0)])),_:1},8,["loading","row-key","columns","data-source"]),i("div",I,[N.value.length>0?(l(),s(R,d({key:0},J.value,{onChange:B}),null,16)):p("",!0)])],512)]),r(E,{title:G.title,"body-style":{maxHeight:"80vh",paddingBottom:"40px",overflowY:"auto",overflowX:"hidden"},"wrap-class-name":"cus-modal",footer:null,width:900,open:G.show,"mask-closable":!1,onCancel:a[4]||(a[4]=e=>G.show=!1)},{default:o((()=>[i("div",{class:"editor-html",innerHTML:G.content},null,8,S)])),_:1},8,["title","open"])])])),_:1},8,["title","open"])}}}),[["__scopeId","data-v-cf57d2e2"]]);export{V as default};
