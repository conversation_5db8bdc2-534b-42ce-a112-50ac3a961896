import{d as e,r as a,j as t,S as s,al as l,V as o,c as n,bJ as r,G as i,am as d,F as p,b7 as y,u,bk as m,A as c,U as f}from"./@vue-DgI1lw0Y.js";import{_ as g,e as _}from"./main-DE7o6g98.js";import{_ as v}from"./PreviewForm.vue_vue_type_script_setup_true_lang-BPt1qaC1.js";import{a2 as k,B as w}from"./ant-design-vue-DW0D0Hn-.js";const x={key:0,class:"down-row"},b=e({__name:"PreviewAndDownload",props:{text:{type:String,default:""},item:{type:String,default:""},record:{type:Object,default:()=>{}}},setup(e){const b=a(),j=e,K=t((()=>JSON.parse(j.text||"[]"))),S=(e,a)=>{var t,s;a.loadingKey=!0;let l=[];l="string"==typeof e?JSON.parse(e):e,g({id:l[0].dataId||(null==(s=null==(t=l[0])?void 0:t.response)?void 0:s.data)}).then((e=>{a.loadingKey=!1,_(e)})).catch((()=>{a.loadingKey=!1})).finally((()=>{a.loadingKey=!1}))},h=()=>{};return(a,t)=>{const g=w,_=k;return"[]"!==(j=e.text)&&""!==j&&'""'!==j&&void 0!==j?(f(),s("span",x,[""!==e.text&&JSON.parse(e.text).length>1?(f(),o(_,{key:0,placement:"top"},{content:r((()=>[(f(!0),s(p,null,y(u(K),((e,a)=>(f(),o(g,{type:"link",size:"small",loading:e.loadingKey,key:a,title:"点击下载",style:{display:"block"},class:"download",onClick:a=>S([e],e)},{default:r((()=>[i(m(e.name),1)])),_:2},1032,["loading","onClick"])))),128))])),title:r((()=>t[1]||(t[1]=[d("span",null,"下载",-1)]))),default:r((()=>[n(g,{type:"primary",size:"small"},{default:r((()=>t[2]||(t[2]=[i("下载")]))),_:1})])),_:1})):(f(),o(g,{key:1,type:"primary",size:"small",loading:e.record.loadingKey,onClick:t[0]||(t[0]=c((a=>S(e.text,e.record)),["stop"]))},{default:r((()=>t[3]||(t[3]=[i("下载")]))),_:1},8,["loading"])),n(v,{ref_key:"previewFormRef",ref:b,onClosePreview:h},null,512)])):l("",!0);var j}}});export{b as _};
