import{a0 as e,u as a}from"./main-DE7o6g98.js";import{M as s,S as t}from"./ant-design-vue-DW0D0Hn-.js";import{d as o,r as l,V as i,bJ as n,c as r,am as u,U as d}from"./@vue-DgI1lw0Y.js";const m={style:{"text-align":"center"}},v=["src"],c=o({__name:"PreviewForm",emits:["closePreview"],setup(o,{expose:c,emit:p}){const w=p,g=l(!1),f=l(""),x=l(!1),b=s=>{const t=s.id?s.id:s.dataId?s.dataId:"";e({id:t}).then((e=>{x.value=!1,f.value=`${window.baseConfig.previewResourceUrl}${e.data}`})).catch((e=>{x.value=!1,a("error",`预览错误：${e.message}`)}))},h=()=>{f.value="",g.value=!1,w("closePreview")};return c({preview:e=>{g.value=!0,x.value=!0,b(e)}}),(e,a)=>{const o=t,l=s;return d(),i(l,{title:"预览图片","wrap-class-name":"cus-modal","body-style":{maxHeight:"600px",paddingBottom:"40px",overflow:"auto"},footer:null,width:900,open:g.value,"mask-closable":!1,onCancel:h},{default:n((()=>[r(o,{spinning:x.value},{default:n((()=>[u("div",m,[u("img",{src:f.value,style:{"max-width":"99%"}},null,8,v)])])),_:1},8,["spinning"])])),_:1},8,["open"])}}});export{c as _};
