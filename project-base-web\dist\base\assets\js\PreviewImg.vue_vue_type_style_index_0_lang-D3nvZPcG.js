import{F as e,c as a,M as s}from"./ant-design-vue-DW0D0Hn-.js";import{d as l,r as t,a as r,V as o,U as i,bJ as n,c as m,am as d,n as c}from"./@vue-DgI1lw0Y.js";const u={style:{width:"100%"}},f=["src"],g=l({__name:"PreviewImg",setup(l,{expose:g}){const h=t(!1),p=t(),v=r({imgUrl:""}),b=()=>{p.value.resetFields(),h.value=!1};return g({init:e=>{h.value=!0,c((()=>{p.value.resetFields(),v.imgUrl=e}))}}),(l,t)=>{const r=a,c=e,g=s;return i(),o(g,{width:800,title:"预览图片",footer:null,"body-style":{height:"600px",overflow:"hidden"},"wrap-class-name":"cus-modal",open:h.value,"mask-closable":!1,onCancel:b},{default:n((()=>[m(c,{ref_key:"formRef",ref:p,model:v,"label-align":"left",class:"checkForm"},{default:n((()=>[m(r,{name:"imgUrl","has-feedback":""},{default:n((()=>[d("div",u,[d("img",{style:{width:"100%",height:"520px","object-fit":"contain"},alt:" 图片",src:v.imgUrl},null,8,f)])])),_:1})])),_:1},8,["model"])])),_:1},8,["open"])}}});export{g as _};
