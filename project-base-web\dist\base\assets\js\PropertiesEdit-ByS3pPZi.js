import{v as e}from"./@ti-cli-Z3vwStYr.js";import{R as s}from"./RemoteSet-Cp4Rkq2C.js";import{aE as t,u as o}from"./main-DE7o6g98.js";import{_ as i}from"./AssociatedConfig.vue_vue_type_script_setup_true_lang-BE6hFNQe.js";import a from"./AssociatedTwins-BINKmP0O.js";import{S as r,M as n}from"./ant-design-vue-DW0D0Hn-.js";import{d as l,r as m,V as p,U as u,bJ as c,c as d,am as j,u as v,al as f,n as g}from"./@vue-DgI1lw0Y.js";import{_ as y}from"./vue-qr-6l_NUpj8.js";import"./@babel-B4rXMRun.js";import"./vue-CqsM5HEV.js";import"./dictManage-CTOLVV06.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const _=[{source:"extend",label:"关联孪生体",type:"releative",options:{defaultValue:"[]",width:"200px",placeholder:"请选择",clearable:!1,disabled:!1,twins:{twinClassCode:"",props:[]}},model:"",key:""}],w={class:"kform"},b=y(l({__name:"PropertiesEdit",emits:["success"],setup(l,{expose:y,emit:b}){const k=b,x=m(!1),h=m({}),C=m(!1),J=m(),O=m(""),S=()=>{x.value=!1,C.value=!1};return y({init:e=>{h.value=e;const s=JSON.parse(e.form);O.value=e.id,x.value=!0,g((()=>{J.value.setJson(s)}))}}),(l,m)=>{var g;const y=r,b=n;return u(),p(b,{width:1400,title:"属性编辑【"+(null==(g=h.value)?void 0:g.name)+"】","wrap-class-name":"cus-modal",centered:"",open:x.value,"confirm-loading":C.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:m[0]||(m[0]=e=>(()=>{if(C.value)return;const e=J.value.getJson().list.every((e=>"releative"!==e.type||!(!e.options.twins.twinClassCode||!e.options.twins.props.length))),s=JSON.stringify(J.value.getJson());e?(C.value=!0,t({id:O.value,form:s}).then((e=>{C.value=!1,200===e.code&&(k("success"),o("success","属性设置成功"),S())})).catch((()=>{C.value=!1}))):o("warning","请配置关联孪生体属性!")})()),onCancel:S},{default:c((()=>[d(y,{spinning:C.value},{default:c((()=>[j("div",w,[d(v(e.AntDesignForm),{ref_key:"designForm",ref:J,"extend-config":v(_)},{extendContent:c((({element:e,data:s,onBack:t})=>[d(a,{element:e,data:s,disable:!0,onOnBack:e=>t(e)},null,8,["element","data","onOnBack"])])),extendConfig:c((({data:e})=>["releative"===e.type?(u(),p(i,{key:0,source:e},null,8,["source"])):f("",!0)])),extendRemote:c((({data:e})=>[d(s,{source:e},null,8,["source"])])),_:1},8,["extend-config"])])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-952e832e"]]);export{b as default};
