import{m as e}from"./dictManage-CTOLVV06.js";import{al as a}from"./main-DE7o6g98.js";import{m as l,R as t,k as s,q as o,V as u}from"./ant-design-vue-DW0D0Hn-.js";import{d as i,r as d,o as n,S as p,U as v,c,al as r,bJ as h,F as m,b7 as y,G as f,bk as b}from"./@vue-DgI1lw0Y.js";import{_ as k}from"./vue-qr-6l_NUpj8.js";const _={class:"remote"},g={key:0,class:"sel_list"},T={key:0},L={key:1},j=k(i({__name:"RemoteSet",props:{source:{type:Object}},setup(i){const k=d(i.source),j=d({children:"children",label:"title",value:"key"}),w=d([]);n((()=>{V()}));const V=()=>{w.value=[],e().then((e=>{if(e.success){const a=e.data;x(a),w.value=a}}))},x=e=>{e.forEach((e=>{const l=e;l.children||(l.children=[]),l.dictTypeList||(l.dictTypeList=[]),l.dictTypeName?(l.title=e.dictTypeName,l.value=e.dictTypeId,l.key=e.dictTypeId):(l.key=e.id,l.disabled=!0),e.dictTypeList.forEach((e=>{l.children.push(a(e))})),delete l.dictTypeList,l.children&&l.children.length>0&&x(l.children)}))},E=(e,a,l)=>{const t=[];e&&l.triggerNode.props.dictValueList.forEach((e=>{t.push({value:e,label:e})})),k.value.options.options=t};return(e,a)=>{const i=l,d=t,n=s,V=o,x=u;return v(),p("div",_,[c(i,{"tree-default-expand-all":"","allow-clear":"","field-names":j.value,style:{width:"100%"},"dropdown-style":{maxHeight:"300px",overflow:"auto"},"tree-data":w.value,placeholder:"动态选择数据源",onChange:E},null,8,["field-names","tree-data"]),k.value.options.options.length>0?(v(),p("div",g,["radio"===k.value.type||"select"===k.value.type&&!k.value.options.multiple?(v(),p("div",T,[c(n,{value:k.value.options.defaultValue,"onUpdate:value":a[0]||(a[0]=e=>k.value.options.defaultValue=e)},{default:h((()=>[(v(!0),p(m,null,y(k.value.options.options,(e=>(v(),p("div",{key:e.value},[c(d,{value:e.value},{default:h((()=>[f(b(k.value.options.showLabel?e.label:e.value),1)])),_:2},1032,["value"])])))),128))])),_:1},8,["value"])])):r("",!0),"checkbox"===k.value.type||"select"===k.value.type&&k.value.options.multiple?(v(),p("div",L,[c(x,{value:k.value.options.defaultValue,"onUpdate:value":a[1]||(a[1]=e=>k.value.options.defaultValue=e)},{default:h((()=>[(v(!0),p(m,null,y(k.value.options.options,(e=>(v(),p("div",{key:e.value},[c(V,{value:e.value},{default:h((()=>[f(b(k.value.options.showLabel?e.label:e.value),1)])),_:2},1032,["value"])])))),128))])),_:1},8,["value"])])):r("",!0)])):r("",!0)])}}}),[["__scopeId","data-v-7e6bbba2"]]);export{j as R};
