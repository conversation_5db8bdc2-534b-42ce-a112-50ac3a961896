import{R as e}from"./vue-pick-colors-CjUSS-xa.js";import{a}from"./main-DE7o6g98.js";import{F as t}from"./ant-design-vue-DW0D0Hn-.js";import{d as o,j as s,V as u,u as r,U as m}from"./@vue-DgI1lw0Y.js";const n=o({__name:"SelectColor",props:{value:{type:String,default:"#FF0000"}},emits:["update:value","change"],setup(o,{emit:n}){const l=a(),v=t.useInjectFormItemContext(),p=o,i=n,d=s((()=>p.value)),c=s((()=>l.modeName));return(a,t)=>(m(),u(r(e),{value:d.value,"onUpdate:value":t[0]||(t[0]=e=>d.value=e),theme:c.value,format:"rgb",size:26,"show-alpha":"",onChange:t[1]||(t[1]=e=>{return i("update:value",a=e),i("change",a),void v.onFieldChange();var a})},null,8,["value","theme"]))}});export{n as _};
