import{aq as e,ar as l}from"./main-DE7o6g98.js";import{l as a}from"./ti-B4l9Nygm.js";import{a as t}from"./axios-ChCdAMPF.js";import{a4 as s,E as n,a5 as o,S as d,a8 as i,a9 as r,r as u,a7 as c}from"./ant-design-vue-DW0D0Hn-.js";import{d as m,r as p,a as v,V as y,U as h,bJ as f,c as g,am as k,S as _,F as b,b7 as j,Z as C,bk as I}from"./@vue-DgI1lw0Y.js";import{_ as S}from"./vue-qr-6l_NUpj8.js";const T={class:"bubbleContent"},U={key:0,class:"model-list"},K=["onClick"],x=["src","alt"],M=["title"],w=S(m({__name:"SelectModel",props:{thingsModelUuid:{type:String,default:""}},emits:["checkModel"],setup(m,{emit:S}){const w=p(localStorage.getItem("XI_TONG_LOGO")||a),E=m,O=S,$=v({classify:"",title:"",modelType:null}),q=p(!1),G=p([]),F=p([]),J=p(["-1"]),L=p([]);e().then((e=>{var l,a;const t=[];t.unshift({key:"-1",title:"全部模型",label:"全部模型"}),(null==(l=e.data)?void 0:l.children)&&N(e.data.children),G.value=e.data?t.concat(e.data.children):t,L.value=[null==(a=e.data)?void 0:a.key]}));const N=e=>{e.forEach((e=>{var l;(null==(l=e.children)?void 0:l.length)?N(e.children):e.children=null}))},P=()=>{q.value=!0,l($).then((e=>{F.value=e.data,q.value=!1}))};P();const R=e=>{const l=t.defaults.headers.common.Tenant;return`${window.baseConfig.previewResourceUrl}${l}/model/${e}/0/gltf/screenshot.jpg`},V=e=>{$.modelType=e.item.modelType,"-1"===e.key?($.classify="",$.modelType=""):$.classify=e.key,P()},X=()=>{P()};return(e,l)=>{const a=n,t=o,m=r,p=i,v=u,S=d,N=c,P=s;return h(),y(P,{class:"menuClass","data-type":"selectTree"},{default:f((()=>[g(t,null,{default:f((()=>[g(a,{selectedKeys:J.value,"onUpdate:selectedKeys":l[0]||(l[0]=e=>J.value=e),inlineIndent:20,openKeys:L.value,"onUpdate:openKeys":l[1]||(l[1]=e=>L.value=e),onClick:V,mode:"inline",items:G.value},null,8,["selectedKeys","openKeys","items"])])),_:1}),g(N,{style:{height:"100%",padding:"0 24px"}},{default:f((()=>[g(S,{spinning:q.value},{default:f((()=>[g(p,null,{default:f((()=>[g(m,{value:$.title,"onUpdate:value":l[2]||(l[2]=e=>$.title=e),placeholder:"请输入名称进行搜索",onSearch:X},null,8,["value"])])),_:1}),k("ul",T,[F.value.length?(h(),_("div",U,[(h(!0),_(b,null,j(F.value,(e=>(h(),_("li",{key:e.modelId,class:C({bgColor:e.modelId===E.thingsModelUuid}),onClick:l=>(e=>{O("checkModel",e)})(e)},[k("img",{src:R(e.modelId),alt:e.title,onError:l[3]||(l[3]=e=>e.target.src=w.value)},null,40,x),k("span",{class:"text",title:e.title},I(e.title),9,M)],10,K)))),128))])):(h(),y(v,{key:1,style:{"padding-top":"30px"}}))])])),_:1},8,["spinning"])])),_:1})])),_:1})}}}),[["__scopeId","data-v-e6cdd53a"]]);export{w as S};
