import{s as e,u as s}from"./main-DE7o6g98.js";import{d as a}from"./systemAuthoriza-YtR9ek7A.js";import{S as t,F as i,c as o,q as r,f as l,h as n,g as m,V as p,M as u}from"./ant-design-vue-DW0D0Hn-.js";import{d as c,r as d,a as v,b9 as j,V as b,U as f,bJ as h,c as g,am as y,S as x,F as _,b7 as k,G as w,bk as S,n as D}from"./@vue-DgI1lw0Y.js";import{_ as L}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./qs-Cgg8q2iR.js";import"./side-channel-C0354c6F.js";import"./es-errors-Bq3kx8t5.js";import"./object-inspect-7jlLeo80.js";import"./side-channel-list-9pNTKFEK.js";import"./side-channel-map-qN4s3zsW.js";import"./get-intrinsic-BQNEepf0.js";import"./es-object-atoms-Ditt1eQ6.js";import"./math-intrinsics-z0bYe-bF.js";import"./gopd-uUVnxxv0.js";import"./es-define-property-lN4EXf1s.js";import"./has-symbols-CRskrF1U.js";import"./get-proto-nTH32ZvN.js";import"./dunder-proto-W0bZB-Yu.js";import"./call-bind-apply-helpers-CohWVzbO.js";import"./function-bind-Ckw9YnhN.js";import"./hasown-Dpzr2-IR.js";import"./call-bound-BVGeUV4S.js";import"./side-channel-weakmap-dRphfAXb.js";import"./js-binary-schema-parser-G48GG52R.js";const q="/systemx/data-migration/version";const z={class:"form-item-notice"},F=L(c({__name:"SelectVersion",setup(c,{expose:L}){const F=d(!1),U=d(!1),V=d(),A=v({businessData:!0,version:"",code:"",migrationServerList:["system-x","twin-x","model-x","scene-x"]}),C=d([]),E=d([{label:"系统数据",value:"system-x"},{label:"孪生体数据",value:"twin-x"},{label:"模型数据",value:"model-x"},{label:"场景数据",value:"scene-x"}]),G={version:[{required:!0,message:"请选择数据迁移版本！"}]},H=()=>{U.value||(V.value.resetFields(),F.value=!1,U.value=!1)},J=()=>{C.value=[];const s=[];var a;(a={businessData:A.businessData},e({url:q,method:"get",params:a})).then((e=>{e.success&&(e.data.forEach((e=>{s.push({id:e,name:e})})),C.value=s,D((()=>{C.value.length&&(A.version=C.value.id)})))}))};return L({init:e=>{F.value=!0,D((()=>{V.value.resetFields(),A.code=e,J()}))}}),(e,c)=>{const d=r,v=j("question-circle-outlined"),D=l,L=o,q=m,J=n,M=p,O=i,Q=t,R=u;return f(),b(R,{width:678,title:"选择版本","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:F.value,"confirm-loading":U.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:c[3]||(c[3]=e=>(U.value=!0,void V.value.validate().then((()=>{try{a({code:A.code,tag:A.version,businessData:A.businessData,migrationServerList:A.migrationServerList}).then((e=>{U.value=!1,e.success?(s("success",`系统正在打包项目【${A.code}】系统数据，这可能需要一段时间，现在您可以进行其他操作，完成后将通知您`),H()):e.message&&s("error",e.message)}))}catch(e){U.value=!1}})).catch((e=>{U.value=!1})))),onCancel:H},{default:h((()=>[g(Q,{spinning:U.value},{default:h((()=>[g(O,{ref_key:"formRef",ref:V,model:A,rules:G,"label-align":"left"},{default:h((()=>[g(L,{label:"是否导出业务数据","has-feedback":"",class:"form-item"},{default:h((()=>[g(d,{checked:A.businessData,"onUpdate:checked":c[0]||(c[0]=e=>A.businessData=e)},null,8,["checked"]),y("span",z,[g(D,{title:"业务数据具体指：孪生体点位数据、告警和性能指标、视点动画、部门和用户信息、日志和系统配置（除字典管理）相关数据",placement:"right"},{default:h((()=>[g(v,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),g(L,{name:"version",label:"请选择版本","has-feedback":""},{default:h((()=>[g(J,{value:A.version,"onUpdate:value":c[1]||(c[1]=e=>A.version=e),placeholder:"请选择版本"},{default:h((()=>[(f(!0),x(_,null,k(C.value,((e,s)=>(f(),b(q,{key:s,value:e.id},{default:h((()=>[w(S(e.name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1}),g(L,{name:"migrationServerList",label:"导出配置","has-feedback":""},{default:h((()=>[g(M,{value:A.migrationServerList,"onUpdate:value":c[2]||(c[2]=e=>A.migrationServerList=e),options:E.value},null,8,["value","options"])])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-7ac10d73"]]);export{F as default};
