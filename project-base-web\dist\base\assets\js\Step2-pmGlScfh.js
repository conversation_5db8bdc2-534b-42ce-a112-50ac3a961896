import{l as e}from"./twinFusion-CyqtDZS9.js";import{$ as a}from"./main-DE7o6g98.js";import{u as l,w as t,m as o,n as i,d as s,h as n,g as r,B as d,v as u}from"./ant-design-vue-DW0D0Hn-.js";import{T as p}from"./@ant-design-tBRGNTkq.js";import{d as m,r as c,w as v,D as f,b9 as h,S as y,U as j,c as w,am as b,bJ as C,F as g,b7 as _,V as k,al as x,G as D,bk as F,u as E}from"./@vue-DgI1lw0Y.js";import{_ as M}from"./vue-qr-6l_NUpj8.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const T={class:"fields-rels-content-body"},U={class:"add-box"},O=M(m({__name:"Step2",props:{formData:{type:Object,default:()=>{}},record:{type:Object,default:()=>{}}},setup(m,{expose:M}){const O=c("0"),K=c([]),P=c([]),J={children:"children",label:"name",value:"code",key:"code"},L=c([]),S=m;v((()=>S.formData),(e=>{e&&e.list&&(P.value=e.list)}),{immediate:!0}),f((()=>{q()}));const q=()=>{e({code:"XXV"}).then((e=>{K.value=e.data,G()}))},z=(e,a)=>-1!==a.name.indexOf(e)?a:null,G=()=>{L.value=[];const{record:e,formData:a}=S;if(e){const{twinCoalesceMapParamListMap:l}=e,t=[];Object.keys(l).forEach(((e,a)=>{N();const l=H(K.value,e),{list:o}=JSON.parse(l.form);L.value[a].twinFields=o,L.value[a].twinClass=e,L.value[a].map=[],V(a),L.value[a].title=l.name,t.push(A(e,a))})),Promise.all(t).then((()=>{L.value.forEach(((e,t)=>{const o=e.twinClass;l[o].forEach((l=>{if(l.twinModel&&"null"!==l.twinModel){const o=a.list.filter((e=>e.model===l.twinCoalesceModel))[0];o&&L.value[t].map.push({name:o.label,model:l.twinCoalesceModel,type:o.type,typeCode:o.type,filterTreeData:e.twinFields,inter:l.twinModel})}}))}))}))}else N(),O.value="0"},H=(e,a)=>{let l=!1,t=null;return function e(a,o){for(let i=0;i<a.length;i++)if(a[i].children&&a[i].children.length>0&&e(a[i].children,o),o===a[i].code||l){l||(t=a[i]),l=!0;break}}(e,a),t},N=()=>{L.value.push({title:"",twinClass:void 0,treeData:[],twinFields:[],map:[{name:"",model:"",type:"",typeCode:"",filterTreeData:[],inter:void 0}],dynamicForm:[{field:void 0}]})},V=e=>{L.value[e].treeData=X(K.value)},X=e=>e.map((e=>(e.disabled=1===e.type,e.children&&e.children instanceof Array&&e.children.length&&X(e.children),$(e)||(e.disabled=2===e.type),e))),$=e=>{let a=!0;return L.value.forEach((l=>{""===l.title||l.title[0]!=e.name||(a=!1)})),a},A=(e,l)=>new Promise((t=>{a({code:e,addFixed:!0}).then((e=>{if(e.success){const a=e.data;L.value[l].twinFields=a;const o=[];a.forEach((e=>{o.push({label:e.label,value:e.model})})),L.value[l].map.forEach((e=>{e.filterTreeData=o})),t(!0)}}))}));M({mapParamListMap:()=>{const e={};return L.value.some((a=>a.twinClass?!!a.map.some((e=>e.inter))||(e.error="请添加字段映射",!1):(e.error="请选择孪生对象！",!1)))?(L.value.forEach((a=>{if(a.twinClass){const l=a.twinClass,t=[];P.value.forEach((e=>{const o=a.map.filter((a=>e.model===a.model))[0];t.push({twinCode:l,twinCoalesceModel:e.model,twinModel:o?o.inter:null})})),e[l]=t}})),e):e}});return(e,a)=>{const m=l,c=o,v=s,f=i,M=r,K=n,S=h("icon-font"),q=d,G=t,H=u;return j(),y("div",null,[w(m,{message:"请选择需要融合的孪生对象，然后将接入的属性字段与服务的属性字段形成映射",banner:""}),b("div",T,[w(H,{activeKey:O.value,"onUpdate:activeKey":a[0]||(a[0]=e=>O.value=e)},{default:C((()=>[(j(!0),y(g,null,_(L.value,((e,l)=>(j(),k(G,{key:l+"",header:e.title?e.title:"映射组"+(l+1)},{extra:C((()=>[L.value.length>1?(j(),k(E(p),{key:0,onClick:e=>{return a=l,void L.value.splice(a,1);var a},title:"删除映射组"},null,8,["onClick"])):x("",!0)])),default:C((()=>[w(c,{value:e.twinClass,"onUpdate:value":a=>e.twinClass=a,style:{width:"300px","margin-bottom":"10px"},"dropdown-style":{maxHeight:"300px",overflow:"auto"},"tree-data":e.treeData,placeholder:"请选择孪生对象","tree-default-expand-all":"","show-search":"","field-names":J,"filter-tree-node":z,onFocus:e=>V(l),onChange:(e,a,t)=>{((e,a,l)=>{L.value[e].title=l,L.value[e].map.forEach((e=>{e.inter=void 0})),A(a,e)})(l,e,a)}},null,8,["value","onUpdate:value","tree-data","onFocus","onChange"]),w(f,{style:{"margin-bottom":"10px"},align:"middle"},{default:C((()=>[w(v,{span:8},{default:C((()=>a[1]||(a[1]=[D(" 服务属性字段 ")]))),_:1}),w(v,{span:6,offset:1},{default:C((()=>a[2]||(a[2]=[D(" 控件类型 ")]))),_:1}),w(v,{span:8},{default:C((()=>a[3]||(a[3]=[D(" 接入属性字段 ")]))),_:1}),w(v,{span:1})])),_:1}),(j(!0),y(g,null,_(e.map,((a,t)=>(j(),k(f,{key:a.key,style:{"margin-bottom":"10px"},align:"middle"},{default:C((()=>[w(v,{span:8},{default:C((()=>[w(K,{value:a.model,"onUpdate:value":e=>a.model=e,placeholder:"请选择",style:{width:"100%"},onChange:e=>(e=>{e.inter="",e.filterTreeData=[]})(a)},{default:C((()=>[(j(!0),y(g,null,_(P.value,(e=>(j(),k(M,{key:e.model,value:e.model,disabled:L.value[l].map.find((a=>a.model==e.model))},{default:C((()=>[D(F(e.label),1)])),_:2},1032,["value","disabled"])))),128))])),_:2},1032,["value","onUpdate:value","onChange"])])),_:2},1024),w(v,{span:6,offset:1,style:{"line-height":"36px"}},{default:C((()=>{var e;return[D(F(null==(e=P.value.filter((e=>e.model===a.model))[0])?void 0:e.type),1)]})),_:2},1024),w(v,{span:8},{default:C((()=>[w(K,{value:a.inter,"onUpdate:value":e=>a.inter=e,placeholder:"请选择属性字段",style:{width:"100%"},"allow-clear":"",disabled:!e.twinClass||!a.model,onFocus:e=>((e,a)=>{var l;let t=[];const o=[];L.value[e].map.forEach(((e,l)=>{a!==l&&e.inter&&o.push(e.inter)}));const i=null==(l=P.value.filter((l=>l.model===L.value[e].map[a].model))[0])?void 0:l.type;t=L.value[e].twinFields.filter((e=>e.type===i&&!o.includes(e.model))),L.value[e].map[a].filterTreeData=t})(l,t)},{default:C((()=>[(j(!0),y(g,null,_(a.filterTreeData,((e,a)=>(j(),k(M,{key:a,value:e.model,title:e.label},{default:C((()=>[D(F(e.label),1)])),_:2},1032,["value","title"])))),128))])),_:2},1032,["value","onUpdate:value","disabled","onFocus"])])),_:2},1024),w(v,{span:1,style:{"line-height":"36px","text-align":"center"}},{default:C((()=>[e.map.length>1?(j(),k(E(p),{key:0,title:"删除映射字段",onClick:e=>((e,a)=>{L.value[e].map.splice(a,1)})(l,t)},null,8,["onClick"])):x("",!0)])),_:2},1024)])),_:2},1024)))),128)),e.map.length<P.value.length?(j(),k(q,{key:0,type:"dashed",block:"",onClick:e=>(e=>{L.value[e].map.push({name:"",model:"",type:"",typeCode:"",filterTreeData:[],inter:void 0})})(l)},{default:C((()=>[w(S,{type:"icon-add",class:"icon-add"}),a[4]||(a[4]=D(" 添加映射字段 "))])),_:2},1032,["onClick"])):x("",!0)])),_:2},1032,["header"])))),128))])),_:1},8,["activeKey"])]),b("div",U,[w(q,{type:"primary",onClick:N},{default:C((()=>a[5]||(a[5]=[D(" 新增映射组")]))),_:1})])])}}}),[["__scopeId","data-v-fcdd9579"]]);export{O as default};
