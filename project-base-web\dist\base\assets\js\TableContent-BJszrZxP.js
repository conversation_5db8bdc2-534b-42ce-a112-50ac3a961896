import{d as e,r as t,o as a,S as n,am as s,Y as o,Z as l,b8 as u,U as c}from"./@vue-DgI1lw0Y.js";import{_ as d}from"./vue-qr-6l_NUpj8.js";import"./js-binary-schema-parser-G48GG52R.js";const r={class:"table-content keep-px"},m={class:"content"},i=d(e({__name:"TableContent",setup(e){let d=0;const i=t(0),v=t(),p=t(!1),f=()=>{p.value=!0,i.value>0?i.value=0:i.value=960,setTimeout((()=>{p.value=!1}),200)};return a((()=>{d=document.documentElement.clientWidth-37,v.value.onmousedown=e=>{const t=e.clientX,a=i.value;let n=0;document.onselectstart=function(){return!1},document.onmousemove=e=>{const s=e.clientX;n=t-s+a,n<960&&(n=960),n>d&&(n=d),i.value=n},document.onmouseup=()=>{0!==n&&(document.onmousemove=null,document.onmouseup=null,document.onselectstart=null,i.value=n)}}})),(e,t)=>(c(),n("div",r,[s("div",{class:l(["right-content-wrap",p.value?"showAnimate":""]),style:o({width:i.value+"px"})},[s("div",{ref_key:"dragBarRef",ref:v,class:"drag-bar"},null,512),s("div",m,[u(e.$slots,"default",{},void 0,!0)])],6),s("div",{class:"right-content-expand",onClick:f},[s("div",{class:l(["right-content-hand",{hide:!i.value}])},null,2)])]))}}),[["__scopeId","data-v-ca0afdbe"]]);export{i as default};
