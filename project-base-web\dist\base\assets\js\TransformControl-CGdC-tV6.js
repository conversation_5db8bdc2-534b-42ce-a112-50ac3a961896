var t=Object.defineProperty,e=(e,o,s)=>((e,o,s)=>o in e?t(e,o,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[o]=s)(e,"symbol"!=typeof o?o+"":o,s);const o=class t extends THING.Component{constructor(){super(...arguments),e(this,"prePos"),e(this,"preAngle"),e(this,"angleBtnDir"),e(this,"shiftRotateStep",45)}onAwake(){"ParticleSystem"!==this.object.type&&(this.object.helper.orientedBox.visible=!0,this.object.style.outlineColor="#135EBF")}onUpdate(){const e=this.calcBtnPos();this.object.trigger(t.TRANSFORM_CONTROL_UPDATE,{data:e})}onDestroy(){"ParticleSystem"!==this.object.type&&(this.object.helper.orientedBox.visible=!1,this.object.style.outlineColor=null)}calcBtnPos(){let t=null;const e=this.object.orientedBox,o=e.size[1]*(this.object.scale[1]>0?1:-1);if(t=this.app.camera.worldToScreen([e.center[0],e.center[1]+o/3,e.center[2]]),this.angleBtnDir){const o=this.app.camera.worldToScreen(e.center);t=THING.Math.addVector([o[0]+45,o[1]-55,0],THING.Math.scaleVector(this.angleBtnDir,60))}return t}updateCtrlObj(t){const e=this,{mode:o,shift:s,x:i,y:r}=t;switch(o){case"translateY":e.moveY(i,r);break;case"translateXZ":e.moveXZ(i,r);break;case"mirror":e.mirror();break;case"rotate":s?e.rotateYStep():e.rotateY(i,r)}}resetCtrlData(){this.prePos=null,this.preAngle=null,this.angleBtnDir=null}moveY(t,e){if(this.prePos){const o=THING.Math.getDistance(this.object.position,this.app.camera.position),s=(this.prePos[1]-e)*o*.0013;this.object.position=[this.object.position[0],this.object.position[1]+s,this.object.position[2]],this.prePos=[t,e]}else this.prePos=[t,e]}moveXZ(t,e){const o=-THING.Math.dotVector([0,1,0],[0,this.object.position[1],0]),s=this.camera.intersectPlane(t,e,[0,1,0],o);if(this.prePos)this.object.position=[s[0]-this.prePos[0],s[1]-this.prePos[1],s[2]-this.prePos[2]];else{const t=this.object.position;this.prePos=[s[0]-t[0],s[1]-t[1],s[2]-t[2]]}}rotateY(t,e){const o=this.object.orientedBox,s=o.center,i=o.size[1]/this.object.scale[1],r=this.app.camera.worldToScreen([s[0],s[1]+i/2,s[2]]),n=THING.Math.subVector([t,e,0],[r[0],r[1],0]),a=THING.Math.normalizeVector(n),c=THING.Math.getAngleBetweenVectors([0,1,0],a);if(this.preAngle){const t=THING.Math.crossVector([0,1,0],a)[2]<0?c:360-c;this.object.rotateY(t-this.preAngle),this.preAngle=t}else this.preAngle=c;this.angleBtnDir=a}rotateYStep(t,e){const o=this.object.position,s=this.object.orientedBox.size[1]/this.object.scale[1],i=this.app.camera.worldToScreen([o[0],o[1]+s/2,o[2]]),r=THING.Math.subVector([t,e,0],[i[0],i[1],0]),n=THING.Math.normalizeVector(r);let a=THING.Math.getAngleBetweenVectors([0,1,0],n);this.preAngle?(a=Math.floor(a/this.shiftRotateStep)*this.shiftRotateStep,this.object.rotateY(Math.abs(this.preAngle-a)),this.preAngle=a):this.preAngle=a,this.angleBtnDir=n}mirror(){this.object.rotateY(180)}};e(o,"TRANSFORM_CONTROL_UPDATE","transform_control_update"),e(o,"exportFunctions",["updateCtrlObj","resetCtrlData"]);let s=o;export{s as TransformControl};
