var t=Object.defineProperty,e=(e,o,s)=>((e,o,s)=>o in e?t(e,o,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[o]=s)(e,"symbol"!=typeof o?o+"":o,s);const o=class t extends THING.Component{constructor(){super(...arguments),e(this,"prePos"),e(this,"preHeight"),e(this,"prePosV3"),e(this,"preAngle"),e(this,"angleBtnDir"),e(this,"shiftRotateStep",45),e(this,"isEditor")}onAwake(){}onUpdate(){const e=this.calcBtnPos();return this.object.trigger(t.TRANSFORM_CONTROL_UPDATE,{data:e}),!0}onDestroy(){}resetCtrlData(){this.prePos=null,this.preAngle=null,this.angleBtnDir=null}calcBtnPos(){let t=null;const e=this.object.boundingBox,o=this.getTargetSize(this.object,e)*(this.object.scale[1]>0?1:-1);if(t=this.app.camera.worldToScreen([this.object.position[0],this.object.position[1]+o/3,this.object.position[2]]),this.angleBtnDir){const e=this.app.camera.worldToScreen(this.object.position),o=THING.Math.scaleVector(this.angleBtnDir,60);t=THING.Math.addVector([e[0]+45,e[1]-15,0],o)}return t}updateCtrlObj(t){const e=this,{mode:o,shift:s,x:i,y:r}=t;switch(o){case"translateY":e.moveY(i,r);break;case"translateXZ":e.moveXZ(i,r);break;case"mirror":e.mirror();break;case"rotate":s?e.rotateYStep(i,r):e.rotateY(i,r)}}moveY(t,e){if(this.prePos){const o=THING.EARTH.Utils.convertWorldToLonlat(this.object.position),s=THING.Math.getDistance(this.object.position,this.app.camera.position),i=(this.prePos[1]-e)*s*5e-4,r=o[2]+i,n=THING.EARTH.Utils.convertLonlatToWorld([o[0],o[1]],r);this.object.position=n,this.prePos=[t,e]}else this.prePos=[t,e]}moveXZ(t,e){const o=this.j3([0,1,0],[0,0,0,1]),s=-THING.Math.dotVector([0,1,0],[0,this.object.position[1],0]),i=this.camera.intersectPlane(t,e,o,s);if(this.prePos){const t=THING.EARTH.Utils.convertWorldToLonlat(this.object.position),e=THING.EARTH.Utils.convertWorldToLonlat([i[0]-this.prePos[0],i[1]-this.prePos[1],i[2]-(this.prePos[2]||0)]),o=THING.EARTH.Utils.convertLonlatToWorld([e[0],e[1]],t[2]);this.object.position=o}else{const t=this.object.position;this.prePos=[i[0]-t[0],i[1]-t[1],i[2]-t[2]]}}getTargetSize(t,e){if("GeoLine"===t.type||"GeoPolygon"===t.type)return 0;const o=e.size[1];let s=null;t.children&&t.children[0]&&(s=t.children[0].boundingBox.size[1]);let i=[];return i=s?[o,s]:[o],i.sort(((t,e)=>t-e)),i[0]}rotateY(t,e){this.prePosV3=null;const o=this.object.position,s=this.object.boundingBox,i=this.getTargetSize(this.object,s)/this.object.scale[1],r=this.object.app.camera.worldToScreen([o[0],o[1]+i/2,o[2]]),n=THING.Math.subVector([t,e,0],[r[0],r[1],0]),a=THING.Math.normalizeVector(n),h=THING.Math.getAngleBetweenVectors([0,1,0],a);if(this.preAngle){const t=THING.Math.crossVector([0,1,0],a)[2]<0?h:360-h;this.object.rotateY(t-this.preAngle),this.preAngle=t}else this.preAngle=h;this.angleBtnDir=a}rotateYStep(t,e){const o=this.object.position,s=this.object.orientedBox.size[1]/this.object.scale[1],i=this.app.camera.worldToScreen([o[0],o[1]+s/2,o[2]]),r=THING.Math.subVector([t,e,0],[i[0],i[1],0]),n=THING.Math.normalizeVector(r);let a=THING.Math.getAngleBetweenVectors([0,1,0],n);this.preAngle?(a=Math.floor(a/this.shiftRotateStep)*this.shiftRotateStep,this.object.rotateY(Math.abs(this.preAngle-a)),this.preAngle=a):this.preAngle=a,this.angleBtnDir=n}mirror(){this.object.rotateY(180)}j3(t,e){const o=t[0],s=t[1],i=t[2],r=e[0],n=e[1],a=e[2],h=e[3],c=h*o+n*i-a*s,l=h*s+a*o-r*i,p=h*i+r*s-n*o,b=-r*o-n*s-a*i,T=[];return T[0]=c*h+b*-r+l*-a-p*-n,T[1]=l*h+b*-n+p*-r-c*-a,T[2]=p*h+b*-a+c*-n-l*-r,T}};e(o,"TRANSFORM_CONTROL_UPDATE","transform_control_update"),e(o,"exportFunctions",["updateCtrlObj","resetCtrlData"]);let s=o;export{s as TransformControl};
