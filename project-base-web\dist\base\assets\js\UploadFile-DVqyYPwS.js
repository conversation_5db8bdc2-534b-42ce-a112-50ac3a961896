import{d as e,r as a,a as o,V as s,U as t,bJ as r,c as l,q as i,am as n,u as p,G as u,B as m}from"./@vue-DgI1lw0Y.js";import{a as c,u as v}from"./main-DE7o6g98.js";import{u as d}from"./attachmentManage-CasMLJka.js";import{g as j}from"./developConfig-CkJLRJ2N.js";import{s as f}from"./pinia-iScrtxv6.js";import{S as g,F as b,c as y,U as h,e as k,M as _}from"./ant-design-vue-DW0D0Hn-.js";import{a7 as x}from"./@ant-design-tBRGNTkq.js";import{_ as F}from"./vue-qr-6l_NUpj8.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const w={class:"ant-upload-drag-icon"},z=F(e({__name:"UploadFile",emits:["ok"],setup(e,{expose:F,emit:z}){const I=c(),{themeColor:O}=f(I),H=z,N=a(!1),U=a(!1),q=a(),C=o({name:"",kind:"",bucketName:""}),D=a(),E=a([]),L=async()=>{try{const e=await j({code:"PROHIBITED_UPLOAD_FILE_TYPES"});200===e.code&&(E.value=e.data)}catch(e){}},M=()=>{D.value=[],C.bucketName=""},P=e=>{const{file:a}=e;D.value=[a]},R=e=>{const a=e.name,o=a.substr(a.lastIndexOf(".")+1);return!!E.value.filter((e=>e.value===o)).length||(D.value=[],v("error",`不允许上传.${o}格式的文件`),!1)},B=()=>{U.value||(q.value.resetFields(),C.kind="",D.value=[],N.value=!1,U.value=!1)},G=a({percent:0,progressFlag:!1}),J=e=>{e&&e.loaded&&e.total&&(G.value.percent=Math.round(100*e.loaded/e.total))};return F({init:e=>{N.value=!0,L()}}),(e,a)=>{const o=h,c=k,j=y,f=b,F=g,z=_;return t(),s(z,{title:"上传文件","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:N.value,"confirm-loading":U.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[0]||(a[0]=e=>{q.value.validate().then((()=>{if(D.value&&D.value.length){U.value=!0;const e=new FormData;e.append("file",D.value[0]),e.append("bucketName","bubble"),G.value.percent=0,G.value.progressFlag=!0,d(e,J).then((e=>{U.value=!1,200===e.code?(v("success","文件上传成功"),B(),H("ok")):v("error",e.message),G.value.percent=0,G.value.progressFlag=!1})).catch((()=>{U.value=!1,G.value.percent=0,G.value.progressFlag=!1}))}})).catch((e=>{U.value=!1}))}),onCancel:B},{default:r((()=>[l(F,{spinning:U.value},{default:r((()=>[l(f,{ref_key:"formRef",ref:q,model:C,"label-align":"left"},{default:r((()=>[l(j,{label:" "},{default:r((()=>[l(o,{"file-list":D.value,"before-upload":R,"custom-request":P,onRemove:M},{default:r((()=>[n("p",w,[l(p(x),{style:{"font-size":"40px",color:"var(--upload-icon-color)"}})]),a[1]||(a[1]=n("p",{style:{"font-size":"14px"}},[u("将文件拖至此处，或点击 "),n("a",null,"上传数据")],-1))])),_:1},8,["file-list"]),i(n("div",null,[l(c,{percent:G.value.percent,size:"small","stroke-color":{from:"#108ee9",to:p(O)}},null,8,["percent","stroke-color"])],512),[[m,G.value.progressFlag]])])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-cee6b4a0"]]);export{z as default};
