import{u as e}from"./main-DE7o6g98.js";import{I as a,W as s,_ as i,B as t}from"./ant-design-vue-DW0D0Hn-.js";import{P as o}from"./@ant-design-tBRGNTkq.js";import{d as r,r as l,a as n,o as d,l as p,w as m,S as u,U as c,c as j,am as v,bJ as y,F as f,bk as k,al as b,G as w,u as g}from"./@vue-DgI1lw0Y.js";import{_ as h}from"./vue-qr-6l_NUpj8.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./pinia-iScrtxv6.js";import"./mitt-CNZ6avp8.js";import"./vue3-cookies-ll55Epyr.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./axios-ChCdAMPF.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./@ctrl-B2IeE8ye.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const x={key:0},J={key:1},_={key:1,class:"table-actions"},C=["onClick"],I=["onClick"],$=["onClick"],A={class:"edd-new-line"},z=h(r({__name:"editReturnJson",props:{sourceJson:{type:Array,default:()=>[{name:"123",data:"456"}]}},setup(r,{expose:h}){const z=r,U=[{title:"参数名",dataIndex:"name"},{title:"参数值",dataIndex:"value"},{title:"操作",dataIndex:"selfEdit",width:165}],G=l([]),L=n({}),N=n({});function O(){if(Array.isArray(z.sourceJson))G.value=z.sourceJson;else try{G.value=JSON.parse(z.sourceJson)}catch{G.value=[]}}d((()=>{O()})),p((()=>{G.value=[]})),m((()=>z.sourceJson),(e=>{(e instanceof Array&&e.length||e)&&O()}),{deep:!0});const S=()=>{const e={name:"",value:"",id:Math.random()},a=G.value.length;G.value[a]=e,L[e.id]=!0,N[e.id]={...e}};return h({getData:()=>G.value.map((e=>({name:e.name,value:e.value})))}),(r,l)=>{const n=a,d=s,p=i,m=t;return c(),u(f,null,[j(p,{class:"table newJsonTab",scroll:{y:"120px"},pagination:!1,"row-key":e=>e.id,size:"small",columns:U,"data-source":G.value},{bodyCell:y((({column:a,record:s})=>["操作"!==a.title?(c(),u(f,{key:0},[L[s.id]?(c(),u("div",x,[j(n,{value:N[s.id][`${a.dataIndex}`],"onUpdate:value":e=>N[s.id][`${a.dataIndex}`]=e,placeholder:`请输入${a.title}`},null,8,["value","onUpdate:value","placeholder"])])):(c(),u("div",J,k(s[`${a.dataIndex}`]),1))],64)):(c(),u("div",_,[L[s.id]?(c(),u("a",{key:1,onClick:a=>function(a){N[a.id].name?N[a.id].value?(G.value=G.value.map((e=>e.id===a.id?N[a.id]:e)),delete L[a.id]):e("warning","请输入参数值"):e("warning","请输入参数名")}(s)},"保存",8,I)):(c(),u("a",{key:0,onClick:e=>function(e){L[e.id]=!0,N[e.id]={...e}}(s)},"编辑",8,C)),j(d,{type:"vertical"}),L[s.id]?b("",!0):(c(),u("a",{key:2,onClick:e=>function(e){G.value=G.value.filter((a=>a.id!=e.id)),delete N[e.id]}(s)},"删除",8,$))]))])),_:1},8,["row-key","data-source"]),v("div",A,[j(m,{type:"text",class:"actionList",onClick:S},{icon:y((()=>[j(g(o))])),default:y((()=>[l[0]||(l[0]=w(" 新增参数 "))])),_:1})])],64)}}}),[["__scopeId","data-v-bdcc84ad"]]);export{z as default};
