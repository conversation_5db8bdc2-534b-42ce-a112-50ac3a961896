import{d as t}from"./pinia-iScrtxv6.js";const a=t({id:"mapStore",state:()=>({twinProp:{scale:[1,1,1],offsetHeight:0,size:[0,0,0],angles:[0,0,0],customColor:"rgba(220,20,60,1)"},showEditPanel:!1,twinData:{},defaultData:{},markFlag:!1,deleteThing:+new Date}),actions:{updateTwinProp(t){this.twinProp=t},updateShowEditPanel(t){this.showEditPanel=t},updateTwinData(t){this.twinData=t},updateDefaultData(t){this.defaultData=t},updateMarkFlag(t){this.markFlag=t},updateDeleteThing(t){this.deleteThing=t}}});export{a as u};
