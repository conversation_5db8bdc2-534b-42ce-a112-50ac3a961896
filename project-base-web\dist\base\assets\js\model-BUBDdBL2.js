import{s as o}from"./main-DE7o6g98.js";const e="/modelx/model-lib/tree",l="/modelx/model-lib/sync-by-id",t="/modelx/model-lib/sync-classify",d="/modelx/model-lib/page",a="/modelx/model-lib/offline-project-upload",r="/modelx/model-lib/offline-basic-upload",n="/modelx/model-lib/offline-basic-upload-by-classify",s="/modelx/model-lib/detail",m="/modelx/model-lib/resource-page",u="/modelx/model-lib/online-all-upload",i="/modelx/model-lib/download",c="/modelx/model-lib/download-basic",p="/modelx/model-lib/online-texture-upload",f="/modelx/model-lib/sync-by-classify",b="/modelx/model-lib/download-hard",h="/modelx/model-lib/delete",x="/modelx/model-lib/classify-add",y="/modelx/model-lib/classify-edit",g="/modelx/model-lib/classify-delete";function j(e){return o({url:b,method:"post",data:e})}function w(e){return o({url:s,method:"get",params:e})}function P(l){return o({url:e,method:"get",params:l})}function T(e){return o({url:l,method:"post",params:e,accept:"*/*"})}function U(e){return o({url:f,method:"post",data:e})}function k(e){return o({url:t,method:"post",data:e})}function D(e){return o({url:u,method:"get",params:e})}function q(e){return o({url:d,method:"get",params:e})}function v(e){return o({url:h,method:"post",data:e})}function z(e,l=!1,t){return o({url:a,method:"post",data:e,accept:"*/*",responseType:l?"blob":"json",onUploadProgress(o){t(o)}})}function A(e,l){return o({url:r,method:"post",data:e,accept:"*/*",onUploadProgress(o){l(o)}})}function B(e,l){return o({url:n,method:"post",data:e,accept:"*/*",onUploadProgress(o){l(o)}})}function C(e){return o({url:m,method:"get",params:e})}function E(e){return o({url:i,method:"post",data:e,responseType:"blob"})}function F(e){return o({url:c,method:"post",data:e,responseType:"blob"})}function G(e){return o({url:p,method:"post",data:e})}function H(e){return o({url:x,method:"post",data:e})}function I(e){return o({url:y,method:"post",data:e})}function J(e){return o({url:g,method:"post",data:e})}export{F as D,H as a,v as b,E as c,J as d,I as e,T as f,P as g,C as h,G as i,j,w as k,A as l,U as m,B as n,z as o,k as s,q as t,D as u};
