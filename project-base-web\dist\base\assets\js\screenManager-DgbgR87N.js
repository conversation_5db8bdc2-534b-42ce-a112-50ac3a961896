var e=Object.defineProperty,a=(a,t,r)=>((a,t,r)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[t]=r)(a,"symbol"!=typeof t?t+"":t,r);class t{constructor(){a(this,"param"),a(this,"chartIns")}async createScreen(e){const a=this;this.destroyScreen();const{container:t,id:r,hide3D:c,hideCanvasBackground:s,offline:n,baseUrl:i,dataSource:h,cb:o}=e;this.param=e;try{a.chartIns=await THING.CHARTS.Utils.loadBundle(i,{container:t,id:r,offline:!!n,hide3D:c,hideCanvasBackground:s,baseUrl:i,dataSource:h,complete:()=>{o&&o(a.chartIns)}})}catch(l){o&&o()}}changeScreen(e){this.destroyScreen(),this.param=Object.assign(this.param,e),this.createScreen(this.param)}destroyScreen(){const e=this;try{e.chartIns&&(e.chartIns.destroy(),e.chartIns=null)}catch(a){e.chartIns=null}}}export{t as S};
