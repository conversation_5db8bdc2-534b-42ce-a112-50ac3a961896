import{s as t}from"./main-DE7o6g98.js";const e="/twinx/twin-coalesce/page",n="/twinx/twin-coalesce/add",a="/twinx/twin-coalesce/delete",o="/twinx/twin-coalesce/edit",r="/twinx/twin-xxv/list-twin",s="/twinx/jrm/get",i="/systemx/sys-file-info/download";function u(n){return t({url:e,method:"get",params:n})}function d(e){return t({url:n,method:"post",data:e})}function l(e){return t({url:a,method:"post",data:e})}function c(e){return t({url:o,method:"post",data:e})}function m(e){return t({url:r,method:"get",params:e})}function w(e){return t({url:s,method:"post",data:e})}function p(e){return t({url:i,method:"get",params:e,responseType:"blob"})}export{d as a,p as b,l as d,c as e,u as g,m as l,w as q};
