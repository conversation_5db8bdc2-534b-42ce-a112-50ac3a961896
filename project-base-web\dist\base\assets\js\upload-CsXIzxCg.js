import{d as e,r as a,V as l,bJ as s,al as t,S as o,am as u,c as i,G as r,b9 as n,Z as p,bk as d,q as v,B as c,u as m,U as f}from"./@vue-DgI1lw0Y.js";import{a as j}from"./axios-ChCdAMPF.js";import{a as g,x as y,aj as x,e as _,u as k,ak as b}from"./main-DE7o6g98.js";import{u as h}from"./vue3-cookies-ll55Epyr.js";import{s as w}from"./pinia-iScrtxv6.js";import{c as C,k as F,R as S,f as z,U as G,e as M,B as T,i as $,M as A}from"./ant-design-vue-DW0D0Hn-.js";import{_ as B}from"./vue-qr-6l_NUpj8.js";import"./@ant-design-tBRGNTkq.js";import"./@ctrl-B2IeE8ye.js";import"./wujie-vue3-BaOa_gE7.js";import"./wujie-LbeE4pDA.js";import"./@babel-B4rXMRun.js";import"./mitt-CNZ6avp8.js";import"./vue-router-DmgHFAdP.js";import"./nprogress-DfxabVLP.js";import"./vue3-ts-jsoneditor-C_C2tCrf.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-B4JHbcDU.js";import"./crypto-js-HwOCoVPb.js";import"./dayjs-D9wJ8dSB.js";/* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-FcETe_Th.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const O={key:2,class:"download"},q={class:"explain"},E={style:{"padding-left":"3px",color:"#73d13d"}},J={key:3,class:"download"},U={class:"upload"},W={class:"ant-upload-drag-icon"},I={style:{"margin-top":"70px"}},P=B(e({__name:"upload",emits:["ok"],setup(e,{expose:B,emit:P}){const R=g(),{themeColor:K}=w(R),N=j.defaults.headers.common.Tenant,D=a(!1),H=a(!1),L=a(!1),V=a("add"),X=a(0),Z=a({}),Q=a("wgs84_position"),Y=a("gcj02_position"),ee=a(["wgs84_position","gcj02_position"]),ae=a({}),le=a([]),{cookies:se}=h(),te=P,oe=()=>{Y.value=ee.value.filter((e=>e!==Q.value)).join(",")},ue=()=>{0===X.value&&(x({id:Z.value.id,excludeCoordinateSys:Y.value}).then((e=>{_(e)})),X.value=3,ie())},ie=()=>{setTimeout((()=>{X.value-=1,X.value>0&&ie()}),1e3)},re=e=>{const{file:a}=e;le.value[0]=a},ne=()=>{le.value=[]},pe=()=>{le.value=[],H.value=!1},de=a({percent:0,progressFlag:!1}),ve=e=>{e&&e.loaded&&e.total&&(de.value.percent=Math.round(100*e.loaded/e.total))},ce=()=>{if(!le.value.length)return void k("warning","请选择上传的文件");const e=le.value[0],a=new FormData;a.append("file",e),a.append("id",Z.value.id),a.append("type",V.value),"MAP"===Z.value.level&&a.append("positionType",Q.value),L.value=!0,de.value.percent=0,de.value.progressFlag=!0,b(a,ve).then((a=>{V.value,k("success",`导入${e.name}成功，共更新${a.data}条数据`),H.value=!1,te("ok")})).finally((()=>{le.value=[],L.value=!1,de.value.percent=0,de.value.progressFlag=!1}))},me=e=>{const a=e.name,l=a.substr(a.lastIndexOf(".")+1),s=["xls","xlsx"].includes(l);return s||(le.value=[],k("error","仅支持上传.xls,.xlsx格式的文件")),s};return B({init:e=>{Z.value=e,D.value="1476825472376508418"===e.groupId,D.value&&(V.value="update"),le.value=[],ae.value={Authorization:`Bearer ${se.get(`ACCESS_TOKEN_${N}`)}`,Tenant:N,"X-Security-FreshToken":y()},H.value=!0}}),(e,a)=>{const j=S,g=F,y=C,x=n("question-circle-outlined"),_=z,k=n("ExclamationCircleFilled"),b=n("FileExcelFilled"),h=G,w=M,B=T,P=$,R=A;return f(),l(R,{"wrap-class-name":"cus-modal",title:"导入孪生对象数据",open:H.value,"mask-closable":!1,"confirm-loading":L.value,"ok-text":"确认","cancel-text":"取消",onCancel:pe,onOk:ce},{footer:s((()=>[u("div",I,[i(B,{key:"back",onClick:pe},{default:s((()=>a[14]||(a[14]=[r("取消")]))),_:1}),"MAP"===Z.value.level?(f(),l(P,{key:0,placement:"topRight",title:"上传孪生体数据时将以您选择的坐标系为准进行校验，请确认您填写的坐标系与选择的坐标系一致","ok-text":"确认","cancel-text":"取消","overlay-style":{maxWidth:"400px"},onConfirm:ce},{default:s((()=>[i(B,{key:"submit",type:"primary"},{default:s((()=>a[15]||(a[15]=[r(" 确定 ")]))),_:1})])),_:1})):(f(),l(B,{key:"submit",type:"primary",onClick:ce},{default:s((()=>a[16]||(a[16]=[r(" 确定 ")]))),_:1}))])])),default:s((()=>[D.value?t("",!0):(f(),l(y,{key:0,"label-col":{span:6},"wrapper-col":{span:18},"label-align":"left"},{label:s((()=>a[2]||(a[2]=[u("span",null,"请选择您的操作：",-1)]))),default:s((()=>[i(g,{value:V.value,"onUpdate:value":a[0]||(a[0]=e=>V.value=e),"default-value":V.value},{default:s((()=>[i(j,{value:"add"},{default:s((()=>a[3]||(a[3]=[r("新增")]))),_:1}),i(j,{value:"update"},{default:s((()=>a[4]||(a[4]=[r("更新")]))),_:1})])),_:1},8,["value","default-value"])])),_:1})),"MAP"===Z.value.level?(f(),l(y,{key:1,"label-col":{span:6},"wrapper-col":{span:18},"label-align":"left"},{label:s((()=>a[5]||(a[5]=[u("span",null," 请选择坐标系：",-1)]))),default:s((()=>[i(g,{value:Q.value,"onUpdate:value":a[1]||(a[1]=e=>Q.value=e),"default-value":Q.value,onChange:oe},{default:s((()=>[i(j,{value:"wgs84_position"},{default:s((()=>a[6]||(a[6]=[r("WGS84")]))),_:1}),i(j,{value:"gcj02_position"},{default:s((()=>[a[8]||(a[8]=r(" GCJ02 ")),i(_,null,{title:s((()=>a[7]||(a[7]=[u("span",null,"上传孪生体数据时将以您选择的坐标系为准进行校验，请准确填写孪生体的坐标数据；",-1),u("br",null,null,-1),u("span",null,"WGS84坐标系常用于天地图、OpenStreetMap等；",-1),u("br",null,null,-1),u("span",null,"GCJ02坐标系常用于高德、Google中国地图等；",-1)]))),default:s((()=>[i(x,{style:{"margin-right":"3px",color:"#ef7b1a"}})])),_:1})])),_:1})])),_:1},8,["value","default-value"])])),_:1})):t("",!0),"add"===V.value?(f(),o("div",O,[i(k,{style:{fontSize:"20px",color:"#4878FB"}}),u("span",q,[a[10]||(a[10]=r(" 请下载")),u("span",{class:p(0!==X.value?"disablecls":""),onClick:ue},[u("span",E,d(0===X.value?"":`${X.value}s `),1),a[9]||(a[9]=r("孪生体数据导入模板"))],2),a[11]||(a[11]=r("，按格式修改后导入。"))])])):t("",!0),"update"===V.value?(f(),o("div",J,[i(k,{style:{fontSize:"20px",color:"#4878FB"}}),a[12]||(a[12]=u("span",{class:"explain"},"请上传您从系统导出的当前孪生体数据文件",-1))])):t("",!0),u("div",U,[i(h,{name:"file",multiple:!1,accept:".xls,.xlsx","show-upload-list":!0,"custom-request":re,"before-upload":me,"file-list":le.value,headers:ae.value,onRemove:ne},{default:s((()=>[u("p",W,[i(b)]),a[13]||(a[13]=u("p",{style:{"font-size":"14px"}},[r("将文件拖至此处，或点击 "),u("a",null,"上传数据")],-1))])),_:1},8,["file-list","headers"]),v(u("div",null,[i(w,{percent:de.value.percent,size:"small","stroke-color":{from:"#108ee9",to:m(K)}},null,8,["percent","stroke-color"])],512),[[c,de.value.progressFlag]])])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-9c62f186"]]);export{P as default};
