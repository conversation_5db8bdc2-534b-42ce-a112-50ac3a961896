/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    AddEditForm: typeof import('./../src/views/projectManage/campusPreview/components/twinDataList/AddEditForm.vue')['default']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AFormItemRest: typeof import('ant-design-vue/es')['FormItemRest']
    AImage: typeof import('ant-design-vue/es')['Image']
    AImagePreviewGroup: typeof import('ant-design-vue/es')['ImagePreviewGroup']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputGroup: typeof import('ant-design-vue/es')['InputGroup']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    APopover: typeof import('ant-design-vue/es')['Popover']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASlider: typeof import('ant-design-vue/es')['Slider']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AssociatedTwins: typeof import('./../src/views/projectManage/campusPreview/components/twinDataList/AssociatedTwins.vue')['default']
    AStep: typeof import('ant-design-vue/es')['Step']
    ASteps: typeof import('ant-design-vue/es')['Steps']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    ATreeSelect: typeof import('ant-design-vue/es')['TreeSelect']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    AUploadDragger: typeof import('ant-design-vue/es')['UploadDragger']
    BuildingMarker: typeof import('./../src/components/BuildingMarker.vue')['default']
    ChartPreview: typeof import('./../src/components/ChartPreview.vue')['default']
    ChildScencePosPanel: typeof import('./../src/views/projectManage/campusPreview/components/childScencePosPanel.vue')['default']
    CollapseList: typeof import('./../src/components/CollapseList.vue')['default']
    CommonDowmIcon: typeof import('./../src/components/commonDowmIcon.vue')['default']
    ConditionBox: typeof import('./../src/views/projectManage/campusPreview/components/twinDataList/ConditionBox.vue')['default']
    CustomContextMenu: typeof import('./../src/components/CustomContextMenu.vue')['default']
    DelayedFileUpload: typeof import('./../src/components/DelayedFileUpload.vue')['default']
    DelayedImageUpload: typeof import('./../src/components/DelayedImageUpload.vue')['default']
    DepartGroup: typeof import('./../src/components/DepartGroup.vue')['default']
    DictGroup: typeof import('./../src/components/DictGroup.vue')['default']
    Header: typeof import('./../src/components/Header.vue')['default']
    JsonEdit: typeof import('./../src/components/JsonEdit.vue')['default']
    MapGroup: typeof import('./../src/components/MapGroup.vue')['default']
    ModelGroup: typeof import('./../src/components/ModelGroup.vue')['default']
    NoticeIcon: typeof import('./../src/components/NoticeIcon.vue')['default']
    PerformanceAnalysis: typeof import('./../src/views/projectManage/campusPreview/components/PerformanceAnalysis.vue')['default']
    PreviewAndDownload: typeof import('./../src/views/projectManage/campusPreview/components/twinDataList/PreviewAndDownload.vue')['default']
    PreviewForm: typeof import('./../src/views/projectManage/campusPreview/components/twinDataList/PreviewForm.vue')['default']
    Reletive: typeof import('./../src/views/projectManage/campusPreview/components/twinDataList/Reletive.vue')['default']
    RemoteSet: typeof import('./../src/components/formExtend/RemoteSet.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScreenPreview: typeof import('./../src/components/ScreenPreview.vue')['default']
    SecondMenu: typeof import('./../src/components/SecondMenu.vue')['default']
    SelectBubble: typeof import('./../src/components/SelectBubble.vue')['default']
    SelectColor: typeof import('./../src/components/SelectColor.vue')['default']
    SelectModel: typeof import('./../src/components/SelectModel.vue')['default']
    SetEffect: typeof import('./../src/views/projectManage/campusPreview/components/SetEffect.vue')['default']
    SyncClassify: typeof import('./../src/components/SyncClassify.vue')['default']
    ThirdMenu: typeof import('./../src/components/ThirdMenu.vue')['default']
    TwinDataList: typeof import('./../src/views/projectManage/campusPreview/components/twinDataList/index.vue')['default']
    TwinObjGroup: typeof import('./../src/components/TwinObjGroup.vue')['default']
  }
}
