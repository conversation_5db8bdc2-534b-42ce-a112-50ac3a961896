!function(){"use strict";const e=new class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout((()=>{if(e.stack){if(s.isErrorNoTelemetry(e))throw new s(e.message+"\n\n"+e.stack);throw new Error(e.message+"\n\n"+e.stack)}throw e}),0)}}emit(e){this.listeners.forEach((t=>{t(e)}))}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}};function t(t){(function(e){if(e instanceof r)return!0;return e instanceof Error&&e.name===i&&e.message===i})(t)||e.onUnexpectedError(t)}function n(e){if(e instanceof Error){const{name:t,message:n}=e;return{$isError:!0,name:t,message:n,stack:e.stacktrace||e.stack,noTelemetry:s.isErrorNoTelemetry(e)}}return e}const i="Canceled";class r extends Error{constructor(){super(i),this.name=this.message}}class s extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof s)return e;const t=new s;return t.message=e.message,t.stack=e.stack,t}static isErrorNoTelemetry(e){return"CodeExpectedError"===e.name}}class o extends Error{constructor(e){super(e||"An unexpected bug occurred."),Object.setPrototypeOf(this,o.prototype)}}function a(e,t){const n=this;let i,r=!1;return function(){return r||(r=!0,i=e.apply(n,arguments)),i}}var l;function u(e){if(l.is(e)){const n=[];for(const i of e)if(i)try{i.dispose()}catch(t){n.push(t)}if(1===n.length)throw n[0];if(n.length>1)throw new AggregateError(n,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}if(e)return e.dispose(),e}function h(e){return{dispose:a((()=>{e()}))}}!function(e){function t(e){return e&&"object"==typeof e&&"function"==typeof e[Symbol.iterator]}e.is=t;const n=Object.freeze([]);function*i(e){yield e}e.empty=function(){return n},e.single=i,e.wrap=function(e){return t(e)?e:i(e)},e.from=function(e){return e||n},e.reverse=function*(e){for(let t=e.length-1;t>=0;t--)yield e[t]},e.isEmpty=function(e){return!e||!0===e[Symbol.iterator]().next().done},e.first=function(e){return e[Symbol.iterator]().next().value},e.some=function(e,t){for(const n of e)if(t(n))return!0;return!1},e.find=function(e,t){for(const n of e)if(t(n))return n},e.filter=function*(e,t){for(const n of e)t(n)&&(yield n)},e.map=function*(e,t){let n=0;for(const i of e)yield t(i,n++)},e.concat=function*(...e){for(const t of e)yield*t},e.reduce=function(e,t,n){let i=n;for(const r of e)i=t(i,r);return i},e.slice=function*(e,t,n=e.length){for(t<0&&(t+=e.length),n<0?n+=e.length:n>e.length&&(n=e.length);t<n;t++)yield e[t]},e.consume=function(t,n=Number.POSITIVE_INFINITY){const i=[];if(0===n)return[i,t];const r=t[Symbol.iterator]();for(let s=0;s<n;s++){const t=r.next();if(t.done)return[i,e.empty()];i.push(t.value)}return[i,{[Symbol.iterator]:()=>r}]},e.asyncToArray=async function(e){const t=[];for await(const n of e)t.push(n);return Promise.resolve(t)}}(l||(l={}));class c{constructor(){this._toDispose=new Set,this._isDisposed=!1}dispose(){this._isDisposed||(this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(0!==this._toDispose.size)try{u(this._toDispose)}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return this._isDisposed?c.DISABLE_DISPOSED_WARNING:this._toDispose.add(e),e}deleteAndLeak(e){e&&this._toDispose.has(e)&&this._toDispose.delete(e)}}c.DISABLE_DISPOSED_WARNING=!1;class d{constructor(){this._store=new c,this._store}dispose(){this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}}d.None=Object.freeze({dispose(){}});class g{constructor(e){this.element=e,this.next=g.Undefined,this.prev=g.Undefined}}g.Undefined=new g(void 0);class m{constructor(){this._first=g.Undefined,this._last=g.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===g.Undefined}clear(){let e=this._first;for(;e!==g.Undefined;){const t=e.next;e.prev=g.Undefined,e.next=g.Undefined,e=t}this._first=g.Undefined,this._last=g.Undefined,this._size=0}unshift(e){return this._insert(e,!1)}push(e){return this._insert(e,!0)}_insert(e,t){const n=new g(e);if(this._first===g.Undefined)this._first=n,this._last=n;else if(t){const e=this._last;this._last=n,n.prev=e,e.next=n}else{const e=this._first;this._first=n,n.next=e,e.prev=n}this._size+=1;let i=!1;return()=>{i||(i=!0,this._remove(n))}}shift(){if(this._first!==g.Undefined){const e=this._first.element;return this._remove(this._first),e}}pop(){if(this._last!==g.Undefined){const e=this._last.element;return this._remove(this._last),e}}_remove(e){if(e.prev!==g.Undefined&&e.next!==g.Undefined){const t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===g.Undefined&&e.next===g.Undefined?(this._first=g.Undefined,this._last=g.Undefined):e.next===g.Undefined?(this._last=this._last.prev,this._last.next=g.Undefined):e.prev===g.Undefined&&(this._first=this._first.next,this._first.prev=g.Undefined);this._size-=1}*[Symbol.iterator](){let e=this._first;for(;e!==g.Undefined;)yield e.element,e=e.next}}const f=globalThis.performance&&"function"==typeof globalThis.performance.now;class p{static create(e){return new p(e)}constructor(e){this._now=f&&!1===e?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}elapsed(){return-1!==this._stopTime?this._stopTime-this._startTime:this._now()-this._startTime}}var b;!function(e){function t(e){return(t,n=null,i)=>{let r,s=!1;return r=e((e=>{if(!s)return r?r.dispose():s=!0,t.call(n,e)}),null,i),s&&r.dispose(),r}}function n(e,t,n){return r(((n,i=null,r)=>e((e=>n.call(i,t(e))),null,r)),n)}function i(e,t,n){return r(((n,i=null,r)=>e((e=>t(e)&&n.call(i,e)),null,r)),n)}function r(e,t){let n;const i=new L({onWillAddFirstListener(){n=e(i.fire,i)},onDidRemoveLastListener(){null==n||n.dispose()}});return null==t||t.add(i),i.event}function s(e,t,n=100,i=!1,r=!1,s,o){let a,l,u,h,c=0;const d=new L({leakWarningThreshold:s,onWillAddFirstListener(){a=e((e=>{c++,l=t(l,e),i&&!u&&(d.fire(l),l=void 0),h=()=>{const e=l;l=void 0,u=void 0,(!i||c>1)&&d.fire(e),c=0},"number"==typeof n?(clearTimeout(u),u=setTimeout(h,n)):void 0===u&&(u=0,queueMicrotask(h))}))},onWillRemoveListener(){r&&c>0&&(null==h||h())},onDidRemoveLastListener(){h=void 0,a.dispose()}});return null==o||o.add(d),d.event}e.None=()=>d.None,e.defer=function(e,t){return s(e,(()=>{}),0,void 0,!0,void 0,t)},e.once=t,e.map=n,e.forEach=function(e,t,n){return r(((n,i=null,r)=>e((e=>{t(e),n.call(i,e)}),null,r)),n)},e.filter=i,e.signal=function(e){return e},e.any=function(...e){return(t,n=null,i)=>{const r=function(...e){return h((()=>u(e)))}(...e.map((e=>e((e=>t.call(n,e))))));return function(e,t){t instanceof Array?t.push(e):t&&t.add(e);return e}(r,i)}},e.reduce=function(e,t,i,r){let s=i;return n(e,(e=>(s=t(s,e),s)),r)},e.debounce=s,e.accumulate=function(t,n=0,i){return e.debounce(t,((e,t)=>e?(e.push(t),e):[t]),n,void 0,!0,void 0,i)},e.latch=function(e,t=(e,t)=>e===t,n){let r,s=!0;return i(e,(e=>{const n=s||!t(e,r);return s=!1,r=e,n}),n)},e.split=function(t,n,i){return[e.filter(t,n,i),e.filter(t,(e=>!n(e)),i)]},e.buffer=function(e,t=!1,n=[],i){let r=n.slice(),s=e((e=>{r?r.push(e):a.fire(e)}));i&&i.add(s);const o=()=>{null==r||r.forEach((e=>a.fire(e))),r=null},a=new L({onWillAddFirstListener(){s||(s=e((e=>a.fire(e))),i&&i.add(s))},onDidAddFirstListener(){r&&(t?setTimeout(o):o())},onDidRemoveLastListener(){s&&s.dispose(),s=null}});return i&&i.add(a),a.event},e.chain=function(e,t){return(n,i,r)=>{const s=t(new a);return e((function(e){const t=s.evaluate(e);t!==o&&n.call(i,t)}),void 0,r)}};const o=Symbol("HaltChainable");class a{constructor(){this.steps=[]}map(e){return this.steps.push(e),this}forEach(e){return this.steps.push((t=>(e(t),t))),this}filter(e){return this.steps.push((t=>e(t)?t:o)),this}reduce(e,t){let n=t;return this.steps.push((t=>(n=e(n,t),n))),this}latch(e=(e,t)=>e===t){let t,n=!0;return this.steps.push((i=>{const r=n||!e(i,t);return n=!1,t=i,r?i:o})),this}evaluate(e){for(const t of this.steps)if((e=t(e))===o)break;return e}}e.fromNodeEventEmitter=function(e,t,n=e=>e){const i=(...e)=>r.fire(n(...e)),r=new L({onWillAddFirstListener:()=>e.on(t,i),onDidRemoveLastListener:()=>e.removeListener(t,i)});return r.event},e.fromDOMEventEmitter=function(e,t,n=e=>e){const i=(...e)=>r.fire(n(...e)),r=new L({onWillAddFirstListener:()=>e.addEventListener(t,i),onDidRemoveLastListener:()=>e.removeEventListener(t,i)});return r.event},e.toPromise=function(e){return new Promise((n=>t(e)(n)))},e.fromPromise=function(e){const t=new L;return e.then((e=>{t.fire(e)}),(()=>{t.fire(void 0)})).finally((()=>{t.dispose()})),t.event},e.runAndSubscribe=function(e,t,n){return t(n),e((e=>t(e)))};class l{constructor(e,t){this._observable=e,this._counter=0,this._hasChanged=!1;const n={onWillAddFirstListener:()=>{e.addObserver(this)},onDidRemoveLastListener:()=>{e.removeObserver(this)}};this.emitter=new L(n),t&&t.add(this.emitter)}beginUpdate(e){this._counter++}handlePossibleChange(e){}handleChange(e,t){this._hasChanged=!0}endUpdate(e){this._counter--,0===this._counter&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}e.fromObservable=function(e,t){return new l(e,t).emitter.event},e.fromObservableLight=function(e){return(t,n,i)=>{let r=0,s=!1;const o={beginUpdate(){r++},endUpdate(){r--,0===r&&(e.reportChanges(),s&&(s=!1,t.call(n)))},handlePossibleChange(){},handleChange(){s=!0}};e.addObserver(o),e.reportChanges();const a={dispose(){e.removeObserver(o)}};return i instanceof c?i.add(a):Array.isArray(i)&&i.push(a),a}}}(b||(b={}));class _{constructor(e){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${e}_${_._idPool++}`,_.all.add(this)}start(e){this._stopWatch=new p,this.listenerCount=e}stop(){if(this._stopWatch){const e=this._stopWatch.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this._stopWatch=void 0}}}_.all=new Set,_._idPool=0;class C{constructor(e,t=Math.random().toString(18).slice(2,5)){this.threshold=e,this.name=t,this._warnCountdown=0}dispose(){var e;null===(e=this._stacks)||void 0===e||e.clear()}check(e,t){const n=this.threshold;if(n<=0||t<n)return;this._stacks||(this._stacks=new Map);const i=this._stacks.get(e.value)||0;if(this._stacks.set(e.value,i+1),this._warnCountdown-=1,this._warnCountdown<=0){let e;this._warnCountdown=.5*n;let t=0;for(const[n,i]of this._stacks)(!e||t<i)&&(e=n,t=i)}return()=>{const t=this._stacks.get(e.value)||0;this._stacks.set(e.value,t-1)}}}class y{static create(){var e;return new y(null!==(e=(new Error).stack)&&void 0!==e?e:"")}constructor(e){this.value=e}print(){}}class v{constructor(e){this.value=e}}class L{constructor(e){var t,n,i,r,s;this._size=0,this._options=e,this._leakageMon=(null===(t=this._options)||void 0===t?void 0:t.leakWarningThreshold)?new C(null!==(i=null===(n=this._options)||void 0===n?void 0:n.leakWarningThreshold)&&void 0!==i?i:-1):void 0,this._perfMon=(null===(r=this._options)||void 0===r?void 0:r._profName)?new _(this._options._profName):void 0,this._deliveryQueue=null===(s=this._options)||void 0===s?void 0:s.deliveryQueue}dispose(){var e,t,n,i;this._disposed||(this._disposed=!0,(null===(e=this._deliveryQueue)||void 0===e?void 0:e.current)===this&&this._deliveryQueue.reset(),this._listeners&&(this._listeners=void 0,this._size=0),null===(n=null===(t=this._options)||void 0===t?void 0:t.onDidRemoveLastListener)||void 0===n||n.call(t),null===(i=this._leakageMon)||void 0===i||i.dispose())}get event(){var e;return null!==(e=this._event)&&void 0!==e||(this._event=(e,t,n)=>{var i,r,s,o,a;if(this._leakageMon&&this._size>3*this._leakageMon.threshold)return d.None;if(this._disposed)return d.None;t&&(e=e.bind(t));const l=new v(e);let u;this._leakageMon&&this._size>=Math.ceil(.2*this._leakageMon.threshold)&&(l.stack=y.create(),u=this._leakageMon.check(l.stack,this._size+1)),this._listeners?this._listeners instanceof v?(null!==(a=this._deliveryQueue)&&void 0!==a||(this._deliveryQueue=new w),this._listeners=[this._listeners,l]):this._listeners.push(l):(null===(r=null===(i=this._options)||void 0===i?void 0:i.onWillAddFirstListener)||void 0===r||r.call(i,this),this._listeners=l,null===(o=null===(s=this._options)||void 0===s?void 0:s.onDidAddFirstListener)||void 0===o||o.call(s,this)),this._size++;const g=h((()=>{null==u||u(),this._removeListener(l)}));return n instanceof c?n.add(g):Array.isArray(n)&&n.push(g),g}),this._event}_removeListener(e){var t,n,i,r;if(null===(n=null===(t=this._options)||void 0===t?void 0:t.onWillRemoveListener)||void 0===n||n.call(t,this),!this._listeners)return;if(1===this._size)return this._listeners=void 0,null===(r=null===(i=this._options)||void 0===i?void 0:i.onDidRemoveLastListener)||void 0===r||r.call(i,this),void(this._size=0);const s=this._listeners,o=s.indexOf(e);if(-1===o)throw new Error("Attempted to dispose unknown listener");this._size--,s[o]=void 0;const a=this._deliveryQueue.current===this;if(2*this._size<=s.length){let e=0;for(let t=0;t<s.length;t++)s[t]?s[e++]=s[t]:a&&(this._deliveryQueue.end--,e<this._deliveryQueue.i&&this._deliveryQueue.i--);s.length=e}}_deliver(e,n){var i;if(!e)return;const r=(null===(i=this._options)||void 0===i?void 0:i.onListenerError)||t;if(r)try{e.value(n)}catch(s){r(s)}else e.value(n)}_deliverQueue(e){const t=e.current._listeners;for(;e.i<e.end;)this._deliver(t[e.i++],e.value);e.reset()}fire(e){var t,n,i,r;if((null===(t=this._deliveryQueue)||void 0===t?void 0:t.current)&&(this._deliverQueue(this._deliveryQueue),null===(n=this._perfMon)||void 0===n||n.stop()),null===(i=this._perfMon)||void 0===i||i.start(this._size),this._listeners)if(this._listeners instanceof v)this._deliver(this._listeners,e);else{const t=this._deliveryQueue;t.enqueue(this,e,this._listeners.length),this._deliverQueue(t)}else;null===(r=this._perfMon)||void 0===r||r.stop()}hasListeners(){return this._size>0}}class w{constructor(){this.i=-1,this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}}function N(e){const t=[];for(const n of function(e){let t=[];for(;Object.prototype!==e;)t=t.concat(Object.getOwnPropertyNames(e)),e=Object.getPrototypeOf(e);return t}(e))"function"==typeof e[n]&&t.push(n);return t}let E="undefined"!=typeof document&&document.location&&document.location.hash.indexOf("pseudo=true")>=0;function S(e,t,...n){return function(e,t){let n;return n=0===t.length?e:e.replace(/\{(\d+)\}/g,((e,n)=>{const i=n[0],r=t[i];let s=e;return"string"==typeof r?s=r:"number"!=typeof r&&"boolean"!=typeof r&&null!=r||(s=String(r)),s})),E&&(n="［"+n.replace(/[aouei]/g,"$&$&")+"］"),n}(t,n)}var R;const A="en";let x,M,k,O=!1,T=!1,P=!1,I=A,D=A;const F=globalThis;let q;void 0!==F.vscode&&void 0!==F.vscode.process?q=F.vscode.process:"undefined"!=typeof process&&(q=process);const K="string"==typeof(null===(R=null==q?void 0:q.versions)||void 0===R?void 0:R.electron)&&"renderer"===(null==q?void 0:q.type);if("object"==typeof q){O="win32"===q.platform,T="darwin"===q.platform,P="linux"===q.platform,P&&q.env.SNAP&&q.env.SNAP_REVISION,q.env.CI||q.env.BUILD_ARTIFACTSTAGINGDIRECTORY,x=A,I=A;const e=q.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e),n=t.availableLanguages["*"];x=t.locale,D=t.osLocale,I=n||A,M=t._translationsConfigFile}catch(Zr){}}else"object"!=typeof navigator||K||(k=navigator.userAgent,O=k.indexOf("Windows")>=0,T=k.indexOf("Macintosh")>=0,(k.indexOf("Macintosh")>=0||k.indexOf("iPad")>=0||k.indexOf("iPhone")>=0)&&navigator.maxTouchPoints&&navigator.maxTouchPoints,P=k.indexOf("Linux")>=0,null==k||k.indexOf("Mobi"),S(0,"_"),x=A,I=x,D=navigator.language);const V=O,B=T,U=k,W="function"==typeof F.postMessage&&!F.importScripts;(()=>{if(W){const e=[];F.addEventListener("message",(t=>{if(t.data&&t.data.vscodeScheduleAsyncWork)for(let n=0,i=e.length;n<i;n++){const i=e[n];if(i.id===t.data.vscodeScheduleAsyncWork)return e.splice(n,1),void i.callback()}}));let t=0;return n=>{const i=++t;e.push({id:i,callback:n}),F.postMessage({vscodeScheduleAsyncWork:i},"*")}}})();const H=!!(U&&U.indexOf("Chrome")>=0);U&&U.indexOf("Firefox"),!H&&U&&U.indexOf("Safari"),U&&U.indexOf("Edg/"),U&&U.indexOf("Android");class z{constructor(e){this.executor=e,this._didRun=!1}get value(){if(!this._didRun)try{this._value=this.executor()}catch(e){this._error=e}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}}var $;function j(e){return e>=65&&e<=90}function G(e){return 55296<=e&&e<=56319}function Q(e,t,n){const i=e.charCodeAt(n);if(G(i)&&n+1<t){const t=e.charCodeAt(n+1);if(function(e){return 56320<=e&&e<=57343}(t))return t-56320+(i-55296<<10)+65536}return i}const Y=/^[\t\n\r\x20-\x7E]*$/;class X{static getInstance(e){return $.cache.get(Array.from(e))}static getLocales(){return $._locales.value}constructor(e){this.confusableDictionary=e}isAmbiguous(e){return this.confusableDictionary.has(e)}getPrimaryConfusable(e){return this.confusableDictionary.get(e)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}}$=X,X.ambiguousCharacterData=new z((()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}'))),X.cache=new class{constructor(e){this.fn=e,this.lastCache=void 0,this.lastArgKey=void 0}get(e){const t=JSON.stringify(e);return this.lastArgKey!==t&&(this.lastArgKey=t,this.lastCache=this.fn(e)),this.lastCache}}((e=>{function t(e){const t=new Map;for(let n=0;n<e.length;n+=2)t.set(e[n],e[n+1]);return t}function n(e,t){if(!e)return t;const n=new Map;for(const[i,r]of e)t.has(i)&&n.set(i,r);return n}const i=$.ambiguousCharacterData.value;let r,s=e.filter((e=>!e.startsWith("_")&&e in i));0===s.length&&(s=["_default"]);for(const a of s){r=n(r,t(i[a]))}const o=function(e,t){const n=new Map(e);for(const[i,r]of t)n.set(i,r);return n}(t(i._common),r);return new $(o)})),X._locales=new z((()=>Object.keys($.ambiguousCharacterData.value).filter((e=>!e.startsWith("_")))));class J{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(J.getRawData())),this._data}static isInvisibleCharacter(e){return J.getData().has(e)}static get codePoints(){return J.getData()}}J._data=void 0;class Z{constructor(e,t,n,i){this.vsWorker=e,this.req=t,this.method=n,this.args=i,this.type=0}}class ee{constructor(e,t,n,i){this.vsWorker=e,this.seq=t,this.res=n,this.err=i,this.type=1}}class te{constructor(e,t,n,i){this.vsWorker=e,this.req=t,this.eventName=n,this.arg=i,this.type=2}}class ne{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}}class ie{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}}class re{constructor(e){this._workerId=-1,this._handler=e,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(e){this._workerId=e}sendMessage(e,t){const n=String(++this._lastSentReq);return new Promise(((i,r)=>{this._pendingReplies[n]={resolve:i,reject:r},this._send(new Z(this._workerId,n,e,t))}))}listen(e,t){let n=null;const i=new L({onWillAddFirstListener:()=>{n=String(++this._lastSentReq),this._pendingEmitters.set(n,i),this._send(new te(this._workerId,n,e,t))},onDidRemoveLastListener:()=>{this._pendingEmitters.delete(n),this._send(new ie(this._workerId,n)),n=null}});return i.event}handleMessage(e){e&&e.vsWorker&&(-1!==this._workerId&&e.vsWorker!==this._workerId||this._handleMessage(e))}_handleMessage(e){switch(e.type){case 1:return this._handleReplyMessage(e);case 0:return this._handleRequestMessage(e);case 2:return this._handleSubscribeEventMessage(e);case 3:return this._handleEventMessage(e);case 4:return this._handleUnsubscribeEventMessage(e)}}_handleReplyMessage(e){if(!this._pendingReplies[e.seq])return;const t=this._pendingReplies[e.seq];if(delete this._pendingReplies[e.seq],e.err){let n=e.err;return e.err.$isError&&(n=new Error,n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),void t.reject(n)}t.resolve(e.res)}_handleRequestMessage(e){const t=e.req;this._handler.handleMessage(e.method,e.args).then((e=>{this._send(new ee(this._workerId,t,e,void 0))}),(e=>{e.detail instanceof Error&&(e.detail=n(e.detail)),this._send(new ee(this._workerId,t,void 0,n(e)))}))}_handleSubscribeEventMessage(e){const t=e.req,n=this._handler.handleEvent(e.eventName,e.arg)((e=>{this._send(new ne(this._workerId,t,e))}));this._pendingEvents.set(t,n)}_handleEventMessage(e){this._pendingEmitters.has(e.req)&&this._pendingEmitters.get(e.req).fire(e.event)}_handleUnsubscribeEventMessage(e){this._pendingEvents.has(e.req)&&(this._pendingEvents.get(e.req).dispose(),this._pendingEvents.delete(e.req))}_send(e){const t=[];if(0===e.type)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else 1===e.type&&e.res instanceof ArrayBuffer&&t.push(e.res);this._handler.sendMessage(e,t)}}function se(e){return"o"===e[0]&&"n"===e[1]&&j(e.charCodeAt(2))}function oe(e){return/^onDynamic/.test(e)&&j(e.charCodeAt(9))}class ae{constructor(e,t){this._requestHandlerFactory=t,this._requestHandler=null,this._protocol=new re({sendMessage:(t,n)=>{e(t,n)},handleMessage:(e,t)=>this._handleMessage(e,t),handleEvent:(e,t)=>this._handleEvent(e,t)})}onmessage(e){this._protocol.handleMessage(e)}_handleMessage(e,t){if("$initialize"===e)return this.initialize(t[0],t[1],t[2],t[3]);if(!this._requestHandler||"function"!=typeof this._requestHandler[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._requestHandler[e].apply(this._requestHandler,t))}catch(Zr){return Promise.reject(Zr)}}_handleEvent(e,t){if(!this._requestHandler)throw new Error("Missing requestHandler");if(oe(e)){const n=this._requestHandler[e].call(this._requestHandler,t);if("function"!=typeof n)throw new Error(`Missing dynamic event ${e} on request handler.`);return n}if(se(e)){const t=this._requestHandler[e];if("function"!=typeof t)throw new Error(`Missing event ${e} on request handler.`);return t}throw new Error(`Malformed event name ${e}`)}initialize(e,t,n,i){this._protocol.setWorkerId(e);const r=function(e,t,n){const i=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)},r=e=>function(t){return n(e,t)},s={};for(const o of e)oe(o)?s[o]=r(o):se(o)?s[o]=n(o,void 0):s[o]=i(o);return s}(i,((e,t)=>this._protocol.sendMessage(e,t)),((e,t)=>this._protocol.listen(e,t)));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(r),Promise.resolve(N(this._requestHandler))):(t&&(void 0!==t.baseUrl&&delete t.baseUrl,void 0!==t.paths&&void 0!==t.paths.vs&&delete t.paths.vs,void 0!==t.trustedTypesPolicy&&delete t.trustedTypesPolicy,t.catchError=!0,globalThis.require.config(t)),new Promise(((e,t)=>{(0,globalThis.require)([n],(n=>{this._requestHandler=n.create(r),this._requestHandler?e(N(this._requestHandler)):t(new Error("No RequestHandler!"))}),t)})))}}class le{constructor(e,t,n,i){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=i}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}function ue(e,t){return(t<<5)-t+e|0}function he(e,t){t=ue(149417,t);for(let n=0,i=e.length;n<i;n++)t=ue(e.charCodeAt(n),t);return t}class ce{constructor(e){this.source=e}getElements(){const e=this.source,t=new Int32Array(e.length);for(let n=0,i=e.length;n<i;n++)t[n]=e.charCodeAt(n);return t}}function de(e,t,n){return new pe(new ce(e),new ce(t)).ComputeDiff(n).changes}class ge{static Assert(e,t){if(!e)throw new Error(t)}}class me{static Copy(e,t,n,i,r){for(let s=0;s<r;s++)n[i+s]=e[t+s]}static Copy2(e,t,n,i,r){for(let s=0;s<r;s++)n[i+s]=e[t+s]}}class fe{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new le(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_originalCount++}AddModifiedElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class pe{constructor(e,t,n=null){this.ContinueProcessingPredicate=n,this._originalSequence=e,this._modifiedSequence=t;const[i,r,s]=pe._getElements(e),[o,a,l]=pe._getElements(t);this._hasStrings=s&&l,this._originalStringElements=i,this._originalElementsOrHash=r,this._modifiedStringElements=o,this._modifiedElementsOrHash=a,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(e){return e.length>0&&"string"==typeof e[0]}static _getElements(e){const t=e.getElements();if(pe._isStringArray(t)){const e=new Int32Array(t.length);for(let n=0,i=t.length;n<i;n++)e[n]=he(t[n],0);return[t,e,!0]}return t instanceof Int32Array?[[],t,!1]:[[],new Int32Array(t),!1]}ElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._modifiedStringElements[t])}ElementsAreStrictEqual(e,t){if(!this.ElementsAreEqual(e,t))return!1;return pe._getStrictElement(this._originalSequence,e)===pe._getStrictElement(this._modifiedSequence,t)}static _getStrictElement(e,t){return"function"==typeof e.getStrictElement?e.getStrictElement(t):null}OriginalElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._originalElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._originalStringElements[t])}ModifiedElementsAreEqual(e,t){return this._modifiedElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._modifiedStringElements[e]===this._modifiedStringElements[t])}ComputeDiff(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}_ComputeDiff(e,t,n,i,r){const s=[!1];let o=this.ComputeDiffRecursive(e,t,n,i,s);return r&&(o=this.PrettifyChanges(o)),{quitEarly:s[0],changes:o}}ComputeDiffRecursive(e,t,n,i,r){for(r[0]=!1;e<=t&&n<=i&&this.ElementsAreEqual(e,n);)e++,n++;for(;t>=e&&i>=n&&this.ElementsAreEqual(t,i);)t--,i--;if(e>t||n>i){let r;return n<=i?(ge.Assert(e===t+1,"originalStart should only be one more than originalEnd"),r=[new le(e,0,n,i-n+1)]):e<=t?(ge.Assert(n===i+1,"modifiedStart should only be one more than modifiedEnd"),r=[new le(e,t-e+1,n,0)]):(ge.Assert(e===t+1,"originalStart should only be one more than originalEnd"),ge.Assert(n===i+1,"modifiedStart should only be one more than modifiedEnd"),r=[]),r}const s=[0],o=[0],a=this.ComputeRecursionPoint(e,t,n,i,s,o,r),l=s[0],u=o[0];if(null!==a)return a;if(!r[0]){const s=this.ComputeDiffRecursive(e,l,n,u,r);let o=[];return o=r[0]?[new le(l+1,t-(l+1)+1,u+1,i-(u+1)+1)]:this.ComputeDiffRecursive(l+1,t,u+1,i,r),this.ConcatenateChanges(s,o)}return[new le(e,t-e+1,n,i-n+1)]}WALKTRACE(e,t,n,i,r,s,o,a,l,u,h,c,d,g,m,f,p,b){let _=null,C=null,y=new fe,v=t,L=n,w=d[0]-f[0]-i,N=-1073741824,E=this.m_forwardHistory.length-1;do{const t=w+e;t===v||t<L&&l[t-1]<l[t+1]?(g=(h=l[t+1])-w-i,h<N&&y.MarkNextChange(),N=h,y.AddModifiedElement(h+1,g),w=t+1-e):(g=(h=l[t-1]+1)-w-i,h<N&&y.MarkNextChange(),N=h-1,y.AddOriginalElement(h,g+1),w=t-1-e),E>=0&&(e=(l=this.m_forwardHistory[E])[0],v=1,L=l.length-1)}while(--E>=-1);if(_=y.getReverseChanges(),b[0]){let e=d[0]+1,t=f[0]+1;if(null!==_&&_.length>0){const n=_[_.length-1];e=Math.max(e,n.getOriginalEnd()),t=Math.max(t,n.getModifiedEnd())}C=[new le(e,c-e+1,t,m-t+1)]}else{y=new fe,v=s,L=o,w=d[0]-f[0]-a,N=1073741824,E=p?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const e=w+r;e===v||e<L&&u[e-1]>=u[e+1]?(g=(h=u[e+1]-1)-w-a,h>N&&y.MarkNextChange(),N=h+1,y.AddOriginalElement(h+1,g+1),w=e+1-r):(g=(h=u[e-1])-w-a,h>N&&y.MarkNextChange(),N=h,y.AddModifiedElement(h+1,g+1),w=e-1-r),E>=0&&(r=(u=this.m_reverseHistory[E])[0],v=1,L=u.length-1)}while(--E>=-1);C=y.getChanges()}return this.ConcatenateChanges(_,C)}ComputeRecursionPoint(e,t,n,i,r,s,o){let a=0,l=0,u=0,h=0,c=0,d=0;e--,n--,r[0]=0,s[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const g=t-e+(i-n),m=g+1,f=new Int32Array(m),p=new Int32Array(m),b=i-n,_=t-e,C=e-n,y=t-i,v=(_-b)%2==0;f[b]=e,p[_]=t,o[0]=!1;for(let L=1;L<=g/2+1;L++){let g=0,w=0;u=this.ClipDiagonalBound(b-L,L,b,m),h=this.ClipDiagonalBound(b+L,L,b,m);for(let e=u;e<=h;e+=2){a=e===u||e<h&&f[e-1]<f[e+1]?f[e+1]:f[e-1]+1,l=a-(e-b)-C;const n=a;for(;a<t&&l<i&&this.ElementsAreEqual(a+1,l+1);)a++,l++;if(f[e]=a,a+l>g+w&&(g=a,w=l),!v&&Math.abs(e-_)<=L-1&&a>=p[e])return r[0]=a,s[0]=l,n<=p[e]&&L<=1448?this.WALKTRACE(b,u,h,C,_,c,d,y,f,p,a,t,r,l,i,s,v,o):null}const N=(g-e+(w-n)-L)/2;if(null!==this.ContinueProcessingPredicate&&!this.ContinueProcessingPredicate(g,N))return o[0]=!0,r[0]=g,s[0]=w,N>0&&L<=1448?this.WALKTRACE(b,u,h,C,_,c,d,y,f,p,a,t,r,l,i,s,v,o):(e++,n++,[new le(e,t-e+1,n,i-n+1)]);c=this.ClipDiagonalBound(_-L,L,_,m),d=this.ClipDiagonalBound(_+L,L,_,m);for(let m=c;m<=d;m+=2){a=m===c||m<d&&p[m-1]>=p[m+1]?p[m+1]-1:p[m-1],l=a-(m-_)-y;const g=a;for(;a>e&&l>n&&this.ElementsAreEqual(a,l);)a--,l--;if(p[m]=a,v&&Math.abs(m-b)<=L&&a<=f[m])return r[0]=a,s[0]=l,g>=f[m]&&L<=1448?this.WALKTRACE(b,u,h,C,_,c,d,y,f,p,a,t,r,l,i,s,v,o):null}if(L<=1447){let e=new Int32Array(h-u+2);e[0]=b-u+1,me.Copy2(f,u,e,1,h-u+1),this.m_forwardHistory.push(e),e=new Int32Array(d-c+2),e[0]=_-c+1,me.Copy2(p,c,e,1,d-c+1),this.m_reverseHistory.push(e)}}return this.WALKTRACE(b,u,h,C,_,c,d,y,f,p,a,t,r,l,i,s,v,o)}PrettifyChanges(e){for(let t=0;t<e.length;t++){const n=e[t],i=t<e.length-1?e[t+1].originalStart:this._originalElementsOrHash.length,r=t<e.length-1?e[t+1].modifiedStart:this._modifiedElementsOrHash.length,s=n.originalLength>0,o=n.modifiedLength>0;for(;n.originalStart+n.originalLength<i&&n.modifiedStart+n.modifiedLength<r&&(!s||this.OriginalElementsAreEqual(n.originalStart,n.originalStart+n.originalLength))&&(!o||this.ModifiedElementsAreEqual(n.modifiedStart,n.modifiedStart+n.modifiedLength));){const e=this.ElementsAreStrictEqual(n.originalStart,n.modifiedStart);if(this.ElementsAreStrictEqual(n.originalStart+n.originalLength,n.modifiedStart+n.modifiedLength)&&!e)break;n.originalStart++,n.modifiedStart++}const a=[null];t<e.length-1&&this.ChangesOverlap(e[t],e[t+1],a)&&(e[t]=a[0],e.splice(t+1,1),t--)}for(let t=e.length-1;t>=0;t--){const n=e[t];let i=0,r=0;if(t>0){const n=e[t-1];i=n.originalStart+n.originalLength,r=n.modifiedStart+n.modifiedLength}const s=n.originalLength>0,o=n.modifiedLength>0;let a=0,l=this._boundaryScore(n.originalStart,n.originalLength,n.modifiedStart,n.modifiedLength);for(let e=1;;e++){const t=n.originalStart-e,u=n.modifiedStart-e;if(t<i||u<r)break;if(s&&!this.OriginalElementsAreEqual(t,t+n.originalLength))break;if(o&&!this.ModifiedElementsAreEqual(u,u+n.modifiedLength))break;const h=(t===i&&u===r?5:0)+this._boundaryScore(t,n.originalLength,u,n.modifiedLength);h>l&&(l=h,a=e)}n.originalStart-=a,n.modifiedStart-=a;const u=[null];t>0&&this.ChangesOverlap(e[t-1],e[t],u)&&(e[t-1]=u[0],e.splice(t,1),t++)}if(this._hasStrings)for(let t=1,n=e.length;t<n;t++){const n=e[t-1],i=e[t],r=i.originalStart-n.originalStart-n.originalLength,s=n.originalStart,o=i.originalStart+i.originalLength,a=o-s,l=n.modifiedStart,u=i.modifiedStart+i.modifiedLength,h=u-l;if(r<5&&a<20&&h<20){const e=this._findBetterContiguousSequence(s,a,l,h,r);if(e){const[t,s]=e;t===n.originalStart+n.originalLength&&s===n.modifiedStart+n.modifiedLength||(n.originalLength=t-n.originalStart,n.modifiedLength=s-n.modifiedStart,i.originalStart=t+r,i.modifiedStart=s+r,i.originalLength=o-i.originalStart,i.modifiedLength=u-i.modifiedStart)}}}return e}_findBetterContiguousSequence(e,t,n,i,r){if(t<r||i<r)return null;const s=e+t-r+1,o=n+i-r+1;let a=0,l=0,u=0;for(let h=e;h<s;h++)for(let e=n;e<o;e++){const t=this._contiguousSequenceScore(h,e,r);t>0&&t>a&&(a=t,l=h,u=e)}return a>0?[l,u]:null}_contiguousSequenceScore(e,t,n){let i=0;for(let r=0;r<n;r++){if(!this.ElementsAreEqual(e+r,t+r))return 0;i+=this._originalStringElements[e+r].length}return i}_OriginalIsBoundary(e){return e<=0||e>=this._originalElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._originalStringElements[e])}_OriginalRegionIsBoundary(e,t){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._OriginalIsBoundary(n-1)||this._OriginalIsBoundary(n))return!0}return!1}_ModifiedIsBoundary(e){return e<=0||e>=this._modifiedElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[e])}_ModifiedRegionIsBoundary(e,t){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._ModifiedIsBoundary(n-1)||this._ModifiedIsBoundary(n))return!0}return!1}_boundaryScore(e,t,n,i){return(this._OriginalRegionIsBoundary(e,t)?1:0)+(this._ModifiedRegionIsBoundary(n,i)?1:0)}ConcatenateChanges(e,t){const n=[];if(0===e.length||0===t.length)return t.length>0?t:e;if(this.ChangesOverlap(e[e.length-1],t[0],n)){const i=new Array(e.length+t.length-1);return me.Copy(e,0,i,0,e.length-1),i[e.length-1]=n[0],me.Copy(t,1,i,e.length,t.length-1),i}{const n=new Array(e.length+t.length);return me.Copy(e,0,n,0,e.length),me.Copy(t,0,n,e.length,t.length),n}}ChangesOverlap(e,t,n){if(ge.Assert(e.originalStart<=t.originalStart,"Left change is not less than or equal to right change"),ge.Assert(e.modifiedStart<=t.modifiedStart,"Left change is not less than or equal to right change"),e.originalStart+e.originalLength>=t.originalStart||e.modifiedStart+e.modifiedLength>=t.modifiedStart){const i=e.originalStart;let r=e.originalLength;const s=e.modifiedStart;let o=e.modifiedLength;return e.originalStart+e.originalLength>=t.originalStart&&(r=t.originalStart+t.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=t.modifiedStart&&(o=t.modifiedStart+t.modifiedLength-e.modifiedStart),n[0]=new le(i,r,s,o),!0}return n[0]=null,!1}ClipDiagonalBound(e,t,n,i){if(e>=0&&e<i)return e;const r=t%2==0;if(e<0){return r===(n%2==0)?0:1}return r===((i-n-1)%2==0)?i-1:i-2}}var be={};let _e;const Ce=globalThis.vscode;if(void 0!==Ce&&void 0!==Ce.process){const e=Ce.process;_e={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd:()=>e.cwd()}}else _e="undefined"!=typeof process?{get platform(){return process.platform},get arch(){return process.arch},get env(){return be},cwd:()=>be.VSCODE_CWD||process.cwd()}:{get platform(){return V?"win32":B?"darwin":"linux"},get arch(){},get env(){return{}},cwd:()=>"/"};const ye=_e.cwd,ve=_e.env,Le=_e.platform,we=46,Ne=47,Ee=92,Se=58;class Re extends Error{constructor(e,t,n){let i;"string"==typeof t&&0===t.indexOf("not ")?(i="must not be",t=t.replace(/^not /,"")):i="must be";const r=-1!==e.indexOf(".")?"property":"argument";let s=`The "${e}" ${r} ${i} of type ${t}`;s+=". Received type "+typeof n,super(s),this.code="ERR_INVALID_ARG_TYPE"}}function Ae(e,t){if("string"!=typeof e)throw new Re(t,"string",e)}const xe="win32"===Le;function Me(e){return e===Ne||e===Ee}function ke(e){return e===Ne}function Oe(e){return e>=65&&e<=90||e>=97&&e<=122}function Te(e,t,n,i){let r="",s=0,o=-1,a=0,l=0;for(let u=0;u<=e.length;++u){if(u<e.length)l=e.charCodeAt(u);else{if(i(l))break;l=Ne}if(i(l)){if(o===u-1||1===a);else if(2===a){if(r.length<2||2!==s||r.charCodeAt(r.length-1)!==we||r.charCodeAt(r.length-2)!==we){if(r.length>2){const e=r.lastIndexOf(n);-1===e?(r="",s=0):(r=r.slice(0,e),s=r.length-1-r.lastIndexOf(n)),o=u,a=0;continue}if(0!==r.length){r="",s=0,o=u,a=0;continue}}t&&(r+=r.length>0?`${n}..`:"..",s=2)}else r.length>0?r+=`${n}${e.slice(o+1,u)}`:r=e.slice(o+1,u),s=u-o-1;o=u,a=0}else l===we&&-1!==a?++a:a=-1}return r}function Pe(e,t){!function(e,t){if(null===e||"object"!=typeof e)throw new Re(t,"Object",e)}(t,"pathObject");const n=t.dir||t.root,i=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${i}`:`${n}${e}${i}`:i}const Ie={resolve(...e){let t="",n="",i=!1;for(let r=e.length-1;r>=-1;r--){let s;if(r>=0){if(s=e[r],Ae(s,"path"),0===s.length)continue}else 0===t.length?s=ye():(s=ve[`=${t}`]||ye(),(void 0===s||s.slice(0,2).toLowerCase()!==t.toLowerCase()&&s.charCodeAt(2)===Ee)&&(s=`${t}\\`));const o=s.length;let a=0,l="",u=!1;const h=s.charCodeAt(0);if(1===o)Me(h)&&(a=1,u=!0);else if(Me(h))if(u=!0,Me(s.charCodeAt(1))){let e=2,t=e;for(;e<o&&!Me(s.charCodeAt(e));)e++;if(e<o&&e!==t){const n=s.slice(t,e);for(t=e;e<o&&Me(s.charCodeAt(e));)e++;if(e<o&&e!==t){for(t=e;e<o&&!Me(s.charCodeAt(e));)e++;e!==o&&e===t||(l=`\\\\${n}\\${s.slice(t,e)}`,a=e)}}}else a=1;else Oe(h)&&s.charCodeAt(1)===Se&&(l=s.slice(0,2),a=2,o>2&&Me(s.charCodeAt(2))&&(u=!0,a=3));if(l.length>0)if(t.length>0){if(l.toLowerCase()!==t.toLowerCase())continue}else t=l;if(i){if(t.length>0)break}else if(n=`${s.slice(a)}\\${n}`,i=u,u&&t.length>0)break}return n=Te(n,!i,"\\",Me),i?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){Ae(e,"path");const t=e.length;if(0===t)return".";let n,i=0,r=!1;const s=e.charCodeAt(0);if(1===t)return ke(s)?"\\":e;if(Me(s))if(r=!0,Me(e.charCodeAt(1))){let r=2,s=r;for(;r<t&&!Me(e.charCodeAt(r));)r++;if(r<t&&r!==s){const o=e.slice(s,r);for(s=r;r<t&&Me(e.charCodeAt(r));)r++;if(r<t&&r!==s){for(s=r;r<t&&!Me(e.charCodeAt(r));)r++;if(r===t)return`\\\\${o}\\${e.slice(s)}\\`;r!==s&&(n=`\\\\${o}\\${e.slice(s,r)}`,i=r)}}}else i=1;else Oe(s)&&e.charCodeAt(1)===Se&&(n=e.slice(0,2),i=2,t>2&&Me(e.charCodeAt(2))&&(r=!0,i=3));let o=i<t?Te(e.slice(i),!r,"\\",Me):"";return 0!==o.length||r||(o="."),o.length>0&&Me(e.charCodeAt(t-1))&&(o+="\\"),void 0===n?r?`\\${o}`:o:r?`${n}\\${o}`:`${n}${o}`},isAbsolute(e){Ae(e,"path");const t=e.length;if(0===t)return!1;const n=e.charCodeAt(0);return Me(n)||t>2&&Oe(n)&&e.charCodeAt(1)===Se&&Me(e.charCodeAt(2))},join(...e){if(0===e.length)return".";let t,n;for(let s=0;s<e.length;++s){const i=e[s];Ae(i,"path"),i.length>0&&(void 0===t?t=n=i:t+=`\\${i}`)}if(void 0===t)return".";let i=!0,r=0;if("string"==typeof n&&Me(n.charCodeAt(0))){++r;const e=n.length;e>1&&Me(n.charCodeAt(1))&&(++r,e>2&&(Me(n.charCodeAt(2))?++r:i=!1))}if(i){for(;r<t.length&&Me(t.charCodeAt(r));)r++;r>=2&&(t=`\\${t.slice(r)}`)}return Ie.normalize(t)},relative(e,t){if(Ae(e,"from"),Ae(t,"to"),e===t)return"";const n=Ie.resolve(e),i=Ie.resolve(t);if(n===i)return"";if((e=n.toLowerCase())===(t=i.toLowerCase()))return"";let r=0;for(;r<e.length&&e.charCodeAt(r)===Ee;)r++;let s=e.length;for(;s-1>r&&e.charCodeAt(s-1)===Ee;)s--;const o=s-r;let a=0;for(;a<t.length&&t.charCodeAt(a)===Ee;)a++;let l=t.length;for(;l-1>a&&t.charCodeAt(l-1)===Ee;)l--;const u=l-a,h=o<u?o:u;let c=-1,d=0;for(;d<h;d++){const n=e.charCodeAt(r+d);if(n!==t.charCodeAt(a+d))break;n===Ee&&(c=d)}if(d!==h){if(-1===c)return i}else{if(u>h){if(t.charCodeAt(a+d)===Ee)return i.slice(a+d+1);if(2===d)return i.slice(a+d)}o>h&&(e.charCodeAt(r+d)===Ee?c=d:2===d&&(c=3)),-1===c&&(c=0)}let g="";for(d=r+c+1;d<=s;++d)d!==s&&e.charCodeAt(d)!==Ee||(g+=0===g.length?"..":"\\..");return a+=c,g.length>0?`${g}${i.slice(a,l)}`:(i.charCodeAt(a)===Ee&&++a,i.slice(a,l))},toNamespacedPath(e){if("string"!=typeof e||0===e.length)return e;const t=Ie.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===Ee){if(t.charCodeAt(1)===Ee){const e=t.charCodeAt(2);if(63!==e&&e!==we)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(Oe(t.charCodeAt(0))&&t.charCodeAt(1)===Se&&t.charCodeAt(2)===Ee)return`\\\\?\\${t}`;return e},dirname(e){Ae(e,"path");const t=e.length;if(0===t)return".";let n=-1,i=0;const r=e.charCodeAt(0);if(1===t)return Me(r)?e:".";if(Me(r)){if(n=i=1,Me(e.charCodeAt(1))){let r=2,s=r;for(;r<t&&!Me(e.charCodeAt(r));)r++;if(r<t&&r!==s){for(s=r;r<t&&Me(e.charCodeAt(r));)r++;if(r<t&&r!==s){for(s=r;r<t&&!Me(e.charCodeAt(r));)r++;if(r===t)return e;r!==s&&(n=i=r+1)}}}}else Oe(r)&&e.charCodeAt(1)===Se&&(n=t>2&&Me(e.charCodeAt(2))?3:2,i=n);let s=-1,o=!0;for(let a=t-1;a>=i;--a)if(Me(e.charCodeAt(a))){if(!o){s=a;break}}else o=!1;if(-1===s){if(-1===n)return".";s=n}return e.slice(0,s)},basename(e,t){void 0!==t&&Ae(t,"ext"),Ae(e,"path");let n,i=0,r=-1,s=!0;if(e.length>=2&&Oe(e.charCodeAt(0))&&e.charCodeAt(1)===Se&&(i=2),void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=i;--n){const l=e.charCodeAt(n);if(Me(l)){if(!s){i=n+1;break}}else-1===a&&(s=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?-1==--o&&(r=n):(o=-1,r=a))}return i===r?r=a:-1===r&&(r=e.length),e.slice(i,r)}for(n=e.length-1;n>=i;--n)if(Me(e.charCodeAt(n))){if(!s){i=n+1;break}}else-1===r&&(s=!1,r=n+1);return-1===r?"":e.slice(i,r)},extname(e){Ae(e,"path");let t=0,n=-1,i=0,r=-1,s=!0,o=0;e.length>=2&&e.charCodeAt(1)===Se&&Oe(e.charCodeAt(0))&&(t=i=2);for(let a=e.length-1;a>=t;--a){const t=e.charCodeAt(a);if(Me(t)){if(!s){i=a+1;break}}else-1===r&&(s=!1,r=a+1),t===we?-1===n?n=a:1!==o&&(o=1):-1!==n&&(o=-1)}return-1===n||-1===r||0===o||1===o&&n===r-1&&n===i+1?"":e.slice(n,r)},format:Pe.bind(null,"\\"),parse(e){Ae(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=e.length;let i=0,r=e.charCodeAt(0);if(1===n)return Me(r)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(Me(r)){if(i=1,Me(e.charCodeAt(1))){let t=2,r=t;for(;t<n&&!Me(e.charCodeAt(t));)t++;if(t<n&&t!==r){for(r=t;t<n&&Me(e.charCodeAt(t));)t++;if(t<n&&t!==r){for(r=t;t<n&&!Me(e.charCodeAt(t));)t++;t===n?i=t:t!==r&&(i=t+1)}}}}else if(Oe(r)&&e.charCodeAt(1)===Se){if(n<=2)return t.root=t.dir=e,t;if(i=2,Me(e.charCodeAt(2))){if(3===n)return t.root=t.dir=e,t;i=3}}i>0&&(t.root=e.slice(0,i));let s=-1,o=i,a=-1,l=!0,u=e.length-1,h=0;for(;u>=i;--u)if(r=e.charCodeAt(u),Me(r)){if(!l){o=u+1;break}}else-1===a&&(l=!1,a=u+1),r===we?-1===s?s=u:1!==h&&(h=1):-1!==s&&(h=-1);return-1!==a&&(-1===s||0===h||1===h&&s===a-1&&s===o+1?t.base=t.name=e.slice(o,a):(t.name=e.slice(o,s),t.base=e.slice(o,a),t.ext=e.slice(s,a))),t.dir=o>0&&o!==i?e.slice(0,o-1):t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},De=(()=>{if(xe){const e=/\\/g;return()=>{const t=ye().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>ye()})(),Fe={resolve(...e){let t="",n=!1;for(let i=e.length-1;i>=-1&&!n;i--){const r=i>=0?e[i]:De();Ae(r,"path"),0!==r.length&&(t=`${r}/${t}`,n=r.charCodeAt(0)===Ne)}return t=Te(t,!n,"/",ke),n?`/${t}`:t.length>0?t:"."},normalize(e){if(Ae(e,"path"),0===e.length)return".";const t=e.charCodeAt(0)===Ne,n=e.charCodeAt(e.length-1)===Ne;return 0===(e=Te(e,!t,"/",ke)).length?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute:e=>(Ae(e,"path"),e.length>0&&e.charCodeAt(0)===Ne),join(...e){if(0===e.length)return".";let t;for(let n=0;n<e.length;++n){const i=e[n];Ae(i,"path"),i.length>0&&(void 0===t?t=i:t+=`/${i}`)}return void 0===t?".":Fe.normalize(t)},relative(e,t){if(Ae(e,"from"),Ae(t,"to"),e===t)return"";if((e=Fe.resolve(e))===(t=Fe.resolve(t)))return"";const n=e.length,i=n-1,r=t.length-1,s=i<r?i:r;let o=-1,a=0;for(;a<s;a++){const n=e.charCodeAt(1+a);if(n!==t.charCodeAt(1+a))break;n===Ne&&(o=a)}if(a===s)if(r>s){if(t.charCodeAt(1+a)===Ne)return t.slice(1+a+1);if(0===a)return t.slice(1+a)}else i>s&&(e.charCodeAt(1+a)===Ne?o=a:0===a&&(o=0));let l="";for(a=1+o+1;a<=n;++a)a!==n&&e.charCodeAt(a)!==Ne||(l+=0===l.length?"..":"/..");return`${l}${t.slice(1+o)}`},toNamespacedPath:e=>e,dirname(e){if(Ae(e,"path"),0===e.length)return".";const t=e.charCodeAt(0)===Ne;let n=-1,i=!0;for(let r=e.length-1;r>=1;--r)if(e.charCodeAt(r)===Ne){if(!i){n=r;break}}else i=!1;return-1===n?t?"/":".":t&&1===n?"//":e.slice(0,n)},basename(e,t){void 0!==t&&Ae(t,"ext"),Ae(e,"path");let n,i=0,r=-1,s=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=0;--n){const l=e.charCodeAt(n);if(l===Ne){if(!s){i=n+1;break}}else-1===a&&(s=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?-1==--o&&(r=n):(o=-1,r=a))}return i===r?r=a:-1===r&&(r=e.length),e.slice(i,r)}for(n=e.length-1;n>=0;--n)if(e.charCodeAt(n)===Ne){if(!s){i=n+1;break}}else-1===r&&(s=!1,r=n+1);return-1===r?"":e.slice(i,r)},extname(e){Ae(e,"path");let t=-1,n=0,i=-1,r=!0,s=0;for(let o=e.length-1;o>=0;--o){const a=e.charCodeAt(o);if(a!==Ne)-1===i&&(r=!1,i=o+1),a===we?-1===t?t=o:1!==s&&(s=1):-1!==t&&(s=-1);else if(!r){n=o+1;break}}return-1===t||-1===i||0===s||1===s&&t===i-1&&t===n+1?"":e.slice(t,i)},format:Pe.bind(null,"/"),parse(e){Ae(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=e.charCodeAt(0)===Ne;let i;n?(t.root="/",i=1):i=0;let r=-1,s=0,o=-1,a=!0,l=e.length-1,u=0;for(;l>=i;--l){const t=e.charCodeAt(l);if(t!==Ne)-1===o&&(a=!1,o=l+1),t===we?-1===r?r=l:1!==u&&(u=1):-1!==r&&(u=-1);else if(!a){s=l+1;break}}if(-1!==o){const i=0===s&&n?1:s;-1===r||0===u||1===u&&r===o-1&&r===s+1?t.base=t.name=e.slice(i,o):(t.name=e.slice(i,r),t.base=e.slice(i,o),t.ext=e.slice(r,o))}return s>0?t.dir=e.slice(0,s-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};Fe.win32=Ie.win32=Ie,Fe.posix=Ie.posix=Fe,xe?Ie.normalize:Fe.normalize,xe?Ie.resolve:Fe.resolve,xe?Ie.relative:Fe.relative,xe?Ie.dirname:Fe.dirname,xe?Ie.basename:Fe.basename,xe?Ie.extname:Fe.extname,xe?Ie.sep:Fe.sep;const qe=/^\w[\w\d+.-]*$/,Ke=/^\//,Ve=/^\/\//;const Be="",Ue="/",We=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class He{static isUri(e){return e instanceof He||!!e&&("string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString)}constructor(e,t,n,i,r,s=!1){"object"==typeof e?(this.scheme=e.scheme||Be,this.authority=e.authority||Be,this.path=e.path||Be,this.query=e.query||Be,this.fragment=e.fragment||Be):(this.scheme=function(e,t){return e||t?e:"file"}(e,s),this.authority=t||Be,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==Ue&&(t=Ue+t):t=Ue}return t}(this.scheme,n||Be),this.query=i||Be,this.fragment=r||Be,function(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!qe.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!Ke.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(Ve.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}(this,s))}get fsPath(){return Ye(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:i,query:r,fragment:s}=e;return void 0===t?t=this.scheme:null===t&&(t=Be),void 0===n?n=this.authority:null===n&&(n=Be),void 0===i?i=this.path:null===i&&(i=Be),void 0===r?r=this.query:null===r&&(r=Be),void 0===s?s=this.fragment:null===s&&(s=Be),t===this.scheme&&n===this.authority&&i===this.path&&r===this.query&&s===this.fragment?this:new $e(t,n,i,r,s)}static parse(e,t=!1){const n=We.exec(e);return n?new $e(n[2]||Be,et(n[4]||Be),et(n[5]||Be),et(n[7]||Be),et(n[9]||Be),t):new $e(Be,Be,Be,Be,Be)}static file(e){let t=Be;if(V&&(e=e.replace(/\\/g,Ue)),e[0]===Ue&&e[1]===Ue){const n=e.indexOf(Ue,2);-1===n?(t=e.substring(2),e=Ue):(t=e.substring(2,n),e=e.substring(n)||Ue)}return new $e("file",t,e,Be,Be)}static from(e,t){return new $e(e.scheme,e.authority,e.path,e.query,e.fragment,t)}static joinPath(e,...t){if(!e.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let n;return n=V&&"file"===e.scheme?He.file(Ie.join(Ye(e,!0),...t)).path:Fe.join(e.path,...t),e.with({path:n})}toString(e=!1){return Xe(this,e)}toJSON(){return this}static revive(e){var t,n;if(e){if(e instanceof He)return e;{const i=new $e(e);return i._formatted=null!==(t=e.external)&&void 0!==t?t:null,i._fsPath=e._sep===ze&&null!==(n=e.fsPath)&&void 0!==n?n:null,i}}return e}}const ze=V?1:void 0;class $e extends He{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=Ye(this,!1)),this._fsPath}toString(e=!1){return e?Xe(this,!0):(this._formatted||(this._formatted=Xe(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=ze),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const je={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function Ge(e,t,n){let i,r=-1;for(let s=0;s<e.length;s++){const o=e.charCodeAt(s);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o||n&&91===o||n&&93===o||n&&58===o)-1!==r&&(i+=encodeURIComponent(e.substring(r,s)),r=-1),void 0!==i&&(i+=e.charAt(s));else{void 0===i&&(i=e.substr(0,s));const t=je[o];void 0!==t?(-1!==r&&(i+=encodeURIComponent(e.substring(r,s)),r=-1),i+=t):-1===r&&(r=s)}}return-1!==r&&(i+=encodeURIComponent(e.substring(r))),void 0!==i?i:e}function Qe(e){let t;for(let n=0;n<e.length;n++){const i=e.charCodeAt(n);35===i||63===i?(void 0===t&&(t=e.substr(0,n)),t+=je[i]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function Ye(e,t){let n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?`//${e.authority}${e.path}`:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,V&&(n=n.replace(/\//g,"\\")),n}function Xe(e,t){const n=t?Qe:Ge;let i="",{scheme:r,authority:s,path:o,query:a,fragment:l}=e;if(r&&(i+=r,i+=":"),(s||"file"===r)&&(i+=Ue,i+=Ue),s){let e=s.indexOf("@");if(-1!==e){const t=s.substr(0,e);s=s.substr(e+1),e=t.lastIndexOf(":"),-1===e?i+=n(t,!1,!1):(i+=n(t.substr(0,e),!1,!1),i+=":",i+=n(t.substr(e+1),!1,!0)),i+="@"}s=s.toLowerCase(),e=s.lastIndexOf(":"),-1===e?i+=n(s,!1,!0):(i+=n(s.substr(0,e),!1,!0),i+=s.substr(e))}if(o){if(o.length>=3&&47===o.charCodeAt(0)&&58===o.charCodeAt(2)){const e=o.charCodeAt(1);e>=65&&e<=90&&(o=`/${String.fromCharCode(e+32)}:${o.substr(3)}`)}else if(o.length>=2&&58===o.charCodeAt(1)){const e=o.charCodeAt(0);e>=65&&e<=90&&(o=`${String.fromCharCode(e+32)}:${o.substr(2)}`)}i+=n(o,!0,!1)}return a&&(i+="?",i+=n(a,!1,!1)),l&&(i+="#",i+=t?l:Ge(l,!1,!1)),i}function Je(e){try{return decodeURIComponent(e)}catch(t){return e.length>3?e.substr(0,3)+Je(e.substr(3)):e}}const Ze=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function et(e){return e.match(Ze)?e.replace(Ze,(e=>Je(e))):e}class tt{constructor(e,t){this.lineNumber=e,this.column=t}with(e=this.lineNumber,t=this.column){return e===this.lineNumber&&t===this.column?this:new tt(e,t)}delta(e=0,t=0){return this.with(this.lineNumber+e,this.column+t)}equals(e){return tt.equals(this,e)}static equals(e,t){return!e&&!t||!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column}isBefore(e){return tt.isBefore(this,e)}static isBefore(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<t.column}isBeforeOrEqual(e){return tt.isBeforeOrEqual(this,e)}static isBeforeOrEqual(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<=t.column}static compare(e,t){const n=0|e.lineNumber,i=0|t.lineNumber;if(n===i){return(0|e.column)-(0|t.column)}return n-i}clone(){return new tt(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(e){return new tt(e.lineNumber,e.column)}static isIPosition(e){return e&&"number"==typeof e.lineNumber&&"number"==typeof e.column}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}}class nt{constructor(e,t,n,i){e>n||e===n&&t>i?(this.startLineNumber=n,this.startColumn=i,this.endLineNumber=e,this.endColumn=t):(this.startLineNumber=e,this.startColumn=t,this.endLineNumber=n,this.endColumn=i)}isEmpty(){return nt.isEmpty(this)}static isEmpty(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn}containsPosition(e){return nt.containsPosition(this,e)}static containsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber)&&(!(t.lineNumber===e.startLineNumber&&t.column<e.startColumn)&&!(t.lineNumber===e.endLineNumber&&t.column>e.endColumn))}static strictContainsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber)&&(!(t.lineNumber===e.startLineNumber&&t.column<=e.startColumn)&&!(t.lineNumber===e.endLineNumber&&t.column>=e.endColumn))}containsRange(e){return nt.containsRange(this,e)}static containsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn)))}strictContainsRange(e){return nt.strictContainsRange(this,e)}static strictContainsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn)))}plusRange(e){return nt.plusRange(this,e)}static plusRange(e,t){let n,i,r,s;return t.startLineNumber<e.startLineNumber?(n=t.startLineNumber,i=t.startColumn):t.startLineNumber===e.startLineNumber?(n=t.startLineNumber,i=Math.min(t.startColumn,e.startColumn)):(n=e.startLineNumber,i=e.startColumn),t.endLineNumber>e.endLineNumber?(r=t.endLineNumber,s=t.endColumn):t.endLineNumber===e.endLineNumber?(r=t.endLineNumber,s=Math.max(t.endColumn,e.endColumn)):(r=e.endLineNumber,s=e.endColumn),new nt(n,i,r,s)}intersectRanges(e){return nt.intersectRanges(this,e)}static intersectRanges(e,t){let n=e.startLineNumber,i=e.startColumn,r=e.endLineNumber,s=e.endColumn;const o=t.startLineNumber,a=t.startColumn,l=t.endLineNumber,u=t.endColumn;return n<o?(n=o,i=a):n===o&&(i=Math.max(i,a)),r>l?(r=l,s=u):r===l&&(s=Math.min(s,u)),n>r||n===r&&i>s?null:new nt(n,i,r,s)}equalsRange(e){return nt.equalsRange(this,e)}static equalsRange(e,t){return!e&&!t||!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn}getEndPosition(){return nt.getEndPosition(this)}static getEndPosition(e){return new tt(e.endLineNumber,e.endColumn)}getStartPosition(){return nt.getStartPosition(this)}static getStartPosition(e){return new tt(e.startLineNumber,e.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(e,t){return new nt(this.startLineNumber,this.startColumn,e,t)}setStartPosition(e,t){return new nt(e,t,this.endLineNumber,this.endColumn)}collapseToStart(){return nt.collapseToStart(this)}static collapseToStart(e){return new nt(e.startLineNumber,e.startColumn,e.startLineNumber,e.startColumn)}collapseToEnd(){return nt.collapseToEnd(this)}static collapseToEnd(e){return new nt(e.endLineNumber,e.endColumn,e.endLineNumber,e.endColumn)}delta(e){return new nt(this.startLineNumber+e,this.startColumn,this.endLineNumber+e,this.endColumn)}static fromPositions(e,t=e){return new nt(e.lineNumber,e.column,t.lineNumber,t.column)}static lift(e){return e?new nt(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):null}static isIRange(e){return e&&"number"==typeof e.startLineNumber&&"number"==typeof e.startColumn&&"number"==typeof e.endLineNumber&&"number"==typeof e.endColumn}static areIntersectingOrTouching(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<e.startColumn)}static areIntersecting(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<=t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<=e.startColumn)}static compareRangesUsingStarts(e,t){if(e&&t){const n=0|e.startLineNumber,i=0|t.startLineNumber;if(n===i){const n=0|e.startColumn,i=0|t.startColumn;if(n===i){const n=0|e.endLineNumber,i=0|t.endLineNumber;if(n===i){return(0|e.endColumn)-(0|t.endColumn)}return n-i}return n-i}return n-i}return(e?1:0)-(t?1:0)}static compareRangesUsingEnds(e,t){return e.endLineNumber===t.endLineNumber?e.endColumn===t.endColumn?e.startLineNumber===t.startLineNumber?e.startColumn-t.startColumn:e.startLineNumber-t.startLineNumber:e.endColumn-t.endColumn:e.endLineNumber-t.endLineNumber}static spansMultipleLines(e){return e.endLineNumber>e.startLineNumber}toJSON(){return this}}var it,rt;function st(e,t){return(n,i)=>t(e(n),e(i))}(rt=it||(it={})).isLessThan=function(e){return e<0},rt.isLessThanOrEqual=function(e){return e<=0},rt.isGreaterThan=function(e){return e>0},rt.isNeitherLessOrGreaterThan=function(e){return 0===e},rt.greaterThan=1,rt.lessThan=-1,rt.neitherLessOrGreaterThan=0;const ot=(e,t)=>e-t;function at(e){return e<0?0:e>255?255:0|e}function lt(e){return e<0?0:e>4294967295?4294967295:0|e}class ut{constructor(e){this.values=e,this.prefixSum=new Uint32Array(e.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(e,t){e=lt(e);const n=this.values,i=this.prefixSum,r=t.length;return 0!==r&&(this.values=new Uint32Array(n.length+r),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e),e+r),this.values.set(t,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(i.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(e,t){return e=lt(e),t=lt(t),this.values[e]!==t&&(this.values[e]=t,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)}removeValues(e,t){e=lt(e),t=lt(t);const n=this.values,i=this.prefixSum;if(e>=n.length)return!1;const r=n.length-e;return t>=r&&(t=r),0!==t&&(this.values=new Uint32Array(n.length-t),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e+t),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(i.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return 0===this.values.length?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(e){return e<0?0:(e=lt(e),this._getPrefixSum(e))}_getPrefixSum(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];let t=this.prefixSumValidIndex[0]+1;0===t&&(this.prefixSum[0]=this.values[0],t++),e>=this.values.length&&(e=this.values.length-1);for(let n=t;n<=e;n++)this.prefixSum[n]=this.prefixSum[n-1]+this.values[n];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.values.length-1,i=0,r=0,s=0;for(;t<=n;)if(i=t+(n-t)/2|0,r=this.prefixSum[i],s=r-this.values[i],e<s)n=i-1;else{if(!(e>=r))break;t=i+1}return new ht(i,e-s)}}class ht{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}}class ct{constructor(e,t,n,i){this._uri=e,this._lines=t,this._eol=n,this._versionId=i,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return null===this._cachedTextValue&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);const t=e.changes;for(const n of t)this._acceptDeleteRange(n.range),this._acceptInsertText(new tt(n.range.startLineNumber,n.range.startColumn),n.text);this._versionId=e.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const e=this._eol.length,t=this._lines.length,n=new Uint32Array(t);for(let i=0;i<t;i++)n[i]=this._lines[i].length+e;this._lineStarts=new ut(n)}}_setLineText(e,t){this._lines[e]=t,this._lineStarts&&this._lineStarts.setValue(e,this._lines[e].length+this._eol.length)}_acceptDeleteRange(e){if(e.startLineNumber!==e.endLineNumber)this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber);else{if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1))}}_acceptInsertText(e,t){if(0===t.length)return;const n=t.split(/\r\n|\r|\n/);if(1===n.length)return void this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]+this._lines[e.lineNumber-1].substring(e.column-1));n[n.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]);const i=new Uint32Array(n.length-1);for(let r=1;r<n.length;r++)this._lines.splice(e.lineNumber+r-1,0,n[r]),i[r-1]=n[r].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,i)}}const dt=function(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of"`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?")e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}();function gt(e){let t=dt;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}const mt=new m;function ft(e,t,n,i,r){if(t=gt(t),r||(r=l.first(mt)),n.length>r.maxLen){let s=e-r.maxLen/2;return s<0?s=0:i+=s,ft(e,t,n=n.substring(s,e+r.maxLen/2),i,r)}const s=Date.now(),o=e-1-i;let a=-1,u=null;for(let l=1;!(Date.now()-s>=r.timeBudget);l++){const e=o-r.windowSize*l;t.lastIndex=Math.max(0,e);const i=pt(t,n,o,a);if(!i&&u)break;if(u=i,e<=0)break;a=e}if(u){const e={word:u[0],startColumn:i+1+u.index,endColumn:i+1+u.index+u[0].length};return t.lastIndex=0,e}return null}function pt(e,t,n,i){let r;for(;r=e.exec(t);){const t=r.index||0;if(t<=n&&e.lastIndex>=n)return r;if(i>0&&t>i)return null}return null}mt.unshift({maxLen:1e3,windowSize:15,timeBudget:150});class bt{constructor(e){const t=at(e);this._defaultValue=t,this._asciiMap=bt._createAsciiMap(t),this._map=new Map}static _createAsciiMap(e){const t=new Uint8Array(256);return t.fill(e),t}set(e,t){const n=at(t);e>=0&&e<256?this._asciiMap[e]=n:this._map.set(e,n)}get(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue}clear(){this._asciiMap.fill(this._defaultValue),this._map.clear()}}class _t{constructor(e,t,n){const i=new Uint8Array(e*t);for(let r=0,s=e*t;r<s;r++)i[r]=n;this._data=i,this.rows=e,this.cols=t}get(e,t){return this._data[e*this.cols+t]}set(e,t,n){this._data[e*this.cols+t]=n}}class Ct{constructor(e){let t=0,n=0;for(let r=0,s=e.length;r<s;r++){const[i,s,o]=e[r];s>t&&(t=s),i>n&&(n=i),o>n&&(n=o)}t++,n++;const i=new _t(n,t,0);for(let r=0,s=e.length;r<s;r++){const[t,n,s]=e[r];i.set(t,n,s)}this._states=i,this._maxCharCode=t}nextState(e,t){return t<0||t>=this._maxCharCode?0:this._states.get(e,t)}}let yt=null;let vt=null;class Lt{static _createLink(e,t,n,i,r){let s=r-1;do{const n=t.charCodeAt(s);if(2!==e.get(n))break;s--}while(s>i);if(i>0){const e=t.charCodeAt(i-1),n=t.charCodeAt(s);(40===e&&41===n||91===e&&93===n||123===e&&125===n)&&s--}return{range:{startLineNumber:n,startColumn:i+1,endLineNumber:n,endColumn:s+2},url:t.substring(i,s+1)}}static computeLinks(e,t=function(){return null===yt&&(yt=new Ct([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),yt}()){const n=function(){if(null===vt){vt=new bt(0);const e=" \t<>'\"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…";for(let n=0;n<e.length;n++)vt.set(e.charCodeAt(n),1);const t=".,;:";for(let n=0;n<t.length;n++)vt.set(t.charCodeAt(n),2)}return vt}(),i=[];for(let r=1,s=e.getLineCount();r<=s;r++){const s=e.getLineContent(r),o=s.length;let a=0,l=0,u=0,h=1,c=!1,d=!1,g=!1,m=!1;for(;a<o;){let e=!1;const o=s.charCodeAt(a);if(13===h){let t;switch(o){case 40:c=!0,t=0;break;case 41:t=c?0:1;break;case 91:g=!0,d=!0,t=0;break;case 93:g=!1,t=d?0:1;break;case 123:m=!0,t=0;break;case 125:t=m?0:1;break;case 39:case 34:case 96:t=u===o?1:39===u||34===u||96===u?0:1;break;case 42:t=42===u?1:0;break;case 124:t=124===u?1:0;break;case 32:t=g?0:1;break;default:t=n.get(o)}1===t&&(i.push(Lt._createLink(n,s,r,l,a)),e=!0)}else if(12===h){let t;91===o?(d=!0,t=0):t=n.get(o),1===t?e=!0:h=13}else h=t.nextState(h,o),0===h&&(e=!0);e&&(h=1,c=!1,d=!1,m=!1,l=a+1,u=o),a++}13===h&&i.push(Lt._createLink(n,s,r,l,o))}return i}}class wt{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(e,t,n,i,r){if(e&&t){const n=this.doNavigateValueSet(t,r);if(n)return{range:e,value:n}}if(n&&i){const e=this.doNavigateValueSet(i,r);if(e)return{range:n,value:e}}return null}doNavigateValueSet(e,t){const n=this.numberReplace(e,t);return null!==n?n:this.textReplace(e,t)}numberReplace(e,t){const n=Math.pow(10,e.length-(e.lastIndexOf(".")+1));let i=Number(e);const r=parseFloat(e);return isNaN(i)||isNaN(r)||i!==r?null:0!==i||t?(i=Math.floor(i*n),i+=t?n:-n,String(i/n)):null}textReplace(e,t){return this.valueSetsReplace(this._defaultValueSet,e,t)}valueSetsReplace(e,t,n){let i=null;for(let r=0,s=e.length;null===i&&r<s;r++)i=this.valueSetReplace(e[r],t,n);return i}valueSetReplace(e,t,n){let i=e.indexOf(t);return i>=0?(i+=n?1:-1,i<0?i=e.length-1:i%=e.length,e[i]):null}}wt.INSTANCE=new wt;const Nt=Object.freeze((function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}));var Et,St;(St=Et||(Et={})).isCancellationToken=function(e){return e===St.None||e===St.Cancelled||e instanceof Rt||!(!e||"object"!=typeof e)&&"boolean"==typeof e.isCancellationRequested&&"function"==typeof e.onCancellationRequested},St.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:b.None}),St.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Nt});class Rt{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Nt:(this._emitter||(this._emitter=new L),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class At{constructor(e){this._token=void 0,this._parentListener=void 0,this._parentListener=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new Rt),this._token}cancel(){this._token?this._token instanceof Rt&&this._token.cancel():this._token=Et.Cancelled}dispose(e=!1){var t;e&&this.cancel(),null===(t=this._parentListener)||void 0===t||t.dispose(),this._token?this._token instanceof Rt&&this._token.dispose():this._token=Et.None}}class xt{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}}const Mt=new xt,kt=new xt,Ot=new xt,Tt=new Array(230),Pt=Object.create(null),It=Object.create(null);var Dt,Ft;!function(){const e="",t=[[1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[1,1,"Hyper",0,e,0,e,e,e],[1,2,"Super",0,e,0,e,e,e],[1,3,"Fn",0,e,0,e,e,e],[1,4,"FnLock",0,e,0,e,e,e],[1,5,"Suspend",0,e,0,e,e,e],[1,6,"Resume",0,e,0,e,e,e],[1,7,"Turbo",0,e,0,e,e,e],[1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[1,9,"WakeUp",0,e,0,e,e,e],[0,10,"KeyA",31,"A",65,"VK_A",e,e],[0,11,"KeyB",32,"B",66,"VK_B",e,e],[0,12,"KeyC",33,"C",67,"VK_C",e,e],[0,13,"KeyD",34,"D",68,"VK_D",e,e],[0,14,"KeyE",35,"E",69,"VK_E",e,e],[0,15,"KeyF",36,"F",70,"VK_F",e,e],[0,16,"KeyG",37,"G",71,"VK_G",e,e],[0,17,"KeyH",38,"H",72,"VK_H",e,e],[0,18,"KeyI",39,"I",73,"VK_I",e,e],[0,19,"KeyJ",40,"J",74,"VK_J",e,e],[0,20,"KeyK",41,"K",75,"VK_K",e,e],[0,21,"KeyL",42,"L",76,"VK_L",e,e],[0,22,"KeyM",43,"M",77,"VK_M",e,e],[0,23,"KeyN",44,"N",78,"VK_N",e,e],[0,24,"KeyO",45,"O",79,"VK_O",e,e],[0,25,"KeyP",46,"P",80,"VK_P",e,e],[0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[0,27,"KeyR",48,"R",82,"VK_R",e,e],[0,28,"KeyS",49,"S",83,"VK_S",e,e],[0,29,"KeyT",50,"T",84,"VK_T",e,e],[0,30,"KeyU",51,"U",85,"VK_U",e,e],[0,31,"KeyV",52,"V",86,"VK_V",e,e],[0,32,"KeyW",53,"W",87,"VK_W",e,e],[0,33,"KeyX",54,"X",88,"VK_X",e,e],[0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[0,36,"Digit1",22,"1",49,"VK_1",e,e],[0,37,"Digit2",23,"2",50,"VK_2",e,e],[0,38,"Digit3",24,"3",51,"VK_3",e,e],[0,39,"Digit4",25,"4",52,"VK_4",e,e],[0,40,"Digit5",26,"5",53,"VK_5",e,e],[0,41,"Digit6",27,"6",54,"VK_6",e,e],[0,42,"Digit7",28,"7",55,"VK_7",e,e],[0,43,"Digit8",29,"8",56,"VK_8",e,e],[0,44,"Digit9",30,"9",57,"VK_9",e,e],[0,45,"Digit0",21,"0",48,"VK_0",e,e],[1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[0,51,"Minus",88,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[0,52,"Equal",86,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[0,53,"BracketLeft",92,"[",219,"VK_OEM_4","[","OEM_4"],[0,54,"BracketRight",94,"]",221,"VK_OEM_6","]","OEM_6"],[0,55,"Backslash",93,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,56,"IntlHash",0,e,0,e,e,e],[0,57,"Semicolon",85,";",186,"VK_OEM_1",";","OEM_1"],[0,58,"Quote",95,"'",222,"VK_OEM_7","'","OEM_7"],[0,59,"Backquote",91,"`",192,"VK_OEM_3","`","OEM_3"],[0,60,"Comma",87,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[0,61,"Period",89,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[0,62,"Slash",90,"/",191,"VK_OEM_2","/","OEM_2"],[1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[1,64,"F1",59,"F1",112,"VK_F1",e,e],[1,65,"F2",60,"F2",113,"VK_F2",e,e],[1,66,"F3",61,"F3",114,"VK_F3",e,e],[1,67,"F4",62,"F4",115,"VK_F4",e,e],[1,68,"F5",63,"F5",116,"VK_F5",e,e],[1,69,"F6",64,"F6",117,"VK_F6",e,e],[1,70,"F7",65,"F7",118,"VK_F7",e,e],[1,71,"F8",66,"F8",119,"VK_F8",e,e],[1,72,"F9",67,"F9",120,"VK_F9",e,e],[1,73,"F10",68,"F10",121,"VK_F10",e,e],[1,74,"F11",69,"F11",122,"VK_F11",e,e],[1,75,"F12",70,"F12",123,"VK_F12",e,e],[1,76,"PrintScreen",0,e,0,e,e,e],[1,77,"ScrollLock",84,"ScrollLock",145,"VK_SCROLL",e,e],[1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[1,80,"Home",14,"Home",36,"VK_HOME",e,e],[1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[1,83,"End",13,"End",35,"VK_END",e,e],[1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[1,89,"NumLock",83,"NumLock",144,"VK_NUMLOCK",e,e],[1,90,"NumpadDivide",113,"NumPad_Divide",111,"VK_DIVIDE",e,e],[1,91,"NumpadMultiply",108,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[1,92,"NumpadSubtract",111,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[1,93,"NumpadAdd",109,"NumPad_Add",107,"VK_ADD",e,e],[1,94,"NumpadEnter",3,e,0,e,e,e],[1,95,"Numpad1",99,"NumPad1",97,"VK_NUMPAD1",e,e],[1,96,"Numpad2",100,"NumPad2",98,"VK_NUMPAD2",e,e],[1,97,"Numpad3",101,"NumPad3",99,"VK_NUMPAD3",e,e],[1,98,"Numpad4",102,"NumPad4",100,"VK_NUMPAD4",e,e],[1,99,"Numpad5",103,"NumPad5",101,"VK_NUMPAD5",e,e],[1,100,"Numpad6",104,"NumPad6",102,"VK_NUMPAD6",e,e],[1,101,"Numpad7",105,"NumPad7",103,"VK_NUMPAD7",e,e],[1,102,"Numpad8",106,"NumPad8",104,"VK_NUMPAD8",e,e],[1,103,"Numpad9",107,"NumPad9",105,"VK_NUMPAD9",e,e],[1,104,"Numpad0",98,"NumPad0",96,"VK_NUMPAD0",e,e],[1,105,"NumpadDecimal",112,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[0,106,"IntlBackslash",97,"OEM_102",226,"VK_OEM_102",e,e],[1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[1,108,"Power",0,e,0,e,e,e],[1,109,"NumpadEqual",0,e,0,e,e,e],[1,110,"F13",71,"F13",124,"VK_F13",e,e],[1,111,"F14",72,"F14",125,"VK_F14",e,e],[1,112,"F15",73,"F15",126,"VK_F15",e,e],[1,113,"F16",74,"F16",127,"VK_F16",e,e],[1,114,"F17",75,"F17",128,"VK_F17",e,e],[1,115,"F18",76,"F18",129,"VK_F18",e,e],[1,116,"F19",77,"F19",130,"VK_F19",e,e],[1,117,"F20",78,"F20",131,"VK_F20",e,e],[1,118,"F21",79,"F21",132,"VK_F21",e,e],[1,119,"F22",80,"F22",133,"VK_F22",e,e],[1,120,"F23",81,"F23",134,"VK_F23",e,e],[1,121,"F24",82,"F24",135,"VK_F24",e,e],[1,122,"Open",0,e,0,e,e,e],[1,123,"Help",0,e,0,e,e,e],[1,124,"Select",0,e,0,e,e,e],[1,125,"Again",0,e,0,e,e,e],[1,126,"Undo",0,e,0,e,e,e],[1,127,"Cut",0,e,0,e,e,e],[1,128,"Copy",0,e,0,e,e,e],[1,129,"Paste",0,e,0,e,e,e],[1,130,"Find",0,e,0,e,e,e],[1,131,"AudioVolumeMute",117,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[1,132,"AudioVolumeUp",118,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[1,133,"AudioVolumeDown",119,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[1,134,"NumpadComma",110,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[0,135,"IntlRo",115,"ABNT_C1",193,"VK_ABNT_C1",e,e],[1,136,"KanaMode",0,e,0,e,e,e],[0,137,"IntlYen",0,e,0,e,e,e],[1,138,"Convert",0,e,0,e,e,e],[1,139,"NonConvert",0,e,0,e,e,e],[1,140,"Lang1",0,e,0,e,e,e],[1,141,"Lang2",0,e,0,e,e,e],[1,142,"Lang3",0,e,0,e,e,e],[1,143,"Lang4",0,e,0,e,e,e],[1,144,"Lang5",0,e,0,e,e,e],[1,145,"Abort",0,e,0,e,e,e],[1,146,"Props",0,e,0,e,e,e],[1,147,"NumpadParenLeft",0,e,0,e,e,e],[1,148,"NumpadParenRight",0,e,0,e,e,e],[1,149,"NumpadBackspace",0,e,0,e,e,e],[1,150,"NumpadMemoryStore",0,e,0,e,e,e],[1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[1,152,"NumpadMemoryClear",0,e,0,e,e,e],[1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[1,155,"NumpadClear",131,"Clear",12,"VK_CLEAR",e,e],[1,156,"NumpadClearEntry",0,e,0,e,e,e],[1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[1,0,e,6,"Alt",18,"VK_MENU",e,e],[1,0,e,57,"Meta",91,"VK_COMMAND",e,e],[1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[1,165,"BrightnessUp",0,e,0,e,e,e],[1,166,"BrightnessDown",0,e,0,e,e,e],[1,167,"MediaPlay",0,e,0,e,e,e],[1,168,"MediaRecord",0,e,0,e,e,e],[1,169,"MediaFastForward",0,e,0,e,e,e],[1,170,"MediaRewind",0,e,0,e,e,e],[1,171,"MediaTrackNext",124,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[1,172,"MediaTrackPrevious",125,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[1,173,"MediaStop",126,"MediaStop",178,"VK_MEDIA_STOP",e,e],[1,174,"Eject",0,e,0,e,e,e],[1,175,"MediaPlayPause",127,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[1,176,"MediaSelect",128,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[1,177,"LaunchMail",129,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[1,178,"LaunchApp2",130,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[1,180,"SelectTask",0,e,0,e,e,e],[1,181,"LaunchScreenSaver",0,e,0,e,e,e],[1,182,"BrowserSearch",120,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[1,183,"BrowserHome",121,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[1,184,"BrowserBack",122,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[1,185,"BrowserForward",123,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[1,189,"ZoomToggle",0,e,0,e,e,e],[1,190,"MailReply",0,e,0,e,e,e],[1,191,"MailForward",0,e,0,e,e,e],[1,192,"MailSend",0,e,0,e,e,e],[1,0,e,114,"KeyInComposition",229,e,e,e],[1,0,e,116,"ABNT_C2",194,"VK_ABNT_C2",e,e],[1,0,e,96,"OEM_8",223,"VK_OEM_8",e,e],[1,0,e,0,e,0,"VK_KANA",e,e],[1,0,e,0,e,0,"VK_HANGUL",e,e],[1,0,e,0,e,0,"VK_JUNJA",e,e],[1,0,e,0,e,0,"VK_FINAL",e,e],[1,0,e,0,e,0,"VK_HANJA",e,e],[1,0,e,0,e,0,"VK_KANJI",e,e],[1,0,e,0,e,0,"VK_CONVERT",e,e],[1,0,e,0,e,0,"VK_NONCONVERT",e,e],[1,0,e,0,e,0,"VK_ACCEPT",e,e],[1,0,e,0,e,0,"VK_MODECHANGE",e,e],[1,0,e,0,e,0,"VK_SELECT",e,e],[1,0,e,0,e,0,"VK_PRINT",e,e],[1,0,e,0,e,0,"VK_EXECUTE",e,e],[1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[1,0,e,0,e,0,"VK_HELP",e,e],[1,0,e,0,e,0,"VK_APPS",e,e],[1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[1,0,e,0,e,0,"VK_PACKET",e,e],[1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[1,0,e,0,e,0,"VK_ATTN",e,e],[1,0,e,0,e,0,"VK_CRSEL",e,e],[1,0,e,0,e,0,"VK_EXSEL",e,e],[1,0,e,0,e,0,"VK_EREOF",e,e],[1,0,e,0,e,0,"VK_PLAY",e,e],[1,0,e,0,e,0,"VK_ZOOM",e,e],[1,0,e,0,e,0,"VK_NONAME",e,e],[1,0,e,0,e,0,"VK_PA1",e,e],[1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]],n=[],i=[];for(const r of t){const[e,t,s,o,a,l,u,h,c]=r;if(i[t]||(i[t]=!0,Pt[s]=t,It[s.toLowerCase()]=t),!n[o]){if(n[o]=!0,!a)throw new Error(`String representation missing for key code ${o} around scan code ${s}`);Mt.define(o,a),kt.define(o,h||a),Ot.define(o,c||h||a)}l&&(Tt[l]=o)}}(),(Ft=Dt||(Dt={})).toString=function(e){return Mt.keyCodeToStr(e)},Ft.fromString=function(e){return Mt.strToKeyCode(e)},Ft.toUserSettingsUS=function(e){return kt.keyCodeToStr(e)},Ft.toUserSettingsGeneral=function(e){return Ot.keyCodeToStr(e)},Ft.fromUserSettings=function(e){return kt.strToKeyCode(e)||Ot.strToKeyCode(e)},Ft.toElectronAccelerator=function(e){if(e>=98&&e<=113)return null;switch(e){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return Mt.keyCodeToStr(e)};class qt extends nt{constructor(e,t,n,i){super(e,t,n,i),this.selectionStartLineNumber=e,this.selectionStartColumn=t,this.positionLineNumber=n,this.positionColumn=i}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(e){return qt.selectionsEqual(this,e)}static selectionsEqual(e,t){return e.selectionStartLineNumber===t.selectionStartLineNumber&&e.selectionStartColumn===t.selectionStartColumn&&e.positionLineNumber===t.positionLineNumber&&e.positionColumn===t.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(e,t){return 0===this.getDirection()?new qt(this.startLineNumber,this.startColumn,e,t):new qt(e,t,this.startLineNumber,this.startColumn)}getPosition(){return new tt(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new tt(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(e,t){return 0===this.getDirection()?new qt(e,t,this.endLineNumber,this.endColumn):new qt(this.endLineNumber,this.endColumn,e,t)}static fromPositions(e,t=e){return new qt(e.lineNumber,e.column,t.lineNumber,t.column)}static fromRange(e,t){return 0===t?new qt(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):new qt(e.endLineNumber,e.endColumn,e.startLineNumber,e.startColumn)}static liftSelection(e){return new qt(e.selectionStartLineNumber,e.selectionStartColumn,e.positionLineNumber,e.positionColumn)}static selectionsArrEqual(e,t){if(e&&!t||!e&&t)return!1;if(!e&&!t)return!0;if(e.length!==t.length)return!1;for(let n=0,i=e.length;n<i;n++)if(!this.selectionsEqual(e[n],t[n]))return!1;return!0}static isISelection(e){return e&&"number"==typeof e.selectionStartLineNumber&&"number"==typeof e.selectionStartColumn&&"number"==typeof e.positionLineNumber&&"number"==typeof e.positionColumn}static createWithDirection(e,t,n,i,r){return 0===r?new qt(e,t,n,i):new qt(n,i,e,t)}}const Kt=Object.create(null);function Vt(e,t){if("string"==typeof t){const n=Kt[t];if(void 0===n)throw new Error(`${e} references an unknown codicon: ${t}`);t=n}return Kt[e]=t,{id:e}}const Bt={add:Vt("add",6e4),plus:Vt("plus",6e4),gistNew:Vt("gist-new",6e4),repoCreate:Vt("repo-create",6e4),lightbulb:Vt("lightbulb",60001),lightBulb:Vt("light-bulb",60001),repo:Vt("repo",60002),repoDelete:Vt("repo-delete",60002),gistFork:Vt("gist-fork",60003),repoForked:Vt("repo-forked",60003),gitPullRequest:Vt("git-pull-request",60004),gitPullRequestAbandoned:Vt("git-pull-request-abandoned",60004),recordKeys:Vt("record-keys",60005),keyboard:Vt("keyboard",60005),tag:Vt("tag",60006),tagAdd:Vt("tag-add",60006),tagRemove:Vt("tag-remove",60006),gitPullRequestLabel:Vt("git-pull-request-label",60006),person:Vt("person",60007),personFollow:Vt("person-follow",60007),personOutline:Vt("person-outline",60007),personFilled:Vt("person-filled",60007),gitBranch:Vt("git-branch",60008),gitBranchCreate:Vt("git-branch-create",60008),gitBranchDelete:Vt("git-branch-delete",60008),sourceControl:Vt("source-control",60008),mirror:Vt("mirror",60009),mirrorPublic:Vt("mirror-public",60009),star:Vt("star",60010),starAdd:Vt("star-add",60010),starDelete:Vt("star-delete",60010),starEmpty:Vt("star-empty",60010),comment:Vt("comment",60011),commentAdd:Vt("comment-add",60011),alert:Vt("alert",60012),warning:Vt("warning",60012),search:Vt("search",60013),searchSave:Vt("search-save",60013),logOut:Vt("log-out",60014),signOut:Vt("sign-out",60014),logIn:Vt("log-in",60015),signIn:Vt("sign-in",60015),eye:Vt("eye",60016),eyeUnwatch:Vt("eye-unwatch",60016),eyeWatch:Vt("eye-watch",60016),circleFilled:Vt("circle-filled",60017),primitiveDot:Vt("primitive-dot",60017),closeDirty:Vt("close-dirty",60017),debugBreakpoint:Vt("debug-breakpoint",60017),debugBreakpointDisabled:Vt("debug-breakpoint-disabled",60017),debugBreakpointPending:Vt("debug-breakpoint-pending",60377),debugHint:Vt("debug-hint",60017),primitiveSquare:Vt("primitive-square",60018),edit:Vt("edit",60019),pencil:Vt("pencil",60019),info:Vt("info",60020),issueOpened:Vt("issue-opened",60020),gistPrivate:Vt("gist-private",60021),gitForkPrivate:Vt("git-fork-private",60021),lock:Vt("lock",60021),mirrorPrivate:Vt("mirror-private",60021),close:Vt("close",60022),removeClose:Vt("remove-close",60022),x:Vt("x",60022),repoSync:Vt("repo-sync",60023),sync:Vt("sync",60023),clone:Vt("clone",60024),desktopDownload:Vt("desktop-download",60024),beaker:Vt("beaker",60025),microscope:Vt("microscope",60025),vm:Vt("vm",60026),deviceDesktop:Vt("device-desktop",60026),file:Vt("file",60027),fileText:Vt("file-text",60027),more:Vt("more",60028),ellipsis:Vt("ellipsis",60028),kebabHorizontal:Vt("kebab-horizontal",60028),mailReply:Vt("mail-reply",60029),reply:Vt("reply",60029),organization:Vt("organization",60030),organizationFilled:Vt("organization-filled",60030),organizationOutline:Vt("organization-outline",60030),newFile:Vt("new-file",60031),fileAdd:Vt("file-add",60031),newFolder:Vt("new-folder",60032),fileDirectoryCreate:Vt("file-directory-create",60032),trash:Vt("trash",60033),trashcan:Vt("trashcan",60033),history:Vt("history",60034),clock:Vt("clock",60034),folder:Vt("folder",60035),fileDirectory:Vt("file-directory",60035),symbolFolder:Vt("symbol-folder",60035),logoGithub:Vt("logo-github",60036),markGithub:Vt("mark-github",60036),github:Vt("github",60036),terminal:Vt("terminal",60037),console:Vt("console",60037),repl:Vt("repl",60037),zap:Vt("zap",60038),symbolEvent:Vt("symbol-event",60038),error:Vt("error",60039),stop:Vt("stop",60039),variable:Vt("variable",60040),symbolVariable:Vt("symbol-variable",60040),array:Vt("array",60042),symbolArray:Vt("symbol-array",60042),symbolModule:Vt("symbol-module",60043),symbolPackage:Vt("symbol-package",60043),symbolNamespace:Vt("symbol-namespace",60043),symbolObject:Vt("symbol-object",60043),symbolMethod:Vt("symbol-method",60044),symbolFunction:Vt("symbol-function",60044),symbolConstructor:Vt("symbol-constructor",60044),symbolBoolean:Vt("symbol-boolean",60047),symbolNull:Vt("symbol-null",60047),symbolNumeric:Vt("symbol-numeric",60048),symbolNumber:Vt("symbol-number",60048),symbolStructure:Vt("symbol-structure",60049),symbolStruct:Vt("symbol-struct",60049),symbolParameter:Vt("symbol-parameter",60050),symbolTypeParameter:Vt("symbol-type-parameter",60050),symbolKey:Vt("symbol-key",60051),symbolText:Vt("symbol-text",60051),symbolReference:Vt("symbol-reference",60052),goToFile:Vt("go-to-file",60052),symbolEnum:Vt("symbol-enum",60053),symbolValue:Vt("symbol-value",60053),symbolRuler:Vt("symbol-ruler",60054),symbolUnit:Vt("symbol-unit",60054),activateBreakpoints:Vt("activate-breakpoints",60055),archive:Vt("archive",60056),arrowBoth:Vt("arrow-both",60057),arrowDown:Vt("arrow-down",60058),arrowLeft:Vt("arrow-left",60059),arrowRight:Vt("arrow-right",60060),arrowSmallDown:Vt("arrow-small-down",60061),arrowSmallLeft:Vt("arrow-small-left",60062),arrowSmallRight:Vt("arrow-small-right",60063),arrowSmallUp:Vt("arrow-small-up",60064),arrowUp:Vt("arrow-up",60065),bell:Vt("bell",60066),bold:Vt("bold",60067),book:Vt("book",60068),bookmark:Vt("bookmark",60069),debugBreakpointConditionalUnverified:Vt("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:Vt("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:Vt("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:Vt("debug-breakpoint-data-unverified",60072),debugBreakpointData:Vt("debug-breakpoint-data",60073),debugBreakpointDataDisabled:Vt("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:Vt("debug-breakpoint-log-unverified",60074),debugBreakpointLog:Vt("debug-breakpoint-log",60075),debugBreakpointLogDisabled:Vt("debug-breakpoint-log-disabled",60075),briefcase:Vt("briefcase",60076),broadcast:Vt("broadcast",60077),browser:Vt("browser",60078),bug:Vt("bug",60079),calendar:Vt("calendar",60080),caseSensitive:Vt("case-sensitive",60081),check:Vt("check",60082),checklist:Vt("checklist",60083),chevronDown:Vt("chevron-down",60084),dropDownButton:Vt("drop-down-button",60084),chevronLeft:Vt("chevron-left",60085),chevronRight:Vt("chevron-right",60086),chevronUp:Vt("chevron-up",60087),chromeClose:Vt("chrome-close",60088),chromeMaximize:Vt("chrome-maximize",60089),chromeMinimize:Vt("chrome-minimize",60090),chromeRestore:Vt("chrome-restore",60091),circle:Vt("circle",60092),circleOutline:Vt("circle-outline",60092),debugBreakpointUnverified:Vt("debug-breakpoint-unverified",60092),circleSlash:Vt("circle-slash",60093),circuitBoard:Vt("circuit-board",60094),clearAll:Vt("clear-all",60095),clippy:Vt("clippy",60096),closeAll:Vt("close-all",60097),cloudDownload:Vt("cloud-download",60098),cloudUpload:Vt("cloud-upload",60099),code:Vt("code",60100),collapseAll:Vt("collapse-all",60101),colorMode:Vt("color-mode",60102),commentDiscussion:Vt("comment-discussion",60103),compareChanges:Vt("compare-changes",60157),creditCard:Vt("credit-card",60105),dash:Vt("dash",60108),dashboard:Vt("dashboard",60109),database:Vt("database",60110),debugContinue:Vt("debug-continue",60111),debugDisconnect:Vt("debug-disconnect",60112),debugPause:Vt("debug-pause",60113),debugRestart:Vt("debug-restart",60114),debugStart:Vt("debug-start",60115),debugStepInto:Vt("debug-step-into",60116),debugStepOut:Vt("debug-step-out",60117),debugStepOver:Vt("debug-step-over",60118),debugStop:Vt("debug-stop",60119),debug:Vt("debug",60120),deviceCameraVideo:Vt("device-camera-video",60121),deviceCamera:Vt("device-camera",60122),deviceMobile:Vt("device-mobile",60123),diffAdded:Vt("diff-added",60124),diffIgnored:Vt("diff-ignored",60125),diffModified:Vt("diff-modified",60126),diffRemoved:Vt("diff-removed",60127),diffRenamed:Vt("diff-renamed",60128),diff:Vt("diff",60129),discard:Vt("discard",60130),editorLayout:Vt("editor-layout",60131),emptyWindow:Vt("empty-window",60132),exclude:Vt("exclude",60133),extensions:Vt("extensions",60134),eyeClosed:Vt("eye-closed",60135),fileBinary:Vt("file-binary",60136),fileCode:Vt("file-code",60137),fileMedia:Vt("file-media",60138),filePdf:Vt("file-pdf",60139),fileSubmodule:Vt("file-submodule",60140),fileSymlinkDirectory:Vt("file-symlink-directory",60141),fileSymlinkFile:Vt("file-symlink-file",60142),fileZip:Vt("file-zip",60143),files:Vt("files",60144),filter:Vt("filter",60145),flame:Vt("flame",60146),foldDown:Vt("fold-down",60147),foldUp:Vt("fold-up",60148),fold:Vt("fold",60149),folderActive:Vt("folder-active",60150),folderOpened:Vt("folder-opened",60151),gear:Vt("gear",60152),gift:Vt("gift",60153),gistSecret:Vt("gist-secret",60154),gist:Vt("gist",60155),gitCommit:Vt("git-commit",60156),gitCompare:Vt("git-compare",60157),gitMerge:Vt("git-merge",60158),githubAction:Vt("github-action",60159),githubAlt:Vt("github-alt",60160),globe:Vt("globe",60161),grabber:Vt("grabber",60162),graph:Vt("graph",60163),gripper:Vt("gripper",60164),heart:Vt("heart",60165),home:Vt("home",60166),horizontalRule:Vt("horizontal-rule",60167),hubot:Vt("hubot",60168),inbox:Vt("inbox",60169),issueClosed:Vt("issue-closed",60324),issueReopened:Vt("issue-reopened",60171),issues:Vt("issues",60172),italic:Vt("italic",60173),jersey:Vt("jersey",60174),json:Vt("json",60175),bracket:Vt("bracket",60175),kebabVertical:Vt("kebab-vertical",60176),key:Vt("key",60177),law:Vt("law",60178),lightbulbAutofix:Vt("lightbulb-autofix",60179),linkExternal:Vt("link-external",60180),link:Vt("link",60181),listOrdered:Vt("list-ordered",60182),listUnordered:Vt("list-unordered",60183),liveShare:Vt("live-share",60184),loading:Vt("loading",60185),location:Vt("location",60186),mailRead:Vt("mail-read",60187),mail:Vt("mail",60188),markdown:Vt("markdown",60189),megaphone:Vt("megaphone",60190),mention:Vt("mention",60191),milestone:Vt("milestone",60192),gitPullRequestMilestone:Vt("git-pull-request-milestone",60192),mortarBoard:Vt("mortar-board",60193),move:Vt("move",60194),multipleWindows:Vt("multiple-windows",60195),mute:Vt("mute",60196),noNewline:Vt("no-newline",60197),note:Vt("note",60198),octoface:Vt("octoface",60199),openPreview:Vt("open-preview",60200),package:Vt("package",60201),paintcan:Vt("paintcan",60202),pin:Vt("pin",60203),play:Vt("play",60204),run:Vt("run",60204),plug:Vt("plug",60205),preserveCase:Vt("preserve-case",60206),preview:Vt("preview",60207),project:Vt("project",60208),pulse:Vt("pulse",60209),question:Vt("question",60210),quote:Vt("quote",60211),radioTower:Vt("radio-tower",60212),reactions:Vt("reactions",60213),references:Vt("references",60214),refresh:Vt("refresh",60215),regex:Vt("regex",60216),remoteExplorer:Vt("remote-explorer",60217),remote:Vt("remote",60218),remove:Vt("remove",60219),replaceAll:Vt("replace-all",60220),replace:Vt("replace",60221),repoClone:Vt("repo-clone",60222),repoForcePush:Vt("repo-force-push",60223),repoPull:Vt("repo-pull",60224),repoPush:Vt("repo-push",60225),report:Vt("report",60226),requestChanges:Vt("request-changes",60227),rocket:Vt("rocket",60228),rootFolderOpened:Vt("root-folder-opened",60229),rootFolder:Vt("root-folder",60230),rss:Vt("rss",60231),ruby:Vt("ruby",60232),saveAll:Vt("save-all",60233),saveAs:Vt("save-as",60234),save:Vt("save",60235),screenFull:Vt("screen-full",60236),screenNormal:Vt("screen-normal",60237),searchStop:Vt("search-stop",60238),server:Vt("server",60240),settingsGear:Vt("settings-gear",60241),settings:Vt("settings",60242),shield:Vt("shield",60243),smiley:Vt("smiley",60244),sortPrecedence:Vt("sort-precedence",60245),splitHorizontal:Vt("split-horizontal",60246),splitVertical:Vt("split-vertical",60247),squirrel:Vt("squirrel",60248),starFull:Vt("star-full",60249),starHalf:Vt("star-half",60250),symbolClass:Vt("symbol-class",60251),symbolColor:Vt("symbol-color",60252),symbolCustomColor:Vt("symbol-customcolor",60252),symbolConstant:Vt("symbol-constant",60253),symbolEnumMember:Vt("symbol-enum-member",60254),symbolField:Vt("symbol-field",60255),symbolFile:Vt("symbol-file",60256),symbolInterface:Vt("symbol-interface",60257),symbolKeyword:Vt("symbol-keyword",60258),symbolMisc:Vt("symbol-misc",60259),symbolOperator:Vt("symbol-operator",60260),symbolProperty:Vt("symbol-property",60261),wrench:Vt("wrench",60261),wrenchSubaction:Vt("wrench-subaction",60261),symbolSnippet:Vt("symbol-snippet",60262),tasklist:Vt("tasklist",60263),telescope:Vt("telescope",60264),textSize:Vt("text-size",60265),threeBars:Vt("three-bars",60266),thumbsdown:Vt("thumbsdown",60267),thumbsup:Vt("thumbsup",60268),tools:Vt("tools",60269),triangleDown:Vt("triangle-down",60270),triangleLeft:Vt("triangle-left",60271),triangleRight:Vt("triangle-right",60272),triangleUp:Vt("triangle-up",60273),twitter:Vt("twitter",60274),unfold:Vt("unfold",60275),unlock:Vt("unlock",60276),unmute:Vt("unmute",60277),unverified:Vt("unverified",60278),verified:Vt("verified",60279),versions:Vt("versions",60280),vmActive:Vt("vm-active",60281),vmOutline:Vt("vm-outline",60282),vmRunning:Vt("vm-running",60283),watch:Vt("watch",60284),whitespace:Vt("whitespace",60285),wholeWord:Vt("whole-word",60286),window:Vt("window",60287),wordWrap:Vt("word-wrap",60288),zoomIn:Vt("zoom-in",60289),zoomOut:Vt("zoom-out",60290),listFilter:Vt("list-filter",60291),listFlat:Vt("list-flat",60292),listSelection:Vt("list-selection",60293),selection:Vt("selection",60293),listTree:Vt("list-tree",60294),debugBreakpointFunctionUnverified:Vt("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:Vt("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:Vt("debug-breakpoint-function-disabled",60296),debugStackframeActive:Vt("debug-stackframe-active",60297),circleSmallFilled:Vt("circle-small-filled",60298),debugStackframeDot:Vt("debug-stackframe-dot",60298),debugStackframe:Vt("debug-stackframe",60299),debugStackframeFocused:Vt("debug-stackframe-focused",60299),debugBreakpointUnsupported:Vt("debug-breakpoint-unsupported",60300),symbolString:Vt("symbol-string",60301),debugReverseContinue:Vt("debug-reverse-continue",60302),debugStepBack:Vt("debug-step-back",60303),debugRestartFrame:Vt("debug-restart-frame",60304),callIncoming:Vt("call-incoming",60306),callOutgoing:Vt("call-outgoing",60307),menu:Vt("menu",60308),expandAll:Vt("expand-all",60309),feedback:Vt("feedback",60310),gitPullRequestReviewer:Vt("git-pull-request-reviewer",60310),groupByRefType:Vt("group-by-ref-type",60311),ungroupByRefType:Vt("ungroup-by-ref-type",60312),account:Vt("account",60313),gitPullRequestAssignee:Vt("git-pull-request-assignee",60313),bellDot:Vt("bell-dot",60314),debugConsole:Vt("debug-console",60315),library:Vt("library",60316),output:Vt("output",60317),runAll:Vt("run-all",60318),syncIgnored:Vt("sync-ignored",60319),pinned:Vt("pinned",60320),githubInverted:Vt("github-inverted",60321),debugAlt:Vt("debug-alt",60305),serverProcess:Vt("server-process",60322),serverEnvironment:Vt("server-environment",60323),pass:Vt("pass",60324),stopCircle:Vt("stop-circle",60325),playCircle:Vt("play-circle",60326),record:Vt("record",60327),debugAltSmall:Vt("debug-alt-small",60328),vmConnect:Vt("vm-connect",60329),cloud:Vt("cloud",60330),merge:Vt("merge",60331),exportIcon:Vt("export",60332),graphLeft:Vt("graph-left",60333),magnet:Vt("magnet",60334),notebook:Vt("notebook",60335),redo:Vt("redo",60336),checkAll:Vt("check-all",60337),pinnedDirty:Vt("pinned-dirty",60338),passFilled:Vt("pass-filled",60339),circleLargeFilled:Vt("circle-large-filled",60340),circleLarge:Vt("circle-large",60341),circleLargeOutline:Vt("circle-large-outline",60341),combine:Vt("combine",60342),gather:Vt("gather",60342),table:Vt("table",60343),variableGroup:Vt("variable-group",60344),typeHierarchy:Vt("type-hierarchy",60345),typeHierarchySub:Vt("type-hierarchy-sub",60346),typeHierarchySuper:Vt("type-hierarchy-super",60347),gitPullRequestCreate:Vt("git-pull-request-create",60348),runAbove:Vt("run-above",60349),runBelow:Vt("run-below",60350),notebookTemplate:Vt("notebook-template",60351),debugRerun:Vt("debug-rerun",60352),workspaceTrusted:Vt("workspace-trusted",60353),workspaceUntrusted:Vt("workspace-untrusted",60354),workspaceUnspecified:Vt("workspace-unspecified",60355),terminalCmd:Vt("terminal-cmd",60356),terminalDebian:Vt("terminal-debian",60357),terminalLinux:Vt("terminal-linux",60358),terminalPowershell:Vt("terminal-powershell",60359),terminalTmux:Vt("terminal-tmux",60360),terminalUbuntu:Vt("terminal-ubuntu",60361),terminalBash:Vt("terminal-bash",60362),arrowSwap:Vt("arrow-swap",60363),copy:Vt("copy",60364),personAdd:Vt("person-add",60365),filterFilled:Vt("filter-filled",60366),wand:Vt("wand",60367),debugLineByLine:Vt("debug-line-by-line",60368),inspect:Vt("inspect",60369),layers:Vt("layers",60370),layersDot:Vt("layers-dot",60371),layersActive:Vt("layers-active",60372),compass:Vt("compass",60373),compassDot:Vt("compass-dot",60374),compassActive:Vt("compass-active",60375),azure:Vt("azure",60376),issueDraft:Vt("issue-draft",60377),gitPullRequestClosed:Vt("git-pull-request-closed",60378),gitPullRequestDraft:Vt("git-pull-request-draft",60379),debugAll:Vt("debug-all",60380),debugCoverage:Vt("debug-coverage",60381),runErrors:Vt("run-errors",60382),folderLibrary:Vt("folder-library",60383),debugContinueSmall:Vt("debug-continue-small",60384),beakerStop:Vt("beaker-stop",60385),graphLine:Vt("graph-line",60386),graphScatter:Vt("graph-scatter",60387),pieChart:Vt("pie-chart",60388),bracketDot:Vt("bracket-dot",60389),bracketError:Vt("bracket-error",60390),lockSmall:Vt("lock-small",60391),azureDevops:Vt("azure-devops",60392),verifiedFilled:Vt("verified-filled",60393),newLine:Vt("newline",60394),layout:Vt("layout",60395),layoutActivitybarLeft:Vt("layout-activitybar-left",60396),layoutActivitybarRight:Vt("layout-activitybar-right",60397),layoutPanelLeft:Vt("layout-panel-left",60398),layoutPanelCenter:Vt("layout-panel-center",60399),layoutPanelJustify:Vt("layout-panel-justify",60400),layoutPanelRight:Vt("layout-panel-right",60401),layoutPanel:Vt("layout-panel",60402),layoutSidebarLeft:Vt("layout-sidebar-left",60403),layoutSidebarRight:Vt("layout-sidebar-right",60404),layoutStatusbar:Vt("layout-statusbar",60405),layoutMenubar:Vt("layout-menubar",60406),layoutCentered:Vt("layout-centered",60407),layoutSidebarRightOff:Vt("layout-sidebar-right-off",60416),layoutPanelOff:Vt("layout-panel-off",60417),layoutSidebarLeftOff:Vt("layout-sidebar-left-off",60418),target:Vt("target",60408),indent:Vt("indent",60409),recordSmall:Vt("record-small",60410),errorSmall:Vt("error-small",60411),arrowCircleDown:Vt("arrow-circle-down",60412),arrowCircleLeft:Vt("arrow-circle-left",60413),arrowCircleRight:Vt("arrow-circle-right",60414),arrowCircleUp:Vt("arrow-circle-up",60415),heartFilled:Vt("heart-filled",60420),map:Vt("map",60421),mapFilled:Vt("map-filled",60422),circleSmall:Vt("circle-small",60423),bellSlash:Vt("bell-slash",60424),bellSlashDot:Vt("bell-slash-dot",60425),commentUnresolved:Vt("comment-unresolved",60426),gitPullRequestGoToChanges:Vt("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:Vt("git-pull-request-new-changes",60428),searchFuzzy:Vt("search-fuzzy",60429),commentDraft:Vt("comment-draft",60430),send:Vt("send",60431),sparkle:Vt("sparkle",60432),insert:Vt("insert",60433),mic:Vt("mic",60434),thumbsDownFilled:Vt("thumbsdown-filled",60435),thumbsUpFilled:Vt("thumbsup-filled",60436),coffee:Vt("coffee",60437),snake:Vt("snake",60438),game:Vt("game",60439),vr:Vt("vr",60440),chip:Vt("chip",60441),piano:Vt("piano",60442),music:Vt("music",60443),micFilled:Vt("mic-filled",60444),gitFetch:Vt("git-fetch",60445),copilot:Vt("copilot",60446),lightbulbSparkle:Vt("lightbulb-sparkle",60447),lightbulbSparkleAutofix:Vt("lightbulb-sparkle-autofix",60447),robot:Vt("robot",60448),sparkleFilled:Vt("sparkle-filled",60449),diffSingle:Vt("diff-single",60450),diffMultiple:Vt("diff-multiple",60451),surroundWith:Vt("surround-with",60452),gitStash:Vt("git-stash",60454),gitStashApply:Vt("git-stash-apply",60455),gitStashPop:Vt("git-stash-pop",60456),dialogError:Vt("dialog-error","error"),dialogWarning:Vt("dialog-warning","warning"),dialogInfo:Vt("dialog-info","info"),dialogClose:Vt("dialog-close","close"),treeItemExpanded:Vt("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:Vt("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:Vt("tree-filter-on-type-off","list-selection"),treeFilterClear:Vt("tree-filter-clear","close"),treeItemLoading:Vt("tree-item-loading","loading"),menuSelection:Vt("menu-selection","check"),menuSubmenu:Vt("menu-submenu","chevron-right"),menuBarMore:Vt("menubar-more","more"),scrollbarButtonLeft:Vt("scrollbar-button-left","triangle-left"),scrollbarButtonRight:Vt("scrollbar-button-right","triangle-right"),scrollbarButtonUp:Vt("scrollbar-button-up","triangle-up"),scrollbarButtonDown:Vt("scrollbar-button-down","triangle-down"),toolBarMore:Vt("toolbar-more","more"),quickInputBack:Vt("quick-input-back","arrow-left")};class Ut extends d{get isResolved(){return this._isResolved}constructor(e,t,n){super(),this._registry=e,this._languageId=t,this._factory=n,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}dispose(){this._isDisposed=!0,super.dispose()}async resolve(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise}async _create(){const e=await this._factory.tokenizationSupport;this._isResolved=!0,e&&!this._isDisposed&&this._register(this._registry.register(this._languageId,e))}}class Wt{constructor(e,t,n){this.offset=e,this.type=t,this.language=n,this._tokenBrand=void 0}toString(){return"("+this.offset+", "+this.type+")"}}var Ht,zt,$t,jt,Gt,Qt,Yt,Xt,Jt,Zt,en,tn,nn,rn,sn,on,an,ln,un,hn,cn,dn,gn,mn,fn,pn,bn,_n,Cn,yn,vn,Ln,wn,Nn,En,Sn,Rn,An,xn,Mn,kn,On,Tn,Pn,In,Dn,Fn,qn,Kn,Vn,Bn,Un,Wn,Hn,zn,$n,jn,Gn,Qn,Yn,Xn,Jn,Zn,ei,ti,ni,ii,ri,si,oi,ai,li,ui,hi,ci,di,gi,mi,fi,pi,bi,_i,Ci,yi,vi,Li,wi,Ni,Ei,Si,Ri;!function(e){const t=new Map;t.set(0,Bt.symbolMethod),t.set(1,Bt.symbolFunction),t.set(2,Bt.symbolConstructor),t.set(3,Bt.symbolField),t.set(4,Bt.symbolVariable),t.set(5,Bt.symbolClass),t.set(6,Bt.symbolStruct),t.set(7,Bt.symbolInterface),t.set(8,Bt.symbolModule),t.set(9,Bt.symbolProperty),t.set(10,Bt.symbolEvent),t.set(11,Bt.symbolOperator),t.set(12,Bt.symbolUnit),t.set(13,Bt.symbolValue),t.set(15,Bt.symbolEnum),t.set(14,Bt.symbolConstant),t.set(15,Bt.symbolEnum),t.set(16,Bt.symbolEnumMember),t.set(17,Bt.symbolKeyword),t.set(27,Bt.symbolSnippet),t.set(18,Bt.symbolText),t.set(19,Bt.symbolColor),t.set(20,Bt.symbolFile),t.set(21,Bt.symbolReference),t.set(22,Bt.symbolCustomColor),t.set(23,Bt.symbolFolder),t.set(24,Bt.symbolTypeParameter),t.set(25,Bt.account),t.set(26,Bt.issues),e.toIcon=function(e){let n=t.get(e);return n||(n=Bt.symbolProperty),n};const n=new Map;n.set("method",0),n.set("function",1),n.set("constructor",2),n.set("field",3),n.set("variable",4),n.set("class",5),n.set("struct",6),n.set("interface",7),n.set("module",8),n.set("property",9),n.set("event",10),n.set("operator",11),n.set("unit",12),n.set("value",13),n.set("constant",14),n.set("enum",15),n.set("enum-member",16),n.set("enumMember",16),n.set("keyword",17),n.set("snippet",27),n.set("text",18),n.set("color",19),n.set("file",20),n.set("reference",21),n.set("customcolor",22),n.set("folder",23),n.set("type-parameter",24),n.set("typeParameter",24),n.set("account",25),n.set("issue",26),e.fromString=function(e,t){let i=n.get(e);return void 0!==i||t||(i=9),i}}(Ht||(Ht={})),($t=zt||(zt={}))[$t.Automatic=0]="Automatic",$t[$t.Explicit=1]="Explicit",(Gt=jt||(jt={}))[Gt.Invoke=1]="Invoke",Gt[Gt.TriggerCharacter=2]="TriggerCharacter",Gt[Gt.ContentChange=3]="ContentChange",(Yt=Qt||(Qt={}))[Yt.Text=0]="Text",Yt[Yt.Read=1]="Read",Yt[Yt.Write=2]="Write",S(0,"array"),S(0,"boolean"),S(0,"class"),S(0,"constant"),S(0,"constructor"),S(0,"enumeration"),S(0,"enumeration member"),S(0,"event"),S(0,"field"),S(0,"file"),S(0,"function"),S(0,"interface"),S(0,"key"),S(0,"method"),S(0,"module"),S(0,"namespace"),S(0,"null"),S(0,"number"),S(0,"object"),S(0,"operator"),S(0,"package"),S(0,"property"),S(0,"string"),S(0,"struct"),S(0,"type parameter"),S(0,"variable"),function(e){const t=new Map;t.set(0,Bt.symbolFile),t.set(1,Bt.symbolModule),t.set(2,Bt.symbolNamespace),t.set(3,Bt.symbolPackage),t.set(4,Bt.symbolClass),t.set(5,Bt.symbolMethod),t.set(6,Bt.symbolProperty),t.set(7,Bt.symbolField),t.set(8,Bt.symbolConstructor),t.set(9,Bt.symbolEnum),t.set(10,Bt.symbolInterface),t.set(11,Bt.symbolFunction),t.set(12,Bt.symbolVariable),t.set(13,Bt.symbolConstant),t.set(14,Bt.symbolString),t.set(15,Bt.symbolNumber),t.set(16,Bt.symbolBoolean),t.set(17,Bt.symbolArray),t.set(18,Bt.symbolObject),t.set(19,Bt.symbolKey),t.set(20,Bt.symbolNull),t.set(21,Bt.symbolEnumMember),t.set(22,Bt.symbolStruct),t.set(23,Bt.symbolEvent),t.set(24,Bt.symbolOperator),t.set(25,Bt.symbolTypeParameter),e.toIcon=function(e){let n=t.get(e);return n||(n=Bt.symbolProperty),n}}(Xt||(Xt={})),(Jt||(Jt={})).is=function(e){return!(!e||"object"!=typeof e)&&"string"==typeof e.id&&"string"==typeof e.title},(en=Zt||(Zt={}))[en.Type=1]="Type",en[en.Parameter=2]="Parameter",new class{constructor(){this._tokenizationSupports=new Map,this._factories=new Map,this._onDidChange=new L,this.onDidChange=this._onDidChange.event,this._colorMap=null}handleChange(e){this._onDidChange.fire({changedLanguages:e,changedColorMap:!1})}register(e,t){return this._tokenizationSupports.set(e,t),this.handleChange([e]),h((()=>{this._tokenizationSupports.get(e)===t&&(this._tokenizationSupports.delete(e),this.handleChange([e]))}))}get(e){return this._tokenizationSupports.get(e)||null}registerFactory(e,t){var n;null===(n=this._factories.get(e))||void 0===n||n.dispose();const i=new Ut(this,e,t);return this._factories.set(e,i),h((()=>{const t=this._factories.get(e);t&&t===i&&(this._factories.delete(e),t.dispose())}))}async getOrCreate(e){const t=this.get(e);if(t)return t;const n=this._factories.get(e);return!n||n.isResolved?null:(await n.resolve(),this.get(e))}isResolved(e){if(this.get(e))return!0;const t=this._factories.get(e);return!(t&&!t.isResolved)}setColorMap(e){this._colorMap=e,this._onDidChange.fire({changedLanguages:Array.from(this._tokenizationSupports.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}},(nn=tn||(tn={}))[nn.Unknown=0]="Unknown",nn[nn.Disabled=1]="Disabled",nn[nn.Enabled=2]="Enabled",(sn=rn||(rn={}))[sn.Invoke=1]="Invoke",sn[sn.Auto=2]="Auto",(an=on||(on={}))[an.None=0]="None",an[an.KeepWhitespace=1]="KeepWhitespace",an[an.InsertAsSnippet=4]="InsertAsSnippet",(un=ln||(ln={}))[un.Method=0]="Method",un[un.Function=1]="Function",un[un.Constructor=2]="Constructor",un[un.Field=3]="Field",un[un.Variable=4]="Variable",un[un.Class=5]="Class",un[un.Struct=6]="Struct",un[un.Interface=7]="Interface",un[un.Module=8]="Module",un[un.Property=9]="Property",un[un.Event=10]="Event",un[un.Operator=11]="Operator",un[un.Unit=12]="Unit",un[un.Value=13]="Value",un[un.Constant=14]="Constant",un[un.Enum=15]="Enum",un[un.EnumMember=16]="EnumMember",un[un.Keyword=17]="Keyword",un[un.Text=18]="Text",un[un.Color=19]="Color",un[un.File=20]="File",un[un.Reference=21]="Reference",un[un.Customcolor=22]="Customcolor",un[un.Folder=23]="Folder",un[un.TypeParameter=24]="TypeParameter",un[un.User=25]="User",un[un.Issue=26]="Issue",un[un.Snippet=27]="Snippet",(cn=hn||(hn={}))[cn.Deprecated=1]="Deprecated",(gn=dn||(dn={}))[gn.Invoke=0]="Invoke",gn[gn.TriggerCharacter=1]="TriggerCharacter",gn[gn.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions",(fn=mn||(mn={}))[fn.EXACT=0]="EXACT",fn[fn.ABOVE=1]="ABOVE",fn[fn.BELOW=2]="BELOW",(bn=pn||(pn={}))[bn.NotSet=0]="NotSet",bn[bn.ContentFlush=1]="ContentFlush",bn[bn.RecoverFromMarkers=2]="RecoverFromMarkers",bn[bn.Explicit=3]="Explicit",bn[bn.Paste=4]="Paste",bn[bn.Undo=5]="Undo",bn[bn.Redo=6]="Redo",(Cn=_n||(_n={}))[Cn.LF=1]="LF",Cn[Cn.CRLF=2]="CRLF",function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"}(yn||(yn={})),(Ln=vn||(vn={}))[Ln.None=0]="None",Ln[Ln.Keep=1]="Keep",Ln[Ln.Brackets=2]="Brackets",Ln[Ln.Advanced=3]="Advanced",Ln[Ln.Full=4]="Full",(Nn=wn||(wn={}))[Nn.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",Nn[Nn.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",Nn[Nn.accessibilitySupport=2]="accessibilitySupport",Nn[Nn.accessibilityPageSize=3]="accessibilityPageSize",Nn[Nn.ariaLabel=4]="ariaLabel",Nn[Nn.ariaRequired=5]="ariaRequired",Nn[Nn.autoClosingBrackets=6]="autoClosingBrackets",Nn[Nn.autoClosingComments=7]="autoClosingComments",Nn[Nn.screenReaderAnnounceInlineSuggestion=8]="screenReaderAnnounceInlineSuggestion",Nn[Nn.autoClosingDelete=9]="autoClosingDelete",Nn[Nn.autoClosingOvertype=10]="autoClosingOvertype",Nn[Nn.autoClosingQuotes=11]="autoClosingQuotes",Nn[Nn.autoIndent=12]="autoIndent",Nn[Nn.automaticLayout=13]="automaticLayout",Nn[Nn.autoSurround=14]="autoSurround",Nn[Nn.bracketPairColorization=15]="bracketPairColorization",Nn[Nn.guides=16]="guides",Nn[Nn.codeLens=17]="codeLens",Nn[Nn.codeLensFontFamily=18]="codeLensFontFamily",Nn[Nn.codeLensFontSize=19]="codeLensFontSize",Nn[Nn.colorDecorators=20]="colorDecorators",Nn[Nn.colorDecoratorsLimit=21]="colorDecoratorsLimit",Nn[Nn.columnSelection=22]="columnSelection",Nn[Nn.comments=23]="comments",Nn[Nn.contextmenu=24]="contextmenu",Nn[Nn.copyWithSyntaxHighlighting=25]="copyWithSyntaxHighlighting",Nn[Nn.cursorBlinking=26]="cursorBlinking",Nn[Nn.cursorSmoothCaretAnimation=27]="cursorSmoothCaretAnimation",Nn[Nn.cursorStyle=28]="cursorStyle",Nn[Nn.cursorSurroundingLines=29]="cursorSurroundingLines",Nn[Nn.cursorSurroundingLinesStyle=30]="cursorSurroundingLinesStyle",Nn[Nn.cursorWidth=31]="cursorWidth",Nn[Nn.disableLayerHinting=32]="disableLayerHinting",Nn[Nn.disableMonospaceOptimizations=33]="disableMonospaceOptimizations",Nn[Nn.domReadOnly=34]="domReadOnly",Nn[Nn.dragAndDrop=35]="dragAndDrop",Nn[Nn.dropIntoEditor=36]="dropIntoEditor",Nn[Nn.emptySelectionClipboard=37]="emptySelectionClipboard",Nn[Nn.experimentalWhitespaceRendering=38]="experimentalWhitespaceRendering",Nn[Nn.extraEditorClassName=39]="extraEditorClassName",Nn[Nn.fastScrollSensitivity=40]="fastScrollSensitivity",Nn[Nn.find=41]="find",Nn[Nn.fixedOverflowWidgets=42]="fixedOverflowWidgets",Nn[Nn.folding=43]="folding",Nn[Nn.foldingStrategy=44]="foldingStrategy",Nn[Nn.foldingHighlight=45]="foldingHighlight",Nn[Nn.foldingImportsByDefault=46]="foldingImportsByDefault",Nn[Nn.foldingMaximumRegions=47]="foldingMaximumRegions",Nn[Nn.unfoldOnClickAfterEndOfLine=48]="unfoldOnClickAfterEndOfLine",Nn[Nn.fontFamily=49]="fontFamily",Nn[Nn.fontInfo=50]="fontInfo",Nn[Nn.fontLigatures=51]="fontLigatures",Nn[Nn.fontSize=52]="fontSize",Nn[Nn.fontWeight=53]="fontWeight",Nn[Nn.fontVariations=54]="fontVariations",Nn[Nn.formatOnPaste=55]="formatOnPaste",Nn[Nn.formatOnType=56]="formatOnType",Nn[Nn.glyphMargin=57]="glyphMargin",Nn[Nn.gotoLocation=58]="gotoLocation",Nn[Nn.hideCursorInOverviewRuler=59]="hideCursorInOverviewRuler",Nn[Nn.hover=60]="hover",Nn[Nn.inDiffEditor=61]="inDiffEditor",Nn[Nn.inlineSuggest=62]="inlineSuggest",Nn[Nn.letterSpacing=63]="letterSpacing",Nn[Nn.lightbulb=64]="lightbulb",Nn[Nn.lineDecorationsWidth=65]="lineDecorationsWidth",Nn[Nn.lineHeight=66]="lineHeight",Nn[Nn.lineNumbers=67]="lineNumbers",Nn[Nn.lineNumbersMinChars=68]="lineNumbersMinChars",Nn[Nn.linkedEditing=69]="linkedEditing",Nn[Nn.links=70]="links",Nn[Nn.matchBrackets=71]="matchBrackets",Nn[Nn.minimap=72]="minimap",Nn[Nn.mouseStyle=73]="mouseStyle",Nn[Nn.mouseWheelScrollSensitivity=74]="mouseWheelScrollSensitivity",Nn[Nn.mouseWheelZoom=75]="mouseWheelZoom",Nn[Nn.multiCursorMergeOverlapping=76]="multiCursorMergeOverlapping",Nn[Nn.multiCursorModifier=77]="multiCursorModifier",Nn[Nn.multiCursorPaste=78]="multiCursorPaste",Nn[Nn.multiCursorLimit=79]="multiCursorLimit",Nn[Nn.occurrencesHighlight=80]="occurrencesHighlight",Nn[Nn.overviewRulerBorder=81]="overviewRulerBorder",Nn[Nn.overviewRulerLanes=82]="overviewRulerLanes",Nn[Nn.padding=83]="padding",Nn[Nn.pasteAs=84]="pasteAs",Nn[Nn.parameterHints=85]="parameterHints",Nn[Nn.peekWidgetDefaultFocus=86]="peekWidgetDefaultFocus",Nn[Nn.definitionLinkOpensInPeek=87]="definitionLinkOpensInPeek",Nn[Nn.quickSuggestions=88]="quickSuggestions",Nn[Nn.quickSuggestionsDelay=89]="quickSuggestionsDelay",Nn[Nn.readOnly=90]="readOnly",Nn[Nn.readOnlyMessage=91]="readOnlyMessage",Nn[Nn.renameOnType=92]="renameOnType",Nn[Nn.renderControlCharacters=93]="renderControlCharacters",Nn[Nn.renderFinalNewline=94]="renderFinalNewline",Nn[Nn.renderLineHighlight=95]="renderLineHighlight",Nn[Nn.renderLineHighlightOnlyWhenFocus=96]="renderLineHighlightOnlyWhenFocus",Nn[Nn.renderValidationDecorations=97]="renderValidationDecorations",Nn[Nn.renderWhitespace=98]="renderWhitespace",Nn[Nn.revealHorizontalRightPadding=99]="revealHorizontalRightPadding",Nn[Nn.roundedSelection=100]="roundedSelection",Nn[Nn.rulers=101]="rulers",Nn[Nn.scrollbar=102]="scrollbar",Nn[Nn.scrollBeyondLastColumn=103]="scrollBeyondLastColumn",Nn[Nn.scrollBeyondLastLine=104]="scrollBeyondLastLine",Nn[Nn.scrollPredominantAxis=105]="scrollPredominantAxis",Nn[Nn.selectionClipboard=106]="selectionClipboard",Nn[Nn.selectionHighlight=107]="selectionHighlight",Nn[Nn.selectOnLineNumbers=108]="selectOnLineNumbers",Nn[Nn.showFoldingControls=109]="showFoldingControls",Nn[Nn.showUnused=110]="showUnused",Nn[Nn.snippetSuggestions=111]="snippetSuggestions",Nn[Nn.smartSelect=112]="smartSelect",Nn[Nn.smoothScrolling=113]="smoothScrolling",Nn[Nn.stickyScroll=114]="stickyScroll",Nn[Nn.stickyTabStops=115]="stickyTabStops",Nn[Nn.stopRenderingLineAfter=116]="stopRenderingLineAfter",Nn[Nn.suggest=117]="suggest",Nn[Nn.suggestFontSize=118]="suggestFontSize",Nn[Nn.suggestLineHeight=119]="suggestLineHeight",Nn[Nn.suggestOnTriggerCharacters=120]="suggestOnTriggerCharacters",Nn[Nn.suggestSelection=121]="suggestSelection",Nn[Nn.tabCompletion=122]="tabCompletion",Nn[Nn.tabIndex=123]="tabIndex",Nn[Nn.unicodeHighlighting=124]="unicodeHighlighting",Nn[Nn.unusualLineTerminators=125]="unusualLineTerminators",Nn[Nn.useShadowDOM=126]="useShadowDOM",Nn[Nn.useTabStops=127]="useTabStops",Nn[Nn.wordBreak=128]="wordBreak",Nn[Nn.wordSeparators=129]="wordSeparators",Nn[Nn.wordWrap=130]="wordWrap",Nn[Nn.wordWrapBreakAfterCharacters=131]="wordWrapBreakAfterCharacters",Nn[Nn.wordWrapBreakBeforeCharacters=132]="wordWrapBreakBeforeCharacters",Nn[Nn.wordWrapColumn=133]="wordWrapColumn",Nn[Nn.wordWrapOverride1=134]="wordWrapOverride1",Nn[Nn.wordWrapOverride2=135]="wordWrapOverride2",Nn[Nn.wrappingIndent=136]="wrappingIndent",Nn[Nn.wrappingStrategy=137]="wrappingStrategy",Nn[Nn.showDeprecated=138]="showDeprecated",Nn[Nn.inlayHints=139]="inlayHints",Nn[Nn.editorClassName=140]="editorClassName",Nn[Nn.pixelRatio=141]="pixelRatio",Nn[Nn.tabFocusMode=142]="tabFocusMode",Nn[Nn.layoutInfo=143]="layoutInfo",Nn[Nn.wrappingInfo=144]="wrappingInfo",Nn[Nn.defaultColorDecorators=145]="defaultColorDecorators",Nn[Nn.colorDecoratorsActivatedOn=146]="colorDecoratorsActivatedOn",Nn[Nn.inlineCompletionsAccessibilityVerbose=147]="inlineCompletionsAccessibilityVerbose",(Sn=En||(En={}))[Sn.TextDefined=0]="TextDefined",Sn[Sn.LF=1]="LF",Sn[Sn.CRLF=2]="CRLF",(An=Rn||(Rn={}))[An.LF=0]="LF",An[An.CRLF=1]="CRLF",(Mn=xn||(xn={}))[Mn.Left=1]="Left",Mn[Mn.Center=2]="Center",Mn[Mn.Right=3]="Right",(On=kn||(kn={}))[On.None=0]="None",On[On.Indent=1]="Indent",On[On.IndentOutdent=2]="IndentOutdent",On[On.Outdent=3]="Outdent",(Pn=Tn||(Tn={}))[Pn.Both=0]="Both",Pn[Pn.Right=1]="Right",Pn[Pn.Left=2]="Left",Pn[Pn.None=3]="None",function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"}(In||(In={})),function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"}(Dn||(Dn={})),(qn=Fn||(Fn={}))[qn.DependsOnKbLayout=-1]="DependsOnKbLayout",qn[qn.Unknown=0]="Unknown",qn[qn.Backspace=1]="Backspace",qn[qn.Tab=2]="Tab",qn[qn.Enter=3]="Enter",qn[qn.Shift=4]="Shift",qn[qn.Ctrl=5]="Ctrl",qn[qn.Alt=6]="Alt",qn[qn.PauseBreak=7]="PauseBreak",qn[qn.CapsLock=8]="CapsLock",qn[qn.Escape=9]="Escape",qn[qn.Space=10]="Space",qn[qn.PageUp=11]="PageUp",qn[qn.PageDown=12]="PageDown",qn[qn.End=13]="End",qn[qn.Home=14]="Home",qn[qn.LeftArrow=15]="LeftArrow",qn[qn.UpArrow=16]="UpArrow",qn[qn.RightArrow=17]="RightArrow",qn[qn.DownArrow=18]="DownArrow",qn[qn.Insert=19]="Insert",qn[qn.Delete=20]="Delete",qn[qn.Digit0=21]="Digit0",qn[qn.Digit1=22]="Digit1",qn[qn.Digit2=23]="Digit2",qn[qn.Digit3=24]="Digit3",qn[qn.Digit4=25]="Digit4",qn[qn.Digit5=26]="Digit5",qn[qn.Digit6=27]="Digit6",qn[qn.Digit7=28]="Digit7",qn[qn.Digit8=29]="Digit8",qn[qn.Digit9=30]="Digit9",qn[qn.KeyA=31]="KeyA",qn[qn.KeyB=32]="KeyB",qn[qn.KeyC=33]="KeyC",qn[qn.KeyD=34]="KeyD",qn[qn.KeyE=35]="KeyE",qn[qn.KeyF=36]="KeyF",qn[qn.KeyG=37]="KeyG",qn[qn.KeyH=38]="KeyH",qn[qn.KeyI=39]="KeyI",qn[qn.KeyJ=40]="KeyJ",qn[qn.KeyK=41]="KeyK",qn[qn.KeyL=42]="KeyL",qn[qn.KeyM=43]="KeyM",qn[qn.KeyN=44]="KeyN",qn[qn.KeyO=45]="KeyO",qn[qn.KeyP=46]="KeyP",qn[qn.KeyQ=47]="KeyQ",qn[qn.KeyR=48]="KeyR",qn[qn.KeyS=49]="KeyS",qn[qn.KeyT=50]="KeyT",qn[qn.KeyU=51]="KeyU",qn[qn.KeyV=52]="KeyV",qn[qn.KeyW=53]="KeyW",qn[qn.KeyX=54]="KeyX",qn[qn.KeyY=55]="KeyY",qn[qn.KeyZ=56]="KeyZ",qn[qn.Meta=57]="Meta",qn[qn.ContextMenu=58]="ContextMenu",qn[qn.F1=59]="F1",qn[qn.F2=60]="F2",qn[qn.F3=61]="F3",qn[qn.F4=62]="F4",qn[qn.F5=63]="F5",qn[qn.F6=64]="F6",qn[qn.F7=65]="F7",qn[qn.F8=66]="F8",qn[qn.F9=67]="F9",qn[qn.F10=68]="F10",qn[qn.F11=69]="F11",qn[qn.F12=70]="F12",qn[qn.F13=71]="F13",qn[qn.F14=72]="F14",qn[qn.F15=73]="F15",qn[qn.F16=74]="F16",qn[qn.F17=75]="F17",qn[qn.F18=76]="F18",qn[qn.F19=77]="F19",qn[qn.F20=78]="F20",qn[qn.F21=79]="F21",qn[qn.F22=80]="F22",qn[qn.F23=81]="F23",qn[qn.F24=82]="F24",qn[qn.NumLock=83]="NumLock",qn[qn.ScrollLock=84]="ScrollLock",qn[qn.Semicolon=85]="Semicolon",qn[qn.Equal=86]="Equal",qn[qn.Comma=87]="Comma",qn[qn.Minus=88]="Minus",qn[qn.Period=89]="Period",qn[qn.Slash=90]="Slash",qn[qn.Backquote=91]="Backquote",qn[qn.BracketLeft=92]="BracketLeft",qn[qn.Backslash=93]="Backslash",qn[qn.BracketRight=94]="BracketRight",qn[qn.Quote=95]="Quote",qn[qn.OEM_8=96]="OEM_8",qn[qn.IntlBackslash=97]="IntlBackslash",qn[qn.Numpad0=98]="Numpad0",qn[qn.Numpad1=99]="Numpad1",qn[qn.Numpad2=100]="Numpad2",qn[qn.Numpad3=101]="Numpad3",qn[qn.Numpad4=102]="Numpad4",qn[qn.Numpad5=103]="Numpad5",qn[qn.Numpad6=104]="Numpad6",qn[qn.Numpad7=105]="Numpad7",qn[qn.Numpad8=106]="Numpad8",qn[qn.Numpad9=107]="Numpad9",qn[qn.NumpadMultiply=108]="NumpadMultiply",qn[qn.NumpadAdd=109]="NumpadAdd",qn[qn.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",qn[qn.NumpadSubtract=111]="NumpadSubtract",qn[qn.NumpadDecimal=112]="NumpadDecimal",qn[qn.NumpadDivide=113]="NumpadDivide",qn[qn.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",qn[qn.ABNT_C1=115]="ABNT_C1",qn[qn.ABNT_C2=116]="ABNT_C2",qn[qn.AudioVolumeMute=117]="AudioVolumeMute",qn[qn.AudioVolumeUp=118]="AudioVolumeUp",qn[qn.AudioVolumeDown=119]="AudioVolumeDown",qn[qn.BrowserSearch=120]="BrowserSearch",qn[qn.BrowserHome=121]="BrowserHome",qn[qn.BrowserBack=122]="BrowserBack",qn[qn.BrowserForward=123]="BrowserForward",qn[qn.MediaTrackNext=124]="MediaTrackNext",qn[qn.MediaTrackPrevious=125]="MediaTrackPrevious",qn[qn.MediaStop=126]="MediaStop",qn[qn.MediaPlayPause=127]="MediaPlayPause",qn[qn.LaunchMediaPlayer=128]="LaunchMediaPlayer",qn[qn.LaunchMail=129]="LaunchMail",qn[qn.LaunchApp2=130]="LaunchApp2",qn[qn.Clear=131]="Clear",qn[qn.MAX_VALUE=132]="MAX_VALUE",(Vn=Kn||(Kn={}))[Vn.Hint=1]="Hint",Vn[Vn.Info=2]="Info",Vn[Vn.Warning=4]="Warning",Vn[Vn.Error=8]="Error",(Un=Bn||(Bn={}))[Un.Unnecessary=1]="Unnecessary",Un[Un.Deprecated=2]="Deprecated",(Hn=Wn||(Wn={}))[Hn.Inline=1]="Inline",Hn[Hn.Gutter=2]="Gutter",($n=zn||(zn={}))[$n.UNKNOWN=0]="UNKNOWN",$n[$n.TEXTAREA=1]="TEXTAREA",$n[$n.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",$n[$n.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",$n[$n.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",$n[$n.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",$n[$n.CONTENT_TEXT=6]="CONTENT_TEXT",$n[$n.CONTENT_EMPTY=7]="CONTENT_EMPTY",$n[$n.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",$n[$n.CONTENT_WIDGET=9]="CONTENT_WIDGET",$n[$n.OVERVIEW_RULER=10]="OVERVIEW_RULER",$n[$n.SCROLLBAR=11]="SCROLLBAR",$n[$n.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",$n[$n.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR",(Gn=jn||(jn={}))[Gn.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",Gn[Gn.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",Gn[Gn.TOP_CENTER=2]="TOP_CENTER",(Yn=Qn||(Qn={}))[Yn.Left=1]="Left",Yn[Yn.Center=2]="Center",Yn[Yn.Right=4]="Right",Yn[Yn.Full=7]="Full",(Jn=Xn||(Xn={}))[Jn.Left=0]="Left",Jn[Jn.Right=1]="Right",Jn[Jn.None=2]="None",Jn[Jn.LeftOfInjectedText=3]="LeftOfInjectedText",Jn[Jn.RightOfInjectedText=4]="RightOfInjectedText",(ei=Zn||(Zn={}))[ei.Off=0]="Off",ei[ei.On=1]="On",ei[ei.Relative=2]="Relative",ei[ei.Interval=3]="Interval",ei[ei.Custom=4]="Custom",(ni=ti||(ti={}))[ni.None=0]="None",ni[ni.Text=1]="Text",ni[ni.Blocks=2]="Blocks",(ri=ii||(ii={}))[ri.Smooth=0]="Smooth",ri[ri.Immediate=1]="Immediate",(oi=si||(si={}))[oi.Auto=1]="Auto",oi[oi.Hidden=2]="Hidden",oi[oi.Visible=3]="Visible",(li=ai||(ai={}))[li.LTR=0]="LTR",li[li.RTL=1]="RTL",(hi=ui||(ui={})).Off="off",hi.OnCode="onCode",hi.On="on",function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"}(ci||(ci={})),(gi=di||(di={}))[gi.File=0]="File",gi[gi.Module=1]="Module",gi[gi.Namespace=2]="Namespace",gi[gi.Package=3]="Package",gi[gi.Class=4]="Class",gi[gi.Method=5]="Method",gi[gi.Property=6]="Property",gi[gi.Field=7]="Field",gi[gi.Constructor=8]="Constructor",gi[gi.Enum=9]="Enum",gi[gi.Interface=10]="Interface",gi[gi.Function=11]="Function",gi[gi.Variable=12]="Variable",gi[gi.Constant=13]="Constant",gi[gi.String=14]="String",gi[gi.Number=15]="Number",gi[gi.Boolean=16]="Boolean",gi[gi.Array=17]="Array",gi[gi.Object=18]="Object",gi[gi.Key=19]="Key",gi[gi.Null=20]="Null",gi[gi.EnumMember=21]="EnumMember",gi[gi.Struct=22]="Struct",gi[gi.Event=23]="Event",gi[gi.Operator=24]="Operator",gi[gi.TypeParameter=25]="TypeParameter",(fi=mi||(mi={}))[fi.Deprecated=1]="Deprecated",(bi=pi||(pi={}))[bi.Hidden=0]="Hidden",bi[bi.Blink=1]="Blink",bi[bi.Smooth=2]="Smooth",bi[bi.Phase=3]="Phase",bi[bi.Expand=4]="Expand",bi[bi.Solid=5]="Solid",(Ci=_i||(_i={}))[Ci.Line=1]="Line",Ci[Ci.Block=2]="Block",Ci[Ci.Underline=3]="Underline",Ci[Ci.LineThin=4]="LineThin",Ci[Ci.BlockOutline=5]="BlockOutline",Ci[Ci.UnderlineThin=6]="UnderlineThin",(vi=yi||(yi={}))[vi.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",vi[vi.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",vi[vi.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",vi[vi.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter",(wi=Li||(Li={}))[wi.None=0]="None",wi[wi.Same=1]="Same",wi[wi.Indent=2]="Indent",wi[wi.DeepIndent=3]="DeepIndent";class Ai{static chord(e,t){return function(e,t){return(e|(65535&t)<<16>>>0)>>>0}(e,t)}}function xi(e,t,n,i,r){return function(e,t,n,i,r){if(0===i)return!0;const s=t.charCodeAt(i-1);if(0!==e.get(s))return!0;if(13===s||10===s)return!0;if(r>0){const n=t.charCodeAt(i);if(0!==e.get(n))return!0}return!1}(e,t,0,i,r)&&function(e,t,n,i,r){if(i+r===n)return!0;const s=t.charCodeAt(i+r);if(0!==e.get(s))return!0;if(13===s||10===s)return!0;if(r>0){const n=t.charCodeAt(i+r-1);if(0!==e.get(n))return!0}return!1}(e,t,n,i,r)}Ai.CtrlCmd=2048,Ai.Shift=1024,Ai.Alt=512,Ai.WinCtrl=256,function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"}(Ni||(Ni={})),function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=3]="Right"}(Ei||(Ei={})),function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"}(Si||(Si={})),function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"}(Ri||(Ri={}));class Mi{constructor(e,t){this._wordSeparators=e,this._searchRegex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(e){this._searchRegex.lastIndex=e,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(e){const t=e.length;let n;do{if(this._prevMatchStartIndex+this._prevMatchLength===t)return null;if(n=this._searchRegex.exec(e),!n)return null;const i=n.index,r=n[0].length;if(i===this._prevMatchStartIndex&&r===this._prevMatchLength){if(0===r){Q(e,t,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=i,this._prevMatchLength=r,!this._wordSeparators||xi(this._wordSeparators,e,t,i,r))return n}while(n);return null}}function ki(e,t="Unreachable"){throw new Error(t)}function Oi(e){e()||(e(),t(new o("Assertion Failed")))}function Ti(e,t){let n=0;for(;n<e.length-1;){if(!t(e[n],e[n+1]))return!1;n++}return!0}class Pi{static computeUnicodeHighlights(e,t,n){const i=n?n.startLineNumber:1,r=n?n.endLineNumber:e.getLineCount(),s=new Ii(t),o=s.getCandidateCodePoints();let a;var l,u;a="allNonBasicAscii"===o?new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):new RegExp(""+(l=Array.from(o),`[${u=l.map((e=>String.fromCodePoint(e))).join(""),u.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}]`),"g");const h=new Mi(null,a),c=[];let d,g=!1,m=0,f=0,p=0;e:for(let b=i,_=r;b<=_;b++){const t=e.getLineContent(b),n=t.length;h.reset(0);do{if(d=h.next(t),d){let e=d.index,i=d.index+d[0].length;if(e>0){G(t.charCodeAt(e-1))&&e--}if(i+1<n){G(t.charCodeAt(i-1))&&i++}const r=t.substring(e,i);let o=ft(e+1,dt,t,0);o&&o.endColumn<=e+1&&(o=null);const a=s.shouldHighlightNonBasicASCII(r,o?o.word:null);if(0!==a){3===a?m++:2===a?f++:1===a?p++:ki();const t=1e3;if(c.length>=t){g=!0;break e}c.push(new nt(b,e+1,b,i+1))}}}while(d)}return{ranges:c,hasMore:g,ambiguousCharacterCount:m,invisibleCharacterCount:f,nonBasicAsciiCharacterCount:p}}static computeUnicodeHighlightReason(e,t){const n=new Ii(t);switch(n.shouldHighlightNonBasicASCII(e,null)){case 0:return null;case 2:return{kind:1};case 3:{const i=e.codePointAt(0),r=n.ambiguousCharacters.getPrimaryConfusable(i),s=X.getLocales().filter((e=>!X.getInstance(new Set([...t.allowedLocales,e])).isAmbiguous(i)));return{kind:0,confusableWith:String.fromCodePoint(r),notAmbiguousInLocales:s}}case 1:return{kind:2}}}}class Ii{constructor(e){this.options=e,this.allowedCodePoints=new Set(e.allowedCodePoints),this.ambiguousCharacters=X.getInstance(new Set(e.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";const e=new Set;if(this.options.invisibleCharacters)for(const t of J.codePoints)Di(String.fromCodePoint(t))||e.add(t);if(this.options.ambiguousCharacters)for(const t of this.ambiguousCharacters.getConfusableCodePoints())e.add(t);for(const t of this.allowedCodePoints)e.delete(t);return e}shouldHighlightNonBasicASCII(e,t){const n=e.codePointAt(0);if(this.allowedCodePoints.has(n))return 0;if(this.options.nonBasicASCII)return 1;let i=!1,r=!1;if(t)for(const o of t){const e=o.codePointAt(0),t=(s=o,Y.test(s));i=i||t,t||this.ambiguousCharacters.isAmbiguous(e)||J.isInvisibleCharacter(e)||(r=!0)}var s;return!i&&r?0:this.options.invisibleCharacters&&!Di(e)&&J.isInvisibleCharacter(n)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(n)?3:0}}function Di(e){return" "===e||"\n"===e||"\t"===e}class Fi{constructor(e,t,n){this.changes=e,this.moves=t,this.hitTimeout=n}}class qi{constructor(e,t){this.lineRangeMapping=e,this.changes=t}}class Ki{static addRange(e,t){let n=0;for(;n<t.length&&t[n].endExclusive<e.start;)n++;let i=n;for(;i<t.length&&t[i].start<=e.endExclusive;)i++;if(n===i)t.splice(n,0,e);else{const r=Math.min(e.start,t[n].start),s=Math.max(e.endExclusive,t[i-1].endExclusive);t.splice(n,i-n,new Ki(r,s))}}static ofLength(e){return new Ki(0,e)}static ofStartAndLength(e,t){return new Ki(e,e+t)}constructor(e,t){if(this.start=e,this.endExclusive=t,e>t)throw new o(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(e){return new Ki(this.start+e,this.endExclusive+e)}deltaStart(e){return new Ki(this.start+e,this.endExclusive)}deltaEnd(e){return new Ki(this.start,this.endExclusive+e)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}contains(e){return this.start<=e&&e<this.endExclusive}join(e){return new Ki(Math.min(this.start,e.start),Math.max(this.endExclusive,e.endExclusive))}intersect(e){const t=Math.max(this.start,e.start),n=Math.min(this.endExclusive,e.endExclusive);if(t<=n)return new Ki(t,n)}intersects(e){return Math.max(this.start,e.start)<Math.min(this.endExclusive,e.endExclusive)}isBefore(e){return this.endExclusive<=e.start}isAfter(e){return this.start>=e.endExclusive}slice(e){return e.slice(this.start,this.endExclusive)}clip(e){if(this.isEmpty)throw new o(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,e))}clipCyclic(e){if(this.isEmpty)throw new o(`Invalid clipping range: ${this.toString()}`);return e<this.start?this.endExclusive-(this.start-e)%this.length:e>=this.endExclusive?this.start+(e-this.start)%this.length:e}forEach(e){for(let t=this.start;t<this.endExclusive;t++)e(t)}}function Vi(e,t){const n=Bi(e,t);return-1===n?void 0:e[n]}function Bi(e,t,n=0,i=e.length){let r=n,s=i;for(;r<s;){const n=Math.floor((r+s)/2);t(e[n])?r=n+1:s=n}return r-1}function Ui(e,t,n=0,i=e.length){let r=n,s=i;for(;r<s;){const n=Math.floor((r+s)/2);t(e[n])?s=n:r=n+1}return r}class Wi{constructor(e){this._array=e,this._findLastMonotonousLastIdx=0}findLastMonotonous(e){if(Wi.assertInvariants){if(this._prevFindLastPredicate)for(const t of this._array)if(this._prevFindLastPredicate(t)&&!e(t))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.");this._prevFindLastPredicate=e}const t=Bi(this._array,e,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=t+1,-1===t?void 0:this._array[t]}}Wi.assertInvariants=!1;class Hi{static fromRangeInclusive(e){return new Hi(e.startLineNumber,e.endLineNumber+1)}static joinMany(e){if(0===e.length)return[];let t=new zi(e[0].slice());for(let n=1;n<e.length;n++)t=t.getUnion(new zi(e[n].slice()));return t.ranges}static ofLength(e,t){return new Hi(e,e+t)}static deserialize(e){return new Hi(e[0],e[1])}constructor(e,t){if(e>t)throw new o(`startLineNumber ${e} cannot be after endLineNumberExclusive ${t}`);this.startLineNumber=e,this.endLineNumberExclusive=t}contains(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(e){return new Hi(this.startLineNumber+e,this.endLineNumberExclusive+e)}deltaLength(e){return new Hi(this.startLineNumber,this.endLineNumberExclusive+e)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(e){return new Hi(Math.min(this.startLineNumber,e.startLineNumber),Math.max(this.endLineNumberExclusive,e.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(e){const t=Math.max(this.startLineNumber,e.startLineNumber),n=Math.min(this.endLineNumberExclusive,e.endLineNumberExclusive);if(t<=n)return new Hi(t,n)}intersectsStrict(e){return this.startLineNumber<e.endLineNumberExclusive&&e.startLineNumber<this.endLineNumberExclusive}overlapOrTouch(e){return this.startLineNumber<=e.endLineNumberExclusive&&e.startLineNumber<=this.endLineNumberExclusive}equals(e){return this.startLineNumber===e.startLineNumber&&this.endLineNumberExclusive===e.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new nt(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)}toExclusiveRange(){return new nt(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(e){const t=[];for(let n=this.startLineNumber;n<this.endLineNumberExclusive;n++)t.push(e(n));return t}forEach(e){for(let t=this.startLineNumber;t<this.endLineNumberExclusive;t++)e(t)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}includes(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}toOffsetRange(){return new Ki(this.startLineNumber-1,this.endLineNumberExclusive-1)}}class zi{constructor(e=[]){this._normalizedRanges=e}get ranges(){return this._normalizedRanges}addRange(e){if(0===e.length)return;const t=Ui(this._normalizedRanges,(t=>t.endLineNumberExclusive>=e.startLineNumber)),n=Bi(this._normalizedRanges,(t=>t.startLineNumber<=e.endLineNumberExclusive))+1;if(t===n)this._normalizedRanges.splice(t,0,e);else if(t===n-1){const n=this._normalizedRanges[t];this._normalizedRanges[t]=n.join(e)}else{const i=this._normalizedRanges[t].join(this._normalizedRanges[n-1]).join(e);this._normalizedRanges.splice(t,n-t,i)}}contains(e){const t=Vi(this._normalizedRanges,(t=>t.startLineNumber<=e));return!!t&&t.endLineNumberExclusive>e}intersects(e){const t=Vi(this._normalizedRanges,(t=>t.startLineNumber<e.endLineNumberExclusive));return!!t&&t.endLineNumberExclusive>e.startLineNumber}getUnion(e){if(0===this._normalizedRanges.length)return e;if(0===e._normalizedRanges.length)return this;const t=[];let n=0,i=0,r=null;for(;n<this._normalizedRanges.length||i<e._normalizedRanges.length;){let s=null;if(n<this._normalizedRanges.length&&i<e._normalizedRanges.length){const t=this._normalizedRanges[n],r=e._normalizedRanges[i];t.startLineNumber<r.startLineNumber?(s=t,n++):(s=r,i++)}else n<this._normalizedRanges.length?(s=this._normalizedRanges[n],n++):(s=e._normalizedRanges[i],i++);null===r?r=s:r.endLineNumberExclusive>=s.startLineNumber?r=new Hi(r.startLineNumber,Math.max(r.endLineNumberExclusive,s.endLineNumberExclusive)):(t.push(r),r=s)}return null!==r&&t.push(r),new zi(t)}subtractFrom(e){const t=Ui(this._normalizedRanges,(t=>t.endLineNumberExclusive>=e.startLineNumber)),n=Bi(this._normalizedRanges,(t=>t.startLineNumber<=e.endLineNumberExclusive))+1;if(t===n)return new zi([e]);const i=[];let r=e.startLineNumber;for(let s=t;s<n;s++){const e=this._normalizedRanges[s];e.startLineNumber>r&&i.push(new Hi(r,e.startLineNumber)),r=e.endLineNumberExclusive}return r<e.endLineNumberExclusive&&i.push(new Hi(r,e.endLineNumberExclusive)),new zi(i)}toString(){return this._normalizedRanges.map((e=>e.toString())).join(", ")}getIntersection(e){const t=[];let n=0,i=0;for(;n<this._normalizedRanges.length&&i<e._normalizedRanges.length;){const r=this._normalizedRanges[n],s=e._normalizedRanges[i],o=r.intersect(s);o&&!o.isEmpty&&t.push(o),r.endLineNumberExclusive<s.endLineNumberExclusive?n++:i++}return new zi(t)}getWithDelta(e){return new zi(this._normalizedRanges.map((t=>t.delta(e))))}}class $i{static inverse(e,t,n){const i=[];let r=1,s=1;for(const a of e){const e=new $i(new Hi(r,a.original.startLineNumber),new Hi(s,a.modified.startLineNumber));e.modified.isEmpty||i.push(e),r=a.original.endLineNumberExclusive,s=a.modified.endLineNumberExclusive}const o=new $i(new Hi(r,t+1),new Hi(s,n+1));return o.modified.isEmpty||i.push(o),i}static clip(e,t,n){const i=[];for(const r of e){const e=r.original.intersect(t),s=r.modified.intersect(n);e&&!e.isEmpty&&s&&!s.isEmpty&&i.push(new $i(e,s))}return i}constructor(e,t){this.original=e,this.modified=t}toString(){return`{${this.original.toString()}->${this.modified.toString()}}`}flip(){return new $i(this.modified,this.original)}join(e){return new $i(this.original.join(e.original),this.modified.join(e.modified))}}class ji extends $i{constructor(e,t,n){super(e,t),this.innerChanges=n}flip(){var e;return new ji(this.modified,this.original,null===(e=this.innerChanges)||void 0===e?void 0:e.map((e=>e.flip())))}}class Gi{constructor(e,t){this.originalRange=e,this.modifiedRange=t}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}flip(){return new Gi(this.modifiedRange,this.originalRange)}}class Qi{computeDiff(e,t,n){var i;const r=new tr(e,t,{maxComputationTime:n.maxComputationTimeMs,shouldIgnoreTrimWhitespace:n.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),s=[];let o=null;for(const a of r.changes){let e,t;e=0===a.originalEndLineNumber?new Hi(a.originalStartLineNumber+1,a.originalStartLineNumber+1):new Hi(a.originalStartLineNumber,a.originalEndLineNumber+1),t=0===a.modifiedEndLineNumber?new Hi(a.modifiedStartLineNumber+1,a.modifiedStartLineNumber+1):new Hi(a.modifiedStartLineNumber,a.modifiedEndLineNumber+1);let n=new ji(e,t,null===(i=a.charChanges)||void 0===i?void 0:i.map((e=>new Gi(new nt(e.originalStartLineNumber,e.originalStartColumn,e.originalEndLineNumber,e.originalEndColumn),new nt(e.modifiedStartLineNumber,e.modifiedStartColumn,e.modifiedEndLineNumber,e.modifiedEndColumn)))));o&&(o.modified.endLineNumberExclusive!==n.modified.startLineNumber&&o.original.endLineNumberExclusive!==n.original.startLineNumber||(n=new ji(o.original.join(n.original),o.modified.join(n.modified),o.innerChanges&&n.innerChanges?o.innerChanges.concat(n.innerChanges):void 0),s.pop())),s.push(n),o=n}return Oi((()=>Ti(s,((e,t)=>t.original.startLineNumber-e.original.endLineNumberExclusive==t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber)))),new Fi(s,[],r.quitEarly)}}function Yi(e,t,n,i){return new pe(e,t,n).ComputeDiff(i)}let Xi=class{constructor(e){const t=[],n=[];for(let i=0,r=e.length;i<r;i++)t[i]=nr(e[i],1),n[i]=ir(e[i],1);this.lines=e,this._startColumns=t,this._endColumns=n}getElements(){const e=[];for(let t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this._startColumns[t]-1,this._endColumns[t]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,t,n){const i=[],r=[],s=[];let o=0;for(let a=t;a<=n;a++){const t=this.lines[a],l=e?this._startColumns[a]:1,u=e?this._endColumns[a]:t.length+1;for(let e=l;e<u;e++)i[o]=t.charCodeAt(e-1),r[o]=a+1,s[o]=e,o++;!e&&a<n&&(i[o]=10,r[o]=a+1,s[o]=t.length+1,o++)}return new Ji(i,r,s)}};class Ji{constructor(e,t,n){this._charCodes=e,this._lineNumbers=t,this._columns=n}toString(){return"["+this._charCodes.map(((e,t)=>(10===e?"\\n":String.fromCharCode(e))+`-(${this._lineNumbers[t]},${this._columns[t]})`)).join(", ")+"]"}_assertIndex(e,t){if(e<0||e>=t.length)throw new Error("Illegal index")}getElements(){return this._charCodes}getStartLineNumber(e){return e>0&&e===this._lineNumbers.length?this.getEndLineNumber(e-1):(this._assertIndex(e,this._lineNumbers),this._lineNumbers[e])}getEndLineNumber(e){return-1===e?this.getStartLineNumber(e+1):(this._assertIndex(e,this._lineNumbers),10===this._charCodes[e]?this._lineNumbers[e]+1:this._lineNumbers[e])}getStartColumn(e){return e>0&&e===this._columns.length?this.getEndColumn(e-1):(this._assertIndex(e,this._columns),this._columns[e])}getEndColumn(e){return-1===e?this.getStartColumn(e+1):(this._assertIndex(e,this._columns),10===this._charCodes[e]?1:this._columns[e]+1)}}class Zi{constructor(e,t,n,i,r,s,o,a){this.originalStartLineNumber=e,this.originalStartColumn=t,this.originalEndLineNumber=n,this.originalEndColumn=i,this.modifiedStartLineNumber=r,this.modifiedStartColumn=s,this.modifiedEndLineNumber=o,this.modifiedEndColumn=a}static createFromDiffChange(e,t,n){const i=t.getStartLineNumber(e.originalStart),r=t.getStartColumn(e.originalStart),s=t.getEndLineNumber(e.originalStart+e.originalLength-1),o=t.getEndColumn(e.originalStart+e.originalLength-1),a=n.getStartLineNumber(e.modifiedStart),l=n.getStartColumn(e.modifiedStart),u=n.getEndLineNumber(e.modifiedStart+e.modifiedLength-1),h=n.getEndColumn(e.modifiedStart+e.modifiedLength-1);return new Zi(i,r,s,o,a,l,u,h)}}class er{constructor(e,t,n,i,r){this.originalStartLineNumber=e,this.originalEndLineNumber=t,this.modifiedStartLineNumber=n,this.modifiedEndLineNumber=i,this.charChanges=r}static createFromDiffResult(e,t,n,i,r,s,o){let a,l,u,h,c;if(0===t.originalLength?(a=n.getStartLineNumber(t.originalStart)-1,l=0):(a=n.getStartLineNumber(t.originalStart),l=n.getEndLineNumber(t.originalStart+t.originalLength-1)),0===t.modifiedLength?(u=i.getStartLineNumber(t.modifiedStart)-1,h=0):(u=i.getStartLineNumber(t.modifiedStart),h=i.getEndLineNumber(t.modifiedStart+t.modifiedLength-1)),s&&t.originalLength>0&&t.originalLength<20&&t.modifiedLength>0&&t.modifiedLength<20&&r()){const s=n.createCharSequence(e,t.originalStart,t.originalStart+t.originalLength-1),a=i.createCharSequence(e,t.modifiedStart,t.modifiedStart+t.modifiedLength-1);if(s.getElements().length>0&&a.getElements().length>0){let e=Yi(s,a,r,!0).changes;o&&(e=function(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let i=1,r=e.length;i<r;i++){const r=e[i],s=r.originalStart-(n.originalStart+n.originalLength),o=r.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(s,o)<3?(n.originalLength=r.originalStart+r.originalLength-n.originalStart,n.modifiedLength=r.modifiedStart+r.modifiedLength-n.modifiedStart):(t.push(r),n=r)}return t}(e)),c=[];for(let t=0,n=e.length;t<n;t++)c.push(Zi.createFromDiffChange(e[t],s,a))}}return new er(a,l,u,h,c)}}class tr{constructor(e,t,n){this.shouldComputeCharChanges=n.shouldComputeCharChanges,this.shouldPostProcessCharChanges=n.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=n.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=n.shouldMakePrettyDiff,this.originalLines=e,this.modifiedLines=t,this.original=new Xi(e),this.modified=new Xi(t),this.continueLineDiff=rr(n.maxComputationTime),this.continueCharDiff=rr(0===n.maxComputationTime?0:Math.min(n.maxComputationTime,5e3))}computeDiff(){if(1===this.original.lines.length&&0===this.original.lines[0].length)return 1===this.modified.lines.length&&0===this.modified.lines[0].length?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:void 0}]};if(1===this.modified.lines.length&&0===this.modified.lines[0].length)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};const e=Yi(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),t=e.changes,n=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){const e=[];for(let n=0,i=t.length;n<i;n++)e.push(er.createFromDiffResult(this.shouldIgnoreTrimWhitespace,t[n],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:n,changes:e}}const i=[];let r=0,s=0;for(let o=-1,a=t.length;o<a;o++){const e=o+1<a?t[o+1]:null,n=e?e.originalStart:this.originalLines.length,l=e?e.modifiedStart:this.modifiedLines.length;for(;r<n&&s<l;){const e=this.originalLines[r],t=this.modifiedLines[s];if(e!==t){{let n=nr(e,1),o=nr(t,1);for(;n>1&&o>1;){if(e.charCodeAt(n-2)!==t.charCodeAt(o-2))break;n--,o--}(n>1||o>1)&&this._pushTrimWhitespaceCharChange(i,r+1,1,n,s+1,1,o)}{let n=ir(e,1),o=ir(t,1);const a=e.length+1,l=t.length+1;for(;n<a&&o<l;){if(e.charCodeAt(n-1)!==e.charCodeAt(o-1))break;n++,o++}(n<a||o<l)&&this._pushTrimWhitespaceCharChange(i,r+1,n,a,s+1,o,l)}}r++,s++}e&&(i.push(er.createFromDiffResult(this.shouldIgnoreTrimWhitespace,e,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),r+=e.originalLength,s+=e.modifiedLength)}return{quitEarly:n,changes:i}}_pushTrimWhitespaceCharChange(e,t,n,i,r,s,o){if(this._mergeTrimWhitespaceCharChange(e,t,n,i,r,s,o))return;let a;this.shouldComputeCharChanges&&(a=[new Zi(t,n,t,i,r,s,r,o)]),e.push(new er(t,t,r,r,a))}_mergeTrimWhitespaceCharChange(e,t,n,i,r,s,o){const a=e.length;if(0===a)return!1;const l=e[a-1];return 0!==l.originalEndLineNumber&&0!==l.modifiedEndLineNumber&&(l.originalEndLineNumber===t&&l.modifiedEndLineNumber===r?(this.shouldComputeCharChanges&&l.charChanges&&l.charChanges.push(new Zi(t,n,t,i,r,s,r,o)),!0):l.originalEndLineNumber+1===t&&l.modifiedEndLineNumber+1===r&&(l.originalEndLineNumber=t,l.modifiedEndLineNumber=r,this.shouldComputeCharChanges&&l.charChanges&&l.charChanges.push(new Zi(t,n,t,i,r,s,r,o)),!0))}}function nr(e,t){const n=function(e){for(let t=0,n=e.length;t<n;t++){const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}return-1}(e);return-1===n?t:n+1}function ir(e,t){const n=function(e,t=e.length-1){for(let n=t;n>=0;n--){const t=e.charCodeAt(n);if(32!==t&&9!==t)return n}return-1}(e);return-1===n?t:n+2}function rr(e){if(0===e)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}class sr{static trivial(e,t){return new sr([new or(Ki.ofLength(e.length),Ki.ofLength(t.length))],!1)}static trivialTimedOut(e,t){return new sr([new or(Ki.ofLength(e.length),Ki.ofLength(t.length))],!0)}constructor(e,t){this.diffs=e,this.hitTimeout=t}}class or{static invert(e,t){const n=[];return function(e,t){for(let n=0;n<=e.length;n++)t(0===n?void 0:e[n-1],n===e.length?void 0:e[n])}(e,((e,i)=>{n.push(or.fromOffsetPairs(e?e.getEndExclusives():ar.zero,i?i.getStarts():new ar(t,(e?e.seq2Range.endExclusive-e.seq1Range.endExclusive:0)+t)))})),n}static fromOffsetPairs(e,t){return new or(new Ki(e.offset1,t.offset1),new Ki(e.offset2,t.offset2))}constructor(e,t){this.seq1Range=e,this.seq2Range=t}swap(){return new or(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(e){return new or(this.seq1Range.join(e.seq1Range),this.seq2Range.join(e.seq2Range))}delta(e){return 0===e?this:new or(this.seq1Range.delta(e),this.seq2Range.delta(e))}deltaStart(e){return 0===e?this:new or(this.seq1Range.deltaStart(e),this.seq2Range.deltaStart(e))}deltaEnd(e){return 0===e?this:new or(this.seq1Range.deltaEnd(e),this.seq2Range.deltaEnd(e))}intersect(e){const t=this.seq1Range.intersect(e.seq1Range),n=this.seq2Range.intersect(e.seq2Range);if(t&&n)return new or(t,n)}getStarts(){return new ar(this.seq1Range.start,this.seq2Range.start)}getEndExclusives(){return new ar(this.seq1Range.endExclusive,this.seq2Range.endExclusive)}}class ar{constructor(e,t){this.offset1=e,this.offset2=t}toString(){return`${this.offset1} <-> ${this.offset2}`}delta(e){return 0===e?this:new ar(this.offset1+e,this.offset2+e)}equals(e){return this.offset1===e.offset1&&this.offset2===e.offset2}}ar.zero=new ar(0,0),ar.max=new ar(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER);class lr{isValid(){return!0}}lr.instance=new lr;class ur{constructor(e){if(this.timeout=e,this.startTime=Date.now(),this.valid=!0,e<=0)throw new o("timeout must be positive")}isValid(){return!(Date.now()-this.startTime<this.timeout)&&this.valid&&(this.valid=!1),this.valid}}class hr{constructor(e,t){this.width=e,this.height=t,this.array=[],this.array=new Array(e*t)}get(e,t){return this.array[e+t*this.width]}set(e,t,n){this.array[e+t*this.width]=n}}function cr(e){return 32===e||9===e}class dr{static getKey(e){let t=this.chrKeys.get(e);return void 0===t&&(t=this.chrKeys.size,this.chrKeys.set(e,t)),t}constructor(e,t,n){this.range=e,this.lines=t,this.source=n,this.histogram=[];let i=0;for(let r=e.startLineNumber-1;r<e.endLineNumberExclusive-1;r++){const e=t[r];for(let t=0;t<e.length;t++){i++;const n=e[t],r=dr.getKey(n);this.histogram[r]=(this.histogram[r]||0)+1}i++;const n=dr.getKey("\n");this.histogram[n]=(this.histogram[n]||0)+1}this.totalCount=i}computeSimilarity(e){var t,n;let i=0;const r=Math.max(this.histogram.length,e.histogram.length);for(let s=0;s<r;s++)i+=Math.abs((null!==(t=this.histogram[s])&&void 0!==t?t:0)-(null!==(n=e.histogram[s])&&void 0!==n?n:0));return 1-i/(this.totalCount+e.totalCount)}}dr.chrKeys=new Map;class gr{compute(e,t,n=lr.instance,i){if(0===e.length||0===t.length)return sr.trivial(e,t);const r=new hr(e.length,t.length),s=new hr(e.length,t.length),o=new hr(e.length,t.length);for(let g=0;g<e.length;g++)for(let a=0;a<t.length;a++){if(!n.isValid())return sr.trivialTimedOut(e,t);const l=0===g?0:r.get(g-1,a),u=0===a?0:r.get(g,a-1);let h;e.getElement(g)===t.getElement(a)?(h=0===g||0===a?0:r.get(g-1,a-1),g>0&&a>0&&3===s.get(g-1,a-1)&&(h+=o.get(g-1,a-1)),h+=i?i(g,a):1):h=-1;const c=Math.max(l,u,h);if(c===h){const e=g>0&&a>0?o.get(g-1,a-1):0;o.set(g,a,e+1),s.set(g,a,3)}else c===l?(o.set(g,a,0),s.set(g,a,1)):c===u&&(o.set(g,a,0),s.set(g,a,2));r.set(g,a,c)}const a=[];let l=e.length,u=t.length;function h(e,t){e+1===l&&t+1===u||a.push(new or(new Ki(e+1,l),new Ki(t+1,u))),l=e,u=t}let c=e.length-1,d=t.length-1;for(;c>=0&&d>=0;)3===s.get(c,d)?(h(c,d),c--,d--):1===s.get(c,d)?c--:d--;return h(-1,-1),a.reverse(),new sr(a,!1)}}class mr{compute(e,t,n=lr.instance){if(0===e.length||0===t.length)return sr.trivial(e,t);const i=e,r=t;function s(e,t){for(;e<i.length&&t<r.length&&i.getElement(e)===r.getElement(t);)e++,t++;return e}let o=0;const a=new pr;a.set(0,s(0,0));const l=new br;l.set(0,0===a.get(0)?null:new fr(null,0,0,a.get(0)));let u=0;e:for(;;){if(o++,!n.isValid())return sr.trivialTimedOut(i,r);const e=-Math.min(o,r.length+o%2),t=Math.min(o,i.length+o%2);for(u=e;u<=t;u+=2){const n=u===t?-1:a.get(u+1),o=u===e?-1:a.get(u-1)+1,h=Math.min(Math.max(n,o),i.length),c=h-u;if(h>i.length||c>r.length)continue;const d=s(h,c);a.set(u,d);const g=h===n?l.get(u+1):l.get(u-1);if(l.set(u,d!==h?new fr(g,h,c,d-h):g),a.get(u)===i.length&&a.get(u)-u===r.length)break e}}let h=l.get(u);const c=[];let d=i.length,g=r.length;for(;;){const e=h?h.x+h.length:0,t=h?h.y+h.length:0;if(e===d&&t===g||c.push(new or(new Ki(e,d),new Ki(t,g))),!h)break;d=h.x,g=h.y,h=h.prev}return c.reverse(),new sr(c,!1)}}class fr{constructor(e,t,n,i){this.prev=e,this.x=t,this.y=n,this.length=i}}class pr{constructor(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){if(e<0){if((e=-e-1)>=this.negativeArr.length){const e=this.negativeArr;this.negativeArr=new Int32Array(2*e.length),this.negativeArr.set(e)}this.negativeArr[e]=t}else{if(e>=this.positiveArr.length){const e=this.positiveArr;this.positiveArr=new Int32Array(2*e.length),this.positiveArr.set(e)}this.positiveArr[e]=t}}}class br{constructor(){this.positiveArr=[],this.negativeArr=[]}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){e<0?(e=-e-1,this.negativeArr[e]=t):this.positiveArr[e]=t}}class _r{constructor(){this.map=new Map}add(e,t){let n=this.map.get(e);n||(n=new Set,this.map.set(e,n)),n.add(t)}delete(e,t){const n=this.map.get(e);n&&(n.delete(t),0===n.size&&this.map.delete(e))}forEach(e,t){const n=this.map.get(e);n&&n.forEach(t)}get(e){const t=this.map.get(e);return t||new Set}}class Cr{constructor(e,t,n){this.lines=e,this.considerWhitespaceChanges=n,this.elements=[],this.firstCharOffsetByLine=[],this.additionalOffsetByLine=[];let i=!1;t.start>0&&t.endExclusive>=e.length&&(t=new Ki(t.start-1,t.endExclusive),i=!0),this.lineRange=t,this.firstCharOffsetByLine[0]=0;for(let r=this.lineRange.start;r<this.lineRange.endExclusive;r++){let t=e[r],s=0;if(i)s=t.length,t="",i=!1;else if(!n){const e=t.trimStart();s=t.length-e.length,t=e.trimEnd()}this.additionalOffsetByLine.push(s);for(let e=0;e<t.length;e++)this.elements.push(t.charCodeAt(e));r<e.length-1&&(this.elements.push("\n".charCodeAt(0)),this.firstCharOffsetByLine[r-this.lineRange.start+1]=this.elements.length)}this.additionalOffsetByLine.push(0)}toString(){return`Slice: "${this.text}"`}get text(){return this.getText(new Ki(0,this.length))}getText(e){return this.elements.slice(e.start,e.endExclusive).map((e=>String.fromCharCode(e))).join("")}getElement(e){return this.elements[e]}get length(){return this.elements.length}getBoundaryScore(e){const t=wr(e>0?this.elements[e-1]:-1),n=wr(e<this.elements.length?this.elements[e]:-1);if(7===t&&8===n)return 0;if(8===t)return 150;let i=0;return t!==n&&(i+=10,0===t&&1===n&&(i+=1)),i+=Lr(t),i+=Lr(n),i}translateOffset(e){if(this.lineRange.isEmpty)return new tt(this.lineRange.start+1,1);const t=Bi(this.firstCharOffsetByLine,(t=>t<=e));return new tt(this.lineRange.start+t+1,e-this.firstCharOffsetByLine[t]+this.additionalOffsetByLine[t]+1)}translateRange(e){return nt.fromPositions(this.translateOffset(e.start),this.translateOffset(e.endExclusive))}findWordContaining(e){if(e<0||e>=this.elements.length)return;if(!yr(this.elements[e]))return;let t=e;for(;t>0&&yr(this.elements[t-1]);)t--;let n=e;for(;n<this.elements.length&&yr(this.elements[n]);)n++;return new Ki(t,n)}countLinesIn(e){return this.translateOffset(e.endExclusive).lineNumber-this.translateOffset(e.start).lineNumber}isStronglyEqual(e,t){return this.elements[e]===this.elements[t]}extendToFullLines(e){var t,n;const i=null!==(t=Vi(this.firstCharOffsetByLine,(t=>t<=e.start)))&&void 0!==t?t:0,r=null!==(n=function(e,t){const n=Ui(e,t);return n===e.length?void 0:e[n]}(this.firstCharOffsetByLine,(t=>e.endExclusive<=t)))&&void 0!==n?n:this.elements.length;return new Ki(i,r)}}function yr(e){return e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57}const vr={0:0,1:0,2:0,3:10,4:2,5:30,6:3,7:10,8:10};function Lr(e){return vr[e]}function wr(e){return 10===e?8:13===e?7:cr(e)?6:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:-1===e?3:44===e||59===e?5:4}function Nr(e,t,n,i,r,s){let{moves:o,excludedChanges:a}=function(e,t,n,i){const r=[],s=e.filter((e=>e.modified.isEmpty&&e.original.length>=3)).map((e=>new dr(e.original,t,e))),o=new Set(e.filter((e=>e.original.isEmpty&&e.modified.length>=3)).map((e=>new dr(e.modified,n,e)))),a=new Set;for(const l of s){let e,t=-1;for(const n of o){const i=l.computeSimilarity(n);i>t&&(t=i,e=n)}if(t>.9&&e&&(o.delete(e),r.push(new $i(l.range,e.range)),a.add(l.source),a.add(e.source)),!i.isValid())return{moves:r,excludedChanges:a}}return{moves:r,excludedChanges:a}}(e,t,n,s);if(!s.isValid())return[];const l=function(e,t,n,i,r,s){const o=[],a=new _r;for(const g of e)for(let e=g.original.startLineNumber;e<g.original.endLineNumberExclusive-2;e++){const n=`${t[e-1]}:${t[e+1-1]}:${t[e+2-1]}`;a.add(n,{range:new Hi(e,e+3)})}const l=[];e.sort(st((e=>e.modified.startLineNumber),ot));for(const g of e){let e=[];for(let t=g.modified.startLineNumber;t<g.modified.endLineNumberExclusive-2;t++){const i=`${n[t-1]}:${n[t+1-1]}:${n[t+2-1]}`,r=new Hi(t,t+3),s=[];a.forEach(i,(({range:t})=>{for(const i of e)if(i.originalLineRange.endLineNumberExclusive+1===t.endLineNumberExclusive&&i.modifiedLineRange.endLineNumberExclusive+1===r.endLineNumberExclusive)return i.originalLineRange=new Hi(i.originalLineRange.startLineNumber,t.endLineNumberExclusive),i.modifiedLineRange=new Hi(i.modifiedLineRange.startLineNumber,r.endLineNumberExclusive),void s.push(i);const n={modifiedLineRange:r,originalLineRange:t};l.push(n),s.push(n)})),e=s}if(!s.isValid())return[]}l.sort((u=st((e=>e.modifiedLineRange.length),ot),(e,t)=>-u(e,t)));var u;const h=new zi,c=new zi;for(const g of l){const e=g.modifiedLineRange.startLineNumber-g.originalLineRange.startLineNumber,t=h.subtractFrom(g.modifiedLineRange),n=c.subtractFrom(g.originalLineRange).getWithDelta(e),i=t.getIntersection(n);for(const r of i.ranges){if(r.length<3)continue;const t=r,n=r.delta(-e);o.push(new $i(n,t)),h.addRange(t),c.addRange(n)}}o.sort(st((e=>e.original.startLineNumber),ot));const d=new Wi(e);for(let g=0;g<o.length;g++){const t=o[g],n=d.findLastMonotonous((e=>e.original.startLineNumber<=t.original.startLineNumber)),a=Vi(e,(e=>e.modified.startLineNumber<=t.modified.startLineNumber)),l=Math.max(t.original.startLineNumber-n.original.startLineNumber,t.modified.startLineNumber-a.modified.startLineNumber),u=d.findLastMonotonous((e=>e.original.startLineNumber<t.original.endLineNumberExclusive)),m=Vi(e,(e=>e.modified.startLineNumber<t.modified.endLineNumberExclusive)),f=Math.max(u.original.endLineNumberExclusive-t.original.endLineNumberExclusive,m.modified.endLineNumberExclusive-t.modified.endLineNumberExclusive);let p,b;for(p=0;p<l;p++){const e=t.original.startLineNumber-p-1,n=t.modified.startLineNumber-p-1;if(e>i.length||n>r.length)break;if(h.contains(n)||c.contains(e))break;if(!Er(i[e-1],r[n-1],s))break}for(p>0&&(c.addRange(new Hi(t.original.startLineNumber-p,t.original.startLineNumber)),h.addRange(new Hi(t.modified.startLineNumber-p,t.modified.startLineNumber))),b=0;b<f;b++){const e=t.original.endLineNumberExclusive+b,n=t.modified.endLineNumberExclusive+b;if(e>i.length||n>r.length)break;if(h.contains(n)||c.contains(e))break;if(!Er(i[e-1],r[n-1],s))break}b>0&&(c.addRange(new Hi(t.original.endLineNumberExclusive,t.original.endLineNumberExclusive+b)),h.addRange(new Hi(t.modified.endLineNumberExclusive,t.modified.endLineNumberExclusive+b))),(p>0||b>0)&&(o[g]=new $i(new Hi(t.original.startLineNumber-p,t.original.endLineNumberExclusive+b),new Hi(t.modified.startLineNumber-p,t.modified.endLineNumberExclusive+b)))}return o}(e.filter((e=>!a.has(e))),i,r,t,n,s);return function(e,t){for(const n of t)e.push(n)}(o,l),o=function(e){if(0===e.length)return e;e.sort(st((e=>e.original.startLineNumber),ot));const t=[e[0]];for(let n=1;n<e.length;n++){const i=t[t.length-1],r=e[n],s=r.original.startLineNumber-i.original.endLineNumberExclusive,o=r.modified.startLineNumber-i.modified.endLineNumberExclusive;s>=0&&o>=0&&s+o<=2?t[t.length-1]=i.join(r):t.push(r)}return t}(o),o=o.filter((e=>{const n=e.original.toOffsetRange().slice(t).map((e=>e.trim()));return n.join("\n").length>=15&&function(e,t){let n=0;for(const i of e)t(i)&&n++;return n}(n,(e=>e.length>=2))>=2})),o=function(e,t){const n=new Wi(e);return t=t.filter((t=>(n.findLastMonotonous((e=>e.original.startLineNumber<t.original.endLineNumberExclusive))||new $i(new Hi(1,1),new Hi(1,1)))!==Vi(e,(e=>e.modified.startLineNumber<t.modified.endLineNumberExclusive)))),t}(e,o),o}function Er(e,t,n){if(e.trim()===t.trim())return!0;if(e.length>300&&t.length>300)return!1;const i=(new mr).compute(new Cr([e],new Ki(0,1),!1),new Cr([t],new Ki(0,1),!1),n);let r=0;const s=or.invert(i.diffs,e.length);for(const a of s)a.seq1Range.forEach((t=>{cr(e.charCodeAt(t))||r++}));const o=function(t){let n=0;for(let i=0;i<e.length;i++)cr(t.charCodeAt(i))||n++;return n}(e.length>t.length?e:t);return r/o>.6&&o>10}function Sr(e,t,n){let i=n;return i=Rr(e,t,i),i=Rr(e,t,i),i=function(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(let i=0;i<n.length;i++){const r=i>0?n[i-1]:void 0,s=n[i],o=i+1<n.length?n[i+1]:void 0,a=new Ki(r?r.seq1Range.endExclusive+1:0,o?o.seq1Range.start-1:e.length),l=new Ki(r?r.seq2Range.endExclusive+1:0,o?o.seq2Range.start-1:t.length);s.seq1Range.isEmpty?n[i]=Ar(s,e,t,a,l):s.seq2Range.isEmpty&&(n[i]=Ar(s.swap(),t,e,l,a).swap())}return n}(e,t,i),i}function Rr(e,t,n){if(0===n.length)return n;const i=[];i.push(n[0]);for(let s=1;s<n.length;s++){const r=i[i.length-1];let o=n[s];if(o.seq1Range.isEmpty||o.seq2Range.isEmpty){const n=o.seq1Range.start-r.seq1Range.endExclusive;let s;for(s=1;s<=n&&(e.getElement(o.seq1Range.start-s)===e.getElement(o.seq1Range.endExclusive-s)&&t.getElement(o.seq2Range.start-s)===t.getElement(o.seq2Range.endExclusive-s));s++);if(s--,s===n){i[i.length-1]=new or(new Ki(r.seq1Range.start,o.seq1Range.endExclusive-n),new Ki(r.seq2Range.start,o.seq2Range.endExclusive-n));continue}o=o.delta(-s)}i.push(o)}const r=[];for(let s=0;s<i.length-1;s++){const n=i[s+1];let o=i[s];if(o.seq1Range.isEmpty||o.seq2Range.isEmpty){const r=n.seq1Range.start-o.seq1Range.endExclusive;let a;for(a=0;a<r&&(e.isStronglyEqual(o.seq1Range.start+a,o.seq1Range.endExclusive+a)&&t.isStronglyEqual(o.seq2Range.start+a,o.seq2Range.endExclusive+a));a++);if(a===r){i[s+1]=new or(new Ki(o.seq1Range.start+r,n.seq1Range.endExclusive),new Ki(o.seq2Range.start+r,n.seq2Range.endExclusive));continue}a>0&&(o=o.delta(a))}r.push(o)}return i.length>0&&r.push(i[i.length-1]),r}function Ar(e,t,n,i,r){let s=1;for(;e.seq1Range.start-s>=i.start&&e.seq2Range.start-s>=r.start&&n.isStronglyEqual(e.seq2Range.start-s,e.seq2Range.endExclusive-s)&&s<100;)s++;s--;let o=0;for(;e.seq1Range.start+o<i.endExclusive&&e.seq2Range.endExclusive+o<r.endExclusive&&n.isStronglyEqual(e.seq2Range.start+o,e.seq2Range.endExclusive+o)&&o<100;)o++;if(0===s&&0===o)return e;let a=0,l=-1;for(let u=-s;u<=o;u++){const i=e.seq2Range.start+u,r=e.seq2Range.endExclusive+u,s=e.seq1Range.start+u,o=t.getBoundaryScore(s)+n.getBoundaryScore(i)+n.getBoundaryScore(r);o>l&&(l=o,a=u)}return e.delta(a)}class xr{constructor(e,t){this.trimmedHash=e,this.lines=t}getElement(e){return this.trimmedHash[e]}get length(){return this.trimmedHash.length}getBoundaryScore(e){return 1e3-((0===e?0:Mr(this.lines[e-1]))+(e===this.lines.length?0:Mr(this.lines[e])))}getText(e){return this.lines.slice(e.start,e.endExclusive).join("\n")}isStronglyEqual(e,t){return this.lines[e]===this.lines[t]}}function Mr(e){let t=0;for(;t<e.length&&(32===e.charCodeAt(t)||9===e.charCodeAt(t));)t++;return t}class kr{constructor(){this.dynamicProgrammingDiffing=new gr,this.myersDiffingAlgorithm=new mr}computeDiff(e,t,n){if(e.length<=1&&function(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;if(e.length!==t.length)return!1;for(let i=0,r=e.length;i<r;i++)if(!n(e[i],t[i]))return!1;return!0}(e,t,((e,t)=>e===t)))return new Fi([],[],!1);if(1===e.length&&0===e[0].length||1===t.length&&0===t[0].length)return new Fi([new ji(new Hi(1,e.length+1),new Hi(1,t.length+1),[new Gi(new nt(1,1,e.length,e[0].length+1),new nt(1,1,t.length,t[0].length+1))])],[],!1);const i=0===n.maxComputationTimeMs?lr.instance:new ur(n.maxComputationTimeMs),r=!n.ignoreTrimWhitespace,s=new Map;function o(e){let t=s.get(e);return void 0===t&&(t=s.size,s.set(e,t)),t}const a=e.map((e=>o(e.trim()))),l=t.map((e=>o(e.trim()))),u=new xr(a,e),h=new xr(l,t),c=(()=>u.length+h.length<1700?this.dynamicProgrammingDiffing.compute(u,h,i,((n,i)=>e[n]===t[i]?0===t[i].length?.1:1+Math.log(1+t[i].length):.99)):this.myersDiffingAlgorithm.compute(u,h))();let d=c.diffs,g=c.hitTimeout;d=Sr(u,h,d),d=function(e,t,n){let i=n;if(0===i.length)return i;let r,s=0;do{r=!1;const t=[i[0]];for(let n=1;n<i.length;n++){let s=function(t,n){const i=new Ki(a.seq1Range.endExclusive,o.seq1Range.start);return e.getText(i).replace(/\s/g,"").length<=4&&(t.seq1Range.length+t.seq2Range.length>5||n.seq1Range.length+n.seq2Range.length>5)};const o=i[n],a=t[t.length-1];s(a,o)?(r=!0,t[t.length-1]=t[t.length-1].join(o)):t.push(o)}i=t}while(s++<10&&r);return i}(u,0,d);const m=[],f=n=>{if(r)for(let s=0;s<n;s++){const n=p+s,o=b+s;if(e[n]!==t[o]){const s=this.refineDiff(e,t,new or(new Ki(n,n+1),new Ki(o,o+1)),i,r);for(const e of s.mappings)m.push(e);s.hitTimeout&&(g=!0)}}};let p=0,b=0;for(const y of d){Oi((()=>y.seq1Range.start-p==y.seq2Range.start-b));f(y.seq1Range.start-p),p=y.seq1Range.endExclusive,b=y.seq2Range.endExclusive;const n=this.refineDiff(e,t,y,i,r);n.hitTimeout&&(g=!0);for(const e of n.mappings)m.push(e)}f(e.length-p);const _=Or(m,e,t);let C=[];return n.computeMoves&&(C=this.computeMoves(_,e,t,a,l,i,r)),Oi((()=>{function n(e,t){if(e.lineNumber<1||e.lineNumber>t.length)return!1;const n=t[e.lineNumber-1];return!(e.column<1||e.column>n.length+1)}function i(e,t){return!(e.startLineNumber<1||e.startLineNumber>t.length+1)&&!(e.endLineNumberExclusive<1||e.endLineNumberExclusive>t.length+1)}for(const r of _){if(!r.innerChanges)return!1;for(const i of r.innerChanges){if(!(n(i.modifiedRange.getStartPosition(),t)&&n(i.modifiedRange.getEndPosition(),t)&&n(i.originalRange.getStartPosition(),e)&&n(i.originalRange.getEndPosition(),e)))return!1}if(!i(r.modified,t)||!i(r.original,e))return!1}return!0})),new Fi(_,C,g)}computeMoves(e,t,n,i,r,s,o){return Nr(e,t,n,i,r,s).map((e=>{const i=Or(this.refineDiff(t,n,new or(e.original.toOffsetRange(),e.modified.toOffsetRange()),s,o).mappings,t,n,!0);return new qi(e,i)}))}refineDiff(e,t,n,i,r){const s=new Cr(e,n.seq1Range,r),o=new Cr(t,n.seq2Range,r),a=s.length+o.length<500?this.dynamicProgrammingDiffing.compute(s,o,i):this.myersDiffingAlgorithm.compute(s,o,i);let l=a.diffs;l=Sr(s,o,l),l=function(e,t,n){const i=or.invert(n,e.length),r=[];let s=new ar(0,0);function o(n,o){if(n.offset1<s.offset1||n.offset2<s.offset2)return;const a=e.findWordContaining(n.offset1),l=t.findWordContaining(n.offset2);if(!a||!l)return;let u=new or(a,l);const h=u.intersect(o);let c=h.seq1Range.length,d=h.seq2Range.length;for(;i.length>0;){const n=i[0];if(!n.seq1Range.intersects(a)&&!n.seq2Range.intersects(l))break;const r=e.findWordContaining(n.seq1Range.start),s=t.findWordContaining(n.seq2Range.start),o=new or(r,s),h=o.intersect(n);if(c+=h.seq1Range.length,d+=h.seq2Range.length,u=u.join(o),!(u.seq1Range.endExclusive>=n.seq1Range.endExclusive))break;i.shift()}c+d<2*(u.seq1Range.length+u.seq2Range.length)/3&&r.push(u),s=u.getEndExclusives()}for(;i.length>0;){const e=i.shift();e.seq1Range.isEmpty||(o(e.getStarts(),e),o(e.getEndExclusives().delta(-1),e))}return function(e,t){const n=[];for(;e.length>0||t.length>0;){const i=e[0],r=t[0];let s;s=i&&(!r||i.seq1Range.start<r.seq1Range.start)?e.shift():t.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=s.seq1Range.start?n[n.length-1]=n[n.length-1].join(s):n.push(s)}return n}(n,r)}(s,o,l),l=function(e,t,n){const i=[];for(const r of n){const e=i[i.length-1];e&&(r.seq1Range.start-e.seq1Range.endExclusive<=2||r.seq2Range.start-e.seq2Range.endExclusive<=2)?i[i.length-1]=new or(e.seq1Range.join(r.seq1Range),e.seq2Range.join(r.seq2Range)):i.push(r)}return i}(0,0,l),l=function(e,t,n){let i=n;if(0===i.length)return i;let r,s=0;do{r=!1;const n=[i[0]];for(let s=1;s<i.length;s++){let o=function(n,i){const r=new Ki(l.seq1Range.endExclusive,a.seq1Range.start);if(e.countLinesIn(r)>5||r.length>500)return!1;const s=e.getText(r).trim();if(s.length>20||s.split(/\r\n|\r|\n/).length>1)return!1;const o=e.countLinesIn(n.seq1Range),u=n.seq1Range.length,h=t.countLinesIn(n.seq2Range),c=n.seq2Range.length,d=e.countLinesIn(i.seq1Range),g=i.seq1Range.length,m=t.countLinesIn(i.seq2Range),f=i.seq2Range.length;function p(e){return Math.min(e,130)}return Math.pow(Math.pow(p(40*o+u),1.5)+Math.pow(p(40*h+c),1.5),1.5)+Math.pow(Math.pow(p(40*d+g),1.5)+Math.pow(p(40*m+f),1.5),1.5)>74184.96480721243};const a=i[s],l=n[n.length-1];o(l,a)?(r=!0,n[n.length-1]=n[n.length-1].join(a)):n.push(a)}i=n}while(s++<10&&r);const o=[];return function(e,t){for(let n=0;n<e.length;n++)t(0===n?void 0:e[n-1],e[n],n+1===e.length?void 0:e[n+1])}(i,((t,n,i)=>{let r=n;function s(e){return e.length>0&&e.trim().length<=3&&n.seq1Range.length+n.seq2Range.length>100}const a=e.extendToFullLines(n.seq1Range),l=e.getText(new Ki(a.start,n.seq1Range.start));s(l)&&(r=r.deltaStart(-l.length));const u=e.getText(new Ki(n.seq1Range.endExclusive,a.endExclusive));s(u)&&(r=r.deltaEnd(u.length));const h=or.fromOffsetPairs(t?t.getEndExclusives():ar.zero,i?i.getStarts():ar.max),c=r.intersect(h);o.length>0&&c.getStarts().equals(o[o.length-1].getEndExclusives())?o[o.length-1]=o[o.length-1].join(c):o.push(c)})),o}(s,o,l);return{mappings:l.map((e=>new Gi(s.translateRange(e.seq1Range),o.translateRange(e.seq2Range)))),hitTimeout:a.hitTimeout}}}function Or(e,t,n,i=!1){const r=[];for(const s of function*(e,t){let n,i;for(const r of e)void 0!==i&&t(i,r)?n.push(r):(n&&(yield n),n=[r]),i=r;n&&(yield n)}(e.map((e=>function(e,t,n){let i=0,r=0;1===e.modifiedRange.endColumn&&1===e.originalRange.endColumn&&e.originalRange.startLineNumber+i<=e.originalRange.endLineNumber&&e.modifiedRange.startLineNumber+i<=e.modifiedRange.endLineNumber&&(r=-1);e.modifiedRange.startColumn-1>=n[e.modifiedRange.startLineNumber-1].length&&e.originalRange.startColumn-1>=t[e.originalRange.startLineNumber-1].length&&e.originalRange.startLineNumber<=e.originalRange.endLineNumber+r&&e.modifiedRange.startLineNumber<=e.modifiedRange.endLineNumber+r&&(i=1);const s=new Hi(e.originalRange.startLineNumber+i,e.originalRange.endLineNumber+1+r),o=new Hi(e.modifiedRange.startLineNumber+i,e.modifiedRange.endLineNumber+1+r);return new ji(s,o,[e])}(e,t,n))),((e,t)=>e.original.overlapOrTouch(t.original)||e.modified.overlapOrTouch(t.modified)))){const e=s[0],t=s[s.length-1];r.push(new ji(e.original.join(t.original),e.modified.join(t.modified),s.map((e=>e.innerChanges[0]))))}return Oi((()=>!(!i&&r.length>0&&r[0].original.startLineNumber!==r[0].modified.startLineNumber)&&Ti(r,((e,t)=>t.original.startLineNumber-e.original.endLineNumberExclusive==t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber)))),r}const Tr=()=>new Qi,Pr=()=>new kr;function Ir(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}class Dr{constructor(e,t,n,i=1){this._rgbaBrand=void 0,this.r=0|Math.min(255,Math.max(0,e)),this.g=0|Math.min(255,Math.max(0,t)),this.b=0|Math.min(255,Math.max(0,n)),this.a=Ir(Math.max(Math.min(1,i),0),3)}static equals(e,t){return e.r===t.r&&e.g===t.g&&e.b===t.b&&e.a===t.a}}class Fr{constructor(e,t,n,i){this._hslaBrand=void 0,this.h=0|Math.max(Math.min(360,e),0),this.s=Ir(Math.max(Math.min(1,t),0),3),this.l=Ir(Math.max(Math.min(1,n),0),3),this.a=Ir(Math.max(Math.min(1,i),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.l===t.l&&e.a===t.a}static fromRGBA(e){const t=e.r/255,n=e.g/255,i=e.b/255,r=e.a,s=Math.max(t,n,i),o=Math.min(t,n,i);let a=0,l=0;const u=(o+s)/2,h=s-o;if(h>0){switch(l=Math.min(u<=.5?h/(2*u):h/(2-2*u),1),s){case t:a=(n-i)/h+(n<i?6:0);break;case n:a=(i-t)/h+2;break;case i:a=(t-n)/h+4}a*=60,a=Math.round(a)}return new Fr(a,l,u,r)}static _hue2rgb(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}static toRGBA(e){const t=e.h/360,{s:n,l:i,a:r}=e;let s,o,a;if(0===n)s=o=a=i;else{const e=i<.5?i*(1+n):i+n-i*n,r=2*i-e;s=Fr._hue2rgb(r,e,t+1/3),o=Fr._hue2rgb(r,e,t),a=Fr._hue2rgb(r,e,t-1/3)}return new Dr(Math.round(255*s),Math.round(255*o),Math.round(255*a),r)}}class qr{constructor(e,t,n,i){this._hsvaBrand=void 0,this.h=0|Math.max(Math.min(360,e),0),this.s=Ir(Math.max(Math.min(1,t),0),3),this.v=Ir(Math.max(Math.min(1,n),0),3),this.a=Ir(Math.max(Math.min(1,i),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.v===t.v&&e.a===t.a}static fromRGBA(e){const t=e.r/255,n=e.g/255,i=e.b/255,r=Math.max(t,n,i),s=r-Math.min(t,n,i),o=0===r?0:s/r;let a;return a=0===s?0:r===t?((n-i)/s%6+6)%6:r===n?(i-t)/s+2:(t-n)/s+4,new qr(Math.round(60*a),o,r,e.a)}static toRGBA(e){const{h:t,s:n,v:i,a:r}=e,s=i*n,o=s*(1-Math.abs(t/60%2-1)),a=i-s;let[l,u,h]=[0,0,0];return t<60?(l=s,u=o):t<120?(l=o,u=s):t<180?(u=s,h=o):t<240?(u=o,h=s):t<300?(l=o,h=s):t<=360&&(l=s,h=o),l=Math.round(255*(l+a)),u=Math.round(255*(u+a)),h=Math.round(255*(h+a)),new Dr(l,u,h,r)}}class Kr{static fromHex(e){return Kr.Format.CSS.parseHex(e)||Kr.red}static equals(e,t){return!e&&!t||!(!e||!t)&&e.equals(t)}get hsla(){return this._hsla?this._hsla:Fr.fromRGBA(this.rgba)}get hsva(){return this._hsva?this._hsva:qr.fromRGBA(this.rgba)}constructor(e){if(!e)throw new Error("Color needs a value");if(e instanceof Dr)this.rgba=e;else if(e instanceof Fr)this._hsla=e,this.rgba=Fr.toRGBA(e);else{if(!(e instanceof qr))throw new Error("Invalid color ctor argument");this._hsva=e,this.rgba=qr.toRGBA(e)}}equals(e){return!!e&&Dr.equals(this.rgba,e.rgba)&&Fr.equals(this.hsla,e.hsla)&&qr.equals(this.hsva,e.hsva)}getRelativeLuminance(){return Ir(.2126*Kr._relativeLuminanceForComponent(this.rgba.r)+.7152*Kr._relativeLuminanceForComponent(this.rgba.g)+.0722*Kr._relativeLuminanceForComponent(this.rgba.b),4)}static _relativeLuminanceForComponent(e){const t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}isLighter(){return(299*this.rgba.r+587*this.rgba.g+114*this.rgba.b)/1e3>=128}isLighterThan(e){return this.getRelativeLuminance()>e.getRelativeLuminance()}isDarkerThan(e){return this.getRelativeLuminance()<e.getRelativeLuminance()}lighten(e){return new Kr(new Fr(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*e,this.hsla.a))}darken(e){return new Kr(new Fr(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*e,this.hsla.a))}transparent(e){const{r:t,g:n,b:i,a:r}=this.rgba;return new Kr(new Dr(t,n,i,r*e))}isTransparent(){return 0===this.rgba.a}isOpaque(){return 1===this.rgba.a}opposite(){return new Kr(new Dr(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}makeOpaque(e){if(this.isOpaque()||1!==e.rgba.a)return this;const{r:t,g:n,b:i,a:r}=this.rgba;return new Kr(new Dr(e.rgba.r-r*(e.rgba.r-t),e.rgba.g-r*(e.rgba.g-n),e.rgba.b-r*(e.rgba.b-i),1))}toString(){return this._toString||(this._toString=Kr.Format.CSS.format(this)),this._toString}static getLighterColor(e,t,n){if(e.isLighterThan(t))return e;n=n||.5;const i=e.getRelativeLuminance(),r=t.getRelativeLuminance();return n=n*(r-i)/r,e.lighten(n)}static getDarkerColor(e,t,n){if(e.isDarkerThan(t))return e;n=n||.5;const i=e.getRelativeLuminance();return n=n*(i-t.getRelativeLuminance())/i,e.darken(n)}}var Vr,Br;function Ur(e){const t=[];for(const n of e){const e=Number(n);(e||0===e&&""!==n.replace(/\s/g,""))&&t.push(e)}return t}function Wr(e,t,n,i){return{red:e/255,blue:n/255,green:t/255,alpha:i}}function Hr(e,t){const n=t.index,i=t[0].length;if(!n)return;const r=e.positionAt(n);return{startLineNumber:r.lineNumber,startColumn:r.column,endLineNumber:r.lineNumber,endColumn:r.column+i}}function zr(e,t){if(!e)return;const n=Kr.Format.CSS.parseHex(t);return n?{range:e,color:Wr(n.rgba.r,n.rgba.g,n.rgba.b,n.rgba.a)}:void 0}function $r(e,t,n){if(!e||1!==t.length)return;const i=Ur(t[0].values());return{range:e,color:Wr(i[0],i[1],i[2],n?i[3]:1)}}function jr(e,t,n){if(!e||1!==t.length)return;const i=Ur(t[0].values()),r=new Kr(new Fr(i[0],i[1]/100,i[2]/100,n?i[3]:1));return{range:e,color:Wr(r.rgba.r,r.rgba.g,r.rgba.b,r.rgba.a)}}function Gr(e,t){return"string"==typeof e?[...e.matchAll(t)]:e.findMatches(t)}function Qr(e){return e&&"function"==typeof e.getValue&&"function"==typeof e.positionAt?function(e){const t=[],n=Gr(e,/\b(rgb|rgba|hsl|hsla)(\([0-9\s,.\%]*\))|(#)([A-Fa-f0-9]{3})\b|(#)([A-Fa-f0-9]{4})\b|(#)([A-Fa-f0-9]{6})\b|(#)([A-Fa-f0-9]{8})\b/gm);if(n.length>0)for(const i of n){const n=i.filter((e=>void 0!==e)),r=n[1],s=n[2];if(!s)continue;let o;if("rgb"===r){const t=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*\)$/gm;o=$r(Hr(e,i),Gr(s,t),!1)}else if("rgba"===r){const t=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;o=$r(Hr(e,i),Gr(s,t),!0)}else if("hsl"===r){const t=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*\)$/gm;o=jr(Hr(e,i),Gr(s,t),!1)}else if("hsla"===r){const t=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;o=jr(Hr(e,i),Gr(s,t),!0)}else"#"===r&&(o=zr(Hr(e,i),r+s));o&&t.push(o)}return t}(e):[]}Kr.white=new Kr(new Dr(255,255,255,1)),Kr.black=new Kr(new Dr(0,0,0,1)),Kr.red=new Kr(new Dr(255,0,0,1)),Kr.blue=new Kr(new Dr(0,0,255,1)),Kr.green=new Kr(new Dr(0,255,0,1)),Kr.cyan=new Kr(new Dr(0,255,255,1)),Kr.lightgrey=new Kr(new Dr(211,211,211,1)),Kr.transparent=new Kr(new Dr(0,0,0,0)),Vr=Kr||(Kr={}),function(e){function t(e){const t=e.toString(16);return 2!==t.length?"0"+t:t}function n(e){switch(e){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:case 65:return 10;case 98:case 66:return 11;case 99:case 67:return 12;case 100:case 68:return 13;case 101:case 69:return 14;case 102:case 70:return 15}return 0}e.formatRGB=function(e){return 1===e.rgba.a?`rgb(${e.rgba.r}, ${e.rgba.g}, ${e.rgba.b})`:Vr.Format.CSS.formatRGBA(e)},e.formatRGBA=function(e){return`rgba(${e.rgba.r}, ${e.rgba.g}, ${e.rgba.b}, ${+e.rgba.a.toFixed(2)})`},e.formatHSL=function(e){return 1===e.hsla.a?`hsl(${e.hsla.h}, ${(100*e.hsla.s).toFixed(2)}%, ${(100*e.hsla.l).toFixed(2)}%)`:Vr.Format.CSS.formatHSLA(e)},e.formatHSLA=function(e){return`hsla(${e.hsla.h}, ${(100*e.hsla.s).toFixed(2)}%, ${(100*e.hsla.l).toFixed(2)}%, ${e.hsla.a.toFixed(2)})`},e.formatHex=function(e){return`#${t(e.rgba.r)}${t(e.rgba.g)}${t(e.rgba.b)}`},e.formatHexA=function(e,n=!1){return n&&1===e.rgba.a?Vr.Format.CSS.formatHex(e):`#${t(e.rgba.r)}${t(e.rgba.g)}${t(e.rgba.b)}${t(Math.round(255*e.rgba.a))}`},e.format=function(e){return e.isOpaque()?Vr.Format.CSS.formatHex(e):Vr.Format.CSS.formatRGBA(e)},e.parseHex=function(e){const t=e.length;if(0===t)return null;if(35!==e.charCodeAt(0))return null;if(7===t){const t=16*n(e.charCodeAt(1))+n(e.charCodeAt(2)),i=16*n(e.charCodeAt(3))+n(e.charCodeAt(4)),r=16*n(e.charCodeAt(5))+n(e.charCodeAt(6));return new Vr(new Dr(t,i,r,1))}if(9===t){const t=16*n(e.charCodeAt(1))+n(e.charCodeAt(2)),i=16*n(e.charCodeAt(3))+n(e.charCodeAt(4)),r=16*n(e.charCodeAt(5))+n(e.charCodeAt(6)),s=16*n(e.charCodeAt(7))+n(e.charCodeAt(8));return new Vr(new Dr(t,i,r,s/255))}if(4===t){const t=n(e.charCodeAt(1)),i=n(e.charCodeAt(2)),r=n(e.charCodeAt(3));return new Vr(new Dr(16*t+t,16*i+i,16*r+r))}if(5===t){const t=n(e.charCodeAt(1)),i=n(e.charCodeAt(2)),r=n(e.charCodeAt(3)),s=n(e.charCodeAt(4));return new Vr(new Dr(16*t+t,16*i+i,16*r+r,(16*s+s)/255))}return null}}((Br=Vr.Format||(Vr.Format={})).CSS||(Br.CSS={}));class Yr extends ct{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}findMatches(e){const t=[];for(let n=0;n<this._lines.length;n++){const i=this._lines[n],r=this.offsetAt(new tt(n+1,1)),s=i.matchAll(e);for(const e of s)(e.index||0===e.index)&&(e.index=e.index+r),t.push(e)}return t}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(e){return this._lines[e-1]}getWordAtPosition(e,t){const n=ft(e.column,gt(t),this._lines[e.lineNumber-1],0);return n?new nt(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}words(e){const t=this._lines,n=this._wordenize.bind(this);let i=0,r="",s=0,o=[];return{*[Symbol.iterator](){for(;;)if(s<o.length){const e=r.substring(o[s].start,o[s].end);s+=1,yield e}else{if(!(i<t.length))break;r=t[i],o=n(r,e),s=0,i+=1}}}}getLineWords(e,t){const n=this._lines[e-1],i=this._wordenize(n,t),r=[];for(const s of i)r.push({word:n.substring(s.start,s.end),startColumn:s.start+1,endColumn:s.end+1});return r}_wordenize(e,t){const n=[];let i;for(t.lastIndex=0;(i=t.exec(e))&&0!==i[0].length;)n.push({start:i.index,end:i.index+i[0].length});return n}getValueInRange(e){if((e=this._validateRange(e)).startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);const t=this._eol,n=e.startLineNumber-1,i=e.endLineNumber-1,r=[];r.push(this._lines[n].substring(e.startColumn-1));for(let s=n+1;s<i;s++)r.push(this._lines[s]);return r.push(this._lines[i].substring(0,e.endColumn-1)),r.join(t)}offsetAt(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();const t=this._lineStarts.getIndexOf(e),n=this._lines[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}_validateRange(e){const t=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),n=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}_validatePosition(e){if(!tt.isIPosition(e))throw new Error("bad position");let{lineNumber:t,column:n}=e,i=!1;if(t<1)t=1,n=1,i=!0;else if(t>this._lines.length)t=this._lines.length,n=this._lines[t-1].length+1,i=!0;else{const e=this._lines[t-1].length+1;n<1?(n=1,i=!0):n>e&&(n=e,i=!0)}return i?{lineNumber:t,column:n}:e}}class Xr{constructor(e,t){this._host=e,this._models=Object.create(null),this._foreignModuleFactory=t,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(e){return this._models[e]}_getModels(){const e=[];return Object.keys(this._models).forEach((t=>e.push(this._models[t]))),e}acceptNewModel(e){this._models[e.url]=new Yr(He.parse(e.url),e.lines,e.EOL,e.versionId)}acceptModelChanged(e,t){if(!this._models[e])return;this._models[e].onEvents(t)}acceptRemovedModel(e){this._models[e]&&delete this._models[e]}async computeUnicodeHighlights(e,t,n){const i=this._getModel(e);return i?Pi.computeUnicodeHighlights(i,t,n):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}}async computeDiff(e,t,n,i){const r=this._getModel(e),s=this._getModel(t);if(!r||!s)return null;return Xr.computeDiff(r,s,n,i)}static computeDiff(e,t,n,i){const r="advanced"===i?Pr():Tr(),s=e.getLinesContent(),o=t.getLinesContent(),a=r.computeDiff(s,o,n);function l(e){return e.map((e=>{var t;return[e.original.startLineNumber,e.original.endLineNumberExclusive,e.modified.startLineNumber,e.modified.endLineNumberExclusive,null===(t=e.innerChanges)||void 0===t?void 0:t.map((e=>[e.originalRange.startLineNumber,e.originalRange.startColumn,e.originalRange.endLineNumber,e.originalRange.endColumn,e.modifiedRange.startLineNumber,e.modifiedRange.startColumn,e.modifiedRange.endLineNumber,e.modifiedRange.endColumn]))]}))}return{identical:!(a.changes.length>0)&&this._modelsAreIdentical(e,t),quitEarly:a.hitTimeout,changes:l(a.changes),moves:a.moves.map((e=>[e.lineRangeMapping.original.startLineNumber,e.lineRangeMapping.original.endLineNumberExclusive,e.lineRangeMapping.modified.startLineNumber,e.lineRangeMapping.modified.endLineNumberExclusive,l(e.changes)]))}}static _modelsAreIdentical(e,t){const n=e.getLineCount();if(n!==t.getLineCount())return!1;for(let i=1;i<=n;i++){if(e.getLineContent(i)!==t.getLineContent(i))return!1}return!0}async computeMoreMinimalEdits(e,t,n){const i=this._getModel(e);if(!i)return t;const r=[];let s;t=t.slice(0).sort(((e,t)=>{if(e.range&&t.range)return nt.compareRangesUsingStarts(e.range,t.range);return(e.range?0:1)-(t.range?0:1)}));let o=0;for(let a=1;a<t.length;a++)nt.getEndPosition(t[o].range).equals(nt.getStartPosition(t[a].range))?(t[o].range=nt.fromPositions(nt.getStartPosition(t[o].range),nt.getEndPosition(t[a].range)),t[o].text+=t[a].text):(o++,t[o]=t[a]);t.length=o+1;for(let{range:a,text:l,eol:u}of t){if("number"==typeof u&&(s=u),nt.isEmpty(a)&&!l)continue;const e=i.getValueInRange(a);if(l=l.replace(/\r\n|\n|\r/g,i.eol),e===l)continue;if(Math.max(l.length,e.length)>Xr._diffLimit){r.push({range:a,text:l});continue}const t=de(e,l,n),o=i.offsetAt(nt.lift(a).getStartPosition());for(const n of t){const e=i.positionAt(o+n.originalStart),t=i.positionAt(o+n.originalStart+n.originalLength),s={text:l.substr(n.modifiedStart,n.modifiedLength),range:{startLineNumber:e.lineNumber,startColumn:e.column,endLineNumber:t.lineNumber,endColumn:t.column}};i.getValueInRange(s.range)!==s.text&&r.push(s)}}return"number"==typeof s&&r.push({eol:s,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),r}async computeLinks(e){const t=this._getModel(e);return t?function(e){return e&&"function"==typeof e.getLineCount&&"function"==typeof e.getLineContent?Lt.computeLinks(e):[]}(t):null}async computeDefaultDocumentColors(e){const t=this._getModel(e);return t?Qr(t):null}async textualSuggest(e,t,n,i){const r=new p,s=new RegExp(n,i),o=new Set;e:for(const a of e){const e=this._getModel(a);if(e)for(const n of e.words(s))if(n!==t&&isNaN(Number(n))&&(o.add(n),o.size>Xr._suggestionsLimit))break e}return{words:Array.from(o),duration:r.elapsed()}}async computeWordRanges(e,t,n,i){const r=this._getModel(e);if(!r)return Object.create(null);const s=new RegExp(n,i),o=Object.create(null);for(let a=t.startLineNumber;a<t.endLineNumber;a++){const e=r.getLineWords(a,s);for(const t of e){if(!isNaN(Number(t.word)))continue;let e=o[t.word];e||(e=[],o[t.word]=e),e.push({startLineNumber:a,startColumn:t.startColumn,endLineNumber:a,endColumn:t.endColumn})}}return o}async navigateValueSet(e,t,n,i,r){const s=this._getModel(e);if(!s)return null;const o=new RegExp(i,r);t.startColumn===t.endColumn&&(t={startLineNumber:t.startLineNumber,startColumn:t.startColumn,endLineNumber:t.endLineNumber,endColumn:t.endColumn+1});const a=s.getValueInRange(t),l=s.getWordAtPosition({lineNumber:t.startLineNumber,column:t.startColumn},o);if(!l)return null;const u=s.getValueInRange(l);return wt.INSTANCE.navigateValueSet(t,a,l,u,n)}loadForeignModule(e,t,n){const i=function(e,t){const n=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)},i={};for(const r of e)i[r]=n(r);return i}(n,((e,t)=>this._host.fhr(e,t))),r={host:i,getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(r,t),Promise.resolve(N(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}fmr(e,t){if(!this._foreignModule||"function"!=typeof this._foreignModule[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,t))}catch(Zr){return Promise.reject(Zr)}}}Xr._diffLimit=1e5,Xr._suggestionsLimit=1e4,"function"==typeof importScripts&&(globalThis.monaco={editor:void 0,languages:void 0,CancellationTokenSource:At,Emitter:L,KeyCode:Fn,KeyMod:Ai,Position:tt,Range:nt,Selection:qt,SelectionDirection:ai,MarkerSeverity:Kn,MarkerTag:Bn,Uri:He,Token:Wt});let Jr=!1;globalThis.onmessage=e=>{Jr||function(e){if(Jr)return;Jr=!0;const t=new ae((e=>{globalThis.postMessage(e)}),(t=>new Xr(t,e)));globalThis.onmessage=e=>{t.onmessage(e.data)}}(null)}}();
