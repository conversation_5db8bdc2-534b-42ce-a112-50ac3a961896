function o(o){for(var r,e=0,t=0,i=o.length;i>=4;++t,i-=4)r=1540483477*(65535&(r=255&o.charCodeAt(t)|(255&o.charCodeAt(++t))<<8|(255&o.charCodeAt(++t))<<16|(255&o.charCodeAt(++t))<<24))+(59797*(r>>>16)<<16),e=1540483477*(65535&(r^=r>>>24))+(59797*(r>>>16)<<16)^1540483477*(65535&e)+(59797*(e>>>16)<<16);switch(i){case 3:e^=(255&o.charCodeAt(t+2))<<16;case 2:e^=(255&o.charCodeAt(t+1))<<8;case 1:e=1540483477*(65535&(e^=255&o.charCodeAt(t)))+(59797*(e>>>16)<<16)}return(((e=1540483477*(65535&(e^=e>>>13))+(59797*(e>>>16)<<16))^e>>>15)>>>0).toString(36)}var r={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};export{o as m,r as u};
