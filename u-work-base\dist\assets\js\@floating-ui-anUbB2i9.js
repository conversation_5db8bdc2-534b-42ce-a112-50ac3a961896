const t=Math.min,e=Math.max,n=Math.round,o=Math.floor,i=t=>({x:t,y:t}),r={left:"right",right:"left",bottom:"top",top:"bottom"},l={start:"end",end:"start"};function c(n,o,i){return e(n,t(o,i))}function s(t,e){return"function"==typeof t?t(e):t}function a(t){return t.split("-")[0]}function f(t){return t.split("-")[1]}function u(t){return"x"===t?"y":"x"}function d(t){return"y"===t?"height":"width"}function h(t){return["top","bottom"].includes(a(t))?"y":"x"}function m(t){return u(h(t))}function p(t){return t.replace(/start|end/g,(t=>l[t]))}function g(t){return t.replace(/left|right|bottom|top/g,(t=>r[t]))}function y(t){return"number"!=typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function w(t){const{x:e,y:n,width:o,height:i}=t;return{width:o,height:i,top:n,left:e,right:e+o,bottom:n+i,x:e,y:n}}function x(t,e,n){let{reference:o,floating:i}=t;const r=h(e),l=m(e),c=d(l),s=a(e),u="y"===r,p=o.x+o.width/2-i.width/2,g=o.y+o.height/2-i.height/2,y=o[c]/2-i[c]/2;let w;switch(s){case"top":w={x:p,y:o.y-i.height};break;case"bottom":w={x:p,y:o.y+o.height};break;case"right":w={x:o.x+o.width,y:g};break;case"left":w={x:o.x-i.width,y:g};break;default:w={x:o.x,y:o.y}}switch(f(e)){case"start":w[l]-=y*(n&&u?-1:1);break;case"end":w[l]+=y*(n&&u?-1:1)}return w}async function v(t,e){var n;void 0===e&&(e={});const{x:o,y:i,platform:r,rects:l,elements:c,strategy:a}=t,{boundary:f="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:h=!1,padding:m=0}=s(e,t),p=y(m),g=c[h?"floating"===d?"reference":"floating":d],x=w(await r.getClippingRect({element:null==(n=await(null==r.isElement?void 0:r.isElement(g)))||n?g:g.contextElement||await(null==r.getDocumentElement?void 0:r.getDocumentElement(c.floating)),boundary:f,rootBoundary:u,strategy:a})),v="floating"===d?{x:o,y:i,width:l.floating.width,height:l.floating.height}:l.reference,b=await(null==r.getOffsetParent?void 0:r.getOffsetParent(c.floating)),R=await(null==r.isElement?void 0:r.isElement(b))&&await(null==r.getScale?void 0:r.getScale(b))||{x:1,y:1},T=w(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:v,offsetParent:b,strategy:a}):v);return{top:(x.top-T.top+p.top)/R.y,bottom:(T.bottom-x.bottom+p.bottom)/R.y,left:(x.left-T.left+p.left)/R.x,right:(T.right-x.right+p.right)/R.x}}function b(){return"undefined"!=typeof window}function R(t){return E(t)?(t.nodeName||"").toLowerCase():"#document"}function T(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function L(t){var e;return null==(e=(E(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function E(t){return!!b()&&(t instanceof Node||t instanceof T(t).Node)}function A(t){return!!b()&&(t instanceof Element||t instanceof T(t).Element)}function D(t){return!!b()&&(t instanceof HTMLElement||t instanceof T(t).HTMLElement)}function O(t){return!(!b()||"undefined"==typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof T(t).ShadowRoot)}function S(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=H(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(i)}function C(t){return["table","td","th"].includes(R(t))}function P(t){return[":popover-open",":modal"].some((e=>{try{return t.matches(e)}catch(n){return!1}}))}function F(t){const e=k(),n=A(t)?H(t):t;return["transform","translate","scale","rotate","perspective"].some((t=>!!n[t]&&"none"!==n[t]))||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((t=>(n.willChange||"").includes(t)))||["paint","layout","strict","content"].some((t=>(n.contain||"").includes(t)))}function k(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function B(t){return["html","body","#document"].includes(R(t))}function H(t){return T(t).getComputedStyle(t)}function W(t){return A(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function V(t){if("html"===R(t))return t;const e=t.assignedSlot||t.parentNode||O(t)&&t.host||L(t);return O(e)?e.host:e}function M(t){const e=V(t);return B(e)?t.ownerDocument?t.ownerDocument.body:t.body:D(e)&&S(e)?e:M(e)}function z(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const i=M(t),r=i===(null==(o=t.ownerDocument)?void 0:o.body),l=T(i);if(r){const t=N(l);return e.concat(l,l.visualViewport||[],S(i)?i:[],t&&n?z(t):[])}return e.concat(i,z(i,[],n))}function N(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function I(t){const e=H(t);let o=parseFloat(e.width)||0,i=parseFloat(e.height)||0;const r=D(t),l=r?t.offsetWidth:o,c=r?t.offsetHeight:i,s=n(o)!==l||n(i)!==c;return s&&(o=l,i=c),{width:o,height:i,$:s}}function j(t){return A(t)?t:t.contextElement}function q(t){const e=j(t);if(!D(e))return i(1);const o=e.getBoundingClientRect(),{width:r,height:l,$:c}=I(e);let s=(c?n(o.width):o.width)/r,a=(c?n(o.height):o.height)/l;return s&&Number.isFinite(s)||(s=1),a&&Number.isFinite(a)||(a=1),{x:s,y:a}}const X=i(0);function Y(t){const e=T(t);return k()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:X}function $(t,e,n,o){void 0===e&&(e=!1),void 0===n&&(n=!1);const r=t.getBoundingClientRect(),l=j(t);let c=i(1);e&&(o?A(o)&&(c=q(o)):c=q(t));const s=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==T(t))&&e}(l,n,o)?Y(l):i(0);let a=(r.left+s.x)/c.x,f=(r.top+s.y)/c.y,u=r.width/c.x,d=r.height/c.y;if(l){const t=T(l),e=o&&A(o)?T(o):o;let n=t,i=N(n);for(;i&&o&&e!==n;){const t=q(i),e=i.getBoundingClientRect(),o=H(i),r=e.left+(i.clientLeft+parseFloat(o.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(o.paddingTop))*t.y;a*=t.x,f*=t.y,u*=t.x,d*=t.y,a+=r,f+=l,n=T(i),i=N(n)}}return w({width:u,height:d,x:a,y:f})}function _(t,e){const n=W(t).scrollLeft;return e?e.left+n:$(L(t)).left+n}function G(t,e,n){void 0===n&&(n=!1);const o=t.getBoundingClientRect();return{x:o.left+e.scrollLeft-(n?0:_(t,o)),y:o.top+e.scrollTop}}function J(t,n,o){let r;if("viewport"===n)r=function(t,e){const n=T(t),o=L(t),i=n.visualViewport;let r=o.clientWidth,l=o.clientHeight,c=0,s=0;if(i){r=i.width,l=i.height;const t=k();(!t||t&&"fixed"===e)&&(c=i.offsetLeft,s=i.offsetTop)}return{width:r,height:l,x:c,y:s}}(t,o);else if("document"===n)r=function(t){const n=L(t),o=W(t),i=t.ownerDocument.body,r=e(n.scrollWidth,n.clientWidth,i.scrollWidth,i.clientWidth),l=e(n.scrollHeight,n.clientHeight,i.scrollHeight,i.clientHeight);let c=-o.scrollLeft+_(t);const s=-o.scrollTop;return"rtl"===H(i).direction&&(c+=e(n.clientWidth,i.clientWidth)-r),{width:r,height:l,x:c,y:s}}(L(t));else if(A(n))r=function(t,e){const n=$(t,!0,"fixed"===e),o=n.top+t.clientTop,r=n.left+t.clientLeft,l=D(t)?q(t):i(1);return{width:t.clientWidth*l.x,height:t.clientHeight*l.y,x:r*l.x,y:o*l.y}}(n,o);else{const e=Y(t);r={x:n.x-e.x,y:n.y-e.y,width:n.width,height:n.height}}return w(r)}function K(t,e){const n=V(t);return!(n===e||!A(n)||B(n))&&("fixed"===H(n).position||K(n,e))}function Q(t,e,n){const o=D(e),r=L(e),l="fixed"===n,c=$(t,!0,l,e);let s={scrollLeft:0,scrollTop:0};const a=i(0);if(o||!o&&!l)if(("body"!==R(e)||S(r))&&(s=W(e)),o){const t=$(e,!0,l,e);a.x=t.x+e.clientLeft,a.y=t.y+e.clientTop}else r&&(a.x=_(r));const f=!r||o||l?i(0):G(r,s);return{x:c.left+s.scrollLeft-a.x-f.x,y:c.top+s.scrollTop-a.y-f.y,width:c.width,height:c.height}}function U(t){return"static"===H(t).position}function Z(t,e){if(!D(t)||"fixed"===H(t).position)return null;if(e)return e(t);let n=t.offsetParent;return L(t)===n&&(n=n.ownerDocument.body),n}function tt(t,e){const n=T(t);if(P(t))return n;if(!D(t)){let e=V(t);for(;e&&!B(e);){if(A(e)&&!U(e))return e;e=V(e)}return n}let o=Z(t,e);for(;o&&C(o)&&U(o);)o=Z(o,e);return o&&B(o)&&U(o)&&!F(o)?n:o||function(t){let e=V(t);for(;D(e)&&!B(e);){if(F(e))return e;if(P(e))return null;e=V(e)}return null}(t)||n}const et={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:o,strategy:r}=t;const l="fixed"===r,c=L(o),s=!!e&&P(e.floating);if(o===c||s&&l)return n;let a={scrollLeft:0,scrollTop:0},f=i(1);const u=i(0),d=D(o);if((d||!d&&!l)&&(("body"!==R(o)||S(c))&&(a=W(o)),D(o))){const t=$(o);f=q(o),u.x=t.x+o.clientLeft,u.y=t.y+o.clientTop}const h=!c||d||l?i(0):G(c,a,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-a.scrollLeft*f.x+u.x+h.x,y:n.y*f.y-a.scrollTop*f.y+u.y+h.y}},getDocumentElement:L,getClippingRect:function(n){let{element:o,boundary:i,rootBoundary:r,strategy:l}=n;const c=[..."clippingAncestors"===i?P(o)?[]:function(t,e){const n=e.get(t);if(n)return n;let o=z(t,[],!1).filter((t=>A(t)&&"body"!==R(t))),i=null;const r="fixed"===H(t).position;let l=r?V(t):t;for(;A(l)&&!B(l);){const e=H(l),n=F(l);n||"fixed"!==e.position||(i=null),(r?!n&&!i:!n&&"static"===e.position&&i&&["absolute","fixed"].includes(i.position)||S(l)&&!n&&K(t,l))?o=o.filter((t=>t!==l)):i=e,l=V(l)}return e.set(t,o),o}(o,this._c):[].concat(i),r],s=c[0],a=c.reduce(((n,i)=>{const r=J(o,i,l);return n.top=e(r.top,n.top),n.right=t(r.right,n.right),n.bottom=t(r.bottom,n.bottom),n.left=e(r.left,n.left),n}),J(o,s,l));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:tt,getElementRects:async function(t){const e=this.getOffsetParent||tt,n=this.getDimensions,o=await n(t.floating);return{reference:Q(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=I(t);return{width:e,height:n}},getScale:q,isElement:A,isRTL:function(t){return"rtl"===H(t).direction}};function nt(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function ot(n,i,r,l){void 0===l&&(l={});const{ancestorScroll:c=!0,ancestorResize:s=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:u=!1}=l,d=j(n),h=c||s?[...d?z(d):[],...z(i)]:[];h.forEach((t=>{c&&t.addEventListener("scroll",r,{passive:!0}),s&&t.addEventListener("resize",r)}));const m=d&&f?function(n,i){let r,l=null;const c=L(n);function s(){var t;clearTimeout(r),null==(t=l)||t.disconnect(),l=null}return function a(f,u){void 0===f&&(f=!1),void 0===u&&(u=1),s();const d=n.getBoundingClientRect(),{left:h,top:m,width:p,height:g}=d;if(f||i(),!p||!g)return;const y={rootMargin:-o(m)+"px "+-o(c.clientWidth-(h+p))+"px "+-o(c.clientHeight-(m+g))+"px "+-o(h)+"px",threshold:e(0,t(1,u))||1};let w=!0;function x(t){const e=t[0].intersectionRatio;if(e!==u){if(!w)return a();e?a(!1,e):r=setTimeout((()=>{a(!1,1e-7)}),1e3)}1!==e||nt(d,n.getBoundingClientRect())||a(),w=!1}try{l=new IntersectionObserver(x,{...y,root:c.ownerDocument})}catch(v){l=new IntersectionObserver(x,y)}l.observe(n)}(!0),s}(d,r):null;let p,g=-1,y=null;a&&(y=new ResizeObserver((t=>{let[e]=t;e&&e.target===d&&y&&(y.unobserve(i),cancelAnimationFrame(g),g=requestAnimationFrame((()=>{var t;null==(t=y)||t.observe(i)}))),r()})),d&&!u&&y.observe(d),y.observe(i));let w=u?$(n):null;return u&&function t(){const e=$(n);w&&!nt(w,e)&&r();w=e,p=requestAnimationFrame(t)}(),r(),()=>{var t;h.forEach((t=>{c&&t.removeEventListener("scroll",r),s&&t.removeEventListener("resize",r)})),null==m||m(),null==(t=y)||t.disconnect(),y=null,u&&cancelAnimationFrame(p)}}const it=v,rt=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:i,y:r,placement:l,middlewareData:c}=e,u=await async function(t,e){const{placement:n,platform:o,elements:i}=t,r=await(null==o.isRTL?void 0:o.isRTL(i.floating)),l=a(n),c=f(n),u="y"===h(n),d=["left","top"].includes(l)?-1:1,m=r&&u?-1:1,p=s(e,t);let{mainAxis:g,crossAxis:y,alignmentAxis:w}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return c&&"number"==typeof w&&(y="end"===c?-1*w:w),u?{x:y*m,y:g*d}:{x:g*d,y:y*m}}(e,t);return l===(null==(n=c.offset)?void 0:n.placement)&&null!=(o=c.arrow)&&o.alignmentOffset?{}:{x:i+u.x,y:r+u.y,data:{...u,placement:l}}}}},lt=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:i}=e,{mainAxis:r=!0,crossAxis:l=!1,limiter:f={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...d}=s(t,e),m={x:n,y:o},p=await v(e,d),g=h(a(i)),y=u(g);let w=m[y],x=m[g];if(r){const t="y"===y?"bottom":"right";w=c(w+p["y"===y?"top":"left"],w,w-p[t])}if(l){const t="y"===g?"bottom":"right";x=c(x+p["y"===g?"top":"left"],x,x-p[t])}const b=f.fn({...e,[y]:w,[g]:x});return{...b,data:{x:b.x-n,y:b.y-o,enabled:{[y]:r,[g]:l}}}}}},ct=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,o;const{placement:i,middlewareData:r,rects:l,initialPlacement:c,platform:u,elements:y}=e,{mainAxis:w=!0,crossAxis:x=!0,fallbackPlacements:b,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:L=!0,...E}=s(t,e);if(null!=(n=r.arrow)&&n.alignmentOffset)return{};const A=a(i),D=h(c),O=a(c)===c,S=await(null==u.isRTL?void 0:u.isRTL(y.floating)),C=b||(O||!L?[g(c)]:function(t){const e=g(t);return[p(t),e,p(e)]}(c)),P="none"!==T;!b&&P&&C.push(...function(t,e,n,o){const i=f(t);let r=function(t,e,n){const o=["left","right"],i=["right","left"],r=["top","bottom"],l=["bottom","top"];switch(t){case"top":case"bottom":return n?e?i:o:e?o:i;case"left":case"right":return e?r:l;default:return[]}}(a(t),"start"===n,o);return i&&(r=r.map((t=>t+"-"+i)),e&&(r=r.concat(r.map(p)))),r}(c,L,T,S));const F=[c,...C],k=await v(e,E),B=[];let H=(null==(o=r.flip)?void 0:o.overflows)||[];if(w&&B.push(k[A]),x){const t=function(t,e,n){void 0===n&&(n=!1);const o=f(t),i=m(t),r=d(i);let l="x"===i?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return e.reference[r]>e.floating[r]&&(l=g(l)),[l,g(l)]}(i,l,S);B.push(k[t[0]],k[t[1]])}if(H=[...H,{placement:i,overflows:B}],!B.every((t=>t<=0))){var W,V;const t=((null==(W=r.flip)?void 0:W.index)||0)+1,e=F[t];if(e)return{data:{index:t,overflows:H},reset:{placement:e}};let n=null==(V=H.filter((t=>t.overflows[0]<=0)).sort(((t,e)=>t.overflows[1]-e.overflows[1]))[0])?void 0:V.placement;if(!n)switch(R){case"bestFit":{var M;const t=null==(M=H.filter((t=>{if(P){const e=h(t.placement);return e===D||"y"===e}return!0})).map((t=>[t.placement,t.overflows.filter((t=>t>0)).reduce(((t,e)=>t+e),0)])).sort(((t,e)=>t[1]-e[1]))[0])?void 0:M[0];t&&(n=t);break}case"initialPlacement":n=c}if(i!==n)return{reset:{placement:n}}}return{}}}},st=e=>({name:"arrow",options:e,async fn(n){const{x:o,y:i,placement:r,rects:l,platform:a,elements:u,middlewareData:h}=n,{element:p,padding:g=0}=s(e,n)||{};if(null==p)return{};const w=y(g),x={x:o,y:i},v=m(r),b=d(v),R=await a.getDimensions(p),T="y"===v,L=T?"top":"left",E=T?"bottom":"right",A=T?"clientHeight":"clientWidth",D=l.reference[b]+l.reference[v]-x[v]-l.floating[b],O=x[v]-l.reference[v],S=await(null==a.getOffsetParent?void 0:a.getOffsetParent(p));let C=S?S[A]:0;C&&await(null==a.isElement?void 0:a.isElement(S))||(C=u.floating[A]||l.floating[b]);const P=D/2-O/2,F=C/2-R[b]/2-1,k=t(w[L],F),B=t(w[E],F),H=k,W=C-R[b]-B,V=C/2-R[b]/2+P,M=c(H,V,W),z=!h.arrow&&null!=f(r)&&V!==M&&l.reference[b]/2-(V<H?k:B)-R[b]/2<0,N=z?V<H?V-H:V-W:0;return{[v]:x[v]+N,data:{[v]:M,centerOffset:V-M-N,...z&&{alignmentOffset:N}},reset:z}}}),at=(t,e,n)=>{const o=new Map,i={platform:et,...n},r={...i.platform,_c:o};return(async(t,e,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:r=[],platform:l}=n,c=r.filter(Boolean),s=await(null==l.isRTL?void 0:l.isRTL(e));let a=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:f,y:u}=x(a,o,s),d=o,h={},m=0;for(let p=0;p<c.length;p++){const{name:n,fn:r}=c[p],{x:g,y:y,data:w,reset:v}=await r({x:f,y:u,initialPlacement:o,placement:d,strategy:i,middlewareData:h,rects:a,platform:l,elements:{reference:t,floating:e}});f=null!=g?g:f,u=null!=y?y:u,h={...h,[n]:{...h[n],...w}},v&&m<=50&&(m++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(a=!0===v.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):v.rects),({x:f,y:u}=x(a,d,s))),p=-1)}return{x:f,y:u,placement:d,strategy:i,middlewareData:h}})(t,e,{...i,platform:r})};export{st as a,ot as b,at as c,it as d,ct as f,rt as o,lt as s};
