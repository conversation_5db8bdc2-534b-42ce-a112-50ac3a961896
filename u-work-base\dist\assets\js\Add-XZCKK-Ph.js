import{b as e}from"./main-Djn9RDyT.js";import{b as s,u as a}from"./userManage-DLgtGpjc.js";import i from"./UserList-Bo1tWySN.js";import{M as t,S as r}from"./ant-design-vue-DYY9BtJq.js";import{d as o,r as l,a9 as p,aa as n,n as m,c as d,e as u,b as c,ag as v,ad as j,am as f,F as g,o as y}from"./@vue-HScy-mz9.js";import{_ as h}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./Depart-B4y-bHgG.js";import"./department-CTxHSeTj.js";import"./js-binary-schema-parser-G48GG52R.js";const b={class:"selector-wrap keep-px"},k={class:"tree-search"},w={class:"content-wrap"},_={class:"book"},x={class:"left"},z={class:"right"},I={class:"checked-wrap"},S={class:"name"},L=h(o({__name:"Add",props:{id:{type:String||Array,default:""}},emits:["ok"],setup(o,{expose:h,emit:L}){const C=L,D=l(!1),O=l(!1),U=l(""),A=o,J=l([]),M=l([]);let N=[];const $=l(),F=e=>{"add"===e.type?T.value=[...T.value,e]:"delete"===e.type&&(T.value=T.value.filter((s=>s.id!==e.id)))},T=l([]),q=e=>`${e.name}(${e.account})`,B=async()=>{O.value=!0;let i=T.value.filter((e=>!N.includes(e)));i=i.map((e=>e.id));let t=N.filter((e=>!T.value.includes(e)));if(t=t.map((e=>e.id)),i.length)try{const a=await s({enterpriseId:A.id,grantUserIdList:i});if(200!==a.code)return O.value=!1,void e("error",a.message)}catch(r){O.value=!1}if(t.length)try{const s=await a({enterpriseId:A.id,grantUserIdList:t});if(200!==s.code)return O.value=!1,void e("error",s.message)}catch(r){O.value=!1}O.value=!1,e("success","团队成员新增成功"),C("ok"),E()},E=()=>{O.value||(O.value=!1,J.value=[],D.value=!1)};return h({init:async(e,s)=>{U.value=e,M.value=s,T.value=JSON.parse(JSON.stringify(s)),N=[...T.value],D.value=!0,m((()=>{$.value.changeData(T.value,"init")}))}}),(e,s)=>{const a=f("close-outlined"),o=r,l=t;return y(),p(l,{title:"新增团队成员","body-style":{height:"500px",paddingTop:"10px",overflow:"auto",boxSizing:"border-box"},"wrap-class-name":"cus-modal add-wrap",width:900,open:D.value,"mask-closable":!1,"confirm-loading":O.value,onOk:B,onCancel:E},{default:n((()=>[d(o,{spinning:O.value},{default:n((()=>[u("div",b,[u("div",k,[u("div",w,[u("div",_,[u("div",x,[d(i,{ref_key:"userListRef",ref:$,onUserSelectChange:F},null,512)]),u("div",z,[s[0]||(s[0]=u("p",null,"已选",-1)),u("div",I,[(y(!0),c(g,null,v(T.value,((e,s)=>(y(),c("div",{key:e.id,class:"checked-dev"},[u("div",S,j(q(e)),1),d(a,{class:"delete-icon",onClick:a=>((e,s)=>{T.value.splice(e,1),$.value.changeData(s,"delete")})(s,e)},null,8,["onClick"])])))),128))])])])])])])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-853493f0"]]);export{L as default};
