import{u as e,a,b as s}from"./main-Djn9RDyT.js";import{g as o,a as l,e as r}from"./userManage-D6iEBY45.js";import{u as t}from"./vue-router-BEwRlUkF.js";import{S as i,F as m,_ as n,b as d,c as p,I as u,d as c,M as v}from"./ant-design-vue-DYY9BtJq.js";import{d as f,r as j,a as h,am as g,a9 as w,o as _,aa as b,c as y,ab as k,e as x}from"./@vue-HScy-mz9.js";import{_ as z}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const A={class:"form-item-notice keep-px"},P=z(f({__name:"AddAndEdit",emits:["ok"],setup(f,{expose:z,emit:P}){e();const q=a(),E=P,U=j(!1),Z=j(!1),$=j("");j([]);const F=j(),I=h({account:"",name:"",password:"",confirm:"",phone:"",email:"",id:""}),M={account:[{required:!0,min:5,message:"请输入至少五个字符的账号！"},{max:32,message:"账号长度不超过32！"}],name:[{required:!0,message:"请输入姓名！"},{max:20,message:"姓名长度不超过20！"}],password:[{required:!0,message:"请输入密码！"},{validator:(e,a)=>a?/(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,15}/.test(a)?Promise.resolve():Promise.reject(new Error("请使用大小写字母、数字和符号组合的密码，长度为8-15位!")):Promise.reject(new Error("请输入密码!"))}],confirm:[{required:!0,message:"请再次输入密码！"},{validator:(e,a)=>a!==I.password?Promise.reject(new Error("请确认两次输入密码的一致性！")):Promise.resolve()}],phone:[{required:!0,message:"请输入手机号！"},{validator:(e,a)=>a&&!/^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(a)?Promise.reject(new Error("手机号码格式不正确!")):Promise.resolve()}],email:[{required:!0,message:"请输入邮箱！"},{validator:(e,a)=>a&&!/^([A-Za-z0-9_\-.])+@([A-Za-z0-9_\-.])+\.([A-Za-z]{2,4})$/.test(a)?Promise.reject(new Error("邮箱地址格式不正确!")):Promise.resolve()}]},{params:K}=t(),{id:B}=K,C=()=>{var e;Z.value=!0,null==(e=F.value)||e.validateFields().then((()=>{const e={account:I.account,name:I.name,phone:I.phone,email:I.email,source:"development"};"add"===$.value?(e.password=I.password,e.confirm=I.confirm,e.status=3,e.enterpriseId=q.checkedEnterprise.id||B,l(e).then((e=>{200===e.code?(s("success","提交成功，请等待管理员审批"),E("ok"),D()):s("error",`${e.message}`)})).finally((()=>{Z.value=!1}))):"edit"===$.value&&(e.id=I.id,r(e).then((e=>{200===e.code?(s("success","用户修改成功"),E("ok"),D()):s("error",`修改失败：${e.message}`)})).finally((()=>{Z.value=!1})))})).catch((e=>{Z.value=!1}))},D=()=>{var e;null==(e=F.value)||e.resetFields(),Z.value=!1,U.value=!1};return z({add:(e,a)=>{$.value=e,"add"===e||"edit"===e&&o({id:a}).then((e=>{if(200===e.code){const a=e.data;I.account=a.account,I.name=a.name,I.phone=a.phone,I.email=a.email,I.id=a.id}})),U.value=!0}}),(e,a)=>{const s=u,o=p,l=d,r=g("question-circle-outlined"),t=c,f=n,j=m,h=i,z=v;return _(),w(z,{title:("add"===$.value?"新增":"编辑")+"团队成员","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",width:700,open:U.value,"mask-closable":!1,"confirm-loading":Z.value,onOk:C,onCancel:D},{default:b((()=>[y(h,{spinning:Z.value},{default:b((()=>[y(j,{ref_key:"formAddRef",ref:F,model:I,rules:M,"label-align":"left"},{default:b((()=>[y(f,{gutter:24},{default:b((()=>[y(l,{md:12,sm:24},{default:b((()=>[y(o,{label:"账号","has-feedback":"",name:"account"},{default:b((()=>[y(s,{value:I.account,"onUpdate:value":a[0]||(a[0]=e=>I.account=e),placeholder:"请输入账号",maxlength:32},null,8,["value"])])),_:1})])),_:1}),y(l,{md:12,sm:24},{default:b((()=>[y(o,{label:"姓名","has-feedback":"",name:"name"},{default:b((()=>[y(s,{value:I.name,"onUpdate:value":a[1]||(a[1]=e=>I.name=e),placeholder:"请输入姓名",maxlength:20},null,8,["value"])])),_:1})])),_:1}),"add"===$.value?(_(),w(l,{key:0,md:12,sm:24},{default:b((()=>[y(o,{label:"密码","validate-first":!0,"has-feedback":"",name:"password"},{default:b((()=>[y(s,{value:I.password,"onUpdate:value":a[2]||(a[2]=e=>I.password=e),autocomplete:"new-password",type:"password",placeholder:"请输入密码",maxlength:15},null,8,["value"]),x("span",A,[y(t,{placement:"right"},{title:b((()=>a[6]||(a[6]=[x("div",null,"请使用大小写字母、数字和符号组合的密码，长度为8-15位!",-1)]))),default:b((()=>[y(r,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1})):k("",!0),"add"===$.value?(_(),w(l,{key:1,md:12,sm:24},{default:b((()=>[y(o,{label:"重复密码","validate-first":!0,maxlength:15,"has-feedback":"",name:"confirm"},{default:b((()=>[y(s,{value:I.confirm,"onUpdate:value":a[3]||(a[3]=e=>I.confirm=e),autocomplete:"new-password",type:"password",placeholder:"请再次输入密码",maxlength:15},null,8,["value"])])),_:1})])),_:1})):k("",!0),y(l,{md:12,sm:24},{default:b((()=>[y(o,{label:"手机号码","validate-first":!0,"has-feedback":"",name:"phone"},{default:b((()=>[y(s,{value:I.phone,"onUpdate:value":a[4]||(a[4]=e=>I.phone=e),placeholder:"请输入手机号码",maxlength:11},null,8,["value"])])),_:1})])),_:1}),y(l,{md:12,sm:24},{default:b((()=>[y(o,{label:"邮箱","validate-first":!0,"has-feedback":"",name:"email"},{default:b((()=>[y(s,{value:I.email,"onUpdate:value":a[5]||(a[5]=e=>I.email=e),placeholder:"请输入邮箱",maxlength:30},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-73192c99"]]);export{P as default};
