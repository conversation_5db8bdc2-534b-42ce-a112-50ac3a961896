import{d as e,r as a,am as l,a9 as t,o as s,aa as r,c as o,y as i,u,J as n,ad as p,e as m,G as d,n as c}from"./@vue-HScy-mz9.js";import{Q as v}from"./@vueup-CLVdhRgW.js";import{b as f}from"./main-Djn9RDyT.js";import{a as g,e as h,m as j}from"./index-GENaTOlC.js";import{F as k,_ as b,b as y,c as w,I as F,z as _,T as z,U as x,k as T,M as E}from"./ant-design-vue-DYY9BtJq.js";import{_ as M}from"./vue-qr-CB2aNKv5.js";import"./quill-BBEhJLA6.js";import"./@babel-B4rXMRun.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const q={class:"ant-upload-drag-icon"},I={class:"progress-box"},N=M(e({__name:"AddAndEdit",emits:["ok"],setup(e,{expose:M,emit:N}){const L=N,U=a(!1),O=a(!1),H=a(),V=a({name:"",remark:"",effectFile:"",id:"",templateTags:[]}),$=a(0);let P={};const R=a([]),A=a({modules:{clipboard:{matchers:[["img",(e,a)=>{const l=[];return a.ops.forEach((e=>{e.insert&&"string"==typeof e.insert&&l.push({insert:e.insert})})),a.ops=l,a}]]}},placeholder:"请输入效果包说明",readOnly:!1,theme:"snow"}),C=a([]),D=a(),J=a(new Map),S=a([]),B=a(new Map),G={templateTags:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let t=0;return a.forEach((e=>{if(1===(null==e?void 0:e.length))if(S.value.includes(e[0])){const a=R.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+((null==e?void 0:e.length)||0)-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}],name:[{required:!0,message:"请输入效果包名称！",trigger:"blur"}],remark:[{required:!0,message:"请输入效果包说明！",trigger:"blur"}],effectFile:[{required:!0,validator:e=>{const a=C.value[0];return new Promise(((e,l)=>{if(a){const t=a.name.split(".").pop().toLowerCase(),s=2097152e3;["zip"].includes(t)?a.size?a.size<=s?e():l(new Error("效果包文件大小不能超过2000MB！")):e():l(new Error("只支持zip格式的文件！"))}else l(new Error("请上传效果包文件！"))}))},trigger:"change"}]},K=a(),Q=()=>{O.value||(H.value.resetFields(),U.value=!1,C.value=[],O.value=!1,V.value={name:"",remark:"",effectFile:"",id:"",templateTags:[]},K.value.setHTML(""),$.value+=1)},W=a({percent:0,progressFlag:!1}),Z=e=>{e&&e.loaded&&e.total&&(W.value.percent=Math.round(100*e.loaded/e.total))},X=e=>{const a=e.name;return".zip"!==a.substring(a.lastIndexOf("."))?(C.value=[],f("error","请上传.zip格式的文件"),!1):(C.value=[e],V.value.effectFile=e,!0)},Y=async e=>{const{file:a}=e;C.value[0]=a,V.value.effectFile=a},ee=e=>{V.value.remark=e},ae=()=>{C.value=[],V.value.effectFile=""};return M({init:async(e,a,l)=>{var t;if(U.value=!0,D.value=e,await new Promise((e=>{j().then((a=>{P={};const l=a.data.map((e=>{var a;return P[e.id]=e.tags,S.value.push(e.id),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(J.value.set(`${a.tagName}`,a.color),B.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));R.value=l.filter((e=>e.children)),e(R.value)}))})),"add"===e)V.value={name:"",remark:"",effectFile:"",id:"",templateTags:[]},C.value=[],c((()=>{H.value.resetFields()}));else if("edit"===e){V.value.name=a.name,V.value.remark=a.remark,V.value.id=a.id,V.value.templateTags=[];const e=[];null==(t=a.tags)||t.forEach((a=>{B.value.get(a.tagId)&&e.push([B.value.get(a.tagId),a.tagId])})),c((()=>{K.value.setHTML(V.value.remark),V.value.templateTags=e})),C.value[0]={name:`${a.name}.zip`,uid:Math.floor(900*Math.random())+100}}}}),(e,a)=>{const c=F,j=w,M=y,N=z,$=_,S=l("UploadOutlined"),B=x,le=T,te=b,se=k,re=E;return s(),t(re,{width:800,title:"edit"===D.value?"编辑效果包":"新增效果包","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:U.value,"confirm-loading":O.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[4]||(a[4]=e=>(O.value=!0,void H.value.validate().then((()=>{const{name:e,remark:a,effectFile:l,id:t,templateTags:s}=V.value,r=new FormData,o=[];s.forEach((e=>{if(e[1]){const{id:a,tagName:l,color:t}=P[e[0]].find((a=>a.id===e[1]));o.push({tagId:a,tagName:l,color:t})}else e[0]&&P[e[0]].forEach((e=>{const{id:a,tagName:l,color:t}=e;o.push({tagId:a,tagName:l,color:t})}))})),r.append("name",e),r.append("effectFile",l),r.append("remark",a),r.append("tags",JSON.stringify(o)),r.append("kind","1"),W.value.percent=0,W.value.progressFlag=!0,"add"===D.value?g(r,Z).then((e=>{O.value=!1,200===e.code?(f("success","上传成功，请等待管理员审批"),U.value=!1,Q(),L("ok")):f("error",e.message),W.value.percent=0,W.value.progressFlag=!1})).catch((()=>{O.value=!1,W.value.percent=0,W.value.progressFlag=!1})):("string"==typeof l&&r.delete("effectFile"),r.append("id",t),W.value.percent=0,W.value.progressFlag=!0,h(r,Z).then((e=>{O.value=!1,200===e.code?(f("success","效果包修改成功"),U.value=!1,Q(),L("ok")):f("error",e.message),W.value.percent=0,W.value.progressFlag=!1})).catch((()=>{O.value=!1,W.value.percent=0,W.value.progressFlag=!1})))})).catch((e=>{O.value=!1})))),onCancel:Q},{default:r((()=>[o(se,{ref_key:"formRef",ref:H,model:V.value,rules:G,"label-align":"left"},{default:r((()=>[o(te,{gutter:24},{default:r((()=>[o(M,{md:24,sm:24},{default:r((()=>[o(j,{name:"name",label:"名称","has-feedback":""},{default:r((()=>[o(c,{value:V.value.name,"onUpdate:value":a[0]||(a[0]=e=>V.value.name=e),placeholder:"请输入效果包名称",maxlength:30},null,8,["value"])])),_:1})])),_:1}),o(M,{md:24,sm:24},{default:r((()=>[o(j,{name:"templateTags",label:"标签","has-feedback":""},{default:r((()=>[o($,{value:V.value.templateTags,"onUpdate:value":a[1]||(a[1]=e=>V.value.templateTags=e),defaultValue:V.value.templateTags,"show-checked-strategy":u(_).SHOW_CHILD,style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:R.value,placeholder:"请选择标签",checkStrictly:!0},{tagRender:r((e=>{return[(s(),t(N,{key:e.value,color:(a=e.label,J.value.get(a)||"blue")},{default:r((()=>[n(p(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","options"])])),_:1})])),_:1}),o(M,{md:24,sm:24,class:"form-item"},{default:r((()=>[o(j,{name:"effectFile",label:"效果包文件","has-feedback":""},{default:r((()=>[o(B,{fileList:C.value,"onUpdate:fileList":a[2]||(a[2]=e=>C.value=e),"before-upload":X,"show-upload-list":!0,accept:".zip",multiple:!1,"max-count":1,"custom-request":Y,onRemove:ae},{default:r((()=>[m("a",q,[o(S,{class:"UploadOutlined",style:{"font-size":"30px",color:"var(--upload-icon-color)"}})]),a[5]||(a[5]=m("p",{style:{"font-size":"14px"}},[n("将文件拖至此处，或点击 "),m("a",null,"上传文件")],-1)),a[6]||(a[6]=m("p",{style:{"font-size":"12px",color:"#ccc"},class:"ant-upload-hint"},"支持.zip且不超过2000M的文件",-1))])),_:1},8,["fileList"]),i(m("div",null,[o(le,{percent:W.value.percent,size:"small"},null,8,["percent"])],512),[[d,W.value.progressFlag]])])),_:1})])),_:1}),o(M,{md:24,sm:24},{default:r((()=>[o(j,{name:"remark",label:"效果包说明","has-feedback":""},{default:r((()=>[o(u(v),{ref_key:"quillEditorRef",ref:K,modelValue:V.value.remark,"onUpdate:modelValue":a[3]||(a[3]=e=>V.value.remark=e),modelModifiers:{content:!0},style:{height:"180px"},options:A.value,"content-type":"html","onUpdate:content":ee},null,8,["modelValue","options"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"]),i(m("div",I,[o(le,{type:"circle",percent:W.value.percent},null,8,["percent"])],512),[[d,W.value.progressFlag]])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-b4088993"]]);export{N as default};
