import{a1 as e,l as a,b as l}from"./main-Djn9RDyT.js";import{a as t,c as s,e as d}from"./userManage-DLgtGpjc.js";import{n as o}from"./department-CTxHSeTj.js";import{d as r}from"./dayjs-CA7qlNSr.js";import{S as i,F as m,_ as n,b as p,c as u,I as c,d as v,y as f,o as h,B as j,M as g}from"./ant-design-vue-DYY9BtJq.js";import{ac as w}from"./@ant-design-CA72ad83.js";import{v as y}from"./uuid-P-OY1HC2.js";import{d as b,r as _,a as k,am as I,a9 as x,o as P,aa as U,c as D,ab as z,b as A,e as E,F as N,ag as T,u as q,J as Y}from"./@vue-HScy-mz9.js";import{_ as M}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./@babel-B4rXMRun.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const Z={class:"form-item-notice keep-px"},$={class:"form-item-notice keep-px",style:{left:"-17px"}},C={class:"ext-org"},F={class:"delete-icon"},O=M(b({__name:"AddAndEdit",emits:["ok"],setup(b,{expose:M,emit:O}){const S=O,V=_(!1),B=_(!1),H=_([]),J=_(""),K={children:"children",label:"name",key:"id",value:"id"},L=e=>e&&e<r().endOf("day"),R=_([]),W=()=>{R.value.push({deptId:"",key:y()})},X=_(!1),G=a=>e(a,"22e3d8b29abf2a9e6f82ae1495cd7040"),Q=e=>{e.forEach((e=>{var a;"dept"!==e.source&&(e.disabled=!0),(null==(a=e.children)?void 0:a.length)&&Q(e.children)}))},ee=_(),ae=k({account:"",name:"",password:"",confirm:"",phone:"",email:"",deptId:null,deptName:"",enterpriseId:"",passwordPeriodTemp:"",id:""}),le=_([]),te=async()=>{try{const e=await a({code:"KEY_WORD"});200===e.code&&(le.value=e.data)}catch(e){}},se={account:[{required:!0,min:5,message:"请输入至少五个字符的账号！"},{max:32,message:"账号长度不超过32！"},{validator:(e,a)=>{if(a){if(le.value.filter((e=>a.includes(e.value))).length>0)return Promise.reject(new Error(`账号中不建议带有${le.value.map((e=>e.value)).toString()}信息!`))}return Promise.resolve()}}],name:[{required:!0,message:"请输入姓名！"},{max:20,message:"姓名长度不超过20！"}],password:[{required:!0,message:"请输入密码！"},{validator:(e,a)=>a?/(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,15}/.test(a)?Promise.resolve():Promise.reject(new Error("请使用大小写字母、数字和符号组合的密码，长度为8-15位!")):Promise.reject(new Error("请输入密码!"))}],confirm:[{required:!0,message:"请再次输入密码！"},{validator:(e,a)=>a!==ae.password?Promise.reject(new Error("请确认两次输入密码的一致性！")):Promise.resolve()}],phone:[{required:!0,message:"请输入手机号！"},{validator:(e,a)=>a&&!/^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(a)?Promise.reject(new Error("手机号码格式不正确!")):Promise.resolve()}],email:[{required:!0,message:"请输入邮箱！"},{validator:(e,a)=>a&&!/^([A-Za-z0-9_\-.])+@([A-Za-z0-9_\-.])+\.([A-Za-z]{2,4})$/.test(a)?Promise.reject(new Error("邮箱地址格式不正确!")):Promise.resolve()}],deptId:[{required:!0,message:"请选择部门！"}]},de=()=>{var e;B.value=!0,null==(e=ee.value)||e.validateFields().then((()=>{const e={account:ae.account,name:ae.name,phone:ae.phone,email:ae.email,passwordPeriodTemp:ae.passwordPeriodTemp,source:"operation",sysUserDetailParam:{deptId:ae.deptId,extIds:R.value.filter((e=>e.deptId)).map((e=>({deptId:e.deptId}))),deptName:ae.deptName}};"add"===J.value?(e.password=ae.password,e.confirm=ae.confirm,e.enterpriseId=ae.enterpriseId,s(e).then((e=>{200===e.code?(l("success","用户新增成功"),S("ok"),oe()):l("error",`用户新增失败：${e.message}`)})).finally((()=>{B.value=!1}))):"edit"===J.value&&(e.id=ae.id,d(e).then((e=>{200===e.code?(l("success","用户修改成功"),S("ok"),oe()):l("error",`用户修改失败：${e.message}`)})).finally((()=>{B.value=!1})))})).catch((e=>{B.value=!1}))},oe=()=>{var e;null==(e=ee.value)||e.resetFields(),B.value=!1,R.value=[],V.value=!1},re=(e,a)=>{ae.enterpriseId=a.enterpriseId,ae.deptName=a.name};return M({init:(e,a,l)=>{J.value=e,"add"===e?(X.value=!1,ae.deptId=a.deptId,ae.deptName=a.deptName,ae.enterpriseId=a.enterpriseId):"edit"===e&&t({id:l}).then((e=>{var a;if(200===e.code){const l=e.data;ae.account=l.account,ae.name=l.name;const t=G(l.phone);ae.phone=t.flag?t.data:l.phone;const s=G(l.email);ae.email=s.flag?s.data:l.email,ae.passwordPeriodTemp=l.passwordPeriodTemp,ae.deptId=l.sysUserDetailVo.deptId,ae.deptName=l.sysUserDetailVo.deptName,ae.id=l.id,"LDAP"===(null==(a=l.sysUserDetailVo)?void 0:a.deptName)?X.value=!0:X.value=!1,R.value=l.sysUserDetailVo.extDeptPos.map((e=>({deptId:e.deptId,key:y()})))}})),o({}).then((e=>{var a,l;200===e.code&&(e.data.disabled=!0,(null==(l=null==(a=e.data)?void 0:a.children)?void 0:l.length)&&Q(e.data.children),H.value=[e.data])})),te(),V.value=!0}}),(e,a)=>{const l=c,t=u,s=p,d=I("question-circle-outlined"),o=v,r=f,y=h,b=I("icon-font"),_=j,k=n,M=m,O=i,S=g;return P(),x(S,{title:"用户"+("add"===J.value?"新增":"编辑"),"body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",width:700,open:V.value,"mask-closable":!1,"confirm-loading":B.value,onOk:de,onCancel:oe},{default:U((()=>[D(O,{spinning:B.value},{default:U((()=>[D(M,{ref_key:"formAddRef",ref:ee,model:ae,rules:se,"label-align":"left"},{default:U((()=>[D(k,{gutter:24},{default:U((()=>[D(s,{md:12,sm:24},{default:U((()=>[D(t,{label:"账号","has-feedback":"",name:"account"},{default:U((()=>[D(l,{value:ae.account,"onUpdate:value":a[0]||(a[0]=e=>ae.account=e),placeholder:"请输入账号",maxlength:32,disabled:X.value},null,8,["value","disabled"])])),_:1})])),_:1}),D(s,{md:12,sm:24},{default:U((()=>[D(t,{label:"姓名","has-feedback":"",name:"name"},{default:U((()=>[D(l,{value:ae.name,"onUpdate:value":a[1]||(a[1]=e=>ae.name=e),autocomplete:"off",placeholder:"请输入姓名",maxlength:20},null,8,["value"])])),_:1})])),_:1}),"add"===J.value?(P(),x(s,{key:0,md:12,sm:24},{default:U((()=>[D(t,{label:"密码","validate-first":!0,"has-feedback":"",name:"password"},{default:U((()=>[D(l,{value:ae.password,"onUpdate:value":a[2]||(a[2]=e=>ae.password=e),autocomplete:"new-password",type:"password",placeholder:"请输入密码",maxlength:15},null,8,["value"]),E("span",Z,[D(o,{placement:"right"},{title:U((()=>a[8]||(a[8]=[E("div",null,"请使用大小写字母、数字和符号组合的密码，长度为8-15位!",-1)]))),default:U((()=>[D(d,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1})):z("",!0),"add"===J.value?(P(),x(s,{key:1,md:12,sm:24},{default:U((()=>[D(t,{label:"重复密码","validate-first":!0,maxlength:15,"has-feedback":"",name:"confirm"},{default:U((()=>[D(l,{value:ae.confirm,"onUpdate:value":a[3]||(a[3]=e=>ae.confirm=e),autocomplete:"new-password",type:"password",placeholder:"请再次输入密码",maxlength:15},null,8,["value"])])),_:1})])),_:1})):z("",!0),D(s,{md:12,sm:24},{default:U((()=>[D(t,{label:"账号期限","validate-first":!0,"has-feedback":"",name:"passwordPeriodTemp"},{default:U((()=>[D(r,{showTime:!0,value:ae.passwordPeriodTemp,"onUpdate:value":a[4]||(a[4]=e=>ae.passwordPeriodTemp=e),"disabled-date":L,format:"YYYY-MM-DD hh:mm:ss","value-format":"YYYY-MM-DD hh:mm:ss"},null,8,["value"]),E("span",$,[D(o,{placement:"right"},{title:U((()=>a[9]||(a[9]=[E("div",null,"账号过期时间：不传值则为永久用户",-1)]))),default:U((()=>[D(d,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1}),D(s,{md:12,sm:24},{default:U((()=>[D(t,{label:"手机号码","validate-first":!0,"has-feedback":"",name:"phone"},{default:U((()=>[D(l,{value:ae.phone,"onUpdate:value":a[5]||(a[5]=e=>ae.phone=e),placeholder:"请输入手机号码",maxlength:11},null,8,["value"])])),_:1})])),_:1}),D(s,{md:12,sm:24},{default:U((()=>[D(t,{label:"邮箱","validate-first":!0,"has-feedback":"",name:"email"},{default:U((()=>[D(l,{value:ae.email,"onUpdate:value":a[6]||(a[6]=e=>ae.email=e),autocomplete:"off",placeholder:"请输入邮箱",maxlength:30},null,8,["value"])])),_:1})])),_:1}),D(s,{md:12,sm:24},{default:U((()=>[D(t,{label:"部门","validate-first":!0,"has-feedback":"",name:"deptId"},{default:U((()=>[D(y,{value:ae.deptId,"onUpdate:value":a[7]||(a[7]=e=>ae.deptId=e),style:{width:"100%"},"dropdown-style":{maxHeight:"300px",overflow:"auto"},"tree-data":H.value,"field-names":K,placeholder:"请选择部门","tree-default-expand-all":"",onSelect:re},null,8,["value","tree-data"])])),_:1})])),_:1}),(P(!0),A(N,null,T(R.value,(e=>(P(),x(s,{key:e.key,md:12,sm:24},{default:U((()=>[D(t,{label:"附属部门"},{default:U((()=>[E("div",C,[D(y,{value:e.deptId,"onUpdate:value":a=>e.deptId=a,"tree-data":H.value,style:{width:"90%"},"field-names":K,placeholder:"请选择附属部门","tree-default-expand-all":""},null,8,["value","onUpdate:value","tree-data"]),E("div",F,[D(q(w),{onClick:a=>{return l=e.key,void(R.value=R.value.filter((e=>e.key!==l)));var l}},null,8,["onClick"])])])])),_:2},1024)])),_:2},1024)))),128)),D(s,{md:12,sm:24},{default:U((()=>[D(_,{class:"btn",onClick:W},{icon:U((()=>[D(b,{type:"icon-add",class:"icon-add"})])),default:U((()=>[a[10]||(a[10]=Y(" 增加附属部门 "))])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-5e1695e3"]]);export{O as default};
