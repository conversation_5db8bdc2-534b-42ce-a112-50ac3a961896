import{d as e,r as a,am as l,a9 as t,o as s,aa as r,c as o,y as i,u as n,J as u,ad as p,e as d,G as m,n as c}from"./@vue-HScy-mz9.js";import{Q as v}from"./@vueup-CLVdhRgW.js";import{b as f}from"./main-Djn9RDyT.js";import{a as g,e as h,m as j}from"./index-GENaTOlC.js";import{F,_ as y,b,c as k,I as _,z as w,T as x,U as z,k as M,M as T}from"./ant-design-vue-DYY9BtJq.js";import{_ as q}from"./vue-qr-CB2aNKv5.js";import"./quill-BBEhJLA6.js";import"./@babel-B4rXMRun.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const E={class:"ant-upload-drag-icon"},I={class:"ant-upload-drag-icon"},L={class:"progress-box"},U=q(e({__name:"AddAndEdit",emits:["ok"],setup(e,{expose:q,emit:U}){const N=U,O=a(!1),H=a(!1),$=a(),R=a({name:"",remark:"",effectFile:"",sceneFile:"",id:"",templateTags:[]}),V=a(0);let P={};const A=a([]),B=a({modules:{clipboard:{matchers:[["img",(e,a)=>{const l=[];return a.ops.forEach((e=>{e.insert&&"string"==typeof e.insert&&l.push({insert:e.insert})})),a.ops=l,a}]]}},placeholder:"请输入效果包说明",readOnly:!1,theme:"snow"}),C=a([]),D=a([]),J=a(),S=a(new Map),G=a([]),K=a(new Map),Q=e=>{const a="effectFile"===e.field?C.value[0]:D.value[0];return new Promise(((l,t)=>{if(a){const s=a.name.split(".").pop().toLowerCase(),r=1048576e3;("effectFile"===e.field?["zip"]:["tjs"]).includes(s)?a.size?a.size<=r?l():t(new Error("effectFile"===e.field?"效果包文件大小不能超过1000MB！":"文件大小不能超过1000MB！")):l():t(new Error("effectFile"===e.field?"只支持zip格式的文件！":"只支持tjs格式的文件！"))}else t(new Error("effectFile"===e.field?"请上传效果包文件！":"请上传场景文件！"))}))},W={templateTags:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let t=0;return a.forEach((e=>{if(1===(null==e?void 0:e.length))if(G.value.includes(e[0])){const a=A.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+((null==e?void 0:e.length)||0)-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}],name:[{required:!0,message:"请输入效果包名称！",trigger:"blur"}],remark:[{required:!0,message:"请输入效果包说明！",trigger:"blur"}],effectFile:[{required:!0,validator:Q,trigger:"change"}],sceneFile:[{required:!0,validator:Q,trigger:"change"}]},Z=a(),X=()=>{H.value||($.value.resetFields(),O.value=!1,C.value=[],D.value=[],H.value=!1,R.value={name:"",remark:"",sceneFile:"",effectFile:"",id:"",templateTags:[]},Z.value.setHTML(""),V.value+=1,Y.value.percent=0,Y.value.progressFlag=!1)},Y=a({percent:0,progressFlag:!1}),ee=e=>{e&&e.loaded&&e.total&&(Y.value.percent=Math.round(100*e.loaded/e.total))},ae=e=>{R.value.remark=e},le=e=>{const a=e.name;return".zip"!==a.substring(a.lastIndexOf("."))?(C.value=[],f("error","请上传.zip格式的文件"),!1):(C.value=[e],R.value.effectFile=e,!0)},te=async e=>{const{file:a}=e;C.value[0]=a,R.value.effectFile=a},se=()=>{C.value=[],R.value.effectFile=""},re=e=>{const a=e.name;return".tjs"!==a.substring(a.lastIndexOf("."))?(D.value=[],f("error","请上传.tjs格式的文件"),!1):(D.value=[e],R.value.sceneFile=e,!0)},oe=async e=>{const{file:a}=e;D.value[0]=a,R.value.sceneFile=a},ie=()=>{D.value=[],R.value.sceneFile=""};return q({init:async(e,a,l)=>{var t;if(O.value=!0,J.value=e,await new Promise((e=>{j().then((a=>{P={};const l=a.data.map((e=>{var a;return P[e.id]=e.tags,G.value.push(e.id),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(S.value.set(`${a.tagName}`,a.color),K.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));A.value=l.filter((e=>e.children)),e(A.value)}))})),"add"===e)R.value={name:"",remark:"",effectFile:"",sceneFile:"",id:"",templateTags:[]},C.value=[],c((()=>{$.value.resetFields()}));else if("edit"===e){R.value.name=a.name,R.value.remark=a.remark,R.value.id=a.id,R.value.templateTags=[];const e=[];null==(t=a.tags)||t.forEach((a=>{K.value.get(a.tagId)&&e.push([K.value.get(a.tagId),a.tagId])})),c((()=>{Z.value.setHTML(R.value.remark),R.value.templateTags=e})),C.value[0]={name:`${a.name}.zip`,uid:Math.floor(900*Math.random())+100},D.value[0]={name:`${a.code}.tjs`,uid:Math.floor(900*Math.random())+100}}}}),(e,a)=>{const c=_,j=k,q=b,U=x,V=w,G=l("UploadOutlined"),K=z,Q=y,ne=F,ue=M,pe=T;return s(),t(pe,{width:800,title:"edit"===J.value?"编辑效果包":"新增效果包","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:O.value,"confirm-loading":H.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[5]||(a[5]=e=>(H.value=!0,void $.value.validate().then((()=>{const{name:e,remark:a,effectFile:l,sceneFile:t,id:s,templateTags:r}=R.value,o=new FormData,i=[];r.forEach((e=>{if(e[1]){const{id:a,tagName:l,color:t}=P[e[0]].find((a=>a.id===e[1]));i.push({tagId:a,tagName:l,color:t})}else e[0]&&P[e[0]].forEach((e=>{const{id:a,tagName:l,color:t}=e;i.push({tagId:a,tagName:l,color:t})}))})),o.append("name",e),o.append("effectFile",l),o.append("sceneFile",t),o.append("remark",a),o.append("tags",JSON.stringify(i)),o.append("kind","2"),Y.value.percent=0,Y.value.progressFlag=!0,"add"===J.value?g(o,ee).then((e=>{H.value=!1,200===e.code?(f("success","上传成功，请等待管理员审批"),O.value=!1,X(),N("ok")):f("error",e.message),Y.value.percent=0,Y.value.progressFlag=!1})).catch((()=>{H.value=!1,Y.value.percent=0,Y.value.progressFlag=!1})):("string"==typeof l&&o.delete("effectFile"),"string"==typeof t&&o.delete("sceneFile"),o.append("id",s),h(o,ee).then((e=>{H.value=!1,200===e.code?(f("success","效果包修改成功"),O.value=!1,X(),N("ok")):f("error",e.message),Y.value.percent=0,Y.value.progressFlag=!1})).catch((()=>{H.value=!1,Y.value.percent=0,Y.value.progressFlag=!1})))})).catch((e=>{H.value=!1})))),onCancel:X},{default:r((()=>[o(ne,{ref_key:"formRef",ref:$,model:R.value,rules:W,"label-align":"left"},{default:r((()=>[o(Q,{gutter:24},{default:r((()=>[o(q,{md:24,sm:24},{default:r((()=>[o(j,{name:"name",label:"名称","has-feedback":""},{default:r((()=>[o(c,{value:R.value.name,"onUpdate:value":a[0]||(a[0]=e=>R.value.name=e),placeholder:"请输入效果包名称",maxlength:30},null,8,["value"])])),_:1})])),_:1}),o(q,{md:24,sm:24},{default:r((()=>[o(j,{name:"templateTags",label:"标签","has-feedback":""},{default:r((()=>[o(V,{value:R.value.templateTags,"onUpdate:value":a[1]||(a[1]=e=>R.value.templateTags=e),defaultValue:R.value.templateTags,"show-checked-strategy":n(w).SHOW_CHILD,style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:A.value,placeholder:"请选择标签",checkStrictly:!0},{tagRender:r((e=>{return[(s(),t(U,{key:e.value,color:(a=e.label,S.value.get(a)||"blue")},{default:r((()=>[u(p(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","options"])])),_:1})])),_:1}),o(q,{md:24,sm:24,class:"form-item"},{default:r((()=>[o(j,{name:"effectFile",label:"效果包文件","has-feedback":""},{default:r((()=>[o(K,{fileList:C.value,"onUpdate:fileList":a[2]||(a[2]=e=>C.value=e),"before-upload":le,"show-upload-list":!0,accept:".zip",multiple:!1,"max-count":1,"custom-request":te,onRemove:se},{default:r((()=>[d("a",E,[o(G,{class:"UploadOutlined",style:{"font-size":"30px",color:"var(--upload-icon-color)"}})]),a[6]||(a[6]=d("p",{style:{"font-size":"14px"}},[u("将文件拖至此处，或点击 "),d("a",null,"上传文件")],-1)),a[7]||(a[7]=d("p",{style:{"font-size":"12px",color:"#ccc"},class:"ant-upload-hint"},"支持 .zip且不超过1000M的文件",-1))])),_:1},8,["fileList"])])),_:1})])),_:1}),o(q,{md:24,sm:24,class:"form-item"},{default:r((()=>[o(j,{name:"sceneFile",label:"场景文件","has-feedback":""},{default:r((()=>[o(K,{fileList:D.value,"onUpdate:fileList":a[3]||(a[3]=e=>D.value=e),"before-upload":re,"show-upload-list":!0,accept:".tjs",multiple:!1,"max-count":1,"custom-request":oe,onRemove:ie},{default:r((()=>[d("a",I,[o(G,{class:"UploadOutlined",style:{"font-size":"30px",color:"var(--upload-icon-color)"}})]),a[8]||(a[8]=d("p",{style:{"font-size":"14px"}},[u("将文件拖至此处，或点击 "),d("a",null,"上传文件")],-1)),a[9]||(a[9]=d("p",{style:{"font-size":"12px",color:"#ccc"},class:"ant-upload-hint"},"支持 .tjs且不超过1000M的文件",-1))])),_:1},8,["fileList"])])),_:1})])),_:1}),o(q,{md:24,sm:24},{default:r((()=>[o(j,{name:"remark",label:"效果包说明","has-feedback":""},{default:r((()=>[o(n(v),{ref_key:"quillEditorRef",ref:Z,modelValue:R.value.remark,"onUpdate:modelValue":a[4]||(a[4]=e=>R.value.remark=e),modelModifiers:{content:!0},style:{height:"180px"},options:B.value,"content-type":"html","onUpdate:content":ae},null,8,["modelValue","options"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"]),i(d("div",L,[o(ue,{type:"circle",percent:Y.value.percent},null,8,["percent"])],512),[[m,Y.value.progressFlag]])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-2c953801"]]);export{U as default};
