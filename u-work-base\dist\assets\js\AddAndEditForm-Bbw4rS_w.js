import{d as e,r as a,a as t,a9 as s,aa as l,n as r,c as i,y as o,ab as n,G as d,b as u,ag as m,J as c,ad as p,F as v,u as f,e as g,am as y,o as T}from"./@vue-HScy-mz9.js";import{u as h}from"./vue-router-BEwRlUkF.js";import{d as j}from"./dayjs-CA7qlNSr.js";import{u as w,p as x,b as _,r as b,t as k,v as C,n as I,o as E,e as A}from"./main-Djn9RDyT.js";import{_ as M}from"./Map.vue_vue_type_style_index_0_lang-CZrcyiOo.js";import{M as q,F as U,_ as N,b as Y,c as H,I as D,w as z,x as P,p as $,z as F,T as L,h as O,E as R,G as S,i as Z,S as G,y as J}from"./ant-design-vue-DYY9BtJq.js";import{_ as V}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const W=["src"],B={key:1},K=V(e({__name:"AddAndEditForm",props:{projectType:{type:Array,required:!0,default:()=>[]}},emits:["ok"],setup(e,{expose:J,emit:V}){const K=w(),Q=V,X=a(new Map),ee=a(new Map),ae=a([]);let te={};const se=a([]),{params:le}=h(),{id:re}=le,ie=a(),oe=()=>{ie.value.init(ye.lnglat,ye.formattedAddress)},ne=e=>{const{country:a,province:t,city:s,district:l,street:r,formattedAddress:i,lnglat:o}=e;Object.assign(ye,{country:a,city:s,district:l,street:r,formattedAddress:i,lnglat:o})},de=a(),ue=async e=>{var a;const{file:t}=e;if(ce.value[0]=t,ce.value[0]){const e=new FormData;e.append("file",ce.value[0]),e.append("bucketName","twinfile");const s=await I(e);200===s.code?(ye.previewId=null==(a=s.data)?void 0:a.id,E(t,(e=>{de.value=e}))):(_("error",s.message),ye.previewId="")}},me=e=>{const a="image/jpeg"===e.type||"image/png"===e.type||"image/jpg"===e.type||"image/gif"===e.type||"image/webp"===e.type||"image/apng"===e.type;a||_("error","支持上传.png, .jpg, .jpeg, .gif, .webp, .apng格式的图片");const t=e.size/1024/1024<5;return t||_("error","图片大小不超过5M"),a&&t},ce=a([]),pe=()=>{ce.value=[],ye.previewId=""},ve=a(!1),fe=a(!1),ge=a(),ye=t({name:"",code:"",id:"",migrationType:"1",userCount:99,sceneCount:null,licenseExpiredTime:j("2099-01-01").format("YYYY-MM-DD HH:mm:ss"),previewId:"",resolution:"",remark:"",place:"",templateTags:[],country:"",province:"",city:"",district:"",street:"",formattedAddress:"",lnglat:"",classified:0,exampleType:"1"}),Te={name:[{required:!0,message:"请输入项目名称！",trigger:"blur"},{max:30,message:"名字长度不能超过30",trigger:"blur"}],code:[{required:!0,message:"请输入项目编码！"},{trigger:"blur",validator:(e,a,t)=>/^[a-zA-Z_][a-zA-Z0-9_]{4,}$/.test(a)?Promise.resolve():a.length<5?Promise.reject(new Error("项目编码长度不能少于五个字符")):/^\d/.test(a)?Promise.reject(new Error("项目编码不能以数字开头")):Promise.reject(new Error("项目编码只能包含字母、数字、下划线"))}],migrationType:[{required:!0,message:"请选择数据初始化类型！"}],exampleType:[{required:!0,message:"请选择项目类型！"}],sceneCount:[{required:!0,message:"请输入场景数量！"}],userCount:[{required:!0,message:"请输入用户数量！"}],licenseExpiredTime:[{required:!0,message:"请选择到期时间!"}],previewId:[{required:!1,message:"请上传封面图!",trigger:"blur"}],templateTags:[{required:!0,message:"请选择标签!"},{required:!0,validator:(e,a,t)=>{let s=0;return a.forEach((e=>{if(1===(null==e?void 0:e.length))if(ae.value.includes(e[0])){const a=se.value.find((a=>a.value===e[0]));a&&a.children&&(s+=a.children.length)}else s++;else s=s+((null==e?void 0:e.length)||0)-1})),s>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}]},he=a(""),je=a(""),we=a("false"),xe=e=>e.previewUrl?`${window.config.previewUrl}${e.previewUrl}`:"",_e=()=>{ge.value.resetFields(),ve.value=!1,de.value="",fe.value=!1},be=e=>{const a=(Number(j(e))-Number(j()))/1e3;return Math.round(a)};return J({init:async(e,a)=>{var t;he.value=e,ve.value=!0,null==(t=ie.value)||t.close(),await new Promise((e=>{C().then((a=>{te={};const t=a.data.map((e=>{var a;return te[e.id]=e.tags,ae.value.push(e.id),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(X.value.set(`${a.tagName}`,a.color),ee.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));se.value=t.filter((e=>e.children)),e(se.value)}))})),await(async()=>{var e,a;try{const t=await A({code:"ZERO_CODE_SWITCH"});we.value=(null==(a=null==(e=t.data)?void 0:e.rows[0])?void 0:a.value)||"false"}catch(t){}})(),r((()=>{if(ge.value.resetFields(),"edit"===e&&a){ye.name=a.name,ye.code=a.code,ye.migrationType=a.migrationType,ye.sceneCount=a.sceneCount,ye.userCount=a.userCount,ye.resolution=a.resolution,ye.exampleType=a.exampleType||"1",ye.id=a.id,ye.remark=a.remark,ye.previewId=a.previewId,ye.place=a.place,ye.templateTags=[],ye.country=a.country,ye.province=a.province,ye.city=a.city,ye.district=a.district,ye.street=a.street,ye.formattedAddress=a.formattedAddress,ye.lnglat=a.lnglat,ye.classified=a.classified;const e=[];a.functionExampleTags.forEach((a=>{ee.value.get(a.tagId)&&e.push([ee.value.get(a.tagId),a.tagId])})),de.value=xe(a),r((()=>{ye.templateTags=e}))}else if("copy"===e){je.value=a.code,ye.name="",ye.code="",ye.migrationType="1",ye.sceneCount=a.sceneCount,ye.userCount=a.userCount,ye.remark=a.remark,ye.previewId=a.previewId,ye.country=a.country,ye.province=a.province,ye.city=a.city,ye.district=a.district,ye.street=a.street,ye.formattedAddress=a.formattedAddress,ye.lnglat=a.lnglat,ye.classified=a.classified,ye.templateTags=[];const e=[];a.functionExampleTags.forEach((a=>{e.push([ee.value.get(a.tagId),a.tagId])})),de.value=xe(a),r((()=>{ye.templateTags=e}))}else"add"===e&&(ye.country="",ye.province="",ye.city="",ye.district="",ye.street="",ye.formattedAddress="",ye.lnglat="")}))}}),(a,t)=>{const r=D,h=H,w=Y,C=P,I=z,E=$,A=L,J=F,V=R,ee=O,ae=y("plus-outlined"),le=S,xe=Z,ke=N,Ce=U,Ie=G,Ee=q;return T(),s(Ee,{width:678,title:"add"===he.value?"新增项目":"edit"===he.value?"编辑项目":"复制项目","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:ve.value,"confirm-loading":fe.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:t[11]||(t[11]=e=>(fe.value=!0,void ge.value.validate().then((()=>{const e=[];if(ye.templateTags.forEach((a=>{if(a[1]){const{id:t,tagName:s,color:l}=te[a[0]].find((e=>e.id===a[1]));e.push({tagId:t,tagName:s,color:l})}else a[0]&&te[a[0]].forEach((a=>{const{id:t,tagName:s,color:l}=a;e.push({tagId:t,tagName:s,color:l})}))})),"add"===he.value){const a={code:ye.code,name:ye.name,exampleType:ye.exampleType,migrationType:"1",sceneCount:ye.sceneCount,userCount:ye.userCount,licenseExpiredTime:ye.licenseExpiredTime,licenseTotalTime:be(ye.licenseExpiredTime),remark:ye.remark,previewId:ye.previewId,resolution:ye.resolution,place:ye.place,enterpriseId:re,functionExampleTags:e,userId:K.userInfo.id,country:ye.country,province:ye.province,city:ye.city,district:ye.district,street:ye.street,formattedAddress:ye.formattedAddress,lnglat:ye.lnglat,classified:ye.classified};"2"===ye.exampleType&&(a.sceneCount=0,a.userCount=0,a.licenseExpiredTime=j("2099-01-01").format("YYYY-MM-DD HH:mm:ss"),a.licenseTotalTime=be(a.licenseExpiredTime)),x(a).then((e=>{fe.value=!1,200===e.code?(_("success","后台新建项目中，这可能需要一段时间，现在您可以进行其他操作，完成后将通知您！"),_e(),Q("ok","add")):_("error",`新建失败：${e.message}`)})).catch((()=>{fe.value=!1})).finally((()=>{fe.value=!1}))}else if("edit"===he.value){const a={code:ye.code,name:ye.name,exampleType:ye.exampleType,migrationType:ye.migrationType,sceneCount:ye.sceneCount,userCount:ye.userCount,licenseExpiredTime:ye.licenseExpiredTime,licenseTotalTime:be(ye.licenseExpiredTime),id:ye.id,remark:ye.remark,previewId:ye.previewId,resolution:ye.resolution,place:ye.place,functionExampleTags:e,enterpriseId:re,userId:K.userInfo.id,country:ye.country,province:ye.province,city:ye.city,district:ye.district,street:ye.street,formattedAddress:ye.formattedAddress,lnglat:ye.lnglat,classified:ye.classified};"2"===ye.exampleType&&(a.sceneCount=0,a.userCount=0,a.licenseExpiredTime=j("2099-01-01").format("YYYY-MM-DD HH:mm:ss"),a.licenseTotalTime=be(a.licenseExpiredTime)),b(a).then((e=>{fe.value=!1,200===e.code?(_("success","项目编辑成功"),_e(),Q("ok")):_("error",e.message)})).catch((()=>{fe.value=!1})).finally((()=>{fe.value=!1}))}else if("copy"===he.value){const a={originCode:je.value,code:ye.code,name:ye.name,exampleType:ye.exampleType,migrationType:ye.migrationType,sceneCount:ye.sceneCount,userCount:ye.userCount,licenseExpiredTime:ye.licenseExpiredTime,licenseTotalTime:be(ye.licenseExpiredTime),id:ye.id,remark:ye.remark,previewId:ye.previewId,country:ye.country,province:ye.province,city:ye.city,district:ye.district,street:ye.street,formattedAddress:ye.formattedAddress,lnglat:ye.lnglat,functionExampleTags:e,enterpriseId:re,userId:K.userInfo.id,classified:ye.classified};k(a).then((e=>{fe.value=!1,200===e.code?(_("success","项目复制成功"),_e(),Q("ok")):_("error",e.message)})).catch((()=>{fe.value=!1})).finally((()=>{fe.value=!1}))}})).catch((e=>{fe.value=!1})))),onCancel:_e},{default:l((()=>[i(Ie,{spinning:fe.value},{default:l((()=>[i(Ce,{ref_key:"formRef",ref:ge,model:ye,rules:Te,"label-align":"left"},{default:l((()=>[i(ke,{gutter:24},{default:l((()=>[i(w,{md:12,sm:24},{default:l((()=>[i(h,{name:"name",label:"项目名称","has-feedback":""},{default:l((()=>[i(r,{value:ye.name,"onUpdate:value":t[0]||(t[0]=e=>ye.name=e),placeholder:"请输入项目名称",maxlength:30},null,8,["value"])])),_:1})])),_:1}),o(i(w,{md:12,sm:24},{default:l((()=>[i(h,{name:"code",label:"项目编码","has-feedback":""},{default:l((()=>[i(r,{value:ye.code,"onUpdate:value":t[1]||(t[1]=e=>ye.code=e),placeholder:"请输入项目编码",maxlength:30},null,8,["value"])])),_:1})])),_:1},512),[[d,"add"===he.value||"copy"===he.value]]),o(i(w,{md:12,sm:24},{default:l((()=>[i(h,{name:"exampleType",label:"项目类型","has-feedback":""},{default:l((()=>[i(I,{value:ye.exampleType,"onUpdate:value":t[2]||(t[2]=e=>ye.exampleType=e),placeholder:"请选择项目类型",style:{width:"100%"},"dropdown-style":{maxHeight:"300px",overflow:"auto"}},{default:l((()=>[(T(!0),u(v,null,m(e.projectType,(e=>(T(),s(C,{key:e.code,value:e.code},{default:l((()=>[c(p(e.value),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},512),[[d,"add"===he.value&&"true"===we.value]]),"1"===ye.exampleType?(T(),s(w,{key:0,md:12,sm:24},{default:l((()=>[i(h,{name:"sceneCount",label:"场景数量","has-feedback":""},{default:l((()=>[i(E,{value:ye.sceneCount,"onUpdate:value":t[3]||(t[3]=e=>ye.sceneCount=e),plcaceholder:"请输入场景数量",style:{width:"100%"},maxlength:10,min:1,max:1e4},null,8,["value"])])),_:1})])),_:1})):n("",!0),"1"===ye.exampleType?(T(),s(w,{key:1,md:12,sm:24},{default:l((()=>[i(h,{name:"userCount",label:"用户数量","has-feedback":""},{default:l((()=>[i(E,{value:ye.userCount,"onUpdate:value":t[4]||(t[4]=e=>ye.userCount=e),plcaceholder:"请输入用户数量",style:{width:"100%"},maxlength:10,min:1,max:1e4},null,8,["value"])])),_:1})])),_:1})):n("",!0),n("",!0),i(w,{md:12,sm:24},{default:l((()=>[i(h,{name:"resolution",label:"项目位置","has-feedback":""},{default:l((()=>[i(r,{value:ye.formattedAddress,"onUpdate:value":t[6]||(t[6]=e=>ye.formattedAddress=e),title:ye.formattedAddress,placeholder:"请输入项目所属地",onClick:oe},null,8,["value","title"])])),_:1})])),_:1}),i(w,{md:24,sm:24},{default:l((()=>[i(h,{name:"templateTags",label:"标签","has-feedback":""},{default:l((()=>[i(J,{value:ye.templateTags,"onUpdate:value":t[7]||(t[7]=e=>ye.templateTags=e),defaultValue:ye.templateTags,"show-checked-strategy":f(F).SHOW_CHILD,style:{width:"100%"},dropdownClassName:"add-project-dropdown",multiple:"","max-tag-count":"responsive",options:se.value,placeholder:"请选择标签",checkStrictly:!0},{tagRender:l((e=>{return[(T(),s(A,{key:e.value,color:(a=e.label,X.value.get(a)||"blue")},{default:l((()=>[c(p(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","options"])])),_:1})])),_:1}),i(w,{md:12,sm:24},{default:l((()=>[i(h,{name:"resolution",label:"是否涉密","has-feedback":""},{default:l((()=>[i(ee,{value:ye.classified,"onUpdate:value":t[8]||(t[8]=e=>ye.classified=e)},{default:l((()=>[i(V,{value:1},{default:l((()=>t[12]||(t[12]=[c("是")]))),_:1}),i(V,{value:0},{default:l((()=>t[13]||(t[13]=[c("否")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1}),i(w,{md:24,sm:24},{default:l((()=>[i(h,{name:"previewId",label:"项目封面图","has-feedback":""},{default:l((()=>[i(le,{fileList:ce.value,"onUpdate:fileList":t[9]||(t[9]=e=>ce.value=e),maxCount:1,"show-upload-list":!1,accept:".png, .jpg, .jpeg, .gif, .webp, .apng",multiple:!1,"custom-request":ue,"list-type":"picture-card","before-upload":me,onRemove:pe},{default:l((()=>[de.value?(T(),u("img",{key:0,src:de.value,alt:"avatar",class:"avatar-img"},null,8,W)):(T(),u("div",B,[i(ae),t[14]||(t[14]=g("div",{class:"ant-upload-text"},"上传",-1))]))])),_:1},8,["fileList"])])),_:1})])),_:1}),i(w,{md:24,sm:24},{default:l((()=>[i(h,{name:"remark",label:"备注"},{default:l((()=>[i(xe,{value:ye.remark,"onUpdate:value":t[10]||(t[10]=e=>ye.remark=e),rows:4,placeholder:"请输入备注"},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"]),i(M,{ref_key:"mapPanel",ref:ie,onGetMapPlace:ne},null,512)])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-3552b4f8"]]);export{K as default};
