import{d as e,r as a,a as t,am as s,a9 as r,o as l,aa as i,c as o,y as d,ab as n,G as u,b as m,F as c,ag as p,J as v,ad as f,u as g,e as y,n as h}from"./@vue-HScy-mz9.js";import{d as T}from"./dayjs-CA7qlNSr.js";import{u as j,a as w,n as x,o as _,b,p as k,r as C,t as I,v as E,e as A}from"./main-Djn9RDyT.js";import{_ as q}from"./Map.vue_vue_type_style_index_0_lang-CZrcyiOo.js";import{S as U,F as N,_ as M,b as z,c as P,I as F,w as H,x as $,p as D,y as L,z as O,T as R,h as S,E as Y,G as Z,i as G,M as J}from"./ant-design-vue-DYY9BtJq.js";import{_ as V}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const W=["src"],B={key:1},K=V(e({__name:"AddAndEditForm",props:{projectType:{type:Array,required:!0,default:()=>[]}},emits:["ok"],setup(e,{expose:L,emit:V}){const K=j(),Q=w(),X=V,ee=a(new Map),ae=a(new Map),te=a([]);let se={};const re=a([]);t(null),t(null);const le=a(),ie=t({name:"",code:"",id:"",migrationType:"1",userCount:99,sceneCount:null,licenseExpiredTime:T("2099-01-01").format("YYYY-MM-DD HH:mm:ss"),previewId:"",remark:"",templateTags:[],country:"",province:"",city:"",district:"",street:"",lnglat:"",formattedAddress:"",classified:0,exampleType:"1"}),oe=()=>{le.value.init(ie.lnglat,ie.formattedAddress)},de=e=>{const{country:a,province:t,city:s,district:r,street:l,formattedAddress:i,lnglat:o}=e;Object.assign(ie,{country:a,city:s,district:r,street:l,formattedAddress:i,lnglat:o})},ne=a(),ue=async e=>{var a;const{file:t}=e;if(ce.value[0]=t,ce.value[0]){const e=new FormData;e.append("file",ce.value[0]),e.append("bucketName","twinfile");const s=await x(e);200===s.code?(ie.previewId=null==(a=s.data)?void 0:a.id,_(t,(e=>{ne.value=e}))):(b("error",s.message),ie.previewId="")}},me=e=>{const a="image/jpeg"===e.type||"image/png"===e.type||"image/jpg"===e.type||"image/gif"===e.type||"image/webp"===e.type||"image/apng"===e.type;a||b("error","支持上传.png, .jpg, .jpeg, .gif, .webp, .apng格式的图片");const t=e.size/1024/1024<5;return t||b("error","图片大小不超过5M"),a&&t},ce=a([]),pe=()=>{ce.value=[],ie.previewId=""},ve=a(!1),fe=a(!1),ge=a(),ye={name:[{required:!0,message:"请输入项目名称！",trigger:"blur"},{max:30,message:"名字长度不能超过30",trigger:"blur"}],code:[{required:!0,message:"请输入项目编码！"},{trigger:"blur",validator:(e,a,t)=>/^[a-zA-Z_][a-zA-Z0-9_]{4,}$/.test(a)?Promise.resolve():a.length<5?Promise.reject(new Error("项目编码长度不能少于五个字符")):/^\d/.test(a)?Promise.reject(new Error("项目编码不能以数字开头")):Promise.reject(new Error("项目编码只能包含字母、数字、下划线"))}],migrationType:[{required:!0,message:"请选择数据初始化类型！"}],exampleType:[{required:!0,message:"请选择项目类型！"}],sceneCount:[{required:!0,message:"请输入场景数量！"}],userCount:[{required:!0,message:"请输入用户数量！"}],licenseExpiredTime:[{required:!0,message:"请选择到期时间!"}],previewId:[{required:!1,message:"请上传封面图!",trigger:"blur"}],templateTags:[{required:!0,message:"请选择标签!"},{required:!0,validator:(e,a,t)=>{let s=0;return a.forEach((e=>{if(1===e.length)if(te.value.includes(e[0])){const a=re.value.find((a=>a.value===e[0]));a&&a.children&&(s+=a.children.length)}else s++;else s=s+e.length-1})),s>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}]},he=a(""),Te=a(""),je=a("false"),we=e=>e.previewUrl?`${window.config.previewUrl}${e.previewUrl}`:"",xe=()=>{fe.value||(ge.value.resetFields(),ve.value=!1,ne.value="",fe.value=!1)},_e=e=>{const a=(Number(T(e))-Number(T()))/1e3;return Math.round(a)};return L({init:async(e,a)=>{var t;ve.value=!0,he.value=e,null==(t=le.value)||t.close(),await new Promise((e=>{E().then((a=>{se={};const t=a.data.map((e=>{var a;return se[e.id]=e.tags,te.value.push(e.id),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(ee.value.set(`${a.tagName}`,a.color),ae.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));re.value=t.filter((e=>e.children)),e(re.value)}))})),await(async()=>{var e,a;try{const t=await A({code:"ZERO_CODE_SWITCH"});je.value=(null==(a=null==(e=t.data)?void 0:e.rows[0])?void 0:a.value)||"false"}catch(t){}})(),h((()=>{if(ge.value.resetFields(),"edit"===e&&a){ie.name=a.name,ie.code=a.code,ie.migrationType=a.migrationType,ie.sceneCount=a.sceneCount,ie.userCount=a.userCount,ie.exampleType=a.exampleType||"1",ie.id=a.id,ie.remark=a.remark,ie.previewId=a.previewId,ie.country=a.country,ie.province=a.province,ie.city=a.city,ie.district=a.district,ie.street=a.street,ie.formattedAddress=a.formattedAddress,ie.lnglat=a.lnglat,ie.classified=a.classified,ie.templateTags=[];const e=[];a.functionExampleTags.forEach((a=>{ae.value.get(a.tagId)&&e.push([ae.value.get(a.tagId),a.tagId])})),ne.value=we(a),h((()=>{ie.templateTags=e}))}else if("copy"===e){Te.value=a.code,ie.name="",ie.code="",ie.migrationType="1",ie.sceneCount=a.sceneCount,ie.userCount=a.userCount,ie.remark=a.remark,ie.previewId=a.previewId,ie.country=a.country,ie.province=a.province,ie.city=a.city,ie.district=a.district,ie.street=a.street,ie.formattedAddress=a.formattedAddress,ie.lnglat=a.lnglat,ie.classified=a.classified,ie.templateTags=[];const e=[];a.functionExampleTags.forEach((a=>{e.push([ae.value.get(a.tagId),a.tagId])})),ne.value=we(a),h((()=>{ie.templateTags=e}))}else"add"===e&&(ie.country="",ie.province="",ie.city="",ie.district="",ie.street="",ie.formattedAddress="",ie.lnglat="")}))}}),(a,t)=>{const h=F,T=P,j=z,w=$,x=H,_=D,E=R,A=O,L=Y,V=S,ae=s("plus-outlined"),te=Z,we=G,be=M,ke=N,Ce=U,Ie=J;return l(),r(Ie,{width:678,title:"add"===he.value?"新增项目":"copy"===he.value?"复制项目":"编辑项目","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:ve.value,"confirm-loading":fe.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:t[11]||(t[11]=e=>(fe.value=!0,void ge.value.validate().then((()=>{const e=[];if(ie.templateTags.forEach((a=>{if(a[1]){const{id:t,tagName:s,color:r}=se[a[0]].find((e=>e.id===a[1]));e.push({tagId:t,tagName:s,color:r})}else a[0]&&se[a[0]].forEach((a=>{const{id:t,tagName:s,color:r}=a;e.push({tagId:t,tagName:s,color:r})}))})),"add"===he.value){const a={code:ie.code,name:ie.name,migrationType:ie.migrationType,sceneCount:ie.sceneCount,userCount:ie.userCount,exampleType:ie.exampleType,licenseExpiredTime:ie.licenseExpiredTime,licenseTotalTime:_e(ie.licenseExpiredTime),remark:ie.remark,previewId:ie.previewId,country:ie.country,province:ie.province,city:ie.city,district:ie.district,street:ie.street,formattedAddress:ie.formattedAddress,lnglat:ie.lnglat,enterpriseId:Q.checkedEnterprise.id,functionExampleTags:e,userId:K.userInfo.id,classified:ie.classified};"2"===ie.exampleType&&(a.sceneCount=0,a.userCount=0),k(a).then((e=>{fe.value=!1,200===e.code?(b("success","后台新建项目中，这可能需要一段时间，现在您可以进行其他操作，完成后将通知您！"),xe(),X("ok","add")):b("error",e.message)})).catch((()=>{fe.value=!1})).finally((()=>{fe.value=!1}))}else if("edit"===he.value){const a={code:ie.code,name:ie.name,exampleType:ie.exampleType,migrationType:ie.migrationType,sceneCount:ie.sceneCount,userCount:ie.userCount,licenseExpiredTime:ie.licenseExpiredTime,licenseTotalTime:_e(ie.licenseExpiredTime),id:ie.id,remark:ie.remark,previewId:ie.previewId,country:ie.country,province:ie.province,city:ie.city,district:ie.district,street:ie.street,formattedAddress:ie.formattedAddress,lnglat:ie.lnglat,functionExampleTags:e,enterpriseId:Q.checkedEnterprise.id,userId:K.userInfo.id,classified:ie.classified};"2"===ie.exampleType&&(a.sceneCount=0,a.userCount=0),C(a).then((e=>{fe.value=!1,200===e.code?(b("success","项目编辑成功"),xe(),X("ok")):b("error",e.message)})).catch((()=>{fe.value=!1})).finally((()=>{fe.value=!1}))}else if("copy"===he.value){const a={originCode:Te.value,code:ie.code,name:ie.name,migrationType:ie.migrationType,exampleType:ie.exampleType,sceneCount:ie.sceneCount,userCount:ie.userCount,licenseExpiredTime:ie.licenseExpiredTime,licenseTotalTime:_e(ie.licenseExpiredTime),id:ie.id,remark:ie.remark,previewId:ie.previewId,country:ie.country,province:ie.province,city:ie.city,district:ie.district,street:ie.street,formattedAddress:ie.formattedAddress,lnglat:ie.lnglat,functionExampleTags:e,enterpriseId:Q.checkedEnterprise.id,userId:K.userInfo.id,classified:ie.classified};I(a).then((e=>{fe.value=!1,200===e.code?(b("success","项目复制成功"),xe(),X("ok")):b("error",e.message)})).catch((()=>{fe.value=!1})).finally((()=>{fe.value=!1}))}})).catch((e=>{fe.value=!1})))),onCancel:xe},{default:i((()=>[o(Ce,{spinning:fe.value},{default:i((()=>[o(ke,{ref_key:"formRef",ref:ge,model:ie,rules:ye,"label-align":"left"},{default:i((()=>[o(be,{gutter:24},{default:i((()=>[o(j,{md:12,sm:24},{default:i((()=>[o(T,{name:"name",label:"项目名称","has-feedback":""},{default:i((()=>[o(h,{value:ie.name,"onUpdate:value":t[0]||(t[0]=e=>ie.name=e),placeholder:"请输入项目名称",maxlength:30},null,8,["value"])])),_:1})])),_:1}),d(o(j,{md:12,sm:24},{default:i((()=>[o(T,{name:"code",label:"项目编码","has-feedback":""},{default:i((()=>[o(h,{value:ie.code,"onUpdate:value":t[1]||(t[1]=e=>ie.code=e),placeholder:"请输入项目编码",maxlength:30},null,8,["value"])])),_:1})])),_:1},512),[[u,"add"===he.value||"copy"===he.value]]),d(o(j,{md:12,sm:24},{default:i((()=>[o(T,{name:"exampleType",label:"项目类型","has-feedback":""},{default:i((()=>[o(x,{value:ie.exampleType,"onUpdate:value":t[2]||(t[2]=e=>ie.exampleType=e),placeholder:"请选择项目类型",style:{width:"100%"},"dropdown-style":{maxHeight:"300px",overflow:"auto"}},{default:i((()=>[(l(!0),m(c,null,p(e.projectType,(e=>(l(),r(w,{key:e.code,value:e.code},{default:i((()=>[v(f(e.value),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},512),[[u,"add"===he.value&&"true"===je.value]]),"1"===ie.exampleType?(l(),r(j,{key:0,md:12,sm:24},{default:i((()=>[o(T,{name:"sceneCount",label:"场景数量","has-feedback":""},{default:i((()=>[o(_,{value:ie.sceneCount,"onUpdate:value":t[3]||(t[3]=e=>ie.sceneCount=e),style:{width:"100%"},placeholder:"请输入场景数量",maxlength:10,min:1,max:1e4},null,8,["value"])])),_:1})])),_:1})):n("",!0),"1"===ie.exampleType?(l(),r(j,{key:1,md:12,sm:24},{default:i((()=>[o(T,{name:"userCount",label:"用户数量","has-feedback":""},{default:i((()=>[o(_,{value:ie.userCount,"onUpdate:value":t[4]||(t[4]=e=>ie.userCount=e),style:{width:"100%"},maxlength:10,min:1,max:1e4},null,8,["value"])])),_:1})])),_:1})):n("",!0),n("",!0),o(j,{md:12,sm:24},{default:i((()=>[o(T,{name:"place",label:"项目位置","has-feedback":""},{default:i((()=>[o(h,{value:ie.formattedAddress,"onUpdate:value":t[6]||(t[6]=e=>ie.formattedAddress=e),placeholder:"请输入项目位置",title:ie.formattedAddress,onFocus:oe},null,8,["value","title"])])),_:1})])),_:1}),o(j,{md:24,sm:24},{default:i((()=>[o(T,{name:"templateTags",label:"标签","has-feedback":""},{default:i((()=>[o(A,{value:ie.templateTags,"onUpdate:value":t[7]||(t[7]=e=>ie.templateTags=e),defaultValue:ie.templateTags,"show-checked-strategy":g(O).SHOW_CHILD,style:{width:"100%"},multiple:"",dropdownClassName:"add-project-dropdown","max-tag-count":"responsive",options:re.value,placeholder:"请选择标签",checkStrictly:!0},{tagRender:i((e=>{return[(l(),r(E,{key:e.value,color:(a=e.label,ee.value.get(a)||"blue")},{default:i((()=>[v(f(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","options"])])),_:1})])),_:1}),o(j,{md:12,sm:24},{default:i((()=>[o(T,{name:"resolution",label:"是否涉密","has-feedback":""},{default:i((()=>[o(V,{value:ie.classified,"onUpdate:value":t[8]||(t[8]=e=>ie.classified=e)},{default:i((()=>[o(L,{value:1},{default:i((()=>t[12]||(t[12]=[v("是")]))),_:1}),o(L,{value:0},{default:i((()=>t[13]||(t[13]=[v("否")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1}),o(j,{md:24,sm:24},{default:i((()=>[o(T,{name:"previewId",label:"项目封面图","has-feedback":""},{default:i((()=>[o(te,{fileList:ce.value,"onUpdate:fileList":t[9]||(t[9]=e=>ce.value=e),maxCount:1,"show-upload-list":!1,accept:".png, .jpg, .jpeg, .gif, .webp, .apng",multiple:!1,"custom-request":ue,"list-type":"picture-card","before-upload":me,onRemove:pe},{default:i((()=>[ne.value?(l(),m("img",{key:0,src:ne.value,alt:"avatar",class:"avatar-img"},null,8,W)):(l(),m("div",B,[o(ae),t[14]||(t[14]=y("div",{class:"ant-upload-text"},"上传",-1))]))])),_:1},8,["fileList"])])),_:1})])),_:1}),o(j,{md:24,sm:24},{default:i((()=>[o(T,{name:"remark",label:"备注"},{default:i((()=>[o(we,{value:ie.remark,"onUpdate:value":t[10]||(t[10]=e=>ie.remark=e),rows:4,placeholder:"请输入备注"},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"]),o(q,{ref_key:"mapPanel",ref:le,onGetMapPlace:de},null,512)])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-82af2e82"]]);export{K as default};
