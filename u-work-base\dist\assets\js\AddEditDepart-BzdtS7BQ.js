import{f as e,b as a,h as s,i as t}from"./main-Djn9RDyT.js";import{n as r,b as l,e as o}from"./department-CTxHSeTj.js";import{S as i,F as d,_ as m,b as n,c as p,I as u,o as c,p as v,i as j,M as f}from"./ant-design-vue-DYY9BtJq.js";import{d as h,r as g,a as b,a9 as k,o as _,aa as y,c as x,n as I}from"./@vue-HScy-mz9.js";import{_ as w}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const S=w(h({__name:"AddEditDepart",emits:["ok"],setup(h,{expose:w,emit:S}){const q=S,z=g(!1),U=g(!1),A=g(),F=b({id:"",name:"",code:"",pid:"",sort:100,enterpriseId:"",remark:"",type:"",selectSource:""}),D={children:"children",label:"name",key:"id",value:"id"},E={name:[{required:!0,message:"请输入部门名称！",trigger:"blur"},{max:30,message:"名字长度不能超过30",trigger:"blur"}],code:[{required:!0,validator:e}],pid:[{required:!0,message:"请选择上级部门！"}],sort:[{required:!0,message:"请输入序号"}],remark:[{max:80,message:"备注长度不超过80！"}]},H=g(""),K=()=>{U.value||(A.value.resetFields(),z.value=!1,U.value=!1)},M=g([]),O=()=>{r({enterpriseId:F.enterpriseId}).then((e=>{e.success||(M.value=[]),M.value=[e.data]})).catch((()=>{U.value=!1}))},B=(e,a)=>{F.selectSource=a.source,F.pid=a.id,F.enterpriseId=a.enterpriseId},C=()=>{if(F.name=s(F.name),"edit"===H.value)return;const e=t(F.name);if("_"===e.charAt(e.length-1)){const a=e.slice(0,e.length-1);F.code=a.length>50?a.substr(0,50):a}else F.code=e.length>50?e.substr(0,50):e};return w({init:(e,a,s,t,r)=>{z.value=!0,H.value=e,I((()=>{A.value.resetFields(),F.enterpriseId=t,F.pid="dept"===r?s:"team"===r?t:"",F.selectSource=r,"edit"===e&&a&&(F.code=a.code,F.id=a.id,F.name=a.name,"0"===a.pid?"1"===a.enterpriseId?F.pid="1":F.pid=a.enterpriseId:F.pid=a.pid,F.sort=a.sort,F.remark=a.remark),O()}))}}),(e,s)=>{const t=u,r=p,h=n,g=c,b=v,I=j,w=m,S=d,O=i,J=f;return _(),k(J,{width:676,title:"add"===H.value?"新增部门":"编辑部门","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:z.value,"confirm-loading":U.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:s[5]||(s[5]=e=>(U.value=!0,void A.value.validate().then((()=>{if("add"===H.value){const e={code:F.code,name:F.name,pid:"dept"!==F.selectSource?"0":F.pid,sort:F.sort,remark:F.remark,enterpriseId:F.enterpriseId};l(e).then((e=>{U.value=!1,200===e.code?(a("success","部门新增成功"),K(),q("ok")):a("error",e.message)})).catch((()=>{U.value=!1})).finally((()=>{U.value=!1}))}else{const e={code:F.code,name:F.name,pid:"1"===F.pid?"0":F.pid,sort:F.sort,remark:F.remark,id:F.id,enterpriseId:F.enterpriseId};o(e).then((e=>{U.value=!1,200===e.code?(a("success","部门编辑成功"),K(),q("ok")):a("error",e.message)})).catch((()=>{U.value=!1})).finally((()=>{U.value=!1}))}})).catch((e=>{U.value=!1})))),onCancel:K},{default:y((()=>[x(O,{spinning:U.value},{default:y((()=>[x(S,{ref_key:"formRef",ref:A,model:F,rules:E,"label-align":"left"},{default:y((()=>[x(w,{gutter:24},{default:y((()=>[x(h,{md:12,sm:24},{default:y((()=>[x(r,{name:"name",label:"部门名称","has-feedback":""},{default:y((()=>[x(t,{value:F.name,"onUpdate:value":s[0]||(s[0]=e=>F.name=e),placeholder:"请输入部门名称",maxlength:30,onKeyup:C},null,8,["value"])])),_:1})])),_:1}),x(h,{md:12,sm:24},{default:y((()=>[x(r,{name:"code",label:"唯一编码","has-feedback":""},{default:y((()=>[x(t,{value:F.code,"onUpdate:value":s[1]||(s[1]=e=>F.code=e),placeholder:"请输入唯一编码",maxlength:50},null,8,["value"])])),_:1})])),_:1}),x(h,{md:12,sm:24},{default:y((()=>[x(r,{name:"pid",label:"上级部门","has-feedback":""},{default:y((()=>[x(g,{value:F.pid,"onUpdate:value":s[2]||(s[2]=e=>F.pid=e),"field-names":D,style:{width:"100%"},"dropdown-style":{maxHeight:"300px",overflow:"auto"},"tree-data":M.value,placeholder:"请选择上级部门","tree-default-expand-all":"",onSelect:B},null,8,["value","tree-data"])])),_:1})])),_:1}),x(h,{md:12,sm:24},{default:y((()=>[x(r,{name:"sort",label:"排序","has-feedback":""},{default:y((()=>[x(b,{value:F.sort,"onUpdate:value":s[3]||(s[3]=e=>F.sort=e),style:{width:"100%"},placeholder:"请输入排序",maxlength:10,min:1,max:1e3},null,8,["value"])])),_:1})])),_:1}),x(h,{md:24,sm:24},{default:y((()=>[x(r,{name:"remark",label:"备注"},{default:y((()=>[x(I,{value:F.remark,"onUpdate:value":s[4]||(s[4]=e=>F.remark=e),rows:4,placeholder:"请输入备注",maxlength:80},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-ca4b56a0"]]);export{S as default};
