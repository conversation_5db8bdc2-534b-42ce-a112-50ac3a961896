import{f as e,l as a,b as l,h as s,i as t,c as o,ah as d,ai as u}from"./main-Djn9RDyT.js";import{i as r,s as i}from"./dictionaryManage-gGpiMShb.js";import{S as n,F as m,_ as c,b as p,c as v,I as g,h as f,E as y,d as _,w as h,x as j,p as b,i as T,M as k}from"./ant-design-vue-DYY9BtJq.js";import{Q as x}from"./@ant-design-CA72ad83.js";import{d as w,r as F,a as C,a9 as I,o as O,aa as U,c as q,e as N,J as A,u as G,b as S,F as z,ag as E,ad as L,n as X}from"./@vue-HScy-mz9.js";import{_ as Y}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const B={class:"icon_open"},D={class:"icon_open"},M=Y(w({__name:"AddEditForm",emits:["ok"],setup(w,{expose:Y,emit:M}){const J=M,K=F(""),W=F(!1),$=F([]),H=async()=>{try{const e=await a({code:"system_constant"});200===e.code&&($.value=e.data)}catch(e){}},Q=F([]),R=()=>{Q.value=[],r().then((e=>{200===e.code&&(Q.value=e.data,Q.value.unshift({code:"",name:"无"}),ae.diceType&&""!==ae.diceType?Z():ae.diceType=Q.value[0].code)})).catch((()=>{Q.value=[{code:"",name:"无"}],ae.diceType&&""!==ae.diceType||(ae.diceType=Q.value[0].code)}))},Z=(e,a,l=!0)=>{P.value=[],l||(ae.value=""),ae.diceType&&V(ae.diceType)},P=F([]),V=e=>{i({code:e}).then((e=>{const{code:a,data:s,message:t}=e;200===a?0===s.length?(ae.diceType=Q.value[0].code,l("warning","该字典未设置字典值")):P.value=s:(ae.diceType=Q.value[0].code,l("error",t))})).catch((e=>{ae.diceType=Q.value[0].code}))};Y({init:(e,a)=>{te.value=!0,K.value=e,X((()=>{H(),R(),ee.value.resetFields(),"edit"===e&&a&&(ae.id=a.id,ae.name=a.name,ae.code=a.code,ae.sysFlag=a.sysFlag,ae.groupCode=a.groupCode,ae.value=a.value,ae.remark=a.remark,ae.diceType=a.diceType?a.diceType:"",ae.sort=a.sort?a.sort:100,"Y"===a.sysFlag&&(W.value=!0))}))}});const ee=F(),ae=C({id:"",name:"",code:"",sysFlag:null,groupCode:null,value:"",remark:"",diceType:"",sort:100}),le={name:[{required:!0,message:"请输入参数名称！",trigger:"blur"},{max:30,message:"名字长度不能超过30",trigger:"blur"}],code:[{required:!0,validator:e}],sysFlag:[{required:!0,message:"请选择系统参数！"}],groupCode:[{required:!0,message:"请选择所属分类"}],sort:[{required:!0,message:"请输入序号"}]},se=()=>{if(ae.name=s(ae.name),"edit"===K.value)return;const e=t(ae.name);if("_"===e.charAt(e.length-1)){const a=e.slice(0,e.length-1);ae.code=a.length>50?a.substring(0,50):a}else ae.code=e.length>50?e.substring(0,50):e},te=F(!1),oe=F(!1),de=()=>{ee.value.resetFields(),te.value=!1,W.value=!1,oe.value=!1};return(e,a)=>{const s=g,t=v,r=p,i=y,w=f,F=_,C=j,X=h,Y=b,M=T,W=c,H=m,R=n,V=k;return O(),I(V,{width:676,title:"add"===K.value?"新增配置":"编辑配置","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:te.value,"confirm-loading":oe.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[9]||(a[9]=e=>(oe.value=!0,void ee.value.validate().then((async()=>{let e;const a={};"add"===K.value?(e=d,Object.assign(a,{name:ae.name,code:ae.code,sysFlag:ae.sysFlag,groupCode:ae.groupCode,value:ae.value,remark:ae.remark,diceType:ae.diceType,sort:ae.sort})):(e=u,Object.assign(a,{...ae}));try{if(200===(await e(a)).code){if(l("success",("add"===K.value?"开发配置新增":"开发配置编辑")+"成功"),"XI_TONG_BIAO_TI"===ae.code&&(sessionStorage.setItem("XI_TONG_BIAO_TI",ae.value),document.title=`${ae.value}`,o().setUpdateSetting(1)),"DENG_LU_YE_LOGO"===ae.code){sessionStorage.setItem("XI_TONG_LOGO",ae.value);let e=document.querySelector('link[rel="icon"]');e&&(e.href=ae.value),o().setUpdateSetting(1)}ee.value.resetFields(),te.value=!1,J("ok")}}catch(s){}finally{oe.value=!1}})).catch((e=>{oe.value=!1})))),onCancel:de},{default:U((()=>[q(R,{spinning:oe.value},{default:U((()=>[q(H,{ref_key:"formRef",ref:ee,model:ae,rules:le,"label-align":"left"},{default:U((()=>[q(W,{gutter:24},{default:U((()=>[q(r,{md:12,sm:24},{default:U((()=>[q(t,{name:"name",label:"参数名称","has-feedback":""},{default:U((()=>[q(s,{value:ae.name,"onUpdate:value":a[0]||(a[0]=e=>ae.name=e),placeholder:"请输入参数名称",maxlength:30,onKeyup:se},null,8,["value"])])),_:1})])),_:1}),q(r,{md:12,sm:24},{default:U((()=>[q(t,{name:"code",label:"唯一编码","has-feedback":""},{default:U((()=>[q(s,{value:ae.code,"onUpdate:value":a[1]||(a[1]=e=>ae.code=e),placeholder:"请输入唯一编码称",maxlength:50,disabled:"edit"===K.value},null,8,["value","disabled"])])),_:1})])),_:1}),q(r,{md:12,sm:24},{default:U((()=>[q(t,{name:"sysFlag",class:"sys-flag",label:"系统参数","has-feedback":""},{default:U((()=>[q(w,{value:ae.sysFlag,"onUpdate:value":a[2]||(a[2]=e=>ae.sysFlag=e),placeholder:"请选择系统参数",disabled:"edit"===K.value&&"Y"===ae.sysFlag},{default:U((()=>[q(i,{value:"Y"},{default:U((()=>a[10]||(a[10]=[A("是")]))),_:1}),q(i,{value:"N"},{default:U((()=>a[11]||(a[11]=[A("否")]))),_:1})])),_:1},8,["value","disabled"]),N("span",B,[q(F,{title:"选择“系统参数”后，此配置无法删除",placement:"right"},{default:U((()=>[q(G(x),{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1}),q(r,{md:12,sm:24},{default:U((()=>[q(t,{name:"groupCode",class:"group-code",label:"所属分类","has-feedback":""},{default:U((()=>[q(X,{value:ae.groupCode,"onUpdate:value":a[3]||(a[3]=e=>ae.groupCode=e),"tree-data":$.value,placeholder:"请选择所属分类","tree-default-expand-all":""},{default:U((()=>[(O(!0),S(z,null,E($.value,(e=>(O(),I(C,{key:e.value,value:e.code},{default:U((()=>[A(L(e.value),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value","tree-data"]),N("span",D,[q(F,{placement:"bottom","auto-adjust-overflow":!0,"overlay-style":{maxWidth:"800px"}},{title:U((()=>a[12]||(a[12]=[N("div",{class:"custom-tooltip"},[N("p",null,"所属分类的数据源在【系统管理】>【系统管理】>【字典管理】页面下的”系统配置分类“中维护"),N("img",{src:"/assets/png/belongType-ITAXxDW6.png",class:"img",alt:"",width:"100%"})],-1)]))),default:U((()=>[q(G(x),{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1}),q(r,{md:12,sm:24},{default:U((()=>[q(t,{name:"sort",label:"排序","has-feedback":""},{default:U((()=>[q(Y,{value:ae.sort,"onUpdate:value":a[4]||(a[4]=e=>ae.sort=e),style:{width:"100%"},placeholder:"请输入排序",maxlength:10,min:1,max:1e3},null,8,["value"])])),_:1})])),_:1}),q(r,{md:12,sm:24},{default:U((()=>[q(t,{name:"diceType",class:"group-cs",label:"参数值设置字典","has-feedback":""},{default:U((()=>[q(X,{value:ae.diceType,"onUpdate:value":a[5]||(a[5]=e=>ae.diceType=e),"tree-data":Q.value,placeholder:"请选择参数值所属字典","tree-default-expand-all":"",onChange:Z},{default:U((()=>[(O(!0),S(z,null,E(Q.value,(e=>(O(),I(C,{key:e.code,value:e.code},{default:U((()=>[A(L(e.name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value","tree-data"])])),_:1})])),_:1}),q(r,{md:24,sm:24},{default:U((()=>[q(t,{name:"value",label:"参数值","has-feedback":""},{default:U((()=>[ae.diceType&&""!=ae.diceType?(O(),I(X,{key:0,allowClear:!0,value:ae.value,"onUpdate:value":a[6]||(a[6]=e=>ae.value=e),"tree-data":P.value,placeholder:"请选择参数值","tree-default-expand-all":""},{default:U((()=>[(O(!0),S(z,null,E(P.value,(e=>(O(),I(C,{key:e.code,value:e.code},{default:U((()=>[A(L(e.value),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value","tree-data"])):(O(),I(s,{key:1,allowClear:!0,value:ae.value,"onUpdate:value":a[7]||(a[7]=e=>ae.value=e),style:{width:"100%"},placeholder:"请输入参数值"},null,8,["value"]))])),_:1})])),_:1}),q(r,{md:24,sm:24},{default:U((()=>[q(t,{name:"remark",label:"备注"},{default:U((()=>[q(M,{value:ae.remark,"onUpdate:value":a[8]||(a[8]=e=>ae.remark=e),rows:4,placeholder:"请输入备注",maxlength:80},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-64554632"]]);export{M as default};
