import{M as e,aa as a,b as l,ab as t,ac as s,ad as i}from"./main-Djn9RDyT.js";import o from"./IconManage-BfCKI8e5.js";import{F as r,_ as n,b as d,c as m,I as p,w as u,x as c,h as f,R as v,o as h,d as y,p as k,Y as b,M as _}from"./ant-design-vue-DYY9BtJq.js";import{T as g,af as j,Q as T}from"./@ant-design-CA72ad83.js";import{d as w,r as x,a as I,p as U,a9 as A,o as q,aa as C,c as F,ab as M,b as z,F as R,ag as Y,J as E,ad as P,e as B,u as J,n as L}from"./@vue-HScy-mz9.js";import{_ as N}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const O={class:"icon-btns"},D={class:"form-item-notice"},H={class:"form-item-notice"},K=N(w({__name:"AddEditForm",emits:["ok"],setup(w,{expose:N,emit:K}){const Q=K,Z={children:"children",title:"title",key:"id",value:"id"},$=x([{id:"-1",parentId:"0",title:"顶级",value:"0",pid:"0",children:[]}]),G=x([]),S=(e,t,s)=>{a({application:e}).then((e=>{e.success?(ee.pid=s||"0",$.value=[{id:"-1",parentId:"0",title:"顶级",value:"0",pid:"0",children:e.data}]):l("warning",e.message)}))},V=x(!1),W=x(!1),X=x(),ee=I({id:"",name:"",code:"",application:null,type:0,pid:"0",redirect:"",component:"",router:"",permission:"",iframeType:"0",sort:100,icon:"",remark:"",visible:!0,microAppId:"",twinId:[],openType:1,link:"",sysCategoryId:null,withToken:"0"}),ae=x({icon:[{required:!0,message:"请选择图标！",trigger:"blur"}],link:[{required:U((()=>2===ee.openType)),message:"请输入内外链地址！"},{validator:e}],name:[{required:!0,min:1,message:"请输入菜单名称！"},{max:30,message:"菜单名称长度不超过30！"}],code:[{required:!0,min:1,message:"请输入菜单编号！"},{min:1,max:50,message:"菜单编号长度不超过50！"},{validator:(e,a)=>{if(!a)return Promise.resolve();return/\s/.test(a)?Promise.reject(new Error("不能输入空格!")):Promise.resolve()}}],pid:[{required:U((()=>0!==ee.type)),message:"请选择父级菜单！"}],microAppId:[{required:U((()=>3===ee.openType)),message:"请选择子应用！",trigger:"change"}],component:[{required:U((()=>0===ee.type||1===ee.type)),message:"请输入前端组件"},{max:255,message:"前端组件长度不超过255！"}],router:[{required:U((()=>0===ee.type||1===ee.type)),message:"请输入路由！"},{max:255,message:"路由地址长度不超过255！"}],permission:[{max:80,message:"权限标识长度不超过80！"},{required:U((()=>2===ee.type)),message:"请输入权限标识！"}],type:[{required:!0,message:"请选择菜单层级！"}],application:[{required:!0,message:"请选择功能模块分类！"}]}),le=x(""),te=x(!1),se=()=>{switch(ee.openType=1,ee.application||(ee.pid=null),ee.type){case 0:ee.pid="0",ee.permission="",ee.microAppId="",ee.iframeType="0",ee.withToken="0",ee.link="";break;case 1:ee.redirect="",ee.permission="",ee.microAppId="",ee.iframeType="0",ee.withToken="0",ee.link="";break;case 2:ee.redirect="",ee.component="/",ee.router="/",ee.microAppId="",ee.withToken="0",ee.iframeType="0",ee.link=""}},ie=()=>{ee.link="",ee.iframeType="0",ee.withToken="0",ee.router="",ee.microAppId="",1===ee.openType?ee.component="":2===ee.openType?ee.component="Iframe":3===ee.openType&&(ee.component="Micro",ee.router="/")},oe=()=>{W.value||(X.value.resetFields(),ee.application="",ee.icon="",ee.sysCategoryId="",ee.permission="",ee.pid="0",ee.microAppId="",ee.withToken="0",ee.type=0,ee.openType=1,ee.link="",ee.iframeType="0",te.value=!1,V.value=!1,W.value=!1)},re=x([]),ne=()=>{i().then((e=>{e.success?re.value=e.data:l("error",e.message)}))},de=x(),me=e=>{X.value.resetFields("icon"),ee.icon=e};return N({init:(e,a)=>{V.value=!0,le.value=e,L((()=>{X.value.resetFields(),"edit"===e&&a&&(ee.code=a.code,ee.id=a.id,ee.withToken=a.withToken||"0",ee.name=a.name,ee.application=a.application,S(a.application,0,a.pid),ee.type=a.type,ee.pid=a.pid,ee.redirect=a.redirect,ee.component=a.component,ee.router=a.router,ee.permission=a.permission,ee.openType=a.openType,ee.iframeType=a.iframeType||"0",ee.link=a.link,ee.sort=a.sort,ee.remark=a.remark,ee.visible="Y"===a.visible,ee.microAppId=a.microAppId,ee.icon=a.icon||"icon-system",3===ee.openType&&(te.value=!0)),ne()}))}}),(e,a)=>{const i=p,w=m,x=d,I=c,U=u,L=v,N=f,K=h,ne=y,pe=n,ue=k,ce=b,fe=r,ve=_;return q(),A(ve,{width:800,title:"add"===le.value?"新增菜单":"编辑菜单","wrap-class-name":"cus-modal",open:V.value,"confirm-loading":W.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[20]||(a[20]=e=>(W.value=!0,void X.value.validate().then((()=>{const e={...ee,visible:ee.visible?"Y":"N",weight:"1"};"add"===le.value?(delete e.id,t(e).then((e=>{W.value=!1,200===e.code?(l("success","菜单新增成功"),oe(),Q("ok")):l("error",e.message)})).catch((()=>{W.value=!1})).finally((()=>{W.value=!1}))):s(e).then((e=>{W.value=!1,200===e.code?(l("success","菜单编辑成功"),oe(),Q("ok")):l("error",e.message)})).catch((()=>{W.value=!1})).finally((()=>{W.value=!1}))})).catch((e=>{W.value=!1})))),onCancel:oe},{default:C((()=>[F(fe,{ref_key:"formRef",ref:X,model:ee,rules:ae.value,"label-align":"left"},{default:C((()=>[F(pe,{gutter:24,class:"form-top"},{default:C((()=>[F(x,{md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"name",label:"菜单名称","has-feedback":""},{default:C((()=>[F(i,{value:ee.name,"onUpdate:value":a[0]||(a[0]=e=>ee.name=e),placeholder:"请输入菜单名称",maxlength:30},null,8,["value"])])),_:1})])),_:1}),F(x,{md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"code",label:"菜单编号","has-feedback":""},{default:C((()=>[F(i,{value:ee.code,"onUpdate:value":a[1]||(a[1]=e=>ee.code=e),placeholder:"请输入菜单编号",maxlength:50},null,8,["value"])])),_:1})])),_:1}),F(x,{md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"application",label:"功能模块","has-feedback":""},{default:C((()=>[F(U,{value:ee.application,"onUpdate:value":a[2]||(a[2]=e=>ee.application=e),class:"selt","allow-clear":"",placeholder:"请选择功能模块分类",onChange:a[3]||(a[3]=(e,a)=>S(e,0,"0"))},{default:C((()=>[(q(!0),z(R,null,Y(re.value,((e,a)=>(q(),A(I,{key:a,value:e.code},{default:C((()=>[E(P(e.name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1}),F(x,{md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"type",label:"菜单层级","has-feedback":""},{default:C((()=>[F(N,{value:ee.type,"onUpdate:value":a[4]||(a[4]=e=>ee.type=e),disabled:te.value,onChange:se},{default:C((()=>[F(L,{value:0},{default:C((()=>a[21]||(a[21]=[E("目录")]))),_:1}),F(L,{value:1},{default:C((()=>a[22]||(a[22]=[E("菜单")]))),_:1}),F(L,{value:2},{default:C((()=>a[23]||(a[23]=[E("按钮")]))),_:1})])),_:1},8,["value","disabled"])])),_:1})])),_:1}),F(x,{md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"pid",label:"父级菜单","has-feedback":""},{default:C((()=>[F(K,{value:ee.pid,"onUpdate:value":a[5]||(a[5]=e=>ee.pid=e),"fild-names":Z,style:{width:"100%"},"allow-clear":"","dropdown-style":{maxHeight:"300px",overflow:"auto"},"tree-data":$.value,placeholder:"请选择父级菜单","tree-default-expand-all":""},null,8,["value","tree-data"])])),_:1})])),_:1}),1===ee.type?(q(),A(x,{key:0,md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"type",label:"菜单类型","has-feedback":""},{default:C((()=>[F(N,{value:ee.openType,"onUpdate:value":a[6]||(a[6]=e=>ee.openType=e),disabled:te.value,onChange:ie},{default:C((()=>[F(L,{value:1},{default:C((()=>a[24]||(a[24]=[E("组件")]))),_:1}),F(L,{value:2},{default:C((()=>a[25]||(a[25]=[E("内外链")]))),_:1}),F(L,{value:3},{default:C((()=>a[26]||(a[26]=[E("子应用")]))),_:1})])),_:1},8,["value","disabled"])])),_:1})])),_:1})):M("",!0),F(x,{md:12,sm:24},{default:C((()=>[F(w,{name:"icon",label:"图标","has-feedback":""},{default:C((()=>[F(i,{value:ee.icon,"onUpdate:value":a[9]||(a[9]=e=>ee.icon=e),placeholder:"请选择图标",class:"icon-input"},{addonAfter:C((()=>[B("div",O,[F(ne,{title:"删除",placement:"bottom"},{default:C((()=>[F(J(g),{class:"icon-btn",onClick:a[7]||(a[7]=e=>{ee.icon=""})})])),_:1}),F(ne,{title:"设置图标",placement:"bottom"},{default:C((()=>[F(J(j),{class:"icon-btn",onClick:a[8]||(a[8]=e=>{de.value.init()})})])),_:1})])])),_:1},8,["value"])])),_:1})])),_:1})])),_:1}),F(pe,{gutter:24,class:"form-bottom"},{default:C((()=>[F(x,{md:12,sm:24,class:"form-item"},{default:C((()=>[F(w,{name:"component",label:"前端组件","has-feedback":""},{default:C((()=>[F(i,{value:ee.component,"onUpdate:value":a[10]||(a[10]=e=>ee.component=e),disabled:2===ee.openType||3===ee.openType,placeholder:"请输入前端组件",maxlength:255},null,8,["value","disabled"])])),_:1}),B("span",D,[F(ne,{title:"前端vue组件，views文件夹下路径，例：system/menu/index",placement:"right"},{default:C((()=>[F(J(T),{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),0===ee.type||1===ee.type?(q(),A(x,{key:0,md:12,sm:24,class:"form-item"},{default:C((()=>[F(w,{name:"router",label:"路由地址","has-feedback":""},{default:C((()=>[F(i,{value:ee.router,"onUpdate:value":a[11]||(a[11]=e=>ee.router=e),placeholder:"请输入路由地址",maxlength:255},null,8,["value"])])),_:1}),B("span",H,[F(ne,{title:"浏览器显示的URL，例：/menu，对应打开页面为菜单页面",placement:"right"},{default:C((()=>[F(J(T),{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})):M("",!0),2===ee.openType?(q(),A(x,{key:1,md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"link",label:"内外链地址","has-feedback":""},{default:C((()=>[F(i,{value:ee.link,"onUpdate:value":a[12]||(a[12]=e=>ee.link=e),placeholder:"请输入内外链地址"},null,8,["value"])])),_:1})])),_:1})):M("",!0),2===ee.openType?(q(),A(x,{key:2,md:12,sm:24},{default:C((()=>[F(w,{name:"type",label:"打开方式","has-feedback":""},{default:C((()=>[F(N,{value:ee.iframeType,"onUpdate:value":a[13]||(a[13]=e=>ee.iframeType=e)},{default:C((()=>[F(L,{value:"0"},{default:C((()=>a[27]||(a[27]=[E("当前页面")]))),_:1}),F(L,{value:"1"},{default:C((()=>a[28]||(a[28]=[E("新标签页")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1})):M("",!0),2===ee.openType?(q(),A(x,{key:3,md:12,sm:24},{default:C((()=>[F(w,{name:"withToken",label:"携带token","has-feedback":""},{default:C((()=>[F(N,{value:ee.withToken,"onUpdate:value":a[14]||(a[14]=e=>ee.withToken=e)},{default:C((()=>[F(L,{value:"0"},{default:C((()=>a[29]||(a[29]=[E("否")]))),_:1}),F(L,{value:"1"},{default:C((()=>a[30]||(a[30]=[E("工作台")]))),_:1}),F(L,{value:"2"},{default:C((()=>a[31]||(a[31]=[E("底座")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1})):M("",!0),3===ee.openType?(q(),A(x,{key:4,md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"microAppId",label:"子应用","has-feedback":""},{default:C((()=>[F(U,{value:ee.microAppId,"onUpdate:value":a[15]||(a[15]=e=>ee.microAppId=e),placeholder:"请选择子应用"},{default:C((()=>[(q(!0),z(R,null,Y(G.value,(e=>(q(),A(I,{key:e.id,value:e.id,disabled:1===e.status||e.used},{default:C((()=>[E(P(e.name),1)])),_:2},1032,["value","disabled"])))),128))])),_:1},8,["value"])])),_:1})])),_:1})):M("",!0),2===ee.type?(q(),A(x,{key:5,md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"permission",label:"权限标识","has-feedback":""},{default:C((()=>[F(i,{value:ee.permission,"onUpdate:value":a[16]||(a[16]=e=>ee.permission=e),placeholder:"请输入权限标识",maxlength:80},null,8,["value"])])),_:1})])),_:1})):M("",!0),F(x,{md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"sort",label:"排序","has-feedback":""},{default:C((()=>[F(ue,{value:ee.sort,"onUpdate:value":a[17]||(a[17]=e=>ee.sort=e),style:{width:"100%"},placeholder:"请输入排序",min:1,max:1e10},null,8,["value"])])),_:1})])),_:1}),F(x,{md:12,sm:24},{default:C((()=>[F(w,{class:"form-item",name:"remark",label:"备注","has-feedback":""},{default:C((()=>[F(i,{value:ee.remark,"onUpdate:value":a[18]||(a[18]=e=>ee.remark=e),placeholder:"请输入备注",maxlength:80},null,8,["value"])])),_:1})])),_:1}),F(x,{md:12,sm:24},{default:C((()=>[F(w,{class:"form-item switch-wrap",name:"visible",label:"是否可见","has-feedback":""},{default:C((()=>[F(ce,{checked:ee.visible,"onUpdate:checked":a[19]||(a[19]=e=>ee.visible=e),"checked-children":"是","un-checked-children":"否"},null,8,["checked"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"]),F(o,{ref_key:"IconManageRef",ref:de,onOnBack:me},null,512)])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-7ac7240f"]]);export{K as default};
