import{s as e,u as a,a as t,f as l,h as d,i as r}from"./main-Djn9RDyT.js";import{S as s,F as n,_ as o,b as u,c as i,I as m,o as c,p,i as v,M as f}from"./ant-design-vue-DYY9BtJq.js";import{d as h,r as g,a as k,a9 as _,o as b,aa as y,c as x,n as w}from"./@vue-HScy-mz9.js";import{_ as j}from"./vue-qr-CB2aNKv5.js";const I="/edtap/sys-dept/tree",q="/edtap/sys-dept/page",E="/edtap/sys-dept/add",U="/edtap/sys-dept/edit",F="/edtap/sys-dept/delete",A="/edtap/sys-dept/delete-batch";function O(a){return e({url:q,method:"get",params:a})}function S(a){return e({url:F,method:"post",data:a})}function H(a){return e({url:A,method:"post",data:a})}const M=j(h({__name:"AddEditForm",emits:["ok"],setup(h,{expose:j,emit:q}){a();const F=t(),A=q,O=g(!1),S=g(!1),H=g(),M=k({id:"",name:"",code:"",pid:"",sort:100,remark:""}),z={children:"children",title:"title",key:"id",value:"id"},C={name:[{required:!0,message:"请输入部门名称！",trigger:"blur"},{max:30,message:"名字长度不能超过30",trigger:"blur"}],code:[{required:!0,message:"请输入唯一编码！",validator:l}],pid:[{required:!0,message:"请选择上级部门！"}],sort:[{required:!0,message:"请输入序号"}],remark:[{max:80,message:"备注长度不超过80！"}]},K=g(""),P=()=>{H.value.resetFields(),O.value=!1,S.value=!1},R=g([]),T=()=>{var a,t;S.value=!0,(t={enterpriseId:null==(a=F.checkedEnterprise)?void 0:a.id},e({url:I,method:"get",params:t})).then((e=>{var a;S.value=!1,e.success||(R.value=[]),R.value=[{children:e.data,id:"0",parentId:"0",value:"0",pid:"0",title:(null==(a=F.checkedEnterprise)?void 0:a.name)||"默认"}]})).catch((()=>{S.value=!1}))},Z=()=>{S.value=!0,H.value.validate().then((()=>{var a,t,l;if("add"===K.value){const t={code:M.code,name:M.name,pid:M.pid,sort:M.sort,remark:M.remark,enterpriseId:null==(a=F.checkedEnterprise)?void 0:a.id};(l=t,e({url:E,method:"post",data:l})).then((e=>{200===e.code?(P(),A("ok")):S.value=!1})).catch((()=>{S.value=!1})).finally((()=>{S.value=!1}))}else{(function(a){return e({url:U,method:"post",data:a})})({code:M.code,name:M.name,pid:M.pid,sort:M.sort,remark:M.remark,id:M.id,enterpriseId:null==(t=F.checkedEnterprise)?void 0:t.id}).then((e=>{200===e.code?(P(),A("ok")):S.value=!1})).catch((()=>{S.value=!1})).finally((()=>{S.value=!1}))}})).catch((e=>{S.value=!1}))},B=()=>{if(M.name=d(M.name),"edit"===K.value)return;const e=r(M.name);if("_"===e.charAt(e.length-1)){const a=e.slice(0,e.length-1);M.code=a.length>50?a.substr(0,50):a}else M.code=e.length>50?e.substr(0,50):e};return j({init:(e,a,t)=>{O.value=!0,K.value=e,w((()=>{T(),H.value.resetFields(),"edit"===e&&a?(M.code=a.code,M.id=a.id,M.name=a.name,M.pid=a.pid,M.sort=a.sort,M.remark=a.remark):M.pid=t}))}}),(e,a)=>{const t=m,l=i,d=u,r=c,h=p,g=v,k=o,w=n,j=s,I=f;return b(),_(I,{width:676,title:"add"===K.value?"新增部门":"编辑部门","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:O.value,"confirm-loading":S.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[5]||(a[5]=e=>Z()),onCancel:P},{default:y((()=>[x(j,{spinning:S.value},{default:y((()=>[x(w,{ref_key:"formRef",ref:H,model:M,rules:C,"label-align":"left"},{default:y((()=>[x(k,{gutter:24},{default:y((()=>[x(d,{md:12,sm:24},{default:y((()=>[x(l,{name:"name",label:"部门名称","has-feedback":""},{default:y((()=>[x(t,{value:M.name,"onUpdate:value":a[0]||(a[0]=e=>M.name=e),placeholder:"请输入部门名称",maxlength:30,onKeyup:B},null,8,["value"])])),_:1})])),_:1}),x(d,{md:12,sm:24},{default:y((()=>[x(l,{name:"code",label:"唯一编码","has-feedback":""},{default:y((()=>[x(t,{value:M.code,"onUpdate:value":a[1]||(a[1]=e=>M.code=e),placeholder:"请输入唯一编码",maxlength:50},null,8,["value"])])),_:1})])),_:1}),x(d,{md:12,sm:24},{default:y((()=>[x(l,{name:"pid",label:"上级部门","has-feedback":""},{default:y((()=>[x(r,{value:M.pid,"onUpdate:value":a[2]||(a[2]=e=>M.pid=e),"fild-names":z,style:{width:"100%"},"dropdown-style":{maxHeight:"300px",overflow:"auto"},"tree-data":R.value,placeholder:"请选择上级部门","tree-default-expand-all":""},null,8,["value","tree-data"])])),_:1})])),_:1}),x(d,{md:12,sm:24},{default:y((()=>[x(l,{name:"sort",label:"排序","has-feedback":""},{default:y((()=>[x(h,{value:M.sort,"onUpdate:value":a[3]||(a[3]=e=>M.sort=e),style:{width:"100%"},placeholder:"请输入排序",maxlength:10,min:1,max:1e3},null,8,["value"])])),_:1})])),_:1}),x(d,{md:24,sm:24},{default:y((()=>[x(l,{name:"remark",label:"备注"},{default:y((()=>[x(g,{value:M.remark,"onUpdate:value":a[4]||(a[4]=e=>M.remark=e),rows:4,placeholder:"请输入备注",maxlength:80},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-4575e4ac"]]),z=Object.freeze(Object.defineProperty({__proto__:null,default:M},Symbol.toStringTag,{value:"Module"}));export{M as A,H as a,z as b,S as d,O as g};
