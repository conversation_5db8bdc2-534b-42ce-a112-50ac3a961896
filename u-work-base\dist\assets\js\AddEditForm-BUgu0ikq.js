import{f as e,h as a,i as s,b as t}from"./main-Djn9RDyT.js";import{b as o,c as r}from"./dictionaryManage-gGpiMShb.js";import{S as l,F as i,_ as m,b as n,c as d,I as u,p,i as c,M as j}from"./ant-design-vue-DYY9BtJq.js";import{d as v,r as f,a as g,a9 as h,o as b,aa as _,c as k,n as y}from"./@vue-HScy-mz9.js";import{_ as x}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const w=x(v({__name:"AddEditForm",emits:["ok"],setup(v,{expose:x,emit:w}){const z=w,F=f("");x({init:(e,a)=>{M.value=!0,F.value=e,y((()=>{U.value.resetFields(),"edit"===e&&a&&(q.id=a.id,q.name=a.name,q.code=a.code,q.sort=a.sort,q.remark=a.remark)}))}});const U=f(),q=g({id:"",name:"",code:"",sort:100,remark:""}),A={name:[{required:!0,message:"请输入类型名称！"},{max:30,message:"类型名称长度不超过30！"}],code:[{required:!0,validator:e}],remark:[{max:80,message:"备注长度不超过80！"}]},I=()=>{if(q.name=a(q.name),"edit"===F.value)return;const e=s(q.name);if("_"===e.charAt(e.length-1)){const a=e.slice(0,e.length-1);q.code=a.length>50?a.substring(0,50):a}else q.code=e.length>50?e.substring(0,50):e},M=f(!1),O=f(!1),E=()=>{U.value.resetFields(),M.value=!1,O.value=!1};return(e,a)=>{const s=u,v=d,f=n,g=p,y=c,x=m,w=i,K=l,N=j;return b(),h(N,{width:676,title:"add"===F.value?"新增字典类型":"编辑字典类型","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:M.value,"confirm-loading":O.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[4]||(a[4]=e=>(O.value=!0,void U.value.validate().then((async()=>{let e;const a={groupId:1};"add"===F.value?(e=o,Object.assign(a,{name:q.name,code:q.code,sort:q.sort,remark:q.remark})):(e=r,Object.assign(a,{...q}));try{200===(await e(a)).code&&(t("success",("add"===F.value?"字典类型新增":"字典类型编辑")+"成功"),U.value.resetFields(),M.value=!1,z("ok"))}catch(s){}finally{O.value=!1}})).catch((e=>{O.value=!1})))),onCancel:E},{default:_((()=>[k(K,{spinning:O.value},{default:_((()=>[k(w,{ref_key:"formRef",ref:U,model:q,rules:A,"label-align":"left"},{default:_((()=>[k(x,{gutter:24},{default:_((()=>[k(f,{md:12,sm:24},{default:_((()=>[k(v,{name:"name",label:"类型名称","has-feedback":""},{default:_((()=>[k(s,{value:q.name,"onUpdate:value":a[0]||(a[0]=e=>q.name=e),placeholder:"请输入类型名称",maxlength:30,onKeyup:I},null,8,["value"])])),_:1})])),_:1}),k(f,{md:12,sm:24},{default:_((()=>[k(v,{name:"code",label:"唯一编码","has-feedback":""},{default:_((()=>[k(s,{value:q.code,"onUpdate:value":a[1]||(a[1]=e=>q.code=e),placeholder:"请输入唯一编码",maxlength:50},null,8,["value"])])),_:1})])),_:1}),k(f,{md:12,sm:24},{default:_((()=>[k(v,{name:"sort",label:"排序","has-feedback":""},{default:_((()=>[k(g,{value:q.sort,"onUpdate:value":a[2]||(a[2]=e=>q.sort=e),style:{width:"100%"},placeholder:"请输入唯一编码",min:1,max:1e3},null,8,["value"])])),_:1})])),_:1}),k(f,{md:24,sm:24},{default:_((()=>[k(v,{name:"remark",label:"备注"},{default:_((()=>[k(y,{value:q.remark,"onUpdate:value":a[3]||(a[3]=e=>q.remark=e),rows:4,placeholder:"请输入备注",maxlength:80},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-6fae3a6f"]]);export{w as default};
