import{M as e,f as a,a5 as l,b as i,a6 as t,h as s,i as o}from"./main-Djn9RDyT.js";import d from"./IconManage-esGETqvN.js";import{S as r,F as n,_ as m,b as u,c as p,I as c,d as v,h as f,R as b,w as _,x as y,p as j,M as h}from"./ant-design-vue-DYY9BtJq.js";import{d as k,r as g,a as I,p as A,am as T,a9 as x,o as U,aa as w,c as q,ab as z,e as C,J as F,b as M,F as N,ag as O,ad as Y,n as R}from"./@vue-HScy-mz9.js";import{_ as B}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const D={class:"icon-btns"},E=B(k({__name:"AddEditForm",emits:["ok"],setup(k,{expose:B,emit:E}){const G=E,J=g(!1),K=g(!1),S=g(),L=g([]),V=I({id:"",name:"",code:"",type:0,sort:100,iframeType:"0",microAppId:void 0,active:"N",visible:"Y",link:"",icon:"",sysCategoryId:null}),Z={name:[{required:!0,message:"请输入功能模块名称！",trigger:"blur"}],type:[{required:!0,message:"请选择功能模块类型！",trigger:"blur"}],code:[{required:!0,validator:a}],sort:[{required:!0,message:"请输入序号！"}],microAppId:[{required:A((()=>1===V.type)),message:"请选择子应用！",trigger:"change"}],link:[{required:A((()=>2===V.type)),message:"请输入iframe地址！"},{validator:e}]},$=g(),H=g(""),P=()=>{V.microAppId=void 0,V.link="",V.iframeType="0"},Q=()=>{K.value||(S.value.resetFields(),V.link="",V.icon="",V.type=0,V.microAppId=void 0,V.iframeType="0",J.value=!1,K.value=!1)},W=()=>{if(V.name=s(V.name),"edit"===H.value)return;const e=o(V.name);if("_"===e.charAt(e.length-1)){const a=e.slice(0,e.length-1);V.code=a.length>50?a.substr(0,50):a}else V.code=e.length>50?e.substr(0,50):e},X=g(),ee=e=>{V.icon=e};return B({init:(e,a)=>{J.value=!0,H.value=e,R((()=>{S.value.resetFields(),"edit"===e&&a&&($.value=a,V.code=a.code,V.id=a.id,V.name=a.name,V.sort=a.sort,V.active=a.active||"N",V.visible=a.visible||"Y",V.type=a.type,V.microAppId=a.microAppId||void 0,V.icon=a.icon,V.link=a.link,V.iframeType=a.iframeType)}))}}),(e,a)=>{const s=c,o=p,k=u,g=T("DeleteOutlined"),I=v,A=T("SettingOutlined"),R=b,B=f,E=y,ae=_,le=j,ie=m,te=n,se=r,oe=h;return U(),x(oe,{width:460,title:"add"===H.value?"新增功能模块":"编辑功能模块","wrap-class-name":"cus-modal",open:J.value,"confirm-loading":K.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[12]||(a[12]=e=>(K.value=!0,void S.value.validate().then((()=>{if("add"===H.value){const e={code:V.code,name:V.name,sort:V.sort,active:V.active,visible:V.visible,type:V.type,icon:V.icon,link:V.link,microAppId:V.microAppId,iframeType:V.iframeType};l(e).then((e=>{K.value=!1,200===e.code?(i("success","功能模块新增成功"),Q(),G("ok")):i("error",e.message)})).catch((()=>{K.value=!1})).finally((()=>{K.value=!1}))}else{const e={code:$.value.code,name:V.name,sort:V.sort,id:V.id,active:V.active,visible:V.visible,type:V.type,icon:V.icon,link:V.link,microAppId:V.microAppId,iframeType:V.iframeType};t(e).then((e=>{K.value=!1,200===e.code?(i("success","功能模块修改成功"),Q(),G("ok")):i("error",e.message)})).catch((()=>{K.value=!1})).finally((()=>{K.value=!1}))}})).catch((e=>{K.value=!1})))),onCancel:Q},{default:w((()=>[q(se,{spinning:K.value},{default:w((()=>[q(te,{ref_key:"formRef",ref:S,model:V,rules:Z,"label-align":"left"},{default:w((()=>[q(ie,{gutter:24},{default:w((()=>[q(k,{md:24,sm:24},{default:w((()=>[q(o,{name:"name",label:"功能模块名称","has-feedback":""},{default:w((()=>[q(s,{value:V.name,"onUpdate:value":a[0]||(a[0]=e=>V.name=e),placeholder:"请输入功能模块名称",maxlength:30,onKeyup:W},null,8,["value"])])),_:1})])),_:1}),q(k,{md:24,sm:24},{default:w((()=>[q(o,{name:"code",label:"唯一编码","has-feedback":""},{default:w((()=>[q(s,{value:V.code,"onUpdate:value":a[1]||(a[1]=e=>V.code=e),placeholder:"请输入唯一编码",disabled:"edit"===H.value,maxlength:50},null,8,["value","disabled"])])),_:1})])),_:1}),q(k,{md:24,sm:24},{default:w((()=>[q(o,{name:"icon",label:"图标","has-feedback":""},{default:w((()=>[q(s,{value:V.icon,"onUpdate:value":a[4]||(a[4]=e=>V.icon=e),readonly:"",placeholder:"请选择图标",class:"icon-input"},{addonAfter:w((()=>[C("div",D,[q(I,{title:"删除",placement:"bottom"},{default:w((()=>[q(g,{class:"icon-btn",onClick:a[2]||(a[2]=e=>{V.icon=""})})])),_:1}),q(I,{title:"设置图标",placement:"bottom"},{default:w((()=>[q(A,{class:"icon-btn",onClick:a[3]||(a[3]=e=>{X.value.init()})})])),_:1})])])),_:1},8,["value"])])),_:1})])),_:1}),q(k,{md:24,sm:24},{default:w((()=>[q(o,{name:"type",label:"功能模块类型","has-feedback":""},{default:w((()=>[q(B,{value:V.type,"onUpdate:value":a[5]||(a[5]=e=>V.type=e),disabled:"edit"===H.value,onChange:P},{default:w((()=>[q(R,{value:0},{default:w((()=>a[13]||(a[13]=[F("应用模块")]))),_:1}),q(R,{value:1},{default:w((()=>a[14]||(a[14]=[F("子应用")]))),_:1}),q(R,{value:2},{default:w((()=>a[15]||(a[15]=[F("iframe")]))),_:1})])),_:1},8,["value","disabled"])])),_:1})])),_:1}),q(k,{md:24,sm:24},{default:w((()=>[1===V.type?(U(),x(o,{key:0,name:"microAppId",label:"子应用","has-feedback":""},{default:w((()=>[q(ae,{value:V.microAppId,"onUpdate:value":a[6]||(a[6]=e=>V.microAppId=e),placeholder:"请选择子应用",disabled:"edit"===H.value},{default:w((()=>[(U(!0),M(N,null,O(L.value,(e=>(U(),x(E,{key:e.id,value:e.id,disabled:1===e.status||e.used},{default:w((()=>[F(Y(e.name),1)])),_:2},1032,["value","disabled"])))),128))])),_:1},8,["value","disabled"])])),_:1})):z("",!0)])),_:1}),2===V.type?(U(),x(k,{key:0,md:24,sm:24},{default:w((()=>[q(o,{class:"form-item",name:"link",label:"iframe地址","has-feedback":""},{default:w((()=>[q(s,{value:V.link,"onUpdate:value":a[7]||(a[7]=e=>V.link=e),placeholder:"请输入iframe地址"},null,8,["value"])])),_:1})])),_:1})):z("",!0),q(k,{md:24,sm:24},{default:w((()=>[2===V.type?(U(),x(o,{key:0,name:"type",label:"打开方式","has-feedback":""},{default:w((()=>[q(B,{value:V.iframeType,"onUpdate:value":a[8]||(a[8]=e=>V.iframeType=e)},{default:w((()=>[q(R,{value:"0"},{default:w((()=>a[16]||(a[16]=[F("当前页面")]))),_:1}),q(R,{value:"1"},{default:w((()=>a[17]||(a[17]=[F("新标签页")]))),_:1})])),_:1},8,["value"])])),_:1})):z("",!0)])),_:1}),q(k,{md:24,sm:24},{default:w((()=>[q(o,{name:"active",label:"是否默认","has-feedback":""},{default:w((()=>[q(B,{value:V.active,"onUpdate:value":a[9]||(a[9]=e=>V.active=e),name:"radioGroup"},{default:w((()=>[q(R,{value:"Y"},{default:w((()=>a[18]||(a[18]=[F("是")]))),_:1}),q(R,{value:"N"},{default:w((()=>a[19]||(a[19]=[F("否")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1}),q(k,{md:24,sm:24},{default:w((()=>[q(o,{name:"visible",label:"是否显示","has-feedback":""},{default:w((()=>[q(B,{value:V.visible,"onUpdate:value":a[10]||(a[10]=e=>V.visible=e),name:"radioGroup"},{default:w((()=>[q(R,{value:"Y"},{default:w((()=>a[20]||(a[20]=[F("是")]))),_:1}),q(R,{value:"N"},{default:w((()=>a[21]||(a[21]=[F("否")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1}),q(k,{md:24,sm:24},{default:w((()=>[q(o,{name:"sort",label:"排序","has-feedback":""},{default:w((()=>[q(le,{value:V.sort,"onUpdate:value":a[11]||(a[11]=e=>V.sort=e),style:{width:"100%"},placeholder:"请输入排序",maxlength:10,min:0,max:1e3},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"]),q(d,{ref_key:"IconManageRef",ref:X,onOnBack:ee},null,512)])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-cfe6e3fe"]]);export{E as default};
