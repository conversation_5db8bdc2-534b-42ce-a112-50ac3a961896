import{a as e,f as a,b as s,h as o,i as t}from"./main-Djn9RDyT.js";import{s as r,a as l}from"./role-OrQ5c9FV.js";import{S as i,F as m,_ as n,b as u,c as d,I as c,p,i as v,M as j}from"./ant-design-vue-DYY9BtJq.js";import{d as f,r as g,a as h,a9 as k,o as b,aa as _,c as y,n as x}from"./@vue-HScy-mz9.js";import{_ as w}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const F=w(f({__name:"AddEditForm",emits:["ok"],setup(f,{expose:w,emit:F}){e();const q=F,z=g(!1),U=g(!1),A=g(),E=h({id:"",name:"",code:"",sort:100,remark:"",acquiesceFlag:1}),I={name:[{required:!0,message:"请输入角色名！"},{max:30,message:"角色名字长度不能超过30个字符",trigger:"blur"}],code:[{required:!0,validator:a}],remark:[{max:80,message:"备注长度不超过80！"}]},K=g(""),M=()=>{A.value.resetFields(),z.value=!1,U.value=!1},B=()=>{if(E.name=o(E.name),"edit"===K.value)return;const e=t(E.name);if("_"===e.charAt(e.length-1)){const a=e.slice(0,e.length-1);E.code=a.length>50?a.substr(0,50):a}else E.code=e.length>50?e.substr(0,50):e};return w({init:(e,a)=>{z.value=!0,K.value=e,x((()=>{A.value.resetFields(),"edit"===e&&a&&(E.acquiesceFlag=a.acquiesceFlag,E.code=a.code,E.id=a.id,E.name=a.name,E.sort=a.sort,E.remark=a.remark)}))}}),(e,a)=>{const o=c,t=d,f=u,g=p,h=v,x=n,w=m,F=i,C=j;return b(),k(C,{width:676,title:"add"===K.value?"新增角色":"编辑角色","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:z.value,"confirm-loading":U.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[4]||(a[4]=e=>(U.value=!0,void A.value.validate().then((()=>{if("add"===K.value){const e={code:E.code,name:E.name,sort:E.sort,remark:E.remark,acquiesceFlag:1};r(e).then((e=>{200===e.code?(s("success","角色新增成功"),M(),q("ok")):(s("error","角色新增失败"),U.value=!1)})).catch((()=>{U.value=!1})).finally((()=>{U.value=!1}))}else{const e={code:E.code,name:E.name,sort:E.sort,remark:E.remark,id:E.id,acquiesceFlag:E.acquiesceFlag};l(e).then((e=>{200===e.code?(s("success","角色编辑成功"),M(),q("ok")):(s("error","角色编辑失败"),U.value=!1)})).catch((()=>{U.value=!1})).finally((()=>{U.value=!1}))}})).catch((e=>{U.value=!1})))),onCancel:M},{default:_((()=>[y(F,{spinning:U.value},{default:_((()=>[y(w,{ref_key:"formRef",ref:A,model:E,rules:I,"label-align":"left"},{default:_((()=>[y(x,{gutter:24},{default:_((()=>[y(f,{md:12,sm:24},{default:_((()=>[y(t,{name:"name",label:"角色名","has-feedback":""},{default:_((()=>[y(o,{value:E.name,"onUpdate:value":a[0]||(a[0]=e=>E.name=e),placeholder:"请输入角色名",maxlength:30,onKeyup:B},null,8,["value"])])),_:1})])),_:1}),y(f,{md:12,sm:24},{default:_((()=>[y(t,{name:"code",label:"唯一编码","has-feedback":""},{default:_((()=>[y(o,{value:E.code,"onUpdate:value":a[1]||(a[1]=e=>E.code=e),placeholder:"请输入唯一编码",maxlength:50},null,8,["value"])])),_:1})])),_:1}),y(f,{md:12,sm:24},{default:_((()=>[y(t,{name:"sort",label:"排序","has-feedback":""},{default:_((()=>[y(g,{value:E.sort,"onUpdate:value":a[2]||(a[2]=e=>E.sort=e),style:{width:"100%"},placeholder:"请输入排序",maxlength:10,min:1,max:1e3},null,8,["value"])])),_:1})])),_:1}),y(f,{md:24,sm:24},{default:_((()=>[y(t,{name:"remark",label:"备注"},{default:_((()=>[y(h,{value:E.remark,"onUpdate:value":a[3]||(a[3]=e=>E.remark=e),rows:4,placeholder:"请输入备注",maxlength:80},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-f32a2b76"]]);export{F as default};
