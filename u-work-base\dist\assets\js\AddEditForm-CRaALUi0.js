import{u as e,a,f as s,b as r,h as o,i as t}from"./main-Djn9RDyT.js";import{b as l,c as i}from"./role-Dauzd5F5.js";import{S as m,F as n,_ as d,b as u,c as p,I as c,p as v,i as j,M as f}from"./ant-design-vue-DYY9BtJq.js";import{d as h,r as g,a as y,a9 as k,o as b,aa as _,c as x,n as w}from"./@vue-HScy-mz9.js";import{_ as I}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const C=I(h({__name:"AddEditForm",emits:["ok"],setup(h,{expose:I,emit:C}){e();const z=a(),E=C,F=g(!1),U=g(!1),q=g(),A=y({id:"",name:"",code:"",sort:100,remark:"",sysCategoryId:""}),K={name:[{required:!0,message:"请输入角色名！"},{max:30,message:"角色名字长度不能超过30个字符",trigger:"blur"}],code:[{required:!0,validator:s}],remark:[{max:80,message:"备注长度不超过80！"}]},M=g(""),B=()=>{q.value.resetFields(),F.value=!1,U.value=!1},D=()=>{if(A.name=o(A.name),"edit"===M.value)return;const e=t(A.name);if("_"===e.charAt(e.length-1)){const a=e.slice(0,e.length-1);A.code=a.length>50?a.substr(0,50):a}else A.code=e.length>50?e.substr(0,50):e};return I({init:(e,a,s,r)=>{F.value=!0,M.value=e,w((()=>{q.value.resetFields(),A.sysCategoryId=a,"edit"===e&&s&&(A.code=s.code,A.id=s.id,A.name=s.name,A.sort=s.sort,A.remark=s.remark)}))}}),(e,a)=>{const s=c,o=p,t=u,h=v,g=j,y=d,w=n,I=m,C=f;return b(),k(C,{width:676,title:"add"===M.value?"新增角色":"编辑角色","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:F.value,"confirm-loading":U.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[4]||(a[4]=e=>(U.value=!0,void q.value.validate().then((()=>{var e,a;if("add"===M.value){const a={code:A.code,name:A.name,sort:A.sort,remark:A.remark,sysCategoryId:A.sysCategoryId,enterpriseId:null==(e=z.checkedEnterprise)?void 0:e.id};l(a).then((e=>{200===e.code?(r("success","角色新增成功"),B(),E("ok")):(r("error","角色新增失败"),U.value=!1)})).catch((()=>{U.value=!1})).finally((()=>{U.value=!1}))}else{const e={code:A.code,name:A.name,sort:A.sort,remark:A.remark,id:A.id,sysCategoryId:A.sysCategoryId,enterpriseId:null==(a=z.checkedEnterprise)?void 0:a.id};i(e).then((e=>{200===e.code?(r("success","角色编辑成功"),B(),E("ok")):(r("error","角色编辑失败"),U.value=!1)})).catch((()=>{U.value=!1})).finally((()=>{U.value=!1}))}})).catch((e=>{U.value=!1})))),onCancel:B},{default:_((()=>[x(I,{spinning:U.value},{default:_((()=>[x(w,{ref_key:"formRef",ref:q,model:A,rules:K,"label-align":"left"},{default:_((()=>[x(y,{gutter:24},{default:_((()=>[x(t,{md:12,sm:24},{default:_((()=>[x(o,{name:"name",label:"角色名","has-feedback":""},{default:_((()=>[x(s,{value:A.name,"onUpdate:value":a[0]||(a[0]=e=>A.name=e),placeholder:"请输入角色名",maxlength:30,onKeyup:D},null,8,["value"])])),_:1})])),_:1}),x(t,{md:12,sm:24},{default:_((()=>[x(o,{name:"code",label:"唯一编码","has-feedback":""},{default:_((()=>[x(s,{value:A.code,"onUpdate:value":a[1]||(a[1]=e=>A.code=e),placeholder:"请输入唯一编码",maxlength:50},null,8,["value"])])),_:1})])),_:1}),x(t,{md:12,sm:24},{default:_((()=>[x(o,{name:"sort",label:"排序","has-feedback":""},{default:_((()=>[x(h,{value:A.sort,"onUpdate:value":a[2]||(a[2]=e=>A.sort=e),style:{width:"100%"},placeholder:"请输入排序",maxlength:10,min:1,max:1e3},null,8,["value"])])),_:1})])),_:1}),x(t,{md:24,sm:24},{default:_((()=>[x(o,{name:"remark",label:"备注"},{default:_((()=>[x(g,{value:A.remark,"onUpdate:value":a[3]||(a[3]=e=>A.remark=e),rows:4,placeholder:"请输入备注",maxlength:80},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-3d179a31"]]);export{C as default};
