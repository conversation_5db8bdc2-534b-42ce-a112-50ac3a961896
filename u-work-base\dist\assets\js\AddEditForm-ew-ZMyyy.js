import{V as e,n as a,o as t,b as o,W as l,X as s}from"./main-Djn9RDyT.js";import{S as r,F as i,_ as n,b as u,c as p,I as d,G as m,i as v,h as c,R as g,p as f,M as j}from"./ant-design-vue-DYY9BtJq.js";import{d as h,r as b,a as x,am as k,a9 as w,o as _,aa as y,c as M,J as C,b as P,e as z,ab as U,n as q}from"./@vue-HScy-mz9.js";import{_ as F}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const L=["src"],T={key:1},D={class:"auth-wrap"},R=F(h({__name:"AddEditForm",emits:["ok"],setup(h,{expose:F,emit:R}){const $=R,A=b(-1),E=b(-1),I=b(),J=async e=>{var l;const{file:s}=e;if(B.value[0]=s,B.value[0]){const e=new FormData;e.append("file",B.value[0]),e.append("bucketName","twinfile");const r=await a(e);200===r.code?(S.logo=null==(l=r.data)?void 0:l.id,t(s,(e=>{I.value=e}))):(S.logo="",o("error",r.message))}},N=e=>{const a="image/jpeg"===e.type||"image/png"===e.type||"image/jpg"===e.type||"image/gif"===e.type||"image/webp"===e.type||"image/apng"===e.type;a||o("error","支持上传.png, .jpg, .jpeg, .gif, .webp, .apng格式的图片");const t=e.size/1024/1024<5;return t||o("error","图片大小不超过5M"),a&&t},B=b([]),G=()=>{B.value=[],S.logo=""},H=b(!1),K=b(!1),O=b(),S=x({id:"",name:"",address:"",website:"",pid:"",logo:"",remark:"",contacts:"",developer:"",developerPhone:"",projectMaxCount:0,userMaxCount:0,status:0,registerTime:""}),V={name:[{required:!0,message:"请输入部门名称！",trigger:"blur"},{max:50,message:"名字长度不能超过50！",trigger:"blur"}],developer:[{required:!0,message:"请输入姓名！",trigger:"blur"}],logo:[{required:!0,message:"请上传Logo！",trigger:"blur"}],developerPhone:[{required:!0,message:"请输入手机号！",trigger:"blur"},{validator:e}]},W=b(""),X=()=>{O.value.resetFields(),B.value=[],I.value="",S.logo="",H.value=!1,K.value=!1};return F({init:(e,a)=>{H.value=!0,W.value=e,q((()=>{var t;O.value.resetFields(),"edit"!==e&&"check"!==e||!a||(S.id=a.id,S.name=a.name,S.address=a.address,S.website=a.website,S.logo=a.logo,S.remark=a.remark,S.contacts=a.contacts,S.developer=a.developer,S.developerPhone=a.developerPhone,S.projectMaxCount=a.projectMaxCount,S.userMaxCount=9999,E.value=9999===a.userMaxCount?-1:1,A.value=9999===a.projectMaxCount?-1:1,S.pid=a.pid||"0",S.status=a.status||0,I.value=(t=a,`${window.config.previewUrl}${t.logoUrl}`))}))}}),(e,a)=>{const t=u,h=d,b=p,x=k("plus-outlined"),q=m,F=v,R=g,E=c,Z=f,Q=n,Y=i,ee=r,ae=j;return _(),w(ae,{width:676,title:"add"===W.value?"新增团队":"check"===W.value?"查看团队":"编辑团队","body-style":{maxHeight:"600px",overflow:"auto",paddingTop:0},"wrap-class-name":"cus-modal",open:H.value,"confirm-loading":K.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[7]||(a[7]=e=>(K.value=!0,void O.value.validate().then((()=>{if("add"===W.value){const e={name:S.name,address:S.address,website:S.website,logo:S.logo,remark:S.remark,contacts:S.contacts,developer:S.developer,developerPhone:S.developerPhone,projectMaxCount:-1===A.value?9999:S.projectMaxCount,status:S.status,registerTime:new Date};l(e).then((e=>{200===e.code?(o("success","新增团队信息成功"),X(),$("ok","add")):K.value=!1})).catch((()=>{K.value=!1})).finally((()=>{K.value=!1}))}else if("check"===W.value)X();else{const e={name:S.name,address:S.address,website:S.website,logo:S.logo,remark:S.remark,contacts:S.contacts,developer:S.developer,developerPhone:S.developerPhone,projectMaxCount:-1===A.value?9999:S.projectMaxCount,status:S.status,id:S.id};s(e).then((e=>{200===e.code?(o("success","编辑团队信息成功"),X(),$("ok")):K.value=!1})).catch((()=>{K.value=!1})).finally((()=>{K.value=!1}))}})).catch((e=>{K.value=!1})))),onCancel:X},{default:y((()=>[M(ee,{spinning:K.value},{default:y((()=>[M(Y,{ref_key:"formRef",ref:O,model:S,rules:V,"label-align":"left"},{default:y((()=>[M(Q,{gutter:24},{default:y((()=>[M(t,{md:24,sm:24,style:{"font-size":"20px","font-weight":"600","line-height":"50px"}},{default:y((()=>a[8]||(a[8]=[C(" 团队基础信息 ")]))),_:1}),M(t,{md:24,sm:24},{default:y((()=>[M(b,{name:"name",label:"团队名称","has-feedback":""},{default:y((()=>[M(h,{value:S.name,"onUpdate:value":a[0]||(a[0]=e=>S.name=e),placeholder:"请输入团队名称",maxlength:50},null,8,["value"])])),_:1})])),_:1}),M(t,{md:24,sm:24},{default:y((()=>[M(b,{name:"logo",label:"logo图标","has-feedback":""},{default:y((()=>[M(q,{fileList:B.value,"onUpdate:fileList":a[1]||(a[1]=e=>B.value=e),accept:".png, .jpg, .jpeg, .gif, .webp, .apng","show-upload-list":!1,"list-type":"picture-card",multiple:!1,"custom-request":J,"max-count":1,"before-upload":N,onRemove:G},{default:y((()=>[I.value?(_(),P("img",{key:0,src:I.value,alt:"avatar",class:"avatar-img"},null,8,L)):(_(),P("div",T,[M(x),a[9]||(a[9]=z("div",{class:"ant-upload-text"},"上传",-1))]))])),_:1},8,["fileList"])])),_:1})])),_:1}),M(t,{md:24,sm:24},{default:y((()=>[M(b,{name:"remark",label:"备注说明"},{default:y((()=>[M(F,{value:S.remark,"onUpdate:value":a[2]||(a[2]=e=>S.remark=e),rows:4,placeholder:"请输入备注",maxlength:300},null,8,["value"])])),_:1})])),_:1}),M(t,{md:24,sm:24,style:{"font-size":"20px","font-weight":"600","line-height":"50px","border-top":"1px solid #0000001a"}},{default:y((()=>a[10]||(a[10]=[C(" 负责人信息 ")]))),_:1}),M(t,{md:24,sm:24},{default:y((()=>[M(b,{name:"developer",label:"姓名","has-feedback":""},{default:y((()=>[M(h,{value:S.developer,"onUpdate:value":a[3]||(a[3]=e=>S.developer=e),placeholder:"请输入姓名",maxlength:30},null,8,["value"])])),_:1})])),_:1}),M(t,{md:24,sm:24},{default:y((()=>[M(b,{name:"developerPhone",label:"联系方式","has-feedback":""},{default:y((()=>[M(h,{value:S.developerPhone,"onUpdate:value":a[4]||(a[4]=e=>S.developerPhone=e),placeholder:"请输入手机号",maxlength:130},null,8,["value"])])),_:1})])),_:1}),M(t,{md:24,sm:24,style:{"font-size":"20px","font-weight":"600","line-height":"50px","border-top":"1px solid #0000001a"}},{default:y((()=>a[11]||(a[11]=[C(" 授权信息 ")]))),_:1}),M(t,{md:24,sm:24},{default:y((()=>[M(b,{name:"projectMaxCount",label:"项目数量","has-feedback":""},{default:y((()=>[z("div",D,[M(E,{value:A.value,"onUpdate:value":a[5]||(a[5]=e=>A.value=e)},{default:y((()=>[M(R,{value:-1},{default:y((()=>a[12]||(a[12]=[C("不限量")]))),_:1}),M(R,{value:1},{default:y((()=>a[13]||(a[13]=[C("规定数量 ")]))),_:1})])),_:1},8,["value"]),1===A.value?(_(),w(Z,{key:0,value:S.projectMaxCount,"onUpdate:value":a[6]||(a[6]=e=>S.projectMaxCount=e),class:"radio-number",placeholder:"请输入最大支持创建项目数量",min:0,max:1e3},null,8,["value"])):U("",!0)])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-edf15771"]]);export{R as default};
