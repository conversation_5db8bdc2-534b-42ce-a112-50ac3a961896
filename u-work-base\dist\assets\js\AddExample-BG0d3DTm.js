import{d as e,r as a,am as l,a9 as t,o as s,aa as r,c as o,u as i,J as p,ad as u,e as n,y as m,G as d,b as v,ab as c,n as g}from"./@vue-HScy-mz9.js";import{_ as f}from"./sample-zip-Cn1wBsZQ.js";import{Q as h}from"./@vueup-CLVdhRgW.js";import{c as j,b as w,u as y,e as b,n as x,o as _}from"./main-Djn9RDyT.js";import{s as k,e as N,a as I}from"./examples-Cf8gesNV.js";import{s as z}from"./pinia-CheWBXuN.js";import{S as T,F,_ as M,b as q,c as E,I as L,z as U,T as H,G as $,B as D,k as O,d as V,M as C}from"./ant-design-vue-DYY9BtJq.js";import{_ as P}from"./vue-qr-CB2aNKv5.js";import"./quill-BBEhJLA6.js";import"./@babel-B4rXMRun.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const R={class:"form-item-notice keep-px"},S=["src"],B={key:1},A={class:"form-item-notice keep-px"},J=P(e({__name:"AddExample",emits:["ok"],setup(e,{expose:P,emit:J}){const G=j(),{themeColor:Z}=z(G),K=J,Q=a(!1),W=a(!1),X=a(),Y=a({expName:"",expNotes:"",file:"",previewId:"",id:"",templateTags:[],status:0}),ee=a(0);let ae={};const le=a([]),te=a({modules:{clipboard:{matchers:[["img",(e,a)=>{const l=[];return a.ops.forEach((e=>{e.insert&&"string"==typeof e.insert&&l.push({insert:e.insert})})),a.ops=l,a}]]}},placeholder:"请输入示例说明",readOnly:!1,theme:"snow"}),se=a([]),re=a([]);let oe="";const ie=a(new Map),pe=a([]),ue=a(new Map),ne={templateTags:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let t=0;return a.forEach((e=>{if(1===e.length)if(pe.value.includes(e[0])){const a=le.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+e.length-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}],expName:[{required:!0,message:"请输入示例名称！",trigger:"blur"}],expNotes:[{required:!0,message:"请输入示例说明！",trigger:"blur"}],expFile:[{required:!0,validator:e=>{const a=se.value[0];return new Promise(((e,l)=>{if(a){const t=a.name.split(".").pop().toLowerCase(),s=1048576e3;["zip"].includes(t)?a.size?a.size<=s?e():l(new Error("示例文件大小不能超过1000MB！")):e():l(new Error("只支持zip格式的文件！"))}else l(new Error("请上传示例文件！"))}))},trigger:"change"}],previewId:[{required:!0,message:"请上传封面图!",trigger:"blur"}]},me=e=>{const a="image/jpeg"===e.type||"image/png"===e.type||"image/jpg"===e.type||"image/gif"===e.type||"image/webp"===e.type||"image/apng"===e.type;a||w("error","支持上传.png, .jpg, .jpeg, .gif, .webp, .apng格式的图片");const l=e.size/1024/1024<5;return l||w("error","图片大小不超过5M"),a&&l},de=a(),ve=e=>`${window.config.previewUrl}${e.previewUrl}`,ce=()=>{X.value.resetFields(),Q.value=!1,be.value="",se.value=[],re.value=[],W.value=!1,Y.value={expName:"",expNotes:"",file:"",previewId:"",id:"",templateTags:[],status:0},de.value.setHTML(""),ee.value+=1},ge=a({percent:0,progressFlag:!1}),fe=e=>{e&&e.loaded&&e.total&&(ge.value.percent=Math.round(100*e.loaded/e.total))},he=e=>{const a=e.name;return".zip"!==a.substring(a.lastIndexOf("."))?(se.value=[],w("error","请上传.zip格式的文件"),!1):(se.value=[e],Y.value.file=e,!0)},je=async e=>{const{file:a}=e;se.value[0]=a,Y.value.file=a},we=()=>{b({code:"SHI_LI_MO_BAN_DI_ZHI"}).then((e=>{var a,l;const t=null==(l=null==(a=e.data)?void 0:a.rows[0])?void 0:l.value;t?window.open(t,"_blank"):w("error","请先配置示例模板下载地址")}))},ye=e=>{Y.value.expNotes=e},be=a(),xe=async e=>{var a;const{file:l}=e;if(re.value[0]=l,re.value[0]){const e=new FormData;e.append("file",l),e.append("bucketName","twinfile");const t=await x(e);200===t.code?(Y.value.previewId=null==(a=t.data)?void 0:a.id,_(l,(e=>{be.value=e}))):(w("error",t.message),Y.value.previewId="")}},_e=()=>{se.value=[],Y.value.file=""},ke=()=>{Y.value.previewId=""};return P({init:async(e,a,l)=>{if(Q.value=!0,oe=e,await new Promise((e=>{I().then((a=>{ae={};const l=a.data.map((e=>{var a;return ae[e.id]=e.tags,pe.value.push(e.id),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(ie.value.set(`${a.tagName}`,a.color),ue.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));le.value=l.filter((e=>e.children)),e(le.value)}))})),"add"===e)Y.value={expName:"",expNotes:"",file:"",previewId:"",id:"",templateTags:[]},se.value=[],re.value=[],Q.value=!0,g((()=>{X.value.resetFields()}));else if("edit"===e){Y.value.expName=a.name,Y.value.expNotes=a.remark,Y.value.file=a.url,Y.value.id=a.id,Y.value.previewId=a.previewId,Y.value.status=a.status,Y.value.templateTags=[];const e=[];a.functionExampleTags.forEach((a=>{ue.value.get(a.tagId)&&e.push([ue.value.get(a.tagId),a.tagId])})),g((()=>{de.value.setHTML(Y.value.expNotes),Y.value.templateTags=e})),se.value[0]={name:`${a.url.split("/")[a.url.split("/").length-2]}.zip`,uid:Math.floor(900*Math.random())+100},be.value=ve(a)}}}),(e,a)=>{const g=L,j=E,b=q,x=H,_=U,I=D,z=$,P=O,J=l("question-circle-outlined"),G=V,ee=l("plus-outlined"),pe=M,ue=F,ve=T,Ne=C;return s(),t(Ne,{width:800,title:"edit"===i(oe)?"编辑示例":"新增示例","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:Q.value,"confirm-loading":W.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[5]||(a[5]=e=>(W.value=!0,void X.value.validate().then((()=>{const{expName:e,expNotes:a,file:l,previewId:t,id:s,templateTags:r,status:o}=Y.value,i=new FormData,p=[];r.forEach((e=>{if(e[1]){const{id:a,tagName:l,color:t}=ae[e[0]].find((a=>a.id===e[1]));p.push({tagId:a,tagName:l,color:t})}else e[0]&&ae[e[0]].forEach((e=>{const{id:a,tagName:l,color:t}=e;p.push({tagId:a,tagName:l,color:t})}))})),i.append("name",e),i.append("file",l),i.append("previewId",t),i.append("remark",a),i.append("sampleDetails",""),i.append("functionExampleValue",JSON.stringify(p)),i.append("status",o||3),i.append("createUser",y().userInfo.id),ge.value.percent=0,ge.value.progressFlag=!0,"add"===oe?k(i,fe).then((e=>{200===e.code?(w("success","功能示例新增成功"),W.value=!1,Q.value=!1,K("ok")):(w("error",e.message),W.value=!1),ce(),ge.value.percent=0,ge.value.progressFlag=!1})).catch((()=>{W.value=!1,ge.value.percent=0,ge.value.progressFlag=!1})):("string"==typeof l&&i.delete("file"),i.append("id",s),ge.value.percent=0,ge.value.progressFlag=!0,N(i,fe).then((e=>{200===e.code?(w("success","功能示例修改成功"),W.value=!1,Q.value=!1,K("ok")):(w("error",e.message),W.value=!1),ce(),ge.value.percent=0,ge.value.progressFlag=!1})).catch((()=>{W.value=!1,ge.value.percent=0,ge.value.progressFlag=!1})))})).catch((e=>{W.value=!1})))),onCancel:ce},{default:r((()=>[o(ve,{spinning:W.value},{default:r((()=>[o(ue,{ref_key:"formRef",ref:X,model:Y.value,rules:ne,"label-align":"left"},{default:r((()=>[o(pe,{gutter:24},{default:r((()=>[o(b,{md:24,sm:24},{default:r((()=>[o(j,{name:"expName",label:"名称","has-feedback":""},{default:r((()=>[o(g,{value:Y.value.expName,"onUpdate:value":a[0]||(a[0]=e=>Y.value.expName=e),placeholder:"请输入示例名称",maxlength:30},null,8,["value"])])),_:1})])),_:1}),o(b,{md:24,sm:24},{default:r((()=>[o(j,{name:"templateTags",label:"标签","has-feedback":""},{default:r((()=>[o(_,{value:Y.value.templateTags,"onUpdate:value":a[1]||(a[1]=e=>Y.value.templateTags=e),defaultValue:Y.value.templateTags,"show-checked-strategy":i(U).SHOW_CHILD,style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:le.value,placeholder:"请选择标签",checkStrictly:!0},{tagRender:r((e=>{return[(s(),t(x,{key:e.value,color:(a=e.label,ie.value.get(a)||"blue")},{default:r((()=>[p(u(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","options"])])),_:1})])),_:1}),o(b,{md:24,sm:24},{default:r((()=>[o(j,{name:"expNotes",label:"示例说明","has-feedback":""},{default:r((()=>[o(i(h),{ref_key:"quillEditorRef",ref:de,modelValue:Y.value.expNotes,"onUpdate:modelValue":a[2]||(a[2]=e=>Y.value.expNotes=e),modelModifiers:{content:!0},style:{height:"180px"},options:te.value,"content-type":"html","onUpdate:content":ye},null,8,["modelValue","options"])])),_:1})])),_:1}),o(b,{md:24,sm:24,class:"form-item"},{default:r((()=>[o(j,{name:"expFile",label:"示例文件","has-feedback":""},{default:r((()=>[o(z,{fileList:se.value,"onUpdate:fileList":a[3]||(a[3]=e=>se.value=e),"before-upload":he,"show-upload-list":!0,accept:".zip",multiple:!1,"max-count":1,"custom-request":je,onRemove:_e},{default:r((()=>[o(I,{type:"primary"},{default:r((()=>a[6]||(a[6]=[p(" 点击上传 ")]))),_:1})])),_:1},8,["fileList"]),m(n("div",null,[o(P,{percent:ge.value.percent,size:"small","stroke-color":{from:"#108ee9",to:i(Z)}},null,8,["percent","stroke-color"])],512),[[d,ge.value.progressFlag]])])),_:1}),n("span",R,[o(G,{placement:"right"},{title:r((()=>[n("div",null,[a[7]||(a[7]=p(" 支持文件格式: zip, 大小限制: 1000M, ")),e.hasPerm("sys-sample:sample-url")?(s(),v("a",{key:0,style:{color:"#ef7b1a"},onClick:we},"下载示例模板")):c("",!0)]),a[8]||(a[8]=n("div",null,[n("p",null,"上传格式如下图所示："),n("img",{src:f,style:{width:"100%"}})],-1))])),default:r((()=>[o(J,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),o(b,{md:24,sm:24,class:"form-item"},{default:r((()=>[o(j,{name:"previewId",label:"封面图","has-feedback":""},{default:r((()=>[o(z,{fileList:re.value,"onUpdate:fileList":a[4]||(a[4]=e=>re.value=e),"before-upload":me,accept:".png, .jpg, .jpeg, .gif, .webp, .apng","show-upload-list":!1,"list-type":"picture-card",multiple:!1,"max-count":1,"custom-request":xe,onRemove:ke},{default:r((()=>[be.value?(s(),v("img",{key:0,src:be.value,alt:"avatar",class:"avatar-img"},null,8,S)):(s(),v("div",B,[o(ee),a[9]||(a[9]=n("div",{class:"ant-upload-text"},"上传",-1))]))])),_:1},8,["fileList"])])),_:1}),n("span",A,[o(G,{title:"支持文件格式: png、jpeg、jpg、gif, 大小限制: 5M",placement:"right"},{default:r((()=>[o(J,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-72e698d4"]]);export{J as default};
