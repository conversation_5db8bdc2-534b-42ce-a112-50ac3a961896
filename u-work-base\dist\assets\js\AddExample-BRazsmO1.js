import{d as e,r as a,am as l,a9 as t,o as s,aa as r,c as o,u as i,J as p,ad as u,e as n,y as d,G as m,b as v,n as c}from"./@vue-HScy-mz9.js";import{_ as g}from"./sample-zip-Cn1wBsZQ.js";import{b as f,u as h,e as j,n as w,o as y}from"./main-Djn9RDyT.js";import{Q as b}from"./@vueup-CLVdhRgW.js";import{s as x,e as _,a as N}from"./examples-Cf8gesNV.js";import{S as k,F as I,_ as z,b as F,c as T,I as M,z as q,T as E,G as L,B as U,k as H,d as $,M as D}from"./ant-design-vue-DYY9BtJq.js";import{_ as O}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./quill-BBEhJLA6.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./js-binary-schema-parser-G48GG52R.js";const V={class:"form-item-notice keep-px"},R=["src"],S={key:1},B={class:"form-item-notice keep-px"},C=O(e({__name:"AddExample",emits:["ok"],setup(e,{expose:O,emit:C}){const P=C,A=a(!1),J=a(!1),G=a(),Z=a({expName:"",expNotes:"",file:"",previewId:"",id:"",templateTags:[],status:0}),K=a(0);let Q={};const W=a([]),X=a({modules:{clipboard:{matchers:[["img",(e,a)=>{const l=[];return a.ops.forEach((e=>{e.insert&&"string"==typeof e.insert&&l.push({insert:e.insert})})),a.ops=l,a}]]}},placeholder:"请输入示例说明",readOnly:!1,theme:"snow"}),Y=a([]),ee=a([]);let ae="";const le=a(new Map),te=a([]),se=a(new Map),re={templateTags:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let t=0;return a.forEach((e=>{if(1===e.length)if(te.value.includes(e[0])){const a=W.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+e.length-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}],expName:[{required:!0,message:"请输入示例名称！",trigger:"blur"}],expNotes:[{required:!0,message:"请输入示例说明！",trigger:"blur"}],expFile:[{required:!0,validator:e=>{const a=Y.value[0];return new Promise(((e,l)=>{if(a){const t=a.name.split(".").pop().toLowerCase(),s=1048576e3;["zip"].includes(t)?a.size?a.size<=s?e():l(new Error("示例文件大小不能超过1000MB！")):e():l(new Error("只支持zip格式的文件！"))}else l(new Error("请上传示例文件！"))}))},trigger:"change"}],previewId:[{required:!0,message:"请上传封面图!",trigger:"blur"}]},oe=e=>{const a="image/jpeg"===e.type||"image/png"===e.type||"image/jpg"===e.type||"image/gif"===e.type||"image/webp"===e.type||"image/apng"===e.type;a||f("error","支持上传.png, .jpg, .jpeg, .gif, .webp, .apng格式的图片");const l=e.size/1024/1024<5;return l||f("error","图片大小不超过5M"),a&&l},ie=a(),pe=e=>`${window.config.previewUrl}${e.previewUrl}`,ue=()=>{G.value.resetFields(),A.value=!1,fe.value="",Y.value=[],ee.value=[],J.value=!1,Z.value={expName:"",expNotes:"",file:"",previewId:"",id:"",templateTags:[]},ie.value.setHTML(""),K.value+=1,ne.value.percent=0,ne.value.progressFlag=!1},ne=a({percent:0,progressFlag:!1}),de=e=>{e&&e.loaded&&e.total&&(ne.value.percent=Math.round(100*e.loaded/e.total))},me=e=>{const a=e.name;return".zip"!==a.substring(a.lastIndexOf("."))?(Y.value=[],f("error","请上传.zip格式的文件"),!1):(Y.value=[e],Z.value.file=e,!0)},ve=async e=>{const{file:a}=e;Y.value[0]=a,Z.value.file=a},ce=()=>{j({code:"SHI_LI_MO_BAN_DI_ZHI"}).then((e=>{var a,l;const t=null==(l=null==(a=e.data)?void 0:a.rows[0])?void 0:l.value;t?window.open(t,"_blank"):f("error","请先配置示例模板下载地址")}))},ge=e=>{Z.value.expNotes=e},fe=a(),he=async e=>{var a;const{file:l}=e;if(ee.value[0]=l,ee.value[0]){const e=new FormData;e.append("file",l),e.append("bucketName","twinfile");const t=await w(e);200===t.code?(Z.value.previewId=null==(a=t.data)?void 0:a.id,y(l,(e=>{fe.value=e}))):(f("error",t.message),Z.value.previewId="")}},je=()=>{Y.value=[],Z.value.file=""},we=()=>{Z.value.previewId=""};return O({init:async(e,a,l)=>{if(A.value=!0,ae=e,await new Promise((e=>{N().then((a=>{Q={};const l=a.data.map((e=>{var a;return Q[e.id]=e.tags,te.value.push(e.id),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(le.value.set(`${a.tagName}`,a.color),se.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));W.value=l.filter((e=>e.children)),e(W.value)}))})),"add"===e)Z.value={expName:"",expNotes:"",file:"",previewId:"",id:"",templateTags:[]},Y.value=[],ee.value=[],A.value=!0,c((()=>{G.value.resetFields()}));else if("edit"===e){Z.value.expName=a.name,Z.value.expNotes=a.remark,Z.value.file=a.url,Z.value.id=a.id,Z.value.previewId=a.previewId,Z.value.status=a.status,Z.value.templateTags=[];const e=[];a.functionExampleTags.forEach((a=>{se.value.get(a.tagId)&&e.push([se.value.get(a.tagId),a.tagId])})),c((()=>{ie.value.setHTML(Z.value.expNotes),Z.value.templateTags=e})),Y.value[0]={name:`${a.url.split("/")[a.url.split("/").length-2]}.zip`,uid:Math.floor(900*Math.random())+100},fe.value=pe(a)}}}),(e,a)=>{const c=M,j=T,w=F,y=E,N=q,O=U,C=L,K=H,te=l("question-circle-outlined"),se=$,pe=l("plus-outlined"),ye=z,be=I,xe=k,_e=D;return s(),t(_e,{width:800,title:"edit"===i(ae)?"编辑示例":"新增示例","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:A.value,"confirm-loading":J.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[5]||(a[5]=e=>(J.value=!0,void G.value.validate().then((()=>{const{expName:e,expNotes:a,file:l,previewId:t,id:s,templateTags:r,status:o}=Z.value,i=new FormData,p=[];r.forEach((e=>{if(e[1]){const{id:a,tagName:l,color:t}=Q[e[0]].find((a=>a.id===e[1]));p.push({tagId:a,tagName:l,color:t})}else e[0]&&Q[e[0]].forEach((e=>{const{id:a,tagName:l,color:t}=e;p.push({tagId:a,tagName:l,color:t})}))})),i.append("name",e),i.append("file",l),i.append("previewId",t),i.append("remark",a),i.append("sampleDetails",""),i.append("functionExampleValue",JSON.stringify(p)),i.append("status",o||"3"),i.append("createUser",h().userInfo.id),ne.value.percent=0,ne.value.progressFlag=!0,"add"===ae?x(i,de).then((e=>{200===e.code?(f("success","上传成功，请等待管理员审批"),J.value=!1,A.value=!1,P("ok")):(f("error",e.message),J.value=!1),ue(),ne.value.percent=0,ne.value.progressFlag=!1})).catch((()=>{J.value=!1,ne.value.percent=0,ne.value.progressFlag=!1})):("string"==typeof l&&i.delete("file"),i.append("id",s),ne.value.percent=0,ne.value.progressFlag=!0,_(i,de).then((e=>{200===e.code?(f("success","功能示例修改成功"),J.value=!1,A.value=!1,P("ok")):(f("error",e.message),J.value=!1),ue(),ne.value.percent=0,ne.value.progressFlag=!1})).catch((()=>{J.value=!1,ne.value.percent=0,ne.value.progressFlag=!1})))})).catch((e=>{J.value=!1})))),onCancel:ue},{default:r((()=>[o(xe,{spinning:J.value},{default:r((()=>[o(be,{ref_key:"formRef",ref:G,model:Z.value,rules:re,"label-align":"left"},{default:r((()=>[o(ye,{gutter:24},{default:r((()=>[o(w,{md:24,sm:24},{default:r((()=>[o(j,{name:"expName",label:"名称","has-feedback":""},{default:r((()=>[o(c,{value:Z.value.expName,"onUpdate:value":a[0]||(a[0]=e=>Z.value.expName=e),placeholder:"请输入示例名称",maxlength:30},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(j,{name:"templateTags",label:"标签","has-feedback":""},{default:r((()=>[o(N,{value:Z.value.templateTags,"onUpdate:value":a[1]||(a[1]=e=>Z.value.templateTags=e),defaultValue:Z.value.templateTags,"show-checked-strategy":i(q).SHOW_CHILD,style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:W.value,placeholder:"请选择标签",checkStrictly:!0},{tagRender:r((e=>{return[(s(),t(y,{key:e.value,color:(a=e.label,le.value.get(a)||"blue")},{default:r((()=>[p(u(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","options"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(j,{name:"expNotes",label:"示例说明","has-feedback":""},{default:r((()=>[o(i(b),{ref_key:"quillEditorRef",ref:ie,modelValue:Z.value.expNotes,"onUpdate:modelValue":a[2]||(a[2]=e=>Z.value.expNotes=e),modelModifiers:{content:!0},style:{height:"180px"},options:X.value,"content-type":"html","onUpdate:content":ge},null,8,["modelValue","options"])])),_:1})])),_:1}),o(w,{md:24,sm:24,class:"form-item"},{default:r((()=>[o(j,{name:"expFile",label:"示例文件","has-feedback":""},{default:r((()=>[o(C,{fileList:Y.value,"onUpdate:fileList":a[3]||(a[3]=e=>Y.value=e),"before-upload":me,"show-upload-list":!0,accept:".zip",multiple:!1,"max-count":1,"custom-request":ve,onRemove:je},{default:r((()=>[o(O,{type:"primary"},{default:r((()=>a[6]||(a[6]=[p(" 点击上传 ")]))),_:1})])),_:1},8,["fileList"]),d(n("div",null,[o(K,{percent:ne.value.percent,size:"small"},null,8,["percent"])],512),[[m,ne.value.progressFlag]])])),_:1}),n("span",V,[o(se,{placement:"right"},{title:r((()=>[n("div",null,[a[7]||(a[7]=p(" 支持文件格式: zip, 大小限制: 1000M, ")),n("a",{style:{color:"#ef7b1a"},onClick:ce},"下载示例模板")]),a[8]||(a[8]=n("div",null,[n("p",null,"上传格式如下图所示："),n("img",{src:g,style:{width:"100%"}})],-1))])),default:r((()=>[o(te,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),o(w,{md:24,sm:24,class:"form-item"},{default:r((()=>[o(j,{name:"previewId",label:"封面图","has-feedback":""},{default:r((()=>[o(C,{fileList:ee.value,"onUpdate:fileList":a[4]||(a[4]=e=>ee.value=e),"before-upload":oe,accept:".png, .jpg, .jpeg, .gif, .webp, .apng","show-upload-list":!1,"list-type":"picture-card",multiple:!1,"max-count":1,"custom-request":he,onRemove:we},{default:r((()=>[fe.value?(s(),v("img",{key:0,src:fe.value,alt:"avatar",class:"avatar-img"},null,8,R)):(s(),v("div",S,[o(pe),a[9]||(a[9]=n("div",{class:"ant-upload-text"},"上传",-1))]))])),_:1},8,["fileList"])])),_:1}),n("span",B,[o(se,{title:"支持文件格式: png、jpeg、jpg、gif, 大小限制: 5M",placement:"right"},{default:r((()=>[o(te,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-fd6c03dd"]]);export{C as default};
