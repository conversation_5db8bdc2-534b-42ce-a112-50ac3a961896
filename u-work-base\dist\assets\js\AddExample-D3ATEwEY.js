import{d as e,r as a,am as l,a9 as t,o as s,aa as i,c as r,u as o,J as p,ad as u,e as n,b as d,y as m,G as v,n as c}from"./@vue-HScy-mz9.js";import{_ as g}from"./chart-template-DJui4TvV.js";import{b as f,u as h,n as j,o as w}from"./main-Djn9RDyT.js";import{a as b,e as y,c as x}from"./chart-CjrRHupv.js";import"./@vueup-CLVdhRgW.js";import{S as _,F as N,_ as k,b as I,c as z,I as F,z as q,T,p as E,G as M,d as U,U as L,k as O,M as $}from"./ant-design-vue-DYY9BtJq.js";import{_ as D}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./quill-BBEhJLA6.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./js-binary-schema-parser-G48GG52R.js";const H=["src"],P={key:1},R={class:"form-item-notice keep-px"},S={class:"ant-upload-drag-icon"},C={class:"form-item-notice keep-px"},J=D(e({__name:"AddExample",emits:["ok"],setup(e,{expose:D,emit:J}){const V=J,A=a(!1),B=a(!1),G=a(),K=a({expName:"",expNotes:"",file:"",previewId:"",width:"",height:"",id:"",templateTags:[],status:0}),W=a(0);let Z={};const Q=a([]);a({modules:{clipboard:{matchers:[["img",(e,a)=>{const l=[];return a.ops.forEach((e=>{e.insert&&"string"==typeof e.insert&&l.push({insert:e.insert})})),a.ops=l,a}]]}},placeholder:"请输入图表说明",readOnly:!1,theme:"snow"});const X=a([]),Y=a([]);let ee="";const ae=a(new Map),le=a([]),te=a(new Map),se={templateTags:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let t=0;return a.forEach((e=>{if(1===e.length)if(le.value.includes(e[0])){const a=Q.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+e.length-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}],expName:[{required:!0,message:"请输入图表名称",trigger:"blur"}],expNotes:[{required:!0,message:"请输入图表说明",trigger:"blur"}],width:[{required:!0,message:"请输入图表宽度",trigger:"blur"}],height:[{required:!0,message:"请输入图表高度",trigger:"blur"}],expFile:[{required:!0,validator:e=>{const a=X.value[0];return new Promise(((e,l)=>{if(a){const t=a.name.split(".").pop().toLowerCase(),s=104857600;["zip"].includes(t)?a.size?a.size<=s?e():l(new Error("图表文件大小不能超过100MB")):e():l(new Error("只支持zip格式的文件"))}else l(new Error("请上传图表文件"))}))},trigger:"change"}],previewId:[{required:!0,message:"请上传封面图",trigger:"blur"}]},ie=e=>{const a="image/jpeg"===e.type||"image/png"===e.type||"image/jpg"===e.type||"image/gif"===e.type||"image/webp"===e.type||"image/apng"===e.type;a||f("error","支持上传.png, .jpg, .jpeg, .gif, .webp, .apng格式的图片");const l=e.size/1024/1024<5;return l||f("error","图片大小不超过5M"),a&&l};a();const re=e=>`${window.config.previewUrl}${e.previewUrl}`,oe=()=>{G.value.resetFields(),A.value=!1,me.value="",X.value=[],Y.value=[],B.value=!1,K.value={expName:"",expNotes:"",file:"",previewId:"",width:"",height:"",id:"",templateTags:[]},W.value+=1,pe.value.percent=0,pe.value.progressFlag=!1},pe=a({percent:0,progressFlag:!1}),ue=e=>{e&&e.loaded&&e.total&&(pe.value.percent=Math.round(100*e.loaded/e.total))},ne=e=>{const a=e.name;return".zip"!==a.substring(a.lastIndexOf("."))?(X.value=[],f("error","请上传.zip格式的文件"),!1):(X.value=[e],K.value.file=e,!0)},de=async e=>{const{file:a}=e;X.value[0]=a,K.value.file=a},me=a(),ve=async e=>{var a;const{file:l}=e;if(Y.value[0]=l,Y.value[0]){const e=new FormData;e.append("file",l),e.append("bucketName","twinfile");const t=await j(e);200===t.code?(K.value.previewId=null==(a=t.data)?void 0:a.id,w(l,(e=>{me.value=e}))):(f("error",t.message),K.value.previewId="")}},ce=()=>{X.value=[],K.value.file=""},ge=()=>{K.value.previewId=""};return D({init:async(e,a,l)=>{if(A.value=!0,ee=e,await new Promise((e=>{x().then((a=>{Z={};const l=a.data.map((e=>{var a;return Z[e.id]=e.tags,le.value.push(e.id),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(ae.value.set(`${a.tagName}`,a.color),te.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));Q.value=l.filter((e=>e.children)),e(Q.value)}))})),"add"===e)K.value={expName:"",expNotes:"",file:"",width:"",height:"",previewId:"",id:"",templateTags:[]},X.value=[],Y.value=[],A.value=!0,c((()=>{G.value.resetFields()}));else if("edit"===e){K.value.expName=a.name,K.value.expNotes=a.remark,K.value.file=a.url,K.value.id=a.id,K.value.width=Number(a.width),K.value.height=Number(a.height),K.value.previewId=a.previewId,K.value.status=a.status,K.value.templateTags=[];const e=[];a.functionExampleTags.forEach((a=>{te.value.get(a.tagId)&&e.push([te.value.get(a.tagId),a.tagId])})),c((()=>{K.value.templateTags=e})),X.value[0]={name:`${a.fileName}.zip`,uid:Math.floor(900*Math.random())+100},me.value=re(a)}}}),(e,a)=>{const c=F,j=z,w=I,x=T,D=q,J=E,W=l("plus-outlined"),le=M,te=l("question-circle-outlined"),re=U,fe=l("UploadOutlined"),he=L,je=O,we=k,be=N,ye=_,xe=$;return s(),t(xe,{width:800,title:"edit"===o(ee)?"编辑图表":"新增图表","body-style":{maxHeight:"800px",overflow:"auto"},"wrap-class-name":"cus-modal",open:A.value,"confirm-loading":B.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[6]||(a[6]=e=>(B.value=!0,void G.value.validate().then((()=>{const{expName:e,expNotes:a,file:l,previewId:t,id:s,templateTags:i,status:r,width:o,height:p}=K.value,u=new FormData,n=[];i.forEach((e=>{if(e[1]){const{id:a,tagName:l,color:t}=Z[e[0]].find((a=>a.id===e[1]));n.push({tagId:a,tagName:l,color:t})}else e[0]&&Z[e[0]].forEach((e=>{const{id:a,tagName:l,color:t}=e;n.push({tagId:a,tagName:l,color:t})}))})),u.append("name",e),u.append("width",o),u.append("height",p),u.append("file",l),u.append("previewId",t),u.append("remark",a),u.append("sampleDetails",""),u.append("functionExampleValue",JSON.stringify(n)),u.append("status",r||"0"),u.append("createUser",h().userInfo.id),pe.value.percent=0,pe.value.progressFlag=!0,"add"===ee?b(u,ue).then((e=>{200===e.code?(f("success","上传成功，请等待管理员审批"),B.value=!1,A.value=!1,V("ok")):(f("error",e.message),B.value=!1),oe(),pe.value.percent=0,pe.value.progressFlag=!1})).catch((()=>{B.value=!1,pe.value.percent=0,pe.value.progressFlag=!1})):("string"==typeof l&&u.delete("file"),u.append("id",s),pe.value.percent=0,pe.value.progressFlag=!0,y(u,ue).then((e=>{200===e.code?(f("success","图表模板修改成功"),B.value=!1,A.value=!1,V("ok")):(f("error",e.message),B.value=!1),oe(),pe.value.percent=0,pe.value.progressFlag=!1})).catch((()=>{B.value=!1,pe.value.percent=0,pe.value.progressFlag=!1})))})).catch((e=>{B.value=!1})))),onCancel:oe},{default:i((()=>[r(ye,{spinning:B.value},{default:i((()=>[r(be,{ref_key:"formRef",ref:G,model:K.value,rules:se,"label-align":"left"},{default:i((()=>[r(we,{gutter:24},{default:i((()=>[r(w,{md:24,sm:24},{default:i((()=>[r(j,{name:"expName",label:"名称","has-feedback":""},{default:i((()=>[r(c,{value:K.value.expName,"onUpdate:value":a[0]||(a[0]=e=>K.value.expName=e),placeholder:"请输入图表名称",maxlength:30},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:i((()=>[r(j,{name:"templateTags",label:"标签","has-feedback":""},{default:i((()=>[r(D,{value:K.value.templateTags,"onUpdate:value":a[1]||(a[1]=e=>K.value.templateTags=e),defaultValue:K.value.templateTags,"show-checked-strategy":o(q).SHOW_CHILD,style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:Q.value,placeholder:"请选择标签",checkStrictly:!0},{tagRender:i((e=>{return[(s(),t(x,{key:e.value,color:(a=e.label,ae.value.get(a)||"blue")},{default:i((()=>[p(u(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","options"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:i((()=>[r(j,{name:"width",label:"图表宽度","has-feedback":""},{default:i((()=>[r(J,{style:{width:"30%"},value:K.value.width,"onUpdate:value":a[2]||(a[2]=e=>K.value.width=e),placeholder:"请输入图表宽度",min:1},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:i((()=>[r(j,{name:"height",label:"图表高度","has-feedback":""},{default:i((()=>[r(J,{style:{width:"30%"},value:K.value.height,"onUpdate:value":a[3]||(a[3]=e=>K.value.height=e),placeholder:"请输入图表高度",min:1},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24,class:"form-item"},{default:i((()=>[r(j,{name:"previewId",label:"封面图","has-feedback":""},{default:i((()=>[r(le,{fileList:Y.value,"onUpdate:fileList":a[4]||(a[4]=e=>Y.value=e),"before-upload":ie,accept:".png, .jpg, .jpeg, .gif, .webp, .apng","show-upload-list":!1,"list-type":"picture-card",multiple:!1,"max-count":1,"custom-request":ve,onRemove:ge},{default:i((()=>[me.value?(s(),d("img",{key:0,src:me.value,alt:"avatar",class:"avatar-img"},null,8,H)):(s(),d("div",P,[r(W),a[7]||(a[7]=n("div",{class:"ant-upload-text"},"上传",-1))]))])),_:1},8,["fileList"])])),_:1}),n("span",R,[r(re,{title:"支持文件格式: png、jpeg、jpg、gif, 大小限制: 5M",placement:"right"},{default:i((()=>[r(te,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),r(w,{md:24,sm:24,class:"form-item"},{default:i((()=>[r(j,{name:"expFile",label:"图表文件","has-feedback":""},{default:i((()=>[r(he,{fileList:X.value,"onUpdate:fileList":a[5]||(a[5]=e=>X.value=e),"before-upload":ne,"show-upload-list":!0,accept:".zip",multiple:!1,"max-count":1,"custom-request":de,onRemove:ce},{default:i((()=>[n("a",S,[r(fe,{class:"UploadOutlined",style:{"font-size":"30px",color:"var(--upload-icon-color)"}})]),a[8]||(a[8]=n("p",{style:{"font-size":"14px"}},[p("将文件拖至此处，或点击 "),n("a",null,"上传文件")],-1)),a[9]||(a[9]=n("p",{style:{"font-size":"12px",color:"#ccc"},class:"ant-upload-hint"},"支持.zip且不超过100M的文件",-1))])),_:1},8,["fileList"]),m(n("div",null,[r(je,{percent:pe.value.percent,size:"small"},null,8,["percent"])],512),[[v,pe.value.progressFlag]])])),_:1}),n("span",C,[r(re,{placement:"right"},{title:i((()=>a[10]||(a[10]=[n("div",null,"支持文件格式: zip, 大小限制: 100M",-1),n("div",null,[n("p",null,"上传格式如下图所示："),n("img",{src:g,style:{width:"100%"}})],-1)]))),default:i((()=>[r(te,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-be490610"]]);export{J as default};
