import{d as e,r as a,a as l,a9 as o,o as s,aa as r,c as t,ab as i,J as n,y as u,e as d,u as p,G as m,n as v}from"./@vue-HScy-mz9.js";import{b as c}from"./main-Djn9RDyT.js";import{a as g,e as f}from"./pluginManage-DfKZgXJX.js";import{S as j,F as b,c as h,I as _,h as M,R as y,U as k,k as x,M as z}from"./ant-design-vue-DYY9BtJq.js";import{ab as F}from"./@ant-design-CA72ad83.js";import{_ as w}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const q={class:"ant-upload-drag-icon"},U=w(e({__name:"AddPlugin",emits:["ok"],setup(e,{expose:w,emit:U}){const I=U,O=a(!1),R=a(!1),X=a(),A=l({name:"",integrationMode:"1",url:"",code:"",version:""}),C=a(null),D=a(),J=a(""),S={name:[{required:!0,message:"请输入应用名称"},{max:32,message:"长度不能超过32个字符"}],integrationMode:[{required:!0,message:"请选择集成方式"}],url:[{required:!0,message:"请输入访问地址"}],code:[{required:!0,message:"请输入应用编码"},{max:32,message:"长度不能超过32个字符"}]},Z=()=>{A.code="",A.version="",A.url=""},B=()=>{D.value=[],A.name="",A.code="",A.version=""},E=()=>{A.name="",A.code="",A.version="",A.integrationMode="",A.url="",X.value.resetFields(),C.value=null,D.value=[],O.value=!1,R.value=!1},G=e=>{const a=e.file.name.substr(0,e.file.name.lastIndexOf("."));2!==a.split("_").length?(c("error","上传包的文件名格式错误,格式如：XXX_1.1.zip"),D.value=[],A.code="",A.version=""):"edit"===J.value&&a.split("_")[0]!==C.value.code?(c("error","应用编码编辑时不可修改，请重新按规定上传应用包"),D.value=[],A.code="",A.version=""):(D.value=[e.file],A.code=a.split("_")[0],A.version=a.split("_")[1])},H=a({percent:0,progressFlag:!1}),K=e=>{e&&e.loaded&&e.total&&(H.value.percent=Math.round(100*e.loaded/e.total))},L=e=>{const a=e.name,l=a.substring(a.lastIndexOf("."));return".zip"===l||".js"===l||(D.value=[],c("error","请上传.zip,.js格式的文件"),!1)};return w({init:(e,a)=>{O.value=!0,J.value=e,v((()=>{a?(C.value=a,A.name=a.name,A.integrationMode=a.integrationMode.toString(),A.url=a.url,A.code=a.code,A.version=a.version):X.value.resetFields()}))}}),(e,a)=>{const l=_,v=h,w=y,U=M,N=k,P=x,T=b,$=j,Q=z;return s(),o(Q,{width:460,title:"edit"===J.value?"编辑应用":"新增应用","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:O.value,"confirm-loading":R.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[5]||(a[5]=e=>(R.value=!0,void X.value.validate().then((()=>{const e=new FormData;if(e.append("name",A.name),e.append("integrationMode",A.integrationMode),e.append("code",A.code),"add"===J.value){if("1"===A.integrationMode){if(!(D.value&&D.value.length>0))return c("error","请选择上传子应用包"),void(R.value=!1);e.append("file",D.value[0]),e.append("version",A.version)}else e.append("url",A.url);H.value.percent=0,H.value.progressFlag=!0,g(e,K).then((e=>{R.value=!1,200===e.code?(c("success","子应用上传成功"),I("ok"),E()):c("error",e.message)})).catch((()=>{H.value.percent=0,H.value.progressFlag=!1})).finally((()=>{R.value=!1,H.value.percent=0,H.value.progressFlag=!1}))}else{if("1"===A.integrationMode?D.value&&D.value.length>0&&(e.append("file",D.value[0]),e.append("version",A.version)):e.append("url",A.url),C.value.code!==A.code)return c("error","应用编码编辑时不可修改"),void(R.value=!1);e.append("id",C.value.id),H.value.percent=0,H.value.progressFlag=!0,f(e,K).then((e=>{R.value=!1,200===e.code?(c("success","更改成功"),E(),I("ok")):c("success",e.message)})).catch((()=>{H.value.percent=0,H.value.progressFlag=!1})).finally((()=>{R.value=!1,H.value.percent=0,H.value.progressFlag=!1}))}})).catch((e=>{R.value=!1})))),onCancel:E},{default:r((()=>[t($,{spinning:R.value},{default:r((()=>[t(T,{ref_key:"formRef",ref:X,model:A,rules:S,"label-align":"left"},{default:r((()=>[t(v,{name:"name",label:"应用名称","has-feedback":""},{default:r((()=>[t(l,{value:A.name,"onUpdate:value":a[0]||(a[0]=e=>A.name=e),placeholder:"请输入应用名称",maxlength:32},null,8,["value"])])),_:1}),t(v,{name:"integrationMode",label:"集成方式","has-feedback":""},{default:r((()=>[t(U,{value:A.integrationMode,"onUpdate:value":a[1]||(a[1]=e=>A.integrationMode=e),disabled:"edit"===J.value,onChange:Z},{default:r((()=>[t(w,{value:"1"},{default:r((()=>a[6]||(a[6]=[n(" 子应用 ")]))),_:1}),t(w,{value:"0"},{default:r((()=>a[7]||(a[7]=[n(" iframe嵌套 ")]))),_:1})])),_:1},8,["value","disabled"])])),_:1}),"1"===A.integrationMode?(s(),o(v,{key:0,label:" "},{default:r((()=>[t(N,{name:"pictre","custom-request":G,"show-upload-list":!0,multiple:!1,"before-upload":L,accept:".js,.zip","file-list":D.value,onRemove:B},{default:r((()=>[d("p",q,[t(p(F),{style:{"font-size":"40px",color:"var(--upload-icon-color)"}})]),a[8]||(a[8]=d("p",{style:{"font-size":"14px"}},"点击上传应用包",-1))])),_:1},8,["file-list"]),u(d("div",null,[t(P,{percent:H.value.percent,size:"small"},null,8,["percent"])],512),[[m,H.value.progressFlag]])])),_:1})):i("",!0),t(v,{name:"code",label:"应用编码","has-feedback":""},{default:r((()=>[t(l,{value:A.code,"onUpdate:value":a[2]||(a[2]=e=>A.code=e),placeholder:"请输入应用编码",maxlength:32,disabled:"1"===A.integrationMode||"edit"===J.value},null,8,["value","disabled"])])),_:1}),"1"===A.integrationMode?(s(),o(v,{key:1,name:"version",label:"应用版本","has-feedback":""},{default:r((()=>[t(l,{value:A.version,"onUpdate:value":a[3]||(a[3]=e=>A.version=e),placeholder:"请输入应用版本",maxlength:32,disabled:"1"===A.integrationMode||"edit"===J.value},null,8,["value","disabled"])])),_:1})):i("",!0),"0"===A.integrationMode?(s(),o(v,{key:2,name:"url",label:"访问地址","has-feedback":""},{default:r((()=>[t(l,{value:A.url,"onUpdate:value":a[4]||(a[4]=e=>A.url=e),placeholder:"请输入访问地址"},null,8,["value"])])),_:1})):i("",!0)])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-c6df320b"]]);export{U as default};
