import{d as e,r as a,f as l,am as t,a9 as s,o as u,aa as r,c as o,y as d,e as i,ab as n,J as c,a4 as v,u as p,ad as m,G as f,b as g,F as h,ag as b,n as j}from"./@vue-HScy-mz9.js";import{a as _}from"./axios-7z2hFSF6.js";import{M as y,N as k,g as w,e as C,b as D}from"./main-Djn9RDyT.js";import{v as x,i as M,a as S,b as U,l as I,g as N}from"./projectGallery-xT8wgNPG.js";import{S as Y,F as $,b as F,c as q,w as V,x as z,I as P,B as L,y as R,z as A,T as O,O as E,_ as G,i as T,P as B,Q as H,d as J,M as K}from"./ant-design-vue-DYY9BtJq.js";import{_ as Q}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const W={class:"step-item"},X={class:"i-box"},Z={style:{display:"flex","align-items":"center"}},ee={class:"i-box"},ae={class:"step-item"},le={style:{display:"flex","align-items":"center"}},te={class:"step-item"},se={class:"upload-wrap"},ue=["accept"],re={class:"file-list"},oe=["onClick"],de={class:"form-item-notice keep-px"},ie={class:"upload-wrap"},ne=["accept"],ce={class:"file-list"},ve=["onClick"],pe={class:"form-item-notice keep-px"},me=Q(e({__name:"AddProject",emits:["ok"],setup(e,{expose:Q,emit:me}){const fe=a(!1),ge=()=>{Ge.value=!1,xe.value.resetFields()},he=a(1),be=a(!1),je=a(1),_e=a([]),ye=async()=>{const e=Ue.value.contractCode;if(e)try{const a=await k({tenantCode:e}),{tenant:l,token:t}=a.data;_.get(`${window.config.appApi}/systemx/project-pack/page`,{params:{pageNo:1,pageSize:99},headers:{Tenant:e,Authorization:`Bearer ${t}`,"X-Security-FreshToken":w()}}).then((e=>{var a,l;const t=(null==(l=null==(a=e.data)?void 0:a.data)?void 0:l.rows)||[];t.length&&(_e.value=t.map((e=>({label:e.name,value:ke(e.url)}))))})).catch((()=>{_e.value=[]}))}catch(a){_e.value=[]}},ke=e=>`${window.location.origin}/osr/resource/${e}index.html`;l((()=>{C({code:"MING_DAO_YUN_TONG_BU"}).then((e=>{var a,l;"是"===(null==(l=null==(a=e.data)?void 0:a.rows[0])?void 0:l.value)?be.value=!0:(be.value=!1,he.value=0)})).catch((()=>{be.value=!1,he.value=0}))}));const we=()=>{De.value||je.value--},Ce=()=>{1===je.value&&xe.value.validate().then((()=>{je.value++})).catch((()=>{})),2===je.value&&Me.value.validate().then((()=>{je.value++})).catch((()=>{}))},De=a(!1),xe=a(),Me=a(),Se=a(),Ue=a({projectName:"",contractCode:"",productName:"",productVersion:"",techPlatform:"",saleDepartment:"",projectManager:"",salesManager:"",saleSupporter:"",industry:"",ultimateCustomer:"",contractedCustomer:"",projectStartDate:"",projectEndDate:"",projectStatus:"",screenResolution:"",projectAccessLinker:"",projectSimpleDescription:"",overallDemand:"",projectScope:"",functionalDecompositio:"",projectPracticalSummary:"",deliverablesImages:[],deliverablesVideo:[],tagIds:[]}),Ie={contractCode:[{required:!0,message:"请输入合同编号"}],projectName:[{required:!0,message:"请输入项目名称"}],techPlatform:[{required:!0,message:"请输入采纳的技术平台"}],saleDepartment:[{required:!0,message:"请输入所属部门"}],projectManager:[{required:!0,message:"请输入项目经理"}],salesManager:[{required:!0,message:"请输入销售经理"}],projectStatus:[{required:!0,message:"请输入项目状态"}],projectAccessLinker:[{required:!1,validator:y}],deliverablesImages:[{required:!0,message:"请上传图片",trigger:"change"}],tagIds:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let t=0;return a.forEach((e=>{if(1===e.length)if(Fe.value.includes(e[0])){const a=Ne.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+e.length-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}]},Ne=a([]),Ye=a(new Map),$e=a(new Map),Fe=a([]),qe=a(new Map),Ve=a(["mp4","MP4"]),ze=async e=>{const a=e.target;if(Ue.value.deliverablesVideo.length>=10)return D("warning","最多支持上传10个文件"),a.value="",!1;const l=a.files[0],{size:t,name:s}=l,u=s.lastIndexOf("."),r=s.slice(u+1).toLocaleLowerCase();if(!Ve.value.includes(r))return D("warning",`文件后缀必须是${Ve.value.join("/")}`),a.value="",!1;if(t>1073741824)return D("warning","文件大小不能超过1024M"),a.value="",!1;a.value="",De.value=!0;try{const e=new FormData;e.append("file",l),e.append("bucket","edtp-source"),e.append("replaceName","false");const a=await x(e);200===a.code?(De.value=!1,Ue.value.deliverablesVideo.push(a.data),D("success","视频上传成功")):(De.value=!1,D("error",a.message))}catch(o){De.value=!1}},Pe=a(!1),Le=a(),Re=(e,a)=>{Pe.value=e,"string"==typeof a&&(Le.value=Ae(a))},Ae=e=>`${e}`,Oe=a(["png","jpg","jpeg","gif"]),Ee=async e=>{const a=e.target;if(Ue.value.deliverablesImages.length>=10)return D("warning","最多支持上传10个文件"),a.value="",!1;const l=a.files[0],{size:t,name:s}=l,u=s.lastIndexOf("."),r=s.slice(u+1).toLocaleLowerCase();if(!Oe.value.includes(r))return D("warning",`文件后缀必须是${Oe.value.join("/")}`),a.value="",!1;if(t>5242880)return D("warning","文件大小不能超过5M"),a.value="",!1;a.value="",De.value=!0;try{const e=new FormData;e.append("file",l),e.append("bucket","edtp-source"),e.append("replaceName","false");const a=await M(e);200===a.code?(Ue.value.deliverablesImages.push(a.data),Se.value.validateFields(["deliverablesImages"]),D("success","图片上传成功"),De.value=!1):(D("error",a.message),De.value=!1)}catch(o){De.value=!1}},Ge=a(!1),Te=((e,a)=>{let l;return function(...t){l||(e.apply(this,t),l=setTimeout((()=>{l=null}),a))}})((()=>{N(Ue.value.contractCode).then((e=>{200===e.code?(Object.keys(e.data).forEach((a=>{if(e.data[a]&&(Ue.value[a]=e.data[a]),("productName"===a||"industry"===a)&&e.data[a]&&$e.value.size){const l=$e.value.get(e.data[a]);l&&Ue.value.tagIds.push(l.split("-"))}})),e.data.contractCode?(Ge.value=!0,xe.value.clearValidate()):D("error","合同编号不存在")):D("error",e.message)}))}),1e3),Be=a(["",""]),He=()=>{De.value||(xe.value.resetFields(),Me.value.resetFields(),Se.value.resetFields(),_e.value=[],fe.value=!1,Ge.value=!1)},Je=()=>{let e=[];return Ue.value.tagIds.forEach((a=>{1===a.length?e=e.concat(qe.value.get(a[0])):e.push(a[1])})),e},Ke=()=>{De.value||Se.value.validate().then((async()=>{var e;const a={...Ue.value,projectPracticalSummary:JSON.stringify(Be.value),tagIds:Je()},l=await S(a);if(200!==l.code)return D("error",l.message),void(De.value=!1);const t=await U({id:null==(e=l.data)?void 0:e.id,approve:1,approveRemark:""});if(200!==t.code)return D("error",t.message),void(De.value=!1);De.value=!1,D("success","新增项目案例成功，请等待管理员审批"),He()})).catch((e=>{De.value=!1}))};return Q({init:()=>{fe.value=!0,je.value=1,I().then((e=>{const a=e.data.map((e=>{var a;return Fe.value.push(e.id),qe.value.set(e.id,[]),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(qe.value.get(e.id).push(a.id),Ye.value.set(`${a.tagName}`,a.color),$e.value.set(`${a.tagName}`,`${e.id}-${a.id}`),{value:a.id,label:`${a.tagName}`})))}}));Ne.value=a.filter((e=>e.children))})),j((()=>{xe.value.resetFields(),Me.value.resetFields(),Se.value.resetFields(),_e.value=[],Ue.value.tagIds=[],Ue.value.deliverablesImages=[],Ue.value.deliverablesVideo=[],Be.value=["",""]}))}}),(e,a)=>{const l=z,j=V,_=q,y=P,k=L,w=F,C=R,D=O,x=A,M=E,S=$,U=T,I=B,N=G,Q=H,me=t("DeleteOutlined"),ke=t("question-circle-outlined"),$e=J,Fe=Y,qe=K;return u(),s(qe,{width:1200,title:"新增项目案例","body-style":{maxHeight:"700px",overflow:"auto"},"wrap-class-name":"cus-modal",open:fe.value,"confirm-loading":De.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[25]||(a[25]=e=>Ke()),onCancel:He},{footer:r((()=>[i("div",null,[d(o(k,{type:"primary",onClick:we},{default:r((()=>a[31]||(a[31]=[c(" 上一步 ")]))),_:1},512),[[f,je.value>1]]),d(o(k,{type:"primary",onClick:Ce},{default:r((()=>a[32]||(a[32]=[c(" 下一步 ")]))),_:1},512),[[f,je.value<3]]),o(k,{onClick:He},{default:r((()=>a[33]||(a[33]=[c(" 取消 ")]))),_:1}),d(o(k,{type:"primary",onClick:Ke},{default:r((()=>a[34]||(a[34]=[c(" 确认 ")]))),_:1},512),[[f,3===je.value]])])])),default:r((()=>[o(Fe,{spinning:De.value,style:{position:"fixed",top:"250px"}},{default:r((()=>[o(S,{ref_key:"form1Ref",ref:xe,model:Ue.value,rules:Ie,"label-align":"left"},{default:r((()=>[d(i("div",W,[i("div",X,[o(w,{md:24,sm:24},{default:r((()=>[o(_,{class:"project-code-item keep-px",name:"contractCode",label:"合同编号","has-feedback":""},{default:r((()=>[i("div",Z,[o(_,{style:{"margin-bottom":"-24px"}},{default:r((()=>[be.value?(u(),s(j,{key:0,placeholder:"请选择",onChange:ge,value:he.value,"onUpdate:value":a[0]||(a[0]=e=>he.value=e),style:{width:"100px",padding:"0"}},{default:r((()=>[o(l,{value:1},{default:r((()=>a[26]||(a[26]=[c("同步")]))),_:1}),o(l,{value:0},{default:r((()=>a[27]||(a[27]=[c("自定义")]))),_:1})])),_:1},8,["value"])):n("",!0)])),_:1}),o(y,{value:Ue.value.contractCode,"onUpdate:value":a[1]||(a[1]=e=>Ue.value.contractCode=e),maxlength:20,disabled:Ge.value,onBlur:ye,style:v({width:be.value&&1===he.value?"calc(100% - 180px)":be.value&&0===he.value?"calc(100% - 80px)":"100%"}),placeholder:"请输入合同编号"},null,8,["value","disabled","style"]),be.value&&1===he.value?(u(),s(k,{key:0,disabled:!!Ue.value.contractCode&&Ge.value,style:{"min-width":"80px",padding:"0"},type:"primary",onClick:p(Te)},{default:r((()=>a[28]||(a[28]=[c("同步")]))),_:1},8,["disabled","onClick"])):n("",!0)])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"productName",label:"产品信息(合同)","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.productName,"onUpdate:value":a[2]||(a[2]=e=>Ue.value.productName=e),maxlength:30,placeholder:"请输入产品信息(合同)"},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"techPlatform",label:"采纳的技术平台","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.techPlatform,"onUpdate:value":a[3]||(a[3]=e=>Ue.value.techPlatform=e),maxlength:30,placeholder:"请输入采纳的技术平台"},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"projectManager",label:"项目经理","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.projectManager,"onUpdate:value":a[4]||(a[4]=e=>Ue.value.projectManager=e),disabled:1===he.value,placeholder:"请输入项目经理"},null,8,["value","disabled"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"saleSupporter",label:"支持售前","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.saleSupporter,"onUpdate:value":a[5]||(a[5]=e=>Ue.value.saleSupporter=e),maxlength:30,placeholder:"请输入支持售前"},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"ultimateCustomer",label:"最终客户名称","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.ultimateCustomer,"onUpdate:value":a[6]||(a[6]=e=>Ue.value.ultimateCustomer=e),maxlength:30,placeholder:"请输入最终客户名称"},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"projectStartDate",label:"项目启动时间","has-feedback":""},{default:r((()=>[o(C,{value:Ue.value.projectStartDate,"onUpdate:value":a[7]||(a[7]=e=>Ue.value.projectStartDate=e),disabled:1===he.value,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",placeholder:"请选择项目启动时间"},null,8,["value","disabled"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"projectStatus",label:"项目状态","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.projectStatus,"onUpdate:value":a[8]||(a[8]=e=>Ue.value.projectStatus=e),disabled:1===he.value,placeholder:"请输入项目状态"},null,8,["value","disabled"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"tagIds",label:"资源标签","has-feedback":""},{default:r((()=>[o(x,{value:Ue.value.tagIds,"onUpdate:value":a[9]||(a[9]=e=>Ue.value.tagIds=e),"show-checked-strategy":p(A).SHOW_CHILD,style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:Ne.value,placeholder:"请选择标签"},{tagRender:r((e=>{return[(u(),s(D,{key:e.value,color:(a=e.label,Ye.value.get(a)||"blue"),title:""},{default:r((()=>[c(m(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","show-checked-strategy","options"])])),_:1})])),_:1})]),i("div",ee,[o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"projectName",label:"项目名称","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.projectName,"onUpdate:value":a[10]||(a[10]=e=>Ue.value.projectName=e),disabled:1===he.value,title:Ue.value.projectName,placeholder:"请输入项目名称"},null,8,["value","disabled","title"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"productVersion",label:"产品版本","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.productVersion,"onUpdate:value":a[11]||(a[11]=e=>Ue.value.productVersion=e),maxlength:30,placeholder:"请输入产品版本"},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"saleDepartment",label:"所属部门(销售)","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.saleDepartment,"onUpdate:value":a[12]||(a[12]=e=>Ue.value.saleDepartment=e),disabled:1===he.value,placeholder:"请输入所属部门(销售)"},null,8,["value","disabled"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"salesManager",label:"销售经理","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.salesManager,"onUpdate:value":a[13]||(a[13]=e=>Ue.value.salesManager=e),disabled:1===he.value,placeholder:"请输入销售经理"},null,8,["value","disabled"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"industry",label:"行业","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.industry,"onUpdate:value":a[14]||(a[14]=e=>Ue.value.industry=e),disabled:1===he.value,placeholder:"请输入行业"},null,8,["value","disabled"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"contractedCustomer",label:"签约客户名称","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.contractedCustomer,"onUpdate:value":a[15]||(a[15]=e=>Ue.value.contractedCustomer=e),disabled:1===he.value,placeholder:"请输入签约客户名称"},null,8,["value","disabled"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"projectEndDate",label:"项目完成时间","has-feedback":""},{default:r((()=>[o(C,{value:Ue.value.projectEndDate,"onUpdate:value":a[16]||(a[16]=e=>Ue.value.projectEndDate=e),disabled:1===he.value,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",placeholder:"请选择项目完成时间"},null,8,["value","disabled"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"screenResolution",label:"屏幕分辨率","has-feedback":""},{default:r((()=>[o(y,{value:Ue.value.screenResolution,"onUpdate:value":a[17]||(a[17]=e=>Ue.value.screenResolution=e),maxlength:30,placeholder:"请输入屏幕分辨率"},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"projectAccessLinker",label:"项目地址","has-feedback":""},{default:r((()=>[o(M,{value:Ue.value.projectAccessLinker,"onUpdate:value":a[18]||(a[18]=e=>Ue.value.projectAccessLinker=e),options:_e.value,placeholder:"请输入项目地址"},null,8,["value","options"])])),_:1})])),_:1})])],512),[[f,1===je.value]])])),_:1},8,["model"]),o(S,{ref_key:"form2Ref",ref:Me,model:Ue.value,rules:Ie,"label-align":"left"},{default:r((()=>[d(i("div",ae,[o(N,{gutter:24},{default:r((()=>[o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"projectSimpleDescription",label:"一句话简介"},{default:r((()=>[o(U,{value:Ue.value.projectSimpleDescription,"onUpdate:value":a[19]||(a[19]=e=>Ue.value.projectSimpleDescription=e),autoSize:{minRows:4,maxRows:4},placeholder:"(在什么样的背景下)利用了什么产品，整合了什么资源，形成了什么类型的平台，为用户带来了什么样的价值。"},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"proSituation",label:"项目整体需求情况"},{default:r((()=>[o(U,{value:Ue.value.overallDemand,"onUpdate:value":a[20]||(a[20]=e=>Ue.value.overallDemand=e),rows:4},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"projectScope",label:"项目范围"},{default:r((()=>[o(U,{value:Ue.value.projectScope,"onUpdate:value":a[21]||(a[21]=e=>Ue.value.projectScope=e),rows:4,placeholder:"请输入项目的交付范围（精炼版sow）"},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"functionalDecompositio",label:"功能介绍分析"},{default:r((()=>[o(U,{value:Ue.value.functionalDecompositio,"onUpdate:value":a[22]||(a[22]=e=>Ue.value.functionalDecompositio=e),rows:4,placeholder:"业务场景分解描述，成果需确认无敏感内容"},null,8,["value"])])),_:1})])),_:1}),o(w,{md:24,sm:24},{default:r((()=>[o(_,{name:"successM",label:"项目实战经验总结"},{default:r((()=>[o(I,null,{default:r((()=>[i("div",le,[o(U,{value:Be.value[0],"onUpdate:value":a[23]||(a[23]=e=>Be.value[0]=e),style:{width:"100%","margin-right":"1%"},rows:4,placeholder:"项目最成功的1-5个因素"},null,8,["value"]),o(U,{value:Be.value[1],"onUpdate:value":a[24]||(a[24]=e=>Be.value[1]=e),style:{width:"100%"},rows:4,placeholder:"项目最痛苦的1-5个因素"},null,8,["value"])])])),_:1})])),_:1})])),_:1})])),_:1})],512),[[f,2===je.value]])])),_:1},8,["model"]),o(S,{ref_key:"form3Ref",ref:Se,model:Ue.value,rules:Ie,"label-align":"left"},{default:r((()=>[d(i("div",te,[o(N,{gutter:24},{default:r((()=>[o(w,{md:24,sm:24,class:"form-item"},{default:r((()=>[o(_,{name:"deliverablesImages",label:"上传图片","has-feedback":""},{default:r((()=>[i("div",se,[o(k,{class:"upload-btn",type:"primary"},{default:r((()=>[a[29]||(a[29]=c(" 点击上传 ")),i("input",{type:"file",class:"input-file",accept:Oe.value.map((e=>`.${e}`)).join(","),onChange:Ee},null,40,ue)])),_:1})]),i("div",re,[(u(!0),g(h,null,b(Ue.value.deliverablesImages,((e,a)=>(u(),g("div",{key:e,class:"file-item"},[i("div",{class:"file-name",onClick:a=>Re(!0,e)},[o(Q,{width:48,height:48,src:Ae(e)},null,8,["src"]),c(m(e.replace("/osr/edtp-source/projectGallery/","")),1)],8,oe),o(me,{class:"file-icon",onClick:e=>(e=>{Ue.value.deliverablesImages.splice(e,1)})(a)},null,8,["onClick"])])))),128)),o(Q,{width:200,style:{display:"none"},preview:{visible:Pe.value,onVisibleChange:Re},src:Le.value},null,8,["preview","src"])])])),_:1}),i("span",de,[o($e,{title:"支持文件格式: png、jpeg、jpg、gif, 大小限制: 5M",placement:"right"},{default:r((()=>[o(ke,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),o(w,{md:24,sm:24,class:"form-item"},{default:r((()=>[o(_,{name:"deliverablesVideo",label:"上传视频","has-feedback":""},{default:r((()=>[i("div",ie,[o(k,{class:"upload-btn",type:"primary"},{default:r((()=>[a[30]||(a[30]=c(" 点击上传 ")),i("input",{type:"file",class:"input-file",accept:Ve.value.map((e=>`.${e}`)).join(","),onChange:ze},null,40,ne)])),_:1})]),i("div",ce,[(u(!0),g(h,null,b(Ue.value.deliverablesVideo,((e,a)=>(u(),g("div",{key:e,class:"file-item"},[i("div",{class:"file-name",onClick:a=>(e=>{window.open(Ae(e),"_blank")})(e)},m(e.replace("/osr/edtp-source/projectGallery/","")),9,ve),o(me,{class:"file-icon",onClick:e=>(e=>{Ue.value.deliverablesVideo.splice(e,1)})(a)},null,8,["onClick"])])))),128))])])),_:1}),i("span",pe,[o($e,{title:"支持文件格式: mp4, 大小限制:1024M",placement:"right"},{default:r((()=>[o(ke,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1})],512),[[f,3===je.value]])])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-b461cf58"]]);export{me as default};
