import{b as e}from"./main-Djn9RDyT.js";import{i as a,j as t}from"./operationAnalysis-D3RTU-GI.js";import{S as s,F as l,_ as o,b as r,c as i,I as u,i as m,M as p}from"./ant-design-vue-DYY9BtJq.js";import{d,r as n,f as v,a9 as c,o as j,aa as f,c as y,n as g}from"./@vue-HScy-mz9.js";import{_ as h}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const b=h(d({__name:"AddProject",emits:["ok"],setup(d,{expose:h,emit:b}){const _={style:{width:"250px"}},w=n(!1),S=n(!1),k=n(),x=n({operateSystem:"",url:"",id:""});n([]);const z={operateSystem:[{required:!0,message:"请填写操作系统！",trigger:"blur"}],url:[{required:!0,message:"请填写安装包说明！",trigger:"blur"}]},F=n("");v((()=>{x.value={operateSystem:"",url:""}}));n([]);const q=()=>{k.value.resetFields(),w.value=!1,S.value=!1,x.value.operateSystem="",x.value.url=""},A=b;return h({init:(e,a)=>{w.value=!0,F.value=e,g((()=>{k.value.resetFields(),"add"===e?x.value={operateSystem:"",url:""}:"edit"!==e&&"check"!==e||!a||(x.value.url=a.url,x.value.operateSystem=a.operateSystem,x.value.id=a.id)}))}}),(d,n)=>{const v=u,g=i,h=r,b=m,D=o,I=l,M=s,U=p;return j(),c(U,{width:676,title:"add"===F.value?"上传安装包":"check"===F.value?"查看安装包":"编辑安装包","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:w.value,"confirm-loading":S.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:n[2]||(n[2]=s=>(S.value=!0,void k.value.validate().then((()=>{const s=new FormData,{operateSystem:l,url:o,id:r}=x.value;s.append("url",o),s.append("operateSystem",l),"add"!==F.value&&s.append("id",r),"add"===F.value?a(s).then((a=>{200===a.code?(e("success",("add"===F.value?"安装包新增":"安装包修改")+"成功"),q(),A("ok","add")):S.value=!1})).catch((()=>{S.value=!1})).finally((()=>{S.value=!1})):"edit"===F.value&&t(s).then((a=>{200===a.code?(e("success",("add"===F.value?"安装包新增":"安装包修改")+"成功"),q(),A("ok","add")):S.value=!1})).catch((()=>{S.value=!1})).finally((()=>{S.value=!1}))})).catch((e=>{S.value=!1})))),onCancel:q},{default:f((()=>[y(M,{spinning:S.value},{default:f((()=>[y(I,{ref_key:"formRef",ref:k,model:x.value,rules:z,"label-align":"left","label-col":_},{default:f((()=>[y(D,{gutter:24},{default:f((()=>[y(h,{md:24,sm:24},{default:f((()=>[y(g,{name:"operateSystem",label:"操作系统","has-feedback":""},{default:f((()=>[y(v,{value:x.value.operateSystem,"onUpdate:value":n[0]||(n[0]=e=>x.value.operateSystem=e),rows:4,placeholder:"请输入操作系统",maxlength:300},null,8,["value"])])),_:1})])),_:1}),y(h,{md:24,sm:24},{default:f((()=>[y(g,{name:"url",label:"安装包说明"},{default:f((()=>[y(b,{value:x.value.url,"onUpdate:value":n[1]||(n[1]=e=>x.value.url=e),rows:4,placeholder:"请填写安装包说明","allow-clear":"",maxlength:1500},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["title","open","confirm-loading"])}}}),[["__scopeId","data-v-8db295e9"]]);export{b as default};
