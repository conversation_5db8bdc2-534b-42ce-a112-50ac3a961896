import{d as e,r as a,f as l,a9 as t,aa as s,n as u,c as r,y as o,G as d,e as i,ab as n,J as c,a4 as v,u as p,ad as m,b as f,ag as g,am as h,F as b,o as j}from"./@vue-HScy-mz9.js";import{a as _}from"./axios-7z2hFSF6.js";import{d as y}from"./dayjs-CA7qlNSr.js";import{u as k,b as w,M as C,e as x,N as D,g as I,v as M}from"./main-Djn9RDyT.js";import{a as S,b as U,v as N,i as Y,g as E}from"./projectGallery-xT8wgNPG.js";import{M as F,F as T,b as L,c as q,w as A,x as P,I as V,B as $,y as z,z as O,T as R,O as B,_ as H,i as G,P as J,Q as K,d as Q,S as W}from"./ant-design-vue-DYY9BtJq.js";import{_ as X}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const Z={class:"step-item"},ee={class:"i-box"},ae={style:{display:"flex","align-items":"center"}},le={class:"i-box"},te={class:"step-item"},se={style:{display:"flex","justify-content":"space-between"}},ue={class:"step-item"},re={class:"upload-wrap"},oe=["accept"],de={class:"file-list"},ie=["onClick"],ne={class:"form-item-notice keep-px"},ce={class:"upload-wrap"},ve=["accept"],pe={class:"file-list"},me=["onClick"],fe={class:"form-item-notice keep-px"},ge=X(e({__name:"AddProject",emits:["ok"],setup(e,{expose:X,emit:ge}){const he=k(),be=ge,je=a(!1),_e=()=>{Ke.value=!1,Ue.value.resetFields()},ye=a(1),ke=a(!1),we=a(1),Ce=a([]),xe=async()=>{const e=Ee.value.contractCode;if(e)try{const a=await D({tenantCode:e}),{tenant:l,token:t}=a.data;_.get(`${window.config.appApi}/systemx/project-pack/page`,{params:{pageNo:1,pageSize:99},headers:{Tenant:e,Authorization:`Bearer ${t}`,"X-Security-FreshToken":I()}}).then((e=>{var a,l;const t=(null==(l=null==(a=e.data)?void 0:a.data)?void 0:l.rows)||[];t.length&&(Ce.value=t.map((e=>({label:e.name,value:De(e.url)}))))})).catch((()=>{Ce.value=[]}))}catch(a){Ce.value=[]}},De=e=>`${window.location.origin}/osr/resource/${e}index.html`;l((()=>{}));const Ie=()=>{we.value--},Me=()=>{1===we.value&&Ue.value.validate().then((()=>{we.value++})).catch((()=>{})),2===we.value&&Ne.value.validate().then((()=>{we.value++})).catch((()=>{}))},Se=a(!1),Ue=a(),Ne=a(),Ye=a(),Ee=a({projectName:"",projectId:"",contractCode:"",productName:"",productVersion:"",techPlatform:"",saleDepartment:"",projectManager:"",salesManager:"",saleSupporter:"",industry:"",ultimateCustomer:"",contractedCustomer:"",projectStartDate:"",projectEndDate:"",projectStatus:"",screenResolution:"",projectAccessLinker:"",projectSimpleDescription:"",overallDemand:"",projectScope:"",functionalDecompositio:"",projectPracticalSummary:"",deliverablesImages:[],deliverablesVideo:[],tagIds:[]}),Fe={contractCode:[{required:!0,message:"请输入合同编号"}],projectName:[{required:!0,message:"请输入项目名称"}],techPlatform:[{required:!0,message:"请输入采纳的技术平台"}],saleDepartment:[{required:!0,message:"请输入所属部门"}],projectManager:[{required:!0,message:"请输入项目经理"}],salesManager:[{required:!0,message:"请输入销售经理"}],projectStatus:[{required:!0,message:"请输入项目状态"}],projectAccessLinker:[{required:!1,validator:C}],deliverablesImages:[{required:!0,message:"请上传图片",trigger:"change"}],tagIds:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let t=0;return a.forEach((e=>{if(1===e.length)if(Ae.value.includes(e[0])){const a=Te.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+e.length-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}]},Te=a([]),Le=a(new Map),qe=a(new Map),Ae=a([]),Pe=a(new Map),Ve=(e,a)=>a.some((a=>a.label.toLowerCase().indexOf(e.toLowerCase())>-1)),$e=a(["mp4","MP4"]),ze=async e=>{const a=e.target;if(Ee.value.deliverablesVideo.length>=10)return w("warning","最多支持上传10个文件"),a.value="",!1;const l=a.files[0],{size:t,name:s}=l,u=s.lastIndexOf("."),r=s.slice(u+1).toLocaleLowerCase();if(!$e.value.includes(r))return w("warning",`文件后缀必须是${$e.value.join("/")}`),a.value="",!1;if(t>1073741824)return w("warning","文件大小不能超过1024M"),a.value="",!1;a.value="",Se.value=!0;const o=new FormData;o.append("file",l),o.append("bucket","edtp-source"),o.append("replaceName","false");const d=await N(o);200===d.code?(Se.value=!1,Ee.value.deliverablesVideo.push(d.data),w("success","视频上传成功")):(Se.value=!1,w("error",d.message))},Oe=a(!1),Re=a(),Be=(e,a)=>{Oe.value=e,"string"==typeof a&&(Re.value=He(a))},He=e=>e?`${e}`:"",Ge=a(["png","jpg","jpeg","gif"]),Je=async e=>{const a=e.target;if(Ee.value.deliverablesImages.length>=10)return w("warning","最多支持上传10个文件"),a.value="",!1;const l=a.files[0],{size:t,name:s}=l,u=s.lastIndexOf("."),r=s.slice(u+1).toLocaleLowerCase();if(!Ge.value.includes(r))return w("warning",`文件后缀必须是${Ge.value.join("/")}`),a.value="",!1;if(t>5242880)return w("warning","文件大小不能超过5M"),a.value="",!1;a.value="",Se.value=!0;const o=new FormData;o.append("file",l),o.append("bucket","edtp-source"),o.append("replaceName","false");const d=await Y(o);200===d.code?(Ee.value.deliverablesImages.push(d.data),Ye.value.validateFields(["deliverablesImages"]),w("success","图片上传成功"),Se.value=!1):(w("error",d.message),Se.value=!1)},Ke=a(!1),Qe=((e,a)=>{let l;return function(...t){l||(e.apply(this,t),l=setTimeout((()=>{l=null}),a))}})((()=>{E(Ee.value.contractCode).then((e=>{200===e.code?(Object.keys(e.data).forEach((a=>{if(e.data[a]&&(Ee.value[a]=e.data[a]),("productName"===a||"industry"===a)&&e.data[a]&&qe.value.size){const l=qe.value.get(e.data[a]);l&&Ee.value.tagIds.push(l.split("-"))}})),e.data.contractCode?(Ke.value=!0,Ue.value.clearValidate()):w("error","合同编号不存在")):w("error",e.message)}))}),1e3),We=a(["",""]),Xe=a(new Map);let Ze={};const ea=e=>{const a=(Number(y(e))-Number(y()))/1e3;return Math.round(a)},aa=a({}),la=async e=>{await new Promise((e=>{M().then((a=>{Ze={};const l=a.data.map((e=>{var a;return Ze[e.id]=e.tags,Ae.value.push(e.id),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(Le.value.set(`${a.tagName}`,a.color),Xe.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));Te.value=l.filter((e=>e.children)),e(Te.value)}))}));const a=[];e.functionExampleTags.forEach((e=>{a.push([Xe.value.get(e.tagId),e.tagId])})),u((()=>{Ee.value.tagIds=a})),aa.value={code:e.code,name:e.name,migrationType:e.migrationType,sceneCount:e.sceneCount,userCount:e.userCount,licenseExpiredTime:y(e.licenseExpiredTime).format("YYYY-MM-DD HH:mm:ss"),licenseTotalTime:ea(e.licenseExpiredTime),id:e.id,remark:e.remark,previewId:e.previewId,country:e.country,province:e.province,city:e.city,district:e.district,street:e.street,formattedAddress:e.formattedAddress,lnglat:e.lnglat,functionExampleTags:a,userId:he.userInfo.id,classified:e.classified,enterpriseId:e.enterpriseId}},ta=()=>{Se.value||(Ue.value.resetFields(),Ne.value.resetFields(),Ye.value.resetFields(),Ce.value=[],je.value=!1,Ke.value=!1)},sa=()=>{let e=[];return Ee.value.tagIds.forEach((a=>{1===a.length?e=e.concat(Pe.value.get(a[0])):e.push(a[1])})),e},ua=()=>{Se.value||Ye.value.validate().then((async()=>{var e;const a={...Ee.value,projectPracticalSummary:JSON.stringify(We.value),tagIds:sa()},l=await S(a);if(200!==l.code)return w("error",l.message),void(Se.value=!1);const t=await U({id:null==(e=l.data)?void 0:e.id,approve:1,approveRemark:""});if(200!==t.code)return w("error",t.message),void(Se.value=!1);Se.value=!1,w("success","关联项目案例成功，请等待管理员审批"),ta(),be("ok")})).catch((e=>{Se.value=!1}))};return X({init:e=>{je.value=!0,we.value=1,la(e),u((()=>{Ue.value.resetFields(),Ne.value.resetFields(),Ye.value.resetFields(),Ee.value.tagIds=[],Ce.value=[],Ee.value.contractCode=e.code,Ee.value.projectId=e.id,Ee.value.projectAccessLinker="",Ee.value.deliverablesImages=[],Ee.value.deliverablesVideo=[],We.value=["",""],xe()})),x({code:"MING_DAO_YUN_TONG_BU"}).then((e=>{var a,l;"是"===(null==(l=null==(a=e.data)?void 0:a.rows[0])?void 0:l.value)?ke.value=!0:(ke.value=!1,ye.value=0)})).catch((()=>{ke.value=!1,ye.value=0}))}}),(e,a)=>{const l=P,u=A,_=q,y=V,k=$,w=L,C=z,x=R,D=O,I=B,M=T,S=G,U=J,N=H,Y=K,E=h("DeleteOutlined"),X=h("question-circle-outlined"),ge=Q,he=W,be=F;return j(),t(be,{width:1200,title:"新增项目案例","body-style":{maxHeight:"700px",overflow:"auto"},"wrap-class-name":"cus-modal",open:je.value,"confirm-loading":Se.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[25]||(a[25]=e=>ua()),onCancel:ta},{footer:s((()=>[i("div",null,[o(r(k,{type:"primary",onClick:Ie},{default:s((()=>a[31]||(a[31]=[c(" 上一步 ")]))),_:1},512),[[d,we.value>1]]),o(r(k,{type:"primary",onClick:Me},{default:s((()=>a[32]||(a[32]=[c(" 下一步 ")]))),_:1},512),[[d,we.value<3]]),r(k,{onClick:ta},{default:s((()=>a[33]||(a[33]=[c(" 取消 ")]))),_:1}),o(r(k,{type:"primary",onClick:ua},{default:s((()=>a[34]||(a[34]=[c(" 确认 ")]))),_:1},512),[[d,3===we.value]])])])),default:s((()=>[r(he,{spinning:Se.value,style:{position:"fixed",top:"250px"}},{default:s((()=>[r(M,{ref_key:"form1Ref",ref:Ue,model:Ee.value,rules:Fe,"label-align":"left"},{default:s((()=>[o(i("div",Z,[i("div",ee,[r(w,{md:24,sm:24},{default:s((()=>[r(_,{class:"project-code-item keep-px",name:"contractCode",label:"合同编号","has-feedback":""},{default:s((()=>[i("div",ae,[r(_,{style:{"margin-bottom":"-24px"}},{default:s((()=>[ke.value?(j(),t(u,{key:0,placeholder:"请选择",onChange:_e,value:ye.value,"onUpdate:value":a[0]||(a[0]=e=>ye.value=e),style:{width:"100px",padding:"0"}},{default:s((()=>[r(l,{value:1},{default:s((()=>a[26]||(a[26]=[c("同步")]))),_:1}),r(l,{value:0},{default:s((()=>a[27]||(a[27]=[c("自定义")]))),_:1})])),_:1},8,["value"])):n("",!0)])),_:1}),r(y,{value:Ee.value.contractCode,"onUpdate:value":a[1]||(a[1]=e=>Ee.value.contractCode=e),maxlength:20,disabled:Ke.value,onBlur:xe,style:v({width:ke.value&&1===ye.value?"calc(100% - 180px)":ke.value&&0===ye.value?"calc(100% - 80px)":"100%"}),placeholder:"请输入合同编号"},null,8,["value","disabled","style"]),ke.value&&1===ye.value?(j(),t(k,{key:0,disabled:!!Ee.value.contractCode&&Ke.value,style:{"min-width":"80px",padding:"0"},type:"primary",onClick:p(Qe)},{default:s((()=>a[28]||(a[28]=[c("同步")]))),_:1},8,["disabled","onClick"])):n("",!0)])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"productName",label:"产品信息(合同)","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.productName,"onUpdate:value":a[2]||(a[2]=e=>Ee.value.productName=e),maxlength:30,placeholder:"请输入产品信息(合同)"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"techPlatform",label:"采纳的技术平台","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.techPlatform,"onUpdate:value":a[3]||(a[3]=e=>Ee.value.techPlatform=e),maxlength:30,placeholder:"请输入采纳的技术平台"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"projectManager",label:"项目经理","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.projectManager,"onUpdate:value":a[4]||(a[4]=e=>Ee.value.projectManager=e),disabled:1===ye.value,placeholder:"请输入项目经理"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"saleSupporter",label:"支持售前","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.saleSupporter,"onUpdate:value":a[5]||(a[5]=e=>Ee.value.saleSupporter=e),maxlength:30,placeholder:"请输入支持售前"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"ultimateCustomer",label:"最终客户名称","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.ultimateCustomer,"onUpdate:value":a[6]||(a[6]=e=>Ee.value.ultimateCustomer=e),maxlength:30,placeholder:"请输入最终客户名称"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"projectStartDate",label:"项目启动时间","has-feedback":""},{default:s((()=>[r(C,{value:Ee.value.projectStartDate,"onUpdate:value":a[7]||(a[7]=e=>Ee.value.projectStartDate=e),disabled:1===ye.value,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",placeholder:"请选择项目启动时间"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"projectStatus",label:"项目状态","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.projectStatus,"onUpdate:value":a[8]||(a[8]=e=>Ee.value.projectStatus=e),disabled:1===ye.value,placeholder:"请输入项目状态"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"tagIds",label:"资源标签","has-feedback":""},{default:s((()=>[r(D,{value:Ee.value.tagIds,"onUpdate:value":a[9]||(a[9]=e=>Ee.value.tagIds=e),"show-checked-strategy":p(O).SHOW_CHILD,"show-search":{filter:Ve},style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:Te.value,placeholder:"请选择标签"},{tagRender:s((e=>{return[(j(),t(x,{key:e.value,color:(a=e.label,Le.value.get(a)||"blue")},{default:s((()=>[c(m(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","show-checked-strategy","show-search","options"])])),_:1})])),_:1})]),i("div",le,[r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"projectName",label:"项目名称","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.projectName,"onUpdate:value":a[10]||(a[10]=e=>Ee.value.projectName=e),disabled:1===ye.value,title:Ee.value.projectName,placeholder:"请输入项目名称"},null,8,["value","disabled","title"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"productVersion",label:"产品版本","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.productVersion,"onUpdate:value":a[11]||(a[11]=e=>Ee.value.productVersion=e),maxlength:30,placeholder:"请输入产品版本"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"saleDepartment",label:"所属部门(销售)","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.saleDepartment,"onUpdate:value":a[12]||(a[12]=e=>Ee.value.saleDepartment=e),disabled:1===ye.value,placeholder:"请输入所属部门(销售)"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"salesManager",label:"销售经理","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.salesManager,"onUpdate:value":a[13]||(a[13]=e=>Ee.value.salesManager=e),disabled:1===ye.value,placeholder:"请输入销售经理"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"industry",label:"行业","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.industry,"onUpdate:value":a[14]||(a[14]=e=>Ee.value.industry=e),disabled:1===ye.value,placeholder:"请输入行业"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"contractedCustomer",label:"签约客户名称","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.contractedCustomer,"onUpdate:value":a[15]||(a[15]=e=>Ee.value.contractedCustomer=e),disabled:1===ye.value,placeholder:"请输入签约客户名称"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"projectEndDate",label:"项目完成时间","has-feedback":""},{default:s((()=>[r(C,{value:Ee.value.projectEndDate,"onUpdate:value":a[16]||(a[16]=e=>Ee.value.projectEndDate=e),disabled:1===ye.value,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",placeholder:"请选择项目完成时间"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"screenResolution",label:"屏幕分辨率","has-feedback":""},{default:s((()=>[r(y,{value:Ee.value.screenResolution,"onUpdate:value":a[17]||(a[17]=e=>Ee.value.screenResolution=e),maxlength:30,placeholder:"请输入屏幕分辨率"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"projectAccessLinker",label:"项目地址","has-feedback":""},{default:s((()=>[r(I,{value:Ee.value.projectAccessLinker,"onUpdate:value":a[18]||(a[18]=e=>Ee.value.projectAccessLinker=e),options:Ce.value,placeholder:"请输入项目地址"},null,8,["value","options"])])),_:1})])),_:1})])],512),[[d,1===we.value]])])),_:1},8,["model"]),r(M,{ref_key:"form2Ref",ref:Ne,model:Ee.value,rules:Fe,"label-align":"left"},{default:s((()=>[o(i("div",te,[r(N,{gutter:24},{default:s((()=>[r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"projectSimpleDescription",label:"一句话简介"},{default:s((()=>[r(S,{value:Ee.value.projectSimpleDescription,"onUpdate:value":a[19]||(a[19]=e=>Ee.value.projectSimpleDescription=e),rows:4,placeholder:"(在什么样的背景下)利用了什么产品，整合了什么资源，形成了什么类型的平台，为用户带来了什么样的价值。"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"proSituation",label:"项目整体需求情况"},{default:s((()=>[r(S,{value:Ee.value.overallDemand,"onUpdate:value":a[20]||(a[20]=e=>Ee.value.overallDemand=e),rows:4},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"projectScope",label:"项目范围"},{default:s((()=>[r(S,{value:Ee.value.projectScope,"onUpdate:value":a[21]||(a[21]=e=>Ee.value.projectScope=e),rows:4,placeholder:"请输入项目的交付范围（精炼版sow）"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"functionalDecompositio",label:"功能介绍分析"},{default:s((()=>[r(S,{value:Ee.value.functionalDecompositio,"onUpdate:value":a[22]||(a[22]=e=>Ee.value.functionalDecompositio=e),rows:4,placeholder:"业务场景分解描述，成果需确认无敏感内容"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:s((()=>[r(_,{name:"successM",label:"项目实战经验总结"},{default:s((()=>[r(U,null,{default:s((()=>[i("div",se,[r(S,{value:We.value[0],"onUpdate:value":a[23]||(a[23]=e=>We.value[0]=e),style:{width:"49.5%","margin-right":"1%"},rows:4,placeholder:"项目最成功的1-5个因素"},null,8,["value"]),r(S,{value:We.value[1],"onUpdate:value":a[24]||(a[24]=e=>We.value[1]=e),style:{width:"49.5%"},rows:4,placeholder:"项目最痛苦的1-5个因素"},null,8,["value"])])])),_:1})])),_:1})])),_:1})])),_:1})],512),[[d,2===we.value]])])),_:1},8,["model"]),r(M,{ref_key:"form3Ref",ref:Ye,model:Ee.value,rules:Fe,"label-align":"left"},{default:s((()=>[o(i("div",ue,[r(N,{gutter:24},{default:s((()=>[r(w,{md:24,sm:24,class:"form-item"},{default:s((()=>[r(_,{name:"deliverablesImages",label:"上传图片","has-feedback":""},{default:s((()=>[i("div",re,[r(k,{class:"upload-btn",type:"primary"},{default:s((()=>[a[29]||(a[29]=c(" 点击上传 ")),i("input",{type:"file",class:"input-file",accept:Ge.value.map((e=>`.${e}`)).join(","),onChange:Je},null,40,oe)])),_:1})]),i("div",de,[(j(!0),f(b,null,g(Ee.value.deliverablesImages,((e,a)=>(j(),f("div",{key:e,class:"file-item"},[i("div",{class:"file-name",onClick:a=>Be(!0,e)},[r(Y,{width:48,height:48,src:He(e)},null,8,["src"]),c(" "+m(e),1)],8,ie),r(E,{class:"file-icon",onClick:e=>(e=>{Ee.value.deliverablesImages.splice(e,1)})(a)},null,8,["onClick"])])))),128)),r(Y,{width:200,style:{display:"none"},preview:{visible:Oe.value,onVisibleChange:Be},src:Re.value},null,8,["preview","src"])])])),_:1}),i("span",ne,[r(ge,{title:"支持文件格式: png、jpeg、jpg、gif, 大小限制: 5M",placement:"right"},{default:s((()=>[r(X,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),r(w,{md:24,sm:24,class:"form-item"},{default:s((()=>[r(_,{name:"deliverablesVideo",label:"上传视频","has-feedback":""},{default:s((()=>[i("div",ce,[r(k,{class:"upload-btn",type:"primary"},{default:s((()=>[a[30]||(a[30]=c(" 点击上传 ")),i("input",{type:"file",class:"input-file",accept:$e.value.map((e=>`.${e}`)).join(","),onChange:ze},null,40,ve)])),_:1})]),i("div",pe,[(j(!0),f(b,null,g(Ee.value.deliverablesVideo,((e,a)=>(j(),f("div",{key:e,class:"file-item"},[i("div",{class:"file-name",onClick:a=>(e=>{window.open(He(e),"_blank")})(e)},m(e),9,me),r(E,{class:"file-icon",onClick:e=>(e=>{Ee.value.deliverablesVideo.splice(e,1)})(a)},null,8,["onClick"])])))),128))])])),_:1}),i("span",fe,[r(ge,{title:"支持文件格式: mp4, 大小限制:1024M",placement:"right"},{default:s((()=>[r(X,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1})],512),[[d,3===we.value]])])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-60921f39"]]);export{ge as default};
