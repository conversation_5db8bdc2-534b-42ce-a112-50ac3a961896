import{_ as e}from"./vue-qr-CB2aNKv5.js";import{b as a,o as l,e as t,d as s,r as i,a as n,am as c,c as o,aa as p,a9 as r,u as d,K as u,J as h}from"./@vue-HScy-mz9.js";import{a as v}from"./axios-7z2hFSF6.js";import{b as f}from"./main-Djn9RDyT.js";import{Z as m,U as g,$ as C,B as L,I as y,a0 as k}from"./ant-design-vue-DYY9BtJq.js";const x={class:"sparkle-button keep-px"};const _=e({},[["render",function(e,s){return l(),a("div",x,s[0]||(s[0]=[t("button",{class:"active"},[t("span",{class:"spark"}),t("span",{class:"backdrop"}),t("svg",{class:"sparkle",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[t("path",{d:"M14.187 8.096L15 5.25L15.813 8.096C16.0231 8.83114 16.4171 9.50062 16.9577 10.0413C17.4984 10.5819 18.1679 10.9759 18.903 11.186L21.75 12L18.904 12.813C18.1689 13.0231 17.4994 13.4171 16.9587 13.9577C16.4181 14.4984 16.0241 15.1679 15.814 15.903L15 18.75L14.187 15.904C13.9769 15.1689 13.5829 14.4994 13.0423 13.9587C12.5016 13.4181 11.8321 13.0241 11.097 12.814L8.25 12L11.096 11.187C11.8311 10.9769 12.5006 10.5829 13.0413 10.0423C13.5819 9.50162 13.9759 8.83214 14.186 8.097L14.187 8.096Z",fill:"black",stroke:"black","stroke-linecap":"round","stroke-linejoin":"round"}),t("path",{d:"M6 14.25L5.741 15.285C5.59267 15.8785 5.28579 16.4206 4.85319 16.8532C4.42059 17.2858 3.87853 17.5927 3.285 17.741L2.25 18L3.285 18.259C3.87853 18.4073 4.42059 18.7142 4.85319 19.1468C5.28579 19.5794 5.59267 20.1215 5.741 20.715L6 21.75L6.259 20.715C6.40725 20.1216 6.71398 19.5796 7.14639 19.147C7.5788 18.7144 8.12065 18.4075 8.714 18.259L9.75 18L8.714 17.741C8.12065 17.5925 7.5788 17.2856 7.14639 16.853C6.71398 16.4204 6.40725 15.8784 6.259 15.285L6 14.25Z",fill:"black",stroke:"black","stroke-linecap":"round","stroke-linejoin":"round"}),t("path",{d:"M6.5 4L6.303 4.5915C6.24777 4.75718 6.15472 4.90774 6.03123 5.03123C5.90774 5.15472 5.75718 5.24777 5.5915 5.303L5 5.5L5.5915 5.697C5.75718 5.75223 5.90774 5.84528 6.03123 5.96877C6.15472 6.09226 6.24777 6.24282 6.303 6.4085L6.5 7L6.697 6.4085C6.75223 6.24282 6.84528 6.09226 6.96877 5.96877C7.09226 5.84528 7.24282 5.75223 7.4085 5.697L8 5.5L7.4085 5.303C7.24282 5.24777 7.09226 5.15472 6.96877 5.03123C6.84528 4.90774 6.75223 4.75718 6.697 4.5915L6.5 4Z",fill:"black",stroke:"black","stroke-linecap":"round","stroke-linejoin":"round"})]),t("span",{class:"text"},"AI识图"),t("span",{class:"beta"},[t("svg",{t:"1701231689528",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"4255",width:"32",height:"32"},[t("path",{d:"M511.107677 501.222539c-24.585955 1.376347-37.904285 13.669324-39.952944 36.879955l78.881558 0C548.670178 514.892887 535.693632 502.599909 511.107677 501.222539z",fill:"#d81e06","p-id":"4256"}),t("path",{d:"M771.311931 586.250074c-0.682545 10.926864 7.853875 16.391319 25.610284 16.391319 30.05041-2.048659 46.440705-16.391319 49.172933-43.025932l0-11.268648c-8.878205 5.474688-22.537296 10.244318-40.977273 14.341636C782.581602 566.786791 771.311931 574.640666 771.311931 586.250074z",fill:"#d81e06","p-id":"4257"}),t("path",{d:"M327.734276 505.32088c-22.537296 1.376347-34.147728 14.693654-34.830273 39.952944l0 14.341636c0.682545 25.269523 11.951193 38.58683 33.805944 39.952944 23.903409 0.682545 35.854603-16.038278 35.854603-50.197262C361.882004 521.370415 350.271572 506.697227 327.734276 505.32088z",fill:"#d81e06","p-id":"4258"}),t("path",{d:"M896.519584 304.875662 225.495754 304.875662c-35.270295 0-63.86454 \r\n                        28.593222-63.86454 63.865563l0 227.13509-84.044138 76.517721 0.213871 0.703011c-7.286963 4.436032-12.410658 \r\n                        12.094456-12.410658 21.249977 0 13.96199 11.317767 25.279756 25.279756 25.279756 0.444115 0 0.814552-0.230244 1.25355-0.25378l0.075725 0.25378 133.496433 0 103.557564 0 567.466266 0c35.270295 0 63.86454-28.593222 63.86454-63.86454L960.384124 368.740202C960.3831 333.468884 931.789878 304.875662 896.519584 304.875662zM341.052606 631.325689c-21.171182 0-36.879955-8.195659-47.12325-24.585955l0 20.488637-44.050262 0L249.879094 401.853367l44.050262 0 0 96.296183c10.244318-17.756409 27.318182-26.635637 51.221592-26.635637 40.293705 2.048659 61.124126 27.659967 62.49024 76.831876C407.640164 603.665723 385.443629 631.325689 341.052606 631.325689zM513.156336 631.325689c-54.636365-2.048659-82.978876-27.659967-85.027535-76.831876 2.048659-53.270251 29.025057-80.930217 80.930217-82.978876 55.31891 0 82.978876 27.659967 82.978876 82.978876l0 10.244318L472.179063 564.738132c1.366114 23.220864 15.025205 35.513842 40.977273 36.878932 18.439978 0 30.391171-6.819313 \r\n                        35.854603-20.488637l46.098921 5.121648C582.817905 616.300484 555.499723 631.325689 \r\n                        513.156336 631.325689zM679.114089 631.325689c-31.4155 0.672312-46.782489-14.693654-46.098921-46.098921l0-77.856205-25.610284 0 \r\n                        0-32.781614 25.610284 0 0-51.221592 42.001603-9.219989 0 60.441581 36.879955 0 0 32.781614-36.879955 0 0 68.63724c0 15.719007 6.487761 23.561625 19.464307 23.561625 4.780887 0 9.219989-1.366114 13.317307-4.097318l9.219989 27.659967C708.822714 628.593462 696.187953 631.325689 679.114089 631.325689zM852.242148 627.227348c-4.097318-6.147-6.487761-13.659091-7.17133-22.537296-14.341636 17.062608-36.538171 25.952068-66.587558 26.635637-30.732955-2.048659-47.124274-16.391319-49.172933-43.025932 0-28.000728 19.122523-44.392046 57.367569-49.172933 30.05041-6.147 48.831148-11.268648 56.343239-15.365966 1.366114-15.70775-9.219989-23.209608-31.757285-22.537296-19.122523 0-31.074739 7.17133-35.854603 \r\n                        21.512966l-40.977273-6.147c8.195659-30.05041 33.465183-45.074591 75.807546-45.074591 52.587706-1.366114 77.856205 20.146852 75.807546 64.538899 0 15.025205 0 35.18229 0 60.441581 0 12.292977 2.732228 22.537296 8.195659 30.732955L852.242148 627.228371z",fill:"#d81e06","p-id":"4259"})])])],-1)]))}],["__scopeId","data-v-dcd2c75e"]]),b={class:"ai-search"},w={class:"ai-search-content"},z={class:"ai-search-item"},j={class:"ant-upload-drag-icon"},M={class:"ai-search-item"},T={class:"search-btn"},U={class:"ai-search-content"},S={class:"ai-search-item"},$={class:"ai-search-item"},I={class:"search-btn"},Z=e(s({__name:"AiSearch",props:{url:{type:String,default:""}},emits:["aiSearch"],setup(e,{emit:s}){const x=v.create(),_=s,Z=e,G=i(!1),K=i(1),V=()=>{G.value=!0;const e=new FormData;e.append("top_n","12"),e.append("search_type","0"),e.append("positive_threshold","10"),e.append("negative_threshold","10"),e.append("positive",`${B.searchText}`),e.append("negative",`${B.filterText}`),x.post(`${Z.url}/api/match`,e).then((e=>{G.value=!1;const a=e.data;if(null==a?void 0:a.length){const e=a.map((e=>e.name));(null==e?void 0:e.length)&&_("aiSearch",e)}else f("info","无匹配图表")})).catch((()=>{G.value=!1}))},A=()=>{G.value=!0;const e=new FormData;e.append("file",J.value[0]),e.append("top_n","12"),e.append("search_type","1"),e.append("image_threshold",`${B.sliderValue}`),x.post(`${Z.url}/api/match`,e).then((e=>{G.value=!1;const a=e.data;if(null==a?void 0:a.length){const e=a.map((e=>e.name));(null==e?void 0:e.length)&&_("aiSearch",e)}else f("info","无匹配图表")})).catch((()=>{G.value=!1}))},B=n({searchText:"",filterText:"",sliderValue:75}),F=e=>{const a="image/jpeg"===e.type||"image/png"===e.type||"image/jpg"===e.type||"image/gif"===e.type;a||f("error","支持上传.png, .jpg, .jpeg, .gif格式的图片");const l=e.size/1024/1024<10;return l||f("error","图片大小不超过10M"),a&&l},J=i([]),P=async e=>{const{file:a}=e;J.value[0]=a},q=()=>{J.value=[]};return(e,s)=>{const i=c("file-image-outlined"),n=c("UploadOutlined"),v=g,f=C,x=L,_=m,Z=c("font-size-outlined"),D=y,O=k;return l(),a("div",b,[o(O,{activeKey:d(K),"onUpdate:activeKey":s[4]||(s[4]=e=>u(K)?K.value=e:null),centered:""},{default:p((()=>[(l(),r(_,{key:1},{tab:p((()=>[t("span",null,[o(i,{style:{"font-size":"14px"}}),s[5]||(s[5]=h(" 以图搜表 "))])])),default:p((()=>[t("div",w,[t("div",z,[o(v,{fileList:d(J),"onUpdate:fileList":s[0]||(s[0]=e=>u(J)?J.value=e:null),style:{width:"100%"},"before-upload":F,accept:".png, .jpg, .jpeg, .gif",multiple:!1,maxCount:1,"custom-request":P,onRemove:q},{default:p((()=>[t("a",j,[o(n,{class:"UploadOutlined",style:{"font-size":"30px",color:"var(--upload-icon-color)"}})]),s[6]||(s[6]=t("p",{style:{"font-size":"14px"}},[h("将图片拖至此处，或点击 "),t("a",null,"上传图片")],-1)),s[7]||(s[7]=t("p",{style:{"font-size":"12px",color:"#ccc"},class:"ant-upload-hint"},"支持JPG、JPEG、PNG、GIF不超过10M的图片",-1))])),_:1},8,["fileList"])]),t("div",M,[s[8]||(s[8]=h("匹配阈值：")),o(f,{value:d(B).sliderValue,"onUpdate:value":s[1]||(s[1]=e=>d(B).sliderValue=e),style:{width:"140px"},min:0,max:100},null,8,["value"]),s[9]||(s[9]=h("高于该分数才显示"))]),t("div",T,[o(x,{type:"primary",loading:d(G),shape:"circle",onClick:A},{default:p((()=>s[10]||(s[10]=[h(" 搜索 ")]))),_:1},8,["loading"])])])])),_:1})),(l(),r(_,{key:2},{tab:p((()=>[t("span",null,[o(Z,{style:{"font-size":"14px"}}),s[11]||(s[11]=h(" 文字搜索 "))])])),default:p((()=>[t("div",U,[t("div",S,[o(D,{value:d(B).searchText,"onUpdate:value":s[2]||(s[2]=e=>d(B).searchText=e),placeholder:"请输入搜索关键字",style:{width:"100%"}},null,8,["value"])]),t("div",$,[o(D,{value:d(B).filterText,"onUpdate:value":s[3]||(s[3]=e=>d(B).filterText=e),placeholder:"请输入需要过滤内容,无过滤内容可不输入",style:{width:"100%"}},null,8,["value"])]),t("div",I,[o(x,{type:"primary",loading:d(G),shape:"circle",onClick:V},{default:p((()=>s[12]||(s[12]=[h(" 搜索 ")]))),_:1},8,["loading"])])])])),_:1}))])),_:1},8,["activeKey"])])}}}),[["__scopeId","data-v-9a02125b"]]);export{Z as A,_ as S};
