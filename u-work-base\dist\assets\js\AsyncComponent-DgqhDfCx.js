import{V as e}from"./vue-BVcE9bLt.js";import{i as s}from"./vue3-sfc-loader-BPS_8u5b.js";import{S as t,s as a,a as r}from"./swiper-CfJ8YdI9.js";import{U as n}from"./echarts-PHL8p6cd.js";import"./echarts-gl-Cssen4G3.js";import"./echarts-liquidfill-CFZelQOA.js";import{c as o}from"./sass-C4uXr8HK.js";import{E as c}from"./element-plus-DGm4IBRH.js";import{d as l}from"./dayjs-CA7qlNSr.js";import{a3 as i}from"./ant-design-vue-DYY9BtJq.js";import{l as u}from"./lodash-Cz2B5noN.js";import{a6 as m}from"./@ant-design-CA72ad83.js";import{Q as p}from"./@element-plus-BuZNC1gX.js";import{s as d,w as f,b as g,o as h,a9 as w,ab as y,ac as j}from"./@vue-HScy-mz9.js";const v={class:"async-component"},S={__name:"AsyncComponent",props:{url:String},setup(S){const b=S,x=d(null),C=["echarts-gl","swiper/css","echarts-liquidfill","element-plus/dist/index.css","ant-design-vue/dist/reset.css"],E=(e,s)=>{let t=e;const a=[...t.matchAll(/url\(["']?([^"')]+)["']?\)/g)];return(null==a?void 0:a.length)?(a.forEach((e=>{const a=e[1];if(a){const e=new URL(a,s).href;t=t.replace(a,e)}})),t):t};return f((()=>b.url),(d=>{d&&(async d=>{let f="";try{const g={moduleCache:Object.assign(Object.create(null),{vue:e,echarts:n,"swiper/vue":r,"swiper/modules":a,Swiper:t,"element-plus":c,dayjs:l,"ant-design-vue":i,lodash:u,"@ant-design/icons-vue":m,"@element-plus/icons-vue":p}),async getFile(e){const s=await fetch(e);if(!s.ok)throw Object.assign(new Error(`${s.statusText}  ${e}`),{res:s});return{getContentData:async t=>t?s.arrayBuffer():(await s.text()).replaceAll("import.meta.url",`'${e}'`)}},async processStyles(e,s,t,a){if("sass"!==s&&"scss"!==s)throw new Error(`unsupported "${s}" style processor`);const r={canonicalize:e=>new URL(e,"file:"),load:async e=>{const s=a.getResource({refPath:t,relPath:e.pathname},a),r=await s.getContent();return{contents:await r.getContentData(!1),syntax:r.type.slice(1)}}};try{return(await o(e,{importers:[r]})).css}catch(n){return void a.log("error",n.message)}},pathResolve:({refPath:e,relPath:s},t)=>(f=e,"."===s?e:"."!==s[0]&&"/"!==s[0]?s:String(new URL(s,void 0===e?window.location:e))),addStyle(e){const s=E(e,f),t=Object.assign(document.createElement("style"),{textContent:s}),a=document.head.getElementsByTagName("style")[0]||null;document.head.insertBefore(t,a)},handleModule:async function(e,s,t,a){if(C.some((e=>t.toString().endsWith(e))))return null;switch(e){case".css":return a.addStyle(await s(!1)),null;case".svg":case".webep":case".png":case".jpg":case".jpeg":case".gif":return t}}},h=await s(d,g);x.value=h}catch(g){}})(d)}),{immediate:!0}),(e,s)=>(h(),g("div",v,[x.value?(h(),w(j(x.value),{key:0})):y("",!0)]))}};export{S as _};
