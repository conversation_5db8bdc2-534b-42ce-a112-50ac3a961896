import{b as e,i as s}from"./role-OrQ5c9FV.js";import{F as a,c as o,n as t,M as r}from"./ant-design-vue-DYY9BtJq.js";import{d as i,a as l,r as p,a9 as m,aa as n,c as u,ab as d,o as j}from"./@vue-HScy-mz9.js";import{_ as c}from"./vue-qr-CB2aNKv5.js";import"./main-Djn9RDyT.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const v=c(i({__name:"AuthMenu",setup(i,{expose:c}){const v=l({key:"id"}),y=p(!1),f=p(!0),h=p({id:"",name:""}),g=p([]),b=p([]),x=p([]),_=s=>{e({id:s.id,sysCategoryId:s.sysCategoryId}).then((e=>{e.success&&(b.value=e.data,w(h.value))}))},w=e=>{s({sysCategoryId:e.sysCategoryId}).then((e=>{e.success&&(g.value=k(e.data),g.value.forEach((e=>{x.value.push(e.id)})))}))},k=e=>{for(const s of e)s.chidlren&&(s.chidlren=k(s.chidlren));return e.filter((e=>b.value.indexOf(e.id)>-1))},z=e=>{x.value=e,f.value=!1},C=()=>{g.value=[],b.value=[],x.value=[],y.value=!1};return c({roleMenu:e=>{h.value=e,_(h.value),y.value=!0}}),(e,s)=>{const i=t,l=o,p=a,c=r;return j(),m(c,{"mask-closable":!1,title:h.value.name,"body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",width:460,open:y.value,footer:null,onCancel:C},{default:n((()=>[u(p,{"label-align":"left"},{default:n((()=>[u(l,{label:""},{default:n((()=>[e.hasPerm("sys-menu:tree-for-grant")?(j(),m(i,{key:0,"auto-expand-parent":f.value,"expanded-keys":x.value,"tree-data":g.value,selectable:!1,"field-names":v,onExpand:z},null,8,["auto-expand-parent","expanded-keys","tree-data","field-names"])):d("",!0)])),_:1})])),_:1})])),_:1},8,["title","open"])}}}),[["__scopeId","data-v-370c891e"]]);export{v as default};
