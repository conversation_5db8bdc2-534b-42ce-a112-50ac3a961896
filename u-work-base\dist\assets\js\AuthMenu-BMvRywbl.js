import{b as e}from"./main-Djn9RDyT.js";import{d as a,e as s,f as t}from"./role-Dauzd5F5.js";import{S as o,F as l,c as i,n as r,d as n,M as u}from"./ant-design-vue-DYY9BtJq.js";import{Q as p}from"./@ant-design-CA72ad83.js";import{d as m,r as d,a9 as c,o as v,aa as j,c as f,ab as h,J as y,ad as g,u as b}from"./@vue-HScy-mz9.js";import{_ as k}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const _=k(m({__name:"AuthMenu",emits:["ok"],setup(m,{expose:k,emit:_}){const x=_,w=d({children:"children",title:"title",key:"id"}),C=d(!1),I=d(!0),z=d(!1),K=d(!1),M=d({id:"",name:""}),E=d([]),A=d([]),J=d([]),L=d([]),P=d([]),q=e=>{a({sysCategoryId:e.sysCategoryId}).then((e=>{e.success&&(E.value=e.data,F(e.data),E.value.forEach((e=>{J.value.push(e.id)})),B(M.value))}))},B=e=>{s({id:e.id,sysCategoryId:e.sysCategoryId}).then((e=>{e.success&&(D(e.data),A.value=e.data),K.value=!1}))},D=e=>{for(let a=0;a<e.length;a++)P.value.includes(e[a])&&L.value.push(e[a])},F=e=>{for(let a=0;a<e.length;a++)H(e[a])},H=e=>{e.children.length>0?F(e.children):P.value.push(e.id)},N=e=>{J.value=e,I.value=!1},O=(e,a)=>{L.value=e,A.value=L.value.concat(a.halfCheckedKeys)},Q=()=>{z.value=!0,t({id:M.value.id,grantMenuIdList:A.value}).then((a=>{z.value=!1,a.success?(e("success","授权成功"),x("ok"),S()):e("error","授权失败")})).finally((()=>{z.value=!1}))},S=()=>{L.value=[],J.value=[],C.value=!1,z.value=!1};return k({roleMenu:e=>{K.value=!0,M.value=e,C.value=!0,q(e)}}),(e,a)=>{const s=n,t=r,m=i,d=l,k=o,_=u;return v(),c(_,{"mask-closable":!1,"body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",width:460,open:C.value,"confirm-loading":z.value,onOk:Q,onCancel:S},{title:j((()=>[y(" 授权菜单 【"+g(M.value.name)+"】 ",1),f(s,{title:"请按照角色需要赋予菜单权限"},{default:j((()=>[f(b(p),{style:{color:"#ef7b1a"}})])),_:1})])),default:j((()=>[f(k,{spinning:K.value},{default:j((()=>[f(d,{"label-align":"left"},{default:j((()=>[f(m,{label:""},{default:j((()=>[e.hasPerm("sys-menu:temp-tree-for-grant")?(v(),c(t,{key:0,checkedKeys:L.value,"onUpdate:checkedKeys":a[0]||(a[0]=e=>L.value=e),multiple:"",checkable:"","auto-expand-parent":I.value,"expanded-keys":J.value,"tree-data":E.value,selectable:!1,"field-names":w.value,onExpand:N,onCheck:O},null,8,["checkedKeys","auto-expand-parent","expanded-keys","tree-data","field-names"])):h("",!0)])),_:1})])),_:1})])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-d0c6ab6b"]]);export{_ as default};
