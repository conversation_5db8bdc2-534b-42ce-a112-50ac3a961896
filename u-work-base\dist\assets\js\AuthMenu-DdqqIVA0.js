import{a as e,b as a}from"./main-Djn9RDyT.js";import{i as s,g as t,b as l,c as o}from"./role-OrQ5c9FV.js";import{S as i,F as r,c as n,n as u,d as p,M as m}from"./ant-design-vue-DYY9BtJq.js";import{Q as d}from"./@ant-design-CA72ad83.js";import{d as c,r as v,a9 as j,o as h,aa as f,c as y,ab as g,J as k,ad as b,u as _}from"./@vue-HScy-mz9.js";import{_ as w}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const x=w(c({__name:"AuthMenu",emits:["ok"],setup(c,{expose:w,emit:x}){e();const z=x,K=v({children:"children",title:"title",key:"id"}),M=v(!1),C=v(!0),I=v(!1),E=v(!1),A=v(),J=v([]),L=v([]),P=v([]),q=v([]),B=v([]),D=async()=>{J.value=[],B.value=[];let e={};e="1"===A.value.enterpriseId?await s({}):await t({}),e.success&&(J.value=e.data.filter((e=>e.children.length>0)),N(e.data),J.value.forEach((e=>{P.value.push(e.id)})),F(A.value))},F=e=>{l({id:e.id,sysCategoryId:null}).then((e=>{e.success&&(H(e.data),L.value=e.data),E.value=!1}))},H=e=>{for(let a=0;a<e.length;a++)B.value.includes(e[a])&&q.value.push(e[a])},N=e=>{for(let a=0;a<e.length;a++)O(e[a])},O=e=>{e.children.length>0?N(e.children):B.value.push(e.id)},Q=e=>{P.value=e,C.value=!1},S=(e,a)=>{q.value=e,L.value=q.value.concat(a.halfCheckedKeys)},T=()=>{I.value=!0,o({id:A.value.id,grantMenuIdList:L.value}).then((e=>{I.value=!1,e.success?(a("success","角色授权成功"),z("ok"),U()):a("error","角色授权失败")})).catch((()=>{I.value=!1})).finally((()=>{I.value=!1}))},U=()=>{I.value||(q.value=[],P.value=[],M.value=!1,I.value=!1)};return w({roleMenu:e=>{E.value=!0,A.value=e,M.value=!0,D()}}),(e,a)=>{const s=p,t=u,l=n,o=r,c=i,v=m;return h(),j(v,{"mask-closable":!1,"body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",width:460,open:M.value,"confirm-loading":I.value,onOk:T,onCancel:U},{title:f((()=>[k(" 授权菜单 【"+b(A.value.name)+"】 ",1),y(s,{title:"请按照角色需要赋予菜单权限"},{default:f((()=>[y(_(d),{style:{color:"#ef7b1a"}})])),_:1})])),default:f((()=>[y(c,{spinning:E.value},{default:f((()=>[y(o,{"label-align":"left"},{default:f((()=>[y(l,{label:""},{default:f((()=>[e.hasPerm("sys-menu:tree")||e.hasPerm("sys-menu:temp-tree-for-grant")?(h(),j(t,{key:0,checkedKeys:q.value,"onUpdate:checkedKeys":a[0]||(a[0]=e=>q.value=e),multiple:"",checkable:"","auto-expand-parent":C.value,"expanded-keys":P.value,"tree-data":J.value,selectable:!1,"field-names":K.value,onExpand:Q,onCheck:S},null,8,["checkedKeys","auto-expand-parent","expanded-keys","tree-data","field-names"])):g("",!0)])),_:1})])),_:1})])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-c3348a31"]]);export{x as default};
