import{a as e,b as a}from"./main-Djn9RDyT.js";import{g as s,b as t,c as o}from"./role-OrQ5c9FV.js";import{S as l,F as i,c as r,n,d as u,M as p}from"./ant-design-vue-DYY9BtJq.js";import{Q as m}from"./@ant-design-CA72ad83.js";import{d,r as c,a9 as v,o as j,aa as f,c as h,ab as y,J as g,ad as k,u as b}from"./@vue-HScy-mz9.js";import{_}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const x=_(d({__name:"AuthMenu",emits:["ok"],setup(d,{expose:_,emit:x}){e();const w=x,z=c({children:"children",title:"title",key:"id"}),K=c(!1),M=c(!0),C=c(!1),E=c(!1),I=c(),A=c([]),J=c([]),L=c([]),q=c([]),B=c([]),D=()=>{A.value=[],B.value=[],s({}).then((e=>{e.success&&(A.value=e.data,N(e.data),A.value.forEach((e=>{L.value.push(e.id)})),F(I.value))}))},F=e=>{t({id:e.id,sysCategoryId:null}).then((e=>{e.success&&(H(e.data),J.value=e.data),E.value=!1}))},H=e=>{for(let a=0;a<e.length;a++)B.value.includes(e[a])&&q.value.push(e[a])},N=e=>{for(let a=0;a<e.length;a++)O(e[a])},O=e=>{e.children.length>0?N(e.children):B.value.push(e.id)},P=e=>{L.value=e,M.value=!1},Q=(e,a)=>{q.value=e,J.value=q.value.concat(a.halfCheckedKeys)},S=()=>{C.value=!0,o({id:I.value.id,grantMenuIdList:J.value}).then((e=>{C.value=!1,e.success?(a("success","角色授权成功"),w("ok"),T()):a("error","角色授权失败")})).finally((()=>{C.value=!1}))},T=()=>{q.value=[],L.value=[],K.value=!1,C.value=!1};return _({roleMenu:e=>{E.value=!0,I.value=e,K.value=!0,D()}}),(e,a)=>{const s=u,t=n,o=r,d=i,c=l,_=p;return j(),v(_,{"mask-closable":!1,"body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",width:460,open:K.value,"confirm-loading":C.value,onOk:S,onCancel:T},{title:f((()=>[g(" 授权菜单 【"+k(I.value.name)+"】 ",1),h(s,{title:"请按照角色需要赋予菜单权限"},{default:f((()=>[h(b(m),{style:{color:"#ef7b1a"}})])),_:1})])),default:f((()=>[h(c,{spinning:E.value},{default:f((()=>[h(d,{"label-align":"left"},{default:f((()=>[h(o,{label:""},{default:f((()=>[e.hasPerm("sys-menu:temp-tree-for-grant")?(j(),v(t,{key:0,checkedKeys:q.value,"onUpdate:checkedKeys":a[0]||(a[0]=e=>q.value=e),multiple:"",checkable:"","auto-expand-parent":M.value,"expanded-keys":L.value,"tree-data":A.value,selectable:!1,"field-names":z.value,onExpand:P,onCheck:Q},null,8,["checkedKeys","auto-expand-parent","expanded-keys","tree-data","field-names"])):y("",!0)])),_:1})])),_:1})])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-2a92620f"]]);export{x as default};
