import{a as e,b as o,C as t,ao as i,L as s,ap as a}from"./main-Djn9RDyT.js";import{f as n,s as r}from"./index-GENaTOlC.js";import{d as p,r as l,w as c,b as m,e as u,ab as d,c as w,am as j,o as f,n as v}from"./@vue-HScy-mz9.js";import{_ as g}from"./vue-qr-CB2aNKv5.js";import"./ant-design-vue-DYY9BtJq.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./js-binary-schema-parser-G48GG52R.js";const h={class:"campus-effect"},y={id:"campus-container",ref:"campusIns"},_={class:"control-wrap"},b={class:"icon-wrap"},T={class:"icon-wrap"},C=g(p({__name:"CampusEffectsPreview",setup(p){const g=e(),C=l(!1),E=l(!1),$=()=>{E.value||(E.value=!0,n({sceneCode:U.value,name:N.value}).then((e=>{E.value=!1,200===e.code?o("success",e.message):o("error",e.message)})).catch((()=>{E.value=!1})))},I=()=>{const e=new FormData;e.append("id",H.value);let t=null;if(1===g.thingjsVersion)t=window.app.captureScreenshotToImage(window.app.domElement.clientWidth,window.app.domElement.clientHeight,"png",.5),e.append("file",k(t));else{t=window.app.camera.captureToImage(window.app.container.clientWidth,window.app.container.clientHeight).src,e.append("file",k(t))}C.value||(C.value=!0,r(e).then((e=>{C.value=!1,200===e.code?o("success","截图保存成功"):o("error",e.message)})).catch((()=>{C.value=!1})))},k=e=>{const o=e.split(",")[1],t=atob(o),i=[];for(let a=0;a<t.length;a+=1)i.push(t.charCodeAt(a));const s=new Blob([new Uint8Array(i)],{type:"image/png"});return new File([s],"preview.png",{type:"image/png"})},U=l(),H=l(),N=l(),A=e=>{i((()=>{e&&e()}),["thingjs","EffectThemeControl","thing.campus"])},G=async()=>{let e=null;1===g.thingjsVersion?(window.THING.disableLoginRequested=!0,1===g.thingjsNoAuth&&(THING.__auth_server_URL__=`${window.config.appApi}/licensex/license-info/thing-js-api`,THING.__force_check_auth_result__=!0),e=new window.THING.App({container:"campus-container",background:"#000000"})):(await THING.Utils.login({method:"POST",url:`${window.config.appApi}/licensex/license-info/thing-js-2-api`,wasmRootPath:"/libs/wasm"}),e=new THING.App({container:document.getElementById("campus-container")||void 0,compatibleOptions:{rendering:!0},isEditor:!1})),window.app=e;const o=`${window.config.previewSceneURL}${U.value}/1`,t=`${window.config.previewSourceUrl}`;e.create({type:"Campus",url:o,loaderResourceUrl:t,complete:o=>{e.level.change(o.object),e.camera.fit(o.object),R(o.object,e)}})},R=async(e,o)=>{const t=`${window.config.previewEffectUrl}${U.value}/frame.js`,i=`${window.config.previewEffectUrl}${U.value}/`;if(1===g.thingjsVersion)s(t,(()=>{const t=new window.THING.EffectThemeControl;o.addControl(t,"themeControlDefault");const s=o.getControl("themeControlDefault");s.registerTheme("default",data,i),e.applyTheme("default"),s.applyThemeEnvironment("default",e),s.applyEffectTheme("default",e)}));else{const t=`${window.config.previewEffectUrl}${U.value}/resource.json`,i=`${window.config.previewEffectUrl}${U.value}/bundle.json`,s=await a(i)?i:t;o.load(s,{apply:!0,root:e})}};return c((()=>g.adminToken),(e=>{e&&v((()=>{(()=>{const{query:e}=t.currentRoute.value,{code:o,id:i,name:s}=e;o&&(U.value=o,H.value=i,N.value=s,v((()=>{A((()=>{G()}))})))})()}))}),{immediate:!0}),(e,o)=>{const t=j("CameraOutlined"),i=j("CloudSyncOutlined");return f(),m("div",h,[u("div",y,null,512),u("div",_,[u("div",b,[H.value&&e.hasPerm("effect-package:editEffectSnap")?(f(),m("div",{key:0,class:"icon-item",onClick:I,title:"封面截图"},[w(t,{title:"封面截图"})])):d("",!0)]),u("div",T,[H.value&&e.hasPerm("effect-package:sync-model")?(f(),m("div",{key:0,class:"icon-item",onClick:$,title:"同步模型"},[w(i,{title:"同步模型"})])):d("",!0)])])])}}}),[["__scopeId","data-v-505c0936"]]);export{C as default};
