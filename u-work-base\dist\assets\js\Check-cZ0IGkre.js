import{m as e,n as a,o as s}from"./projectGallery-xT8wgNPG.js";import{b as l}from"./main-Djn9RDyT.js";import{S as t,F as o,b as r,c as i,h as u,R as p,I as m,z as n,T as v,M as d}from"./ant-design-vue-DYY9BtJq.js";import{d as c,r as j,p as f,a9 as g,o as h,aa as k,c as b,ab as y,J as _,u as w,ad as R,n as x}from"./@vue-HScy-mz9.js";import{_ as z}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const E=z(c({__name:"Check",emits:["ok"],setup(c,{expose:z,emit:E}){const I=E,M=j(!1),q=j(!1),N=j(),S=j({approveRemark:null,approve:2,tags:[]}),$=j([]),C=j(new Map),H=j([]),P=j(new Map),T=j(new Map),U={approveRemark:[{required:f((()=>3===S.value.approve)),message:"请输入原因",trigger:"blur"}],tags:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,s)=>{let l=0;return a.forEach((e=>{if(1===e.length)if(H.value.includes(e[0])){const a=$.value.find((a=>a.value===e[0]));a&&a.children&&(l+=a.children.length)}else l++;else l=l+e.length-1})),l>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}]},D=j(),F=()=>{M.value=!1,S.value.approveRemark="",q.value=!1},J=()=>{let e=[];return S.value.tags.forEach((a=>{1===a.length?e=e.concat(P.value.get(a[0])):e.push(a[1])})),e};return z({init:e=>{M.value=!0,x((()=>{N.value.resetFields()})),S.value.approve=0,S.value.approveRemark="",S.value.tags=[],new Promise((e=>{s().then((a=>{const s=a.data.map((e=>{var a;return H.value.push(e.id),P.value.set(e.id,[]),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(P.value.get(e.id).push(a.id),C.value.set(`${a.tagName}`,a.color),T.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));$.value=s.filter((e=>e.children)),e(!0)}))})).then((a=>{D.value=e;const s=e.templateTags||e.tags||[],l=[];(null==s?void 0:s.length)&&(s.forEach((e=>{const a=T.value.get(e.id);a&&l.push([a,e.id])})),S.value.tags=l)}))}}),(s,c)=>{const j=p,f=u,x=i,z=r,E=m,H=v,P=n,T=o,L=t,O=d;return h(),g(O,{width:580,title:"大屏审批","body-style":{maxHeight:"200px",overflow:"auto"},"wrap-class-name":"cus-modal",open:M.value,"confirm-loading":q.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:c[3]||(c[3]=s=>(async()=>{N.value.validate().then((async()=>{if(q.value=!0,0===S.value.approve)e({id:D.value.id,status:0}).then((async()=>{const e={tagIds:J(),sceneId:D.value.id};a(e).then((e=>{q.value=!1,200===e.code?(l("success","大屏审批完成"),I("ok"),F()):l("error",e.message)}))}));else{const a=await e({status:2,approveRemark:S.value.approveRemark,id:D.value.id});if(200!==(null==a?void 0:a.code))return void l("error",a.message);l("success","大屏审批完成"),M.value=!1,q.value=!1,I("ok")}})).catch((()=>{}))})()),onCancel:F},{default:k((()=>[b(L,{spinning:q.value},{default:k((()=>[b(T,{ref_key:"formRef",ref:N,model:S.value,rules:U,"label-align":"left"},{default:k((()=>[b(z,{md:24,sm:24,class:"form-item"},{default:k((()=>[b(x,{name:"approve",label:"审批","has-feedback":""},{default:k((()=>[b(f,{value:S.value.approve,"onUpdate:value":c[0]||(c[0]=e=>S.value.approve=e)},{default:k((()=>[b(j,{value:0},{default:k((()=>c[4]||(c[4]=[_("通过")]))),_:1}),b(j,{value:2},{default:k((()=>c[5]||(c[5]=[_("不通过")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1}),2===S.value.approve?(h(),g(z,{key:0,md:24,sm:24,class:"form-item"},{default:k((()=>[b(x,{name:"approveRemark",label:"拒绝原因","has-feedback":""},{default:k((()=>[b(E,{value:S.value.approveRemark,"onUpdate:value":c[1]||(c[1]=e=>S.value.approveRemark=e),placeholder:"请输入原因"},null,8,["value"])])),_:1})])),_:1})):y("",!0),0===S.value.approve?(h(),g(z,{key:1,md:24,sm:24,class:"form-item"},{default:k((()=>[b(x,{name:"tags",label:"标签","has-feedback":""},{default:k((()=>[b(P,{value:S.value.tags,"onUpdate:value":c[2]||(c[2]=e=>S.value.tags=e),defaultValue:S.value.tags,"show-checked-strategy":w(n).SHOW_CHILD,style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:$.value,placeholder:"请选择标签",checkStrictly:!0},{tagRender:k((e=>{return[(h(),g(H,{key:e.value,color:(a=e.label,C.value.get(a)||"blue")},{default:k((()=>[_(R(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","options"])])),_:1})])),_:1})):y("",!0)])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-202cb7b0"]]);export{E as default};
