import{M as e,V as a,f as l,B as t}from"./ant-design-vue-DYY9BtJq.js";import{v as i,A as o}from"./@ant-design-CA72ad83.js";import{d as s,r,p as n,b as d,o as u,c,aa as v,ab as p,ad as m,e as g,a4 as A,u as f,J as b}from"./@vue-HScy-mz9.js";import{_ as y}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const w={key:0},h={key:1,class:"table-actions"},k=["onClick"],j={key:0,class:"check-field"},E={key:1,class:"check-field"},I={class:"left"},x={class:"title"},D={class:"left-title"},C={class:"right"},_={class:"title"},N={class:"right-title"},U={class:"footer"},Q=y(s({__name:"CheckField",setup(s,{expose:y}){const Q=r(!1),R=r([{operation:"EDIT",targetName:"孪生体导入孪生体导入孪生体",code:"LUAN_SHENG_TI_DAO_RU_LUAN_SHENG_TI_DAO_RU",originName:"孪生体导入",origin:[{control:"input",label:"孪生体编码",field:"UniqueCode"}],target:[{control:"input",label:"孪生体编码",field:"UniqueCode"}]},{operation:"ADD",targetName:"wqwdghu",originName:"wqwdghu",code:"WQWDGHU",origin:null,target:[{control:"input",label:"孪生体编码",field:"UniqueCode"}]},{operation:"DELETE",targetName:"wqwdghu",originName:"wqwdghu",code:"WQWDGHU",origin:[{control:"input",label:"孪生体编码",field:"UniqueCode"}],target:null}]),q=r(0),G={ADD:"新增",DELETE:"删除",EDIT:"编辑"},S=[{title:"差异对比",dataIndex:"operation",width:100,ellipsis:!0},{title:"孪生体对象名称",dataIndex:"targetName",width:240,ellipsis:!0},{title:"唯一编码",dataIndex:"code",ellipsis:!0},{title:"操作",dataIndex:"action",width:80}],J=[{title:"控件类型",dataIndex:"control",width:100,ellipsis:!0},{title:"标签",dataIndex:"label",ellipsis:!0},{title:"属性字段",dataIndex:"field",ellipsis:!0}],L=r({}),X=r({title:n((()=>`${G[L.value.operation]}-${L.value.targetName}`||"")),visible:!1}),B=e=>{L.value=R.value[e]},F=()=>{0!==q.value&&(q.value-=1,B(q.value))},H=()=>{q.value!==R.value.length-1&&(q.value+=1,B(q.value))},K=()=>{L.value={},X.value.visible=!1},T=()=>{Q.value=!1};return y({init:e=>{R.value=e,Q.value=!0}}),(s,r)=>{const n=a,y=l,W=e,z=t;return u(),d("div",null,[c(W,{width:900,title:"数据校验","wrap-class-name":"cus-modal",open:Q.value,"mask-closable":!1,footer:null,onCancel:T},{default:v((()=>[c(n,{message:"系统检测到迁移的孪生体数据相较于当前环境已发生变化，请手动处理将孪生体结构保持一致后再进行数据迁移工作，否则将导致数据不一致的情况",banner:""}),c(y,{style:{"margin-top":"20px"},columns:S,"data-source":R.value,"row-key":"code",pagination:!1,scroll:{y:400}},{bodyCell:v((({column:e,record:a,index:l})=>["operation"===e.dataIndex?(u(),d("div",w,m(G[a.operation]),1)):p("",!0),"action"===e.dataIndex?(u(),d("div",h,[g("a",{type:"text",onClick:e=>((e,a)=>{q.value=a,B(a),X.value.visible=!0})(0,l)},"详情",8,k)])):p("",!0)])),_:1},8,["data-source"])])),_:1},8,["open"]),c(W,{title:X.value.title,"wrap-class-name":"cus-modal","mask-closable":!1,width:"EDIT"===L.value.operation?1200:900,open:X.value.visible,onCancel:K},{footer:v((()=>[g("div",U,[g("div",{style:A({"margin-right":"EDIT"===L.value.operation?"505px":"360px"})},[c(z,{disabled:0===q.value,onClick:F},{icon:v((()=>[c(f(i))])),_:1},8,["disabled"]),c(z,{disabled:q.value===R.value.length-1,onClick:H},{icon:v((()=>[c(f(o))])),_:1},8,["disabled"])],4),g("div",null,[c(z,{onClick:K},{default:v((()=>r[1]||(r[1]=[b("返回")]))),_:1})])])])),default:v((()=>["ADD"===L.value.operation||"DELETE"===L.value.operation?(u(),d("div",j,[c(y,{columns:J,"row-key":"field","data-source":"ADD"===L.value.operation?L.value.target:L.value.origin,pagination:!1,scroll:{y:400}},null,8,["data-source"])])):(u(),d("div",E,[g("div",I,[g("div",x,[g("span",D,"当前环境("+m(L.value.originName)+")",1)]),c(y,{columns:J,bordered:"","row-key":"field","data-source":L.value.origin,pagination:!1,scroll:{y:400}},null,8,["data-source"])]),r[0]||(r[0]=g("div",{class:"icon"},[g("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAP9JREFUSEvtlbGKwkAQhr8RfQAb7w30Rayv9CHEIqXFJQRtLC0OH8LS2hfRJ1A5kGtE0GQkkuS8IpkNGLBwu2WGb//5Z2dXMFZnqt+HCI9QrlbuY1ys5I+JKrA+nxj8zuRo5WdxVzAKmzjm8yeUrQtcUkXsA7kfUrRPYUeEwd6XtQWvCkaFS0PxdoEsyuAvA3a3wvIq87xy8xzBz79utQ2IVVFR3ByQN7jQAQ1pEjOXCaPSyavioY5p02IJ9GVKaX+cm6chXSJWQC8R8xSwftEHlgjt/L1NFaufvKh/B+V7ywr1GQJzoPXvh3hZcKKyFiuy8mtpXg6v47o9KHcakBtnq6IFZeagxQAAAABJRU5ErkJggg=="})],-1)),g("div",C,[g("div",_,[g("span",N,"数据迁出环境("+m(L.value.targetName)+")",1)]),c(y,{columns:J,"row-key":"field",bordered:"","data-source":L.value.target,pagination:!1,scroll:{y:400}},null,8,["data-source"])])]))])),_:1},8,["title","width","open"])])}}}),[["__scopeId","data-v-16f4d75d"]]);export{Q as default};
