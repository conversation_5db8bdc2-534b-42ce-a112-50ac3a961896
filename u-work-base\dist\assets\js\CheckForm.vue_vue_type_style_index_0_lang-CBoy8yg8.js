import{F as e,c as a,I as l,i as s,B as r,M as u}from"./ant-design-vue-DYY9BtJq.js";import{d,r as t,a as o,a9 as n,o as m,aa as v,c as b,J as f,n as i}from"./@vue-HScy-mz9.js";const p=d({__name:"CheckForm",setup(d,{expose:p}){const c=t(!1),h=t(),_=o({id:"",name:"",url:"",reqMethod:"",browser:"",param:"",result:""}),w=()=>{h.value.resetFields(),c.value=!1};return p({init:e=>{c.value=!0,i((()=>{h.value.resetFields(),_.id=e.id,_.name=e.name,_.url=e.url,_.reqMethod=e.reqMethod,_.browser=e.browser,_.param=e.param,_.result=e.result}))}}),(d,t)=>{const o=l,i=a,p=s,k=e,M=r,q=u;return m(),n(q,{width:678,title:"日志详情","wrap-class-name":"cus-modal",open:c.value,"mask-closable":!1,onCancel:w},{footer:v((()=>[b(M,{onClick:w},{default:v((()=>t[6]||(t[6]=[f("关闭")]))),_:1})])),default:v((()=>[b(k,{ref_key:"formRef",ref:h,model:_,"label-align":"left",class:"checkForm"},{default:v((()=>[b(i,{name:"name",label:"日志名称","has-feedback":""},{default:v((()=>[b(o,{value:_.name,"onUpdate:value":t[0]||(t[0]=e=>_.name=e),disabled:!0},null,8,["value"])])),_:1}),b(i,{name:"url",label:"请求地址","has-feedback":""},{default:v((()=>[b(o,{value:_.url,"onUpdate:value":t[1]||(t[1]=e=>_.url=e),disabled:!0},null,8,["value"])])),_:1}),b(i,{name:"reqMethod",label:"请求方式","has-feedback":""},{default:v((()=>[b(o,{value:_.reqMethod,"onUpdate:value":t[2]||(t[2]=e=>_.reqMethod=e),disabled:!0},null,8,["value"])])),_:1}),b(i,{name:"browser",label:"浏览器","has-feedback":""},{default:v((()=>[b(o,{value:_.browser,"onUpdate:value":t[3]||(t[3]=e=>_.browser=e),disabled:!0},null,8,["value"])])),_:1}),b(i,{name:"param",label:"请求参数"},{default:v((()=>[b(p,{value:_.param,"onUpdate:value":t[4]||(t[4]=e=>_.param=e),rows:4,disabled:!0},null,8,["value"])])),_:1}),b(i,{name:"result",label:"返回结果"},{default:v((()=>[b(p,{value:_.result,"onUpdate:value":t[5]||(t[5]=e=>_.result=e),rows:4,disabled:!0},null,8,["value"])])),_:1})])),_:1},8,["model"])])),_:1},8,["open"])}}});export{p as _};
