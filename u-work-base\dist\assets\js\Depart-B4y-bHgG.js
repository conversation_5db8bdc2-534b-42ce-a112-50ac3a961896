import{g as e}from"./department-CTxHSeTj.js";import{b as s}from"./main-Djn9RDyT.js";import{I as t,m as a,n as i}from"./ant-design-vue-DYY9BtJq.js";import{S as r}from"./@ant-design-CA72ad83.js";import{d as l,r as o,f as n,w as p,b as d,e as m,a9 as u,c,aa as v,u as j,ad as h,am as f,a5 as y,o as g}from"./@vue-HScy-mz9.js";import{_ as w}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const I={class:"tree-wrap"},_={class:"tree-contain"},k={class:"tree-search"},x={key:1,class:"tree-content"},b=["title"],K={class:"title"},E=w(l({__name:"Depart",emits:["handleClick","onBack"],setup(l,{expose:w,emit:E}){const S=E,z=o({children:"children",title:"title",key:"id"}),C=o(""),M=o([]),N=o();let O=[];const R=o([]),D=o([]);n((()=>{L()}));const J=async()=>{N.value?e({enterpriseId:N.value.id}).then((e=>{e.success?(N.value.children=e.data,M.value=[...M.value],O=[...M.value]):s("error",e.message)})):e({enterpriseId:""}).then((e=>{e.success?(M.value[0].children=e.data,M.value=[...M.value],O=[...M.value]):s("error",e.message)}))},L=async()=>{const t={id:"0",title:"默认",enterpriseId:"",isCompany:!1,isLeaf:!1,pid:"0",children:[]},a=await e({enterpriseId:""});a.success?(t.children=a.data,M.value.push(t),O=[...M.value],D.value.push("0"),R.value=["0"],S("handleClick",t)):s("error",a.message)},U=(e,s)=>{if(D.value.length>0){const t=D.value.findIndex((e=>e===s.node.id));t>-1?e.length||D.value.splice(t,1):D.value.push(s.node.id)}else D.value.push(s.node.id);if(!e.length)return void(R.value=[s.node.dataRef.id]);R.value=e;const t=M.value.find((e=>e.id===s.node.dataRef.pids));S("handleClick",s.node.dataRef,null==t?void 0:t.title)};p(C,(e=>{M.value=A(O,e)}));const A=(e,s)=>{const t=e&&JSON.parse(JSON.stringify(e));if(!t||!t.length)return[];const a=[];for(const i of t){const e=A(i.children,s);i.title.indexOf(s)>-1?a.push(i):e&&e.length&&(i.children=e,a.push(i))}return a.length?a:[]};return w({update:()=>{J()}}),(e,s)=>{const l=t,o=f("down-outlined"),n=i;return g(),d("div",I,[m("div",_,[m("div",k,[c(l,{value:C.value,"onUpdate:value":s[0]||(s[0]=e=>C.value=e),valueModifiers:{trim:!0},class:"filter",placeholder:"请输入搜索内容","allow-clear":!0},{suffix:v((()=>[c(j(r))])),_:1},8,["value"])]),0===M.value.length?(g(),u(j(a),{key:0,image:j(a).PRESENTED_IMAGE_SIMPLE},null,8,["image"])):(g(),d("div",x,[c(n,{selectedKeys:R.value,"onUpdate:selectedKeys":s[1]||(s[1]=e=>R.value=e),"show-icon":"","tree-data":M.value,class:"cus-tree",expandedKeys:D.value,"onUpdate:expandedKeys":s[2]||(s[2]=e=>D.value=e),"field-names":z.value,onSelect:U},{switcherIcon:v((({switcherCls:e})=>[c(o,{class:y(e)},null,8,["class"])])),title:v((e=>[m("span",{class:"root-tree-item",title:e.title},[m("span",K,h(e.title),1)],8,b)])),_:1},8,["selectedKeys","tree-data","expandedKeys","field-names"])]))])])}}}),[["__scopeId","data-v-45fc9a9e"]]);export{E as default};
