import{g as e,u as s}from"./department-CTxHSeTj.js";import{b as a}from"./main-Djn9RDyT.js";import{S as t}from"./@ant-design-CA72ad83.js";import{I as i,m as r,n as l}from"./ant-design-vue-DYY9BtJq.js";import{d as o,r as n,f as d,w as p,am as u,b as m,o as c,e as v,a9 as j,c as f,aa as h,u as g,ad as y,a5 as w}from"./@vue-HScy-mz9.js";import{_ as I}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const _={class:"tree-wrap"},k={class:"tree-contain"},x={class:"tree-search"},R={key:1,class:"tree-content"},b=["title"],C={class:"title"},E=I(o({__name:"Depart",emits:["handleClick","onBack"],setup(o,{expose:I,emit:E}){const K=E,S=n({children:"children",title:"title",key:"id"}),z=n(""),L=n([]),M=n();let N=[];const O=n([]),D=n([]);d((()=>{T()}));const J=async()=>{M.value?e({enterpriseId:M.value.id}).then((e=>{e.success?(M.value.children=e.data,L.value=[...L.value],N=[...L.value]):a("error",e.message)})):e({enterpriseId:""}).then((e=>{e.success?(L.value[0].children=e.data,L.value=[...L.value],N=[...L.value]):a("error",e.message)}))},P=s=>new Promise((t=>{setTimeout((()=>{if(s.dataRef.isCompany){if(M.value=s.dataRef,s.dataRef.children)return void t();e({enterpriseId:s.dataRef.id}).then((e=>{e.success?(s.dataRef.children=e.data,L.value=[...L.value],N=[...L.value],t()):a("error",e.message)}))}t()}),200)})),T=async()=>{const t={id:"0",title:"默认",enterpriseId:"",isCompany:!1,isLeaf:!1,pid:"0",children:[]},i=await e({enterpriseId:""});if(!i.success)return void a("error",i.message);t.children=i.data,L.value.push(t),N=[...L.value],D.value.push("0"),O.value=["0"],K("handleClick",t);const r=await s();if(200===r.code){if(r.data&&r.data.length){const e=r.data.map((e=>({id:e.id,title:e.name,enterpriseId:e.id,pids:e.id,isCompany:!0,isLeaf:!1,pid:"0"})));L.value=L.value.concat(e),N=[...L.value]}}else a("error",r.message)},A=(e,s)=>{if(D.value.length>0){const a=D.value.findIndex((e=>e===s.node.id));a>-1?e.length||D.value.splice(a,1):D.value.push(s.node.id)}else D.value.push(s.node.id);if(!e.length)return void(O.value=[s.node.dataRef.id]);O.value=e;const a=L.value.find((e=>e.id===s.node.dataRef.pids));K("handleClick",s.node.dataRef,null==a?void 0:a.title)};p(z,(e=>{L.value=B(N,e)}));const B=(e,s)=>{const a=e&&JSON.parse(JSON.stringify(e));if(!a||!a.length)return[];const t=[];for(const i of a){const e=B(i.children,s);i.title.indexOf(s)>-1?t.push(i):e&&e.length&&(i.children=e,t.push(i))}return t.length?t:[]};return I({update:()=>{J()}}),(e,s)=>{const a=i,o=u("down-outlined"),n=l;return c(),m("div",_,[v("div",k,[v("div",x,[f(a,{value:z.value,"onUpdate:value":s[0]||(s[0]=e=>z.value=e),valueModifiers:{trim:!0},class:"filter",placeholder:"请输入搜索内容","allow-clear":!0},{suffix:h((()=>[f(g(t))])),_:1},8,["value"])]),0===L.value.length?(c(),j(g(r),{key:0,image:g(r).PRESENTED_IMAGE_SIMPLE},null,8,["image"])):(c(),m("div",R,[f(n,{selectedKeys:O.value,"onUpdate:selectedKeys":s[1]||(s[1]=e=>O.value=e),"show-icon":"","tree-data":L.value,"load-data":P,class:"cus-tree","default-expand-all":!1,expandedKeys:D.value,"field-names":S.value,onSelect:A},{switcherIcon:h((({switcherCls:e})=>[f(o,{class:w(e)},null,8,["class"])])),title:h((e=>[v("span",{class:"root-tree-item",title:e.title},[v("span",C,y(e.title),1)],8,b)])),_:1},8,["selectedKeys","tree-data","expandedKeys","field-names"])]))])])}}}),[["__scopeId","data-v-800d0f17"]]);export{E as default};
