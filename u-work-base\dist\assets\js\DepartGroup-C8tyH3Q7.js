import{d as e,r as s,f as t,w as a,am as o,b as i,o as l,F as r,e as n,c as p,a9 as d,aa as m,u as c,a4 as u,ab as j,E as v,ad as f,a5 as y}from"./@vue-HScy-mz9.js";import{n as h,d as g}from"./department-CTxHSeTj.js";import{b as k}from"./main-Djn9RDyT.js";import w from"./AddEditDepart-BzdtS7BQ.js";import{g as C}from"./getThinyColor-DtFDxjua.js";import{I as _,m as b,n as x,e as E}from"./ant-design-vue-DYY9BtJq.js";import{S as D}from"./@ant-design-CA72ad83.js";import{_ as I}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./tinycolor2-DJ_qK68I.js";const K={class:"tree-wrap"},O={class:"tree-contain"},P={class:"tree-search"},R={key:1,class:"tree-content"},S=["title"],z={class:"title"},A=I(e({__name:"DepartGroup",emits:["handleClick","onBack"],setup(e,{expose:I,emit:A}){const L=A,M=s({children:"children",title:"name",value:"id",key:"id"}),N=s(""),U=s([]);let J=[];const T=s([]),B=s([]);t((()=>{q()}));const F=()=>{G()},G=async()=>{const e=await h({});if(200!==e.code)return void k("error",e.message);const{data:s}=e;U.value=[s],B.value.push(s.id),B.value.push(s.id),J=[...U.value]},q=async()=>{const e=await h({});if(200!==e.code)return void k("error",e.message);const{data:s}=e,t=[s];U.value=t,J=[...U.value],B.value.push(t[0].id),T.value=[t[0].id],L("handleClick",t[0])},Z=(e,s)=>{e.length?(T.value=e,L("handleClick",s.node.dataRef)):T.value=[s.node.dataRef.id]};a(N,(e=>{U.value=$(J,e)}));const $=(e,s)=>{const t=e&&JSON.parse(JSON.stringify(e));if(!t||!t.length)return[];const a=[];for(const o of t){const e=$(o.children,s);o.name.indexOf(s)>-1?a.push(o):e&&e.length&&(o.children=e,a.push(o))}return a.length?a:[]},H=s(),Q=()=>{F()};return I({update:F}),(e,s)=>{const t=_,a=o("down-outlined"),h=o("plus-outlined"),I=o("FormOutlined"),A=o("DeleteOutlined"),L=E,J=x;return l(),i(r,null,[n("div",K,[n("div",O,[n("div",P,[p(t,{value:N.value,"onUpdate:value":s[0]||(s[0]=e=>N.value=e),valueModifiers:{trim:!0},class:"filter",placeholder:"请输入搜索内容","allow-clear":!0},{suffix:m((()=>[p(c(D))])),_:1},8,["value"])]),0===U.value.length?(l(),d(c(b),{key:0,image:c(b).PRESENTED_IMAGE_SIMPLE},null,8,["image"])):(l(),i("div",R,[p(J,{selectedKeys:T.value,"onUpdate:selectedKeys":s[2]||(s[2]=e=>T.value=e),expandedKeys:B.value,"onUpdate:expandedKeys":s[3]||(s[3]=e=>B.value=e),"show-icon":"","tree-data":U.value,class:"cus-tree","default-expand-all":!1,"field-names":M.value,onSelect:Z},{switcherIcon:m((({switcherCls:e})=>[p(a,{class:y(e)},null,8,["class"])])),title:m((t=>[n("span",{class:"root-tree-item",title:t.name},[n("div",{class:"tab-bar",style:u({"--bgColor":c(C)(.1)})},[e.hasPerm("sys-dept:add")?(l(),d(h,{key:0,title:"添加",onClick:v((e=>(e=>{const s="dept"===e.source?e.id:"0",t=e.enterpriseId,a=e.source;H.value.init("add",null,s,t,a)})(t)),["stop"])},null,8,["onClick"])):j("",!0),e.hasPerm("sys-dept:edit")&&"team"!==t.source&&"LDAP"!==t.name?(l(),d(I,{key:1,title:"编辑",style:{"padding-left":"10px"},onClick:v((e=>(e=>{const s=e.pid,t=e.enterpriseId,a=e.source;H.value.init("edit",e.data,s,t,a)})(t)),["stop"])},null,8,["onClick"])):j("",!0),n("span",null,[e.hasPerm("sys-dept:delete")&&"team"!==t.source&&"LDAP"!==t.name?(l(),d(L,{key:0,title:"确认删除当前分类？",placement:"topRight",onConfirm:v((e=>(e=>{g(e).then((e=>{e.success?(k("success","部门删除成功"),F()):k("error",e.message)})).finally((()=>{}))})(t)),["stop"])},{default:m((()=>[n("span",{type:"delete",title:"删除",onClick:s[1]||(s[1]=v((()=>{}),["stop"]))},[p(A,{style:{"padding-left":"10px"}})])])),_:2},1032,["onConfirm"])):j("",!0),s[4]||(s[4]=n("span",null,null,-1))])],4),n("span",z,f(t.name),1)],8,S)])),_:1},8,["selectedKeys","expandedKeys","tree-data","field-names"])]))])]),p(w,{ref_key:"addEditDepartRef",ref:H,onOk:Q},null,512)],64)}}}),[["__scopeId","data-v-c3017145"]]);export{A as default};
