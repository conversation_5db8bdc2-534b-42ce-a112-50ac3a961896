import{V as e}from"./viewerjs-_Br7E8dP.js";import{V as l}from"./ViewerArrow-r3R4USz-.js";import{d as t,p as s,r as i,b as a,o as r,e as o,c,ad as n,F as d,ag as v}from"./@vue-HScy-mz9.js";import{_ as f}from"./vue-qr-CB2aNKv5.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./js-binary-schema-parser-G48GG52R.js";const m={class:"detail"},p={class:"projectName"},u={class:"info",style:{"margin-bottom":"20px"}},g={class:"info-item"},y={class:"text"},x={class:"info-item"},I={class:"text"},w={class:"info"},j={class:"info-item"},h={class:"text"},S={class:"info-item"},b={class:"text"},D={class:"info-item"},k={class:"text"},C={class:"info-item"},N={class:"text"},U={class:"info-item"},_={class:"text"},A={class:"info-item"},F={class:"text"},V={class:"info-item"},E={class:"text"},M={class:"info-item"},$={class:"text"},O={class:"info-item"},P={class:"text"},R={class:"info-item"},q={class:"text"},J={class:"info-item"},L={class:"text"},B={class:"info-item"},T={class:"text"},Z={class:"info-item"},z={class:"text"},G={class:"info-item"},H={class:"text"},K={class:"info-item"},Q={class:"text"},W={class:"info-text"},X={class:"text text-erea"},Y={class:"info-text"},ee={class:"text text-erea"},le={class:"info-text"},te={class:"text text-erea"},se={class:"info-text"},ie={class:"text text-erea"},ae={class:"info-text"},re={class:"text-wrapper"},oe={class:"text-more text-erea"},ce={class:"text-more text-erea"},ne={class:"video"},de={id:"gallary-img-list",class:"img-list"},ve=["src","onClick"],fe={class:"video"},me={class:"video-list"},pe=["src"],ue=f(t({__name:"Detail",props:{galleryInfo:{type:Object,required:!0}},setup(t){const f=t,ue=e=>`${e}`,ge=s((()=>{let e=["",""];try{e=Array.isArray(JSON.parse(f.galleryInfo.projectPracticalSummary))?JSON.parse(f.galleryInfo.projectPracticalSummary):["",""]}catch(l){e=["",""]}return e})),ye=i([]),xe=i([]);(async()=>{const e=f.galleryInfo.deliverablesImages||[],l=f.galleryInfo.deliverablesVideo||[];for(let t=0;t<e.length;t++){const l=e[t];if(l.includes("https://www.mingdao.com")){const e=l.split("excelfile")[1],t=await fetch(`/mingdaoapi/api/File/ExcelFile${e}`);ye.value.push({showUrl:t.url,originalUrl:l})}else ye.value.push({showUrl:ue(l),originalUrl:l})}for(let t=0;t<l.length;t++){const e=l[t];if(e.includes("https://www.mingdao.com")){const l=e.split("excelfile")[1],t=await fetch(`/mingdaoapi/api/File/ExcelFile${l}`);xe.value.push(t.url)}else xe.value.push(ue(e))}})();let Ie=null;const we=i(),je=i(0),he=i(0),Se=()=>{Ie&&Ie.prev()},be=()=>{Ie&&Ie.next()};return(s,i)=>(r(),a("div",m,[o("div",p,n(t.galleryInfo.projectName),1),o("div",u,[o("div",g,[i[2]||(i[2]=o("div",{class:"title"},"预览数",-1)),o("div",y,n(t.galleryInfo.previewTimes)+"人预览",1)]),o("div",x,[i[3]||(i[3]=o("div",{class:"title"},"合同编号",-1)),o("div",I,n(t.galleryInfo.contractCode||"-"),1)])]),i[26]||(i[26]=o("div",{class:"projectName"},"项目信息",-1)),o("div",w,[o("div",j,[i[4]||(i[4]=o("div",{class:"title"},"产品信息（合同）",-1)),o("div",h,n(t.galleryInfo.productName||"-"),1)]),o("div",S,[i[5]||(i[5]=o("div",{class:"title"},"产品版本",-1)),o("div",b,n(t.galleryInfo.productVersion||"-"),1)]),o("div",D,[i[6]||(i[6]=o("div",{class:"title"},"采纳的技术平台",-1)),o("div",k,n(t.galleryInfo.techPlatform||"-"),1)]),o("div",C,[i[7]||(i[7]=o("div",{class:"title"},"所属部门（销售）",-1)),o("div",N,n(t.galleryInfo.saleDepartment||"-"),1)]),o("div",U,[i[8]||(i[8]=o("div",{class:"title"},"项目经理",-1)),o("div",_,n(t.galleryInfo.projectManger||"-"),1)]),o("div",A,[i[9]||(i[9]=o("div",{class:"title"},"销售经理",-1)),o("div",F,n(t.galleryInfo.salesManager||"-"),1)]),o("div",V,[i[10]||(i[10]=o("div",{class:"title"},"支持售前",-1)),o("div",E,n(t.galleryInfo.saleSupporter||"-"),1)]),o("div",M,[i[11]||(i[11]=o("div",{class:"title"},"行业",-1)),o("div",$,n(t.galleryInfo.industry||"-"),1)]),o("div",O,[i[12]||(i[12]=o("div",{class:"title"},"最终用户名称",-1)),o("div",P,n(t.galleryInfo.ultimateCustomer||"-"),1)]),o("div",R,[i[13]||(i[13]=o("div",{class:"title"},"签约客户名称",-1)),o("div",q,n(t.galleryInfo.contractedCustomer||"-"),1)]),o("div",J,[i[14]||(i[14]=o("div",{class:"title"},"项目启动时间",-1)),o("div",L,n(t.galleryInfo.projectStartDate||"-"),1)]),o("div",B,[i[15]||(i[15]=o("div",{class:"title"},"项目完成时间",-1)),o("div",T,n(t.galleryInfo.projectEndDate||"-"),1)]),o("div",Z,[i[16]||(i[16]=o("div",{class:"title"},"项目状态",-1)),o("div",z,n(t.galleryInfo.projectStatus||"-"),1)]),o("div",G,[i[17]||(i[17]=o("div",{class:"title"},"屏幕分辨率",-1)),o("div",H,n(t.galleryInfo.screenResolution||"-"),1)]),o("div",K,[i[18]||(i[18]=o("div",{class:"title"},"项目地址",-1)),o("div",Q,n(t.galleryInfo.projectAccessLinker||"-"),1)])]),o("div",W,[i[19]||(i[19]=o("div",{class:"title"},"一句话简介",-1)),o("div",X,n(t.galleryInfo.projectSimpleDescription||"-"),1)]),o("div",Y,[i[20]||(i[20]=o("div",{class:"title"},"项目整体需求情况",-1)),o("div",ee,n(t.galleryInfo.overallDemand||"-"),1)]),o("div",le,[i[21]||(i[21]=o("div",{class:"title"},"项目范围",-1)),o("div",te,n(t.galleryInfo.projectScope||"-"),1)]),o("div",se,[i[22]||(i[22]=o("div",{class:"title"},"功能介绍分析",-1)),o("div",ie,n(t.galleryInfo.functionalDecompositio||"-"),1)]),o("div",ae,[i[23]||(i[23]=o("div",{class:"title"},"项目实战经验总结",-1)),o("div",re,[o("div",oe,n(ge.value[0]||"-"),1),o("div",ce,n(ge.value[1]||"-"),1)])]),o("div",ne,[i[24]||(i[24]=o("div",{class:"title"},"项目成果图片",-1)),o("div",de,[(r(!0),a(d,null,v(ye.value,((l,t)=>(r(),a("div",{key:l,class:"img-wrapper"},[o("img",{src:l.showUrl,alt:"",onClick:l=>(l=>{Ie=new e(document.getElementById("gallary-img-list"),{toolbar:!1,navbar:!0,title:!1,transition:!1,hidden:()=>{null==Ie||Ie.destroy(),Ie=null,he.value=0},viewed(){je.value=Ie.index,he.value=Ie.length,Ie.viewer.appendChild(we.value.$el)}}),Ie.view(l)})(t)},null,8,ve)])))),128))])]),o("div",fe,[i[25]||(i[25]=o("div",{class:"title"},"项目成果视频",-1)),o("div",me,[(r(!0),a(d,null,v(xe.value,(e=>(r(),a("video",{key:e,controls:"",muted:"",onMouseenter:i[0]||(i[0]=e=>(e=>{e.target.play()})(e)),onMouseleave:i[1]||(i[1]=e=>(e=>{e.target.pause()})(e))},[o("source",{src:e,type:"video/mp4"},null,8,pe)],32)))),128))])]),c(l,{ref_key:"viewerArrowRef",ref:we,current:je.value,total:he.value,onLeft:Se,onRight:be},null,8,["current","total"])]))}}),[["__scopeId","data-v-e1f35460"]]);export{ue as default};
