import{Q as e}from"./@vueup-CLVdhRgW.js";import{F as a,_ as l,b as r,c as t,I as s,M as o}from"./ant-design-vue-DYY9BtJq.js";import{d as m,r as u,a9 as d,o as i,aa as n,c as p,e as v,ad as c,u as f}from"./@vue-HScy-mz9.js";import{_ as b}from"./vue-qr-CB2aNKv5.js";import"./quill-BBEhJLA6.js";import"./@babel-B4rXMRun.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const j=["src"],_=b(m({__name:"Detail",setup(m,{expose:b}){const _=u(!1),h=u(),k=u({debug:"info",modules:{},placeholder:"",readOnly:!0,theme:"snow"}),w=u({name:"",remark:"",code:"",createUser:"",createTime:"",previewUrl:"",url:""}),U=()=>{h.value.resetFields(),_.value=!1};return b({init:e=>{_.value=!0,w.value=e,w.value.createUser=e.createUser||"-"}}),(m,u)=>{const b=s,g=t,y=r,x=l,q=a,$=o;return i(),d($,{width:900,title:"功能示例详情","body-style":{maxHeight:"900px",overflow:"auto"},"wrap-class-name":"cus-modal",open:_.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:u[4]||(u[4]=e=>(h.value.resetFields(),void(_.value=!1))),onCancel:U},{default:n((()=>[p(q,{ref_key:"formRef",ref:h,model:w.value,"label-align":"left"},{default:n((()=>[p(x,{gutter:24},{default:n((()=>[p(y,{md:12,sm:12},{default:n((()=>[p(g,{name:"name",label:"名称","has-feedback":""},{default:n((()=>[p(b,{value:w.value.name,"onUpdate:value":u[0]||(u[0]=e=>w.value.name=e),bordered:!1},null,8,["value"])])),_:1})])),_:1}),p(y,{md:12,sm:12},{default:n((()=>[p(g,{name:"name",label:"创建人","has-feedback":""},{default:n((()=>[p(b,{value:w.value.createUser,"onUpdate:value":u[1]||(u[1]=e=>w.value.createUser=e),bordered:!1},null,8,["value"])])),_:1})])),_:1}),p(y,{md:12,sm:12},{default:n((()=>[p(g,{name:"name",label:"创建时间","has-feedback":""},{default:n((()=>[p(b,{value:w.value.createTime,"onUpdate:value":u[2]||(u[2]=e=>w.value.createTime=e),bordered:!1},null,8,["value"])])),_:1})])),_:1}),p(y,{md:24,sm:24},{default:n((()=>[p(g,{name:"url",label:"访问路径","has-feedback":""},{default:n((()=>{return[v("span",null,c((e=w.value.url,`${window.config.previewUrl}${e}index.html`)),1)];var e})),_:1})])),_:1}),p(y,{md:24,sm:24},{default:n((()=>[p(g,{name:"previewUrl",label:"封面图","has-feedback":""},{default:n((()=>{return[v("img",{src:(e=w.value.previewUrl,`${window.config.previewUrl}/${e}`),class:"preview-img"},null,8,j)];var e})),_:1})])),_:1}),p(y,{md:24,sm:24},{default:n((()=>[p(g,{name:"remark",label:"功能示例简介","has-feedback":""},{default:n((()=>[p(f(e),{ref:"QuillEditorRef",modelValue:w.value.remark,"onUpdate:modelValue":u[3]||(u[3]=e=>w.value.remark=e),style:{height:"250px"},options:k.value,content:w.value.remark,"content-type":"html"},null,8,["modelValue","options","content"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["open"])}}}),[["__scopeId","data-v-36e1250c"]]);export{_ as default};
