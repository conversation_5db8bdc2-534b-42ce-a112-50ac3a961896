import{S as e,B as s,M as o}from"./ant-design-vue-DYY9BtJq.js";import{d as a,r as t,a9 as i,o as l,aa as r,c as n,e as m,J as p}from"./@vue-HScy-mz9.js";import{_ as d}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const u={class:"content"},c=["innerHTML"],v=d(a({__name:"DocPreview",setup(a,{expose:d}){const v=t(!1),j=t(!1),f=t(""),g=()=>{v.value=!1,j.value=!1};return d({init:e=>{v.value=!0,f.value=e}}),(a,t)=>{const d=e,y=s,b=o;return l(),i(b,{width:800,title:"效果包说明","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:v.value,"confirm-loading":j.value,"mask-closable":!1,destroyOnClose:!0,footer:null,onCancel:g},{footer:r((()=>[n(y,{disabled:j.value,onClick:g},{default:r((()=>t[0]||(t[0]=[p("关闭")]))),_:1},8,["disabled"])])),default:r((()=>[n(d,{spinning:j.value},{default:r((()=>[m("div",u,[m("div",{class:"ql-editor",style:{height:"500px",overflow:"auto"},innerHTML:f.value},null,8,c)])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-c51c2737"]]);export{v as default};
