import{u as e,a,b as s}from"./main-Djn9RDyT.js";import{a as t,d as o}from"./operationAnalysis-D3RTU-GI.js";import{M as i,f as r,g as n,S as l}from"./ant-design-vue-DYY9BtJq.js";import{d as p,r as m,a9 as d,aa as c,c as u,e as j,ab as g,b as v,ad as y,F as h,a7 as w,o as f}from"./@vue-HScy-mz9.js";import{_ as k}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const x={class:"table-wrap"},b={key:0},z={key:1},I={key:2},_=["onClick"],C={class:"pagination"},S=k(p({__name:"DownLoadPaksList",emits:["ok"],setup(p,{expose:k,emit:S}){const $=m();e(),a(),m([]);const N=m(!1),D=m(!1),M=m([]);let T=m("");const L=async()=>{const{data:e}=await t({name:"",pageNo:1,pageSize:10}),{rows:a,pageNo:s,totalRows:o}=e;M.value=a,A.value.current=s,A.value.total=o},P=[{title:"操作系统",dataIndex:"operateSystem",ellipsis:!0},{title:"上传日期",dataIndex:"createTime",width:120,ellipsis:!0},{title:"上传人",dataIndex:"createName",width:80,ellipsis:!0},{title:"操作",dataIndex:"actions",width:80,ellipsis:!0}],A=m({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small",name:""}),F=(e,a)=>{if(!e)return"--";const s=new Date(e),t=s.getFullYear(),o=s.getMonth()+1,i=s.getDate();return s.getHours(),s.getMinutes(),s.getSeconds(),s.getDay(),`${t}-${o<10?`0${o}`:o}-${i<10?`0${i}`:i}`},H=()=>{N.value=!1},J=(e,a)=>{A.value=Object.assign(A.value,{current:e,pageSize:a}),L()};return k({init:e=>{L(),D.value=!1,N.value=!0,T.value=e}}),(e,a)=>{const t=r,p=n,m=l,k=i;return f(),d(k,{width:800,title:"安装包下载列表","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:N.value,"mask-closable":!1,"confirm-loading":D.value,"ok-text":"确认","cancel-text":"取消",footer:null,onCancel:H},{default:c((()=>[u(m,{spinning:D.value},{default:c((()=>[j("div",x,[j("div",{ref_key:"table",ref:$,class:"table-content"},[e.hasPerm("sys-install-manage:page")?(f(),d(t,{key:0,class:"table",scroll:{y:"360px"},pagination:!1,style:{height:"360px"},size:"small",loading:D.value,"row-key":e=>e.id,columns:P,"data-source":M.value,onChange:J},{bodyCell:c((({column:a,record:t})=>["createTime"===a.dataIndex?(f(),v("div",b,y(F(t.createTime)),1)):g("",!0),"createName"===a.dataIndex?(f(),v("div",z,y(t.createName||"-"),1)):g("",!0),"version"===a.dataIndex?(f(),v("div",I,y(t.version||"-"),1)):g("",!0),"actions"===a.dataIndex?(f(),v(h,{key:3},[e.hasPerm("sys-project:download-project")?(f(),v("a",{key:0,style:{"margin-right":"20px"},onClick:e=>(e=>{o({id:T.value,installManageId:e.id,downloadOption:2}).then((e=>{200===e.code&&s("success",e.message)}))})(t)},"下载",8,_)):g("",!0)],64)):g("",!0)])),_:1},8,["loading","row-key","data-source"])):g("",!0),j("div",C,[M.value.length>0?(f(),d(p,w({key:0},A.value,{onChange:J}),null,16)):g("",!0)])],512)])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-7ca7b05f"]]);export{S as default};
