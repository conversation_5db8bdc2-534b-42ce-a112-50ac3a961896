import{b as e}from"./main-Djn9RDyT.js";import{f as a,r as l,c as s}from"./projectGallery-xT8wgNPG.js";import{S as t,F as o,_ as i,b as r,c as n,z as u,T as m,I as d,i as p,B as v,Q as c,d as j,M as f}from"./ant-design-vue-DYY9BtJq.js";import{d as g,r as h,am as w,a9 as b,o as _,aa as y,c as k,ab as x,e as N,ad as z,u as E,J as M,n as C}from"./@vue-HScy-mz9.js";import{_ as I}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const q=I(g({__name:"EditChart",emits:["ok"],setup(a,{expose:d,emit:p}){const v=p,c=h(!1),j=h(!1),g=h(),I=h({id:"",name:"",ownerName:"",tags:[],level1:"",level2:"",media:[],imgs:[],ulink:"",description:""}),q={tags:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let s=0;return a.forEach((e=>{if(1===e.length)if(P.value.includes(e[0])){const a=L.value.find((a=>a.value===e[0]));a&&a.children&&(s+=a.children.length)}else s++;else s=s+e.length-1})),s>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}]},L=h([]),O=h(new Map),P=h([]),S=h(new Map),$=h(new Map),D=(e,a)=>a.some((a=>a.label.toLowerCase().indexOf(e.toLowerCase())>-1)),F=(h(["mp4","MP4"]),h(!1),h(),h(["png","jpg","jpeg","gif"]),()=>{j.value||(g.value.resetFields(),c.value=!1,j.value=!1,j.value=!1)}),H=()=>{let e=[];return I.value.tags.forEach((a=>{1===a.length?e=e.concat(S.value.get(a[0])):e.push(a[1])})),e};return d({init:e=>{c.value=!0,new Promise((e=>{s().then((a=>{const l=a.data.map((e=>{var a;return P.value.push(e.id),S.value.set(e.id,[]),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(S.value.get(e.id).push(a.id),O.value.set(`${a.tagName}`,a.color),$.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));L.value=l.filter((e=>e.children)),e(!0)}))})).then((()=>{C((()=>{var a,l,s,t;g.value.resetFields(),I.value.id=e.id,I.value.name=e.componentName,I.value.ownerName=e.ownerName,I.value.tags=(e=>{const a=[];return e.forEach((e=>{a.push([$.value.get(e.id),e.id])})),a})(e.tags||[]),I.value.level1=e.level1,I.value.level2=e.level2,I.value.media=(null==(a=e.detail)?void 0:a.media)||[],I.value.imgs=(null==(l=e.detail)?void 0:l.imgs)||[],I.value.ulink=(null==(s=e.detail)?void 0:s.ulink)||"",I.value.description=(null==(t=e.detail)?void 0:t.description)||""}))}))}}),(a,s)=>{const d=n,p=r,h=m,C=u,P=(w("DeleteOutlined"),w("question-circle-outlined"),i),S=o,$=t,B=f;return _(),b(B,{width:620,title:"编辑","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:c.value,"confirm-loading":j.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:s[3]||(s[3]=a=>{j.value||(j.value=!0,g.value.validate().then((()=>{const a={tagIds:H(),componentId:I.value.id};l(a).then((a=>{j.value=!1,200===a.code?(e("success","图表修改成功"),v("ok"),F()):e("error","图表修改失败")}))})).catch((e=>{j.value=!1})))}),onCancel:F},{default:y((()=>[k($,{spinning:j.value,style:{position:"fixed",top:"250px"}},{default:y((()=>[k(S,{ref_key:"formRef",ref:g,model:I.value,rules:q,"label-align":"left"},{default:y((()=>[k(P,{gutter:24},{default:y((()=>[k(p,{md:24,sm:24},{default:y((()=>[k(d,{name:"name",label:"图表名称","has-feedback":""},{default:y((()=>[N("div",null,z(I.value.name),1)])),_:1})])),_:1}),k(p,{md:24,sm:24},{default:y((()=>[k(d,{name:"ownerName",label:"创建人","has-feedback":""},{default:y((()=>[N("div",null,z(I.value.ownerName||"-"),1)])),_:1})])),_:1}),k(p,{md:24,sm:24},{default:y((()=>[k(d,{name:"tags",label:"标签","has-feedback":""},{default:y((()=>[k(C,{value:I.value.tags,"onUpdate:value":s[0]||(s[0]=e=>I.value.tags=e),defaultValue:I.value.tags,"show-checked-strategy":E(u).SHOW_CHILD,style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:L.value,placeholder:"请选择标签",checkStrictly:!0,"show-search":{filter:D}},{tagRender:y((e=>{return[(_(),b(h,{key:e.value,color:(a=e.label,O.value.get(a)||"blue")},{default:y((()=>[M(z(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","options","show-search"])])),_:1})])),_:1}),x("",!0),x("",!0),x("",!0),x("",!0)])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-cedf032b"]]);export{q as default};
