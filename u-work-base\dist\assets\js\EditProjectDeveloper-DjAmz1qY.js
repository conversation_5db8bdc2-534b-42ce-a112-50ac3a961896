import e from"./UserList-B68ng9ko.js";import{a as s,T as a,w as i,b as t}from"./main-Djn9RDyT.js";import{u as o}from"./vue-router-BEwRlUkF.js";import{M as r,S as l}from"./ant-design-vue-DYY9BtJq.js";import{d as p,r as n,a9 as m,aa as u,n as c,c as d,e as v,b as j,ag as f,ad as g,am as h,F as y,o as k}from"./@vue-HScy-mz9.js";import{_ as w}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const b={class:"project-developer-wrap keep-px"},_={class:"developer-left"},x={class:"developer-right"},S={class:"checked-wrap"},z={class:"name"},L=w(p({__name:"EditProjectDeveloper",emits:["ok"],setup(p,{expose:w,emit:L}){const N=s(),C=n(),J=L,O=n(!1),D=n(!1),{params:E,query:I}=o(),{id:$}=E;n(I.name);const q=n(),M=e=>`${e.name}(${e.account})`,T=e=>{"add"===e.type?U.value=[...U.value,e]:"delete"===e.type&&(U.value=U.value.filter((s=>s.id!==e.id)))},U=n([]),A=n([]),B=()=>{a({enterpriseId:N.checkedEnterprise.id,pageNo:1,pageSize:999}).then((e=>{if(200===e.code){const{rows:s}=e.data;s&&s.length?A.value=s.filter((e=>0===e.status)):A.value=[]}}))},F=()=>{O.value=!1,D.value=!1};return w({init:async e=>{q.value=e,U.value=[],A.value=[],O.value=!0,await B(),c((()=>{var s;C.value.changeData(JSON.parse(JSON.stringify(e)),"init"),(null==(s=e.userList)?void 0:s.length)&&(U.value=JSON.parse(JSON.stringify(e.userList)))}))}}),(s,a)=>{const o=h("close-outlined"),p=l,n=r;return k(),m(n,{width:900,title:"参与团队成员","body-style":{maxHeight:"600px",overflow:"auto",overflowX:"hidden"},"wrap-class-name":"cus-modal",open:O.value,"confirm-loading":D.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[0]||(a[0]=e=>{i({projectId:q.value.id,userList:U.value}).then((()=>{t("success","团队成员绑定成功"),J("ok"),U.value=[],O.value=!1}))}),onCancel:F},{default:u((()=>[d(p,{spinning:D.value},{default:u((()=>[v("div",b,[v("div",_,[d(e,{ref_key:"userListRef",ref:C,onUserSelectChange:T},null,512)]),v("div",x,[a[1]||(a[1]=v("p",null,"已选",-1)),v("div",S,[(k(!0),j(y,null,f(U.value,((e,s)=>(k(),j("div",{key:e,class:"checked-dev"},[v("div",z,g(M(e)),1),d(o,{class:"delete-icon",onClick:a=>((e,s)=>{U.value.splice(e,1),C.value.changeData(s,"delete")})(s,e)},null,8,["onClick"])])))),128))])])])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-4464a586"]]);export{L as default};
