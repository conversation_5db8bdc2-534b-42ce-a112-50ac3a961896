import{u as e,a as s,w as a,b as o}from"./main-Djn9RDyT.js";import i from"./UserList-B68ng9ko.js";import{S as t,M as r}from"./ant-design-vue-DYY9BtJq.js";import{d as l,r as p,am as n,a9 as m,o as u,aa as c,c as j,e as v,b as d,F as f,ag as g,ad as y,n as h}from"./@vue-HScy-mz9.js";import{_ as k}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const w={class:"project-developer-wrap keep-px"},b={class:"developer-left"},_={class:"developer-right"},x={class:"checked-wrap"},L={class:"name"},S=k(l({__name:"EditProjectDeveloper",emits:["ok"],setup(l,{expose:k,emit:S}){e(),s();const z=S,C=p(!1),J=p(!1),N=p(),O=e=>`${e.name}(${e.account})`,D=p(),$=e=>{"add"===e.type?E.value=[...E.value,e]:"delete"===e.type&&(E.value=E.value.filter((s=>s.id!==e.id)))},E=p([]),I=p([]),M=()=>{C.value=!1,J.value=!1};return k({init:async e=>{N.value=e,E.value=[],I.value=[],C.value=!0,h((()=>{var s;D.value.changeData(JSON.parse(JSON.stringify(e)),"init"),(null==(s=e.userList)?void 0:s.length)&&(E.value=JSON.parse(JSON.stringify(e.userList)))}))}}),(e,s)=>{const l=n("close-outlined"),p=t,h=r;return u(),m(h,{width:900,title:"参与团队成员","body-style":{maxHeight:"600px",overflow:"auto",overflowX:"hidden"},"wrap-class-name":"cus-modal",open:C.value,"confirm-loading":J.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:s[0]||(s[0]=e=>{a({projectId:N.value.id,userList:E.value}).then((()=>{o("success","团队成员绑定成功"),z("ok"),E.value=[],C.value=!1}))}),onCancel:M},{default:c((()=>[j(p,{spinning:J.value},{default:c((()=>[v("div",w,[v("div",b,[j(i,{ref_key:"userListRef",ref:D,onUserSelectChange:$},null,512)]),v("div",_,[s[1]||(s[1]=v("p",null,"已选",-1)),v("div",x,[(u(!0),d(f,null,g(E.value,((e,s)=>(u(),d("div",{key:e,class:"checked-dev"},[v("div",L,y(O(e)),1),j(l,{class:"delete-icon",onClick:a=>((e,s)=>{E.value.splice(e,1),D.value.changeData(s,"delete")})(s,e)},null,8,["onClick"])])))),128))])])])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-059ee4aa"]]);export{S as default};
