import{b as e}from"./main-Djn9RDyT.js";import"./jsencrypt-BWvXBO1z.js";import"./crypto-js-Duvj5un5.js";import{n as a,o as s}from"./projectGallery-xT8wgNPG.js";import{S as t,F as l,_ as o,b as r,c as i,z as n,T as m,M as u}from"./ant-design-vue-DYY9BtJq.js";import{d as p,r as d,a9 as c,o as v,aa as j,c as f,e as g,ad as h,u as b,J as w,n as _}from"./@vue-HScy-mz9.js";import{_ as y}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./validator-DM5yI3AY.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const k=y(p({__name:"EditScreen",emits:["ok"],setup(p,{expose:y,emit:k}){const x=k,N=d(!1),z=d(!1),E=d(),M=d({name:"",userName:"",tags:[],media:[],imgs:[],id:"",description:""}),S={tags:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,s)=>{let t=0;return a.forEach((e=>{if(1===e.length)if(L.value.includes(e[0])){const a=C.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+e.length-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}]},C=d([]),I=d(new Map),L=d([]),P=d(new Map),$=d(new Map),q=(e,a)=>a.some((a=>a.label.toLowerCase().indexOf(e.toLowerCase())>-1));d(["mp4","MP4"]),d(!1),d(),d(["png","jpg","jpeg","gif"]);const F=()=>{z.value||(E.value.resetFields(),N.value=!1,z.value=!1,z.value=!1)},H=()=>{z.value||(z.value=!0,E.value.validate().then((()=>{const s={tagIds:(()=>{let e=[];return M.value.tags.forEach((a=>{1===a.length?e=e.concat(P.value.get(a[0])):e.push(a[1])})),e})(),sceneId:M.value.id};a(s).then((a=>{z.value=!1,200===a.code?(e("success","大屏修改成功"),x("ok"),F()):e("error","大屏修改失败")}))})).catch((e=>{z.value=!1})))};return y({init:e=>{N.value=!0,new Promise((e=>{s().then((a=>{const s=a.data.map((e=>{var a;return L.value.push(e.id),P.value.set(e.id,[]),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(P.value.get(e.id).push(a.id),I.value.set(`${a.tagName}`,a.color),$.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));C.value=s.filter((e=>e.children)),e(!0)}))})).then((()=>{_((()=>{E.value.resetFields(),M.value.id=e.id,M.value.name=e.componentName,M.value.ownerName=e.ownerName,M.value.tags=(e=>{const a=[];return e.forEach((e=>{a.push([$.value.get(e.id),e.id])})),a})(e.tags||[])}))}))}}),(e,a)=>{const s=i,p=r,d=m,_=n,y=o,k=l,x=t,L=u;return v(),c(L,{width:620,title:"编辑","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:N.value,"confirm-loading":z.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[1]||(a[1]=e=>H()),onCancel:F},{default:j((()=>[f(x,{spinning:z.value,style:{position:"fixed",top:"250px"}},{default:j((()=>[f(k,{ref_key:"formRef",ref:E,model:M.value,rules:S,"label-align":"left"},{default:j((()=>[f(y,{gutter:24},{default:j((()=>[f(p,{md:24,sm:24},{default:j((()=>[f(s,{name:"projectName",label:"模板名称","has-feedback":""},{default:j((()=>[g("span",null,h(M.value.name),1)])),_:1})])),_:1}),f(p,{md:24,sm:24},{default:j((()=>[f(s,{name:"projectName",label:"创建人","has-feedback":""},{default:j((()=>[g("span",null,h(M.value.ownerName||"-"),1)])),_:1})])),_:1}),f(p,{md:24,sm:24},{default:j((()=>[f(s,{name:"tags",label:"标签","has-feedback":""},{default:j((()=>[f(_,{value:M.value.tags,"onUpdate:value":a[0]||(a[0]=e=>M.value.tags=e),defaultValue:M.value.tags,"show-checked-strategy":b(n).SHOW_CHILD,style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:C.value,placeholder:"请选择标签",checkStrictly:!0,"show-search":{filter:q}},{tagRender:j((e=>{return[(v(),c(d,{key:e.value,color:(a=e.label,I.value.get(a)||"blue")},{default:j((()=>[w(h(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","options","show-search"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-412deffb"]]);export{k as default};
