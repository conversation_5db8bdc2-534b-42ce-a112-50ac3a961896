import{d as e,r as a,j as l,w as s,am as t,b as o,o as i,e as n,F as r,ag as u,ab as c,ad as v,c as p,aa as d,J as m,a9 as g,ae as h,y as f,G as j,a5 as k,a4 as y}from"./@vue-HScy-mz9.js";import"./viewerjs-_Br7E8dP.js";import{p as w}from"./projectGallery-DFHuwUAq.js";import b from"./Nodata-mmdoiDH6.js";import{V as _}from"./ViewerArrow-r3R4USz-.js";import{I as C,u as z}from"./useCookies-BRWvy2S3.js";import{e as N,b as S}from"./main-Djn9RDyT.js";import{o as P,q as $,t as R}from"./projectGallery-xT8wgNPG.js";import I from"./EditScreen-Cbtgor73.js";import U from"./UploadFile-v543zE10.js";import x from"./Check-cZ0IGkre.js";import O from"./Reason--K4q3y8y.js";import{q as T,r as E,S as G,I as J,x as L,w as q,B as A,e as F,d as K,g as V}from"./ant-design-vue-DYY9BtJq.js";import{_ as B}from"./vue-qr-CB2aNKv5.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./vue3-cookies-D4wQmYyh.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";const D={class:"func-module"},H={class:"tag-search"},M={key:0,class:"tag-content"},Q=["onClick"],W={key:1,class:"tag-item"},X={key:1,class:"no-tag-content"},Z={class:"page-wrap"},Y={class:"search-wrap"},ee={class:"search-content"},ae={class:"search-item"},le={class:"search-item"},se={class:"search-item"},te={class:"search-btns"},oe={class:"table-handle"},ie={class:"content-list"},ne={key:0,class:"list"},re={class:"contain"},ue={class:"img-box"},ce=["src"],ve={class:"bottom-wrapper"},pe={class:"bottom-content"},de={class:"hover-box"},me=["onClick"],ge=["onClick"],he=["onClick"],fe=["onClick"],je=["onClick"],ke={class:"control-icon"},ye={class:"item-bottom"},we={class:"title"},be=["title"],_e=["title"],Ce={class:"tag-wrapper"},ze={class:"tag-wrapper"},Ne=["id"],Se={key:1,class:"list"},Pe={class:"pagination-box"},$e=B(e({__name:"Index",setup(e){const B=a(sessionStorage.getItem("XI_TONG_LOGO")||w),$e=a(),Re=a(0),Ie=a(0),Ue=()=>{},xe=()=>{},Oe=a("");N({code:"SCREEN_UI_IP"}).then((e=>{var a,l;const s=(null==(l=null==(a=e.data)?void 0:a.rows[0])?void 0:l.value)||"";Oe.value=s})).catch((()=>{Oe.value=""})),l((()=>{}));const Te=a(),Ee=e=>{Te.value.init(e)},Ge=a(),Je=e=>{let a="";return 0===e?a="正常":1===e?a="待审批":2===e&&(a="未通过"),a},Le=a(),qe=a({total:0,current:1,pageSize:12,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small",baseUrl:window.config.baseUrl}),Ae=a({}),Fe=a([]),Ke=a(!0);Ke.value=!0,P().then((e=>{Ke.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Ae.value[e.id]=[]})):Ae.value={},Fe.value=e.data||[]):Fe.value=[]})).catch((()=>{Ae.value={},Fe.value=[],Ke.value=!1}));s((()=>Ae.value),(()=>{Ze()}),{deep:!0});const Ve=a(),Be=()=>{Ve.value.init()},De=(e,a)=>{qe.value=Object.assign(qe.value,{current:e,pageSize:a}),Xe()},He=a({likeContent:"",tagIds:[],source:1,approve:-1}),Me=()=>{Xe()},Qe=a(!0),We=a([]),Xe=async()=>{var e;We.value=[],Qe.value=!0;const a=(null==(e=Object.values(Ae.value))?void 0:e.flat(Infinity))||[],l={pageNo:qe.value.current,pageSize:qe.value.pageSize,name:He.value.likeContent,tagIds:a,status:-1===He.value.approve?null:He.value.approve,type:1===He.value.source?"0":"1"},s=await $(l);if(Qe.value=!1,200===s.code){const{rows:e,totalPage:a,totalRows:l}=s.data;We.value=e||[],qe.value.total=l}},Ze=()=>{qe.value.total=0,qe.value.current=1,qe.value.pageSize=12,Xe()};return(e,a)=>{var l,s;const w=T,N=E,P=G,$=J,Ye=L,ea=q,aa=A,la=F,sa=t("exception-outlined"),ta=K,oa=V;return i(),o("div",D,[n("div",H,[(null==(l=Fe.value)?void 0:l.length)?(i(),o("div",M,[(i(!0),o(r,null,u(Fe.value,(e=>{var a,l;return i(),o("div",{key:e.id,class:"tag-group"},[(null==(a=null==e?void 0:e.tags)?void 0:a.length)?(i(),o("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const l=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Ae.value[e.id])===JSON.stringify(l)?Ae.value[e.id]=[]:Ae.value[e.id]=e.tags.map((e=>e.id))})(e)},v(e.groupName),9,Q)):c("",!0),(null==(l=null==e?void 0:e.tags)?void 0:l.length)?(i(),o("div",W,[p(N,{value:Ae.value[e.id],"onUpdate:value":a=>Ae.value[e.id]=a,style:{width:"100%"}},{default:d((()=>[(i(!0),o(r,null,u(e.tags,(e=>(i(),o("div",{key:e.id,class:"tag-item-name"},[p(w,{value:e.id},{default:d((()=>[m(v(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):c("",!0)])})),128))])):(i(),o("div",X,[Ke.value?(i(),g(P,{key:0,class:"loading-icon",spinning:Ke.value},null,8,["spinning"])):c("",!0),Ke.value?c("",!0):(i(),g(b,{key:1,title:"请绑定标签"}))]))]),n("div",Z,[n("div",Y,[n("div",ee,[n("div",ae,[a[8]||(a[8]=n("span",{class:"search-label"},"大屏名称",-1)),n("div",null,[p($,{value:He.value.likeContent,"onUpdate:value":a[0]||(a[0]=e=>He.value.likeContent=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入大屏名称",class:"search-input",onKeyup:a[1]||(a[1]=h((e=>Ze()),["enter"]))},null,8,["value"])])]),n("div",le,[a[11]||(a[11]=n("span",{class:"search-label"},"数据源",-1)),n("div",null,[p(ea,{value:He.value.source,"onUpdate:value":a[2]||(a[2]=e=>He.value.source=e),placeholder:"请选择数据源",class:"search-select",onChange:a[3]||(a[3]=e=>(He.value.approve=-1,void Ze()))},{default:d((()=>[p(Ye,{value:1},{default:d((()=>a[9]||(a[9]=[m("公共资源")]))),_:1}),p(Ye,{value:2},{default:d((()=>a[10]||(a[10]=[m("项目资源")]))),_:1})])),_:1},8,["value"])])]),n("div",se,[a[16]||(a[16]=n("span",{class:"search-label"},"审批状态",-1)),n("div",null,[p(ea,{value:He.value.approve,"onUpdate:value":a[4]||(a[4]=e=>He.value.approve=e),"allow-clear":"",placeholder:"请选择审批状态",class:"search-select",onChange:a[5]||(a[5]=e=>Ze())},{default:d((()=>[p(Ye,{value:-1},{default:d((()=>a[12]||(a[12]=[m("全部")]))),_:1}),p(Ye,{value:1},{default:d((()=>a[13]||(a[13]=[m("待审批")]))),_:1}),p(Ye,{value:0},{default:d((()=>a[14]||(a[14]=[m("审批通过")]))),_:1}),p(Ye,{value:2},{default:d((()=>a[15]||(a[15]=[m("审批不通过")]))),_:1})])),_:1},8,["value"])])]),n("div",te,[p(aa,{type:"primary",class:"search-btn",onClick:a[6]||(a[6]=e=>Ze())},{default:d((()=>a[17]||(a[17]=[m(" 查询 ")]))),_:1})])]),n("div",oe,[e.hasPerm("annotate-component:upload-base")?(i(),g(aa,{key:0,type:"primary",class:"handle-btn",onClick:Be},{default:d((()=>a[18]||(a[18]=[m(" 导入大屏模板 ")]))),_:1})):c("",!0)])]),n("div",ie,[e.hasPerm("annotate-component:page-base")?(i(),o("div",ne,[(i(!0),o(r,null,u(We.value,(l=>{var s,t,m,h,w,_,C;return f((i(),o("div",{key:l.id,class:"item"},[n("div",re,[n("div",ue,[n("img",{src:(_=l.filePath,C=l.snapShot,Oe.value?`${window.config.baseUrl}${C}?width=400`:`${window.config.baseUrl}${_}${C}?width=400`),alt:"图片",class:"img",onError:a[7]||(a[7]=e=>(e.target.src=B.value,e.target.style.width="auto"))},null,40,ce),n("div",ve,[n("div",pe,[n("div",{class:k(["status",{fail:2===l.status,normal:0===l.status}])},v(Je(l.status)),3)])]),n("div",de,[n("div",{class:"btn",onClick:e=>(e=>{Oe.value?(z(),window.open(`${Oe.value}/kunpeng/preview/sandbox/${e.id}?random=${Date.now()}`,"_blank",`width=${window.innerWidth},height=${window.innerHeight-100},top=100,left=100,z-look=yes`)):window.open("_blank").location=`/screen/index.html?path=${e.filePath}`})(l)},"预览",8,me),e.hasPerm("annotate-component:scene-bind")?(i(),o("div",{key:0,class:"btn",onClick:e=>(e=>{const a={...e};Le.value.init(a)})(l)},"编辑",8,ge)):c("",!0),0===l.status&&e.hasPerm("annotate-component:modify")?(i(),o("div",{key:1,class:"btn",onClick:e=>(e=>{Ge.value.init(e)})(l)},"下架",8,he)):c("",!0),1===l.status&&e.hasPerm("annotate-component:modify")&&e.hasPerm("annotate-component:scene-bind")?(i(),o("div",{key:2,class:"btn",onClick:e=>Ee(l)},"审批",8,fe)):c("",!0),2===l.status&&e.hasPerm("annotate-component:modify")&&e.hasPerm("annotate-component:scene-bind")?(i(),o("div",{key:3,class:"btn",onClick:e=>Ee(l)},"重新审批",8,je)):c("",!0),1!==l.status&&2!==l.status||!e.hasPerm("annotate-component:delete-base")?c("",!0):(i(),g(la,{key:4,placement:"topRight",title:"确认删除？",onConfirm:e=>(async e=>{200===(await R([e.id])).code&&(S("success","大屏模板删除成功"),Xe())})(l)},{default:d((()=>a[19]||(a[19]=[n("div",{class:"btn"},"删除",-1)]))),_:2},1032,["onConfirm"])),n("div",ke,[p(ta,{placement:"top"},{title:d((()=>[n("span",null,v(l.approveRemark),1)])),default:d((()=>[2===l.status?(i(),g(sa,{key:0,title:"未通过原因",style:{"margin-right":"12px"}})):c("",!0)])),_:2},1024)])])]),n("div",ye,[n("div",we,[n("div",{class:"name",title:(null==(s=l.sceneTemplate)?void 0:s.name)||l.componentName},v((null==(t=l.sceneTemplate)?void 0:t.name)||l.componentName),9,be),n("div",{class:"user",title:(null==(m=l.sceneTemplate)?void 0:m.ownerName)||l.ownerName},v((null==(h=l.sceneTemplate)?void 0:h.ownerName)||l.ownerName),9,_e)]),n("div",Ce,[n("div",ze,[n("div",{id:l.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(i(!0),o(r,null,u(l.tags,((e,a)=>(i(),o("div",{key:a,class:"tag-item",style:y({backgroundColor:e.color})},v(e.tagName),5)))),128))],8,Ne)])])])]),Qe.value?(i(),g(P,{key:0,class:"loading-icon",spinning:Qe.value},null,8,["spinning"])):c("",!0),Qe.value||We.value.length?c("",!0):(i(),g(b,{key:1}))])),[[j,!Qe.value&&(null==(w=We.value)?void 0:w.length)]])})),128)),Qe.value?(i(),g(P,{key:0,class:"loading-icon",spinning:Qe.value},null,8,["spinning"])):c("",!0),Qe.value||(null==(s=We.value)?void 0:s.length)?c("",!0):(i(),g(b,{key:1}))])):(i(),o("div",Se,[p(b,{title:"暂无权限"})])),n("div",Pe,[p(oa,{total:qe.value.total,"page-size-options":["12","20","30","40"],current:qe.value.current,"page-size":qe.value.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:De},null,8,["total","current","page-size","show-total"])])]),p(I,{ref_key:"editScreenRef",ref:Le,onOk:Me},null,512),p(C,{ref:"imgVideoPreviewRef"},null,512),p(U,{ref_key:"uploadFileRef",ref:Ve,onOk:Me},null,512),p(x,{ref_key:"checkRef",ref:Te,onOk:Me},null,512),p(O,{ref_key:"reasonRef",ref:Ge,onOk:Me},null,512),p(_,{ref_key:"viewerArrowRef",ref:$e,current:Re.value,total:Ie.value,onLeft:Ue,onRight:xe},null,8,["current","total"])])])}}}),[["__scopeId","data-v-074cb894"]]);export{$e as default};
