import{d as e,r as s,a,L as t,b as o,e as i,c as l,J as n,u as r,aa as d,am as c,ae as p,F as m,ag as u,a9 as v,E as g,a5 as j,ad as w,ab as h,o as f}from"./@vue-HScy-mz9.js";import{d as y}from"./dayjs-CA7qlNSr.js";import{g as _,r as b,a as C,d as k}from"./notice-C4zCLYzg.js";import{b as z}from"./main-Djn9RDyT.js";import{q as T,e as N,I as x,r as D,K as I,J as R,L as M,g as S,B as L,M as Y}from"./ant-design-vue-DYY9BtJq.js";import{a8 as A,a9 as J}from"./@ant-design-CA72ad83.js";import{_ as K}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const U={class:"notice-center"},$={class:"header"},q={class:"head"},B={class:"search-content"},E={class:"delete_batch",style:{cursor:"pointer"}},H={class:"tree-search",style:{"margin-right":"5px"}},F={class:"list-item"},O={class:"word"},V={class:"buttons"},Z={class:"pagination"},G={class:"news_card"},P={key:0},Q={class:"download"},W={style:{color:"var(--theme-color)"}},X=K(e({__name:"Index",setup(e){const K=s([]),X=a({visible:!1,loading:!1,unReadCount:0,newsData:[],newsTotal:0,showNews:!1,newsInfo:{},downTime:0,pageNo:1,pageSize:10,url:window.config.downloadUrl,status:0}),ee=s(""),se=()=>{const e={pageNo:X.pageNo,pageSize:X.pageSize,status:X.status,message:ee.value};_(e).then((e=>{200===e.code&&(X.newsData=e.data.rows,X.unReadCount=e.data.unReadCount,X.newsTotal=e.data.totalRows,X.loading=!1)})).finally((()=>{X.loading=!1}))};se();const ae=async e=>{try{await b(e),se()}catch(s){}},te=()=>{C({}).then((e=>{200===e.code&&(se(),z("success","全部消息已读"))})).catch((e=>{z("error",e.message)}))},oe=s(!1),ie=e=>{K.value=e.target.checked?X.newsData.map((e=>e.id)):[]},le=(e,s)=>{X.pageNo=e,X.pageSize=s,se()},ne=()=>{X.showNews=!1},re=()=>{setTimeout((()=>{X.downTime-=1,X.downTime>0&&re()}),1e3)},{visible:de,loading:ce,unReadCount:pe,newsData:me,newsTotal:ue,showNews:ve,newsInfo:ge,downTime:je}=t(X);return(e,s)=>{const a=T,t=c("DeleteOutlined"),_=N,b=c("search-outlined"),C=x,de=I,pe=R,we=D,he=M,fe=S,ye=L,_e=Y;return f(),o("div",U,[i("div",$,[i("div",q,[l(a,{value:oe.value,onChange:ie},null,8,["value"]),s[4]||(s[4]=n("消息通知"))]),i("div",B,[i("div",{class:"read_all",onClick:te},[l(r(A),{style:{"margin-right":"2px"}}),s[5]||(s[5]=n("全部已读"))]),l(_,{placement:"topRight",title:K.value.length?"删除后不可恢复，确认删除所有选中消息":"请选中消息后再点击删除",onConfirm:s[0]||(s[0]=()=>{K.value.length&&k(K.value).then((e=>{200===e.code?(z("success","消息批量删除成功"),K.value=[],se()):z("error",e.message)}))})},{default:d((()=>[i("div",E,[l(t,{style:{"margin-right":"2px"}}),s[6]||(s[6]=n("批量删除"))])])),_:1},8,["title"]),i("div",null,[i("div",H,[l(C,{value:ee.value,"onUpdate:value":s[1]||(s[1]=e=>ee.value=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入查询内容",class:"search-input",onKeyup:p(se,["enter"])},{suffix:d((()=>[l(b,{onClick:se})])),_:1},8,["value"])])])])]),l(he,{split:!1,class:"list",loading:r(ce)},{default:d((()=>[l(we,{value:K.value,"onUpdate:value":s[2]||(s[2]=e=>K.value=e),style:{width:"100%"}},{default:d((()=>[(f(!0),o(m,null,u(r(me),((e,s)=>(f(),v(pe,{key:s,class:"containerList"},{default:d((()=>{return[l(a,{value:e.id},null,8,["value"]),l(de,{description:(s=e.createTime,y(s).format("YYYY-MM-DD HH:mm:ss")),onClick:g((s=>{return a=e,ge.value=a,void(ve.value=!0);var a}),["stop"])},{title:d((()=>[i("div",F,[i("div",O,[i("div",{class:j(["title",0===e.status?"non-read":"read"])},w(e.message),3)])])])),_:2},1032,["description","onClick"]),i("div",V,[l(_,{placement:"topRight",title:0===e.status?"该消息还未读，删除后不可恢复，确认删除？":"消息删除后不可恢复，确认删除？",onConfirm:()=>(e=>{k([e.id]).then((e=>{200===e.code?(z("success","消息删除成功"),se()):z("error",e.message)}))})(e)},{default:d((()=>[l(t,{title:"删除",style:{"margin-left":"10px",color:"red",cursor:"pointer"},class:"btn"})])),_:2},1032,["title","onConfirm"]),0===e.status?(f(),v(r(J),{key:0,style:{color:"red",cursor:"pointer"},title:"未读",class:"btn",onClick:s=>(e=>{0===e.status&&ae({messageId:e.id}),X.newsInfo=e,X.showNews=!0})(e)},null,8,["onClick"])):h("",!0)])];var s})),_:2},1024)))),128))])),_:1},8,["value"])])),_:1},8,["loading"]),i("div",Z,[l(fe,{total:r(ue),"show-total":e=>`共 ${e} 条`,"show-less-items":!0,"page-size":X.pageSize,"show-size-changer":"",onChange:le},null,8,["total","show-total","page-size"])]),l(_e,{footer:null,title:"消息详情",open:r(ve),"wrap-class-name":"noticeModal",onCancel:ne},{default:d((()=>[i("div",G,[i("div",null,w(r(ge).message),1),r(ge).attachmentAddress?(f(),o("div",P,[i("span",Q,[l(ye,{type:"link",disabled:0!==r(je),class:"downbtn",onClick:s[3]||(s[3]=e=>(window.location.href=X.url+X.newsInfo.attachmentAddress,X.downTime=3,void re()))},{default:d((()=>[i("span",W,w(0===r(je)?"":`${r(je)}s `),1),s[7]||(s[7]=n("下载资源包 "))])),_:1},8,["disabled"])])])):h("",!0)])])),_:1},8,["open"])])}}}),[["__scopeId","data-v-3f41c104"]]);export{X as default};
