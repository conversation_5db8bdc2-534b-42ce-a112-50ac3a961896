import{d as e,r as a,f as s,b as t,o as l,e as i,c as o,ae as r,aa as n,J as p,a9 as c,ab as d,a5 as u,ad as m,u as v,a7 as j}from"./@vue-HScy-mz9.js";import y from"./AddEditForm-Bc2-chvN.js";import{a as h,a7 as g,a8 as f,b,a9 as k}from"./main-Djn9RDyT.js";import{u as w}from"./useTableScrollY-DAiBD3Av.js";import{I,B as x,e as C,f as z,g as _}from"./ant-design-vue-DYY9BtJq.js";import{_ as S}from"./vue-qr-CB2aNKv5.js";import"./IconManage-esGETqvN.js";import"./axios-7z2hFSF6.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */const N={class:"func-module"},P={class:"search-wrap"},M={class:"search-content"},R={class:"search-item"},E={class:"search-item"},J={class:"search-btns"},K={class:"table-handle"},T={class:"table-wrap"},U={class:"action"},A={key:1},B={key:2},F={key:3,class:"table-actions"},L=["onClick"],O={class:"pagination"},Y=S(e({__name:"Index",setup(e){h();const S=[{title:"模块名称",dataIndex:"name",ellipsis:!0},{title:"唯一编码",dataIndex:"code",ellipsis:!0},{title:"图标",dataIndex:"icon"},{title:"是否默认",dataIndex:"active",width:150},{title:"是否显示",dataIndex:"visible",width:150},{title:"排序",dataIndex:"sort",ellipsis:!0,width:150},{title:"操作",dataIndex:"option",width:200}],Y=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),$=(e,a)=>{Y.value=Object.assign(Y.value,{current:e,pageSize:a}),G()},q=a(),{scrollY:D}=w(q),Q=a({name:null,code:null,sysCategoryId:null}),V=a(!1),Z=a([]);s((()=>{G()}));const G=()=>{Z.value=[],V.value=!0,g({...Q.value,pageNo:Y.value.current,pageSize:Y.value.pageSize}).then((e=>{if(200===e.code){const{rows:a,pageNo:s,pageSize:t,totalRows:l}=e.data;Z.value=a,Y.value.current=s,Y.value.pageSize=t,Y.value.total=l}V.value=!1})).catch((()=>{V.value=!1}))},H=()=>{Y.value.current=1,Y.value.pageSize=10,G()},W=a(),X=(e,a)=>{W.value.init(e,a)},ee=()=>{G()};return(e,a)=>{const s=I,h=x,g=C,w=z,ae=_;return l(),t("div",N,[i("div",P,[i("div",M,[i("div",R,[a[6]||(a[6]=i("span",{class:"search-label"},"功能模块名称",-1)),i("div",null,[o(s,{value:Q.value.name,"onUpdate:value":a[0]||(a[0]=e=>Q.value.name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入功能模块名称",class:"search-input",onKeyup:a[1]||(a[1]=r((e=>H()),["enter"]))},null,8,["value"])])]),i("div",E,[a[7]||(a[7]=i("span",{class:"search-label"},"唯一编码",-1)),i("div",null,[o(s,{value:Q.value.code,"onUpdate:value":a[2]||(a[2]=e=>Q.value.code=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入唯一编码",class:"search-input",onKeyup:a[3]||(a[3]=r((e=>H()),["enter"]))},null,8,["value"])])]),i("div",J,[o(h,{type:"primary",class:"search-btn",onClick:a[4]||(a[4]=e=>H())},{default:n((()=>a[8]||(a[8]=[p(" 查询 ")]))),_:1})])]),i("div",K,[e.hasPerm("sys-app:add")?(l(),c(h,{key:0,type:"primary",class:"handle-btn",onClick:a[5]||(a[5]=e=>X("add",null))},{default:n((()=>a[9]||(a[9]=[p(" 新增功能模块 ")]))),_:1})):d("",!0)])]),i("div",T,[i("div",{ref_key:"table",ref:q,class:"table-content"},[e.hasPerm("sys-app:page")?(l(),c(w,{key:0,class:"table",scroll:{y:v(D)},pagination:!1,size:"small",loading:V.value,"row-key":e=>e.id,columns:S,"data-source":Z.value,onChange:$},{headerCell:n((({column:e})=>[i("div",U,m(e.title),1)])),bodyCell:n((({column:s,record:o})=>["icon"===s.dataIndex?(l(),t("div",{key:0,class:u(["icon iconfont",o.icon])},null,2)):d("",!0),"active"===s.dataIndex?(l(),t("div",A,m("N"===o.active?"否":"是"),1)):d("",!0),"visible"===s.dataIndex?(l(),t("div",B,m("N"===o.visible?"否":"是"),1)):d("",!0),"option"===s.dataIndex?(l(),t("div",F,[e.hasPerm("sys-app:edit")?(l(),t("a",{key:0,type:"text",onClick:e=>X("edit",o)},"编辑",8,L)):d("",!0),e.hasPerm("sys-app:delete")?(l(),c(g,{key:1,placement:"topRight",title:"确认删除？",onConfirm:e=>(e=>{f({...e,sysCategoryId:Q.value.sysCategoryId}).then((e=>{e.success?(b("success","功能模块删除成功"),G()):b("error",e.message)}))})(o)},{default:n((()=>a[10]||(a[10]=[i("a",{type:"text"},"删除",-1)]))),_:2},1032,["onConfirm"])):d("",!0),e.hasPerm("sys-app:set-as-default")&&"N"==o.active?(l(),c(g,{key:2,placement:"topRight",title:"设置为默认功能模块？",onConfirm:e=>(e=>{V.value=!0,k({id:e.id}).then((e=>{V.value=!1,e.success?(b("success","默认模块设置成功"),G()):b("error",e.message)})).catch((()=>{V.value=!1}))})(o)},{default:n((()=>a[11]||(a[11]=[i("a",{type:"text"},"设置默认",-1)]))),_:2},1032,["onConfirm"])):d("",!0)])):d("",!0)])),_:1},8,["scroll","loading","row-key","data-source"])):d("",!0),i("div",O,[Z.value.length>0?(l(),c(ae,j({key:0},Y.value,{onChange:$}),null,16)):d("",!0)])],512)]),o(y,{ref_key:"addEditFormRef",ref:W,onOk:ee},null,512)])}}}),[["__scopeId","data-v-d541a7c3"]]);export{Y as default};
