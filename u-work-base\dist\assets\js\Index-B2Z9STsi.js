import{d as e,r as a,f as s,b as t,o as l,e as o,c as i,aa as r,F as n,ag as d,a9 as p,J as c,ad as m,ae as u,ab as v,a5 as j,u as h}from"./@vue-HScy-mz9.js";import y from"./AddEditForm-BJsUa4me.js";import{a as f,ae as g,ad as w,b as I,af as b}from"./main-Djn9RDyT.js";import{u as x}from"./useTableScrollY-DAiBD3Av.js";import{x as k,w as C,I as _,B as F,e as z,f as R}from"./ant-design-vue-DYY9BtJq.js";import{_ as E}from"./vue-qr-CB2aNKv5.js";import"./IconManage-BfCKI8e5.js";import"./axios-7z2hFSF6.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */const A={class:"menu-manage"},M={class:"search-wrap"},N={class:"search-content"},P={class:"search-item"},S={class:"search-item"},Y={class:"search-btns"},B={class:"table-handle"},D={class:"table-wrap"},J={class:"action"},K={key:1},L={key:2,class:"table-actions"},T=["onClick"],U=E(e({__name:"Index",setup(e){f();const E=[{title:"菜单名称",dataIndex:"name",width:"20%",ellipsis:!0,sorter:!0},{title:"菜单类型",dataIndex:"type",width:"15%",ellipsis:!0,sorter:!0},{title:"图标",dataIndex:"icon",width:"10%"},{title:"功能模块",dataIndex:"applicationName",width:"20%",ellipsis:!0,sorter:!0},{title:"菜单编号",dataIndex:"code",width:"20%",ellipsis:!0,sorter:!0},{title:"组件",dataIndex:"component",width:"25%",sorter:!0,ellipsis:!0},{title:"路由地址",dataIndex:"router",width:"20%",ellipsis:!0,sorter:!0},{title:"排序",dataIndex:"sort",width:"20%"},{title:"操作",dataIndex:"option",width:"20%"}],U=["目录","菜单","按钮","插件"],q=a(),{scrollY:O}=x(q),X=(e,a,s)=>{"applicationName"===s.field?Z.value.sortField="application":Z.value.sortField=s.field,s.order&&"ascend"===s.order?Z.value.sortRule="ASC":s.order&&"descend"===s.order?Z.value.sortRule="DESC":(Z.value.sortField="",Z.value.sortRule=""),H()},Z=a({name:null,application:null,sortField:"",sortRule:"",sysCategoryId:null}),$=a(!1),G=a([]);s((()=>{ee(),H()}));const H=()=>{G.value=[],$.value=!0,g(Z.value).then((e=>{if(200===e.code){const a=Q(e.data);G.value=a}$.value=!1})).catch((()=>{$.value=!1}))},Q=e=>(e.forEach((e=>{e.children.length?Q(e.children):delete e.children})),e),V=a([]),W=a([]),ee=()=>{Z.value.application=null,w({sysCategoryId:Z.value.sysCategoryId}).then((e=>{e.success?(V.value=e.data||[],W.value=V.value):I("error",e.message)}))},ae=a(),se=(e,a)=>{ae.value.init(e,a)},te=()=>{H()};return(e,a)=>{const s=k,f=C,g=_,w=F,x=z,Q=R;return l(),t("div",A,[o("div",M,[o("div",N,[o("div",P,[a[7]||(a[7]=o("span",{class:"search-label"},"功能模块名称",-1)),o("div",null,[i(f,{value:Z.value.application,"onUpdate:value":a[0]||(a[0]=e=>Z.value.application=e),class:"search-select","allow-clear":"",placeholder:"请选择功能模块分类",onChange:a[1]||(a[1]=e=>H())},{default:r((()=>[(l(!0),t(n,null,d(W.value,((e,a)=>(l(),p(s,{key:a,value:e.code},{default:r((()=>[c(m(e.name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])]),o("div",S,[a[8]||(a[8]=o("span",{class:"search-label"},"菜单名称",-1)),o("div",null,[i(g,{value:Z.value.name,"onUpdate:value":a[2]||(a[2]=e=>Z.value.name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入菜单名称",class:"search-input",onKeyup:a[3]||(a[3]=u((e=>H()),["enter"])),onChange:a[4]||(a[4]=e=>H())},null,8,["value"])])]),o("div",Y,[i(w,{type:"primary",class:"search-btn",onClick:a[5]||(a[5]=e=>H())},{default:r((()=>a[9]||(a[9]=[c(" 查询 ")]))),_:1})])]),o("div",B,[e.hasPerm("sys-menu:add")?(l(),p(w,{key:0,type:"primary",class:"handle-btn",onClick:a[6]||(a[6]=e=>se("add",null))},{default:r((()=>a[10]||(a[10]=[c(" 新增菜单 ")]))),_:1})):v("",!0)])]),o("div",D,[o("div",{ref_key:"table",ref:q,class:"table-content"},[i(Q,{pagination:!1,scroll:{y:h(O)},"show-pagination":"auto",size:"small",loading:$.value,"row-key":e=>e.id,columns:E,"data-source":G.value,onChange:X},{headerCell:r((({column:e})=>[o("div",J,m(e.title),1)])),bodyCell:r((({column:s,record:i})=>["icon"===s.dataIndex?(l(),t("div",{key:0,class:j(["icon iconfont",i.icon])},null,2)):v("",!0),"type"===s.dataIndex?(l(),t("div",K,m(U[i[s.dataIndex]]),1)):v("",!0),"option"===s.dataIndex?(l(),t("div",L,[e.hasPerm("sys-menu:edit")?(l(),t("a",{key:0,type:"text",onClick:e=>se("edit",i)},"编辑",8,T)):v("",!0),e.hasPerm("sys-menu:delete")?(l(),p(x,{key:1,placement:"topRight",title:"确认删除？",onConfirm:e=>(e=>{b({...e,sysCategoryId:Z.value.sysCategoryId}).then((e=>{e.success?(I("success","菜单删除成功"),H()):I("error",e.message)}))})(i)},{default:r((()=>a[11]||(a[11]=[o("a",{type:"text"},"删除",-1)]))),_:2},1032,["onConfirm"])):v("",!0)])):v("",!0)])),_:1},8,["scroll","loading","row-key","data-source"])],512)]),i(y,{ref_key:"addEditFormRef",ref:ae,onOk:te},null,512)])}}}),[["__scopeId","data-v-5026ade9"]]);export{U as default};
