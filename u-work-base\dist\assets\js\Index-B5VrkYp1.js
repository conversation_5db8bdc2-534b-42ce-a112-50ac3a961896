import{d as e,r as a,f as s,w as t,am as l,b as i,o as r,e as o,a9 as n,c as d,aa as c,u,ad as p,a5 as m,ae as v,J as h,ab as y,a7 as f}from"./@vue-HScy-mz9.js";import{n as j,c as g,d as k,f as w}from"./department-CTxHSeTj.js";import{b}from"./main-Djn9RDyT.js";import{I as _,m as x,n as C,B as I,e as z,f as S,g as P,M as E}from"./ant-design-vue-DYY9BtJq.js";import{S as K,T}from"./@ant-design-CA72ad83.js";import{_ as O}from"./vue-qr-CB2aNKv5.js";import R from"./AddEditForm-CUlXWKPY.js";import{u as A}from"./useTableScrollY-DAiBD3Av.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const D={class:"tree-wrap"},L={class:"tree-contain"},M={class:"tree-search"},N={key:1,class:"tree-content"},J=["title"],U={class:"title"},B=O(e({__name:"CompanyDepartGroup",emits:["handleClick","onBack"],setup(e,{expose:v,emit:h}){const y=h,f=a({children:"children",title:"name",value:"id",key:"id"}),g=a(""),k=a([]);let w=[];const I=a([]),z=a([]);s((()=>{P()}));const S=async()=>{const e=await j({});if(200!==e.code)return void b("error",e.message);const{data:a}=e;k.value=a,z.value.push(a[0].id),z.value.push(a[1].id),w=[...k.value]},P=async()=>{const e=await j({});if(200!==e.code)return void b("error",e.message);const{data:a}=e,s=[a];k.value=s,w=[...k.value],z.value.push(s[0].id),I.value=[s[0].id],y("handleClick",s[0])},E=(e,a)=>{e.length?(I.value=e,y("handleClick",a.node.dataRef)):I.value=[a.node.dataRef.id]};t(g,(e=>{k.value=T(w,e)}));const T=(e,a)=>{const s=e&&JSON.parse(JSON.stringify(e));if(!s||!s.length)return[];const t=[];for(const l of s){const e=T(l.children,a);l.title.indexOf(a)>-1?t.push(l):e&&e.length&&(l.children=e,t.push(l))}return t.length?t:[]};return v({update:()=>{S()}}),(e,a)=>{const s=_,t=l("down-outlined"),v=C;return r(),i("div",D,[o("div",L,[o("div",M,[d(s,{value:g.value,"onUpdate:value":a[0]||(a[0]=e=>g.value=e),valueModifiers:{trim:!0},class:"filter",placeholder:"请输入搜索内容","allow-clear":!0},{suffix:c((()=>[d(u(K))])),_:1},8,["value"])]),0===k.value.length?(r(),n(u(x),{key:0,image:u(x).PRESENTED_IMAGE_SIMPLE},null,8,["image"])):(r(),i("div",N,[d(v,{selectedKeys:I.value,"onUpdate:selectedKeys":a[1]||(a[1]=e=>I.value=e),expandedKeys:z.value,"onUpdate:expandedKeys":a[2]||(a[2]=e=>z.value=e),"show-icon":"","tree-data":k.value,class:"cus-tree","default-expand-all":!1,"field-names":f.value,onSelect:E},{switcherIcon:c((({switcherCls:e})=>[d(t,{class:m(e)},null,8,["class"])])),title:c((e=>[o("span",{class:"root-tree-item",title:e.name},[o("span",U,p(e.name),1)],8,J)])),_:1},8,["selectedKeys","expandedKeys","tree-data","field-names"])]))])])}}}),[["__scopeId","data-v-b1f3d5a1"]]),G={class:"depart-manage"},F={class:"left"},Y={class:"right"},$={class:"search-wrap"},q={class:"search-content"},H={class:"search-item"},Q={class:"search-btns"},Z={class:"table-handle"},V={class:"table-wrap"},W={key:0,class:"table-actions"},X=["onClick"],ee={key:0,type:"text"},ae={class:"pagination"},se=O(e({__name:"Index",setup(e){const t=[{title:"部门名称",dataIndex:"name",width:"20%",ellipsis:!0},{title:"唯一编码",dataIndex:"code",width:"20%",ellipsis:!0},{title:"排序",dataIndex:"sort",width:"20%"},{title:"备注",dataIndex:"remark",width:"25%",ellipsis:!0},{title:"操作",dataIndex:"action",width:"15%"}],l=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),p=(e,a)=>{l.value=Object.assign(l.value,{current:e,pageSize:a}),M()},m=a({keys:[],list:[]}),j={onChange:(e,a)=>{m.value.keys=e,m.value.list=a},getCheckboxProps:e=>({disabled:"LDAP"===e.code})};a();const x=a({name:null,pid:"0",enterpriseId:"",source:""}),C=a(!1),K=a([]),O=a(),{scrollY:D}=A(O);s((()=>{}));const L=()=>{l.value.current=1,l.value.pageSize=10,M()},M=()=>{K.value=[],C.value=!0,g({...x.value,pageNo:l.value.current,pageSize:l.value.pageSize}).then((e=>{if(200===e.code){const{rows:a,pageNo:s,pageSize:t,totalRows:i}=e.data;K.value=a,l.value.current=s,l.value.pageSize=t,l.value.total=i}C.value=!1})).catch((()=>{C.value=!1}))},N=a(),J=()=>{E.confirm({title:"提示",content:"确定要删除吗 ?",okText:"确定",cancelText:"取消",onOk:()=>{U()}})},U=()=>{C.value=!0,w({ids:m.value.keys}).then((e=>{e.success?(b("success","部门删除成功"),N.value.update(),L()):b("error",e.message),C.value=!1})).catch((()=>{C.value=!1})).finally((()=>{m.value.keys=[],m.value.list=[]}))};let se=a("");const te=e=>{x.value.pid="dept"===e.source?e.id:"0",x.value.enterpriseId=e.enterpriseId,"0"===e.id&&(x.value.enterpriseId="-1"),se.value=e.source,L()},le=a(),ie=(e,a)=>{le.value.init(e,a,x.value.pid,x.value.enterpriseId,se.value)},re=()=>{N.value.update(),L()};return(e,a)=>{const s=_,g=I,w=z,E=S,A=P;return r(),i("div",G,[o("div",F,[d(B,{ref_key:"departGroupRef",ref:N,class:"side-menu",onHandleClick:te},null,512)]),o("div",Y,[o("div",$,[o("div",q,[o("div",H,[a[4]||(a[4]=o("span",{class:"search-label"},"部门名称",-1)),d(s,{value:x.value.name,"onUpdate:value":a[0]||(a[0]=e=>x.value.name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入部门名称",class:"search-input",onKeyup:a[1]||(a[1]=v((e=>L()),["enter"]))},null,8,["value"])]),o("div",Q,[d(g,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>L())},{default:c((()=>a[5]||(a[5]=[h(" 查询 ")]))),_:1})])]),o("div",Z,[e.hasPerm("sys-dept:add")?(r(),n(g,{key:0,type:"primary",class:"handle-btn",onClick:a[3]||(a[3]=e=>ie("add",null))},{default:c((()=>a[6]||(a[6]=[h(" 新增部门 ")]))),_:1})):y("",!0),e.hasPerm("sys-dept:delete-batch")?(r(),n(g,{key:1,class:"handle-btn",disabled:m.value.keys.length<=0,onClick:J},{icon:c((()=>[d(u(T))])),default:c((()=>[a[7]||(a[7]=h(" 批量删除 "))])),_:1},8,["disabled"])):y("",!0)])]),o("div",V,[o("div",{ref_key:"table",ref:O,class:"table-content"},[e.hasPerm("sys-dept:page")?(r(),n(E,{key:0,class:"table",scroll:{y:u(D)},pagination:!1,size:"small",loading:C.value,"row-key":e=>e.id,"row-selection":j,columns:t,"data-source":K.value},{bodyCell:c((({column:a,record:s})=>["action"===a.dataIndex?(r(),i("div",W,[e.hasPerm("sys-dept:edit")&&"LDAP"!==s.code?(r(),i("a",{key:0,type:"text",onClick:e=>ie("edit",s)},"编辑",8,X)):y("",!0),d(w,{placement:"topRight",title:"确认删除？",onConfirm:()=>(e=>{k(e).then((e=>{e.success?(b("success","部门删除成功"),N.value.update(),L()):b("error",e.message)})).finally((()=>{m.value.keys=[],m.value.list=[]}))})(s)},{default:c((()=>[e.hasPerm("sys-dept:delete")&&"LDAP"!==s.code?(r(),i("a",ee,"删除")):y("",!0)])),_:2},1032,["onConfirm"])])):y("",!0)])),_:1},8,["scroll","loading","row-key","data-source"])):y("",!0),o("div",ae,[K.value.length>0?(r(),n(A,f({key:0},l.value,{onChange:p}),null,16)):y("",!0)])],512)])]),d(R,{ref_key:"addEditFormRef",ref:le,onOk:re},null,512)])}}}),[["__scopeId","data-v-6a840e64"]]);export{se as default};
