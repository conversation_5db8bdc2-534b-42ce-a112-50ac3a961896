import e from"./DepartGroup-C8tyH3Q7.js";import{b as s,d as a}from"./main-Djn9RDyT.js";import{u as t}from"./useTableScrollY-DAiBD3Av.js";import{g as r,d as l,f as i,h as o,i as d}from"./userManage-DLgtGpjc.js";import n from"./AddAndEdit-DJ5_Jshu.js";import c from"./UploadUser-Diy0poFQ.js";import{_ as p}from"./UserRole.vue_vue_type_script_setup_true_lang-BWppbLdA.js";import u from"./ResetPassword-DVtvycd1.js";import{s as m}from"./dictionaryManage-gGpiMShb.js";import{I as h,x as y,w as v,B as f,d as k,e as g,Y as j,D as w,u as I,v as b,f as _,g as D,M as C}from"./ant-design-vue-DYY9BtJq.js";import{ab as P,a0 as x,T as R,D as N}from"./@ant-design-CA72ad83.js";import{d as T,a as S,f as z,r as U,p as V,am as A,b as L,o as K,e as O,c as $,aa as E,F as M,ag as W,a9 as Y,J as F,ad as G,ab as H,u as J,a5 as B,a7 as Z}from"./@vue-HScy-mz9.js";import{_ as q}from"./vue-qr-CB2aNKv5.js";import"./department-CTxHSeTj.js";import"./AddEditDepart-BzdtS7BQ.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./getThinyColor-DtFDxjua.js";import"./tinycolor2-DJ_qK68I.js";import"./uuid-P-OY1HC2.js";const Q={class:"user-manage"},X={class:"left"},ee={class:"right"},se={class:"search-wrap"},ae={class:"search-content"},te={class:"search-item"},re={class:"search-item"},le={class:"search-btns"},ie={class:"table-handle"},oe={class:"table-wrap"},de={class:"table-detail"},ne=["title"],ce=["title"],pe=["title"],ue=["title"],me={key:1,style:{color:"#20e333"}},he={key:2,style:{color:"#f85f5b"}},ye={key:3,style:{color:"#f7d23c"}},ve=["onClick"],fe={key:5,class:"table-actions"},ke=["onClick"],ge=["onClick"],je={class:"ant-dropdown-link"},we=["onClick"],Ie={key:0},be={class:"pagination"},_e=q(T({__name:"Index",setup(T){const q=[{title:"账号",dataIndex:"account",sortDirections:["descend","ascend"],sorter:!0,ellipsis:!0,minWidth:80},{title:"姓名",dataIndex:"name",sortDirections:["descend","ascend"],sorter:!0,ellipsis:!0,minWidth:80},{title:"注册时间",dataIndex:"createTime",sortDirections:["descend","ascend"],sorter:!0,ellipsis:!0,minWidth:50},{title:"上次登录时间",dataIndex:"lastLoginTime",sortDirections:["descend","ascend"],sorter:!0,ellipsis:!0,minWidth:50},{title:"密码有效期",dataIndex:"passwordPeriod",key:"passwordPeriod",sortDirections:["descend","ascend"],sorter:!0,width:200},{title:"账号有效期",dataIndex:"passwordPeriodTemp",key:"passwordPeriodTemp",sortDirections:["descend","ascend"],sorter:!0,width:200},{title:"所属部门",key:"deptName",ellipsis:!0,width:150},{title:"状态",key:"status",dataIndex:"status",sortDirections:["descend","ascend"],sorter:!0,width:100},{title:"操作",key:"action",width:200,minWidth:200}],_e=S({pageNo:1,pageSize:10,searchValue:"",searchStatus:null,tableData:[],selectedRowKeys:[],enterpriseId:"",total:0,deptId:"",deptName:"",loading:!1});z((()=>{Ce("YONG_HU_ZHUANG_TAI")}));const De=U([]),Ce=e=>{m({code:e}).then((e=>{const{code:a,data:t,message:r}=e;200===a?De.value=t:s("error",r)}))},Pe=()=>{_e.pageNo=1,_e.pageSize=10,xe()},xe=(e={})=>{_e.loading=!0,_e.tableData=[],_e.total=0;const s={pageNo:_e.pageNo,pageSize:_e.pageSize,searchValue:_e.searchValue,status:_e.searchStatus,enterpriseId:_e.enterpriseId,"sysUserDetailParam.deptId":_e.deptId,...e};r(s).then((e=>{_e.tableData=e.data.rows,_e.total=e.data.totalRows})).finally((()=>{_e.loading=!1})).catch((()=>{_e.loading=!1}))},Re=V((()=>({total:_e.total,current:_e.pageNo,pageSize:_e.pageSize,showTotal:e=>`共 ${e} 条`,showQuickJumper:!0,showSizeChanger:!0,size:"small"}))),Ne=e=>{_e.selectedRowKeys=e},Te=(e,s,a)=>{let t={};t=a.order?{sortField:a.field,sortRule:"descend"===a.order?"DESC":"ASC"}:{sortField:"",sortRule:""},xe(t)},Se=U(),{scrollY:ze}=t(Se),Ue=(e,s)=>{_e.pageNo=e,_e.pageSize=s,xe()},Ve=e=>{"dept"===e.source?_e.deptId=e.id:_e.deptId="",_e.deptName=e.name,_e.enterpriseId=e.enterpriseId,"0"===e.id&&(_e.enterpriseId="-1"),_e.enterpriseId||delete _e.enterpriseId,_e.deptId||delete _e.deptId,Pe()},Ae=U(),Le=()=>{const e="0"===_e.deptId?"":_e.deptId,s="0"===_e.deptId?"":_e.deptName;Ae.value.init("add",{deptId:e,deptName:s,enterpriseId:_e.enterpriseId})},Ke=U(),Oe=()=>{const e="0"===_e.deptId?"":_e.deptId,s="0"===_e.deptId?"":_e.deptName;Ke.value.showModal(e,s,_e.enterpriseId)},$e=U(!1),Ee=()=>{C.confirm({title:"提示",content:"确定要删除吗 ?",okText:"确定",cancelText:"取消",onOk:()=>{Me()}})},Me=()=>{const e=_e.selectedRowKeys;d({ids:e}).then((e=>{200===e.code?(_e.selectedRowKeys=[],s("success","用户删除成功")):s("error",`用户删除失败：${e.message}`)})).finally((()=>{Pe()}))},We=U(),Ye=U();return(t,r)=>{const d=h,m=y,C=v,T=f,S=k,z=g,U=j,V=A("LinkOutlined"),Ce=b,Me=I,Fe=w,Ge=_,He=D;return K(),L("div",Q,[O("div",X,[$(e,{class:"side-menu",onHandleClick:Ve})]),O("div",ee,[O("div",se,[O("div",ae,[O("div",te,[r[4]||(r[4]=O("span",{class:"search-label"},"关键词",-1)),$(d,{value:_e.searchValue,"onUpdate:value":r[0]||(r[0]=e=>_e.searchValue=e),valueModifiers:{trim:!0},class:"search-input","allow-clear":"",placeholder:"请输入账号或姓名",onPressEnter:Pe},null,8,["value"])]),O("div",re,[r[5]||(r[5]=O("span",{class:"search-label"},"状态",-1)),$(C,{value:_e.searchStatus,"onUpdate:value":r[1]||(r[1]=e=>_e.searchStatus=e),class:"search-select","allow-clear":"",placeholder:"请选择状态",onChange:Pe},{default:E((()=>[(K(!0),L(M,null,W(De.value,((e,s)=>(K(),Y(m,{key:s,value:e.code},{default:E((()=>[F(G(e.value),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])]),O("div",le,[$(T,{type:"primary",class:"search-btn",onClick:r[2]||(r[2]=e=>Pe())},{default:E((()=>r[6]||(r[6]=[F(" 查询 ")]))),_:1})])]),O("div",ie,[t.hasPerm("sys-user:add")?(K(),Y(T,{key:0,class:"handle-btn",type:"primary",onClick:Le},{default:E((()=>r[7]||(r[7]=[F("新增用户")]))),_:1})):H("",!0),$(S,{placement:"top"},{title:E((()=>r[8]||(r[8]=[F(" 请在左侧选中导入用户的所属部门 ")]))),default:E((()=>[t.hasPerm("sys-user:import-user-excel")?(K(),Y(T,{key:0,class:"handle-btn",disabled:"0"===_e.deptId||!_e.deptId,onClick:Oe},{icon:E((()=>[$(J(P))])),default:E((()=>[r[9]||(r[9]=F(" 导入 "))])),_:1},8,["disabled"])):H("",!0)])),_:1}),$(z,{placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:r[3]||(r[3]=()=>(()=>{const e={sysUserDetailParam:{deptId:_e.deptId},ids:_e.selectedRowKeys};_e.searchValue&&(e.searchValue=_e.searchValue),_e.searchStatus&&(e.searchStatus=_e.searchStatus),$e.value=!0,l(e).then((e=>{a(e),$e.value=!1})).catch((()=>{$e.value=!1}))})())},{title:E((()=>r[10]||(r[10]=[O("p",null,"该操作将对选中的用户数据进行下载",-1),O("p",null,"若未选中，将下载全部，是否继续？",-1)]))),default:E((()=>[t.hasPerm("sys-user:export")?(K(),Y(T,{key:0,class:"handle-btn"},{icon:E((()=>[$(J(x))])),default:E((()=>[r[11]||(r[11]=F("导出 "))])),_:1})):H("",!0)])),_:1}),t.hasPerm("sys-user:delete-batch")?(K(),Y(T,{key:1,class:"handle-btn",disabled:_e.selectedRowKeys.length<=0,onClick:Ee},{icon:E((()=>[$(J(R))])),default:E((()=>[r[12]||(r[12]=F("删除"))])),_:1},8,["disabled"])):H("",!0)])]),O("div",oe,[O("div",{ref_key:"table",ref:Se,class:"table-content"},[t.hasPerm("sys-user:page")?(K(),Y(Ge,{key:0,class:"table",style:{"min-width":"1200px"},scroll:{y:J(ze)},pagination:!1,loading:_e.loading,size:"small",columns:q,"data-source":_e.tableData,"row-selection":{selectedRowKeys:_e.selectedRowKeys,onChange:Ne},"row-key":e=>e.id,onChange:Te},{expandedRowRender:E((({record:e})=>[O("div",de,[O("p",null,"授权角色："+G(e.role),1),r[13]||(r[13]=O("div",{class:"line"},null,-1)),O("p",null,"手机："+G(e.phone),1),r[14]||(r[14]=O("div",{class:"line"},null,-1)),O("p",null,"邮箱："+G(e.email),1)])])),bodyCell:E((({column:e,record:a,text:l})=>{var d,n,c,p;return["deptName"===e.key?(K(),L("span",{key:0,title:(null==(d=a.sysUserDetailVo)?void 0:d.deptName)||""},G((null==(n=a.sysUserDetailVo)?void 0:n.deptName)||""),9,ne)):H("",!0),"createTime"===e.dataIndex?(K(),L("span",{key:1,title:a.createTime||""},G((null==(c=a.createTime)?void 0:c.split(" ")[0])||""),9,ce)):H("",!0),"lastLoginTime"===e.dataIndex?(K(),L("span",{key:2,title:a.lastLoginTime||""},G((null==(p=a.lastLoginTime)?void 0:p.split(" ")[0])||""),9,pe)):H("",!0),"passwordPeriodTemp"===e.dataIndex?(K(),L("span",{key:3,title:a.passwordPeriodTemp||""},G(a.passwordPeriodTemp?a.passwordPeriodTemp:"永久"),9,ue)):H("",!0),"status"===e.key?(K(),L(M,{key:4},[0===l||1===l?(K(),Y(z,{key:0,placement:"top",title:0===l?"确定停用该用户？":"确定启用该用户？",onConfirm:()=>(e=>{let a=0;0===e.status?a=1:1===e.status&&(a=0),o({id:e.id,status:a}).then((e=>{e.success?(s("success","状态切换成功"),xe()):s("error",`操作失败：${e.message}`)}))})(a)},{default:E((()=>[$(U,{disabled:!t.hasPerm("sys-user:change-status"),"checked-children":"正常","un-checked-children":"停用",checked:0===l},null,8,["disabled","checked"])])),_:2},1032,["title","onConfirm"])):-1===l?(K(),L("div",me,"初始")):2===l?(K(),L("div",he,"删除")):3===l?(K(),L("div",ye,[r[15]||(r[15]=F(" 休眠")),O("span",{onClick:e=>(e=>{o({id:e.id,status:0}).then((e=>{e.success?(s("success","解除休眠成功"),xe()):s("error",`操作失败：${e.message}`)}))})(a),title:"解除休眠",style:{"margin-left":"5px","font-size":"16px",color:"#20e333",cursor:"pointer"}},[$(V,{title:"解除休眠"})],8,ve)])):H("",!0)],64)):H("",!0),"action"===e.key?(K(),L("div",fe,[t.hasPerm("sys-user:edit")?(K(),L("a",{key:0,onClick:e=>{return s=a,void Ae.value.init("edit",{enterpriseId:_e.enterpriseId},s.id);var s}},"编辑",8,ke)):H("",!0),t.hasPerm("sys-role:grant-menu")?(K(),L("a",{key:1,onClick:e=>{return s=a,void We.value.userRole(s,s.enterpriseId||"1");var s}},"授权角色",8,ge)):H("",!0),$(Fe,null,{overlay:E((()=>[$(Me,null,{default:E((()=>{var e;return[$(Ce,{disabled:"LDAP"===(null==(e=a.sysUserDetailVo)?void 0:e.deptName)},{default:E((()=>{var e;return[t.hasPerm("sys-user:reset-pwd")?(K(),L("a",{key:0,class:B("LDAP"===(null==(e=a.sysUserDetailVo)?void 0:e.deptName)?"non-cursor":""),onClick:e=>(e=>{var s;"LDAP"!==(null==(s=e.sysUserDetailVo)?void 0:s.deptName)&&Ye.value.add(e)})(a)},"重置密码",10,we)):H("",!0)]})),_:2},1032,["disabled"]),$(Ce,null,{default:E((()=>[$(z,{placement:"topRight",title:"确认删除？",onConfirm:()=>{i(a).then((e=>{e.success?(s("success","用户删除成功"),xe()):s("error",`用户删除失败：${e.message}`)})).catch((e=>{s("error",`用户删除失败：${e.message}`)}))}},{default:E((()=>[t.hasPerm("sys-user:delete")?(K(),L("a",Ie,"删除")):H("",!0)])),_:2},1032,["onConfirm"])])),_:2},1024)]})),_:2},1024)])),default:E((()=>[O("a",je,[r[16]||(r[16]=F(" 更多 ")),$(J(N))])])),_:2},1024)])):H("",!0)]})),_:1},8,["scroll","loading","data-source","row-selection","row-key"])):H("",!0),O("div",be,[_e.tableData.length>0?(K(),Y(He,Z({key:0},Re.value,{onChange:Ue}),null,16)):H("",!0)])],512)])]),$(n,{ref_key:"addAndEditRef",ref:Ae,onOk:xe},null,512),$(c,{ref_key:"uploadUserRef",ref:Ke,onOk:xe},null,512),$(p,{ref_key:"userRoleRef",ref:We,onOk:xe},null,512),$(u,{ref_key:"resetPasswordRef",ref:Ye,onOk:xe},null,512)])}}}),[["__scopeId","data-v-27c5e9cf"]]);export{_e as default};
