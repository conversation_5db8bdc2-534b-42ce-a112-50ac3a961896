import{d as e,r as a,w as s,a as t,am as l,b as i,o,e as r,c as n,F as c,ag as u,ab as d,ad as p,aa as m,J as v,a9 as h,ae as g,y,G as j,u as f,a5 as k,a4 as w}from"./@vue-HScy-mz9.js";import{p as b}from"./projectGallery-DFHuwUAq.js";import _ from"./Nodata-mmdoiDH6.js";import{d as C}from"./dayjs-CA7qlNSr.js";import z from"./AddExample-Cqw7liZ4.js";import{c as x,g as N,h as T,d as U,b as S,f as I}from"./chart-CjrRHupv.js";import{C as O,b as P}from"./main-Djn9RDyT.js";import{q as E,r as R,S as $,I as q,x as G,w as L,B as A,e as J,d as M,g as Y}from"./ant-design-vue-DYY9BtJq.js";import{_ as D}from"./vue-qr-CB2aNKv5.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./@babel-B4rXMRun.js";import"./chart-template-DJui4TvV.js";import"./@vueup-CLVdhRgW.js";import"./quill-BBEhJLA6.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";const H={class:"sample-list"},B={class:"content-wrap"},K={class:"tag-search"},F={key:0,class:"tag-content"},X=["onClick"],Z={key:1,class:"tag-item"},Q={key:1,class:"no-tag-content"},V={class:"content-list"},W={class:"search"},ee={class:"search-wrap"},ae={key:0,class:"list"},se={class:"contain"},te={class:"img-box"},le={class:"img-item"},ie=["src"],oe={class:"bottom-wrapper"},re={class:"bottom-content"},ne={class:"time"},ce={class:"hover-box"},ue=["onClick"],de=["onClick"],pe=["onClick"],me={key:0,class:"btn perview"},ve=["onClick"],he={class:"control-icon"},ge={class:"item-bottom"},ye={class:"title"},je=["title"],fe=["title"],ke={class:"tag-wrapper"},we=["id"],be=["title"],_e={key:1,class:"list"},Ce={class:"pagination-box"},ze=D(e({__name:"Index",setup(e){const D=a(),ze=a(sessionStorage.getItem("XI_TONG_LOGO")||b),xe=()=>{D.value.init("add",null)},Ne=()=>{Re()},Te=a(!1),Ue=a({}),Se=a([]);s((()=>Ue.value),(()=>{Ee(1,12)}),{deep:!0});const Ie=a(!0);Ie.value=!0,x().then((e=>{Ie.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Ue.value[e.id]=[]})):Ue.value={},Se.value=e.data||[]):Se.value=[]})).catch((()=>{Ue.value={},Se.value=[],Ie.value=!1}));const Oe=a([]),Pe=t({total:0,current:1,pageSize:12,name:"",source:1,checkedType:-1}),Ee=(e,a)=>{Pe.current=e,Pe.pageSize=a,Re()},Re=()=>{var e;Te.value=!0;const a=(null==(e=Object.values(Ue.value))?void 0:e.flat(Infinity))||[],s={name:Pe.name,pageNo:Pe.current,pageSize:Pe.pageSize,tagId:a,status:0};1===Pe.source?N(s).then((e=>{Te.value=!1,200===e.code&&(Pe.total=e.data.totalRows,Oe.value=e.data.rows||[])})).catch((()=>{Te.value=!1})):2===Pe.source&&(-1!==Pe.checkedType?s.status=Pe.checkedType:s.status=null,T(s).then((e=>{Te.value=!1,200===e.code&&(Pe.total=e.data.totalRows,Oe.value=e.data.rows||[])})).catch((()=>{Te.value=!1})))},$e=async(e,a)=>{try{const s=`${window.config.previewUrl}${e.data}`,t=document.createElement("a");t.href=s,t.setAttribute("download",`${a.filename}.zip`),document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(s)}catch(s){}},qe=e=>`${window.config.previewUrl}${e.previewUrl}?width=400`,Ge=e=>{let a="";return 1===e?a="未提交":3===e?a="待审批":4===e&&(a="未通过"),a};return(e,a)=>{var s;const t=E,b=R,x=$,N=l("search-outlined"),T=q,Le=G,Ae=L,Je=A,Me=J,Ye=l("exception-outlined"),De=M,He=Y;return o(),i("div",H,[r("div",B,[r("div",K,[(null==(s=Se.value)?void 0:s.length)?(o(),i("div",F,[(o(!0),i(c,null,u(Se.value,(e=>{var a,s;return o(),i("div",{key:e.id,class:"tag-group"},[(null==(a=e.tags)?void 0:a.length)?(o(),i("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const s=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Ue.value[e.id])===JSON.stringify(s)?Ue.value[e.id]=[]:Ue.value[e.id]=e.tags.map((e=>e.id))})(e)},p(e.groupName),9,X)):d("",!0),(null==(s=e.tags)?void 0:s.length)?(o(),i("div",Z,[n(b,{value:Ue.value[e.id],"onUpdate:value":a=>Ue.value[e.id]=a,style:{width:"100%"}},{default:m((()=>[(o(!0),i(c,null,u(e.tags,(e=>(o(),i("div",{key:e.id,class:"tag-item-name"},[n(t,{value:e.id},{default:m((()=>[v(p(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):d("",!0)])})),128))])):(o(),i("div",Q,[Ie.value?(o(),h(x,{key:0,class:"loading-icon",spinning:Ie.value},null,8,["spinning"])):d("",!0),Ie.value?d("",!0):(o(),h(_,{key:1,title:"请绑定标签"}))]))]),r("div",V,[r("div",W,[r("div",ee,[n(T,{value:Pe.name,"onUpdate:value":a[1]||(a[1]=e=>Pe.name=e),valueModifiers:{trim:!0},class:"search-wrapper",placeholder:"搜索",onKeyup:a[2]||(a[2]=g((e=>Ee(1,12)),["enter"]))},{suffix:m((()=>[n(N,{style:{cursor:"pointer"},onClick:a[0]||(a[0]=e=>Ee(1,12))})])),_:1},8,["value"]),n(Ae,{value:Pe.source,"onUpdate:value":a[3]||(a[3]=e=>Pe.source=e),placeholder:"请选择数据源",class:"search-select",onChange:a[4]||(a[4]=e=>(Pe.checkedType=-1,void Ee(1,12)))},{default:m((()=>[n(Le,{value:1},{default:m((()=>a[9]||(a[9]=[v("公共资源")]))),_:1}),n(Le,{value:2},{default:m((()=>a[10]||(a[10]=[v("我的资源")]))),_:1})])),_:1},8,["value"]),2===Pe.source?(o(),h(Ae,{key:0,value:Pe.checkedType,"onUpdate:value":a[5]||(a[5]=e=>Pe.checkedType=e),placeholder:"请选择状态",class:"search-select",onChange:a[6]||(a[6]=e=>Ee(1,12))},{default:m((()=>[n(Le,{value:-1},{default:m((()=>a[11]||(a[11]=[v("全部")]))),_:1}),n(Le,{value:1},{default:m((()=>a[12]||(a[12]=[v("未提交")]))),_:1}),n(Le,{value:3},{default:m((()=>a[13]||(a[13]=[v("待审批")]))),_:1}),n(Le,{value:0},{default:m((()=>a[14]||(a[14]=[v("审批通过")]))),_:1}),n(Le,{value:4},{default:m((()=>a[15]||(a[15]=[v("审批不通过")]))),_:1})])),_:1},8,["value"])):d("",!0),n(Je,{type:"primary",class:"search-btn",style:{"margin-left":"20px"},onClick:a[7]||(a[7]=e=>Ee(1,12))},{default:m((()=>a[16]||(a[16]=[v(" 查询 ")]))),_:1})]),e.hasPerm("sys-chart:add")?(o(),h(Je,{key:0,type:"primary",class:"handle-btn",onClick:xe},{default:m((()=>a[17]||(a[17]=[v(" 新增图表 ")]))),_:1})):d("",!0)]),e.hasPerm("sys-chart:page")||e.hasPerm("sys-chart:myChartPage")?(o(),i("div",ae,[(o(!0),i(c,null,u(Oe.value,(s=>y((o(),i("div",{key:s.id,class:"item"},[r("div",se,[r("div",te,[r("div",le,[r("img",{src:qe(s),alt:"图片",class:"img",onError:a[8]||(a[8]=e=>(e.target.src=ze.value,e.target.style.width="auto"))},null,40,ie)]),r("div",oe,[r("div",re,[r("div",ne,"上传时间："+p(f(C)(s.createTime).format("YYYY-MM-DD HH:mm:ss")||""),1),2===Pe.source?(o(),i("div",{key:0,class:k(["status",{fail:4===s.status,check:3===s.status,unpush:1===s.status}])},p(Ge(s.status)),3)):d("",!0)])]),r("div",ce,[r("div",{class:"btn",onClick:e=>(e=>{const{href:a}=O.resolve({path:"/preview/chartPreview",query:{id:e.id,path:e.url,width:e.width,height:e.height}});window.open(a,"_blank")})(s)},"预览",8,ue),2!==Pe.source||1!==s.status&&4!==s.status||!e.hasPerm("sys-chart:edit")?d("",!0):(o(),i("div",{key:0,class:"btn perview",onClick:e=>(e=>{D.value.init("edit",e)})(s)},"编辑",8,de)),0===s.status&&e.hasPerm("sys-chart:download-chart")?(o(),i("div",{key:1,class:"btn perview",onClick:e=>(e=>{U({id:e.id}).then((a=>{$e(a,e)}))})(s)},"下载",8,pe)):d("",!0),n(Me,{placement:"topRight",title:"确认删除？",onConfirm:e=>(async e=>{const a=await I({id:e.id});200===a.code?(P("success","删除成功！"),Re()):P("error",a.message)})(s)},{default:m((()=>[2===Pe.source&&4===s.status?(o(),i("div",me,"删除")):d("",!0)])),_:2},1032,["onConfirm"]),2!==Pe.source||1!==s.status&&4!==s.status||!e.hasPerm("sys-chart:change-status")?d("",!0):(o(),i("div",{key:2,class:"btn perview",onClick:e=>(e=>{S({status:3,id:e.id}).then((e=>{200===e.code?(P("success","提交成功，请等待管理员审批"),Re()):P("error",e.message)}))})(s)},"提交审批",8,ve)),r("div",he,[n(De,{placement:"top"},{title:m((()=>[r("span",null,p(s.failureCause),1)])),default:m((()=>[4===s.status?(o(),h(Ye,{key:0,title:"未通过原因",style:{"margin-right":"5px"}})):d("",!0)])),_:2},1024)])])]),r("div",ge,[r("div",ye,[r("div",{class:"name",title:s.name},"["+p(s.width)+"*"+p(s.height)+"] "+p(s.name),9,je),r("div",{class:"user",title:s.createName},p(s.createName),9,fe)]),r("div",ke,[r("div",{id:s.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(o(!0),i(c,null,u(s.functionExampleTags,((e,a)=>(o(),i("div",{key:a,title:e.tagName,class:"tag-item",style:w({backgroundColor:e.color})},p(e.tagName),13,be)))),128))],8,we)])])])])),[[j,!Te.value&&Oe.value.length]]))),128)),Te.value?(o(),h(x,{key:0,class:"loading-icon",spinning:Te.value},null,8,["spinning"])):d("",!0),Te.value||Oe.value.length?d("",!0):(o(),h(_,{key:1}))])):(o(),i("div",_e,[n(_,{title:"暂无权限"})])),r("div",Ce,[n(He,{total:Pe.total,"page-size-options":["12","20","30","40"],current:Pe.current,"page-size":Pe.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:Ee},null,8,["total","current","page-size","show-total"])])])]),n(z,{ref_key:"AddExampleRef",ref:D,onOk:Ne},null,512)])}}}),[["__scopeId","data-v-049629b3"]]);export{ze as default};
