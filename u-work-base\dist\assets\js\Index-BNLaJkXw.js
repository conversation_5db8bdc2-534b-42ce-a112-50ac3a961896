import{a2 as s,b as o}from"./main-Djn9RDyT.js";import{d as r,r as i,f as t,b as e,o as m,e as p}from"./@vue-HScy-mz9.js";import{_ as a}from"./vue-qr-CB2aNKv5.js";import"./ant-design-vue-DYY9BtJq.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./js-binary-schema-parser-G48GG52R.js";const j={class:"exception-log"},n=["src"],l=a(r({__name:"Index",setup(r){const a=i("");t((()=>{l()}));const l=()=>{s(["LOG_CENTER"]).then((s=>{200===s.code?s.data.length>0&&(a.value=`${window.config.baseUrl}/${s.data[0]}`):o("error",s.message)})).catch((s=>{}))};return(s,o)=>(m(),e("div",j,[p("iframe",{width:"100%",height:"100%",frameborder:"0",scrolling:"auto",src:a.value},null,8,n)]))}}),[["__scopeId","data-v-d826f600"]]);export{l as default};
