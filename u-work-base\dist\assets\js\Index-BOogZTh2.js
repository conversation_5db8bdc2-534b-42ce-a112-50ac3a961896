import{d as s,r as e,w as a,a as t,f as o,b as i,o as r,e as l,ab as m,y as n,c as p,a9 as c,aa as d,J as u,ad as v,u as j,a5 as h,G as f}from"./@vue-HScy-mz9.js";import{_ as b}from"./lodash-Cz2B5noN.js";import{v as y,_}from"./vue-qr-CB2aNKv5.js";import{b as x}from"./vue-router-BEwRlUkF.js";import{u as T}from"./vue-clipboard3-B3WdiG0v.js";import{b as g}from"./main-Djn9RDyT.js";import{g as C,u as w}from"./systemAuthoriza-D4YElOeQ.js";import{_ as k}from"./UpLoadData.vue_vue_type_style_index_0_lang-CNypjWCw.js";import q from"./SelectVersion-BTdd9kaU.js";import z from"./CheckField-buAXy8Dj.js";import{u as D}from"./vue3-cookies-D4wQmYyh.js";import{T as S,i as E,B as U}from"./ant-design-vue-DYY9BtJq.js";import"./@babel-B4rXMRun.js";import"./js-binary-schema-parser-G48GG52R.js";import"./clipboard-Dv7Qpqbb.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./store2-5Qza9FG8.js";const $={class:"system-authoriza"},F={class:"wrap wrap-border"},P={class:"head"},A={class:"sq_wrap"},B={class:"col"},L={class:"val"},M={class:"col"},R={class:"val"},I={class:"col"},J={class:"val"},K={class:"col"},N={class:"val"},O={class:"col"},V={class:"val"},G={class:"col"},W={class:"val"},Y={class:"machine-code"},Z={class:"modal"},H=_(s({__name:"Index",props:{auth:{type:Boolean,default:!0}},setup(s){const{cookies:_}=D(),H=s,Q=e(!0);a((()=>H.auth),(s=>{Q.value=s}),{immediate:!0});const X=x(),ss=e(!1),es=b.debounce((()=>ss.value=!0),300),as=b.debounce((()=>ss.value=!1),300),ts=e(""),{toClipboard:os}=T(),is=t({createTime:"",totalTime:"",expiredTime:"",userCount:"",sceneCount:"",projectCount:"",teamCount:"",authStatus:"",qrText:"",tenant:"master",hidden:!0,downTime:0});o((()=>{rs()}));const rs=()=>{is.hidden=!1,ls()},ls=()=>{C().then((s=>{if(200===s.code&&!0===s.success){const{data:e}=s;is.createTime=e.licenseTime,is.totalTime=`${e.authUseDays}天`,e.authUseDay<0?is.expiredTime="无":is.expiredTime=ms(e.authEndDate),is.qrText=e.machineCode,is.teamCount=e.teamCount,is.projectCount=e.projectCount,is.authStatus=e.valid?"normal":e.authEndDate?"expire":"noPermiss"}}))},ms=s=>{const e=s;if(e&&8===e.length){return`${e.substr(0,4)}-${e.substr(4,2)}-${e.substr(6,2)}`}return""},ns=()=>{try{os(is.qrText),g("success","已复制机器码")}catch(s){g("error","复制机器码失败")}},ps=e(),cs=e(),ds=s=>{cs.value.init(s)},us=e();return(s,e)=>{const a=S,t=E,o=U;return r(),i("div",$,[l("div",F,[l("div",P,[e[7]||(e[7]=l("span",{class:"title"},"授权状态",-1)),"normal"===is.authStatus?(r(),c(a,{key:0,class:"tag normal"},{default:d((()=>e[4]||(e[4]=[u("已授权")]))),_:1})):"expire"===is.authStatus?(r(),c(a,{key:1,class:"tag expire"},{default:d((()=>e[5]||(e[5]=[u("授权过期")]))),_:1})):"noPermiss"===is.authStatus?(r(),c(a,{key:2,class:"tag noPermiss"},{default:d((()=>e[6]||(e[6]=[u("未授权")]))),_:1})):m("",!0)]),l("div",A,[l("div",B,[e[8]||(e[8]=l("div",{class:"lab"},"授权更新时间:",-1)),l("div",L,v(is.createTime),1)]),l("div",M,[e[9]||(e[9]=l("div",{class:"lab"},"授权时间:",-1)),l("div",R,v(is.totalTime),1)]),l("div",I,[e[10]||(e[10]=l("div",{class:"lab"},"授权到期时间:",-1)),l("div",J,v(is.expiredTime),1)]),l("div",K,[e[11]||(e[11]=l("div",{class:"lab"},"团队数:",-1)),l("div",N,v(is.teamCount),1)]),l("div",O,[e[12]||(e[12]=l("div",{class:"lab"},"项目数:",-1)),l("div",V,v(is.projectCount),1)]),l("div",G,[e[13]||(e[13]=l("div",{class:"lab"},"机器码:",-1)),l("div",W,[l("span",Y,v(is.qrText),1),l("a",{class:"abtn",onClick:ns},"复制"),l("a",{class:"abtn",onMouseenter:e[0]||(e[0]=s=>j(es)()),onMouseleave:e[1]||(e[1]=s=>j(as)())},"查看二维码",32)])])])]),is.hidden?m("",!0):(r(),i("div",{key:0,class:h(["wrap",{"wrap-border":Q.value}]),style:{"margin-top":"24px"}},[e[15]||(e[15]=l("div",{class:"head"},[l("span",{class:"title"},"授权许可证")],-1)),l("div",null,[p(t,{value:ts.value,"onUpdate:value":e[2]||(e[2]=s=>ts.value=s),placeholder:"请输入授权许可证","allow-clear":!0,rows:4,style:{width:"1230px","margin-bottom":"12px"}},null,8,["value"])]),p(o,{type:"primary",size:"small",class:"btn",disabled:""===ts.value,style:{"margin-bottom":"24px"},onClick:e[3]||(e[3]=s=>(()=>{const s=new FormData;s.set("licenseCode",ts.value),w(s).then((s=>{200===s.code?(ts.value="",g("success","更新授权成功"),_.get("ACCESS_TOKEN_U")?rs():X.push("/login")):g("error",s.message)}))})())},{default:d((()=>e[14]||(e[14]=[u("注册")]))),_:1},8,["disabled"])],2)),n(l("div",Z,[p(y,{class:"vue-qr modal-qr",text:is.qrText,"logo-scale":50,size:500},null,8,["text"])],512),[[f,ss.value]]),p(k,{ref_key:"upLoadDataRef",ref:ps,onOk:ds},null,512),p(q,{ref_key:"selectVersionRef",ref:us},null,512),p(z,{ref_key:"checkFieldRef",ref:cs},null,512)])}}}),[["__scopeId","data-v-d3cd3626"]]);export{H as default};
