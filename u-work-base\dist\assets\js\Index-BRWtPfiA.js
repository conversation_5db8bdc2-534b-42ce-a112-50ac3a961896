import{d as s,r as e,w as a,a as t,f as o,b as i,e as r,ab as l,y as m,a9 as n,aa as c,ad as p,u as d,a5 as u,c as v,G as j,J as h,o as b}from"./@vue-HScy-mz9.js";import{_ as f}from"./lodash-Cz2B5noN.js";import{v as y,_ as x}from"./vue-qr-CB2aNKv5.js";import{b as T}from"./vue-router-BEwRlUkF.js";import{u as g}from"./vue-clipboard3-B3WdiG0v.js";import{s as C,b as w}from"./main-Djn9RDyT.js";import{u as _}from"./vue3-cookies-D4wQmYyh.js";import{i as k,B as q,T as z}from"./ant-design-vue-DYY9BtJq.js";import"./@babel-B4rXMRun.js";import"./js-binary-schema-parser-G48GG52R.js";import"./clipboard-Dv7Qpqbb.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";const S="/licensex/license-info/upload-license",D="/licensex/license-info/get-license-info";const E={class:"system-authoriza"},$={class:"wrap wrap-border"},U={class:"head"},B={class:"sq_wrap"},M={class:"col"},P={class:"val"},A={class:"col"},F={class:"val"},I={class:"col"},J={class:"val"},K={class:"col"},N={class:"val"},G={class:"col"},L={class:"val"},O={class:"col"},W={class:"val"},Z={class:"machine-code"},H={class:"modal"},Q=x(s({__name:"Index",props:{auth:{type:Boolean,default:!0}},setup(s){const{cookies:x}=_(),Q=s,R=e(!0);a((()=>Q.auth),(s=>{R.value=s}),{immediate:!0});const V=T(),X=e(!1),Y=f.debounce((()=>X.value=!0),300),ss=f.debounce((()=>X.value=!1),300),es=e(""),{toClipboard:as}=g(),ts=t({createTime:"",totalTime:"",expiredTime:"",userCount:"",sceneCount:"",teamCount:"",projectCount:"",authStatus:"",qrText:"",tenant:"master",hidden:!0,downTime:0});o((()=>{os()}));const os=()=>{ts.hidden=!1,is()},is=()=>{C({url:D,method:"get"}).then((s=>{if(200===s.code&&!0===s.success){const{data:e}=s;ts.createTime=e.licenseTime,ts.totalTime=`${e.authUseDays}天`,e.authUseDay<0?ts.expiredTime="无":ts.expiredTime=rs(e.authEndDate),ts.qrText=e.machineCode,ts.teamCount=e.teamCount,ts.projectCount=e.projectCount,ts.authStatus=e.valid?"normal":e.authEndDate?"expire":"noPermiss"}}))},rs=s=>{const e=s;if(e&&8===e.length){return`${e.substr(0,4)}-${e.substr(4,2)}-${e.substr(6,2)}`}return""},ls=()=>{try{as(ts.qrText),w("success","已复制机器码")}catch(s){w("error","复制机器码失败")}},ms=()=>{const s=new FormData;var e;s.set("licenseCode",es.value),(e=s,C({url:S,method:"post",data:e})).then((s=>{200===s.code?(es.value="",w("success","更新授权成功"),x.get("ACCESS_TOKEN_U")?os():V.push("/login")):w("error",s.message)}))};return(s,e)=>{const a=z,t=k,o=q;return b(),i("div",E,[r("div",$,[r("div",U,[e[7]||(e[7]=r("span",{class:"title"},"授权状态",-1)),"normal"===ts.authStatus?(b(),n(a,{key:0,class:"tag normal"},{default:c((()=>e[4]||(e[4]=[h("已授权")]))),_:1})):"expire"===ts.authStatus?(b(),n(a,{key:1,class:"tag expire"},{default:c((()=>e[5]||(e[5]=[h("授权过期")]))),_:1})):"noPermiss"===ts.authStatus?(b(),n(a,{key:2,class:"tag noPermiss"},{default:c((()=>e[6]||(e[6]=[h("未授权")]))),_:1})):l("",!0)]),r("div",B,[r("div",M,[e[8]||(e[8]=r("div",{class:"lab"},"授权更新时间:",-1)),r("div",P,p(ts.createTime),1)]),r("div",A,[e[9]||(e[9]=r("div",{class:"lab"},"授权时间:",-1)),r("div",F,p(ts.totalTime),1)]),r("div",I,[e[10]||(e[10]=r("div",{class:"lab"},"授权到期时间:",-1)),r("div",J,p(ts.expiredTime),1)]),r("div",K,[e[11]||(e[11]=r("div",{class:"lab"},"团队数:",-1)),r("div",N,p(ts.teamCount||0),1)]),r("div",G,[e[12]||(e[12]=r("div",{class:"lab"},"项目数:",-1)),r("div",L,p(ts.projectCount||0),1)]),r("div",O,[e[13]||(e[13]=r("div",{class:"lab"},"机器码:",-1)),r("div",W,[r("span",Z,p(ts.qrText),1),r("a",{class:"abtn",onClick:ls},"复制"),r("a",{class:"abtn",onMouseenter:e[0]||(e[0]=s=>d(Y)()),onMouseleave:e[1]||(e[1]=s=>d(ss)())},"查看二维码",32)])])])]),ts.hidden?l("",!0):(b(),i("div",{key:0,class:u(["wrap",{"wrap-border":R.value}]),style:{"margin-top":"24px"}},[e[15]||(e[15]=r("div",{class:"head"},[r("span",{class:"title"},"授权许可证")],-1)),r("div",null,[v(t,{value:es.value,"onUpdate:value":e[2]||(e[2]=s=>es.value=s),placeholder:"请输入授权许可证","allow-clear":!0,rows:4,style:{width:"1230px","margin-bottom":"12px"}},null,8,["value"])]),v(o,{type:"primary",size:"small",class:"btn",disabled:""===es.value,style:{"margin-bottom":"24px"},onClick:e[3]||(e[3]=s=>ms())},{default:c((()=>e[14]||(e[14]=[h("注册")]))),_:1},8,["disabled"])],2)),m(r("div",H,[v(y,{class:"vue-qr modal-qr",text:ts.qrText,"logo-scale":50,size:500},null,8,["text"])],512),[[j,X.value]])])}}}),[["__scopeId","data-v-eed834ea"]]);export{Q as default};
