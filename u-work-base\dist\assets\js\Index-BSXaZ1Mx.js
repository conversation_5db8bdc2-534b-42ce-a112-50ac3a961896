import{d as e,r as a,w as t,am as s,b as l,u as i,o,e as r,c as n,F as d,ag as c,ab as p,ad as u,aa as m,J as v,a9 as y,ae as k,a4 as j,E as g,a7 as f}from"./@vue-HScy-mz9.js";import{c as h}from"./clipboard-Dv7Qpqbb.js";import w from"./Nodata-mmdoiDH6.js";import x from"./AddProject-SCEWZ0UE.js";import C from"./PublishProject-CTg0PqB0.js";import _ from"./AddAndEditForm-bV5fEuZ9.js";import{s as b,a as I,u as P,j as T,k as z,l as E,D as S,m as N,b as O}from"./main-Djn9RDyT.js";import{u as L}from"./useTableScrollY-DAiBD3Av.js";import R from"./EditProjectDeveloper-N-llUUFu.js";import{g as $,d as A}from"./operationAnalysis-D3RTU-GI.js";import U from"./DownLoadPaksList-Yx_9AsML.js";import{u as D}from"./vue3-cookies-D4wQmYyh.js";import{q as J,r as F,S as M,I as W,B,s as Y,k as q,d as G,D as K,u as Q,v as X,e as V,f as Z,g as H}from"./ant-design-vue-DYY9BtJq.js";import{_ as ee}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./no-data-DShY7eqz.js";import"./axios-7z2hFSF6.js";import"./dayjs-CA7qlNSr.js";import"./projectGallery-xT8wgNPG.js";import"./vue-router-BEwRlUkF.js";import"./Map.vue_vue_type_style_index_0_lang-CZrcyiOo.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./UserList-B68ng9ko.js";import"./js-binary-schema-parser-G48GG52R.js";const ae="/edtap/sys-project/user-project-list";const te={key:0,class:"project-manage"},se={class:"tag-search"},le={key:0,class:"tag-content"},ie=["onClick"],oe={key:1,class:"tag-item"},re={key:1,class:"no-tag-content"},ne={class:"content-wrap"},de={class:"search-wrap"},ce={class:"search-content"},pe={class:"search-item"},ue={class:"search-btns"},me={class:"table-handle"},ve={class:"table-wrap"},ye=["src"],ke={class:"name"},je={class:"name-text"},ge=["onClick"],fe={key:1,class:"name"},he={class:"name-text"},we=["onClick"],xe={key:1,class:"tag-wrapper"},Ce={class:"tag-list"},_e={key:0,class:"edit-developer"},be={class:"all-content"},Ie={class:"text-truncate"},Pe={key:1},Te={key:3,class:"campus-used-wrap",style:{width:"100%"}},ze={key:4,class:"campus-used-wrap",style:{width:"100%"}},Ee=["title"],Se={key:7,class:"campus-used-wrap"},Ne={key:8,class:"table-actions"},Oe=["onClick"],Le=["onClick"],Re=["onClick"],$e=["onClick"],Ae={key:0},Ue=["onClick"],De={class:"ant-dropdown-link"},Je=["onClick"],Fe=["onClick"],Me={class:"pagination"},We={key:1,class:"project-manage"},Be={class:"no-use"},Ye=ee(e({__name:"Index",setup(e){const{cookies:ee}=D(),Ye=I(),qe=P(),Ge=a([]);(async()=>{try{const e=await E({code:"PROJECT_TYPE"});200===e.code&&(Ge.value=e.data)}catch(e){}})();const Ke=e=>{const a=Ge.value.find((a=>a.code===e));return a?a.value:"XXV项目"},Qe=a(),Xe=a(),Ve=()=>{ga()},Ze=e=>{var a;(a={id:e.id},b({url:"/edtap/sys-project/delete",method:"post",data:a})).then((e=>{200===e.code?(O("success","项目删除成功"),ga()):O("error",e.message)}))},He=a(),ea=e=>e.userList&&e.userList.length?e.userList.map((e=>e.name)).join(","):"";a("");const aa=a(""),ta=()=>{aa.value=""},sa=a({}),la=a();a(!0);const ia=e=>{var a;A({id:e.id,installManageId:null==(a=sa.value)?void 0:a.id,downloadOption:1}).then((e=>{200===e.code&&O("success",e.message)}))},oa=a({}),ra=a([]),na=a(!0);na.value=!0,ra.value=[],b({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"6"}}).then((e=>{na.value=!1,200===e.code?(e.data.length&&e.data.forEach((e=>{oa.value[e.id]=[]})),ra.value=e.data||[]):ra.value=[]})).catch((()=>{na.value=!1}));t((()=>oa.value),(()=>{ca()}),{deep:!0});const da=a({name:null,enterpriseId:null}),ca=()=>{var e;1!==(null==(e=Ye.checkedEnterprise)?void 0:e.status)&&(ka.value.current=1,ka.value.pageSize=10,ga())},pa=[{title:"项目名称",dataIndex:"name",ellipsis:!0,width:300},{title:"标签",dataIndex:"tags",width:180,minWidth:100},{title:"参与团队成员",dataIndex:"developer",width:180,minWidth:120},{title:"场景数",dataIndex:"sceneCount",width:140,minWidth:80},{title:"用户数",dataIndex:"userCount",width:140,minWidth:80},{title:"创建日期",dataIndex:"createTime",width:120,ellipsis:!0},{title:"是否涉密",dataIndex:"classified",width:80,ellipsis:!0},{title:"操作",dataIndex:"actions",width:300}],ua=a(),{scrollY:ma}=L(ua),va=a(!1),ya=a([]),ka=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),ja=(e,a)=>{ka.value=Object.assign(ka.value,{current:e,pageSize:a}),ga()};t((()=>Ye.checkedEnterprise),(()=>{ca()}));const ga=()=>{var e,a;if(va.value)return;ya.value=[],va.value=!0;const t=(null==(e=Object.values(oa.value))?void 0:e.flat(Infinity))||[];if(!(null==(a=Ye.checkedEnterprise)?void 0:a.id))return ya.value=[],void(va.value=!1);var s;(s={...da.value,userId:qe.userInfo.id,pageNo:ka.value.current,pageSize:ka.value.pageSize,enterpriseId:Ye.checkedEnterprise.id,tagIds:t},b({url:ae,method:"post",data:s})).then((e=>{if(va.value=!1,200===e.code)if("[]"===JSON.stringify(e.data))ya.value=[];else{const{rows:a,pageNo:t,pageSize:s,totalRows:l}=e.data;ya.value=a,ka.value.current=t,ka.value.pageSize=s,ka.value.total=l}})).catch((()=>{va.value=!1}))},fa=a(),ha=(e,a)=>{fa.value.init(e,a)},wa=async e=>{try{await h.copy(e),O("success","项目编码已复制到剪贴板！")}catch(a){}};return(e,a)=>{var t,h,b;const I=J,P=F,E=M,O=W,L=B,A=s("CopyOutlined"),D=Y,ae=s("FormOutlined"),qe=q,aa=G,sa=s("DownOutlined"),xa=V,Ca=X,_a=Q,ba=K,Ia=Z,Pa=H;return 1!==(null==(t=i(Ye).checkedEnterprise)?void 0:t.status)?(o(),l("div",te,[r("div",se,[(null==(h=ra.value)?void 0:h.length)?(o(),l("div",le,[(o(!0),l(d,null,c(ra.value,(e=>{var a,t;return o(),l("div",{key:e.id,class:"tag-group"},[(null==(a=null==e?void 0:e.tags)?void 0:a.length)?(o(),l("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const t=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(oa.value[e.id])===JSON.stringify(t)?oa.value[e.id]=[]:oa.value[e.id]=e.tags.map((e=>e.id))})(e)},u(e.groupName),9,ie)):p("",!0),(null==(t=null==e?void 0:e.tags)?void 0:t.length)?(o(),l("div",oe,[n(P,{value:oa.value[e.id],"onUpdate:value":a=>oa.value[e.id]=a,style:{width:"100%"}},{default:m((()=>[(o(!0),l(d,null,c(e.tags,(e=>(o(),l("div",{key:e.id,class:"tag-item-name"},[n(I,{value:e.id},{default:m((()=>[v(u(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):p("",!0)])})),128))])):(o(),l("div",re,[na.value?(o(),y(E,{key:0,class:"loading-icon",spinning:na.value},null,8,["spinning"])):p("",!0),na.value?p("",!0):(o(),y(w,{key:1,title:"请绑定标签"}))]))]),r("div",ne,[r("div",de,[r("div",ce,[r("div",pe,[a[4]||(a[4]=r("span",{class:"search-label"},"项目名称",-1)),n(O,{value:da.value.name,"onUpdate:value":a[0]||(a[0]=e=>da.value.name=e),valueModifiers:{trim:!0},class:"search-input",placeholder:"请输入名称查询","allow-clear":!0,onKeyup:a[1]||(a[1]=k((e=>ga()),["enter"]))},null,8,["value"])]),r("div",ue,[n(L,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>ca())},{default:m((()=>a[5]||(a[5]=[v(" 查询 ")]))),_:1})])]),r("div",me,[e.hasPerm("sys-project:add")?(o(),y(L,{key:0,type:"primary",class:"handle-btn",onClick:a[3]||(a[3]=e=>ha("add",null))},{default:m((()=>a[6]||(a[6]=[v(" 新建项目 ")]))),_:1})):p("",!0)])]),r("div",ve,[r("div",{ref_key:"table",ref:ua,class:"table-content"},[e.hasPerm("sys-project:user-project-list")?(o(),y(Ia,{key:0,class:"table",scroll:{y:i(ma)},pagination:!1,size:"small",loading:va.value,"row-key":e=>e.id,columns:pa,"data-source":ya.value,onChange:ja},{bodyCell:m((({column:t,record:s})=>["name"===t.dataIndex?(o(),l(d,{key:0},[s.previewUrl?(o(),y(D,{key:0},{content:m((()=>{return[r("img",{class:"pre-img",src:(e=s,e.previewUrl?`${window.config.previewUrl}${e.previewUrl}`:""),alt:""},null,8,ye)];var e})),default:m((()=>[r("div",ke,[r("span",je,u(s.name),1),r("a",{class:"copy-btn",title:"复制项目编码",onClick:e=>wa(s.code)},[n(A)],8,ge)])])),_:2},1024)):(o(),l("div",fe,[r("span",he,u(s.name),1),r("a",{class:"copy-btn",title:"复制项目编码",onClick:e=>wa(s.code)},[n(A)],8,we)]))],64)):p("",!0),"tags"===t.dataIndex?(o(),l("div",xe,[r("div",Ce,[(o(!0),l(d,null,c(s.functionExampleTags,((e,a)=>(o(),l("div",{key:a,class:"tag-item",style:j({backgroundColor:e.color})},u(e.tagName),5)))),128))])])):p("",!0),"developer"===t.dataIndex?(o(),l(d,{key:2},["1"===s.exampleType?(o(),l("div",_e,[n(D,{title:"",trigger:"hover",placement:"topLeft"},{content:m((()=>[r("div",be,u(ea(s)),1)])),default:m((()=>[r("div",Ie,u(ea(s)),1)])),_:2},1024),e.hasPerm("sys-project:add-edit-user")?(o(),y(ae,{key:0,style:{"margin-top":"5px","margin-left":"5px"},onClick:g((e=>(e=>{He.value.init(e)})(s)),["stop"])},null,8,["onClick"])):p("",!0)])):(o(),l("div",Pe,"-"))],64)):p("",!0),"sceneCount"===t.dataIndex?(o(),l("div",Te,[n(qe,{"stroke-color":i(z)(100*(s.scene||0)/s.sceneCount),showInfo:!1,percent:Number((100*(s.scene||0)/s.sceneCount).toFixed(2)),size:"small"},null,8,["stroke-color","percent"]),v(" "+u(`${s.scene||0}/${s.sceneCount}`),1)])):p("",!0),"userCount"===t.dataIndex?(o(),l("div",ze,[n(qe,{"stroke-color":i(z)(100*(s.user||0)/s.userCount),showInfo:!1,percent:Number((100*(s.user||0)/s.userCount).toFixed(2)),size:"small"},null,8,["stroke-color","percent"]),v(" "+u(`${s.user||0}/${s.userCount}`),1)])):p("",!0),"exampleType"===t.dataIndex?(o(),l(d,{key:5},[v(u(Ke(s.exampleType)),1)],64)):p("",!0),"createTime"===t.dataIndex?(o(),l("div",{key:6,class:"campus-used-wrap overflow-ellipse",title:i(T)(s.createTime)},u(i(T)(s.createTime)),9,Ee)):p("",!0),"classified"===t.dataIndex?(o(),l("div",Se,u(1===s.classified?"是":"否"),1)):p("",!0),"actions"===t.dataIndex?(o(),l("div",Ne,[e.hasPerm("sys-project:edit")?(o(),l("a",{key:0,type:"text",onClick:e=>ha("edit",s)},"编辑",8,Oe)):p("",!0),e.hasPerm("sys-project:copy")&&"1"===s.exampleType?(o(),l("a",{key:1,type:"text",onClick:e=>ha("copy",s)},"复制",8,Le)):p("",!0),n(D,{style:{width:"500px"},title:"下载选项",trigger:"hover"},{content:m((()=>[r("div",null,[n(aa,{title:"仅下载项目数据"},{default:m((()=>[e.hasPerm("sys-project:download-project")?(o(),l("a",{key:0,onClick:e=>(e=>{ia(e),ta()})(s)},"仅下载数据",8,Re)):p("",!0)])),_:2},1024),a[7]||(a[7]=v(" | ")),n(aa,{title:"下载项目文件和安装包地址"},{default:m((()=>[e.hasPerm("sys-install-manage:page")?(o(),l("a",{key:0,onClick:e=>(e=>{la.value.init(e.id),ta()})(s)},"仅下载文件和安装包",8,$e)):p("",!0)])),_:2},1024)])])),default:m((()=>["1"===s.exampleType?(o(),l("a",Ae,"下载")):p("",!0)])),_:2},1024),e.hasPerm("sys-project:project-system")?(o(),l("a",{key:2,type:"text",onClick:e=>(async e=>{const a=e.exampleType;let t="";if("1"===a){let a="";const l=ee.get("ACCESS_P"),i=l?S(l.substring(32),"",l.substring(0,32)):"";if(i)try{const t=await N({tenant:e.code,pwd:i.replaceAll('"',"")});200===t.code&&(a=t.data)}catch(s){}t=`/base/login?tenant=${e.code}&token=${a}&name=${e.name}`}else if("2"===a){const a=await $({projectId:e.id});200===a.code&&(t=a.data)}window.open(t,"_blank")})(s)},"进入项目",8,Ue)):p("",!0),n(ba,null,{overlay:m((()=>[n(_a,null,{default:m((()=>[n(Ca,null,{default:m((()=>[e.hasPerm("sys-project:delete")?(o(),y(xa,{key:0,placement:"topRight",title:"确认删除？",onConfirm:()=>Ze(s)},{default:m((()=>a[9]||(a[9]=[r("a",null,"删除",-1)]))),_:2},1032,["onConfirm"])):p("",!0)])),_:2},1024),e.hasPerm("sys-project:edit")&&"1"===s.exampleType?(o(),y(Ca,{key:0},{default:m((()=>[!s.exampleId&&e.hasPerm("sys-project:edit")?(o(),l("a",{key:0,type:"text",onClick:e=>{return a=s,void Xe.value.init(a);var a}},"发布案例",8,Je)):p("",!0),s.exampleId?(o(),l("a",{key:1,type:"text",onClick:e=>{return a=s,void Qe.value.init(a);var a}},"编辑案例",8,Fe)):p("",!0)])),_:2},1024)):p("",!0)])),_:2},1024)])),default:m((()=>[r("a",De,[a[8]||(a[8]=v(" 更多 ")),n(sa)])])),_:2},1024)])):p("",!0)])),expandedRowRender:m((({record:e})=>[r("div",null,"创建者："+u(e.createName),1),r("div",null,"项目编码："+u(e.code),1),r("div",null,"授权到期："+u(i(T)(e.licenseExpiredTime)),1),r("div",null,"项目所属地："+u(e.formattedAddress||"-"),1)])),_:1},8,["scroll","loading","row-key","data-source"])):p("",!0),r("div",Me,[ya.value.length>0?(o(),y(Pa,f({key:0},ka.value,{onChange:ja}),null,16)):p("",!0)])],512)])]),n(R,{ref_key:"editProjectDeveloperRef",ref:He,onOk:ca},null,512),n(_,{ref_key:"addEditFormRef",ref:fa,onOk:ca,projectType:Ge.value},null,8,["projectType"]),n(U,{ref_key:"downLoadPaksListRef",ref:la},null,512),n(x,{ref_key:"addProjectRef",ref:Xe,onOk:Ve},null,512),n(C,{ref_key:"editProjectRef",ref:Qe,onOk:Ve},null,512)])):(o(),l("div",We,[r("div",Be,"您当前团队【"+u(null==(b=i(Ye).checkedEnterprise)?void 0:b.name)+"】已被禁用，请点击左下角【我的团队】进行切换，并设为默认！",1)]))}}}),[["__scopeId","data-v-4b310a20"]]);export{Ye as default};
