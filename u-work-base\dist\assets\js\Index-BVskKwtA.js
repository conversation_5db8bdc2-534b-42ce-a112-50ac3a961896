import{d as e,r as t,f as a,b as s,e as i,c as r,a9 as o,ab as l,ae as n,aa as u,u as d,a7 as m,J as p,F as c,a5 as v,ad as j,o as f}from"./@vue-HScy-mz9.js";import{u as g,b as y}from"./vue-router-BEwRlUkF.js";import h from"./Add-XZCKK-Ph.js";import{q as k,b,S as x}from"./main-Djn9RDyT.js";import{u as I}from"./userManage-DLgtGpjc.js";import{u as S}from"./useTableScrollY-DAiBD3Av.js";import w from"./Reason-aYyHfuGg.js";import{I as z,f as _,B as T,g as C,d as L,e as R}from"./ant-design-vue-DYY9BtJq.js";import{S as N}from"./@ant-design-CA72ad83.js";import{_ as q}from"./vue-qr-CB2aNKv5.js";import"./UserList-Bo1tWySN.js";import"./Depart-B4y-bHgG.js";import"./department-CTxHSeTj.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const O={class:"developer-list"},P={class:"search-wrap"},U={class:"table-wrap"},$={class:"action"},E={key:1,class:"table-actions"},J={key:2,class:"table-actions"},M={key:3,class:"table-actions"},A={key:4,class:"table-actions"},B={key:0,type:"text"},D={key:1,style:{color:"red"},type:"text"},F={key:0,type:"text"},K={class:"pagination"},Y=q(e({__name:"Index",emits:["ok"],setup(e,{emit:q}){y();const Y=q,{params:H,query:Q}=g(),{id:X}=H;t(Q.name);const Z=()=>{G(),Y("ok")},G=()=>{se.value.current=1,se.value.pageSize=10,de()},V=t(),W=(e,t)=>{var a;let s="";return s=e?e.split(" ")[0]||"":(null==(a=null==t?void 0:t.createTime)?void 0:a.split(" ")[0])||"",s},ee=t(),te=()=>{k({...le.value,enterpriseId:X,pageNo:1,pageSize:9999}).then((e=>{if(200===e.code){const{rows:t}=e.data;ee.value.init("add",t||[])}else ee.value.init("add",[])})).catch((()=>{ee.value.init("add",[])}))},ae=[{title:"账号",dataIndex:"account",ellipsis:!0},{title:"姓名",dataIndex:"name"},{title:"手机号",dataIndex:"phone"},{title:"邮箱",dataIndex:"email"},{title:"状态",dataIndex:"status",width:80},{title:"是否为管理员",dataIndex:"manager",width:120},{title:"最后登录时间",dataIndex:"lastLoginTime",ellipsis:!0,width:150},{title:"操作",dataIndex:"actions",width:260}],se=t({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),ie=t(),{scrollY:re}=S(ie);a((()=>{de()}));const oe=(e,t)=>{se.value=Object.assign(se.value,{current:e,pageSize:t}),de()},le=t({name:null}),ne=t(!1),ue=t([]),de=()=>{k({...le.value,enterpriseId:X,pageNo:se.value.current,pageSize:se.value.pageSize}).then((e=>{if(200===e.code){const{rows:t,pageNo:a,pageSize:s,totalRows:i}=e.data;ue.value=t||[],se.value.current=a,se.value.pageSize=s,se.value.total=i}}))},me=e=>{const{ueStatus:t,status:a}=e;let s="";return 0===a?s=1===t?"冻结":"正常":1===a?s="停用":2===a?s="删除":3===a?s="待审批":4===a&&(s="未通过"),s},pe=e=>{let t="";return t=3===e?"管理员":"非管理员",t};return(e,t)=>{var a;const g=z,y=T,k=L,S=R,q=_,Y=C;return f(),s("div",O,[i("div",P,[r(g,{value:le.value.name,"onUpdate:value":t[1]||(t[1]=e=>le.value.name=e),valueModifiers:{trim:!0},class:"filter",placeholder:"请输入名称查询","allow-clear":!0,onKeyup:t[2]||(t[2]=n((e=>de()),["enter"]))},{suffix:u((()=>[r(d(N),{onClick:t[0]||(t[0]=e=>de())})])),_:1},8,["value"]),e.hasPerm("sys-user:queryEnterPriseUserNew")?(f(),o(y,{key:0,type:"primary",class:"handle-btn",onClick:te},{default:u((()=>t[4]||(t[4]=[p(" 新增团队成员 ")]))),_:1})):l("",!0)]),i("div",U,[i("div",{ref_key:"table",ref:ie,class:"table-content"},[r(q,{class:"table",scroll:{y:d(re)},pagination:!1,size:"small",loading:ne.value,"row-key":e=>e.id,columns:ae,"data-source":ue.value,onChange:oe},{headerCell:u((({column:e})=>[i("div",$,j(e.title),1)])),bodyCell:u((({column:t,record:a})=>["status"===t.dataIndex?(f(),s(c,{key:0},[4===a.status?(f(),o(k,{key:0,title:a.failureCause,placement:"right"},{default:u((()=>[i("div",{class:v(["table-actions",{normal:0===a.status&&1!==a.ueStatus,freeze:0===a.status&&1===a.ueStatus,uncheck:3===a.status,nopass:4===a.status}])},j(me(a)),3)])),_:2},1032,["title"])):(f(),s("div",{key:1,class:v(["table-actions",{normal:0===a.status&&1!==a.ueStatus,freeze:0===a.status&&1===a.ueStatus,uncheck:3===a.status,nopass:4===a.status}])},j(me(a)),3))],64)):l("",!0),"manager"===t.dataIndex?(f(),s("div",E,j(pe(a.ueStatus)),1)):l("",!0),"createTime"===t.dataIndex?(f(),s("div",J,j(W(a.createTime,a)),1)):l("",!0),"lastLoginTime"===t.dataIndex?(f(),s("div",M,j(W(null==a?void 0:a.lastLoginTime,a)),1)):l("",!0),"actions"===t.dataIndex?(f(),s("div",A,[e.hasPerm("sys-user-enterprise:binding-conservator")?(f(),o(S,{key:0,placement:"topRight",title:3===a.ueStatus?"确认取消管理员？":"确认设为管理员？",okText:"确认",cancelText:"取消",onConfirm:e=>((e,t)=>{x({userId:e.id,ueStatus:t,enterpriseId:X}).then((e=>{200===e.code?(b("success","设置成功！"),de()):b("error",e.message)}))})(a,3===a.ueStatus?0:3)},{default:u((()=>[3!==a.ueStatus?(f(),s("a",B,"设为管理员")):l("",!0),3===a.ueStatus?(f(),s("a",D,"取消管理员")):l("",!0)])),_:2},1032,["title","onConfirm"])):l("",!0),r(S,{placement:"topRight",title:"确定移除该用户？",okText:"确认",cancelText:"取消",onConfirm:e=>(e=>{I({enterpriseId:X,grantUserIdList:[e.id]}).then((e=>{e.success?(b("success","团队成员移除成功"),de()):b("error",`移除失败：${e.message}`)})).catch((e=>{b("error",`移除失败：${e.message}`)}))})(a)},{default:u((()=>[e.hasPerm("enterprise:unbind-user")?(f(),s("a",F,"移除")):l("",!0)])),_:2},1032,["onConfirm"])])):l("",!0)])),_:1},8,["scroll","loading","row-key","data-source"]),i("div",K,[(null==(a=ue.value)?void 0:a.length)>0?(f(),o(Y,m({key:0},se.value,{onChange:oe}),null,16)):l("",!0)])],512)]),r(w,{ref_key:"checkRef",ref:V,onOk:t[3]||(t[3]=e=>de())},null,512),r(h,{id:d(X),ref_key:"addRef",ref:ee,onOk:Z},null,8,["id"])])}}}),[["__scopeId","data-v-89dbca04"]]);export{Y as default};
