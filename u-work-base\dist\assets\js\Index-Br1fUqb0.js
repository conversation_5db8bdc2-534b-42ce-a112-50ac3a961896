import{d as e,r as a,w as s,am as t,b as l,o as i,e as o,c as r,F as n,ag as u,ab as c,ad as d,aa as m,J as p,a9 as v,ae as g,y as h,G as y,u as j,a5 as f,a4 as k}from"./@vue-HScy-mz9.js";import{d as w}from"./dayjs-CA7qlNSr.js";import b from"./Nodata-mmdoiDH6.js";import{p as C}from"./projectGallery-DFHuwUAq.js";import{l as _,d as z,g as T,b as x,c as N}from"./layout-Zvdppt1-.js";import S from"./Reason-Bf59dFGF.js";import O from"./AddExample-DV-BvTHz.js";import{C as P,b as R}from"./main-Djn9RDyT.js";import{q as U,r as $,S as I,I as E,x as q,w as W,B as J,e as L,d as A,g as G}from"./ant-design-vue-DYY9BtJq.js";import{_ as M}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./layout-template-DJui4TvV.js";import"./@vueup-CLVdhRgW.js";import"./quill-BBEhJLA6.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";const Y={class:"func-module"},D={class:"tag-search"},H={key:0,class:"tag-content"},B=["onClick"],K={key:1,class:"tag-item"},F={key:1,class:"no-tag-content"},Q={class:"page-wrap"},X={class:"search-wrap"},Z={class:"search-content"},V={class:"search-item"},ee={class:"search-item"},ae={class:"search-btns"},se={class:"table-handle"},te={class:"content-list"},le={key:0,class:"list"},ie={class:"contain"},oe={class:"img-box"},re={class:"img-item"},ne=["src"],ue={class:"bottom-wrapper"},ce={class:"bottom-content"},de={class:"time"},me={class:"hover-box"},pe=["onClick"],ve=["onClick"],ge=["onClick"],he={key:0,class:"btn"},ye={key:1,class:"btn"},je={key:0,class:"btn"},fe={key:1,class:"btn"},ke={class:"control-icon"},we={class:"item-bottom"},be={class:"title"},Ce=["title"],_e=["title"],ze={class:"tag-wrapper"},Te=["id"],xe=["title"],Ne={key:1,class:"list"},Se={class:"pagination-box"},Oe=M(e({__name:"Index",setup(e){const M=a(sessionStorage.getItem("XI_TONG_LOGO")||C),Oe=a(),Pe=a({}),Re=a(-1),Ue=e=>{Re.value=e,Ke()},$e=a(),Ie=(e,a)=>{if(e){const e={status:0,id:a.id};x(e).then((e=>{200===e.code?(R("success","开发模板审批完成"),He()):R("error",e.message)}))}else $e.value.init(a)},Ee=e=>{let a="";return 0===e?a="正常":1===e?a="已下架":2===e?a="已删除":3===e?a="待审批":4===e&&(a="未通过"),a},qe=a([]);s((()=>Pe.value),(()=>{Ke()}),{deep:!0});const We=a(!0);We.value=!0,_().then((e=>{We.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Pe.value[e.id]=[]})):Pe.value={},qe.value=e.data||[]):qe.value=[]})).catch((()=>{Pe.value={},qe.value=[],We.value=!1}));const Je=a({total:0,current:1,pageSize:12,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),Le=(e,a)=>{Je.value=Object.assign(Je.value,{current:e,pageSize:a}),He()},Ae=e=>{let a="删除";return 0===e&&(a="下架"),a},Ge=a({keyWord:""}),Me=()=>{He()},Ye=a(!0),De=a([]),He=async()=>{var e;De.value=[],Ye.value=!0;const a=(null==(e=Object.values(Pe.value))?void 0:e.flat(Infinity))||[],s={name:Ge.value.keyWord,pageNo:Je.value.current,pageSize:Je.value.pageSize,tagId:a},t=Re.value;t>-1&&(s.status=t);const l=await T(s);if(Ye.value=!1,200===l.code){const{rows:e,pageNo:a,totalRows:s}=l.data;Je.value.total=s,Je.value.current=a,De.value=e}},Be=async(e,a)=>{try{const s=`${window.config.previewUrl}${e.data}`,t=document.createElement("a");t.href=s,t.setAttribute("download",`${a.filename}.zip`),document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(s)}catch(s){}},Ke=(e="")=>{Je.value.current=1,Je.value.pageSize=12,He()},Fe=(e,a)=>{Oe.value.init(e,a)},Qe=e=>`${window.config.previewUrl}${e.previewUrl}?width=400`;return(e,a)=>{var s;const C=U,_=$,T=I,Xe=E,Ze=q,Ve=W,ea=J,aa=L,sa=t("exception-outlined"),ta=A,la=G;return i(),l("div",Y,[o("div",D,[(null==(s=qe.value)?void 0:s.length)?(i(),l("div",H,[(i(!0),l(n,null,u(qe.value,(e=>{var a,s;return i(),l("div",{key:e.id,class:"tag-group"},[(null==(a=null==e?void 0:e.tags)?void 0:a.length)?(i(),l("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const s=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Pe.value[e.id])===JSON.stringify(s)?Pe.value[e.id]=[]:Pe.value[e.id]=e.tags.map((e=>e.id))})(e)},d(e.groupName),9,B)):c("",!0),(null==(s=null==e?void 0:e.tags)?void 0:s.length)?(i(),l("div",K,[r(_,{value:Pe.value[e.id],"onUpdate:value":a=>Pe.value[e.id]=a,style:{width:"100%"}},{default:m((()=>[(i(!0),l(n,null,u(e.tags,(e=>(i(),l("div",{key:e.id,class:"tag-item-name"},[r(C,{value:e.id},{default:m((()=>[p(d(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):c("",!0)])})),128))])):(i(),l("div",F,[We.value?(i(),v(T,{key:0,class:"loading-icon",spinning:We.value},null,8,["spinning"])):c("",!0),We.value?c("",!0):(i(),v(b,{key:1,title:"请绑定标签"}))]))]),o("div",Q,[o("div",X,[o("div",Z,[o("div",V,[a[6]||(a[6]=o("span",{class:"search-label"},"名称",-1)),o("div",null,[r(Xe,{value:Ge.value.keyWord,"onUpdate:value":a[0]||(a[0]=e=>Ge.value.keyWord=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入名称",class:"search-input",onKeyup:a[1]||(a[1]=g((e=>Ke(Ge.value.keyWord)),["enter"]))},null,8,["value"])])]),o("div",ee,[a[11]||(a[11]=o("span",{class:"search-label"},"审批状态",-1)),o("div",null,[r(Ve,{ref:"select",value:Re.value,"onUpdate:value":a[2]||(a[2]=e=>Re.value=e),placeholder:"请选择审批状态",class:"search-select","allow-clear":"",onChange:Ue},{default:m((()=>[r(Ze,{value:-1},{default:m((()=>a[7]||(a[7]=[p("全部")]))),_:1}),r(Ze,{value:3},{default:m((()=>a[8]||(a[8]=[p("待审批")]))),_:1}),r(Ze,{value:0},{default:m((()=>a[9]||(a[9]=[p("审批通过")]))),_:1}),r(Ze,{value:4},{default:m((()=>a[10]||(a[10]=[p("审批不通过")]))),_:1})])),_:1},8,["value"])])]),o("div",ae,[r(ea,{type:"primary",class:"search-btn",onClick:a[3]||(a[3]=e=>Ke(Ge.value.keyWord))},{default:m((()=>a[12]||(a[12]=[p(" 查询 ")]))),_:1})])]),o("div",se,[e.hasPerm("sys-layoutTemple:add")?(i(),v(ea,{key:0,type:"primary",class:"handle-btn",onClick:a[4]||(a[4]=e=>Fe("add",null))},{default:m((()=>a[13]||(a[13]=[p(" 新增开发模板 ")]))),_:1})):c("",!0)])]),o("div",te,[e.hasPerm("sys-layoutTemple:page")?(i(),l("div",le,[(i(!0),l(n,null,u(De.value,(s=>h((i(),l("div",{key:s.id,class:"item"},[o("div",ie,[o("div",oe,[o("div",re,[o("img",{src:Qe(s),alt:"图片",class:"img",onError:a[5]||(a[5]=e=>(e.target.src=M.value,e.target.style.width="auto"))},null,40,ne)]),o("div",ue,[o("div",ce,[o("div",de,"上传时间："+d(j(w)(s.createTime).format("YYYY-MM-DD HH:mm:ss")||""),1),0!==s.status?(i(),l("div",{key:0,class:f(["status",{fail:4===s.status,check:3===s.status,delete:2===s.status,unpush:1===s.status}])},d(Ee(s.status)),3)):c("",!0)])]),o("div",me,[o("div",{class:"btn",onClick:e=>(e=>{const{href:a}=P.resolve({path:"/preview/layoutPreview",query:{id:e.id,path:e.url,width:e.width,height:e.height}});window.open(a,"_blank")})(s)},"预览",8,pe),e.hasPerm("sys-layoutTemple:edit")?(i(),l("div",{key:0,class:"btn",onClick:e=>Fe("edit",s)},"编辑",8,ve)):c("",!0),0===s.status&&e.hasPerm("sys-layoutTemple:download-layoutTemple")?(i(),l("div",{key:1,class:"btn perview",onClick:e=>(e=>{z({id:e.id}).then((a=>{Be(a,e)}))})(s)},"下载",8,ge)):c("",!0),r(aa,{placement:"topRight",title:`确认${Ae(s.status)}当前开发模板？`,onConfirm:e=>(async e=>{const{id:a,status:s}=e;if(0===s)x({id:a,status:"1"}).then((e=>{200===e.code?(R("success","开发模板下架完成"),He()):R("error",e.message)}));else{const e=await N({id:a});200===e.code?(R("success","开发模板删除成功"),He()):R("error",e.message)}})(s)},{default:m((()=>[0===s.status&&e.hasPerm("sys-layoutTemple:change-status")?(i(),l("div",he,"下架")):c("",!0),0!==s.status&&e.hasPerm("sys-layoutTemple:batch-delete")?(i(),l("div",ye,"删除")):c("",!0)])),_:2},1032,["title","onConfirm"]),r(aa,{placement:"topRight",title:"确认审批通过？",okText:"通过",cancelText:"不通过",onConfirm:e=>Ie(!0,s),onCancel:e=>Ie(!1,s)},{default:m((()=>[e.hasPerm("sys-layoutTemple:change-status")&&3===s.status?(i(),l("div",je,"审批")):c("",!0),e.hasPerm("sys-layoutTemple:change-status")&&4===s.status?(i(),l("div",fe,"重新审批")):c("",!0)])),_:2},1032,["onConfirm","onCancel"]),o("div",ke,[r(ta,{placement:"top"},{title:m((()=>[o("span",null,d(s.failureCause),1)])),default:m((()=>[4===s.status?(i(),v(sa,{key:0,title:"未通过原因",style:{"margin-right":"5px"}})):c("",!0)])),_:2},1024)])])]),o("div",we,[o("div",be,[o("div",{class:"name",title:s.name},"["+d(s.width)+"*"+d(s.height)+"] "+d(s.name),9,Ce),o("div",{class:"user",title:s.createName},d(s.createName),9,_e)]),o("div",ze,[o("div",{id:s.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(i(!0),l(n,null,u(s.functionExampleTags,((e,a)=>(i(),l("div",{key:a,title:e.tagName,class:"tag-item",style:k({backgroundColor:e.color})},d(e.tagName),13,xe)))),128))],8,Te)])])])])),[[y,!Ye.value&&De.value.length]]))),128)),Ye.value?(i(),v(T,{key:0,class:"loading-icon",spinning:Ye.value},null,8,["spinning"])):c("",!0),Ye.value||De.value.length?c("",!0):(i(),v(b,{key:1}))])):(i(),l("div",Ne,[r(b,{title:"暂无权限"})])),o("div",Se,[r(la,{total:Je.value.total,"page-size-options":["12","20","30","40"],current:Je.value.current,"page-size":Je.value.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:Le},null,8,["total","current","page-size","show-total"])])])]),r(O,{ref_key:"AddExampleRef",ref:Oe,onOk:Me},null,512),r(S,{ref_key:"reasonRef",ref:$e,onOk:Me},null,512)])}}}),[["__scopeId","data-v-c35ef99c"]]);export{Oe as default};
