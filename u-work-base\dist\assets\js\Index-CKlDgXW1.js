import s from"./EditPsd-BBMpRTzv.js";import i from"./EditQuestion-CqhU1PEJ.js";import"./main-Djn9RDyT.js";import{d as o,r as t,w as e,b as r,e as a,c as l,ad as p,a5 as m,o as n}from"./@vue-HScy-mz9.js";import{_ as j}from"./vue-qr-CB2aNKv5.js";import"./userManage-DLgtGpjc.js";import"./ant-design-vue-DYY9BtJq.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./js-binary-schema-parser-G48GG52R.js";const d={class:"security-setting"},c={class:"row"},u={class:"col"},v={class:"txt"},f={class:"row"},y={class:"col"},b={class:"lab"},w={class:"row"},g=j(o({__name:"Index",props:{data:{type:Object,default:()=>{}}},emits:["ok"],setup(o,{emit:j}){const g=j,k=o,h=t({});e((()=>k.data),(s=>{h.value=s}),{immediate:!0});const _=t(),x=t(),z=()=>{g("ok")};return(o,t)=>{var e;return n(),r("div",d,[a("div",c,[a("div",u,[t[2]||(t[2]=a("div",{class:"lab"},"账户密码",-1)),a("div",v,"当前密码强度："+p(h.value.passwordStrength),1)]),a("a",{class:m(["handel-btn","LDAP"===(null==(e=h.value.sysUserDetailVo)?void 0:e.deptName)?"non-cursor":""]),onClick:t[0]||(t[0]=s=>(()=>{var s;"LDAP"!==(null==(s=h.value.sysUserDetailVo)?void 0:s.deptName)&&_.value.init()})())},"修改",2)]),a("div",f,[a("div",y,[a("div",b,[t[3]||(t[3]=a("span",null,"密码有效期",-1)),a("span",null,p(h.value.passwordPeriod),1)]),t[4]||(t[4]=a("div",{class:"txt"},"需定期更新当前账户密码",-1))])]),a("div",w,[t[5]||(t[5]=a("div",{class:"col"},[a("div",{class:"lab"},"密保问题"),a("div",{class:"txt"},"可通过回答密保问题重设密码")],-1)),a("a",{class:"handel-btn",onClick:t[1]||(t[1]=s=>{x.value.init()})},"设置")]),l(s,{ref_key:"editPsdRef",ref:_},null,512),l(i,{ref_key:"editQuestionRef",ref:x,onOk:z},null,512)])}}}),[["__scopeId","data-v-13433308"]]);export{g as default};
