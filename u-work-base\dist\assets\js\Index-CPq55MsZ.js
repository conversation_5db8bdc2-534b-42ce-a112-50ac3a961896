import{d as e,r as a,f as t,am as s,b as o,o as l,e as r,c as i,ae as n,aa as d,J as u,a9 as p,ab as c,F as m,ad as g,ag as v,E as j,a4 as h,u as k,a7 as f}from"./@vue-HScy-mz9.js";import{b as y}from"./main-Djn9RDyT.js";import{u as b}from"./useTableScrollY-DAiBD3Av.js";import{t as x,d as C,e as w,f as I,g as _}from"./tagManage-BChMdEJa.js";import N from"./AddGroup-CGLvukY0.js";import z from"./EditGroup-Dr_kX6GA.js";import T from"./AddTag-M1wjlDyW.js";import G from"./EditTag-BzjTd8xw.js";import{I as O,B as P,D as S,r as E,e as R,f as A,g as F,M as J}from"./ant-design-vue-DYY9BtJq.js";import{E as M}from"./@ant-design-CA72ad83.js";import{_ as B}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./vue-pick-colors-Cc1TP0Vg.js";const Y={class:"tag-manage"},$={class:"search-wrap"},D={class:"search-content"},K={class:"search-item"},L={class:"search-btns"},Q={class:"table-handle"},U={class:"table-wrap"},q={class:"action"},H={key:0,class:"tab-boxi"},V=["onClick"],W=["onClick"],Z={key:1,class:"tab-boxi"},X=["onClick"],ee={key:2,class:"tab-boxi"},ae=["onClick"],te={class:"tag-range-wrap keep-px"},se={key:3,class:"table-actions"},oe=["onClick"],le={class:"pagination"},re=B(e({__name:"Index",setup(e){const B=[{title:"标签组",dataIndex:"groupName",ellipsis:!0,width:200},{title:"排序",dataIndex:"sort",width:100},{title:"标签",dataIndex:"tags"},{title:"适用范围",dataIndex:"range",width:400},{title:"操作",dataIndex:"option",width:200}],re=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),ie=(e,a)=>{re.value=Object.assign(re.value,{current:e,pageSize:a}),be()},ne=a([]);C({code:"BIAO_QIAN_FAN_WEI"}).then((e=>{var a;200===e.code?ne.value=(null==(a=e.data)?void 0:a.length)?e.data.map((e=>({label:e.code,value:Number(e.value)}))):[]:ne.value=[]}));const de=a([]),ue=a(),pe=e=>{if(e.range&&e.range.length){return ne.value.filter((a=>e.range.includes(`${a.value}`))).map((e=>e.label)).join(",")}return""},ce=e=>{var a;e||de.value.join(",")!==(null==(a=ue.value.range)?void 0:a.join(","))&&w(ue.value.id,de.value).then((e=>{200===e.code?(y("success","适用范围修改成功"),be()):y("error",e.message)}))},me=a(),{scrollY:ge}=b(me),ve=a(),je=a();t((()=>{be()}));const he=a({groupName:""}),ke=a(!1),fe=a([]),ye=a(),be=()=>{fe.value=[],ke.value=!0,x({groupName:he.value.groupName?he.value.groupName:{},pageNo:re.value.current,pageSize:re.value.pageSize}).then((e=>{if(ke.value=!1,200===e.code){const{rows:a,pageNo:t,pageSize:s,totalRows:o}=e.data;fe.value=a,re.value.current=t,re.value.pageSize=s,re.value.total=o}})).catch((()=>{ke.value=!1}))},xe=()=>{re.value.current=1,re.value.pageSize=10,be()},Ce=a(),we=()=>{Ce.value.init()},Ie=a(),_e=e=>{Ie.value.init(e)},Ne=e=>{const a=JSON.parse(e),{groupId:t,data:s,type:o}=a;fe.value.forEach((e=>{e.id===t&&("add"===o&&(e.tagsId?e.tagIds.unshift(s.id):e.tagIds=[s.id],e.tags?e.tags.unshift(s):e.tags=[s],s.count=0),ze(e))}))},ze=e=>(e.tags.sort(((e,a)=>e.sort-a.sort)),e.tagIds=[],e.tags.forEach((a=>{e.tagIds.push(a.id)})),e),Te=e=>{const{data:a,type:t}=e;"add"===t?(a.tagsIds=[],a.tags=[],fe.value.unshift(a)):"edit"===t&&fe.value.forEach((e=>{e.id===a.id&&(e.groupName=a.groupName,e.sort=a.sort)})),fe.value.sort(((e,a)=>e.sort-a.sort))};return(e,a)=>{const t=O,b=P,x=s("edit-outlined"),C=s("close-outlined"),w=s("plus-outlined"),ze=s("FormOutlined"),Ge=E,Oe=S,Pe=R,Se=A,Ee=F;return l(),o("div",Y,[r("div",$,[r("div",D,[r("div",K,[a[5]||(a[5]=r("span",{class:"search-label"},"分组名称",-1)),r("div",null,[i(t,{value:he.value.groupName,"onUpdate:value":a[0]||(a[0]=e=>he.value.groupName=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入标签组名称",class:"search-input",onKeyup:a[1]||(a[1]=n((e=>xe()),["enter"]))},null,8,["value"])])]),r("div",L,[i(b,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>xe())},{default:d((()=>a[6]||(a[6]=[u(" 查询 ")]))),_:1})])]),r("div",Q,[e.hasPerm("tagGroup:addTag")?(l(),p(b,{key:0,type:"primary",class:"handle-btn",onClick:we},{default:d((()=>a[7]||(a[7]=[u("新增分组")]))),_:1})):c("",!0)])]),r("div",U,[r("div",{ref_key:"table",ref:me,class:"table-content"},[i(Se,{class:"table",scroll:{y:k(ge)},pagination:!1,size:"small",loading:ke.value,"row-key":e=>e.id,columns:B,"data-source":fe.value,onChange:ie},{headerCell:d((({column:e})=>[r("div",q,g(e.title),1)])),bodyCell:d((({column:t,record:s})=>{var n,k;return["sort"===t.dataIndex?(l(),o(m,{key:0},[u(g(s.sort||"-"),1)],64)):c("",!0),"tags"===t.dataIndex?(l(),o(m,{key:1},[(null==(n=s.tags)?void 0:n.length)?(l(),o("div",H,[(l(!0),o(m,null,v(s.tags,((a,t)=>(l(),o("p",{key:t,style:h({background:a.color}),onClick:j((e=>{return t=a,void(ye.value=t);var t}),["stop"])},[u(g(a.tagName)+"("+g(a.count)+") ",1),e.hasPerm("tagGroup:editTag")?(l(),p(x,{key:0,class:"icon-delete",title:"编辑",onClick:j((e=>{return t=s,o=a,void je.value.init(t,o);var t,o}),["stop"])},null,8,["onClick"])):c("",!0),e.hasPerm("tagGroup:del")?(l(),p(C,{key:1,class:"icon-delete",title:"删除",onClick:j((e=>((e,a,t)=>{const s=a.count?"标签下有资源，是否删除该标签?":"是否删除该标签?";J.confirm({icon:i(M),content:s,okText:"确认",cancelText:"取消",onOk:()=>{_(e.id,a.id).then((a=>{200!==a.code?y("error",a.message):(y("success","标签删除成功"),e.tags.splice(t,1))}))}})})(s,a,t)),["stop"])},null,8,["onClick"])):c("",!0)],12,V)))),128)),e.hasPerm("tagGroup:addTag")?(l(),o("p",{key:0,class:"add-icon",onClick:j((e=>_e(s)),["stop"])},[i(w)],8,W)):c("",!0)])):(l(),o("div",Z,[e.hasPerm("tagGroup:addTag")?(l(),o("p",{key:0,class:"add-icon",onClick:j((e=>_e(s)),["stop"])},[i(w)],8,X)):c("",!0)]))],64)):c("",!0),"range"===t.dataIndex?(l(),o("div",ee,[e.hasPerm("tagGroup:modifyRange")?(l(),p(Oe,{key:0,style:{width:"120px"},trigger:["click"],placement:"bottomRight",destroyPopupOnHide:!0,onVisibleChange:ce},{overlay:d((()=>[r("div",te,[i(Ge,{value:de.value,"onUpdate:value":a[3]||(a[3]=e=>de.value=e),name:"checkboxgroup",options:ne.value,onClick:a[4]||(a[4]=e=>{e.stopPropagation()})},null,8,["value","options"])])])),default:d((()=>[r("p",{class:"add-icon",onClick:j((e=>(e=>{ue.value=e,de.value=e.range?e.range.map((e=>Number(e))):[]})(s)),["stop"])},[u(g(pe(s)),1),i(ze,{style:{"margin-top":"5px","margin-left":"5px"}})],8,ae)])),_:2},1024)):c("",!0)])):c("",!0),"option"===t.dataIndex?(l(),o("div",se,[e.hasPerm("tagGroup:add")?(l(),o("a",{key:0,type:"text",onClick:e=>(e=>{ve.value.init(e)})(s)},"编辑",8,oe)):c("",!0),e.hasPerm("tagGroup:delGroup")?(l(),p(Pe,{key:1,placement:"topRight",title:(null==(k=s.tags)?void 0:k.length)?"请先将标签组下的标签删除再删除标签组。":"确认删除？","ok-text":"确认","cancel-text":"取消",onConfirm:e=>(async e=>{var a;if(null==(a=e.tags)?void 0:a.length)return;const t=await I(e.id);200===t.code?(y("success","标签组删除成功"),be()):y("error",t.message)})(s)},{default:d((()=>a[8]||(a[8]=[r("a",{type:"text"},"删除",-1)]))),_:2},1032,["title","onConfirm"])):c("",!0)])):c("",!0)]})),_:1},8,["scroll","loading","row-key","data-source"]),r("div",le,[fe.value.length>0?(l(),p(Ee,f({key:0},re.value,{onChange:ie}),null,16)):c("",!0)])],512)]),i(N,{ref_key:"addFormRef",ref:Ce,onOk:Te},null,512),i(z,{ref_key:"editFormRef",ref:ve,onOk:Te},null,512),i(T,{ref_key:"addTagRef",ref:Ie,onOk:Ne},null,512),i(G,{ref_key:"editTagRef",ref:je,onOk:Ne},null,512)])}}}),[["__scopeId","data-v-55f7295d"]]);export{re as default};
