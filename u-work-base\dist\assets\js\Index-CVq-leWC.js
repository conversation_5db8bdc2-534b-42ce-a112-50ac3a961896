import{d as e,r as a,f as s,w as t,am as l,b as i,o as r,e as o,a9 as n,c as d,aa as c,u,ad as p,a5 as m,ae as v,J as h,ab as y,a7 as j}from"./@vue-HScy-mz9.js";import{g as f}from"./department-CTxHSeTj.js";import{a as g,u as k,b}from"./main-Djn9RDyT.js";import{I as w,m as _,n as x,B as C,e as S,f as z,g as I,M as E}from"./ant-design-vue-DYY9BtJq.js";import{S as P,T as O}from"./@ant-design-CA72ad83.js";import{_ as T}from"./vue-qr-CB2aNKv5.js";import{g as M,A as N,d as A,a as J}from"./AddEditForm-BOlHGWf5.js";import{u as K}from"./useTableScrollY-DAiBD3Av.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const R={class:"tree-wrap"},D={class:"tree-contain"},L={class:"tree-search"},B={key:1,class:"tree-content"},G=["title"],U={class:"title"},q=T(e({__name:"DepartGroup",emits:["handleClick","onBack"],setup(e,{expose:v,emit:h}){const y=h,j=g(),k=a({children:"children",title:"title",key:"id"}),b=a(""),C=a([]);let S=[];const z=a([]),I=a([]);s((()=>{E("")}));t((()=>j.checkedEnterprise),(()=>{E("")}));const E=e=>{f({enterpriseId:j.checkedEnterprise.id}).then((a=>{if(!a.success)return;const s=[];s.push({children:a.data,id:"0",pid:"0",title:j.checkedEnterprise.name||"默认"}),C.value=s,S=s,z.value=e?[e]:[C.value[0].id]}))},O=(e,a)=>{e.length?z.value=e:z.value=[C.value[0].id],y("handleClick",e,a)};t(b,(e=>{C.value=T(S,e)}));const T=(e,a)=>{const s=e&&JSON.parse(JSON.stringify(e));if(!s||!s.length)return[];const t=[];for(const l of s){const e=T(l.children,a);l.title.indexOf(a)>-1?t.push(l):e&&e.length&&(l.children=e,t.push(l))}return t.length?t:[]};return v({init:e=>{E(e)}}),(e,a)=>{const s=w,t=l("down-outlined"),v=x;return r(),i("div",R,[o("div",D,[o("div",L,[d(s,{value:b.value,"onUpdate:value":a[0]||(a[0]=e=>b.value=e),valueModifiers:{trim:!0},class:"filter",placeholder:"请输入搜索内容","allow-clear":!0},{suffix:c((()=>[d(u(P))])),_:1},8,["value"])]),0===C.value.length?(r(),n(u(_),{key:0,image:u(_).PRESENTED_IMAGE_SIMPLE},null,8,["image"])):(r(),i("div",B,[d(v,{selectedKeys:z.value,"onUpdate:selectedKeys":a[1]||(a[1]=e=>z.value=e),"show-icon":"","tree-data":C.value,class:"cus-tree","default-expand-all":!0,"default-expanded-keys":I.value,"field-names":k.value,onSelect:O},{switcherIcon:c((({switcherCls:e})=>[d(t,{class:m(e)},null,8,["class"])])),title:c((e=>[o("span",{class:"root-tree-item",title:e.title},[o("span",U,p(e.title),1)],8,G)])),_:1},8,["selectedKeys","tree-data","default-expanded-keys","field-names"])]))])])}}}),[["__scopeId","data-v-d75567b6"]]),F={class:"org-manage"},Y={class:"left"},$={class:"right"},H={class:"search-wrap"},Q={class:"search-content"},Z={class:"search-item"},V={class:"search-btns"},W={class:"table-handle"},X={class:"table-wrap"},ee={key:0,class:"table-actions"},ae=["onClick"],se={class:"pagination"},te=T(e({__name:"Index",setup(e){const l=g();k();const p=[{title:"部门名称",dataIndex:"name",width:"20%",ellipsis:!0},{title:"唯一编码",dataIndex:"code",width:"20%",ellipsis:!0},{title:"排序",dataIndex:"sort",width:"10%"},{title:"备注",dataIndex:"remark",width:"35%",ellipsis:!0},{title:"操作",dataIndex:"action",width:"15%"}],m=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),f=(e,a)=>{m.value=Object.assign(m.value,{current:e,pageSize:a}),G()},_=a({keys:[],list:[]}),x={onChange:(e,a)=>{_.value.keys=e,_.value.list=a},getCheckboxProps:e=>({disabled:"LDAP"===e.code})},P=a({name:null,pid:"0"}),T=a(!1),R=a([]),D=a(),{scrollY:L}=K(D);s((()=>{G()})),t((()=>l.checkedEnterprise),(()=>{B()}));const B=()=>{m.value.current=1,m.value.pageSize=10,G()},G=()=>{R.value=[],T.value=!0,M({...P.value,enterpriseId:l.checkedEnterprise.id,pageNo:m.value.current,pageSize:m.value.pageSize}).then((e=>{if(200===e.code){const{rows:a,pageNo:s,pageSize:t,totalRows:l}=e.data;R.value=a,m.value.current=s,m.value.pageSize=t,m.value.total=l}T.value=!1})).catch((()=>{T.value=!1}))},U=a(),te=()=>{E.confirm({title:"提示",content:"确定要删除吗 ?",okText:"确定",cancelText:"取消",onOk:()=>{le()}})},le=()=>{T.value=!0,J({ids:_.value.keys}).then((e=>{e.success?(b("success","部门删除成功"),U.value.init(P.value.pid),B()):b("error",e.message),T.value=!1})).catch((()=>{T.value=!1})).finally((()=>{_.value.keys=[],_.value.list=[]}))},ie=e=>{P.value.pid=e.toString(),B()},re=a(),oe=(e,a)=>{re.value.init(e,a,P.value.pid)},ne=()=>{U.value.init(P.value.pid),B()};return(e,a)=>{const s=w,t=C,l=S,g=z,k=I;return r(),i("div",F,[o("div",Y,[d(q,{ref_key:"departGroupRef",ref:U,class:"side-menu",onHandleClick:ie},null,512)]),o("div",$,[o("div",H,[o("div",Q,[o("div",Z,[a[4]||(a[4]=o("span",{class:"search-label"},"部门名称",-1)),d(s,{value:P.value.name,"onUpdate:value":a[0]||(a[0]=e=>P.value.name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入部门名称",class:"search-input",onKeyup:a[1]||(a[1]=v((e=>B()),["enter"]))},null,8,["value"])]),o("div",V,[d(t,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>B())},{default:c((()=>a[5]||(a[5]=[h(" 查询 ")]))),_:1})])]),o("div",W,[e.hasPerm("sys-dept:add")?(r(),n(t,{key:0,type:"primary",class:"handle-btn",onClick:a[3]||(a[3]=e=>oe("add",null))},{default:c((()=>a[6]||(a[6]=[h(" 新增部门 ")]))),_:1})):y("",!0),e.hasPerm("sys-dept:delete-batch")?(r(),n(t,{key:1,class:"handle-btn",disabled:_.value.keys.length<=0,onClick:te},{icon:c((()=>[d(u(O))])),default:c((()=>[a[7]||(a[7]=h(" 批量删除 "))])),_:1},8,["disabled"])):y("",!0)])]),o("div",X,[o("div",{ref_key:"table",ref:D,class:"table-content"},[e.hasPerm("sys-dept:page")?(r(),n(g,{key:0,class:"table",scroll:{y:u(L)},pagination:!1,size:"small",loading:T.value,"row-key":e=>e.id,"row-selection":x,columns:p,"data-source":R.value},{bodyCell:c((({column:s,record:t})=>["action"===s.dataIndex?(r(),i("div",ee,[e.hasPerm("sys-dept:edit")?(r(),i("a",{key:0,type:"text",onClick:e=>oe("edit",t)},"编辑",8,ae)):y("",!0),e.hasPerm("sys-dept:delete")?(r(),n(l,{key:1,placement:"topRight",title:"确认删除？",onConfirm:()=>(e=>{A(e).then((e=>{e.success?(b("success","部门删除成功"),U.value.init(P.value.pid),B()):b("error",e.message)})).finally((()=>{_.value.keys=[],_.value.list=[]}))})(t)},{default:c((()=>a[8]||(a[8]=[o("a",{type:"text"},"删除",-1)]))),_:2},1032,["onConfirm"])):y("",!0)])):y("",!0)])),_:1},8,["scroll","loading","row-key","data-source"])):y("",!0),o("div",se,[R.value.length>0?(r(),n(k,j({key:0},m.value,{onChange:f}),null,16)):y("",!0)])],512)])]),d(N,{ref_key:"addEditFormRef",ref:re,onOk:ne},null,512)])}}}),[["__scopeId","data-v-74dd59cd"]]);export{te as default};
