import{u as e,a as s,q as a,b as t,d as r}from"./main-Djn9RDyT.js";import{u as o}from"./useTableScrollY-DAiBD3Av.js";import{s as i,u as l}from"./userManage-D6iEBY45.js";import n from"./NonDense-CuRLN4-j.js";import{_ as c}from"./UserRole.vue_vue_type_script_setup_true_lang-D492xu4E.js";import d from"./ResetPassword-DhXzoOpl.js";import p from"./InviteMember-Bb71cHv0.js";import{I as m,B as u,e as h,f as g,g as j,M as v}from"./ant-design-vue-DYY9BtJq.js";import{a0 as f,T as y}from"./@ant-design-CA72ad83.js";import{d as k,a as b,w,p as _,r as R,f as I,b as x,o as C,e as S,c as z,aa as D,J as K,a9 as N,ab as E,u as M,ad as P,a7 as T}from"./@vue-HScy-mz9.js";import{_ as V}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./dictionaryManage-gGpiMShb.js";import"./js-binary-schema-parser-G48GG52R.js";const $={class:"developer-manage"},L={class:"right"},O={class:"search-wrap"},U={class:"search-content"},J={class:"search-item"},q={class:"search-btns"},A={class:"table-handle"},B={class:"table-wrap"},F={key:0,class:"table-actions"},Y={key:1,class:"table-actions"},Q=["onClick"],Z={key:0},G={class:"pagination"},H=V(k({__name:"Index",setup(k){e();const V=s(),H=e=>{let s="";return s=3===e?"管理员":"非管理员",s},W=[{title:"账号",dataIndex:"account",sortDirections:["descend","ascend"],sorter:!1,ellipsis:!0},{title:"姓名",dataIndex:"name",sortDirections:["descend","ascend"],sorter:!1,ellipsis:!0},{title:"是否管理员",dataIndex:"manager",ellipsis:!0},{title:"角色",dataIndex:"role",ellipsis:!0},{title:"手机",dataIndex:"phone",ellipsis:!0},{title:"邮箱",dataIndex:"email",ellipsis:!0},{title:"上次登录时间",dataIndex:"lastLoginTime",sortDirections:["descend","ascend"],sorter:!1,ellipsis:!0},{title:"操作",key:"action",width:200}],X=b({pageNo:1,pageSize:10,searchValue:"",searchStatus:null,tableData:[],selectedRowKeys:[],total:0,loading:!1});w((()=>V.checkedEnterprise),(()=>{ee()}));const ee=()=>{X.pageNo=1,X.pageSize=10,se()},se=(e={})=>{var s;X.loading=!0,X.tableData=[],X.total=0;const r={pageNo:X.pageNo,pageSize:X.pageSize,name:X.searchValue,enterpriseId:null==(s=V.checkedEnterprise)?void 0:s.id};a(r).then((e=>{X.loading=!1,200===e.code?(X.tableData=e.data.rows||[],X.total=e.data.totalRows||0):t("error",e.message)})).finally((()=>{X.loading=!1})).catch((()=>{X.loading=!1}))},ae=_((()=>({total:X.total,current:X.pageNo,pageSize:X.pageSize,showTotal:e=>`共 ${e} 条`,showQuickJumper:!0,showSizeChanger:!0,size:"small"}))),te=e=>{X.selectedRowKeys=e},re=(e,s,a)=>{let t={};t=a.order?{sortField:a.field,sortRule:"descend"===a.order?"DESC":"ASC"}:{sortField:"",sortRule:""},se(t)},oe=R(),{scrollY:ie}=o(oe),le=(e,s)=>{X.pageNo=e,X.pageSize=s,se()};I((()=>{se()}));const ne=R(),ce=()=>{ne.value.init()},de=R(!1),pe=()=>{v.confirm({title:"提示",content:"确定要移除吗 ?",okText:"确定",cancelText:"取消",onOk:()=>{var e;l({enterpriseId:null==(e=V.checkedEnterprise)?void 0:e.id,grantUserIdList:X.selectedRowKeys}).then((e=>{e.success?(t("success","用户移除成功"),ee()):t("error",`移除失败：${e.message}`)})).catch((e=>{t("error",`移除失败：${e.message}`)}))}})},me=R(),ue=R(),he=R();return(e,s)=>{const a=m,o=u,v=h,k=g,b=j;return C(),x("div",$,[S("div",L,[S("div",O,[S("div",U,[S("div",J,[s[3]||(s[3]=S("span",{class:"search-label"},"关键词",-1)),z(a,{value:X.searchValue,"onUpdate:value":s[0]||(s[0]=e=>X.searchValue=e),valueModifiers:{trim:!0},class:"search-input","allow-clear":"",placeholder:"请输入姓名",onPressEnter:ee},null,8,["value"])]),S("div",q,[z(o,{type:"primary",class:"search-btn",onClick:s[1]||(s[1]=e=>ee())},{default:D((()=>s[4]||(s[4]=[K(" 查询 ")]))),_:1})])]),S("div",A,[e.hasPerm("enterprise:invitationCode")?(C(),N(o,{key:0,class:"handle-btn",type:"primary",onClick:ce},{default:D((()=>s[5]||(s[5]=[K("邀请成员")]))),_:1})):E("",!0),z(v,{placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:s[2]||(s[2]=()=>(()=>{const e=V.checkedEnterprise.id,s={ids:X.selectedRowKeys,enterpriseId:e,status:0};X.searchValue&&(s.searchValue=X.searchValue),X.searchStatus&&(s.searchStatus=X.searchStatus),de.value=!0,i(s).then((e=>{r(e),de.value=!1})).catch((()=>{de.value=!1}))})())},{title:D((()=>s[6]||(s[6]=[S("p",null,"该操作将对选中的团队成员数据进行下载",-1),S("p",null,"若未选中，将下载全部，是否继续？",-1)]))),default:D((()=>[e.hasPerm("enterprise:export")?(C(),N(o,{key:0,class:"handle-btn"},{icon:D((()=>[z(M(f))])),default:D((()=>[s[7]||(s[7]=K("导出 "))])),_:1})):E("",!0)])),_:1}),e.hasPerm("enterprise:unbind-user")?(C(),N(o,{key:1,class:"handle-btn",disabled:X.selectedRowKeys.length<=0,onClick:pe},{icon:D((()=>[z(M(y))])),default:D((()=>[s[8]||(s[8]=K("移除"))])),_:1},8,["disabled"])):E("",!0)])]),S("div",B,[S("div",{ref_key:"table",ref:oe,class:"table-content"},[z(k,{class:"table",scroll:{y:M(ie)},pagination:!1,loading:X.loading,size:"small",columns:W,"data-source":X.tableData,"row-selection":{selectedRowKeys:X.selectedRowKeys,onChange:te},"row-key":e=>e.id,onChange:re},{bodyCell:D((({column:s,record:a})=>["manager"===s.dataIndex?(C(),x("div",F,P(H(a.ueStatus)),1)):E("",!0),"action"===s.key?(C(),x("div",Y,[e.hasPerm("sys-user:grant-role")?(C(),x("a",{key:0,onClick:e=>{return s=a,void me.value.userRole(s);var s}},"授权角色",8,Q)):E("",!0),z(v,{placement:"topRight",title:"确认移除？",onConfirm:()=>{return e=a,void l({enterpriseId:null==(s=V.checkedEnterprise)?void 0:s.id,grantUserIdList:[e.id]}).then((e=>{e.success?(t("success","用户移除成功"),ee()):t("error",`移除失败：${e.message}`)})).catch((e=>{t("error",`移除失败：${e.message}`)}));var e,s}},{default:D((()=>[e.hasPerm("enterprise:unbind-user")?(C(),x("a",Z,"移除")):E("",!0)])),_:2},1032,["onConfirm"])])):E("",!0)])),_:1},8,["scroll","loading","data-source","row-selection","row-key"]),S("div",G,[X.tableData.length>0?(C(),N(b,T({key:0},ae.value,{onChange:le}),null,16)):E("",!0)])],512)])]),z(p,{ref_key:"inviteMemberRef",ref:ne,onOk:ee},null,512),z(n,{ref_key:"nonDenseRef",ref:he,onOk:se},null,512),z(c,{ref_key:"userRoleRef",ref:me,onOk:se},null,512),z(d,{ref_key:"resetPasswordRef",ref:ue,onOk:se},null,512)])}}}),[["__scopeId","data-v-5a193593"]]);export{H as default};
