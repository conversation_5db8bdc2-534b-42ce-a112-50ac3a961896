import{d as e,a as s,r as a,p as o,u as t,w as l,f as i,b as r,o as n,e as d,c,ae as p,aa as m,J as _,a9 as u,ab as y,a7 as j}from"./@vue-HScy-mz9.js";import{u as g,a as h,b as v}from"./main-Djn9RDyT.js";import{u as I}from"./useTableScrollY-DAiBD3Av.js";import{g as N,s as f,a as k}from"./role-Dauzd5F5.js";import U from"./AddEditForm-CRaALUi0.js";import A from"./AuthMenu-BMvRywbl.js";import{I as b,B as w,e as G,f as C,g as L,M as O}from"./ant-design-vue-DYY9BtJq.js";import{T as Y}from"./@ant-design-CA72ad83.js";import{_ as x}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const R={class:"role-manage"},z={class:"search-wrap"},T={class:"search-content"},E={class:"search-item"},S={class:"search-item"},P={class:"search-btns"},K={class:"table-handle"},M={class:"table-wrap"},J={key:0,class:"table-actions"},H=["onClick"],Z=["onClick"],X={class:"pagination"},W=x(e({__name:"Index",setup(e){var x;g();const W=h(),B=s({name:"",code:"",pageNo:1,pageSize:10,sysCategoryId:W.sysCategoryId,enterpriseId:null==(x=W.checkedEnterprise)?void 0:x.id}),D=a(0),F=s([{title:"角色名",dataIndex:"name",key:"name",ellipsis:!0},{title:"唯一编码",dataIndex:"code",key:"code",ellipsis:!0},{title:"排序",dataIndex:"sort",key:"sort",ellipsis:!0},{title:"备注",dataIndex:"remark",key:"remark",ellipsis:!0},{title:"操作",key:"action",width:240}]),$=e=>{oe.selectedRowKeys=e},q=o((()=>({current:B.pageNo,pageSize:B.pageSize,total:D.value,pageSizeOptions:["10","20","50","100"],showTotal:(e,s)=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),Q=(e,s)=>{B.pageNo=e,B.pageSize=s,le()},V=a(),{scrollY:ee}=I(V);let se=s([]);const ae=o((()=>({selectedRowKeys:t(oe.selectedRowKeys),onChange:$,hideDefaultSelections:!0,getCheckboxProps:e=>({disabled:["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_CHENG_YUN"].indexOf(e.code)>-1})}))),oe=s({selectedRowKeys:[],loading:!1,addLoading:!1,delLoading:!1});l((()=>W.checkedEnterprise),(()=>{var e;B.enterpriseId=null==(e=W.checkedEnterprise)?void 0:e.id,te()}));const te=()=>{B.pageNo=1,B.pageSize=10,le()},le=async()=>{oe.loading=!0,se=[],D.value=0,N(B).then((e=>{oe.loading=!1,200===e.code&&(se=e.data.rows||[],D.value=e.data.totalRows)}),(()=>{oe.loading=!1}))},ie=a(),re=()=>{O.confirm({title:"提示",content:"确定要删除吗 ?",okText:"确定",cancelText:"取消",onOk:()=>{ne()}})},ne=()=>{k({ids:oe.selectedRowKeys}).then((e=>{200===e.code?(v("success","删除成功"),oe.selectedRowKeys=[],te()):v("error","删除失败")}))},de=a(),ce=(e,s)=>{de.value.init(e,B.sysCategoryId,s)},pe=()=>{te()};return i((()=>{le()})),(e,s)=>{const a=b,o=w,l=G,i=C,g=L;return n(),r("div",R,[d("div",z,[d("div",T,[d("div",E,[s[6]||(s[6]=d("span",{class:"search-label"},"角色名称",-1)),c(a,{value:B.name,"onUpdate:value":s[0]||(s[0]=e=>B.name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入角色名称",class:"search-input",onKeyup:s[1]||(s[1]=p((e=>te()),["enter"]))},null,8,["value"])]),d("div",S,[s[7]||(s[7]=d("span",{class:"search-label"},"唯一编码",-1)),c(a,{value:B.code,"onUpdate:value":s[2]||(s[2]=e=>B.code=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入唯一编码",class:"search-input",onKeyup:s[3]||(s[3]=p((e=>te()),["enter"]))},null,8,["value"])]),d("div",P,[c(o,{type:"primary",class:"search-btn",onClick:s[4]||(s[4]=e=>te())},{default:m((()=>s[8]||(s[8]=[_(" 查询 ")]))),_:1})])]),d("div",K,[e.hasPerm("sys-role:add")?(n(),u(o,{key:0,type:"primary",class:"handle-btn",loading:oe.addLoading,onClick:s[5]||(s[5]=e=>ce("add",null))},{default:m((()=>s[9]||(s[9]=[_(" 新增角色 ")]))),_:1},8,["loading"])):y("",!0),e.hasPerm("sys-role:delete-batch")?(n(),u(o,{key:1,class:"handle-btn",disabled:0===oe.selectedRowKeys.length,loading:oe.delLoading,onClick:re},{icon:m((()=>[c(t(Y))])),default:m((()=>[s[10]||(s[10]=_(" 批量删除"))])),_:1},8,["disabled","loading"])):y("",!0)])]),d("div",M,[d("div",{ref_key:"table",ref:V,class:"table-content"},[c(i,{class:"table",scroll:{y:t(ee)},pagination:!1,"row-key":e=>e.id,size:"small",columns:F,loading:oe.loading,"row-selection":ae.value,"data-source":t(se)},{bodyCell:m((({column:a,record:o})=>["action"===a.key?(n(),r("div",J,[e.hasPerm("sys-role:edit")&&-1===["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_GUAN_LI_YUAN","YE_WU_GUAN_LI_JIAO_SE"].indexOf(o.code)?(n(),r("a",{key:0,onClick:e=>ce("edit",o)},"编辑",8,H)):y("",!0),e.hasPerm("sys-role:grant-menu")&&-1===["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_GUAN_LI_YUAN","YE_WU_GUAN_LI_JIAO_SE"].indexOf(o.code)?(n(),r("a",{key:1,onClick:e=>(e=>{ie.value.roleMenu(e)})(o)},"权限管理",8,Z)):y("",!0),e.hasPerm("sys-role:delete")&&-1===["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_GUAN_LI_YUAN","YE_WU_GUAN_LI_JIAO_SE"].indexOf(o.code)?(n(),u(l,{key:2,placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:e=>(e=>{f({...e,sysCategoryId:B.sysCategoryId}).then((e=>{200===e.code?(v("success","删除成功"),te()):v("error","删除失败")}))})(o)},{title:m((()=>s[11]||(s[11]=[d("p",null,"确定要删除吗?",-1)]))),default:m((()=>[s[12]||(s[12]=d("a",null,"删除",-1))])),_:2},1032,["onConfirm"])):y("",!0)])):y("",!0)])),_:1},8,["scroll","row-key","columns","loading","row-selection","data-source"]),d("div",X,[t(se).length>0?(n(),u(g,j({key:0},q.value,{onChange:Q}),null,16)):y("",!0)])],512),c(U,{ref_key:"addEditFormRef",ref:de,onOk:pe},null,512),c(A,{ref_key:"authMenuRef",ref:ie,onOk:pe},null,512)])])}}}),[["__scopeId","data-v-016e0fc4"]]);export{W as default};
