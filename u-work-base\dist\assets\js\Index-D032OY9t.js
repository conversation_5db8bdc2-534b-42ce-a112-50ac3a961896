import{d as e,r as a,j as t,w as s,a as l,am as o,b as i,o as n,e as r,c,F as u,ag as p,ab as d,ad as v,aa as m,J as g,a9 as f,ae as h,y,G as j,a5 as k,a4 as w,E as _}from"./@vue-HScy-mz9.js";import"./viewerjs-_Br7E8dP.js";import{I as b,u as C}from"./useCookies-BRWvy2S3.js";import{S as T,A as z}from"./AiSearch-B-73Nj9G.js";import{V as S}from"./ViewerArrow-r3R4USz-.js";import{p as N}from"./projectGallery-DFHuwUAq.js";import x from"./Nodata-mmdoiDH6.js";import I from"./EditChart-ByRFzGqX.js";import{U,c as $,a as R,p as O,s as P}from"./UseTemp-DIJiQIPo.js";import{u as A,e as E,b as G}from"./main-Djn9RDyT.js";import D from"./Detail-Ch-fDuOZ.js";import J from"./UploadFile-Drt4IowT.js";import{q as L,r as q,S as F,I as H,x as V,w as B,B as K,s as M,e as W,d as X,g as Z}from"./ant-design-vue-DYY9BtJq.js";import{_ as Q}from"./vue-qr-CB2aNKv5.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./vue3-cookies-D4wQmYyh.js";import"./axios-7z2hFSF6.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./projectGallery-xT8wgNPG.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./screenTemplate-Brx_fHCI.js";const Y={class:"chart-templete"},ee={class:"content-wrap"},ae={class:"tag-search"},te={key:0,class:"tag-content"},se=["onClick"],le={key:1,class:"tag-item"},oe={key:1,class:"no-tag-content"},ie={class:"content-list"},ne={class:"search"},re={class:"search-wrap"},ce={key:0,class:"list"},ue={class:"contain"},pe={class:"img-box"},de=["src"],ve={class:"bottom-wrapper"},me={class:"bottom-content"},ge={class:"hover-box"},fe=["onClick"],he=["onClick"],ye=["onClick"],je={class:"control-icon"},ke={class:"item-bottom"},we={class:"title"},_e=["title"],be=["title"],Ce={class:"tag-wrapper"},Te=["id"],ze=["onClick"],Se={class:"type-list"},Ne={key:1,class:"list"},xe={class:"pagination-box"},Ie=Q(e({__name:"Index",setup(e){const Q=a(sessionStorage.getItem("XI_TONG_LOGO")||N),Ie=A(),Ue=a("");E({code:"AI_SHI_TU"}).then((e=>{var a,t;const s=null==(t=null==(a=e.data)?void 0:a.rows[0])?void 0:t.value;Ue.value="无"!==s?s:""})).catch((()=>{Ue.value=""}));const $e=a("");E({code:"SCREEN_UI_IP"}).then((e=>{var a,t;const s=(null==(t=null==(a=e.data)?void 0:a.rows[0])?void 0:t.value)||"";$e.value=s})).catch((()=>{$e.value=""}));const Re=e=>{We.current=1,We.pageSize=12,Ze()},Oe=a(),Pe=a(0),Ae=a(0),Ee=()=>{},Ge=()=>{};t((()=>{}));const De=a(),Je=a(),Le=()=>{Je.value.init()},qe=a(!0),Fe=a({}),He=a([]);s((()=>Fe.value),(()=>{Xe(1,12)}),{deep:!0});const Ve=a(!0);Ve.value=!0,$().then((e=>{Ve.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Fe.value[e.id]=[]})):Fe.value={},He.value=e.data||[]):He.value=[]})).catch((()=>{Fe.value={},He.value=[],Ve.value=!1}));const Be=a(),Ke=a(),Me=a([]),We=l({total:0,current:1,pageSize:12,likeContent:"",tagIds:[],source:1,checkedType:-1}),Xe=(e,a)=>{We.current=e,We.pageSize=a,Ze()},Ze=async e=>{var a;qe.value=!0;const t=(null==(a=Object.values(Fe.value))?void 0:a.flat(Infinity))||[],s={pageNo:We.current,pageSize:We.pageSize,name:We.likeContent,tagIds:t,status:1===We.source?0:-1===We.checkedType?null:We.checkedType,type:1===We.source||3===We.source?"0":"1"};3===We.source&&(s.createUser=Ie.userInfo.id);const l=await R(s);if(qe.value=!1,200===l.code){const{rows:e,totalRows:a}=l.data;We.total=a,Me.value=e||[]}else G("error",l.message)},Qe=a(),Ye=e=>{let a="";return 0===e?a="正常":1===e?a="待审批":2===e&&(a="未通过"),a};return(e,a)=>{var t,s;const l=L,N=q,$=F,R=o("search-outlined"),A=H,E=V,Ie=B,ea=K,aa=M,ta=W,sa=o("exception-outlined"),la=X,oa=o("ellipsis-outlined"),ia=Z;return n(),i("div",Y,[r("div",ee,[r("div",ae,[(null==(t=He.value)?void 0:t.length)?(n(),i("div",te,[(n(!0),i(u,null,p(He.value,(e=>{var a,t;return n(),i("div",{key:e.id,class:"tag-group"},[(null==(a=e.tags)?void 0:a.length)?(n(),i("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const t=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Fe.value[e.id])===JSON.stringify(t)?Fe.value[e.id]=[]:Fe.value[e.id]=e.tags.map((e=>e.id))})(e)},v(e.groupName),9,se)):d("",!0),(null==(t=e.tags)?void 0:t.length)?(n(),i("div",le,[c(N,{value:Fe.value[e.id],"onUpdate:value":a=>Fe.value[e.id]=a,style:{width:"100%"}},{default:m((()=>[(n(!0),i(u,null,p(e.tags,(e=>(n(),i("div",{key:e.id,class:"tag-item-name"},[c(l,{value:e.id},{default:m((()=>[g(v(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):d("",!0)])})),128))])):(n(),i("div",oe,[Ve.value?(n(),f($,{key:0,class:"loading-icon",spinning:Ve.value},null,8,["spinning"])):d("",!0),Ve.value?d("",!0):(n(),f(x,{key:1,title:"请绑定标签"}))]))]),r("div",ie,[r("div",ne,[r("div",re,[c(A,{value:We.likeContent,"onUpdate:value":a[1]||(a[1]=e=>We.likeContent=e),valueModifiers:{trim:!0},class:"search-wrapper",placeholder:"搜索",onKeyup:a[2]||(a[2]=h((e=>Xe(1,12)),["enter"]))},{suffix:m((()=>[c(R,{style:{cursor:"pointer"},onClick:a[0]||(a[0]=e=>Xe(1,12))})])),_:1},8,["value"]),c(Ie,{value:We.source,"onUpdate:value":a[3]||(a[3]=e=>We.source=e),placeholder:"请选择数据源",class:"search-select",onChange:a[4]||(a[4]=e=>(We.checkedType=-1,void Xe(1,12)))},{default:m((()=>[c(E,{value:1},{default:m((()=>a[9]||(a[9]=[g("公共资源")]))),_:1}),c(E,{value:2},{default:m((()=>a[10]||(a[10]=[g("项目资源")]))),_:1}),c(E,{value:3},{default:m((()=>a[11]||(a[11]=[g("我的资源")]))),_:1})])),_:1},8,["value"]),3===We.source?(n(),f(Ie,{key:0,value:We.checkedType,"onUpdate:value":a[5]||(a[5]=e=>We.checkedType=e),placeholder:"请选择状态",class:"search-select",onChange:a[6]||(a[6]=e=>Xe(1,12))},{default:m((()=>[c(E,{value:-1},{default:m((()=>a[12]||(a[12]=[g("全部")]))),_:1}),c(E,{value:0},{default:m((()=>a[13]||(a[13]=[g("审批通过")]))),_:1}),c(E,{value:1},{default:m((()=>a[14]||(a[14]=[g("待审批")]))),_:1}),c(E,{value:2},{default:m((()=>a[15]||(a[15]=[g("未通过")]))),_:1})])),_:1},8,["value"])):d("",!0),c(ea,{type:"primary",class:"search-btn",style:{"margin-right":"20px"},onClick:a[7]||(a[7]=e=>Xe(1,12))},{default:m((()=>a[16]||(a[16]=[g(" 查询 ")]))),_:1}),Ue.value?(n(),f(aa,{key:1,trigger:"click",destroyTooltipOnHide:!0,placement:"bottom"},{content:m((()=>[c(z,{onAiSearch:Re,url:Ue.value},null,8,["url"])])),default:m((()=>[c(T)])),_:1})):d("",!0)]),e.hasPerm("annotate-component:upload-component")?(n(),f(ea,{key:0,type:"primary",class:"handle-btn",onClick:Le},{default:m((()=>a[17]||(a[17]=[g(" 导入图表模板 ")]))),_:1})):d("",!0)]),e.hasPerm("annotate-component:page-component")?(n(),i("div",ce,[(n(!0),i(u,null,p(Me.value,(t=>{var s,l,o;return y((n(),i("div",{key:t.id,class:"item"},[r("div",ue,[r("div",pe,[r("img",{src:(l=t.filePath,o=t.snapShot,$e.value?`${window.config.baseUrl}${o}?width=400`:`${window.config.baseUrl}${l}${o}?width=400`),alt:"图片",class:"img",onError:a[8]||(a[8]=e=>(e.target.src=Q.value,e.target.style.width="auto"))},null,40,de),y(r("div",ve,[r("div",me,[r("div",{class:k(["status",{fail:2===t.status,normal:0===t.status,unpush:null===t.status}])},v(Ye(t.status)),3)])],512),[[j,3===We.source]]),r("div",ge,[r("div",{class:"btn",onClick:e=>(e=>{$e.value?(C(),window.open(`${$e.value}/nanshan/preview/component/${e.id}?random=${Date.now()}`,"_blank",`width=${window.innerWidth},height=${window.innerHeight-100},top=100,left=100,z-look=yes`)):window.open("_blank").location=`/screen/index.html?path=${e.filePath}&type=chart`})(t)},"预览",8,fe),3===We.source?(n(),i("div",{key:0,class:"btn perview",onClick:e=>(e=>{const a={...e};Qe.value.init(a)})(t)},"编辑",8,he)):d("",!0),3===We.source&&2===t.status&&e.hasPerm("annotate-component:delete-component")?(n(),f(ta,{key:1,placement:"topRight",title:"确认删除？",onConfirm:e=>(async e=>{200===(await P([e.id])).code&&(G("success","删除成功"),Ze())})(t)},{default:m((()=>a[18]||(a[18]=[r("div",{class:"btn perview"},"删除",-1)]))),_:2},1032,["onConfirm"])):d("",!0),3===We.source&&2===t.status&&e.hasPerm("annotate-component:modify-component")?(n(),i("div",{key:2,class:"btn perview",onClick:e=>(async e=>{const a=await O({id:e.id,status:1});200===a.code?(G("success","提交成功，请等待管理员审批！"),Ze()):G("error",a.message)})(t)},"提交审批",8,ye)):d("",!0),r("div",je,[c(la,{placement:"top"},{title:m((()=>[r("span",null,v(t.approveRemark),1)])),default:m((()=>[3===We.source&&2===t.status?(n(),f(sa,{key:0,title:"未通过原因",style:{"margin-right":"12px"}})):d("",!0)])),_:2},1024)])])]),r("div",ke,[r("div",we,[r("div",{class:"name",title:t.componentName},v(t.componentName),9,_e),r("div",{class:"user",title:t.ownerName},v(t.ownerName),9,be)]),r("div",Ce,[r("div",{id:t.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(n(!0),i(u,null,p(t.tags,((e,a)=>(n(),i("div",{key:a,class:"tag-item",style:w({backgroundColor:e.color})},v(e.tagName),5)))),128))],8,Te),y(r("div",{class:"tag-opera",onClick:_((e=>t.visibleTags=!t.visibleTags),["stop"])},[c(oa)],8,ze),[[j,t.moreTags]]),y(r("div",Se,[(n(!0),i(u,null,p(t.exTags,((e,a)=>(n(),i("div",{key:a,class:"type-item",style:w({backgroundColor:e.color})},v(e.tagName),5)))),128))],512),[[j,t.visibleTags]])])])])])),[[j,!qe.value&&(null==(s=Me.value)?void 0:s.length)]])})),128)),qe.value?(n(),f($,{key:0,class:"loading-icon",spinning:qe.value},null,8,["spinning"])):d("",!0),qe.value||(null==(s=Me.value)?void 0:s.length)?d("",!0):(n(),f(x,{key:1}))])):(n(),i("div",Ne,[c(x,{title:"暂无权限"})])),r("div",xe,[c(ia,{total:We.total,"page-size-options":["12","20","30","40"],current:We.current,"page-size":We.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:Xe},null,8,["total","current","page-size","show-total"])])])]),c(U,{ref_key:"useTempxRef",ref:Be},null,512),c(D,{ref_key:"threeDetailRef",ref:Ke},null,512),c(I,{ref_key:"editChartRef",ref:Qe,onOk:Ze},null,512),c(J,{ref_key:"uploadFileRef",ref:Je},null,512),c(b,{ref_key:"imgVideoPreviewRef",ref:De},null,512),c(S,{ref_key:"viewerArrowRef",ref:Oe,current:Pe.value,total:Ae.value,onLeft:Ee,onRight:Ge},null,8,["current","total"])])}}}),[["__scopeId","data-v-6dbc7dde"]]);export{Ie as default};
