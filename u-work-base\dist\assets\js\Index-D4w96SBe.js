import{d as e,r as a,w as s,am as t,b as l,o as i,e as o,c as r,F as n,ag as u,ab as c,ad as d,aa as m,J as p,a9 as v,ae as g,y,G as h,u as j,a5 as f,a4 as k}from"./@vue-HScy-mz9.js";import{d as w}from"./dayjs-CA7qlNSr.js";import b from"./Nodata-mmdoiDH6.js";import{p as _}from"./projectGallery-DFHuwUAq.js";import C from"./DocPreview-C6PH3jgR.js";import{a as z,g as x,b as N,d as O}from"./examples-Cf8gesNV.js";import S from"./Reason-CVWYRqKn.js";import P from"./AddExample-BG0d3DTm.js";import{C as R,b as I}from"./main-Djn9RDyT.js";import{q as E,r as T,S as $,I as q,x as U,w as W,B as J,e as D,d as G,g as M}from"./ant-design-vue-DYY9BtJq.js";import{_ as Y}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./sample-zip-Cn1wBsZQ.js";import"./@vueup-CLVdhRgW.js";import"./quill-BBEhJLA6.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./pinia-CheWBXuN.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */const A={class:"func-module"},H={class:"tag-search"},L={key:0,class:"tag-content"},B=["onClick"],K={key:1,class:"tag-item"},F={key:1,class:"no-tag-content"},Q={class:"page-wrap"},X={class:"search-wrap"},Z={class:"search-content"},V={class:"search-item"},ee={class:"search-item"},ae={class:"search-btns"},se={class:"table-handle"},te={class:"content-list"},le={class:"list"},ie={class:"contain"},oe={class:"img-box"},re={class:"img-item"},ne=["src"],ue={class:"bottom-wrapper"},ce={class:"bottom-content"},de={class:"time"},me={class:"hover-box"},pe=["onClick"],ve=["onClick"],ge={key:0,class:"btn"},ye={key:1,class:"btn"},he={key:0,class:"btn"},je={key:1,class:"btn"},fe={class:"control-icon"},ke={class:"item-bottom"},we={class:"title"},be=["title"],_e=["title"],Ce={class:"tag-wrapper"},ze=["id"],xe=["title"],Ne={class:"pagination-box"},Oe=Y(e({__name:"Index",setup(e){const Y=a(sessionStorage.getItem("XI_TONG_LOGO")||_),Oe=a(),Se=a({}),Pe=a(-1),Re=e=>{Pe.value=e,Le()},Ie=a(),Ee=(e,a)=>{if(e){const e={status:0,id:a.id};N(e).then((e=>{200===e.code?(I("success","功能示例审批完成"),He()):I("error",e.message)}))}else Ie.value.init(a)},Te=e=>{let a="";return 0===e?a="正常":1===e?a="已下架":2===e?a="已删除":3===e?a="待审批":4===e&&(a="未通过"),a},$e=a([]);s((()=>Se.value),(()=>{Le()}),{deep:!0});const qe=a(!0);qe.value=!0,z().then((e=>{qe.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Se.value[e.id]=[]})):Se.value={},$e.value=e.data||[]):$e.value=[]})).catch((()=>{Se.value={},$e.value=[],qe.value=!1}));const Ue=a({total:0,current:1,pageSize:12,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),We=(e,a)=>{Ue.value=Object.assign(Ue.value,{current:e,pageSize:a}),He()},Je=a(),De=e=>{let a="删除";return 0===e&&(a="下架"),a},Ge=a({keyWord:""}),Me=()=>{He()},Ye=a(!0),Ae=a([]),He=async()=>{var e;Ae.value=[],Ye.value=!0;const a=(null==(e=Object.values(Se.value))?void 0:e.flat(Infinity))||[],s={data:{name:Ge.value.keyWord,pageNo:Ue.value.current,pageSize:Ue.value.pageSize,tagId:a}},t=Pe.value;t>-1&&(s.data.status=t);const l=await x(s);if(Ye.value=!1,200===l.code){const{rows:e,pageNo:a,totalRows:s}=l.data;Ue.value.total=s,Ue.value.current=a,Ae.value=e}},Le=(e="")=>{Ue.value.current=1,Ue.value.pageSize=12,He()},Be=(e,a)=>{Oe.value.init(e,a)},Ke=e=>`${window.config.previewUrl}${e.previewUrl}?width=400`;return(e,a)=>{var s;const _=E,z=T,x=$,Fe=q,Qe=U,Xe=W,Ze=J,Ve=D,ea=t("exception-outlined"),aa=G,sa=t("file-word-outlined"),ta=M;return i(),l("div",A,[o("div",H,[(null==(s=$e.value)?void 0:s.length)?(i(),l("div",L,[(i(!0),l(n,null,u($e.value,(e=>{var a,s;return i(),l("div",{key:e.id,class:"tag-group"},[(null==(a=null==e?void 0:e.tags)?void 0:a.length)?(i(),l("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const s=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Se.value[e.id])===JSON.stringify(s)?Se.value[e.id]=[]:Se.value[e.id]=e.tags.map((e=>e.id))})(e)},d(e.groupName),9,B)):c("",!0),(null==(s=null==e?void 0:e.tags)?void 0:s.length)?(i(),l("div",K,[r(z,{value:Se.value[e.id],"onUpdate:value":a=>Se.value[e.id]=a,style:{width:"100%"}},{default:m((()=>[(i(!0),l(n,null,u(e.tags,(e=>(i(),l("div",{key:e.id,class:"tag-item-name"},[r(_,{value:e.id},{default:m((()=>[p(d(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):c("",!0)])})),128))])):(i(),l("div",F,[qe.value?(i(),v(x,{key:0,class:"loading-icon",spinning:qe.value},null,8,["spinning"])):c("",!0),qe.value?c("",!0):(i(),v(b,{key:1,title:"请绑定标签"}))]))]),o("div",Q,[o("div",X,[o("div",Z,[o("div",V,[a[6]||(a[6]=o("span",{class:"search-label"},"示例名称",-1)),o("div",null,[r(Fe,{value:Ge.value.keyWord,"onUpdate:value":a[0]||(a[0]=e=>Ge.value.keyWord=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入示例名称",class:"search-input",onKeyup:a[1]||(a[1]=g((e=>Le(Ge.value.keyWord)),["enter"]))},null,8,["value"])])]),o("div",ee,[a[11]||(a[11]=o("span",{class:"search-label"},"审批状态",-1)),o("div",null,[r(Xe,{ref:"select",value:Pe.value,"onUpdate:value":a[2]||(a[2]=e=>Pe.value=e),placeholder:"请选择审批状态",class:"search-select","allow-clear":"",onChange:Re},{default:m((()=>[r(Qe,{value:-1},{default:m((()=>a[7]||(a[7]=[p("全部")]))),_:1}),r(Qe,{value:3},{default:m((()=>a[8]||(a[8]=[p("待审批")]))),_:1}),r(Qe,{value:0},{default:m((()=>a[9]||(a[9]=[p("审批通过")]))),_:1}),r(Qe,{value:4},{default:m((()=>a[10]||(a[10]=[p("审批不通过")]))),_:1})])),_:1},8,["value"])])]),o("div",ae,[r(Ze,{type:"primary",class:"search-btn",onClick:a[3]||(a[3]=e=>Le(Ge.value.keyWord))},{default:m((()=>a[12]||(a[12]=[p(" 查询 ")]))),_:1})])]),o("div",se,[e.hasPerm("sys-sample:add")?(i(),v(Ze,{key:0,type:"primary",class:"handle-btn",onClick:a[4]||(a[4]=e=>Be("add",null))},{default:m((()=>a[13]||(a[13]=[p(" 新增功能示例 ")]))),_:1})):c("",!0)])]),o("div",te,[o("div",le,[(i(!0),l(n,null,u(Ae.value,(s=>y((i(),l("div",{key:s.id,class:"item"},[o("div",ie,[o("div",oe,[o("div",re,[o("img",{src:Ke(s),alt:"图片",class:"img",onError:a[5]||(a[5]=e=>(e.target.src=Y.value,e.target.style.width="auto"))},null,40,ne)]),o("div",ue,[o("div",ce,[o("div",de,"上传时间："+d(j(w)(s.createTime).format("YYYY-MM-DD HH:mm:ss")||""),1),0!==s.status?(i(),l("div",{key:0,class:f(["status",{fail:4===s.status,check:3===s.status,delete:2===s.status,unpush:1===s.status}])},d(Te(s.status)),3)):c("",!0)])]),o("div",me,[e.hasPerm("sys-sample:detail")?(i(),l("div",{key:0,class:"btn",onClick:e=>(e=>{const{href:a}=R.resolve({path:"/portal/sampleOnlineEditor",query:{id:e.id}});window.open(a,"_blank")})(s)},"预览",8,pe)):c("",!0),e.hasPerm("sys-sample:edit")?(i(),l("div",{key:1,class:"btn",onClick:e=>Be("edit",s)},"编辑",8,ve)):c("",!0),e.hasPerm("sys-sample:change-status")&&e.hasPerm("sys-sample:batch-delete")?(i(),v(Ve,{key:2,placement:"topRight",title:`确认${De(s.status)}当前功能示例？`,onConfirm:e=>(async e=>{const{id:a,status:s}=e;if(0===s)N({id:a,status:"1"}).then((e=>{200===e.code?(I("success","功能示例下架完成"),He()):I("error",e.message)}));else{const e=await O({id:a});200===e.code?(I("success","功能示例删除成功"),He()):I("error",e.message)}})(s)},{default:m((()=>[0===s.status?(i(),l("div",ge,"下架")):c("",!0),0!==s.status?(i(),l("div",ye,"删除")):c("",!0)])),_:2},1032,["title","onConfirm"])):c("",!0),e.hasPerm("sys-sample:change-status")&&e.hasPerm("sys-sample:edit")?(i(),v(Ve,{key:3,placement:"topRight",title:"确认审批通过？",okText:"通过",cancelText:"不通过",onConfirm:e=>Ee(!0,s),onCancel:e=>Ee(!1,s)},{default:m((()=>[3===s.status?(i(),l("div",he,"审批")):c("",!0),4===s.status?(i(),l("div",je,"重新审批")):c("",!0)])),_:2},1032,["onConfirm","onCancel"])):c("",!0),o("div",fe,[r(aa,{placement:"top"},{title:m((()=>[o("span",null,d(s.failureCause),1)])),default:m((()=>[4===s.status?(i(),v(ea,{key:0,title:"未通过原因",style:{"margin-right":"5px"}})):c("",!0)])),_:2},1024),r(sa,{title:"示例说明",onClick:e=>(e=>{Je.value.init(e)})(s.remark)},null,8,["onClick"])])])]),o("div",ke,[o("div",we,[o("div",{class:"name",title:s.name},d(s.name),9,be),o("div",{class:"user",title:s.createName},d(s.createName),9,_e)]),o("div",Ce,[o("div",{id:s.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(i(!0),l(n,null,u(s.functionExampleTags,((e,a)=>(i(),l("div",{key:a,title:e.tagName,class:"tag-item",style:k({backgroundColor:e.color})},d(e.tagName),13,xe)))),128))],8,ze)])])])])),[[h,!Ye.value&&Ae.value.length]]))),128)),Ye.value?(i(),v(x,{key:0,class:"loading-icon",spinning:Ye.value},null,8,["spinning"])):c("",!0),Ye.value||Ae.value.length?c("",!0):(i(),v(b,{key:1}))]),o("div",Ne,[r(ta,{total:Ue.value.total,"page-size-options":["12","20","30","40"],current:Ue.value.current,"page-size":Ue.value.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:We},null,8,["total","current","page-size","show-total"])])])]),r(P,{ref_key:"AddExampleRef",ref:Oe,onOk:Me},null,512),r(C,{ref_key:"docPreviewRef",ref:Je},null,512),r(S,{ref_key:"reasonRef",ref:Ie,onOk:Me},null,512)])}}}),[["__scopeId","data-v-6e028e48"]]);export{Oe as default};
