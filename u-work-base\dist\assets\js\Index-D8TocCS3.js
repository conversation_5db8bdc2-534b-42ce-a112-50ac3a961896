import{b as e}from"./vue-router-BEwRlUkF.js";import{a}from"./axios-7z2hFSF6.js";import{E as t,g as o,F as s,c as i,G as l,R as n,H as d}from"./main-Djn9RDyT.js";import{l as r}from"./ti-B4l9Nygm.js";import{g as c}from"./systemAuthoriza-D4YElOeQ.js";import{u as p}from"./vue3-cookies-D4wQmYyh.js";import u from"./EditPsd-BBMpRTzv.js";import{F as m,d as g,q as v,c as h,I as f,N as _,p as j}from"./ant-design-vue-DYY9BtJq.js";import{d as w,r as T,a as y,m as I,f as S,b as k,e as N,c as A,ad as E,J as O,aa as X,F as G,ab as L,u as b,o as F}from"./@vue-HScy-mz9.js";import{_ as D}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./userManage-DLgtGpjc.js";import"./js-binary-schema-parser-G48GG52R.js";const U={class:"login"},$={class:"left"},B={class:"text"},Y={class:"title"},C={class:"sub-title"},J=["src"],q={class:"right"},z={class:"right-content"},x={class:"title"},M=["src"],R={class:"sub-title"},H={key:0,class:"ldap-toggle"},K=["src"],P={class:"notice-msg"},W=D(w({__name:"Index",setup(w){const{cookies:D}=p(),W=T(!0),Z=T(!1),Q=e(),V=y({userName:"",password:"",code:"",ldapStatus:!1}),ee=T(),ae=T(),te=y({captimage:{codeData:"",uuid:""}});const oe=function(e,a){let t;return function(){t||(e.apply(this,arguments),t=setTimeout((()=>{clearTimeout(t),t=null}),a))}}((()=>{Z.value||ee.value.validate().then((()=>{const e={account:V.userName,code:V.code,password:V.password,uuid:te.captimage.uuid,ldapStatus:V.ldapStatus?1:0};Z.value=!0,l(e).then((e=>{Z.value=!1;const a=n(V.password);D.set("ACCESS_P",a,"","/",window.location.hostname,!1,"Lax"),D.set("ACCESS_TOKEN_U",e.data,"","/",window.location.hostname,!1,"Lax"),d().then((()=>{Q.push("/")}))}),(e=>{Z.value=!1,se(),"密码已过期"===e.message&&ae.value.init(!1,V.userName)})).catch((e=>{Z.value=!1,se()}))}))}),1e3),se=()=>{t().then((e=>{const a=e.data,t={codeData:`data:image/gif;base64,${a.img}`,uuid:a.uuid};te.captimage=t}))},ie=e=>{13===e.keyCode&&oe()},le=y({loginTitle1:"一站式提供",loginTitle2:"数字孪生项目生产工具",loginTitle3:"资源库、项目管理、孪生服务、场景管理、业务管理、SDK",loginBg:"/assets/png/login-bg-qJoBk4qS.png",loginLogo:r,loginTitle:"工作台",loginNotice:"",loginDesc:"数字孪生项目生产工具"});(async()=>{a.get(`${window.config.appApi}/edtap/sys-config/open?code=XI_TONG_BIAO_TI`,{headers:{"X-Security-FreshToken":o()}}).then((e=>{var a,t;le.loginTitle="无"!==(null==(a=e.data)?void 0:a.data)?null==(t=e.data)?void 0:t.data:"",sessionStorage.setItem("XI_TONG_BIAO_TI",le.loginTitle)})).catch((e=>{s(e)})),a.get(`${window.config.appApi}/edtap/sys-config/open?code=YI_JI_BIAO_TI`,{headers:{"X-Security-FreshToken":o()}}).then((e=>{var a,t;le.loginTitle1="无"!==(null==(a=e.data)?void 0:a.data)?null==(t=e.data)?void 0:t.data:""})).catch((e=>{s(e)})),a.get(`${window.config.appApi}/edtap/sys-config/open?code=ER_JI_BIAO_TI`,{headers:{"X-Security-FreshToken":o()}}).then((e=>{var a,t;le.loginTitle2="无"!==(null==(a=e.data)?void 0:a.data)?null==(t=e.data)?void 0:t.data:""})).catch((e=>{s(e)})),a.get(`${window.config.appApi}/edtap/sys-config/open?code=SAN_JI_BIAO_TI`,{headers:{"X-Security-FreshToken":o()}}).then((e=>{var a,t;le.loginTitle3="无"!==(null==(a=e.data)?void 0:a.data)?null==(t=e.data)?void 0:t.data:""})).catch((e=>{s(e)})),a.get(`${window.config.appApi}/edtap/sys-config/open?code=DENG_LU_YE_LOGO`,{headers:{"X-Security-FreshToken":o()}}).then((e=>{var a,t,o;le.loginLogo="无"!==(null==(a=e.data)?void 0:a.data)?null==(t=e.data)?void 0:t.data:"",sessionStorage.setItem("XI_TONG_LOGO",null==(o=e.data)?void 0:o.data)})).catch((e=>{s(e)})),a.get(`${window.config.appApi}/edtap/sys-config/open?code=DENG_LU_YE_BEI_JING_TU`,{headers:{"X-Security-FreshToken":o()}}).then((e=>{var a,t;le.loginBg="无"!==(null==(a=e.data)?void 0:a.data)?null==(t=e.data)?void 0:t.data:""})).catch((e=>{s(e)})),a.get(`${window.config.appApi}/edtap/sys-config/open?code=DENG_LU_YE_TI_SHI_YU`,{headers:{"X-Security-FreshToken":o()}}).then((e=>{var a,t;le.loginNotice="无"!==(null==(a=e.data)?void 0:a.data)?null==(t=e.data)?void 0:t.data:""})).catch((e=>{s(e)})),a.get(`${window.config.appApi}/edtap/sys-config/open?code=DENG_LU_YE_XI_TONG_SHUO_MING`,{headers:{"X-Security-FreshToken":o()}}).then((e=>{var a,t;le.loginDesc="无"!==(null==(a=e.data)?void 0:a.data)?null==(t=e.data)?void 0:t.data:""})).catch((e=>{s(e)})),a.get(`${window.config.appApi}/edtap/sys-config/open?code=XI_TONG_JIA_MI`,{headers:{"X-Security-FreshToken":o()}}).then((e=>{var a;i().setSensitiveConfig((null==(a=e.data)?void 0:a.data)||"Y")})).catch((e=>{s(e)}))})();const ne=T(!1);a.get(`${window.config.appApi}/edtap/auth/ldap-switch`,{headers:{"X-Security-FreshToken":o()}}).then((e=>{var a;ne.value="Y"===(null==(a=e.data)?void 0:a.data)})).catch((e=>{s(e)}));return c().then((e=>{if(200===e.code&&!0===e.success){const{data:a}=e;a.valid||Q.push("/grant")}})),I((()=>{window.removeEventListener("keyup",ie,!1)})),S((()=>{se(),window.addEventListener("keyup",ie),setTimeout((()=>{W.value=!1}),1e3)})),(e,a)=>{const t=v,o=g,s=f,i=h,l=_,n=j,d=m;return F(),k(G,null,[N("div",U,[N("div",$,[N("div",B,E(le.loginTitle1),1),N("div",Y,E(le.loginTitle2),1),N("div",C,E(le.loginTitle3),1),N("img",{class:"img",src:le.loginBg,loading:"lazy",alt:"背景图"},null,8,J)]),N("div",q,[N("div",z,[N("div",x,[N("img",{src:le.loginLogo,alt:"logo"},null,8,M),O(E(le.loginTitle),1)]),N("div",R,E(le.loginDesc),1),A(d,{ref_key:"loginRef",ref:ee,model:V,name:"login",autocomplete:"off"},{default:X((()=>[ne.value?(F(),k("div",H,[A(o,{placement:"top"},{title:X((()=>a[5]||(a[5]=[N("span",null,"勾选代表使用优锘内部账号登录",-1)]))),default:X((()=>[A(t,{checked:V.ldapStatus,"onUpdate:checked":a[0]||(a[0]=e=>V.ldapStatus=e)},{default:X((()=>a[6]||(a[6]=[O("公司OA账号")]))),_:1},8,["checked"])])),_:1})])):L("",!0),A(i,{class:"common-form-item",name:"userName",rules:[{required:!0,message:"请输入用户名"}]},{default:X((()=>[A(s,{value:V.userName,"onUpdate:value":a[1]||(a[1]=e=>V.userName=e),class:"input",placeholder:"用户名"},null,8,["value"])])),_:1}),A(i,{class:"common-form-item",name:"password",rules:[{required:!0,message:"请输入密码"}]},{default:X((()=>[A(l,{value:V.password,"onUpdate:value":a[2]||(a[2]=e=>V.password=e),autocomplete:"off",class:"input",placeholder:"密码"},null,8,["value"])])),_:1}),A(i,{class:"common-form-item",name:"code",rules:[{required:!0,message:"请输入验证码"}]},{default:X((()=>[A(n,{value:V.code,"onUpdate:value":a[3]||(a[3]=e=>V.code=e),class:"input code-input",placeholder:"请输入验证码"},null,8,["value"]),N("img",{src:te.captimage.codeData,class:"code-img",onClick:se},null,8,K)])),_:1}),A(i,null,{default:X((()=>[N("div",{class:"login-btn",onClick:a[4]||(a[4]=(...e)=>b(oe)&&b(oe)(...e))},"登录")])),_:1}),N("div",P,E(le.loginNotice),1)])),_:1},8,["model"])])])]),A(u,{ref_key:"editPsdRef",ref:ae},null,512)],64)}}}),[["__scopeId","data-v-4e675f2e"]]);export{W as default};
