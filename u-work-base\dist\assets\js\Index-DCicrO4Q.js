import{d as e,r as a,a as s,f as t,am as r,b as l,o as i,e as o,c as n,J as A,ad as c,u,a9 as d,ab as m,aa as p,ae as g,F as v,y as j,G as y,a5 as k,a4 as h,a7 as w}from"./@vue-HScy-mz9.js";import{u as S,a as b,Y as T,k as f,C,Z as U,b as x,_ as D,$ as I}from"./main-Djn9RDyT.js";import{u as E}from"./useTableScrollY-DAiBD3Av.js";import H from"./AddEditForm-ew-ZMyyy.js";import{S as J}from"./@ant-design-CA72ad83.js";import{I as Q,B as Y,s as O,k as P,D as R,u as M,v as N,e as B,f as F,g as z}from"./ant-design-vue-DYY9BtJq.js";import{_ as q}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const K="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAgCAYAAAFyR/jpAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAHqADAAQAAAABAAAAIAAAAADUUDHiAAAEg0lEQVRIDb1XXWhURxQ+c7NmU0PNS7VFoihWpS7ubgz486AWpPjS9EkCRrOaSrUUi/gSrPUnVkWNkIeiWJUS2QSlMU/2sfqggn8Quwmm4h+0YNOqpcWKMbp3d/zOdefu3Nl7rxsMDmxmzjnfOWfOmTNnbogKI94lF/NSxLvlJSFpkRJYIOYLm6b3NwvhIEhKkeimvEI487y0/EgxRKJLSkVgHrKYYP1ont7DcrIHERmmascyowxVgrCyb4PIWtjXQgaYg4WQLWOTNoQVJkDRrgugVyCo0yzAvDGTEocVyJ1h7T+XYCATyS55F4HN0ASPENYkTzia0FlaHDjy4dk96M+Yb3G8yOtlh8jTJzyD7nX4/Mc0p2jO6bAijDnnxqpbYNMMdIVKa/aP8t2qSvob9HjFw5yLEE3paxZ/abxXyom0/BmrT3VB6FrStf6UWODUASJaB3A2VEETVghaxSSnykYED0hSA8fCPxj7QsMS5Ns0WX2O6I5KpV91PH5s0we/t4gRapNWYgb9AWO1ukFe8/Fyct43BeXQbrbruuWcvKTB1ykhpCUDzeJiKA47+o7jwu+HUKCfEEpcX+f8ZIrnblsx4mm5B9n9VtE8ozqPD6TEep3Ha1cZXhpB/2QCDPprHNkhxROJEzKGa3xDMcqZLUkf/5oS50NvYpghbkLOTcXmS7tBgCbi7OVqQw8aFtM6ZZVTSQCjUZzncwzQ+w1KMUeGquPZqonQMyQrw0SmWSydFSXcPhpiujD+5y0qRWDPolxzyZkU98SMIzmJI3FuTH2PrHluU9WNJvGAjUCpA9PmgkESFtV5lJUA8xZ4OsB0Mi3XSkGdmsxZhimb2BKalZ3ASyRlMNDmH7HyjjKwJuRY/xrxp16eZ4BoMFEGfRW5cLu3q6xAyOptrGcqujA/xHHV8uOk80uUWVhov3zWUdumqYMtgrtNyfBVLkGBUXdKTs5lqQ3X9XOQnhcRRs7gsmy5nhI3/XT9eIGO64/KcXY1fYmOvB2KE/2UA3jDuGcdI8+p/dY68SQAU+wjDKhLy6X45mgHd36Qwmj5iOwe7ulWHHCPritwgdsg2AamJ306aCzX2Mgp3PsmCy/EQRjehV9gWsbIcZZbHepzE9srOeN5XfJDpHsfsrDiTR3C+AUE1orGdNW0xQ0oCydc/tsH7lE36hZ+iwMPTwMqeT84c4rcwNUQbO2cHaXO040Cr39x4KNlOey0Qx5H+0iyY79PgnMAtOIBvl5UJUKlj89V02a011bwJ+DH6TsSsWh3X5P4R8cmO+W0fAXthbOV4Hsyq7qmn2PdxgsQh+0o7RlsFP/qAn3NLw4eDj6/b/Cr0WXmulzHpt59bP97nMcVfGrFEMtXAMw1QWE0O+anbLSjFsfQ7uTOk8DRmUGnI37y3ub4JbNKZNw9cy9Gme3CRlqwizFtJnBS0stdx2bI/A8ehNxcFpiyMug7+AbYOrBa9AZhAx3rCvxg5N6h9TC2A/xJuqywfoq5Y+QFHQx7GHS9shzrCryO9cjKyDOKYSO30ZXY6ajHSw0Hmwj/fuA+AAAAAElFTkSuQmCC",X={class:"company-manage"},V={class:"header-statistics keep-px"},W={class:"statistics-item"},Z={class:"number"},G={class:"statistics-item"},L={class:"number"},$={class:"statistics-item"},_={class:"number"},ee={class:"all-content"},ae={class:"search-wrap"},se={class:"search-content"},te={class:"search-item"},re={class:"search-btns"},le={class:"table-wrap"},ie={class:"table-detail"},oe=["src"],ne={key:1},Ae={class:"normal campus-status-wrap"},ce={class:"stop campus-status-wrap"},ue={key:2,class:"campus-used-wrap"},de={key:3,class:"campus-used-wrap"},me={key:4,class:"campus-used-wrap",style:{width:"100%"}},pe={key:5,class:"campus-used-wrap",style:{width:"100%"}},ge={key:6,class:"table-actions"},ve=["onClick"],je=["onClick"],ye=["onClick"],ke={class:"ant-dropdown-link"},he={class:"pagination"},we=q(e({__name:"Index",setup(e){const q=S(),we=b(),Se=a(),be=(e,a)=>{Se.value.init(e,a)},Te=e=>{Oe(),"add"===e&&Ce()},fe=s({enterPriseStartTotal:0,enterPriseTotal:0,projectStartTotal:0,projectTotal:0}),Ce=()=>{I({userId:1===we.adminType?"":q.userInfo.id}).then((e=>{if(200===e.code){const{enterPriseStartTotal:a,enterPriseTotal:s,projectStartTotal:t,projectTotal:r}=e.data;fe.enterPriseStartTotal=a||0,fe.enterPriseTotal=s||0,fe.projectStartTotal=t||0,fe.projectTotal=r||0}}))},Ue=[{title:"团队名",dataIndex:"name",ellipsis:!0},{title:"状态",dataIndex:"status"},{title:"负责人",dataIndex:"developer"},{title:"联系方式",dataIndex:"developerPhone"},{title:"注册时间",dataIndex:"registerTime",ellipsis:!0},{title:"项目数量",dataIndex:"projectCount"},{title:"团队成员数量",dataIndex:"userCount"},{title:"操作",dataIndex:"actions",width:250}],xe=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),De=(e,a)=>{xe.value=Object.assign(xe.value,{current:e,pageSize:a}),Oe()},Ie=a(),{scrollY:Ee}=E(Ie),He=a({name:null}),Je=a(!1),Qe=a([]),Ye=(e,a)=>{if(!e)return"--";const s=new Date(e),t=s.getFullYear(),r=s.getMonth()+1,l=s.getDate(),i=s.getHours(),o=s.getMinutes(),n=s.getSeconds();return s.getDay(),`${t}-${r<10?`0${r}`:r}-${l<10?`0${l}`:l} ${i<10?`0${i}`:i}:${o<10?`0${o}`:o}:${n<10?`0${n}`:n}`};t((()=>{Ce(),Oe()}));const Oe=()=>{Qe.value=[],Je.value=!0,T({name:He.value.name,userId:"",pageNo:xe.value.current,pageSize:xe.value.pageSize}).then((e=>{if(200===e.code){const{rows:a,pageNo:s,totalRows:t}=e.data;Qe.value=a,xe.value.current=s,xe.value.total=t}Je.value=!1})).catch((()=>{Je.value=!1}))};return(e,a)=>{const s=Q,t=Y,S=O,b=P,T=r("DownOutlined"),I=B,E=N,q=M,we=R,Pe=F,Re=z;return i(),l("div",X,[o("div",V,[o("div",W,[a[5]||(a[5]=o("div",{class:"title"},[o("img",{src:K,alt:"company"}),A("团队总数")],-1)),o("div",Z,c(u(fe).enterPriseTotal),1)]),a[8]||(a[8]=o("div",{class:"line"},null,-1)),o("div",G,[a[6]||(a[6]=o("div",{class:"title"},[o("img",{src:K,alt:"project"}),A("活动团队数")],-1)),o("div",L,c(u(fe).enterPriseStartTotal),1)]),a[9]||(a[9]=o("div",{class:"line"},null,-1)),o("div",$,[a[7]||(a[7]=o("div",{class:"title"},[o("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAeCAYAAAFF3D5OAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAGqADAAQAAAABAAAAHgAAAAC5SRDKAAADVUlEQVRIDa1WW08TQRSenW23gsR4ARMkGJGgPBqFGmNi5EEfJPqApqI++jP8Kzyq4IYYo8EHbyQqQVrkzcRL1ETDi3i32HTb7ni+7Z7tdDukRZhkO+fyfeecnTkzWyFMw8m6Ck/gYyGVdVcgS1hJmFZCPYrYyaw7C8XCD2CWEs8t5sIY8IRQU1ACGARnwb1M2g0SlZfOBKDgZ8v85D7vaOYmQIT+Jl65DuQEYvqQwihU5R9nVRRF1hWSlCfwaIn7oDeOF9e3sTHBArO8MHT0MgzATFVaon1pcg8UMJiFimW5bC8nF6cOw8nDt+2P4duzqTYHyS1fvuSloXCf4I6qomLGa/iwNN0AOZVzJ5QSVyEHFULQRrTgsBH4IIFfa/6qaIkf3nBmJ9vrSDC2ZSd7K8IO6gtBHmVLMQEzNihH8xAUJf0jhaHxJbJBDQZF/Q2BbNUOIxnLExDgaHVoK0QRlNwFounl9YB1Pak71i9TC3Hbm8jGLXek84sW4CT2y0RqWHJaJXR7za7EFT4XHKDmJIuTc78LJXawk2fLEoPF4cwb1qPyUgvuMxMBwHiX1GUCgMorYoKMYYvK3kL60ueqVv0NMuF08BmmKEEHMIgJwPDJis45nW3BB5IJPOOk+XR2/HJQSS56Jwa0MA//D4lvs1p8auV8TTNLdQ0LCDVrXxwqy3anwkUWjnjD0tKqOXZWZytJ83ndFs/UG79EdDDL614I3LYNHcHRGubZ2YTTsTJDrXY68FnigZfvGhUjI7R7zUdL1dHn5JqzdaUUJUFcSggbfM3T6EfNgE5kb52QwnpIrqjnDTCYPF+oU+X0xadr+LUzrSE65m7v9pLleTLt18ytiB+cUuJY/vjYlzjYuHSU5A4Bu+kp0BN+UuPUOh0YYLtDbp0TSkvNgD8S1DmdDexqgK/FdKbL5NNtCbp4D1G2aTL2a45lT7YdEENn/2q25uLivXbHL7wlYI8Gfk9FXpCUZJGMehJgepJidVADtySGHD0JeP3IgT2yW4qyMZBtbIaNxTSz43eQEVVR1piUYtTkrPhixmSP2+I3ZM2PK2Z7xzkxcAYfqebj3f2U8zN/t+720FhrJ9JAmyFij0qbEahJjJKUCXuA+vwxAekju+nDQ2zk+AcOZRvHn8vbewAAAABJRU5ErkJggg==",alt:"project"}),A("项目数量")],-1)),o("div",_,c(u(fe).projectStartTotal),1)])]),o("div",ee,[o("div",ae,[o("div",se,[o("div",te,[n(s,{value:He.value.name,"onUpdate:value":a[1]||(a[1]=e=>He.value.name=e),valueModifiers:{trim:!0},class:"filter",placeholder:"请输入名称查询","allow-clear":!0,onKeyup:a[2]||(a[2]=g((e=>Oe()),["enter"]))},{suffix:p((()=>[n(u(J),{onClick:a[0]||(a[0]=e=>Oe())})])),_:1},8,["value"])]),o("div",re,[n(t,{type:"primary",class:"search-btn",onClick:a[3]||(a[3]=e=>Oe())},{default:p((()=>a[10]||(a[10]=[A(" 查询 ")]))),_:1})])]),e.hasPerm("enterprise:save")?(i(),d(t,{key:0,type:"primary",class:"handle-btn",onClick:a[4]||(a[4]=e=>be("add",null))},{default:p((()=>a[11]||(a[11]=[A(" 新增团队 ")]))),_:1})):m("",!0)]),o("div",le,[o("div",{ref_key:"table",ref:Ie,class:"table-content"},[e.hasPerm("enterprise:select-page")?(i(),d(Pe,{key:0,class:"table",scroll:{y:u(Ee)},pagination:!1,size:"small",loading:Je.value,"row-key":e=>e.id,columns:Ue,"data-source":Qe.value,onChange:De},{expandedRowRender:p((({record:e})=>[o("div",ie,[o("p",null,"备注说明："+c(e.remark||"无"),1)])])),bodyCell:p((({column:s,record:t})=>{var r,g;return["name"===s.dataIndex?(i(),l(v,{key:0},[t.logoUrl?(i(),d(S,{key:0},{content:p((()=>{return[o("img",{class:"pre-img",src:(e=t,`${window.config.previewUrl}${e.logoUrl}`),alt:""},null,8,oe)];var e})),default:p((()=>[o("div",null,c(t.name),1)])),_:2},1024)):(i(),l("div",ne,c(t.name),1))],64)):m("",!0),"status"===s.dataIndex?(i(),l(v,{key:1},[j(o("div",Ae,a[12]||(a[12]=[o("div",{class:"campus-status-icon"},null,-1),o("div",{class:"campus-status"},"正常",-1)]),512),[[y,0===t.status]]),j(o("div",ce,a[13]||(a[13]=[o("div",{class:"campus-status-icon"},null,-1),o("div",{class:"campus-status"},"停用",-1)]),512),[[y,1===t.status]])],64)):m("",!0),"registerTime"===s.dataIndex?(i(),l("div",ue,c(Ye((null==(r=t.sysUser)?void 0:r.createTime)||t.registerTime)),1)):m("",!0),"contacts"===s.dataIndex?(i(),l("div",de,c(null==(g=t.sysUser)?void 0:g.name),1)):m("",!0),"projectCount"===s.dataIndex?(i(),l("div",me,[n(b,{"stroke-color":u(f)(t.projectCount/t.projectMaxCount*100),showInfo:!1,percent:t.projectCount/t.projectMaxCount*100,size:"small"},null,8,["stroke-color","percent"]),A(" "+c(t.projectCount+"/"+t.projectMaxCount),1)])):m("",!0),"userCount"===s.dataIndex?(i(),l("div",pe,c(t.userCount),1)):m("",!0),"actions"===s.dataIndex?(i(),l("div",ge,[e.hasPerm("enterprise:detail")?(i(),l("a",{key:0,class:k({disable:1===t.status}),onClick:e=>(e=>{C.push(`/operator/companyDetail/${e.id}?name=${e.name}`)})(t)},"进入",10,ve)):m("",!0),e.hasPerm("enterprise:update")?(i(),l("a",{key:1,class:k({disable:1===t.status}),onClick:e=>be("edit",t)},"编辑",10,je)):m("",!0),e.hasPerm("enterprise:updateStatus")?(i(),l("a",{key:2,style:h(0===t.status?{color:"red"}:{}),onClick:e=>(e=>{const a=0===e.status?1:0;U({...e,status:a}).then((e=>{200===e.code?(x("success",(0===a?"团队启用":"团队停用")+"成功"),Oe(),Ce()):x("error",e.message)}))})(t)},c(1===t.status?"启用":"停用"),13,ye)):m("",!0),e.hasPerm("enterprise:delete")?(i(),d(we,{key:3},{overlay:p((()=>[n(q,null,{default:p((()=>[n(E,null,{default:p((()=>[n(I,{placement:"topRight",title:"确认删除？",onConfirm:()=>(e=>{D({id:e.id}).then((e=>{200===e.code?(x("success","团队删除成功"),Oe(),Ce()):x("error",e.message)}))})(t)},{default:p((()=>a[15]||(a[15]=[o("a",null,"删除",-1)]))),_:2},1032,["onConfirm"])])),_:2},1024)])),_:2},1024)])),default:p((()=>[o("a",ke,[a[14]||(a[14]=A(" 更多 ")),n(T)])])),_:2},1024)):m("",!0)])):m("",!0)]})),_:1},8,["scroll","loading","row-key","data-source"])):m("",!0),o("div",he,[Qe.value.length>0?(i(),d(Re,w({key:0},xe.value,{onChange:De}),null,16)):m("",!0)])],512)])]),n(H,{ref_key:"addEditFormRef",ref:Se,onOk:Te},null,512)])}}}),[["__scopeId","data-v-419a8000"]]);export{we as default};
