import{d as e,a as s,p as a,r as o,u as l,f as i,b as t,o as r,e as n,c,ae as d,aa as p,J as m,a9 as u,ab as _,F as y,ad as g,a7 as h}from"./@vue-HScy-mz9.js";import{u as j,a as k,b as v}from"./main-Djn9RDyT.js";import{u as f}from"./useTableScrollY-DAiBD3Av.js";import{d as I,e as N,f as U,h as b}from"./role-OrQ5c9FV.js";import w from"./AddEditForm-Bssn0_2K.js";import A from"./AuthMenu-rER3L4Tk.js";import{I as C,B as G,e as L,f as x,g as O,M as Y}from"./ant-design-vue-DYY9BtJq.js";import{T as z}from"./@ant-design-CA72ad83.js";import{_ as R}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const T={class:"role-manage"},q={class:"right"},F={class:"search-wrap"},S={class:"search-content"},P={class:"search-item"},K={class:"search-item"},E={class:"search-btns"},M={class:"table-handle"},J={class:"btns-wrap"},X={class:"table-wrap"},Z={key:0,class:"table-actions"},H=["onClick"],W=["onClick"],B=["onClick"],D=["onClick"],$={class:"pagination"},Q=R(e({__name:"Index",setup(e){j();const R=k(),Q=s({name:"",code:"",pageNo:1,pageSize:10,sysCategoryId:"",enterpriseId:"",acquiesceFlag:1});a((()=>1===R.adminType));const V=o(0),ee=s([{title:"角色名",dataIndex:"name",key:"name",ellipsis:!0},{title:"唯一编码",dataIndex:"code",key:"code",ellipsis:!0},{title:"排序",dataIndex:"sort",key:"sort",ellipsis:!0},{title:"是否默认",dataIndex:"acquiesceFlag",key:"acquiesceFlag",ellipsis:!0},{title:"备注",dataIndex:"remark",key:"remark",ellipsis:!0},{title:"操作",key:"action",width:280}]),se=e=>{ne.selectedRowKeys=e},ae=a((()=>({current:Q.pageNo,pageSize:Q.pageSize,total:V.value,pageSizeOptions:["10","20","50","100"],showTotal:(e,s)=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),oe=(e,s)=>{Q.pageNo=e,Q.pageSize=s,de()},le=o(),{scrollY:ie}=f(le);let te=s([]);const re=a((()=>({selectedRowKeys:l(ne.selectedRowKeys),onChange:se,hideDefaultSelections:!0,getCheckboxProps:e=>({disabled:["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_GUAN_LI_YUAN","YE_WU_GUAN_LI_JIAO_SE"].indexOf(e.code)>-1})}))),ne=s({selectedRowKeys:[],loading:!1,addLoading:!1,delLoading:!1}),ce=()=>{Q.pageNo=1,Q.pageSize=10,de()},de=()=>{ne.loading=!0,N(Q).then((e=>{ne.loading=!1,200===e.code&&(te=e.data.rows,V.value=e.data.totalRows)}),(()=>{ne.loading=!1}))},pe=o(),me=()=>{Y.confirm({title:"提示",content:"确定要删除吗？",okText:"确定",cancelText:"取消",onOk:()=>{ue()}})},ue=()=>{b({ids:ne.selectedRowKeys}).then((e=>{200===e.code?(v("success","角色删除成功"),ne.selectedRowKeys=[],ce()):v("error","角色删除失败")}))},_e=o(),ye=(e,s)=>{_e.value.init(e,s,Q.sysCategoryId,Q.enterpriseId)},ge=()=>{ce()};return i((()=>{de()})),(e,s)=>{const a=C,o=G,i=L,j=x,k=O;return r(),t("div",T,[n("div",q,[n("div",F,[n("div",S,[n("div",P,[s[6]||(s[6]=n("span",{class:"search-label"},"角色名称",-1)),c(a,{value:Q.name,"onUpdate:value":s[0]||(s[0]=e=>Q.name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入角色名称",class:"search-input",onKeyup:s[1]||(s[1]=d((e=>ce()),["enter"]))},null,8,["value"])]),n("div",K,[s[7]||(s[7]=n("span",{class:"search-label"},"唯一编码",-1)),c(a,{value:Q.code,"onUpdate:value":s[2]||(s[2]=e=>Q.code=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入唯一编码",class:"search-input",onKeyup:s[3]||(s[3]=d((e=>ce()),["enter"]))},null,8,["value"])]),n("div",E,[c(o,{type:"primary",class:"search-btn",onClick:s[4]||(s[4]=e=>ce())},{default:p((()=>s[8]||(s[8]=[m(" 查询 ")]))),_:1})])]),n("div",M,[n("div",J,[e.hasPerm("sys-role:add")?(r(),u(o,{key:0,type:"primary",class:"handle-btn",loading:ne.addLoading,onClick:s[5]||(s[5]=e=>ye("add",null))},{default:p((()=>s[9]||(s[9]=[m(" 新增角色 ")]))),_:1},8,["loading"])):_("",!0),e.hasPerm("sys-role:delete-batch")?(r(),u(o,{key:1,disabled:!ne.selectedRowKeys.length,class:"handle-btn",loading:ne.delLoading,onClick:me},{icon:p((()=>[c(l(z))])),default:p((()=>[s[10]||(s[10]=m(" 批量删除"))])),_:1},8,["disabled","loading"])):_("",!0)])])]),n("div",X,[n("div",{ref_key:"table",ref:le,class:"table-content"},[c(j,{class:"table",scroll:{y:l(ie)},pagination:!1,"row-key":e=>e.id,size:"small",columns:ee,loading:ne.loading,"row-selection":re.value,"data-source":l(te)},{bodyCell:p((({column:a,record:o})=>["action"===a.key?(r(),t("div",Z,[e.hasPerm("sys-role:edit")&&-1===["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_GUAN_LI_YUAN","YE_WU_GUAN_LI_JIAO_SE"].indexOf(o.code)?(r(),t("a",{key:0,onClick:e=>ye("edit",o)},"编辑",8,H)):_("",!0),e.hasPerm("sys-role:grant-menu")?(r(),t("a",{key:1,onClick:e=>(e=>{pe.value.roleMenu(e)})(o)},"权限管理",8,W)):_("",!0),e.hasPerm("sys-role:delete")&&-1===["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_GUAN_LI_YUAN","YE_WU_GUAN_LI_JIAO_SE"].indexOf(o.code)?(r(),u(i,{key:2,placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:e=>(e=>{U({...e,sysCategoryId:Q.sysCategoryId}).then((e=>{200===e.code?(v("success","角色删除成功"),ce()):v("error","角色删除失败")}))})(o)},{title:p((()=>s[11]||(s[11]=[n("p",null,"确定要删除吗?",-1)]))),default:p((()=>[s[12]||(s[12]=n("a",null,"删除",-1))])),_:2},1032,["onConfirm"])):_("",!0),e.hasPerm("sys-role:change-acquiesce")&&2!==o.acquiesceFlag?(r(),t("a",{key:3,onClick:e=>(e=>{const s={id:e.id,acquiesceFlag:2};I(s).then((e=>{200===e.code?(v("success","设置成功"),de()):v("error",e.message)}))})(o)},"设为默认",8,B)):_("",!0),e.hasPerm("sys-role:change-acquiesce")&&2===o.acquiesceFlag?(r(),t("a",{key:4,onClick:e=>(e=>{const s={id:e.id,acquiesceFlag:1};I(s).then((e=>{200===e.code?(v("success","设置成功"),de()):v("error",e.message)}))})(o)},"取消默认",8,D)):_("",!0)])):_("",!0),"acquiesceFlag"===a.key?(r(),t(y,{key:1},[m(g(2===o.acquiesceFlag?"是":"否"),1)],64)):_("",!0)])),_:1},8,["scroll","row-key","columns","loading","row-selection","data-source"]),n("div",$,[l(te).length>0?(r(),u(k,h({key:0},ae.value,{onChange:oe}),null,16)):_("",!0)])],512),c(w,{ref_key:"addEditFormRef",ref:_e,onOk:ge},null,512),c(A,{ref_key:"authMenuRef",ref:pe,onOk:ge},null,512)])])])}}}),[["__scopeId","data-v-3532e2c1"]]);export{Q as default};
