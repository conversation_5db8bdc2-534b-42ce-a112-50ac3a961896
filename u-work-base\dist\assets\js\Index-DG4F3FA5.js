import{d as e,r as a,p as s,f as t,b as l,o as i,e as o,c as r,ae as n,aa as d,J as c,ab as u,a9 as p,ad as m,u as v,a7 as j}from"./@vue-HScy-mz9.js";import{u as g}from"./useTableScrollY-DAiBD3Av.js";import{u as h,b as f}from"./main-Djn9RDyT.js";import k from"./AddPlugin-CLZPyGoQ.js";import{b as y,d as b,c as w}from"./pluginManage-DfKZgXJX.js";import{I as _,x,w as z,B as I,e as C,Y as M,f as S,g as N,M as T}from"./ant-design-vue-DYY9BtJq.js";import{_ as $}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const J={class:"plugin-manage"},U={class:"search-wrap"},Y={class:"search-content"},A={class:"search-item"},B={class:"search-item"},K={class:"search-item"},L={class:"search-btns"},O={class:"table-handle"},P={class:"table-wrap"},R={key:0},Z={key:2,class:"table-actions"},q=["onClick"],D=["onClick"],E={class:"pagination"},Q=$(e({__name:"Index",setup(e){const $=["iframe嵌套","子应用"],Q=[{title:"应用名称",dataIndex:"name",ellipsis:!0},{title:"应用编码",dataIndex:"code",ellipsis:!0},{title:"应用版本",dataIndex:"version",ellipsis:!0},{title:"集成方式",dataIndex:"integrationMode",ellipsis:!0},{title:"状态",dataIndex:"status",ellipsis:!0},{title:"访问地址",dataIndex:"url",ellipsis:!0},{title:"运行时间",dataIndex:"runTime",ellipsis:!0},{title:"操作",dataIndex:"action"}],F=a(!1),G=a([]),H=a(),{scrollY:V}=g(H),W=a({keyword:"",status:null,integrationMode:null,pageNo:1,pageSize:10,total:0}),X=s((()=>({total:W.value.total,current:W.value.pageNo,pageSize:W.value.pageSize,showTotal:e=>`共 ${e} 条`,showQuickJumper:!0,showSizeChanger:!0,size:"small"}))),ee=(e,a)=>{W.value.pageNo=e,W.value.pageSize=a,se()};t((()=>{se()}));const ae=()=>{W.value.pageNo=1,W.value.pageSize=10,se()},se=()=>{G.value=[],F.value=!0,y({...W.value}).then((e=>{if(200===e.code){const{rows:a,pageNo:s,pageSize:t,totalRows:l}=e.data;G.value=a,W.value.pageNo=s,W.value.pageSize=t,W.value.total=l}F.value=!1})).catch((()=>{F.value=!1}))},te=e=>{b({id:e.id}).then((e=>{200===e.code?(f("success","删除成功"),ae()):f("error",e.message)}))},le=a(),ie=(e,a=null)=>{le.value.init(e,a)},oe=()=>{ae()};return(e,a)=>{const s=_,t=x,g=z,y=I,b=M,se=C,re=S,ne=N;return i(),l("div",J,[o("div",U,[o("div",Y,[o("div",A,[a[8]||(a[8]=o("span",{class:"search-label"},"关键字",-1)),r(s,{value:W.value.keyword,"onUpdate:value":a[0]||(a[0]=e=>W.value.keyword=e),placeholder:"请输入应用名称或者应用编码查询",class:"search-input",onKeyup:a[1]||(a[1]=n((e=>ae()),["enter"]))},null,8,["value"])]),o("div",B,[a[11]||(a[11]=o("span",{class:"search-label"},"应用状态",-1)),r(g,{value:W.value.status,"onUpdate:value":a[2]||(a[2]=e=>W.value.status=e),"allow-clear":"",placeholder:"请选择应用是否启用",class:"search-select",onChange:a[3]||(a[3]=e=>ae())},{default:d((()=>[r(t,{value:"0"},{default:d((()=>a[9]||(a[9]=[c("启用")]))),_:1}),r(t,{value:"1"},{default:d((()=>a[10]||(a[10]=[c("停用")]))),_:1})])),_:1},8,["value"])]),o("div",K,[a[14]||(a[14]=o("span",{class:"search-label"},"集成方式",-1)),r(g,{value:W.value.integrationMode,"onUpdate:value":a[4]||(a[4]=e=>W.value.integrationMode=e),"allow-clear":"",placeholder:"请选择集成方式",class:"search-select",onChange:a[5]||(a[5]=e=>ae())},{default:d((()=>[r(t,{value:"0"},{default:d((()=>a[12]||(a[12]=[c("iframe嵌套")]))),_:1}),r(t,{value:"1"},{default:d((()=>a[13]||(a[13]=[c("子应用")]))),_:1})])),_:1},8,["value"])]),o("div",L,[r(y,{type:"primary",class:"search-btn",onClick:a[6]||(a[6]=e=>ae())},{default:d((()=>a[15]||(a[15]=[c(" 查询 ")]))),_:1})])]),o("div",O,[r(y,{class:"handle-btn",type:"primary",onClick:a[7]||(a[7]=e=>ie("add"))},{default:d((()=>a[16]||(a[16]=[c("新增应用")]))),_:1})])]),o("div",P,[o("div",{ref_key:"table",ref:H,class:"table-content"},[r(re,{class:"table",scroll:{y:v(V)},pagination:!1,size:"small",loading:F.value,"row-key":e=>e.id,columns:Q,"data-source":G.value},{bodyCell:d((({column:e,record:a})=>["integrationMode"===e.dataIndex?(i(),l("div",R,m($[a.integrationMode]),1)):u("",!0),"status"===e.dataIndex?(i(),p(se,{key:1,placement:"top",title:0===a.status?"确定停用该应用？":"确定启用该应用？",onConfirm:()=>(e=>{w({id:e.id,status:0===e.status?1:0}).then((a=>{200===a.code?(f("success",`${0===e.status?"禁用":"启用"}${e.name}成功`),ae()):f("error",a.message)}))})(a)},{default:d((()=>[r(b,{disabled:!1,"checked-children":"启用","un-checked-children":"停用",checked:0===a.status},null,8,["checked"])])),_:2},1032,["title","onConfirm"])):u("",!0),"action"===e.dataIndex?(i(),l("div",Z,[o("a",{onClick:e=>ie("edit",a)},"编辑",8,q),o("a",{onClick:e=>(e=>{const a=h();T.confirm({title:"提示",content:a.userInfo.multiTenant?"卸载当前子应用将导致所有项目的相关应用数据被删除且无法恢复，确认卸载？":"将关联删除当前子应用的菜单、模块数据且无法恢复，是否继续？",okText:"确定",cancelText:"取消",onOk:()=>{te(e)}})})(a)},"删除",8,D)])):u("",!0)])),_:1},8,["scroll","loading","row-key","data-source"]),o("div",E,[G.value.length>0?(i(),p(ne,j({key:0},X.value,{onChange:ee}),null,16)):u("",!0)])],512)]),r(k,{ref_key:"addPluginRef",ref:le,onOk:oe},null,512)])}}}),[["__scopeId","data-v-ae6eda3f"]]);export{Q as default};
