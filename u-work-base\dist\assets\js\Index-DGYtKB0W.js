import{d as e,r as a,w as s,am as t,b as l,o as i,e as o,c as r,F as n,ag as c,ab as u,ad as d,aa as v,J as m,a9 as p,ae as g,y as f,G as h,u as k,a5 as j,a4 as y}from"./@vue-HScy-mz9.js";import{d as w}from"./dayjs-CA7qlNSr.js";import b from"./Nodata-mmdoiDH6.js";import{p as S}from"./projectGallery-DFHuwUAq.js";import _ from"./DocPreview-DheFF_Z9.js";import{m as C,g as z,r as x,d as N,b as P}from"./index-GENaTOlC.js";import I from"./Reason-Bs2b4e13.js";import O from"./AddAndEdit-CBYUm5dB.js";import{C as E,a0 as R,b as T}from"./main-Djn9RDyT.js";import{q,r as D,S as G,I as W,x as $,w as A,B as J,e as U,d as L,g as M}from"./ant-design-vue-DYY9BtJq.js";import{_ as Y}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./@vueup-CLVdhRgW.js";import"./quill-BBEhJLA6.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */const B={class:"func-module"},H={class:"tag-search"},K={key:0,class:"tag-content"},X=["onClick"],F={key:1,class:"tag-item"},Q={key:1,class:"no-tag-content"},Z={class:"page-wrap"},V={class:"search-wrap"},ee={class:"search-content"},ae={class:"search-item"},se={class:"search-item"},te={class:"search-btns"},le={class:"table-handle"},ie={class:"content-list"},oe={key:0,class:"list"},re={class:"contain"},ne={class:"img-box"},ce={class:"img-item"},ue=["src"],de={class:"bottom-wrapper"},ve={class:"bottom-content"},me={class:"time"},pe={class:"hover-box"},ge=["onClick"],fe=["onClick"],he={key:0,class:"btn"},ke={key:1,class:"btn"},je={key:0,class:"btn"},ye={key:1,class:"btn"},we={class:"control-icon"},be={class:"item-bottom"},Se={class:"title"},_e=["title"],Ce=["title"],ze={class:"tag-wrapper"},xe=["id"],Ne=["title"],Pe={key:1,class:"list"},Ie={class:"pagination-box"},Oe=Y(e({__name:"Index",setup(e){const Y=a(sessionStorage.getItem("XI_TONG_LOGO")||S),Oe=a(),Ee=a({}),Re=a(-1),Te=e=>{Re.value=e,Fe()},qe=a(),De=(e,a)=>{if(e){const e={reviewStatus:0,id:a.id};x(e).then((e=>{200===e.code?(T("success","效果包审批完成"),Xe()):T("error",e.message)}))}else qe.value.init(a)},Ge=a(!1),We=e=>{let a="";return 0===e?a="正常":1===e?a="已下架":2===e?a="已删除":3===e?a="待审批":4===e&&(a="未通过"),a},$e=a([]);s((()=>Ee.value),(()=>{Fe()}),{deep:!0});const Ae=a(!0);Ae.value=!0,C().then((e=>{Ae.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Ee.value[e.id]=[]})):Ee.value={},$e.value=e.data||[]):$e.value=[]})).catch((()=>{Ee.value={},$e.value=[],Ae.value=!1}));const Je=a({total:0,current:1,pageSize:12,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),Ue=(e,a)=>{Je.value=Object.assign(Je.value,{current:e,pageSize:a}),Xe()},Le=a(),Me=e=>{let a="删除";return 0===e&&(a="下架"),a},Ye=a({keyWord:""}),Be=()=>{Xe()},He=a(!0),Ke=a([]),Xe=async()=>{var e;Ke.value=[],He.value=!0;const a=(null==(e=Object.values(Ee.value))?void 0:e.flat(Infinity))||[],s={name:Ye.value.keyWord,pageNo:Je.value.current,pageSize:Je.value.pageSize,kind:1,tagId:a.join(",")},t=Re.value;t>-1&&(s.reviewStatus=t);const l=await z(s);if(He.value=!1,200===l.code){const{rows:e,pageNo:a,totalRows:s}=l.data;Je.value.total=s,Je.value.current=a,Ke.value=e||[]}},Fe=(e="")=>{Je.value.current=1,Je.value.pageSize=12,Xe()},Qe=(e,a)=>{Oe.value.init(e,a)},Ze=e=>`${window.config.previewEffectUrl}${e.code}/preview.png?width=400&time=`+e.updateTime;return(e,a)=>{var s;const S=q,C=D,z=G,Ve=W,ea=$,aa=A,sa=J,ta=U,la=t("exception-outlined"),ia=L,oa=t("file-word-outlined"),ra=t("download-outlined"),na=M;return i(),l("div",B,[o("div",H,[(null==(s=$e.value)?void 0:s.length)?(i(),l("div",K,[(i(!0),l(n,null,c($e.value,(e=>{var a,s;return i(),l("div",{key:e.id,class:"tag-group"},[(null==(a=null==e?void 0:e.tags)?void 0:a.length)?(i(),l("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const s=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Ee.value[e.id])===JSON.stringify(s)?Ee.value[e.id]=[]:Ee.value[e.id]=e.tags.map((e=>e.id))})(e)},d(e.groupName),9,X)):u("",!0),(null==(s=null==e?void 0:e.tags)?void 0:s.length)?(i(),l("div",F,[r(C,{value:Ee.value[e.id],"onUpdate:value":a=>Ee.value[e.id]=a,style:{width:"100%"}},{default:v((()=>[(i(!0),l(n,null,c(e.tags,(e=>(i(),l("div",{key:e.id,class:"tag-item-name"},[r(S,{value:e.id},{default:v((()=>[m(d(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):u("",!0)])})),128))])):(i(),l("div",Q,[Ae.value?(i(),p(z,{key:0,class:"loading-icon",spinning:Ae.value},null,8,["spinning"])):u("",!0),Ae.value?u("",!0):(i(),p(b,{key:1,title:"请绑定标签"}))]))]),o("div",Z,[o("div",V,[o("div",ee,[o("div",ae,[a[6]||(a[6]=o("span",{class:"search-label"},"效果包名称",-1)),o("div",null,[r(Ve,{value:Ye.value.keyWord,"onUpdate:value":a[0]||(a[0]=e=>Ye.value.keyWord=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入效果包名称",class:"search-input",onKeyup:a[1]||(a[1]=g((e=>Fe(Ye.value.keyWord)),["enter"]))},null,8,["value"])])]),o("div",se,[a[11]||(a[11]=o("span",{class:"search-label"},"审批状态",-1)),o("div",null,[r(aa,{ref:"select",value:Re.value,"onUpdate:value":a[2]||(a[2]=e=>Re.value=e),placeholder:"请选择审批状态",class:"search-select","allow-clear":"",onChange:Te},{default:v((()=>[r(ea,{value:-1},{default:v((()=>a[7]||(a[7]=[m("全部")]))),_:1}),r(ea,{value:3},{default:v((()=>a[8]||(a[8]=[m("待审批")]))),_:1}),r(ea,{value:0},{default:v((()=>a[9]||(a[9]=[m("审批通过")]))),_:1}),r(ea,{value:4},{default:v((()=>a[10]||(a[10]=[m("审批不通过")]))),_:1})])),_:1},8,["value"])])]),o("div",te,[r(sa,{type:"primary",class:"search-btn",onClick:a[3]||(a[3]=e=>Fe(Ye.value.keyWord))},{default:v((()=>a[12]||(a[12]=[m(" 查询 ")]))),_:1})])]),o("div",le,[e.hasPerm("effect-package:upload")?(i(),p(sa,{key:0,type:"primary",class:"handle-btn",onClick:a[4]||(a[4]=e=>Qe("add",null))},{default:v((()=>a[13]||(a[13]=[m(" 新增效果包 ")]))),_:1})):u("",!0)])]),o("div",ie,[e.hasPerm("effect-package:page")?(i(),l("div",oe,[(i(!0),l(n,null,c(Ke.value,(s=>f((i(),l("div",{key:s.id,class:"item"},[o("div",re,[o("div",ne,[o("div",ce,[o("img",{src:Ze(s),alt:"图片",class:"img",onError:a[5]||(a[5]=e=>(e.target.src=Y.value,e.target.style.width="auto"))},null,40,ue)]),o("div",de,[o("div",ve,[o("div",me,"上传时间："+d(k(w)(s.createTime).format("YYYY-MM-DD HH:mm:ss")||""),1),0!==s.reviewStatus?(i(),l("div",{key:0,class:j(["status",{fail:4===s.reviewStatus,check:3===s.reviewStatus,delete:2===s.reviewStatus,unpush:1===s.reviewStatus}])},d(We(s.reviewStatus)),3)):u("",!0)])]),o("div",pe,[o("div",{class:"btn",onClick:e=>(e=>{const{href:a}=E.resolve({path:"/preview/mapEffectsPreview",query:{code:e.code,id:R("CSXGBGL:EDIT")?e.id:""}});window.open(a,"_blank")})(s)},"预览",8,ge),e.hasPerm("effect-package:edit")?(i(),l("div",{key:0,class:"btn",onClick:e=>Qe("edit",s)},"编辑",8,fe)):u("",!0),e.hasPerm("effect-package:delete")&&e.hasPerm("effect-package:review")?(i(),p(ta,{key:1,placement:"topRight",title:`确认${Me(s.reviewStatus)}当前效果包？`,onConfirm:e=>(async e=>{const{id:a,reviewStatus:s}=e;if(0===s)x({id:a,reviewStatus:1}).then((e=>{200===e.code?(T("success","效果包下架完成"),Xe()):T("error",e.message)}));else{const e=await P({id:a});200===e.code?(T("success","效果包删除成功"),Xe()):T("error",e.message)}})(s)},{default:v((()=>[0===s.reviewStatus?(i(),l("div",he,"下架")):u("",!0),0!==s.reviewStatus?(i(),l("div",ke,"删除")):u("",!0)])),_:2},1032,["title","onConfirm"])):u("",!0),r(ta,{placement:"topRight",title:"确认审批通过？",okText:"通过",cancelText:"不通过",onConfirm:e=>De(!0,s),onCancel:e=>De(!1,s)},{default:v((()=>[e.hasPerm("effect-package:review")&&3===s.reviewStatus?(i(),l("div",je,"审批")):u("",!0),e.hasPerm("effect-package:review")&&4===s.reviewStatus?(i(),l("div",ye,"重新审批")):u("",!0)])),_:2},1032,["onConfirm","onCancel"]),o("div",we,[r(ia,{placement:"top"},{title:v((()=>[o("span",null,d(s.reviewDescribe),1)])),default:v((()=>[4===s.reviewStatus?(i(),p(la,{key:0,title:"未通过原因",style:{"margin-right":"5px"}})):u("",!0)])),_:2},1024),r(oa,{title:"效果包说明",style:{"margin-right":"5px"},onClick:e=>(e=>{Le.value.init(e)})(s.remark)},null,8,["onClick"]),e.hasPerm("effect-package:download")?(i(),p(ra,{key:0,title:"下载",onClick:e=>(e=>{Ge.value||(Ge.value=!0,N({code:e.code,kind:1,name:e.name}).then((e=>{Ge.value=!1,200===e.code?T("success","后台打包效果包中，这可能需要一段时间，现在您可以进行其他操作，完成后将通知您！"):T("error",e.message)})).catch((()=>{Ge.value=!1})))})(s)},null,8,["onClick"])):u("",!0)])])]),o("div",be,[o("div",Se,[o("div",{class:"name",title:s.name},d(s.name),9,_e),o("div",{class:"user",title:s.createName},d(s.createName),9,Ce)]),o("div",ze,[o("div",{id:s.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(i(!0),l(n,null,c(s.tags,((e,a)=>(i(),l("div",{key:a,title:e.tagName,class:"tag-item",style:y({backgroundColor:e.color})},d(e.tagName),13,Ne)))),128))],8,xe)])])])])),[[h,!He.value&&Ke.value.length]]))),128)),He.value?(i(),p(z,{key:0,class:"loading-icon",spinning:He.value},null,8,["spinning"])):u("",!0),He.value||Ke.value.length?u("",!0):(i(),p(b,{key:1}))])):(i(),l("div",Pe,[r(b,{title:"暂无权限"})])),o("div",Ie,[r(na,{total:Je.value.total,"page-size-options":["12","20","30","40"],current:Je.value.current,"page-size":Je.value.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:Ue},null,8,["total","current","page-size","show-total"])])])]),r(O,{ref_key:"AddExampleRef",ref:Oe,onOk:Be},null,512),r(_,{ref_key:"docPreviewRef",ref:Le},null,512),r(I,{ref_key:"reasonRef",ref:qe,onOk:Be},null,512)])}}}),[["__scopeId","data-v-cf137763"]]);export{Oe as default};
