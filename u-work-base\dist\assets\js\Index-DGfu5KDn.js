import{d as e,p as s,r as a,a as o,u as l,f as t,b as i,o as r,e as n,c as d,ae as c,aa as m,J as p,a9 as _,ab as u,F as y,ad as g,a7 as j}from"./@vue-HScy-mz9.js";import{u as f,a as h,b as k}from"./main-Djn9RDyT.js";import{u as I}from"./useTableScrollY-DAiBD3Av.js";import{j as v,f as N,h as U}from"./role-OrQ5c9FV.js";import A from"./AddEditForm-DFYI8rDD.js";import b from"./AuthMenu-DdqqIVA0.js";import w from"./checkRoleForm-C8r2W4nQ.js";import{I as G,B as L,e as O,f as C,g as R,M as T}from"./ant-design-vue-DYY9BtJq.js";import{T as Y}from"./@ant-design-CA72ad83.js";import{_ as x}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const S={class:"role-manage"},z={class:"right"},E={class:"search-wrap"},P={class:"search-content"},F={class:"search-item"},K={class:"search-item"},J={class:"search-btns"},M={class:"table-handle"},Z={class:"btns-wrap"},H={class:"table-wrap"},W={key:0,class:"table-actions"},X=["onClick"],B=["onClick"],D=["onClick"],$={key:0},q={class:"pagination"},Q=x(e({__name:"Index",setup(e){const x=f();s((()=>{var e;return null==(e=x.userInfo)?void 0:e.account}));const Q=h(),V=a(),ee=o({name:"",code:"",pageNo:1,pageSize:10,sysCategoryId:"",enterpriseId:"1"}),se=a(0),ae=o([{title:"角色名",dataIndex:"name",key:"name",ellipsis:!0},{title:"唯一编码",dataIndex:"code",key:"code",ellipsis:!0},{title:"排序",dataIndex:"sort",key:"sort",ellipsis:!0},{title:"是否系统内置",dataIndex:"inSystem",key:"inSystem",ellipsis:!0},{title:"备注",dataIndex:"remark",key:"remark",ellipsis:!0},{title:"操作",key:"action",width:240}]),oe=e=>{ce.selectedRowKeys=e},le=s((()=>({current:ee.pageNo,pageSize:ee.pageSize,total:se.value,pageSizeOptions:["10","20","50","100"],showTotal:(e,s)=>`共${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}))),te=(e,s)=>{ee.pageNo=e,ee.pageSize=s,pe()},ie=a(),{scrollY:re}=I(ie);let ne=o([]);const de=s((()=>({selectedRowKeys:l(ce.selectedRowKeys),onChange:oe,hideDefaultSelections:!0,getCheckboxProps:e=>({disabled:["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_GUAN_LI_YUAN","YE_WU_GUAN_LI_JIAO_SE"].indexOf(e.code)>-1})}))),ce=o({selectedRowKeys:[],loading:!1,addLoading:!1,delLoading:!1}),me=()=>{ee.pageNo=1,ee.pageSize=10,pe()},pe=()=>{ce.loading=!0,v(ee).then((e=>{ce.loading=!1,200===e.code&&(ne=e.data.rows,se.value=e.data.totalRows)}),(()=>{ce.loading=!1}))},_e=a(),ue=()=>{T.confirm({title:"提示",content:"确定要删除吗 ?",okText:"确定",cancelText:"取消",onOk:()=>{ye()}})},ye=()=>{U({ids:ce.selectedRowKeys}).then((e=>{200===e.code?(k("success","角色删除成功"),ce.selectedRowKeys=[],me()):k("error","角色删除失败")}))},ge=a(),je=(e,s)=>{ge.value.init(e,s)},fe=()=>{me()};return t((()=>{pe()})),(e,s)=>{const a=G,o=L,t=O,f=C,h=R;return r(),i("div",S,[n("div",z,[n("div",E,[n("div",P,[n("div",F,[s[6]||(s[6]=n("span",{class:"search-label"},"角色名称",-1)),d(a,{value:ee.name,"onUpdate:value":s[0]||(s[0]=e=>ee.name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入角色名称",class:"search-input",onKeyup:s[1]||(s[1]=c((e=>me()),["enter"]))},null,8,["value"])]),n("div",K,[s[7]||(s[7]=n("span",{class:"search-label"},"唯一编码",-1)),d(a,{value:ee.code,"onUpdate:value":s[2]||(s[2]=e=>ee.code=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入唯一编码",class:"search-input",onKeyup:s[3]||(s[3]=c((e=>me()),["enter"]))},null,8,["value"])]),n("div",J,[d(o,{type:"primary",class:"search-btn",onClick:s[4]||(s[4]=e=>me())},{default:m((()=>s[8]||(s[8]=[p(" 查询 ")]))),_:1})])]),n("div",M,[n("div",Z,[e.hasPerm("sys-role:add")?(r(),_(o,{key:0,type:"primary",class:"handle-btn",loading:ce.addLoading,onClick:s[5]||(s[5]=e=>je("add",null))},{default:m((()=>s[9]||(s[9]=[p(" 新增角色 ")]))),_:1},8,["loading"])):u("",!0),e.hasPerm("sys-role:delete-batch")?(r(),_(o,{key:1,disabled:!ce.selectedRowKeys.length,class:"handle-btn",loading:ce.delLoading,onClick:ue},{icon:m((()=>[d(l(Y))])),default:m((()=>[s[10]||(s[10]=p(" 批量删除"))])),_:1},8,["disabled","loading"])):u("",!0)])])]),n("div",H,[n("div",{ref_key:"table",ref:ie,class:"table-content"},[d(f,{class:"table",scroll:{y:l(re)},pagination:!1,"row-key":e=>e.id,size:"small",columns:ae,loading:ce.loading,"row-selection":de.value,"data-source":l(ne)},{bodyCell:m((({column:a,record:o})=>["action"===a.key?(r(),i("div",W,[!e.hasPerm("sys-role:edit")||o.defaultFlag&&1!==l(Q).adminType||-1!==["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_GUAN_LI_YUAN","YE_WU_GUAN_LI_JIAO_SE"].indexOf(o.code)?u("",!0):(r(),i("a",{key:0,onClick:e=>je("edit",o)},"编辑",8,X)),!e.hasPerm("sys-role:grant-menu")||o.defaultFlag&&1!==l(Q).adminType||-1!==["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_GUAN_LI_YUAN","YE_WU_GUAN_LI_JIAO_SE"].indexOf(o.code)?u("",!0):(r(),i("a",{key:1,onClick:e=>(e=>{_e.value.roleMenu(e)})(o)},"权限管理",8,B)),!e.hasPerm("sys-user:page-by-role")||o.defaultFlag&&1!==l(Q).adminType?u("",!0):(r(),i("a",{key:2,onClick:e=>(e=>{V.value.userListInit(e)})(o)},"查看",8,D)),-1===["XI_TONG_GUAN_LI_YUAN","RI_ZHI_GUAN_LI_YUAN","PU_TONG_GUAN_LI_YUAN","YE_WU_GUAN_LI_JIAO_SE"].indexOf(o.code)?(r(),_(t,{key:3,placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:e=>(e=>{N({...e,sysCategoryId:ee.sysCategoryId}).then((e=>{200===e.code?(k("success","角色删除成功"),me()):k("error","角色删除失败")}))})(o)},{title:m((()=>s[11]||(s[11]=[n("p",null,"确定要删除吗?",-1)]))),default:m((()=>[!e.hasPerm("sys-role:delete")||o.defaultFlag&&1!==l(Q).adminType?u("",!0):(r(),i("a",$,"删除"))])),_:2},1032,["onConfirm"])):u("",!0)])):u("",!0),"inSystem"===a.key?(r(),i(y,{key:1},[p(g(o.defaultFlag?"是":"否"),1)],64)):u("",!0)])),_:1},8,["scroll","row-key","columns","loading","row-selection","data-source"]),n("div",q,[l(ne).length>0?(r(),_(h,j({key:0},le.value,{onChange:te}),null,16)):u("",!0)])],512),d(A,{ref_key:"addEditFormRef",ref:ge,onOk:fe},null,512),d(b,{ref_key:"authMenuRef",ref:_e,onOk:fe},null,512),d(w,{ref_key:"checkRoleFormRef",ref:V,onOk:fe},null,512)])])])}}}),[["__scopeId","data-v-5ac820c5"]]);export{Q as default};
