import{d as e,r as s,f as a,b as t,o as l,e as o,c as i,aa as r,F as n,ag as d,a9 as c,ab as p,ad as m,u,E as v,a5 as f,y as j,G as y,J as g}from"./@vue-HScy-mz9.js";import{i as h}from"./no-data-DShY7eqz.js";import{b,d as k}from"./main-Djn9RDyT.js";import{b as x,c as w,e as I,f as C}from"./operationAnalysis-D3RTU-GI.js";import $ from"./UploadFile-CHJdpM2o.js";import{V as _,W as M,X as U,f as E,d as R,e as F,S as H}from"./ant-design-vue-DYY9BtJq.js";import{$ as S,aa as z}from"./@ant-design-CA72ad83.js";import{_ as N}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const A={class:"dependent-file"},P={class:"table-wrap"},T={class:"table-handle"},K={class:"table-content"},D={class:"collapse-wrap"},q={key:0},J={class:"collapse-header"},L={class:"collapse-header-name"},V={class:"opt-btns"},W=["onClick"],B={class:"card"},G={class:"collapse-table"},O={key:0,class:"file-url"},Q={class:"url"},X={class:"icon"},Y=["onClick"],Z={key:2,class:"table-actions"},ee=["onClick"],se={key:1,class:"no-data"},ae=N(e({__name:"Index",setup(e){const N=s([]),ae=s([]),te=s(!1),le=s(-1),oe=s(0),ie=()=>{setTimeout((()=>{oe.value-=1,oe.value>0&&ie()}),1e3)},re=()=>{I({}).then((e=>{200===e.code&&(N.value=e.data,ve(me.value))})).catch((e=>{b("error",`获取数据异常${e}`)}))},ne=s(),de=()=>{re()},ce=s(0),pe=s(!1),me=s(""),ue=e=>{e&&(me.value=e,ve(e))},ve=e=>{e&&(ae.value=N.value.filter((s=>s.id===e))[0].webAssetsVoList,ae.value.forEach((e=>(e.fullUrl=`${window.config.previewUrl}${e.assetsUnFileUrls}`,e.parseTime=fe(new Date(e.createTime),"yyyy-MM-dd HH:mm:ss"),e))))},fe=(e,s)=>{const a={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours()%12==0?12:e.getHours()%12,"H+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds(),a:e.getHours()<12?"上午":"下午",A:e.getHours()<12?"AM":"PM"};/(y+)/.test(s)&&(s=s.replace(RegExp.$1,`${e.getFullYear()}`.substr(4-RegExp.$1.length)));for(const t in a)new RegExp(`(${t})`).test(s)&&(s=s.replace(RegExp.$1,1===RegExp.$1.length?a[t]:`00${a[t]}`.substr(`${a[t]}`.length)));return s},je=[{title:"文件名称",dataIndex:"assetsName",key:"assetsName",ellipsis:!0},{title:"文件地址",dataIndex:"fullUrl",key:"fullUrl",ellipsis:!0,width:560},{title:"上传时间",dataIndex:"parseTime",key:"parseTime",ellipsis:!0},{title:"上传者",dataIndex:"createUserName",key:"createUserName",ellipsis:!0},{title:"状态",dataIndex:"enableStatus",customRender:e=>"",width:100},{title:"操作",dataIndex:"action",width:130}];return a((()=>{re()})),(e,s)=>{const a=_,I=R,ve=F,fe=E,ye=U,ge=M,he=H;return l(),t("div",A,[o("div",P,[o("div",T,[i(a,{class:"handle-alart",message:"依赖文件类型在【运营控制台】>【系统管理】>【字典管理】页面下的“web资源”配置中维护",banner:""})]),o("div",K,[o("div",D,[i(he,{spinning:pe.value},{default:r((()=>[N.value.length>0?(l(),t("div",q,[(l(!0),t(n,null,d(N.value,(a=>(l(),c(ge,{key:a.code,activeKey:me.value,"onUpdate:activeKey":s[0]||(s[0]=e=>me.value=e),class:"collapse-content",bordered:!0,accordion:!0,onChange:ue},{expandIcon:r((({isActive:e})=>[i(u(z),{rotate:e?90:0},null,8,["rotate"])])),default:r((()=>[(l(),c(ye,{key:a.id},{header:r((()=>[o("div",J,[o("div",L,m(a.code),1),o("div",V,[e.hasPerm("sys-file-info:upload")&&e.hasPerm("web-assets:add")?(l(),t("a",{key:0,class:"opt-btn",size:"small",onClick:v((e=>(e=>{ne.value.init(e)})(a)),["stop"])}," 上传新版本",8,W)):p("",!0)])])])),default:r((()=>[o("div",B,[o("div",G,[(l(),c(fe,{ref_for:!0,ref:"table",key:ce.value,class:"table",size:"small",pagination:!1,"default-expand-all-rows":!1,columns:je,"data-source":ae.value,loading:a.loading,scroll:{y:400}},{bodyCell:r((({column:a,record:d})=>["fullUrl"===a.dataIndex?(l(),t("div",O,[i(I,{title:d[a.dataIndex],overlayStyle:{"max-height":"200px",overflow:"auto"}},{default:r((()=>[o("div",Q,m(d[a.dataIndex]),1)])),_:2},1032,["title"]),o("div",X,[i(I,{title:"点击复制文件地址"},{default:r((()=>[i(u(S),{style:{color:"var(--theme-color)"},onClick:v((e=>(e=>{const s=document.createElement("input");s.value=e,document.body.appendChild(s),s.select(),document.execCommand("Copy"),b("success","已复制文件地址"),s.remove()})(d[a.dataIndex])),["stop"])},null,8,["onClick"])])),_:2},1024)])])):p("",!0),"enableStatus"===a.dataIndex?(l(),t(n,{key:1},[e.hasPerm("web-assets:update-status")&&"0"===d.enableStatus?(l(),t("a",{key:0,type:"text",onClick:e=>(async e=>{try{200===(await C({id:e.id,enableStatus:1,assetsName:e.assetsName,assetsFileId:e.assetsFileId,assetsCode:e.assetsCode})).code?(b("success","资源启用成功"),re()):b("error","资源启用失败")}finally{}})(d)},"启用",8,Y)):p("",!0)],64)):p("",!0),"action"===a.dataIndex?(l(),t("div",Z,[e.hasPerm("web-assets:delete")?(l(),c(ve,{key:0,placement:"topRight",title:"确认删除？",onConfirm:()=>(e=>{x({id:e.id}).then((e=>{e.success?(b("success","资源删除成功"),re()):b("error",`删除失败：${e.message}`)})).catch((e=>{b("error",`删除错误：${e.message}`)}))})(d)},{default:r((()=>s[1]||(s[1]=[o("a",{type:"text"},"删除",-1)]))),_:2},1032,["onConfirm"])):p("",!0),e.hasPerm("sys-file-info:download")?(l(),t("a",{key:1,class:f(le.value===d.id&&0!==oe.value?"disablecls":""),onClick:e=>(e=>{0===oe.value&&(le.value=e.id,oe.value=3,ie(),te.value=!0,w({id:e.assetsFileId}).then((e=>{k(e),te.value=!1})).catch((()=>{te.value=!1})))})(d)},[j(o("span",null,m(0===oe.value?"":`${oe.value}s `),513),[[y,le.value===d.id]]),s[2]||(s[2]=o("span",{class:"text"},"下载",-1))],10,ee)):p("",!0)])):p("",!0)])),_:2},1032,["data-source","loading"]))])])])),_:2},1024))])),_:2},1032,["activeKey"])))),128))])):(l(),t("div",se,s[3]||(s[3]=[o("img",{src:h,alt:"no-data"},null,-1),g(" 暂无数据 ")])))])),_:1},8,["spinning"])])])]),i($,{ref_key:"uploadFileRef",ref:ne,onOk:de},null,512)])}}}),[["__scopeId","data-v-d5b4f6cf"]]);export{ae as default};
