import{d as e,r as a,w as t,f as s,b as l,e as i,c as o,F as n,ag as r,a9 as d,ab as c,ae as p,aa as u,u as m,a7 as v,ad as y,am as k,o as g,J as j,a4 as f,E as h}from"./@vue-HScy-mz9.js";import{c as w}from"./clipboard-Dv7Qpqbb.js";import x from"./Nodata-mmdoiDH6.js";import C from"./PublishProject-CTg0PqB0.js";import _ from"./AddProject-SCEWZ0UE.js";import b from"./DownLoadPaksList-Yx_9AsML.js";import I from"./AddAndEditForm-Bbw4rS_w.js";import{v as T,l as P,P as z,k as S,b as $,D as E,m as N,Q as O}from"./main-Djn9RDyT.js";import{u as L}from"./useTableScrollY-DAiBD3Av.js";import R from"./EditProjectDeveloper-DjAmz1qY.js";import{a as D,d as A,g as U}from"./operationAnalysis-D3RTU-GI.js";import{u as F}from"./vue3-cookies-D4wQmYyh.js";import{u as J}from"./vue-router-BEwRlUkF.js";import{I as M,S as W,B as Y,f as B,g as K,r as Q,q,s as X,k as G,d as V,D as Z,u as H,v as ee,e as ae}from"./ant-design-vue-DYY9BtJq.js";import{S as te}from"./@ant-design-CA72ad83.js";import{_ as se}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./dayjs-CA7qlNSr.js";import"./projectGallery-xT8wgNPG.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./Map.vue_vue_type_style_index_0_lang-CZrcyiOo.js";import"./UserList-B68ng9ko.js";const le={class:"project-list"},ie={class:"tag-search"},oe={key:0,class:"tag-content"},ne=["onClick"],re={key:1,class:"tag-item"},de={key:1,class:"no-tag-content"},ce={class:"page-wrap"},pe={class:"project-list"},ue={class:"search-wrap"},me={class:"btn-wrap"},ve={class:"table-wrap"},ye=["src"],ke={class:"name"},ge={class:"name-text"},je=["onClick"],fe={key:1,class:"name"},he={class:"name-text"},we=["onClick"],xe={key:1,class:"tag-wrapper"},Ce={class:"tag-list"},_e={key:0,class:"edit-developer"},be={class:"all-content"},Ie={class:"text-truncate"},Te={key:1},Pe={key:3,class:"campus-used-wrap",style:{width:"100%"}},ze={key:4,class:"campus-used-wrap",style:{width:"100%"}},Se=["title"],$e={key:7,class:"campus-used-wrap"},Ee=["title"],Ne={key:9,class:"table-actions"},Oe=["onClick"],Le=["onClick"],Re=["onClick"],De=["onClick"],Ae={key:0},Ue=["onClick"],Fe={class:"ant-dropdown-link"},Je=["onClick"],Me=["onClick"],We={class:"pagination"},Ye=se(e({__name:"Index",emits:["ok"],setup(e,{emit:se}){const{cookies:Ye}=F(),Be=se,{params:Ke}=J(),{id:Qe}=Ke,qe=a({}),Xe=a([]),Ge=a(!0);Ge.value=!0,Xe.value=[],T().then((e=>{Ge.value=!1,200===e.code?(e.data.length&&e.data.forEach((e=>{qe.value[e.id]=[]})),Xe.value=e.data||[]):Xe.value=[]})).catch((()=>{Ge.value=!1}));const Ve=a([]);(async()=>{try{const e=await P({code:"PROJECT_TYPE"});200===e.code&&(Ve.value=e.data)}catch(e){}})();t((()=>qe.value),(()=>{ka()}),{deep:!0}),a("");const Ze=a([]),He=a(""),ea=()=>{He.value=""},aa=a({}),ta=a();a(!0);const sa=e=>{A({id:e.id,installManageId:aa.value.id,downloadOption:1}).then((e=>{200===e.code&&$("success",e.message)}))};s((()=>{D({name:"",pageNo:1,pageSize:999}).then((e=>{Ze.value=e.data.rows}))}));const la=a(),ia=a(),oa=()=>{xa()},na=a(),ra=e=>{xa(),"add"===e&&Be("ok")},da=a(),ca=e=>e.userList&&e.userList.length?e.userList.map((e=>e.name)).join(","):"",pa=[{title:"项目名称",dataIndex:"name",ellipsis:!0,width:300},{title:"标签",dataIndex:"tags",width:180,minWidth:100},{title:"参与团队成员",dataIndex:"developer",width:180,minWidth:120},{title:"场景数",dataIndex:"sceneCount",width:140,minWidth:80},{title:"用户数",dataIndex:"userCount",width:140,minWidth:80},{title:"创建日期",dataIndex:"createTime",width:120,ellipsis:!0},{title:"是否涉密",dataIndex:"classified",width:80,ellipsis:!0},{title:"操作",dataIndex:"actions",width:300}],ua=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),ma=e=>{const a=Ve.value.find((a=>a.code===e));return a?a.value:"XXV项目"},va=(e,a)=>{if(!e)return"--";const t=new Date(e),s=t.getFullYear(),l=t.getMonth()+1,i=t.getDate();return`${s}-${l<10?`0${l}`:l}-${i<10?`0${i}`:i}`},ya=(e,a)=>{ua.value=Object.assign(ua.value,{current:e,pageSize:a}),xa()},ka=()=>{ua.value.current=1,ua.value.pageSize=10,xa()},ga=a(),{scrollY:ja}=L(ga),fa=a({name:null}),ha=a(!1),wa=a([]),xa=()=>{var e;wa.value=[],ha.value=!0;const a=(null==(e=Object.values(qe.value))?void 0:e.flat(Infinity))||[];z({...fa.value,enterpriseId:Qe,pageNo:ua.value.current,pageSize:ua.value.pageSize,tagIds:a}).then((e=>{if(ha.value=!1,200===e.code){const{rows:a,pageNo:t,pageSize:s,totalRows:l}=e.data;wa.value=a,ua.value.current=t,ua.value.pageSize=s,ua.value.total=l}})).catch((()=>{ha.value=!1}))},Ca=async e=>{try{await w.copy(e),$("success","项目编码已复制到剪贴板！")}catch(a){}};return(e,a)=>{var t;const s=q,w=Q,T=W,P=M,z=Y,L=k("CopyOutlined"),D=X,A=k("FormOutlined"),F=G,J=V,se=k("DownOutlined"),Ke=ae,Qe=ee,Ze=H,He=Z,aa=B,_a=K;return g(),l("div",le,[i("div",ie,[(null==(t=Xe.value)?void 0:t.length)?(g(),l("div",oe,[(g(!0),l(n,null,r(Xe.value,(e=>{var a,t;return g(),l("div",{key:e.id,class:"tag-group"},[(null==(a=null==e?void 0:e.tags)?void 0:a.length)?(g(),l("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const t=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(qe.value[e.id])===JSON.stringify(t)?qe.value[e.id]=[]:qe.value[e.id]=e.tags.map((e=>e.id))})(e)},y(e.groupName),9,ne)):c("",!0),(null==(t=null==e?void 0:e.tags)?void 0:t.length)?(g(),l("div",re,[o(w,{value:qe.value[e.id],"onUpdate:value":a=>qe.value[e.id]=a,style:{width:"100%"}},{default:u((()=>[(g(!0),l(n,null,r(e.tags,(e=>(g(),l("div",{key:e.id,class:"tag-item-name"},[o(s,{value:e.id},{default:u((()=>[j(y(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):c("",!0)])})),128))])):(g(),l("div",de,[Ge.value?(g(),d(T,{key:0,class:"loading-icon",spinning:Ge.value},null,8,["spinning"])):c("",!0),Ge.value?c("",!0):(g(),d(x,{key:1,title:"请绑定标签"}))]))]),i("div",ce,[i("div",pe,[i("div",ue,[o(P,{value:fa.value.name,"onUpdate:value":a[1]||(a[1]=e=>fa.value.name=e),valueModifiers:{trim:!0},class:"filter",placeholder:"请输入名称查询","allow-clear":!0,onKeyup:a[2]||(a[2]=p((e=>xa()),["enter"]))},{suffix:u((()=>[o(m(te),{onClick:a[0]||(a[0]=e=>ka())})])),_:1},8,["value"]),i("div",me,[e.hasPerm("sys-project:add")?(g(),d(z,{key:0,type:"primary",class:"handle-btn",onClick:a[3]||(a[3]=e=>{return a="add",void na.value.init(a);var a})},{default:u((()=>a[4]||(a[4]=[j(" 新建项目 ")]))),_:1})):c("",!0)])]),i("div",ve,[i("div",{ref_key:"table1",ref:ga,class:"table-content"},[e.hasPerm("sys-project:page")?(g(),d(aa,{key:0,class:"table",scroll:{y:m(ja)},pagination:!1,size:"small",loading:ha.value,"row-key":e=>e.id,columns:pa,"data-source":wa.value,onChange:ya},{bodyCell:u((({column:t,record:s})=>["name"===t.dataIndex?(g(),l(n,{key:0},[s.previewUrl?(g(),d(D,{key:0},{content:u((()=>{return[i("img",{class:"pre-img",src:(e=s,e.previewUrl?`${window.config.previewUrl}${e.previewUrl}`:""),alt:""},null,8,ye)];var e})),default:u((()=>[i("div",ke,[i("span",ge,y(s.name),1),i("a",{class:"copy-btn",title:"复制项目编码",onClick:e=>Ca(s.code)},[o(L)],8,je)])])),_:2},1024)):(g(),l("div",fe,[i("span",he,y(s.name),1),i("a",{class:"copy-btn",title:"复制项目编码",onClick:e=>Ca(s.code)},[o(L)],8,we)]))],64)):c("",!0),"tags"===t.dataIndex?(g(),l("div",xe,[i("div",Ce,[(g(!0),l(n,null,r(s.functionExampleTags,((e,a)=>(g(),l("div",{key:a,class:"tag-item",style:f({backgroundColor:e.color})},y(e.tagName),5)))),128))])])):c("",!0),"developer"===t.dataIndex?(g(),l(n,{key:2},["1"===s.exampleType?(g(),l("div",_e,[o(D,{title:"",trigger:"hover",placement:"topLeft"},{content:u((()=>[i("div",be,y(ca(s)),1)])),default:u((()=>[i("div",Ie,y(ca(s)),1)])),_:2},1024),e.hasPerm("sys-project:add-edit-user")?(g(),d(A,{key:0,style:{"margin-top":"5px","margin-left":"5px"},onClick:h((e=>(e=>{da.value.init(e)})(s)),["stop"])},null,8,["onClick"])):c("",!0)])):(g(),l("div",Te,"-"))],64)):c("",!0),"sceneCount"===t.dataIndex?(g(),l("div",Pe,[o(F,{"stroke-color":m(S)(100*(s.scene||0)/s.sceneCount),showInfo:!1,percent:Number((100*(s.scene||0)/s.sceneCount).toFixed(2)),size:"small"},null,8,["stroke-color","percent"]),j(" "+y(`${s.scene||0}/${s.sceneCount}`),1)])):c("",!0),"userCount"===t.dataIndex?(g(),l("div",ze,[o(F,{"stroke-color":m(S)(100*(s.user||0)/s.userCount),showInfo:!1,percent:Number((100*(s.user||0)/s.userCount).toFixed(2)),size:"small"},null,8,["stroke-color","percent"]),j(" "+y(`${s.user||0}/${s.userCount}`),1)])):c("",!0),"createTime"===t.dataIndex?(g(),l("div",{key:5,class:"campus-used-wrap",title:va(s.createTime)},y(va(s.createTime)),9,Se)):c("",!0),"exampleType"===t.dataIndex?(g(),l(n,{key:6},[j(y(ma(s.exampleType)),1)],64)):c("",!0),"classified"===t.dataIndex?(g(),l("div",$e,y(1===s.classified?"是":"否"),1)):c("",!0),"licenseExpiredTime"===t.dataIndex?(g(),l("div",{key:8,class:"campus-used-wrap",title:va(s.licenseExpiredTime)},y(va(s.licenseExpiredTime)),9,Ee)):c("",!0),"actions"===t.dataIndex?(g(),l("div",Ne,[e.hasPerm("sys-project:edit")?(g(),l("a",{key:0,type:"text",onClick:e=>(e=>{na.value.init("edit",e)})(s)},"编辑",8,Oe)):c("",!0),e.hasPerm("sys-project:copy")&&"1"===s.exampleType?(g(),l("a",{key:1,type:"text",onClick:e=>(e=>{na.value.init("copy",e)})(s)},"复制",8,Le)):c("",!0),o(D,{style:{width:"500px"},title:"下载选项",trigger:"hover"},{content:u((()=>[i("div",null,[o(J,{title:"仅下载项目数据"},{default:u((()=>[e.hasPerm("sys-project:download-project")?(g(),l("a",{key:0,onClick:e=>(e=>{sa(e),ea()})(s)},"仅下载数据",8,Re)):c("",!0)])),_:2},1024),a[5]||(a[5]=j(" | ")),o(J,{title:"下载项目文件和安装包地址"},{default:u((()=>[e.hasPerm("sys-install-manage:page")?(g(),l("a",{key:0,onClick:e=>(e=>{ta.value.init(e.id),ea()})(s)},"仅下载文件和安装包",8,De)):c("",!0)])),_:2},1024)])])),default:u((()=>["1"===s.exampleType?(g(),l("a",Ae,"下载")):c("",!0)])),_:2},1024),e.hasPerm("sys-project:project-system")?(g(),l("a",{key:2,type:"text",onClick:e=>(async e=>{const a=e.exampleType;let t="";if("1"===a){let a="";const l=Ye.get("ACCESS_P"),i=l?E(l.substring(32),"",l.substring(0,32)):"";if(i)try{const t=await N({tenant:e.code,pwd:i.replaceAll('"',"")});200===t.code&&(a=t.data)}catch(s){}t=`/base/login?tenant=${e.code}&token=${a}&name=${e.name}`}else if("2"===a){const a=await U({projectId:e.id});200===a.code&&(t=a.data)}window.open(t,"_blank")})(s)},"进入项目",8,Ue)):c("",!0),o(He,null,{overlay:u((()=>[o(Ze,null,{default:u((()=>[o(Qe,null,{default:u((()=>[e.hasPerm("sys-project:delete")?(g(),d(Ke,{key:0,placement:"topRight",title:"确认删除？",onConfirm:()=>(e=>{O({id:e.id}).then((e=>{200===e.code?($("success","项目删除成功"),xa(),Be("ok")):$("error",e.message)}))})(s)},{default:u((()=>a[7]||(a[7]=[i("a",null,"删除",-1)]))),_:2},1032,["onConfirm"])):c("",!0)])),_:2},1024),"1"===s.exampleType&&e.hasPerm("sys-project:edit")?(g(),d(Qe,{key:0},{default:u((()=>[s.exampleId?c("",!0):(g(),l("a",{key:0,type:"text",onClick:e=>{return a=s,void ia.value.init(a);var a}},"发布案例",8,Je)),s.exampleId?(g(),l("a",{key:1,type:"text",onClick:e=>{return a=s,void la.value.init(a);var a}},"编辑案例",8,Me)):c("",!0)])),_:2},1024)):c("",!0)])),_:2},1024)])),default:u((()=>[i("a",Fe,[a[6]||(a[6]=j(" 更多 ")),o(se)])])),_:2},1024)])):c("",!0)])),expandedRowRender:u((({record:e})=>[i("div",null,"创建者："+y(e.createName),1),i("div",null,"项目编码："+y(e.code),1),i("div",null,"授权到期："+y(va(e.licenseExpiredTime)),1),i("div",null,"项目所属地："+y(e.formattedAddress||"-"),1)])),_:1},8,["scroll","loading","row-key","data-source"])):c("",!0),i("div",We,[wa.value.length>0?(g(),d(_a,v({key:0},ua.value,{onChange:ya}),null,16)):c("",!0)])],512)])])]),o(R,{ref_key:"editProjectDeveloperRef",ref:da,onOk:ra},null,512),o(I,{ref_key:"addEditFormRef",ref:na,onOk:ra,projectType:Ve.value},null,8,["projectType"]),o(b,{ref_key:"downLoadPaksListRef",ref:ta},null,512),o(_,{ref_key:"addProjectRef",ref:ia,onOk:oa},null,512),o(C,{ref_key:"editProjectRef",ref:la,onOk:oa},null,512)])}}}),[["__scopeId","data-v-3ccb86a8"]]);export{Ye as default};
