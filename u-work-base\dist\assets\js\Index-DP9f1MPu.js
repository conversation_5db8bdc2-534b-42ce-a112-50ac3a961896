import s from"./AuthMenu-B-dXiyP0.js";import{h as e}from"./department-CTxHSeTj.js";import{d as a,r as i,w as l,b as o,e as t,ab as r,c as v,ad as d,F as m,ag as p,J as n,o as c,n as u}from"./@vue-HScy-mz9.js";import{_ as j}from"./vue-qr-CB2aNKv5.js";import"./role-OrQ5c9FV.js";import"./main-Djn9RDyT.js";import"./ant-design-vue-DYY9BtJq.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./js-binary-schema-parser-G48GG52R.js";const y={class:"basic-information"},g={class:"user"},h={class:"val"},f={class:"user"},b={class:"val"},C={class:"user margin-flag"},k={key:0},w=["onClick"],_={key:1},N={class:"user"},z={class:"val"},D={class:"user"},L={class:"val"},P={class:"user"},x={class:"val"},M={class:"user margin-flag"},A={class:"val"},T={key:0,class:"title"},$={key:1},E={class:"user"},I={class:"val"},J={class:"user"},O={class:"val"},U={class:"user user-phone"},V={class:"val"},q=j(a({__name:"Index",props:{data:{type:Object,default:()=>{}}},setup(a){const j=a,q=i({}),B=i(""),F=i([]),K=i("-"),R=i([]);l((()=>j.data),(s=>{if((async()=>{const s=await e();200===s.code&&(R.value=s.data)})(),q.value=s,q.value.sysUserDetailVo){const{extDeptPos:s,roles:e,deptName:a}=q.value.sysUserDetailVo,i=s.map((s=>s.deptName))||[];B.value=i.toString(","),a&&""!==a&&(B.value=`${a},${B.value}`),u((()=>{S(e)}))}0===q.value.thirdPartyLogin?K.value="系统用户":1===q.value.thirdPartyLogin?K.value="LDAP":K.value="-"}),{immediate:!0});const S=s=>{if(s){const e=[];s.forEach((s=>{const a=e.find((e=>e.sysCategoryCode===s.sysCategoryCode));if(a)a.childs.push(s);else{const a={sysCategoryCode:s.sysCategoryCode,sysCategoryName:s.sysCategoryName,childs:[s]};e.push(a)}})),F.value=e.filter((s=>s.sysCategoryCode))}},Z=i();return(e,a)=>(c(),o("div",y,[a[11]||(a[11]=t("div",{class:"title"},"账号信息",-1)),t("div",g,[a[0]||(a[0]=t("div",{class:"lab"},"组织部门",-1)),t("div",h,d(B.value||"-"),1)]),t("div",f,[a[1]||(a[1]=t("div",{class:"lab"},"账号(登录专用)",-1)),t("div",b,d(q.value.account||"-"),1)]),t("div",C,[a[3]||(a[3]=t("div",{class:"lab"},"账号类型",-1)),F.value.length>0?(c(),o("div",k,[(c(!0),o(m,null,p(F.value,((s,e)=>(c(),o("div",{key:e,class:"dep"},[t("span",null,d(s.sysCategoryName),1),a[2]||(a[2]=n(" > ")),(c(!0),o(m,null,p(s.childs,((e,a)=>(c(),o("a",{key:a,onClick:s=>{return a=e,void Z.value.roleMenu(a);var a}},d(e.name)+d(a!==s.childs.length-1?"、":""),9,w)))),128))])))),128))])):(c(),o("div",_,"-"))]),t("div",N,[a[4]||(a[4]=t("div",{class:"lab"},"手机号码",-1)),t("div",z,d(q.value.phone||"-"),1)]),t("div",D,[a[5]||(a[5]=t("div",{class:"lab"},"邮箱",-1)),t("div",L,d(q.value.email||"-"),1)]),t("div",P,[a[6]||(a[6]=t("div",{class:"lab"},"注册时间",-1)),t("div",x,d(q.value.createTime||"-"),1)]),t("div",M,[a[7]||(a[7]=t("div",{class:"lab"},"上次登录时间",-1)),t("div",A,d(q.value.lastLoginTime||"-"),1)]),R.value.length?(c(),o("div",T,"团队基本信息")):r("",!0),R.value.length?(c(),o("div",$,[(c(!0),o(m,null,p(R.value,((s,e)=>(c(),o("div",{key:e,class:"enterprise-item"},[t("div",E,[a[8]||(a[8]=t("div",{class:"lab"},"团队名称",-1)),t("div",I,d(s.name||"-"),1)]),t("div",J,[a[9]||(a[9]=t("div",{class:"lab"},"联系人姓名",-1)),t("div",O,d(s.developer||"-"),1)]),t("div",U,[a[10]||(a[10]=t("div",{class:"lab"},"联系人手机",-1)),t("div",V,d(s.developerPhone||"-"),1)])])))),128))])):r("",!0),v(s,{ref_key:"authMenuRef",ref:Z},null,512)]))}}),[["__scopeId","data-v-56e19dc1"]]);export{q as default};
