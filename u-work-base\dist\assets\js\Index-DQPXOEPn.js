import{d as e,r as a,w as s,a as t,am as i,b as l,o,e as r,c as n,F as c,ag as u,ab as d,ad as p,aa as v,J as m,a9 as g,ae as f,y as h,G as k,a5 as j,u as y,a4 as w}from"./@vue-HScy-mz9.js";import{p as _}from"./projectGallery-DFHuwUAq.js";import b from"./Nodata-mmdoiDH6.js";import{d as S}from"./dayjs-CA7qlNSr.js";import C from"./DocPreview-BGAPIKZ0.js";import z from"./AddAndEdit-dIE5-YIA.js";import{c as x,g as N,r as T,d as P,b as I}from"./index-GENaTOlC.js";import{a as E,C as O,b as q}from"./main-Djn9RDyT.js";import{q as U,r as D,S as R,I as A,x as G,w as J,B as M,e as Y,d as $,g as H}from"./ant-design-vue-DYY9BtJq.js";import{_ as L}from"./vue-qr-CB2aNKv5.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./@vueup-CLVdhRgW.js";import"./quill-BBEhJLA6.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */const B={class:"sample-list"},K={class:"content-wrap"},F={class:"tag-search"},X={key:0,class:"tag-content"},Z=["onClick"],Q={key:1,class:"tag-item"},V={key:1,class:"no-tag-content"},W={class:"content-list"},ee={class:"search"},ae={class:"search-wrap"},se={key:0,class:"list"},te={class:"contain"},ie={class:"img-box"},le={class:"img-item"},oe=["src"],re={class:"bottom-wrapper"},ne={class:"bottom-content"},ce={class:"time"},ue={class:"hover-box"},de=["onClick"],pe=["onClick"],ve=["onClick"],me={class:"control-icon"},ge={class:"item-bottom"},fe={class:"title"},he=["title"],ke=["title"],je={class:"tag-wrapper"},ye=["id"],we=["title"],_e={key:1,class:"list"},be={class:"pagination-box"},Se=L(e({__name:"Index",setup(e){const L=E(),Se=a(sessionStorage.getItem("XI_TONG_LOGO")||_),Ce=a(),ze=a(),xe=()=>{ze.value.init("add",null)},Ne=()=>{Re()},Te=a(!0),Pe=a({}),Ie=a([]);s((()=>Pe.value),(()=>{De(1,12)}),{deep:!0});const Ee=a(!1),Oe=a(!0);Oe.value=!0,x().then((e=>{Oe.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Pe.value[e.id]=[]})):Pe.value={},Ie.value=e.data||[]):Ie.value=[]})).catch((()=>{Pe.value={},Ie.value=[],Oe.value=!1}));const qe=a([]),Ue=t({total:0,current:1,pageSize:12,name:"",source:1,kind:2,checkedType:-1}),De=(e,a)=>{Ue.current=e,Ue.pageSize=a,Re()},Re=()=>{var e;Te.value=!0;const a=(null==(e=Object.values(Pe.value))?void 0:e.flat(Infinity))||[],s={name:Ue.name,pageNo:Ue.current,pageSize:Ue.pageSize,kind:2,tagId:a.join(","),owner:!1,reviewStatus:0};2===Ue.source&&(s.owner=!0,-1===Ue.checkedType?delete s.reviewStatus:s.reviewStatus=Ue.checkedType),N(s).then((e=>{Te.value=!1,200===e.code&&(Ue.total=e.data.totalRows,qe.value=e.data.rows||[])})).catch((()=>{Te.value=!1}))},Ae=e=>`${window.config.previewEffectUrl}${e.code}/preview.png?width=400&time=`+e.updateTime,Ge=e=>{let a="";return 1===e?a="未提交":3===e?a="待审批":4===e&&(a="未通过"),a};return(e,a)=>{var s;const t=U,_=D,x=R,N=i("search-outlined"),E=A,Je=G,Me=J,Ye=M,$e=Y,He=i("exception-outlined"),Le=$,Be=i("file-word-outlined"),Ke=i("download-outlined"),Fe=H;return o(),l("div",B,[r("div",K,[r("div",F,[(null==(s=Ie.value)?void 0:s.length)?(o(),l("div",X,[(o(!0),l(c,null,u(Ie.value,(e=>{var a,s;return o(),l("div",{key:e.id,class:"tag-group"},[(null==(a=e.tags)?void 0:a.length)?(o(),l("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const s=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Pe.value[e.id])===JSON.stringify(s)?Pe.value[e.id]=[]:Pe.value[e.id]=e.tags.map((e=>e.id))})(e)},p(e.groupName),9,Z)):d("",!0),(null==(s=e.tags)?void 0:s.length)?(o(),l("div",Q,[n(_,{value:Pe.value[e.id],"onUpdate:value":a=>Pe.value[e.id]=a,style:{width:"100%"}},{default:v((()=>[(o(!0),l(c,null,u(e.tags,(e=>(o(),l("div",{key:e.id,class:"tag-item-name"},[n(t,{value:e.id},{default:v((()=>[m(p(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):d("",!0)])})),128))])):(o(),l("div",V,[Oe.value?(o(),g(x,{key:0,class:"loading-icon",spinning:Oe.value},null,8,["spinning"])):d("",!0),Oe.value?d("",!0):(o(),g(b,{key:1,title:"请绑定标签"}))]))]),r("div",W,[r("div",ee,[r("div",ae,[n(E,{value:Ue.name,"onUpdate:value":a[1]||(a[1]=e=>Ue.name=e),valueModifiers:{trim:!0},class:"search-wrapper",placeholder:"搜索",onKeyup:a[2]||(a[2]=f((e=>De(1,12)),["enter"]))},{suffix:v((()=>[n(N,{style:{cursor:"pointer"},onClick:a[0]||(a[0]=e=>De(1,12))})])),_:1},8,["value"]),n(Me,{value:Ue.source,"onUpdate:value":a[3]||(a[3]=e=>Ue.source=e),placeholder:"请选择数据源",class:"search-select",onChange:a[4]||(a[4]=e=>(Ue.checkedType=-1,void De(1,12)))},{default:v((()=>[n(Je,{value:1},{default:v((()=>a[9]||(a[9]=[m("公共资源")]))),_:1}),n(Je,{value:2},{default:v((()=>a[10]||(a[10]=[m("我的资源")]))),_:1})])),_:1},8,["value"]),2===Ue.source?(o(),g(Me,{key:0,value:Ue.checkedType,"onUpdate:value":a[5]||(a[5]=e=>Ue.checkedType=e),placeholder:"请选择状态",class:"search-select",onChange:a[6]||(a[6]=e=>De(1,12))},{default:v((()=>[n(Je,{value:-1},{default:v((()=>a[11]||(a[11]=[m("全部")]))),_:1}),n(Je,{value:1},{default:v((()=>a[12]||(a[12]=[m("未提交")]))),_:1}),n(Je,{value:3},{default:v((()=>a[13]||(a[13]=[m("待审批")]))),_:1}),n(Je,{value:0},{default:v((()=>a[14]||(a[14]=[m("审批通过")]))),_:1}),n(Je,{value:4},{default:v((()=>a[15]||(a[15]=[m("审批不通过")]))),_:1})])),_:1},8,["value"])):d("",!0),n(Ye,{type:"primary",class:"search-btn",style:{"margin-left":"20px"},onClick:a[7]||(a[7]=e=>De(1,12))},{default:v((()=>a[16]||(a[16]=[m(" 查询 ")]))),_:1})]),e.hasPerm("effect-package:upload")?(o(),g(Ye,{key:0,type:"primary",class:"handle-btn",onClick:xe},{default:v((()=>a[17]||(a[17]=[m(" 新增效果包 ")]))),_:1})):d("",!0)]),e.hasPerm("effect-package:page")?(o(),l("div",se,[(o(!0),l(c,null,u(qe.value,(s=>h((o(),l("div",{key:s.id,class:"item"},[r("div",te,[r("div",ie,[r("div",le,[r("img",{src:Ae(s),alt:"图片",class:"img",onError:a[8]||(a[8]=e=>(e.target.src=Se.value,e.target.style.width="auto"))},null,40,oe)]),r("div",re,[r("div",ne,[2===Ue.source?(o(),l("div",{key:0,class:j(["status",{fail:4===s.reviewStatus,check:3===s.reviewStatus,unpush:1===s.reviewStatus}])},p(Ge(s.reviewStatus)),3)):d("",!0),r("div",ce,"上传时间："+p(y(S)(s.createTime).format("YYYY-MM-DD HH:mm:ss")||""),1)])]),r("div",ue,[r("div",{class:"btn",onClick:e=>(e=>{const{href:a}=O.resolve({path:"/preview/campusEffectsPreview",query:{code:e.code,name:e.name,id:2===Ue.source||1===L.adminType?e.id:""}});window.open(a,"_blank")})(s)},"预览",8,de),!e.hasPerm("effect-package:edit")||2!==Ue.source||1!==s.reviewStatus&&4!==s.reviewStatus?d("",!0):(o(),l("div",{key:0,class:"btn perview",onClick:e=>(e=>{ze.value.init("edit",e)})(s)},"编辑",8,pe)),e.hasPerm("effect-package:delete")&&2===Ue.source&&4===s.reviewStatus?(o(),g($e,{key:1,placement:"topRight",title:"确认删除？",onConfirm:e=>(async e=>{const a=await I({id:e.id});200===a.code?(q("success","效果包删除成功"),Re()):q("error",a.message)})(s)},{default:v((()=>a[18]||(a[18]=[r("div",{class:"btn perview"},"删除",-1)]))),_:2},1032,["onConfirm"])):d("",!0),2!==Ue.source||1!==s.reviewStatus&&4!==s.reviewStatus||!e.hasPerm("effect-package:review")?d("",!0):(o(),l("div",{key:2,class:"btn perview",onClick:e=>(e=>{T({reviewStatus:3,id:e.id}).then((e=>{200===e.code?(q("success","提交成功，请等待管理员审批"),Re()):q("error",e.message)}))})(s)}," 提交审批 ",8,ve)),r("div",me,[n(Le,{placement:"top"},{title:v((()=>[r("span",null,p(s.reviewDescribe),1)])),default:v((()=>[4===s.reviewStatus?(o(),g(He,{key:0,title:"未通过原因",style:{"margin-right":"5px"}})):d("",!0)])),_:2},1024),n(Be,{title:"效果包说明",style:{"margin-right":"5px"},onClick:e=>(e=>{Ce.value.init(e)})(s.remark)},null,8,["onClick"]),e.hasPerm("effect-package:download")?(o(),g(Ke,{key:0,title:"下载",onClick:e=>(e=>{Ee.value||(Ee.value=!0,P({code:e.code,kind:2,name:e.name}).then((e=>{Ee.value=!1,200===e.code?q("success","后台打包效果包中，这可能需要一段时间，现在您可以进行其他操作，完成后将通知您！"):q("error",e.message)})).catch((()=>{Ee.value=!1})))})(s)},null,8,["onClick"])):d("",!0)])])]),r("div",ge,[r("div",fe,[r("div",{class:"name",title:s.name},p(s.name),9,he),r("div",{class:"user",title:s.createName},p(s.createName),9,ke)]),r("div",je,[r("div",{id:s.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(o(!0),l(c,null,u(s.tags,((e,a)=>(o(),l("div",{key:a,title:e.tagName,class:"tag-item",style:w({backgroundColor:e.color})},p(e.tagName),13,we)))),128))],8,ye)])])])])),[[k,!Te.value&&qe.value.length]]))),128)),Te.value?(o(),g(x,{key:0,class:"loading-icon",spinning:Te.value},null,8,["spinning"])):d("",!0),Te.value||qe.value.length?d("",!0):(o(),g(b,{key:1}))])):(o(),l("div",_e,[n(b,{title:"暂无权限"})])),r("div",be,[n(Fe,{total:Ue.total,"page-size-options":["12","20","30","40"],current:Ue.current,"page-size":Ue.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:De},null,8,["total","current","page-size","show-total"])])])]),n(C,{ref_key:"docPreviewRef",ref:Ce},null,512),n(z,{ref_key:"AddExampleRef",ref:ze,onOk:Ne},null,512)])}}}),[["__scopeId","data-v-30986ee1"]]);export{Se as default};
