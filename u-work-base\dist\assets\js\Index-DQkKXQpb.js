import{c as e,a as o,u as s,I as i,J as r,K as a,C as t,b as m}from"./main-Djn9RDyT.js";import{g as n}from"./pluginManage-DfKZgXJX.js";import{u as p}from"./vue3-cookies-D4wQmYyh.js";import{m as l}from"./ant-design-vue-DYY9BtJq.js";import{d as u,r as d,p as j,w as c,f as v,j as g,b as f,a9 as w,ab as y,u as h,c as E,am as _,o as C,n as I}from"./@vue-HScy-mz9.js";import{_ as M}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const b={class:"micro-wrap"},S={key:0,class:"micro-wrap"},T=["src"],k={key:1,class:"empty"},N=M(u({__name:"Index",setup(u){const{cookies:M}=p(),{afterMount:N}=r,A=e(),z=o(),K=s(),L=d(null),P=window.config.previewUrl,x=j((()=>{var e;return null==(e=z.menuData)?void 0:e.microAppId}));c((()=>x),(e=>{e&&I((()=>{D()}))}),{immediate:!0});const U=d({token:M.get("ACCESS_TOKEN_U"),modeName:A.modeName,themeColor:A.themeColor,adminType:z.adminType,permissions:z.userPermissions,userInfo:K.userInfo}),D=()=>{n({id:x.value}).then((e=>{200===e.code?(L.value=e.data,L.value&&0===L.value.integrationMode&&O()):m("error",e.message)}))},O=()=>{const e=document.getElementById("micro-app-iframe");e.onload=function(){e.contentWindow.postMessage({token:M.get("ACCESS_TOKEN_U"),modeName:A.modeName,themeColor:A.themeColor,adminType:z.adminType,permissions:z.userPermissions,userInfo:K.userInfo},"*")},window.addEventListener("message",$,!1)},$=e=>{e.data.mainToLogin&&(M.remove("ACCESS_TOKEN_U","/",window.location.hostname),M.remove("ACCESS_P","/",window.location.hostname),t.push("/login"))};return v((()=>{i()})),g((()=>{window.removeEventListener("message",$)})),(e,o)=>{const s=_("WujieVue");return C(),f("div",b,[L.value?(C(),f("div",S,[1===L.value.integrationMode?(C(),w(s,{key:0,plugins:h(a),props:U.value,afterMount:h(N),width:"100%",height:"100%",name:L.value.code,url:`${h(P)}${L.value.url}/index.html`},null,8,["plugins","props","afterMount","name","url"])):y("",!0),0===L.value.integrationMode?(C(),f("iframe",{key:1,id:"micro-app-iframe",src:L.value.url,frameborder:"0",class:"micro-app"},null,8,T)):y("",!0)])):(C(),f("div",k,[E(h(l),{image:h(l).PRESENTED_IMAGE_SIMPLE,description:"该子应用已停用或删除",class:"emptycls"},null,8,["image"])]))])}}}),[["__scopeId","data-v-945eb055"]]);export{N as default};
