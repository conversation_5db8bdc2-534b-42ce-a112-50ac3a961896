import{_ as a}from"./vue-qr-CB2aNKv5.js";import{b as c,o as h,aU as e}from"./@vue-HScy-mz9.js";import"./js-binary-schema-parser-G48GG52R.js";const t={class:"sdk-introduce"};const s=a({},[["render",function(a,s){return h(),c("div",t,s[0]||(s[0]=[e("<h2 data-v-4ce14cb6>简介</h2><h3 data-v-4ce14cb6>Thingjs SDK是面向数字孪生项目的API工具包，快速构建数字孪生项目的工具和框架。帮助团队成员快速搭建项目，减少重复性工作，让初接触3D开发项目的开发能够快速上手，搭建自己的数字孪生项目。</h3><h2 data-v-4ce14cb6>特性</h2><h3 data-v-4ce14cb6>1、基于业务的API</h3><h4 data-v-4ce14cb6>基于优锘自研Thingjs引擎，结合十多年数字孪生项目经验，封装了80多个标准API接口，满足数字孪生项目开发要求</h4><h3 data-v-4ce14cb6>2、数据管理</h3><h4 data-v-4ce14cb6>提供数据管理，对地图管理、场景管理、孪生体管理、点位摆放等功能管理，使用人员可以基于业务灵活创建及管理</h4><h3 data-v-4ce14cb6>3、开发生态</h3><h4 data-v-4ce14cb6>配套提供不同行业功能示例、效果模版、功能组件、图表组件，团队成员可以直接复制使用，提升项目效率</h4><h3 data-v-4ce14cb6>4、项目脚手架</h3><h4 data-v-4ce14cb6>提供完整项目脚手架，封装复杂业务实现逻辑，对外提供简单调用接口，易上手的同时提升项目效率</h4>",11)]))}],["__scopeId","data-v-4ce14cb6"]]);export{s as default};
