import{d as e,r as a,w as s,a as t,am as l,b as i,o,e as r,c as n,F as u,ag as c,ab as d,ad as p,aa as m,J as v,a9 as g,ae as y,y as h,G as f,a5 as j,u as k,a4 as w}from"./@vue-HScy-mz9.js";import{p as _}from"./projectGallery-DFHuwUAq.js";import b from"./Nodata-mmdoiDH6.js";import{d as C}from"./dayjs-CA7qlNSr.js";import z from"./DocPreview-DwjNvfVR.js";import x from"./AddExample-BRazsmO1.js";import{s as N,g as S,a as P}from"./sample-BW6eK2vn.js";import{b as T,d as I}from"./examples-Cf8gesNV.js";import{C as O,b as E}from"./main-Djn9RDyT.js";import R from"./Detail-DFP5xywW.js";import{q as U,r as q,S as D,I as $,x as G,w as J,B as M,e as Y,d as A,g as H}from"./ant-design-vue-DYY9BtJq.js";import{_ as L}from"./vue-qr-CB2aNKv5.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./sample-zip-Cn1wBsZQ.js";import"./@vueup-CLVdhRgW.js";import"./quill-BBEhJLA6.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */const B={class:"sample-list"},K={class:"content-wrap"},F={class:"tag-search"},X={key:0,class:"tag-content"},Z=["onClick"],Q={key:1,class:"tag-item"},V={key:1,class:"no-tag-content"},W={class:"content-list"},ee={class:"search"},ae={class:"search-wrap"},se={key:0,class:"list"},te={class:"contain"},le={class:"img-box"},ie={class:"img-item"},oe=["src"],re={class:"bottom-wrapper"},ne={class:"bottom-content"},ue={class:"time"},ce={class:"hover-box"},de=["onClick"],pe=["onClick"],me={key:0,class:"btn perview"},ve=["onClick"],ge={class:"control-icon"},ye={class:"item-bottom"},he={class:"title"},fe=["title"],je=["title"],ke={class:"tag-wrapper"},we=["id"],_e=["title"],be={key:1,class:"list"},Ce={class:"pagination-box"},ze=L(e({__name:"Index",setup(e){const L=a(sessionStorage.getItem("XI_TONG_LOGO")||_),ze=a(),xe=a(),Ne=()=>{xe.value.init("add",null)},Se=()=>{De()},Pe=a(!0),Te=a({}),Ie=a([]);s((()=>Te.value),(()=>{qe(1,12)}),{deep:!0});const Oe=a(!0);Oe.value=!0,N().then((e=>{Oe.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Te.value[e.id]=[]})):Te.value={},Ie.value=e.data||[]):Ie.value=[]})).catch((()=>{Te.value={},Ie.value=[],Oe.value=!1}));const Ee=a(),Re=a([]),Ue=t({total:0,current:1,pageSize:12,name:"",source:1,checkedType:-1}),qe=(e,a)=>{Ue.current=e,Ue.pageSize=a,De()},De=()=>{var e;Pe.value=!0;const a=(null==(e=Object.values(Te.value))?void 0:e.flat(Infinity))||[],s={name:Ue.name,pageNo:Ue.current,pageSize:Ue.pageSize,tagId:a,status:0};1===Ue.source?S(s).then((e=>{Pe.value=!1,200===e.code&&(Ue.total=e.data.totalRows,Re.value=e.data.rows||[])})).catch((()=>{Pe.value=!1})):2===Ue.source&&(-1!==Ue.checkedType?s.status=Ue.checkedType:s.status=null,P(s).then((e=>{Pe.value=!1,200===e.code&&(Ue.total=e.data.totalRows,Re.value=e.data.rows||[])})).catch((()=>{Pe.value=!1})))},$e=e=>`${window.config.previewUrl}${e.previewUrl}?width=400`,Ge=e=>{let a="";return 1===e?a="未提交":3===e?a="待审批":4===e&&(a="未通过"),a};return(e,a)=>{var s;const t=U,_=q,N=D,S=l("search-outlined"),P=$,Je=G,Me=J,Ye=M,Ae=Y,He=l("exception-outlined"),Le=A,Be=l("file-word-outlined"),Ke=H;return o(),i("div",B,[r("div",K,[r("div",F,[(null==(s=Ie.value)?void 0:s.length)?(o(),i("div",X,[(o(!0),i(u,null,c(Ie.value,(e=>{var a,s;return o(),i("div",{key:e.id,class:"tag-group"},[(null==(a=e.tags)?void 0:a.length)?(o(),i("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const s=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Te.value[e.id])===JSON.stringify(s)?Te.value[e.id]=[]:Te.value[e.id]=e.tags.map((e=>e.id))})(e)},p(e.groupName),9,Z)):d("",!0),(null==(s=e.tags)?void 0:s.length)?(o(),i("div",Q,[n(_,{value:Te.value[e.id],"onUpdate:value":a=>Te.value[e.id]=a,style:{width:"100%"}},{default:m((()=>[(o(!0),i(u,null,c(e.tags,(e=>(o(),i("div",{key:e.id,class:"tag-item-name"},[n(t,{value:e.id},{default:m((()=>[v(p(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):d("",!0)])})),128))])):(o(),i("div",V,[Oe.value?(o(),g(N,{key:0,class:"loading-icon",spinning:Oe.value},null,8,["spinning"])):d("",!0),Oe.value?d("",!0):(o(),g(b,{key:1,title:"请绑定标签"}))]))]),r("div",W,[r("div",ee,[r("div",ae,[n(P,{value:Ue.name,"onUpdate:value":a[1]||(a[1]=e=>Ue.name=e),valueModifiers:{trim:!0},class:"search-wrapper",placeholder:"搜索",onKeyup:a[2]||(a[2]=y((e=>qe(1,12)),["enter"]))},{suffix:m((()=>[n(S,{style:{cursor:"pointer"},onClick:a[0]||(a[0]=e=>qe(1,12))})])),_:1},8,["value"]),n(Me,{value:Ue.source,"onUpdate:value":a[3]||(a[3]=e=>Ue.source=e),placeholder:"请选择数据源",class:"search-select",onChange:a[4]||(a[4]=e=>(Ue.checkedType=-1,void qe(1,12)))},{default:m((()=>[n(Je,{value:1},{default:m((()=>a[9]||(a[9]=[v("公共资源")]))),_:1}),n(Je,{value:2},{default:m((()=>a[10]||(a[10]=[v("我的资源")]))),_:1})])),_:1},8,["value"]),2===Ue.source?(o(),g(Me,{key:0,value:Ue.checkedType,"onUpdate:value":a[5]||(a[5]=e=>Ue.checkedType=e),placeholder:"请选择状态",class:"search-select",onChange:a[6]||(a[6]=e=>qe(1,12))},{default:m((()=>[n(Je,{value:-1},{default:m((()=>a[11]||(a[11]=[v("全部")]))),_:1}),n(Je,{value:1},{default:m((()=>a[12]||(a[12]=[v("未提交")]))),_:1}),n(Je,{value:3},{default:m((()=>a[13]||(a[13]=[v("待审批")]))),_:1}),n(Je,{value:0},{default:m((()=>a[14]||(a[14]=[v("审批通过")]))),_:1}),n(Je,{value:4},{default:m((()=>a[15]||(a[15]=[v("审批不通过")]))),_:1})])),_:1},8,["value"])):d("",!0),n(Ye,{type:"primary",class:"search-btn",style:{"margin-left":"20px"},onClick:a[7]||(a[7]=e=>qe(1,12))},{default:m((()=>a[16]||(a[16]=[v(" 查询 ")]))),_:1})]),e.hasPerm("sys-sample:add")?(o(),g(Ye,{key:0,type:"primary",class:"handle-btn",onClick:Ne},{default:m((()=>a[17]||(a[17]=[v(" 新增功能示例 ")]))),_:1})):d("",!0)]),1===Ue.source&&e.hasPerm("sys-sample:page")||2===Ue.source&&e.hasPerm("sys-sample:mySamplePage")?(o(),i("div",se,[(o(!0),i(u,null,c(Re.value,(s=>h((o(),i("div",{key:s.id,class:"item"},[r("div",te,[r("div",le,[r("div",ie,[r("img",{src:$e(s),alt:"图片",class:"img",onError:a[8]||(a[8]=e=>(e.target.src=L.value,e.target.style.width="auto"))},null,40,oe)]),r("div",re,[r("div",ne,[2===Ue.source?(o(),i("div",{key:0,class:j(["status",{fail:4===s.status,check:3===s.status,unpush:1===s.status}])},p(Ge(s.status)),3)):d("",!0),r("div",ue,"上传时间："+p(k(C)(s.createTime).format("YYYY-MM-DD HH:mm:ss")||""),1)])]),r("div",ce,[e.hasPerm("sys-sample:detail")?(o(),i("div",{key:0,class:"btn",onClick:e=>(e=>{const{href:a}=O.resolve({path:"/portal/sampleOnlineEditor",query:{id:e.id}});window.open(a,"_blank")})(s)},"预览",8,de)):d("",!0),2!==Ue.source||1!==s.status&&4!==s.status&&3!==s.status||!e.hasPerm("sys-sample:edit")?d("",!0):(o(),i("div",{key:1,class:"btn perview",onClick:e=>(e=>{xe.value.init("edit",e)})(s)}," 编辑 ",8,pe)),e.hasPerm("sys-sample:batch-delete")?(o(),g(Ae,{key:2,placement:"topRight",title:"确认删除？",onConfirm:e=>(async e=>{const a=await I({id:e.id});200===a.code?(E("success","删除成功！"),De()):E("error",a.message)})(s)},{default:m((()=>[2!==Ue.source||1!==s.status&&4!==s.status&&3!==s.status?d("",!0):(o(),i("div",me,"删除"))])),_:2},1032,["onConfirm"])):d("",!0),!e.hasPerm("sys-sample:change-status")||2!==Ue.source||1!==s.status&&4!==s.status?d("",!0):(o(),i("div",{key:3,class:"btn perview",onClick:e=>(e=>{T({status:3,id:e.id}).then((e=>{200===e.code?(E("success","提交成功，请等待管理员审批"),De()):E("error",e.message)}))})(s)},"提交审批",8,ve)),r("div",ge,[n(Le,{placement:"top"},{title:m((()=>[r("span",null,p(s.failureCause),1)])),default:m((()=>[4===s.status?(o(),g(He,{key:0,title:"未通过原因",style:{"margin-right":"5px"}})):d("",!0)])),_:2},1024),n(Be,{title:"示例说明",onClick:e=>(e=>{ze.value.init(e)})(s.remark)},null,8,["onClick"])])])]),r("div",ye,[r("div",he,[r("div",{class:"name",title:s.name},p(s.name),9,fe),r("div",{class:"user",title:s.createName},p(s.createName),9,je)]),r("div",ke,[r("div",{id:s.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(o(!0),i(u,null,c(s.functionExampleTags,((e,a)=>(o(),i("div",{key:a,title:e.tagName,class:"tag-item",style:w({backgroundColor:e.color})},p(e.tagName),13,_e)))),128))],8,we)])])])])),[[f,!Pe.value&&Re.value.length]]))),128)),Pe.value?(o(),g(N,{key:0,class:"loading-icon",spinning:Pe.value},null,8,["spinning"])):d("",!0),Pe.value||Re.value.length?d("",!0):(o(),g(b,{key:1}))])):(o(),i("div",be,[n(b,{title:"暂无权限"})])),r("div",Ce,[n(Ke,{total:Ue.total,"page-size-options":["12","20","30","40"],current:Ue.current,"page-size":Ue.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:qe},null,8,["total","current","page-size","show-total"])])])]),n(R,{ref_key:"detailSampleRef",ref:Ee},null,512),n(z,{ref_key:"docPreviewRef",ref:ze},null,512),n(x,{ref_key:"AddExampleRef",ref:xe,onOk:Se},null,512)])}}}),[["__scopeId","data-v-505b2f53"]]);export{ze as default};
