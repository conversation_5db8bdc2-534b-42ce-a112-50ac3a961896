import{d as e,r as a,f as l,j as t,w as s,am as i,b as o,o as r,e as n,c as v,F as u,ag as p,ab as c,ad as d,aa as m,J as g,a9 as h,ae as j,y,G as f,a5 as w,a4 as k,n as b}from"./@vue-HScy-mz9.js";import{p as _}from"./projectGallery-DFHuwUAq.js";import x from"./Nodata-mmdoiDH6.js";import{V as C}from"./viewerjs-_Br7E8dP.js";import{V as N}from"./ViewerArrow-r3R4USz-.js";import z from"./Preview-JkhsQ1Ya.js";import I from"./editProject-dmKYqYHX.js";import P from"./addProject-DP4m5dFC.js";import{h as $,j as E,k as S,b as R}from"./projectGallery-xT8wgNPG.js";import{e as O,x as M,b as U}from"./main-Djn9RDyT.js";import T from"./Reason-0caFKJKC.js";import V from"./ImgVideoPreview-D6eMSuon.js";import{q as G,r as J,S as A,I as B,x as K,w as L,B as F,e as q,d as D,g as H,M as Q}from"./ant-design-vue-DYY9BtJq.js";import{_ as X}from"./vue-qr-CB2aNKv5.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./axios-7z2hFSF6.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";const Z={class:"project-gallery"},W={class:"tag-search"},Y={key:0,class:"tag-content"},ee=["onClick"],ae={key:1,class:"tag-item"},le={key:1,class:"no-tag-content"},te={class:"page-wrap"},se={class:"search-wrap"},ie={class:"search-content"},oe={class:"search-item"},re={class:"search-item"},ne={class:"search-item"},ve={class:"search-item"},ue={class:"search-btns"},pe={class:"table-handle"},ce={class:"content-list"},de={key:0,class:"list"},me={class:"contain"},ge={class:"img-box"},he=["src"],je={class:"bottom-wrapper"},ye={class:"bottom-content"},fe={class:"author"},we={class:"hover-box"},ke=["onClick"],be=["onClick"],_e={key:0,class:"btn"},xe={key:0,class:"btn"},Ce={key:0,class:"btn"},Ne={key:1,class:"btn"},ze={class:"control-icon"},Ie={key:0,style:{display:"inline-block",width:"16px",height:"16px","margin-right":"12px"}},Pe=["id"],$e=["src"],Ee={class:"item-bottom"},Se={class:"title"},Re=["title"],Oe={class:"tag-wrapper"},Me=["id"],Ue=["title"],Te={key:1,class:"list"},Ve={class:"pagination-box"},Ge=X(e({__name:"Index",setup(e){const X=a(sessionStorage.getItem("XI_TONG_LOGO")||_),Ge=a("");l((async()=>{var e,a;const l=await O({pageNo:1,pageSize:9,code:"KAI_FA_SHE_JI_TU"});Ge.value=null==(a=null==(e=l.data)?void 0:e.rows[0])?void 0:a.value}));const Je=()=>{M(Ge.value)?window.open(`${Ge.value}&keyword=${Ze.value.contractCode} ${Ze.value.productName} ${Ze.value.industry} ${Ze.value.techPlatform}`):window.open(`${Ge.value}?keyword=${Ze.value.contractCode} ${Ze.value.productName} ${Ze.value.industry} ${Ze.value.techPlatform}`)},Ae=a();let Be=null;const Ke=a(),Le=a(0),Fe=a(0),qe=()=>{Be&&Be.prev()},De=()=>{Be&&Be.next()};t((()=>{Be&&Be.destroy()}));const He=a(!0),Qe=a(),Xe=a(!1),Ze=a(),We=a({}),Ye=a(),ea=(e,a)=>{e?R({id:a.id,approve:2}).then((e=>{U("success","项目示例审批完成"),va()})):Ye.value.init(a)},aa=e=>{let a="";return a=2===e?"正常":1===e?"待审批":3===e?"未通过":"未提交",a},la=a([]),ta=a(!0);ta.value=!0,$().then((e=>{ta.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{We.value[e.id]=[]})):We.value={},la.value=e.data||[]):la.value=[]})).catch((()=>{We.value={},la.value=[],ta.value=!1}));s((()=>We.value),(()=>{da()}),{deep:!0});const sa=()=>{Xe.value=!1},ia=a({total:0,current:1,pageSize:12,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),oa=(e,a)=>{ia.value=Object.assign(ia.value,{current:e,pageSize:a}),va()},ra=()=>{va()},na=a({projectName:"",projectManager:"",visibility:-1,approve:-1,tagIds:[],classified:0}),va=async()=>{var e;He.value=!0;const a=(null==(e=Object.values(We.value))?void 0:e.flat(Infinity))||[],l=await E({pageNo:ia.value.current,projectName:na.value.projectName,projectManager:na.value.projectManager,visibility:-1===na.value.visibility?null:na.value.visibility,approve:-1===na.value.approve?null:na.value.approve,tagIds:a,pageSize:ia.value.pageSize,sort:""});if(He.value=!1,200===l.code){const{rows:e,totalPage:a,totalRows:t}=l.data;ua.value=e.map((e=>({...e}))),ia.value.total=t,ca()}},ua=a([]),pa=a([]),ca=async()=>{pa.value=[];for(let e=0;e<ua.value.length;e++){const a=ua.value[e].cover||(ua.value[e].deliverablesImages?ua.value[e].deliverablesImages[0]:"");a?pa.value.push(`${window.config.baseUrl}${a}?width=400`):pa.value.push(_)}},da=()=>{ia.value.total=0,ia.value.current=1,ia.value.pageSize=12,va()},ma=async(e,a=0)=>{if(a)1===a&&R({id:e.id,approve:1}).then((e=>{200===e.code&&(U("success","项目已下架"),va())}));else{const a=await S({projExampleId:e.id});200===a.code?(U("success","项目案例删除成功"),va()):U("error",a.message)}},ga=a(),ha=()=>{ga.value.init()};return(e,a)=>{var l;const t=G,s=J,_=A,$=B,E=K,S=L,R=F,O=q,M=i("exception-outlined"),U=D,va=i("picture-outlined"),ca=i("video-camera-outlined"),ja=H,ya=Q;return r(),o("div",Z,[n("div",W,[(null==(l=la.value)?void 0:l.length)?(r(),o("div",Y,[(r(!0),o(u,null,p(la.value,(e=>{var a,l;return r(),o("div",{key:e.id,class:"tag-group"},[(null==(a=null==e?void 0:e.tags)?void 0:a.length)?(r(),o("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const l=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(We.value[e.id])===JSON.stringify(l)?We.value[e.id]=[]:We.value[e.id]=e.tags.map((e=>e.id))})(e)},d(e.groupName),9,ee)):c("",!0),(null==(l=null==e?void 0:e.tags)?void 0:l.length)?(r(),o("div",ae,[v(s,{value:We.value[e.id],"onUpdate:value":a=>We.value[e.id]=a,style:{width:"100%"}},{default:m((()=>[(r(!0),o(u,null,p(e.tags,(e=>(r(),o("div",{key:e.id,class:"tag-item-name"},[v(t,{value:e.id},{default:m((()=>[g(d(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):c("",!0)])})),128))])):(r(),o("div",le,[ta.value?(r(),h(_,{key:0,class:"loading-icon",spinning:ta.value},null,8,["spinning"])):c("",!0),ta.value?c("",!0):(r(),h(x,{key:1,title:"请绑定标签"}))]))]),n("div",te,[n("div",se,[n("div",ie,[n("div",oe,[a[10]||(a[10]=n("span",{class:"search-label"},"项目名称",-1)),n("div",null,[v($,{value:na.value.projectName,"onUpdate:value":a[0]||(a[0]=e=>na.value.projectName=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入项目名称",class:"search-input",onKeyup:a[1]||(a[1]=j((e=>da()),["enter"]))},null,8,["value"])])]),n("div",re,[a[11]||(a[11]=n("span",{class:"search-label"},"项目经理",-1)),n("div",null,[v($,{value:na.value.projectManager,"onUpdate:value":a[2]||(a[2]=e=>na.value.projectManager=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入项目经理",class:"search-input",onKeyup:a[3]||(a[3]=j((e=>da()),["enter"]))},null,8,["value"])])]),n("div",ne,[a[15]||(a[15]=n("span",{class:"search-label"},"是否可见",-1)),n("div",null,[v(S,{value:na.value.visibility,"onUpdate:value":a[4]||(a[4]=e=>na.value.visibility=e),"allow-clear":"",placeholder:"请选择是否可见",class:"search-select select-short",onChange:a[5]||(a[5]=e=>da())},{default:m((()=>[v(E,{value:-1},{default:m((()=>a[12]||(a[12]=[g("全部")]))),_:1}),v(E,{value:1},{default:m((()=>a[13]||(a[13]=[g("是")]))),_:1}),v(E,{value:0},{default:m((()=>a[14]||(a[14]=[g("否")]))),_:1})])),_:1},8,["value"])])]),n("div",ve,[a[20]||(a[20]=n("span",{class:"search-label"},"审批状态",-1)),n("div",null,[v(S,{value:na.value.approve,"onUpdate:value":a[6]||(a[6]=e=>na.value.approve=e),"allow-clear":"",placeholder:"请选择审批状态",class:"search-select select-short",onChange:a[7]||(a[7]=e=>da())},{default:m((()=>[v(E,{value:-1},{default:m((()=>a[16]||(a[16]=[g("全部")]))),_:1}),v(E,{value:1},{default:m((()=>a[17]||(a[17]=[g("待审批")]))),_:1}),v(E,{value:2},{default:m((()=>a[18]||(a[18]=[g("审批通过")]))),_:1}),v(E,{value:3},{default:m((()=>a[19]||(a[19]=[g("审批不通过")]))),_:1})])),_:1},8,["value"])])]),n("div",ue,[v(R,{type:"primary",class:"search-btn",onClick:a[8]||(a[8]=e=>da())},{default:m((()=>a[21]||(a[21]=[g(" 查询 ")]))),_:1})])]),n("div",pe,[e.hasPerm("projectExampleNew:add")&&e.hasPerm("projectExampleNew:approve")?(r(),h(R,{key:0,type:"primary",class:"handle-btn",onClick:ha},{default:m((()=>a[22]||(a[22]=[g(" 新增项目案例 ")]))),_:1})):c("",!0)])]),n("div",ce,[e.hasPerm("projectExampleNew:page")?(r(),o("div",de,[(r(!0),o(u,null,p(ua.value,((l,t)=>{var s,i;return y((r(),o("div",{key:l.id,class:"item"},[n("div",me,[n("div",ge,[n("img",{src:pa.value[t],alt:"图片",class:"img",onError:a[9]||(a[9]=e=>(e.target.src=X.value,e.target.style.width="auto"))},null,40,he),n("div",je,[n("div",ye,[n("span",fe,"项目经理："+d(l.projectManager),1),n("div",{class:w(["status",{fail:3===l.approve,check:1===l.approve,normal:2===l.approve,unpush:!l.approve}])},d(aa(l.approve)),3)])]),n("div",we,[n("div",{class:"btn perview",onClick:e=>{return a=l,Ze.value=a,void(Xe.value=!0);var a}},"详情",8,ke),e.hasPerm("projectExampleNew:modify")?(r(),o("div",{key:0,class:"btn",onClick:e=>{return a=l,void Ae.value.init(a);var a}},"编辑",8,be)):c("",!0),v(O,{placement:"topRight",title:"确认删除？",onConfirm:e=>ma(l)},{default:m((()=>[e.hasPerm("projectExampleNew:delete")&&3===l.approve?(r(),o("div",_e,"删除")):c("",!0)])),_:2},1032,["onConfirm"]),v(O,{placement:"topRight",title:"确认下架？",onConfirm:e=>ma(l,1)},{default:m((()=>[e.hasPerm("projectExampleNew:approve")&&2===l.approve?(r(),o("div",xe,"下架")):c("",!0)])),_:2},1032,["onConfirm"]),e.hasPerm("projectExampleNew:approve")?(r(),h(O,{key:1,placement:"topRight",title:"确认审批通过？",okText:"通过",cancelText:"不通过",onConfirm:e=>ea(!0,l),onCancel:e=>ea(!1,l)},{default:m((()=>[1===l.approve?(r(),o("div",Ce,"审批")):c("",!0),3===l.approve?(r(),o("div",Ne,"重新审批")):c("",!0)])),_:2},1032,["onConfirm","onCancel"])):c("",!0),n("div",ze,[v(U,{placement:"top"},{title:m((()=>[n("span",null,d(l.approveRemark),1)])),default:m((()=>[3===l.approve?(r(),h(M,{key:0,title:"未通过原因",style:{"margin-right":"12px"}})):c("",!0)])),_:2},1024),(null==(s=l.deliverablesImages)?void 0:s.length)?(r(),o("div",Ie,[v(va,{title:"图片预览",style:{"margin-right":"5px"},onClick:e=>(e=>{e.showImgPreview=!0,b((()=>{Be=new C(document.getElementById(`img-pre-wrap${e.id}`),{toolbar:!1,navbar:!0,title:!1,transition:!1,hidden:()=>{null==Be||Be.destroy(),Be=null,Fe.value=0},viewed(){Le.value=Be.index,Fe.value=Be.length,Be.viewer.appendChild(Ke.value.$el)}}),Be.show()}))})(l)},null,8,["onClick"]),l.showImgPreview?(r(),o("div",{key:0,id:"img-pre-wrap"+l.id,style:{display:"none"}},[(r(!0),o(u,null,p(l.deliverablesImages,((e,a)=>{return r(),o("img",{key:a,src:(l=e,l?window.config.baseUrl+l:""),alt:""},null,8,$e);var l})),128))],8,Pe)):c("",!0)])):c("",!0),(null==(i=l.deliverablesVideo)?void 0:i.length)?(r(),h(ca,{key:1,title:"视频预览",onClick:e=>{return a="video",t=l.deliverablesVideo,void Qe.value.init(a,t);var a,t}},null,8,["onClick"])):c("",!0)])])]),n("div",Ee,[n("div",Se,[n("div",{class:"name",title:l.projectName},d(l.projectName),9,Re)]),n("div",Oe,[n("div",{id:l.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(r(!0),o(u,null,p(l.exTags,((e,a)=>(r(),o("div",{key:a,class:"tag-item",title:e.tagName,style:k({backgroundColor:e.color})},d(e.tagName),13,Ue)))),128))],8,Me)])])])])),[[f,ua.value.length]])})),128)),He.value?(r(),h(_,{key:0,class:"loading-icon",spinning:He.value},null,8,["spinning"])):c("",!0),He.value||ua.value.length?c("",!0):(r(),h(x,{key:1}))])):(r(),o("div",Te,[v(x,{title:"暂无权限"})])),n("div",Ve,[v(ja,{total:ia.value.total,"page-size-options":["12","20","30","40"],current:ia.value.current,"page-size":ia.value.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:oa},null,8,["total","current","page-size","show-total"])])]),v(ya,{open:Xe.value,"wrap-class-name":"cus-modal detail-modal",width:"80%","body-style":{maxHeight:"600px",overflow:"auto"},footer:null,onCancel:sa},{title:m((()=>[a[24]||(a[24]=n("span",null,"详情",-1)),y(v(R,{type:"primary",style:{"margin-left":"20px"},onClick:Je},{default:m((()=>a[23]||(a[23]=[g("相关设计图")]))),_:1},512),[[f,"none"!==Ge.value]])])),default:m((()=>[Xe.value?(r(),h(z,{key:0,"gallery-info":Ze.value,onOk:ra},null,8,["gallery-info"])):c("",!0)])),_:1},8,["open"])]),v(I,{ref_key:"editProjectRef",ref:Ae,onOk:ra},null,512),v(P,{ref_key:"addProjectRef",ref:ga,onOk:ra},null,512),v(T,{ref_key:"reasonRef",ref:Ye,onOk:ra},null,512),v(V,{ref_key:"imgVideoPreviewRef",ref:Qe},null,512),v(N,{ref_key:"viewerArrowRef",ref:Ke,current:Le.value,total:Fe.value,onLeft:qe,onRight:De},null,8,["current","total"])])}}}),[["__scopeId","data-v-1e960c63"]]);export{Ge as default};
