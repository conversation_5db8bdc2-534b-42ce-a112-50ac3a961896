import{d as e,r as a,f as s,b as t,o,e as i,c as r,ae as l,u as n,aa as m,J as p,a9 as c,ab as d,ad as u,F as j,a7 as v}from"./@vue-HScy-mz9.js";import{u as g}from"./useTableScrollY-DAiBD3Av.js";import h from"./AddProject-JOuXsh3f.js";import{a as y,k as f}from"./operationAnalysis-D3RTU-GI.js";import{I as b,B as k,e as w,f as x,g as z}from"./ant-design-vue-DYY9BtJq.js";import{_ as $}from"./vue-qr-CB2aNKv5.js";import"./main-Djn9RDyT.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const _={class:"all-content"},I={class:"search-wrap"},C={class:"search-content"},S={class:"search-item"},T={class:"search-btns"},P={class:"table-handle"},D={class:"btns-wrap"},M={class:"table-wrap"},N={key:0},A=["onClick"],J={class:"pagination"},R=$(e({__name:"Index",setup(e){const $=a(),{scrollY:R}=g($);s((()=>{L(),E()}));const Y=[{title:"序号",dataIndex:"index",width:100},{title:"操作系统",dataIndex:"operateSystem"},{title:"安装包说明",dataIndex:"url"},{title:"上传人",dataIndex:"createName"},{title:"上传时间",dataIndex:"createTime"},{title:"操作",dataIndex:"actions"}],B=a({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small",name:""});a("");const E=()=>{B.value.current=1,B.value.pageSize=10,L()},F=a(!1),K=a([]),L=()=>{K.value=[],F.value=!1,y({name:B.value.name,pageNo:B.value.current,pageSize:B.value.pageSize}).then((e=>{if(200===e.code){const{rows:a,pageNo:s,totalRows:t}=e.data;a.forEach(((e,a)=>{e.index=a+1})),K.value=a,B.value.current=s,B.value.total=t}F.value=!1})).catch((()=>{F.value=!1}))},O=a(),Q=(e,a)=>{O.value.init(e,a)},q=e=>{L()},H=(e,a)=>{if(!e)return"--";const s=new Date(e),t=s.getFullYear(),o=s.getMonth()+1,i=s.getDate(),r=s.getHours(),l=s.getMinutes(),n=s.getSeconds();return s.getDay(),`${t}-${o<10?`0${o}`:o}-${i<10?`0${i}`:i} ${r<10?`0${r}`:r}:${l<10?`0${l}`:l}:${n<10?`0${n}`:n}`},U=(e,a)=>{B.value=Object.assign(B.value,{current:e,pageSize:a}),L()};return(e,a)=>{const s=b,g=k,y=w,Z=x,G=z;return o(),t("div",_,[i("div",I,[i("div",C,[i("div",S,[a[4]||(a[4]=i("span",{class:"search-label"},"操作系统",-1)),i("div",null,[r(s,{value:n(B).name,"onUpdate:value":a[0]||(a[0]=e=>n(B).name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入操作系统名称",class:"search-input",onKeyup:a[1]||(a[1]=l((e=>E()),["enter"]))},null,8,["value"])])]),i("div",T,[r(g,{type:"primary",class:"search-btn",onClick:a[2]||(a[2]=e=>E())},{default:m((()=>a[5]||(a[5]=[p(" 查询 ")]))),_:1})])]),i("div",P,[i("div",D,[e.hasPerm("sys-install-manage:add")?(o(),c(g,{key:0,type:"primary",class:"handle-btn",onClick:a[3]||(a[3]=e=>Q("add",null))},{default:m((()=>a[6]||(a[6]=[p(" 新增 ")]))),_:1})):d("",!0)])])]),i("div",M,[i("div",{ref_key:"table",ref:$,class:"table-content"},[e.hasPerm("sys-install-manage:page")?(o(),c(Z,{key:0,class:"table",scroll:{y:n(R)},pagination:!1,size:"small",loading:n(F),"row-key":e=>e.id,columns:Y,"data-source":n(K),onChange:U},{bodyCell:m((({column:s,record:r})=>["createTime"===s.dataIndex?(o(),t("div",N,u(H(r.createTime)),1)):d("",!0),"actions"===s.dataIndex?(o(),t(j,{key:1},[e.hasPerm("sys-install-manage:edit")?(o(),t("a",{key:0,style:{"margin-right":"20px"},onClick:e=>Q("edit",r)},"编辑",8,A)):d("",!0),e.hasPerm("sys-install-manage:batch-delete")?(o(),c(y,{key:1,placement:"topRight",title:"确认删除？",okText:"确认",cancelText:"取消",onConfirm:e=>(e=>{const a={ids:[e.id]};f(a).then((e=>{L()}))})(r)},{default:m((()=>a[7]||(a[7]=[i("a",null,"删除",-1)]))),_:2},1032,["onConfirm"])):d("",!0)],64)):d("",!0)])),_:1},8,["scroll","loading","row-key","data-source"])):d("",!0),i("div",J,[n(K).length>0?(o(),c(G,v({key:0},n(B),{onChange:U}),null,16)):d("",!0)])],512)]),r(h,{ref_key:"addProjectRef",ref:O,onOk:q},null,512)])}}}),[["__scopeId","data-v-305bf0a4"]]);export{R as default};
