import{a as s,x as t}from"./main-Djn9RDyT.js";import{u as o}from"./vue3-cookies-D4wQmYyh.js";import{d as e,r as i,p as r,j as a,b as n,ab as m,u as p,e as l,o as j}from"./@vue-HScy-mz9.js";import{_ as u}from"./vue-qr-CB2aNKv5.js";import"./ant-design-vue-DYY9BtJq.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./js-binary-schema-parser-G48GG52R.js";const d={class:"iframe-wrap"},c=["src"],v={key:1,class:"loading"},k=["src"],f=u(e({__name:"Index",setup(e){const u=i(sessionStorage.getItem("XI_TONG_LOGO")||""),{cookies:f}=o(),g=i(!0),$=s(),y=r((()=>{var s,o,e,i,r,a;let n=(null==(s=$.menuData)?void 0:s.link)||(null==(e=null==(o=$.menuData)?void 0:o.meta)?void 0:e.link);const m=(null==(i=$.menuData)?void 0:i.withToken)||(null==(a=null==(r=$.menuData)?void 0:r.meta)?void 0:a.withToken);if("/base"===n){let s="";return s=`/base?tenant=master&token=${$.adminToken}&name=master`,s}const p=t(n);if(n.startsWith("/")&&(n=window.location.origin+n),"2"===m)n=p?n.endsWith("=")?`${n}${$.adminToken}&tenant=master`:`${n}&token=${$.adminToken}&tenant=master`:`${n}?token=${$.adminToken}&tenant=master`;else if("1"===m){const s=f.get("ACCESS_TOKEN_U");n=p?n.endsWith("=")?`${n}${s}`:`${n}&token=${s}`:`${n}?token=${s}`}return n}));return a((()=>{$.menuData=null})),(s,t)=>(j(),n("div",d,[p(y)?(j(),n("iframe",{key:0,src:p(y),onLoad:t[0]||(t[0]=s=>g.value=!1),onError:t[1]||(t[1]=s=>g.value=!1),frameborder:"0",class:"iframe-app"},null,40,c)):m("",!0),p(g)?(j(),n("div",v,[l("img",{src:p(u),alt:"ti",onError:t[2]||(t[2]=s=>s.target.src=p("/assets/png/ti-La0Htg_q.png"))},null,40,k)])):m("",!0)]))}}),[["__scopeId","data-v-d2207a27"]]);export{f as default};
