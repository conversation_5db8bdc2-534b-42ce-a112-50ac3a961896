import{u as s,b as o}from"./vue-router-BEwRlUkF.js";import e from"./Index-DOto098k.js";import r from"./Index-BVskKwtA.js";import"./main-Djn9RDyT.js";import{d as i,r as t,b as a,e as p,J as m,c as j,am as n,ad as l,F as d,ag as c,ab as u,o as v,a5 as y}from"./@vue-HScy-mz9.js";import{_ as b}from"./vue-qr-CB2aNKv5.js";import"./clipboard-Dv7Qpqbb.js";import"./@babel-B4rXMRun.js";import"./Nodata-mmdoiDH6.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./PublishProject-CTg0PqB0.js";import"./dayjs-CA7qlNSr.js";import"./projectGallery-xT8wgNPG.js";import"./ant-design-vue-DYY9BtJq.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./AddProject-SCEWZ0UE.js";import"./DownLoadPaksList-Yx_9AsML.js";import"./operationAnalysis-D3RTU-GI.js";import"./AddAndEditForm-Bbw4rS_w.js";import"./Map.vue_vue_type_style_index_0_lang-CZrcyiOo.js";import"./useTableScrollY-DAiBD3Av.js";import"./EditProjectDeveloper-DjAmz1qY.js";import"./UserList-B68ng9ko.js";import"./Add-XZCKK-Ph.js";import"./userManage-DLgtGpjc.js";import"./UserList-Bo1tWySN.js";import"./Depart-B4y-bHgG.js";import"./department-CTxHSeTj.js";import"./Reason-aYyHfuGg.js";const g={class:"campus-detail"},_={class:"company-nav keep-px"},k={class:"third-menu"},w={class:"third-menu-wrap"},f={class:"content"},h=["onClick"],x={class:"name"},L={class:"all-content"},A={class:"page-wrap"},N={key:0,class:"project-wrap"},z={key:1,class:"developer-wrap"},E=b(i({__name:"Index",setup(i){const b=o(),E=()=>{b.push("/operator/companyManage")},{query:I,params:P}=s(),C=t(I.name),{id:D}=P,M=t([{id:1,name:"项目列表"},{id:2,name:"团队成员列表"}]),F=t(1);return t({projectNumber:0,projectStartNumber:0,developNumber:0,developEmployNumber:0}),(s,o)=>{const i=n("rollback-outlined");return v(),a("div",g,[p("div",_,[p("span",{class:"name",onClick:E},[j(i,{style:{"margin-right":"5px"}}),m(l(C.value),1)]),o[0]||(o[0]=m("/查看详情 "))]),p("div",k,[p("div",w,[p("div",f,[(v(!0),a(d,null,c(M.value,(s=>(v(),a("div",{key:s.id,class:y(["content-item",s.id===F.value?"active":""]),onClick:o=>(s=>{F.value!==s.id&&(F.value=s.id)})(s)},[p("span",x,l(s.name),1)],10,h)))),128))])])]),p("div",L,[p("div",A,[1===F.value?(v(),a("div",N,[j(e)])):u("",!0),2===F.value?(v(),a("div",z,[j(r)])):u("",!0)])])])}}}),[["__scopeId","data-v-4e7a4803"]]);export{E as default};
