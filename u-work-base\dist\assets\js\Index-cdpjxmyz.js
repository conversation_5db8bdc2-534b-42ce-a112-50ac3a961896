import{d as e,r as a,j as t,w as s,a as l,am as o,b as i,o as n,e as r,c,F as u,ag as p,ab as d,ad as m,aa as v,J as g,a9 as f,ae as y,y as h,G as j,a5 as k,a4 as w}from"./@vue-HScy-mz9.js";import{V as _}from"./ViewerArrow-r3R4USz-.js";import{p as b}from"./projectGallery-DFHuwUAq.js";import C from"./Nodata-mmdoiDH6.js";import{I as N,u as z}from"./useCookies-BRWvy2S3.js";import S from"./EditScreen-EuELiV16.js";import{u as T,e as R,b as $}from"./main-Djn9RDyT.js";import{s as x,a as I,p as U,b as O}from"./screenTemplate-Brx_fHCI.js";import E from"./Detail-DLia8ANG.js";import P from"./UseTemp-BN1jL0yF.js";import G from"./UploadFile-C3CwTFwb.js";import{q as F,r as J,S as L,I as A,x as B,w as D,B as V,e as q,d as K,g as M}from"./ant-design-vue-DYY9BtJq.js";import{_ as H}from"./vue-qr-CB2aNKv5.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./vue3-cookies-D4wQmYyh.js";import"./jsencrypt-BWvXBO1z.js";import"./crypto-js-Duvj5un5.js";import"./@babel-B4rXMRun.js";import"./projectGallery-xT8wgNPG.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./validator-DM5yI3AY.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./tinycolor2-DJ_qK68I.js";const W={class:"screen-templete"},X={class:"content-wrap"},Z={class:"tag-search"},Q={key:0,class:"tag-content"},Y=["onClick"],ee={key:1,class:"tag-item"},ae={key:1,class:"no-tag-content"},te={class:"content-list"},se={class:"search"},le={class:"search-wrap"},oe={key:0,class:"list"},ie={class:"contain"},ne={class:"img-box"},re=["src"],ce={class:"bottom-wrapper"},ue={class:"bottom-content"},pe={class:"hover-box"},de=["onClick"],me=["onClick"],ve={key:0,class:"btn perview"},ge=["onClick"],fe={class:"control-icon"},ye={class:"item-bottom"},he={class:"title"},je=["title"],ke=["title"],we={class:"tag-wrapper"},_e=["id"],be=["title"],Ce={key:1,class:"list"},Ne={class:"pagination-box"},ze=H(e({__name:"Index",setup(e){const H=a(sessionStorage.getItem("XI_TONG_LOGO")||b),ze=T(),Se=a(),Te=a(0),Re=a(0),$e=a(""),xe=()=>{},Ie=()=>{};R({code:"SCREEN_UI_IP"}).then((e=>{var a,t;const s=(null==(t=null==(a=e.data)?void 0:a.rows[0])?void 0:t.value)||"";$e.value=s})).catch((()=>{$e.value=""})),t((()=>{}));const Ue=a(),Oe=a(),Ee=()=>{Oe.value.init()},Pe=a(!0),Ge=a({}),Fe=a([]);s((()=>Ge.value),(()=>{Ve(1,12)}),{deep:!0});const Je=a(!0);Je.value=!0,x().then((e=>{Je.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Ge.value[e.id]=[]})):Ge.value={},Fe.value=e.data||[]):Fe.value=[]})).catch((()=>{Ge.value={},Fe.value=[],Je.value=!1}));const Le=a(),Ae=a(),Be=a([]),De=l({total:0,current:1,pageSize:12,likeContent:"",tagIds:[],source:1,checkedType:-1}),Ve=(e,a)=>{De.current=e,De.pageSize=a,qe()},qe=async()=>{var e;Pe.value=!0;const a=(null==(e=Object.values(Ge.value))?void 0:e.flat(Infinity))||[],t={pageNo:De.current,pageSize:De.pageSize,tagIds:a,name:De.likeContent,status:-1===De.checkedType?null:De.checkedType,type:1===De.source||3===De.source?"0":"1"};3===De.source?t.createUser=ze.userInfo.id:t.status=0;const s=await I(t);if(Pe.value=!1,200===s.code){const{rows:e,totalRows:a}=s.data;Be.value=e||[],De.total=a}},Ke=a(),Me=e=>{let a="";return 0===e?a="正常":1===e?a="待审批":2===e&&(a="未通过"),a};return(e,a)=>{var t;const s=F,l=J,b=L,T=o("search-outlined"),R=A,x=B,I=D,ze=V,He=q,We=o("exception-outlined"),Xe=K,Ze=M;return n(),i("div",W,[r("div",X,[r("div",Z,[(null==(t=Fe.value)?void 0:t.length)?(n(),i("div",Q,[(n(!0),i(u,null,p(Fe.value,(e=>{var a,t;return n(),i("div",{key:e.id,class:"tag-group"},[(null==(a=e.tags)?void 0:a.length)?(n(),i("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const t=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Ge.value[e.id])===JSON.stringify(t)?Ge.value[e.id]=[]:Ge.value[e.id]=e.tags.map((e=>e.id))})(e)},m(e.groupName),9,Y)):d("",!0),(null==(t=e.tags)?void 0:t.length)?(n(),i("div",ee,[c(l,{value:Ge.value[e.id],"onUpdate:value":a=>Ge.value[e.id]=a,style:{width:"100%"}},{default:v((()=>[(n(!0),i(u,null,p(e.tags,(e=>(n(),i("div",{key:e.id,class:"tag-item-name"},[c(s,{value:e.id},{default:v((()=>[g(m(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):d("",!0)])})),128))])):(n(),i("div",ae,[Je.value?(n(),f(b,{key:0,class:"loading-icon",spinning:Je.value},null,8,["spinning"])):d("",!0),Je.value?d("",!0):(n(),f(C,{key:1,title:"请绑定标签"}))]))]),r("div",te,[r("div",se,[r("div",le,[c(R,{value:De.likeContent,"onUpdate:value":a[1]||(a[1]=e=>De.likeContent=e),valueModifiers:{trim:!0},class:"search-wrapper",placeholder:"搜索",onKeyup:a[2]||(a[2]=y((e=>Ve(1,12)),["enter"]))},{suffix:v((()=>[c(T,{style:{cursor:"pointer"},onClick:a[0]||(a[0]=e=>Ve(1,12))})])),_:1},8,["value"]),c(I,{value:De.source,"onUpdate:value":a[3]||(a[3]=e=>De.source=e),placeholder:"请选择数据源",class:"search-select",onChange:a[4]||(a[4]=e=>(De.checkedType=-1,void Ve(1,12)))},{default:v((()=>[c(x,{value:1},{default:v((()=>a[9]||(a[9]=[g("公共资源")]))),_:1}),c(x,{value:2},{default:v((()=>a[10]||(a[10]=[g("项目资源")]))),_:1}),c(x,{value:3},{default:v((()=>a[11]||(a[11]=[g("我的资源")]))),_:1})])),_:1},8,["value"]),3===De.source?(n(),f(I,{key:0,value:De.checkedType,"onUpdate:value":a[5]||(a[5]=e=>De.checkedType=e),placeholder:"请选择状态",class:"search-select",onChange:a[6]||(a[6]=e=>Ve(1,12))},{default:v((()=>[c(x,{value:-1},{default:v((()=>a[12]||(a[12]=[g("全部")]))),_:1}),c(x,{value:1},{default:v((()=>a[13]||(a[13]=[g("待审批")]))),_:1}),c(x,{value:0},{default:v((()=>a[14]||(a[14]=[g("审批通过")]))),_:1}),c(x,{value:2},{default:v((()=>a[15]||(a[15]=[g("审批不通过")]))),_:1})])),_:1},8,["value"])):d("",!0),c(ze,{type:"primary",class:"search-btn",onClick:a[7]||(a[7]=e=>Ve(1,12))},{default:v((()=>a[16]||(a[16]=[g(" 查询 ")]))),_:1})]),e.hasPerm("annotate-component:upload-base")?(n(),f(ze,{key:0,type:"primary",class:"handle-btn",onClick:Ee},{default:v((()=>a[17]||(a[17]=[g(" 导入大屏模板 ")]))),_:1})):d("",!0)]),e.hasPerm("annotate-component:page-base")?(n(),i("div",oe,[(n(!0),i(u,null,p(Be.value,(t=>{var s,l,o,g,y,_;return h((n(),i("div",{key:t.id,class:"item"},[r("div",ie,[r("div",ne,[r("img",{src:(y=t.filePath,_=t.snapShot,$e.value?`${window.config.baseUrl}${_}?width=400`:`${window.config.baseUrl}${y}${_}?width=400`),alt:"图片",class:"img",onError:a[8]||(a[8]=e=>(e.target.src=H.value,e.target.style.width="auto"))},null,40,re),h(r("div",ce,[r("div",ue,[r("div",{class:k(["status",{fail:2===t.status,unpush:null===t.status,normal:0===t.status}])},m(Me(t.status)),3)])],512),[[j,3===De.source]]),r("div",pe,[r("div",{class:"btn",onClick:e=>(e=>{$e.value?(z(),window.open(`${$e.value}/kunpeng/preview/sandbox/${e.id}?random=${Date.now()}`,"_blank",`width=${window.innerWidth},height=${window.innerHeight-100},top=100,left=100,z-look=yes`)):window.open("_blank").location=`/screen/index.html?path=${e.filePath}`})(t)},"预览",8,de),3===De.source&&e.hasPerm("annotate-component:modify")?(n(),i("div",{key:0,class:"btn perview",onClick:e=>(e=>{const a={...e};Ke.value.init(a)})(t)},"编辑",8,me)):d("",!0),c(He,{placement:"topRight",title:"确认删除？",onConfirm:e=>(async e=>{200===(await O([e.id])).code&&($("success","大屏模板删除成功"),qe())})(t)},{default:v((()=>[3===De.source&&2===t.status?(n(),i("div",ve,"删除")):d("",!0)])),_:2},1032,["onConfirm"]),3===De.source&&2===t.status?(n(),i("div",{key:1,class:"btn perview",onClick:e=>(async e=>{const a=await U({id:e.id,status:1});200===a.code?($("success","提交成功，请等待管理员审批"),qe()):$("error",a.message)})(t)},"提交审批",8,ge)):d("",!0),r("div",fe,[c(Xe,{placement:"top"},{title:v((()=>[r("span",null,m(t.approveRemark),1)])),default:v((()=>[3===De.source&&2===t.status?(n(),f(We,{key:0,title:"未通过原因",style:{"margin-right":"12px"}})):d("",!0)])),_:2},1024)])])]),r("div",ye,[r("div",he,[r("div",{class:"name",title:(null==(s=t.sceneTemplate)?void 0:s.name)||t.componentName},m((null==(l=t.sceneTemplate)?void 0:l.name)||t.componentName),9,je),r("div",{class:"user",title:(null==(o=t.sceneTemplate)?void 0:o.ownerName)||t.ownerName},m((null==(g=t.sceneTemplate)?void 0:g.ownerName)||t.ownerName),9,ke)]),r("div",we,[r("div",{id:t.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(n(!0),i(u,null,p(t.tags,((e,a)=>(n(),i("div",{key:a,class:"tag-item",title:e.tagName,style:w({backgroundColor:e.color})},m(e.tagName),13,be)))),128))],8,_e)])])])])),[[j,!Pe.value&&Be.value.length]])})),128)),Pe.value?(n(),f(b,{key:0,class:"loading-icon",spinning:Pe.value},null,8,["spinning"])):d("",!0),Pe.value||Be.value.length?d("",!0):(n(),f(C,{key:1}))])):(n(),i("div",Ce,[c(C,{title:"暂无权限"})])),r("div",Ne,[c(Ze,{total:De.total,"page-size-options":["12","20","30","40"],current:De.current,"page-size":De.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:Ve},null,8,["total","current","page-size","show-total"])])])]),c(E,{ref_key:"detailScreenRef",ref:Le},null,512),c(S,{ref_key:"editScreenRef",ref:Ke,onOk:qe},null,512),c(P,{ref_key:"useTempRef",ref:Ae,onOk:qe},null,512),c(G,{ref_key:"uploadFileRef",ref:Oe},null,512),c(N,{ref_key:"imgVideoPreviewRef",ref:Ue},null,512),c(_,{ref_key:"viewerArrowRef",ref:Se,current:Te.value,total:Re.value,onLeft:xe,onRight:Ie},null,8,["current","total"])])}}}),[["__scopeId","data-v-81aecdb4"]]);export{ze as default};
