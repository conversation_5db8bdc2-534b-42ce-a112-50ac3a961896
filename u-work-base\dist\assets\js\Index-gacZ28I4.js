import{d as e,r as a,w as s,am as t,b as l,o as i,e as o,c as r,F as n,ag as c,ab as u,ad as d,aa as v,J as m,a9 as p,ae as h,y as g,G as y,u as j,a5 as f,a4 as k}from"./@vue-HScy-mz9.js";import{d as w}from"./dayjs-CA7qlNSr.js";import b from"./Nodata-mmdoiDH6.js";import{p as C}from"./projectGallery-DFHuwUAq.js";import{c as _,d as z,g as x,b as N,f as S}from"./chart-CjrRHupv.js";import O from"./Reason-DacUNNUN.js";import P from"./AddExample-D3ATEwEY.js";import{C as R,b as U}from"./main-Djn9RDyT.js";import{q as $,r as I,S as E,I as T,x as q,w as W,B as J,e as L,d as A,g as G}from"./ant-design-vue-DYY9BtJq.js";import{_ as M}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./chart-template-DJui4TvV.js";import"./@vueup-CLVdhRgW.js";import"./quill-BBEhJLA6.js";import"./quill-delta-BsvWD3Kj.js";import"./lodash.clonedeep-BYjN7_az.js";import"./lodash.isequal-CYhSZ-LT.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";const Y={class:"func-module"},D={class:"tag-search"},H={key:0,class:"tag-content"},B=["onClick"],K={key:1,class:"tag-item"},F={key:1,class:"no-tag-content"},Q={class:"page-wrap"},X={class:"search-wrap"},Z={class:"search-content"},V={class:"search-item"},ee={class:"search-item"},ae={class:"search-btns"},se={class:"table-handle"},te={class:"content-list"},le={key:0,class:"list"},ie={class:"contain"},oe={class:"img-box"},re={class:"img-item"},ne=["src"],ce={class:"bottom-wrapper"},ue={class:"bottom-content"},de={class:"time"},ve={class:"hover-box"},me=["onClick"],pe=["onClick"],he=["onClick"],ge={key:0,class:"btn"},ye={key:1,class:"btn"},je={key:0,class:"btn"},fe={key:1,class:"btn"},ke={class:"control-icon"},we={class:"item-bottom"},be={class:"title"},Ce=["title"],_e=["title"],ze={class:"tag-wrapper"},xe=["id"],Ne=["title"],Se={key:1,class:"list"},Oe={class:"pagination-box"},Pe=M(e({__name:"Index",setup(e){const M=a(sessionStorage.getItem("XI_TONG_LOGO")||C),Pe=a(),Re=a({}),Ue=a(-1),$e=e=>{Ue.value=e,Ke()},Ie=a(),Ee=(e,a)=>{if(e){const e={status:0,id:a.id};N(e).then((e=>{200===e.code?(U("success","图表模板审批完成"),He()):U("error",e.message)}))}else Ie.value.init(a)},Te=e=>{let a="";return 0===e?a="正常":1===e?a="已下架":2===e?a="已删除":3===e?a="待审批":4===e&&(a="未通过"),a},qe=a([]);s((()=>Re.value),(()=>{Ke()}),{deep:!0});const We=a(!0);We.value=!0,_().then((e=>{We.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Re.value[e.id]=[]})):Re.value={},qe.value=e.data||[]):qe.value=[]})).catch((()=>{Re.value={},qe.value=[],We.value=!1}));const Je=a({total:0,current:1,pageSize:12,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),Le=(e,a)=>{Je.value=Object.assign(Je.value,{current:e,pageSize:a}),He()},Ae=e=>{let a="删除";return 0===e&&(a="下架"),a},Ge=a({keyWord:""}),Me=()=>{He()},Ye=a(!0),De=a([]),He=async()=>{var e;De.value=[],Ye.value=!0;const a=(null==(e=Object.values(Re.value))?void 0:e.flat(Infinity))||[],s={name:Ge.value.keyWord,pageNo:Je.value.current,pageSize:Je.value.pageSize,tagId:a},t=Ue.value;t>-1&&(s.status=t);const l=await x(s);if(Ye.value=!1,200===l.code){const{rows:e,pageNo:a,totalRows:s}=l.data;Je.value.total=s,Je.value.current=a,De.value=e}},Be=async(e,a)=>{try{const s=`${window.config.previewUrl}${e.data}`,t=document.createElement("a");t.href=s,t.setAttribute("download",`${a.filename}.zip`),document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(s)}catch(s){}},Ke=(e="")=>{Je.value.current=1,Je.value.pageSize=12,He()},Fe=(e,a)=>{Pe.value.init(e,a)},Qe=e=>`${window.config.previewUrl}${e.previewUrl}?width=400`;return(e,a)=>{var s;const C=$,_=I,x=E,Xe=T,Ze=q,Ve=W,ea=J,aa=L,sa=t("exception-outlined"),ta=A,la=G;return i(),l("div",Y,[o("div",D,[(null==(s=qe.value)?void 0:s.length)?(i(),l("div",H,[(i(!0),l(n,null,c(qe.value,(e=>{var a,s;return i(),l("div",{key:e.id,class:"tag-group"},[(null==(a=null==e?void 0:e.tags)?void 0:a.length)?(i(),l("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const s=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Re.value[e.id])===JSON.stringify(s)?Re.value[e.id]=[]:Re.value[e.id]=e.tags.map((e=>e.id))})(e)},d(e.groupName),9,B)):u("",!0),(null==(s=null==e?void 0:e.tags)?void 0:s.length)?(i(),l("div",K,[r(_,{value:Re.value[e.id],"onUpdate:value":a=>Re.value[e.id]=a,style:{width:"100%"}},{default:v((()=>[(i(!0),l(n,null,c(e.tags,(e=>(i(),l("div",{key:e.id,class:"tag-item-name"},[r(C,{value:e.id},{default:v((()=>[m(d(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):u("",!0)])})),128))])):(i(),l("div",F,[We.value?(i(),p(x,{key:0,class:"loading-icon",spinning:We.value},null,8,["spinning"])):u("",!0),We.value?u("",!0):(i(),p(b,{key:1,title:"请绑定标签"}))]))]),o("div",Q,[o("div",X,[o("div",Z,[o("div",V,[a[6]||(a[6]=o("span",{class:"search-label"},"图表名称",-1)),o("div",null,[r(Xe,{value:Ge.value.keyWord,"onUpdate:value":a[0]||(a[0]=e=>Ge.value.keyWord=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入图表名称",class:"search-input",onKeyup:a[1]||(a[1]=h((e=>Ke(Ge.value.keyWord)),["enter"]))},null,8,["value"])])]),o("div",ee,[a[11]||(a[11]=o("span",{class:"search-label"},"审批状态",-1)),o("div",null,[r(Ve,{ref:"select",value:Ue.value,"onUpdate:value":a[2]||(a[2]=e=>Ue.value=e),placeholder:"请选择审批状态",class:"search-select","allow-clear":"",onChange:$e},{default:v((()=>[r(Ze,{value:-1},{default:v((()=>a[7]||(a[7]=[m("全部")]))),_:1}),r(Ze,{value:3},{default:v((()=>a[8]||(a[8]=[m("待审批")]))),_:1}),r(Ze,{value:0},{default:v((()=>a[9]||(a[9]=[m("审批通过")]))),_:1}),r(Ze,{value:4},{default:v((()=>a[10]||(a[10]=[m("审批不通过")]))),_:1})])),_:1},8,["value"])])]),o("div",ae,[r(ea,{type:"primary",class:"search-btn",onClick:a[3]||(a[3]=e=>Ke(Ge.value.keyWord))},{default:v((()=>a[12]||(a[12]=[m(" 查询 ")]))),_:1})])]),o("div",se,[e.hasPerm("sys-chart:add")?(i(),p(ea,{key:0,type:"primary",class:"handle-btn",onClick:a[4]||(a[4]=e=>Fe("add",null))},{default:v((()=>a[13]||(a[13]=[m(" 新增图表模板 ")]))),_:1})):u("",!0)])]),o("div",te,[e.hasPerm("sys-chart:page")?(i(),l("div",le,[(i(!0),l(n,null,c(De.value,(s=>g((i(),l("div",{key:s.id,class:"item"},[o("div",ie,[o("div",oe,[o("div",re,[o("img",{src:Qe(s),alt:"图片",class:"img",onError:a[5]||(a[5]=e=>(e.target.src=M.value,e.target.style.width="auto"))},null,40,ne)]),o("div",ce,[o("div",ue,[o("div",de,"上传时间："+d(j(w)(s.createTime).format("YYYY-MM-DD HH:mm:ss")||""),1),0!==s.status?(i(),l("div",{key:0,class:f(["status",{fail:4===s.status,check:3===s.status,delete:2===s.status,unpush:1===s.status}])},d(Te(s.status)),3)):u("",!0)])]),o("div",ve,[o("div",{class:"btn",onClick:e=>(e=>{const{href:a}=R.resolve({path:"/preview/chartPreview",query:{id:e.id,path:e.url,width:e.width,height:e.height}});window.open(a,"_blank")})(s)},"预览",8,me),e.hasPerm("sys-chart:edit")?(i(),l("div",{key:0,class:"btn",onClick:e=>Fe("edit",s)},"编辑",8,pe)):u("",!0),0===s.status&&e.hasPerm("sys-chart:download-chart")?(i(),l("div",{key:1,class:"btn perview",onClick:e=>(e=>{z({id:e.id}).then((a=>{Be(a,e)}))})(s)},"下载",8,he)):u("",!0),r(aa,{placement:"topRight",title:`确认${Ae(s.status)}当前图表模板？`,onConfirm:e=>(async e=>{const{id:a,status:s}=e;if(0===s)N({id:a,status:"1"}).then((e=>{200===e.code?(U("success","图表模板下架完成"),He()):U("error",e.message)}));else{const e=await S({id:a});200===e.code?(U("success","图表模板删除成功"),He()):U("error",e.message)}})(s)},{default:v((()=>[0===s.status&&e.hasPerm("sys-chart:change-status")?(i(),l("div",ge,"下架")):u("",!0),0!==s.status&&e.hasPerm("sys-chart:batch-delete")?(i(),l("div",ye,"删除")):u("",!0)])),_:2},1032,["title","onConfirm"]),r(aa,{placement:"topRight",title:"确认审批通过？",okText:"通过",cancelText:"不通过",onConfirm:e=>Ee(!0,s),onCancel:e=>Ee(!1,s)},{default:v((()=>[e.hasPerm("sys-chart:change-status")&&3===s.status?(i(),l("div",je,"审批")):u("",!0),e.hasPerm("sys-chart:change-status")&&4===s.status?(i(),l("div",fe,"重新审批")):u("",!0)])),_:2},1032,["onConfirm","onCancel"]),o("div",ke,[r(ta,{placement:"top"},{title:v((()=>[o("span",null,d(s.failureCause),1)])),default:v((()=>[4===s.status?(i(),p(sa,{key:0,title:"未通过原因",style:{"margin-right":"5px"}})):u("",!0)])),_:2},1024)])])]),o("div",we,[o("div",be,[o("div",{class:"name",title:s.name},"["+d(s.width)+"*"+d(s.height)+"] "+d(s.name),9,Ce),o("div",{class:"user",title:s.createName},d(s.createName),9,_e)]),o("div",ze,[o("div",{id:s.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(i(!0),l(n,null,c(s.functionExampleTags,((e,a)=>(i(),l("div",{key:a,title:e.tagName,class:"tag-item",style:k({backgroundColor:e.color})},d(e.tagName),13,Ne)))),128))],8,xe)])])])])),[[y,!Ye.value&&De.value.length]]))),128)),Ye.value?(i(),p(x,{key:0,class:"loading-icon",spinning:Ye.value},null,8,["spinning"])):u("",!0),Ye.value||De.value.length?u("",!0):(i(),p(b,{key:1}))])):(i(),l("div",Se,[r(b,{title:"暂无权限"})])),o("div",Oe,[r(la,{total:Je.value.total,"page-size-options":["12","20","30","40"],current:Je.value.current,"page-size":Je.value.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:Le},null,8,["total","current","page-size","show-total"])])])]),r(P,{ref_key:"AddExampleRef",ref:Pe,onOk:Me},null,512),r(O,{ref_key:"reasonRef",ref:Ie,onOk:Me},null,512)])}}}),[["__scopeId","data-v-43d09af3"]]);export{Pe as default};
