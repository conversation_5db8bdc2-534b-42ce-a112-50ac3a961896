var e=Object.defineProperty,s=(s,a,t)=>((s,a,t)=>a in s?e(s,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[a]=t)(s,"symbol"!=typeof a?a+"":a,t);import{d as a,r as t,a as n,L as l,j as o,f as i,b as c,c as d,ab as r,aa as u,am as v,u as m,e as p,J as g,ae as h,F as f,ag as k,a9 as _,a5 as y,ad as w,a4 as b,h as I,o as j,p as C,w as T,y as S,G as O,E as A,x as M,ac as N}from"./@vue-HScy-mz9.js";import{g as E}from"./getThinyColor-DtFDxjua.js";import{u as x}from"./vue3-cookies-D4wQmYyh.js";import{b as G}from"./vue-router-BEwRlUkF.js";import{d as L}from"./dayjs-CA7qlNSr.js";import{g as R,r as U,a as z}from"./notice-C4zCLYzg.js";import{b as K,c as $,a as H,u as F,g as D,y as X,z as J,e as B,A as q,B as W,C as P}from"./main-Djn9RDyT.js";import{H as Y,I as Z,L as V,J as Q,K as ee,g as se,S as ae,B as te,M as ne,a as le,F as oe,_ as ie,b as ce,c as de}from"./ant-design-vue-DYY9BtJq.js";import{a8 as re,a9 as ue}from"./@ant-design-CA72ad83.js";import{_ as ve}from"./vue-qr-CB2aNKv5.js";import{l as me}from"./ti-B4l9Nygm.js";import{a as pe}from"./axios-7z2hFSF6.js";import{k as ge}from"./userManage-D6iEBY45.js";import{a as he,q as fe,s as ke}from"./department-CTxHSeTj.js";import _e from"./EditPsd-BBMpRTzv.js";import"./pinia-CheWBXuN.js";import"./tinycolor2-DJ_qK68I.js";import"./@babel-B4rXMRun.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./monaco-editor-K5t0WTxP.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./userManage-DLgtGpjc.js";const ye={timeout:3e4,timeoutObj:null,serverTimeoutObj:null,reset(e){clearTimeout(this.timeoutObj),clearTimeout(this.serverTimeoutObj),this.start(e)},start(e){this.timeoutObj&&clearTimeout(this.timeoutObj),this.serverTimeoutObj&&clearTimeout(this.serverTimeoutObj),this.timeoutObj=setTimeout((()=>{e.send("ping")}),this.timeout)}};class we{constructor(e){s(this,"lockReconnect"),s(this,"localUrl"),s(this,"wsUrl"),s(this,"globalCallback"),s(this,"userClose"),s(this,"ws"),this.lockReconnect=!1,this.localUrl="",this.wsUrl="",this.globalCallback=null,this.userClose=!1,this.createWebSocket(e,this.globalCallback)}createWebSocket(e,s){const a=this;a.wsUrl=e;try{a.ws=new WebSocket(a.localUrl+a.wsUrl),s&&(a.globalCallback=s),a.initEventHandle()}catch(t){a.reconnect(e)}}initEventHandle(){const e=this;e.ws.onopen=()=>{ye.start(e.ws)},e.ws.onclose=()=>{e.userClose||e.reconnect(e.wsUrl)},e.ws.onerror=()=>{e.userClose||e.reconnect(e.wsUrl)},e.ws.onmessage=()=>{e.getWebSocketMsg(e.globalCallback)}}reconnect(e){const s=this;s.lockReconnect||(s.lockReconnect=!0,setTimeout((()=>{s.createWebSocket(e,s.globalCallback),s.lockReconnect=!1}),3e4))}webSocketSendMsg(e){const s=setInterval((()=>{1===this.ws.readyState&&(clearInterval(s),this.ws.send(JSON.stringify(e)))}),1e3)}getWebSocketMsg(e){this.globalCallback=e,this.ws.onmessage=s=>{s&&"ping"===s.data?ye.reset(this.ws):e&&e(s)}}closeSocket(){const e=this;e.ws&&(e.userClose=!0,e.ws.close())}}const be={class:"header"},Ie={class:"search-content"},je={class:"notice_page"},Ce={class:"news_card"},Te={key:0},Se={class:"download"},Oe={style:{color:"var(--theme-color)"}},Ae=ve(a({__name:"NoticeIcon",setup(e){const{cookies:s}=x(),a=t(null),C=n({visible:!1,loading:!1,unReadCount:0,newsData:[],newsTotal:0,showNews:!1,newsInfo:{},downTime:0,pageNo:1,pageSize:5,url:window.config.downloadUrl}),T=()=>{C.pageNo=1,C.newsData=[],C.visible?C.loading=!1:O(),C.visible=!C.visible},S=t(""),O=()=>{C.loading=!0;const e={pageNo:C.pageNo,pageSize:C.pageSize,message:S.value};R(e).then((e=>{C.loading=!1,200===e.code&&(C.newsData=e.data.rows,C.unReadCount=e.data.unReadCount,C.newsTotal=e.data.totalRows)})).finally((()=>{C.loading=!1}))};O();const A=e=>{0===e.status&&M({messageId:e.id}),C.newsInfo=e,C.showNews=!0},M=async e=>{try{await U(e),O()}catch(s){}},N=e=>L(e).format("YYYY-MM-DD HH:mm:ss"),E=()=>{z({}).then((e=>{200===e.code&&(O(),K("success","全部消息已读"))})).catch((e=>{K("error",e.message)}))},G=(e,s)=>{C.pageNo=e,C.pageSize=s,O()},$=()=>{C.showNews=!1},H=()=>{setTimeout((()=>{C.downTime-=1,C.downTime>0&&H()}),1e3)},{visible:F,loading:D,unReadCount:X,newsData:J,newsTotal:B,showNews:q,newsInfo:W,downTime:P}=l(C);let oe=null;(()=>{const e=s.get("ACCESS_TOKEN_U");oe=new we(`${window.config.wsApi}?type=message&tenant=master&token=${e}`)})();const ie=e=>{const s=`open${Date.now()}`,a=N(e.createTime),t=e.message&&e.message.length>16?`${e.message.substr(0,15)}...`:e.message;le.open({message:"消息通知",icon:()=>I(ue),description:()=>I("div",null,[I("p",null,`${t}`),I("p",null,`${a}`)]),btn:()=>I(te,{type:"primary",size:"small",onClick:()=>A(e)},(()=>I("span",null,"消息详情"))),key:s})};oe.getWebSocketMsg((e=>{const s=JSON.parse(e.data);O(),ie(s)})),o((()=>{oe.closeSocket()}));const ce=t();return i((()=>{document.addEventListener("click",(e=>{const s=document.querySelector(".noticeModal");ce.value&&!ce.value.contains(e.target)&&(s&&s.contains(e.target)||(F.value=!1,S.value=""))})),a.value&&window.getComputedStyle(a.value).backgroundColor})),(e,s)=>{const t=v("bell-outlined"),n=Y,l=Z,o=ee,i=Q,I=V,M=se,x=ae,L=te,R=ne;return j(),c("div",{ref_key:"notice",ref:ce,class:"notice"},[d(n,{count:m(X)},{default:u((()=>[d(t,{class:"notice-icon",onClick:T})])),_:1},8,["count"]),m(F)?(j(),c("div",{key:0,ref_key:"myElement",ref:a,class:"content"},[d(x,{spinning:m(D)},{default:u((()=>[p("div",be,[s[3]||(s[3]=p("div",{class:"head"},"消息通知",-1)),p("div",{class:"read_all",onClick:E},[d(m(re),{style:{"margin-right":"2px"}}),s[2]||(s[2]=g("全部已读"))])]),p("div",Ie,[s[4]||(s[4]=p("span",{class:"search-label"},"消息查询",-1)),p("div",null,[d(l,{value:S.value,"onUpdate:value":s[0]||(s[0]=e=>S.value=e),valueModifiers:{trim:!0},placeholder:"请输入查询内容",class:"search-input",onKeyup:h(O,["enter"])},null,8,["value"])])]),d(I,{split:!1,class:"list"},{default:u((()=>[(j(!0),c(f,null,k(m(J),((e,s)=>(j(),_(i,{key:s,class:"containerList",onClick:s=>A(e)},{default:u((()=>[d(o,{description:N(e.createTime)},{title:u((()=>[p("div",{class:y(["title",["title",0===e.status?"non-read":"read"]])},w(e.message),3)])),_:2},1032,["description"]),p("div",{class:"read_status",style:b({color:0===e.status?"#f00":"transparent"})},"●",4)])),_:2},1032,["onClick"])))),128))])),_:1}),p("div",je,[d(M,{size:"small",total:m(B),"show-less-items":!0,"page-size":5,"show-size-changer":!1,onChange:G},null,8,["total"])])])),_:1},8,["spinning"])],512)):r("",!0),d(R,{footer:null,title:"消息详情",open:m(q),"wrap-class-name":"noticeModal",onCancel:$},{default:u((()=>[p("div",Ce,[p("div",null,w(m(W).message),1),m(W).attachmentAddress?(j(),c("div",Te,[p("span",Se,[d(L,{type:"link",disabled:0!==m(P),class:"downbtn",onClick:s[1]||(s[1]=e=>(window.location.href=C.url+C.newsInfo.attachmentAddress,C.downTime=3,void H()))},{default:u((()=>[p("span",Oe,w(0===m(P)?"":`${m(P)}s `),1),s[5]||(s[5]=g("下载资源包 "))])),_:1},8,["disabled"])])])):r("",!0)])])),_:1},8,["open"])],512)}}}),[["__scopeId","data-v-8f9efdc8"]]),Me={class:"header"},Ne={class:"logo"},Ee=["src"],xe={class:"content-wrapper"},Ge={key:0,class:"content"},Le=["onClick"],Re={class:"user"},Ue=ve(a({__name:"Header",setup(e){const s=$(),{cookies:a}=x(),n=H(),{userInfo:l}=F(),o=t(sessionStorage.getItem("XI_TONG_BIAO_TI")||"");null===sessionStorage.getItem("XI_TONG_BIAO_TI")&&pe.get(`${window.config.appApi}/edtap/sys-config/open?code=XI_TONG_BIAO_TI`,{headers:{"X-Security-FreshToken":D()}}).then((e=>{var s,a;const t="无"!==(null==(s=e.data)?void 0:s.data)?null==(a=e.data)?void 0:a.data:"";sessionStorage.setItem("XI_TONG_BIAO_TI",t),o.value=t,document.title=`${o.value}`})),document.title=`${o.value}`;const h=t(sessionStorage.getItem("XI_TONG_LOGO")||me);null===sessionStorage.getItem("XI_TONG_LOGO")&&pe.get(`${window.config.appApi}/edtap/sys-config/open?code=DENG_LU_YE_LOGO`,{headers:{"X-Security-FreshToken":D()}}).then((e=>{var s,a,t;const n="无"!==(null==(s=e.data)?void 0:s.data)?null==(a=e.data)?void 0:a.data:"";sessionStorage.setItem("XI_TONG_LOGO",null==(t=e.data)?void 0:t.data),h.value=n||me;let l=document.querySelector('link[rel="icon"]');l&&(l.href=h.value)}));let I=document.querySelector('link[rel="icon"]');I&&(I.href=h.value);const A=a.get("ACCESS_TOKEN_U"),M=async()=>{await se(),window.open(ee.value,"_blank")},N=()=>{W.value=!1,n.updateActiveApp({}),n.updateActiveSecondMenu({}),n.updateActiveThirdMenu({}),n.thirdMenus=[],n.secondMenus=[],R.push("/personal")},L=()=>{R.push("/login")},R=G(),U=C((()=>n.userApps.filter((e=>e.visible)))),z=C((()=>n.activeApp)),W=t(!1),P=C((()=>!a.get("ACCESS_TOKEN_U"))),Y=()=>{W.value=!1,X().then((e=>{200===e.code?(J(),R.push("/login")):K("error",e.message)}))},Z=()=>{W.value=!W.value},V=t(),Q=t(),ee=t(""),se=async()=>{var e,s;const a=await B({code:"SHI_YONG_ZHI_NAN"});ee.value=(null==(s=null==(e=a.data)?void 0:e.rows[0])?void 0:s.value)||"https://wiki.uino.com/book/653a4c678ea0ba892399029b"};return T((()=>s.updateSetting),(e=>{1===e&&(o.value=sessionStorage.getItem("XI_TONG_BIAO_TI")||"",h.value=sessionStorage.getItem("XI_TONG_LOGO")||me)})),i((()=>{document.addEventListener("click",(e=>{Q.value&&!Q.value.contains(e.target)&&V.value&&!V.value.contains(e.target)&&(W.value=!1)}))})),(e,s)=>{var a,t,o;const i=v("router-link"),I=te,C=v("user-outlined"),T=v("file-unknown-outlined"),x=v("logout-outlined");return j(),c("div",Me,[p("div",Ne,[d(i,{to:"/home",target:"_blank"},{default:u((()=>[p("img",{src:h.value,class:"header-logo",alt:"header-logo"},null,8,Ee),s[0]||(s[0]=p("span",{class:"plat-name"},"工作台",-1))])),_:1})]),p("div",xe,[U.value.length?(j(),c("div",Ge,[(j(!0),c(f,null,k(U.value,(e=>(j(),c("div",{key:e.code,class:y(["content-item",e.code===z.value.code?"active":""]),onClick:s=>(e=>{var s;(null==(s=z.value)?void 0:s.code)!==e.code&&("KAI_FA_KONG_ZHI_TAI"!==e.code?(n.checkedEnterprise={},q(e)):n.activeApp=e)})(e)},[p("span",{class:y(["name",e.code===z.value.code?"active":""])},w(e.name),3)],10,Le)))),128))])):r("",!0)]),p("div",Re,[P.value?(j(),_(I,{key:0,type:"primary",onClick:L},{default:u((()=>s[1]||(s[1]=[g(" 登录 ")]))),_:1})):r("",!0),"KAI_FA_KONG_ZHI_TAI"===(null==(a=z.value)?void 0:a.code)?(j(),c("div",{key:1,style:b({"--bor-color":m(E)(.1)}),class:"company-name"},"当前团队："+w((null==(t=m(n).checkedEnterprise)?void 0:t.name)||"-"),5)):r("",!0),m(A)?(j(),_(Ae,{key:2,class:"head-notice-icon"})):r("",!0),m(A)?(j(),c("div",{key:3,ref_key:"imgRef",ref:Q,class:"user-name",onClick:Z},w(null==(o=m(l))?void 0:o.name),513)):r("",!0),S(p("div",{ref_key:"userRef",ref:V,class:"user-info keep-px"},[p("div",{class:"header-item",onClick:N},[d(C,{class:"handel-icon"}),s[2]||(s[2]=p("span",{class:"name"},"个人中心",-1))]),p("div",{class:"header-item",onClick:M},[d(T,{class:"handel-icon"}),s[3]||(s[3]=p("span",{class:"name"},"使用指南",-1))]),p("div",{class:"header-item",onClick:Y},[d(x,{class:"handel-icon"}),s[4]||(s[4]=p("span",{class:"name"},"退出登录",-1))])],512),[[O,W.value]])])])}}}),[["__scopeId","data-v-34643912"]]),ze=ve(a({__name:"commonDowmIcon",props:{down:{type:Boolean,default:!0}},setup:e=>(s,a)=>(j(),c("div",{class:y(["common-down-icon",{down:e.down}])},a[0]||(a[0]=[p("i",null,null,-1)]),2))}),[["__scopeId","data-v-b17f5aa0"]]),Ke=ve(a({__name:"JoinTeam",emits:["ok"],setup(e,{expose:s,emit:a}){const l=a,o=t(!1),i=t(!1),c=t(""),r=t(),v=n({invitationCode:""}),m={invitationCode:[{required:!0,message:"请输入邀请码"}]},p=()=>{var e;null==(e=r.value)||e.resetFields(),i.value=!1,o.value=!1};return s({init:e=>{o.value=!0,c.value=e}}),(e,s)=>{const a=Z,t=de,n=ce,g=ie,h=oe,f=ae,k=ne;return j(),_(k,{title:"加入团队","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",width:400,open:o.value,"mask-closable":!1,"confirm-loading":i.value,"ok-text":"确认","cancel-text":"取消",onCancel:p,onOk:s[1]||(s[1]=e=>{return i.value=!0,void(null==(s=r.value)||s.validate().then((()=>{ge(v.invitationCode).then((e=>{const{code:s,data:a,message:t}=e;200===s?(p(),l("ok",c.value)):K("error",`加入团队失败：${t}`)}))})).finally((()=>{i.value=!1})).catch((e=>{i.value=!1})));var s})},{default:u((()=>[d(f,{spinning:i.value},{default:u((()=>[d(h,{ref_key:"formAddRef",ref:r,model:v,rules:m,"label-align":"left"},{default:u((()=>[d(g,{gutter:24},{default:u((()=>[d(n,{md:24,sm:24},{default:u((()=>[d(t,{name:"invitationCode",label:"邀请码:","has-feedback":""},{default:u((()=>[d(a,{value:v.invitationCode,"onUpdate:value":s[0]||(s[0]=e=>v.invitationCode=e),placeholder:"请输入邀请码"},null,8,["value"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-56155979"]]),$e={class:"second-menu-wrap"},He={key:0,class:"second-menu"},Fe={key:0,class:"menus"},De=["onClick"],Xe={class:"left"},Je=["onClick"],Be={key:1,class:"my-group"},qe=["onClick"],We={class:"left-group"},Pe={class:"name"},Ye={key:1,class:"disable"},Ze={class:"control-btns"},Ve=["onClick"],Qe={key:1,class:"default"},es=ve(a({__name:"SecondMenu",setup(e){const s=C((()=>a.activeApp)),a=H(),n=C((()=>{var e;return a.secondMenus.forEach((e=>{var s;(null==(s=a.activeSecondMenu)?void 0:s.pid)===e.id&&(e.expand=!0)})),a.devMenus.length&&0===(null==(e=a.checkedEnterprise)?void 0:e.ueStatus)?a.secondMenus.filter((e=>a.devMenus.some((s=>s.id===e.id)))):a.secondMenus})),l=C((()=>a.activeSecondMenu)),o=C((()=>a.activeThirdMenu));T((()=>l.value),(e=>{e&&"{}"!==JSON.stringify(e)&&n.value.forEach((s=>{e.pid===s.id?s.expand=!0:s.expand=!1}))}));const u=e=>{var s;return{maxHeight:(e.expand?44*((null==(s=e.children)?void 0:s.length)||0)*(window.innerWidth/1440)+"px":"0px")+" "}},h=(e,s)=>{var t,o,i,c,d;if(1!==(null==(t=a.checkedEnterprise)?void 0:t.status))if(0===(null==(o=e.meta)?void 0:o.type)&&(null==(i=e.children)?void 0:i.length))"second"===s?(e.expand=!e.expand,(e=>{n.value.forEach((s=>{s.id!==e.id&&e.pid!==s.id&&(s.expand=!1)}))})(e)):"third"===s&&(a.updateActiveSecondMenu({}),a.thirdMenus=e.children);else if(1===(null==(c=e.meta)?void 0:c.type)){if((null==(d=l.value)?void 0:d.id)===e.id)return;W(e,"second")}},I=t(!1),M=()=>{I.value=!I.value},N=t([]),x=async()=>{var e;const t=await he();if(200===t.code)if(N.value=t.data||[],null==(e=t.data)?void 0:e.length){s.value&&q(s.value);const e=t.data.find((e=>1===e.acquiesceFlag));if(a.checkedEnterprise=e||t.data[0],1===a.checkedEnterprise.status)return;G(e)}else U.value.init("1"),a.secondMenus=[],a.thirdMenus=[],P.push("/welcome");else K("error",t.message)},G=async e=>{var s,t,n;const l={enterPriseId:null==e?void 0:e.id,sysCategoryId:a.sysCategoryId},o=await fe(l);if(200!==o.code)return void K("error",o.message);a.devAppList=(null==(s=o.data)?void 0:s.appList)||[],a.devButtonList=(null==(t=o.data)?void 0:t.buttonList)||[],a.devMenus=(null==(n=o.data)?void 0:n.menus)||[];const i=a.secondMenus.find((e=>{var s;return 1===(null==(s=e.meta)?void 0:s.type)}));i&&W(i,"second"),a.updateActiveThirdMenu({})},L=t(),R=t();i((()=>{document.addEventListener("click",(e=>{R.value&&!R.value.contains(e.target)&&L.value&&!L.value.contains(e.target)&&(I.value=!1)}))})),T((()=>s.value),(e=>{"KAI_FA_KONG_ZHI_TAI"===(null==e?void 0:e.code)&&x()}),{immediate:!0});const U=t(),z=e=>{x()};return(e,t)=>{var i,C;const T=v("IconFont"),x=v("PlusCircleOutlined");return j(),c("div",$e,[(null==(i=n.value)?void 0:i.length)>0?(j(),c("div",He,[n.value.length?(j(),c("div",Fe,[(j(!0),c(f,null,k(n.value,(e=>{var s,a,t,n;return j(),c("div",{key:e.id,class:"second-menu-wrap"},[p("div",{class:y(["second-menu-tab",{active:(null==(s=l.value)?void 0:s.id)===e.id||(null==(a=o.value)?void 0:a.pid)===e.id}]),style:b({"--active-menu-bg":m(E)(.1)}),onClick:s=>h(e,"second")},[p("div",Xe,[d(T,{class:"menu-icon-font",type:`${e.meta.icon||"icon-system"}`},null,8,["type"]),g(w(e.meta.title),1)]),(null==(t=e.children)?void 0:t.length)?(j(),_(ze,{key:0,down:!!e.expand,class:"menu-icon-right"},null,8,["down"])):r("",!0)],14,De),(null==(n=e.children)?void 0:n.length)?(j(),c("div",{key:0,style:b(u(e)),class:"sub-menus"},[(j(!0),c(f,null,k(e.children,(e=>{var s,a;return j(),c("div",{key:e.id,style:b({"--active-menu-bg":m(E)(.1)}),class:y(["sub-menu-tab",{active:(null==(s=l.value)?void 0:s.id)===e.id||(null==(a=o.value)?void 0:a.pid)===e.id}]),onClick:s=>h(e,"third")},[d(T,{class:"menu-icon-font",type:`${e.meta.icon||"icon-system"}`},null,8,["type"]),p("div",null,w(e.meta.title),1)],14,Je)})),128))],4)):r("",!0)])})),128))])):r("",!0),"KAI_FA_KONG_ZHI_TAI"===(null==(C=s.value)?void 0:C.code)?(j(),c("div",Be,[p("div",{ref_key:"imgRef",ref:R,class:"group-name",onClick:M},[d(T,{class:"group-icon-font",type:"icon-tuandui4"}),t[1]||(t[1]=g("我的团队"))],512),S(p("div",{class:"group-list",ref_key:"userRef",ref:L},[t[3]||(t[3]=p("div",{class:"group-list-title"},"我的团队",-1)),(j(!0),c(f,null,k(m(N),((e,s)=>(j(),c("div",{key:s,class:y(["group-list-item",{active:m(a).checkedEnterprise.id===e.id}]),onClick:s=>(e=>{a.checkedEnterprise.id!==e.id&&1!==e.status&&(a.checkedEnterprise=e,G(e))})(e)},[p("div",We,[p("div",Pe,w(e.name),1),m(a).checkedEnterprise.id===e.id?(j(),c("div",{key:0,class:y(["current",{disableCls:1===e.status}]),style:b({"--bor-color":m(E)(.1)})},"当前",6)):r("",!0),1===e.status?(j(),c("div",Ye,"已禁用")):r("",!0)]),p("div",Ze,[e.acquiesceFlag||1===e.status?r("",!0):(j(),c("div",{key:0,class:"btn",onClick:A((s=>(e=>{ke({enterpriseId:e.id}).then((async e=>{if(200===e.code){K("success","设置成功");const e=await he();if(200!==e.code)return void K("error",e.message);N.value=e.data||[]}else K("error",e.message)}))})(e)),["stop"]),style:b({"--color":m(E)(.8)})},"设为默认",12,Ve)),1===e.acquiesceFlag?(j(),c("div",Qe,"默认")):r("",!0)])],10,qe)))),128)),p("div",{class:"add-box",onClick:t[0]||(t[0]=e=>{U.value.init("2")})},[d(x,{style:{fontSize:"16px"},class:"add-icon"}),t[2]||(t[2]=p("div",{class:"name"},"加入团队",-1))])],512),[[O,m(I)]])])):r("",!0)])):r("",!0),d(Ke,{ref_key:"JoinTeamRef",ref:U,onOk:z},null,512)])}}}),[["__scopeId","data-v-c6a6d166"]]),ss={key:0,class:"third-menu"},as={class:"third-menu-wrap"},ts={key:0,class:"content"},ns=["onClick"],ls={class:"name"},os=ve(a({__name:"ThirdMenu",setup(e){const s=H(),a=C((()=>s.thirdMenus)),t=C((()=>s.activeThirdMenu));T((()=>s.thirdMenus),(e=>{if(e&&e.length>0){let s=e[0];const a=sessionStorage.getItem("activeThirdMenu")?JSON.parse(sessionStorage.getItem("activeThirdMenu")):null;a&&"{}"!==JSON.stringify(a)&&e.map((e=>e.id)).indexOf(a.id)>-1&&(s=a),W(s,"third")}}));return(e,s)=>a.value.length>0?(j(),c("div",ss,[p("div",as,[a.value.length?(j(),c("div",ts,[(j(!0),c(f,null,k(a.value,(e=>(j(),c("div",{key:e.id,class:y(["content-item",e.id===t.value.id?"active":""]),onClick:s=>(e=>{var s;(null==(s=t.value)?void 0:s.id)!==e.id&&W(e,"third")})(e)},[p("span",ls,w(e.meta.title),1)],10,ns)))),128))])):r("",!0)])])):r("",!0)}}),[["__scopeId","data-v-23aee1a4"]]),is={class:"layout"},cs={class:"layout-content"},ds={class:"layout-right-content"},rs={class:"router-wrap"},us=ve(a({__name:"Index",setup(e){const{userInfo:s}=F(),a=t();return i((()=>{var e;"{}"!==JSON.stringify(s)&&-1===(null==s?void 0:s.status)&&"LDAP"!==(null==(e=s.loginUserDetailInfo)?void 0:e.deptName)&&a.value.init(!0)})),(e,s)=>{const t=v("router-view");return j(),c("div",is,[d(Ue),p("div",cs,[d(M,{name:"fade"},{default:u((()=>[d(es)])),_:1}),p("div",ds,[d(M,{name:"fade"},{default:u((()=>[d(os)])),_:1}),p("div",rs,[d(t,null,{default:u((({Component:e})=>[d(M,{name:"fade-slide",mode:"out-in"},{default:u((()=>[(j(),_(N(e)))])),_:2},1024)])),_:1})])])]),d(_e,{ref_key:"editPsdRef",ref:a},null,512)])}}}),[["__scopeId","data-v-f7dd474d"]]);export{us as default};
