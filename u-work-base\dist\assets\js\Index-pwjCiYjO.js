import{d as e,r as a,j as t,w as l,am as s,b as o,o as n,e as i,c as r,F as u,ag as c,ab as v,ad as p,aa as d,J as m,a9 as g,y as h,ae as f,G as j,a5 as k,a4 as y}from"./@vue-HScy-mz9.js";import"./viewerjs-_Br7E8dP.js";import{p as w}from"./projectGallery-DFHuwUAq.js";import{S as b,A as _}from"./AiSearch-B-73Nj9G.js";import C from"./Nodata-mmdoiDH6.js";import{I as z,u as S}from"./useCookies-BRWvy2S3.js";import{V as N}from"./ViewerArrow-r3R4USz-.js";import{c as I,d as P,s as $}from"./projectGallery-xT8wgNPG.js";import R from"./EditChart-D3EUNcEB.js";import{u as O,e as U,b as x}from"./main-Djn9RDyT.js";import A from"./UploadFile-sV7_smQz.js";import E from"./Check-aZI97oUG.js";import G from"./Reason-Ca_oga17.js";import{q as J,r as T,S as L,I as q,x as F,w as H,B as V,s as B,e as D,d as K,g as M}from"./ant-design-vue-DYY9BtJq.js";import{_ as Q}from"./vue-qr-CB2aNKv5.js";import"./axios-7z2hFSF6.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./vue3-cookies-D4wQmYyh.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";const W={class:"func-module"},X={class:"tag-search"},Z={key:0,class:"tag-content"},Y=["onClick"],ee={key:1,class:"tag-item"},ae={key:1,class:"no-tag-content"},te={class:"page-wrap"},le={class:"search-wrap"},se={class:"search-content"},oe={class:"search-item"},ne={class:"search-item"},ie={class:"search-item"},re={class:"search-btns"},ue={class:"table-handle"},ce={class:"content-list"},ve={key:0,class:"list"},pe={class:"contain"},de={class:"img-box"},me=["src"],ge={class:"bottom-wrapper"},he={class:"bottom-content"},fe={class:"hover-box"},je=["onClick"],ke=["onClick"],ye=["onClick"],we=["onClick"],be=["onClick"],_e={class:"control-icon"},Ce={class:"item-bottom"},ze={class:"title"},Se=["title"],Ne=["title"],Ie={class:"tag-wrapper"},Pe=["id"],$e=["title"],Re={key:1,class:"list"},Oe={class:"pagination-box"},Ue=Q(e({__name:"Index",setup(e){const Q=O(),Ue=a(sessionStorage.getItem("XI_TONG_LOGO")||w),xe=a("");U({code:"AI_SHI_TU"}).then((e=>{var a,t;const l=null==(t=null==(a=e.data)?void 0:a.rows[0])?void 0:t.value;xe.value="无"!==l?l:""})).catch((()=>{xe.value=""}));const Ae=e=>{ea.value.current=1,ea.value.pageSize=12,oa()},Ee=a(),Ge=a(0),Je=a(0),Te=()=>{},Le=()=>{};t((()=>{}));const qe=a(),Fe=()=>{qe.value.init()},He=a(),Ve=e=>{He.value.init(e)},Be=e=>{let a="";return 0===e?a="正常":1===e?a="待审批":2===e&&(a="未通过"),a},De=a(),Ke=a({likeContent:"",tagIds:[],source:1,approve:-1}),Me=a({}),Qe=a([]),We=a(!0);We.value=!0,I().then((e=>{We.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{Me.value[e.id]=[]})):Me.value={},Qe.value=e.data||[]):Qe.value=[]})).catch((()=>{Me.value={},Qe.value=[],We.value=!1}));l((()=>Me.value),(()=>{na()}),{deep:!0});const Xe=a(),Ze=a("");U({code:"SCREEN_UI_IP"}).then((e=>{var a,t;const l=(null==(t=null==(a=e.data)?void 0:a.rows[0])?void 0:t.value)||"";Ze.value=l})).catch((()=>{Ze.value=""}));const Ye=a(),ea=a({total:0,current:1,pageSize:12,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),aa=(e,a)=>{ea.value=Object.assign(ea.value,{current:e,pageSize:a}),oa()},ta=()=>{oa()},la=a(!0),sa=a([]),oa=async e=>{var a;sa.value=[],la.value=!0;const t=(null==(a=Object.values(Me.value))?void 0:a.flat(Infinity))||[],l={pageNo:ea.value.current,pageSize:ea.value.pageSize,name:Ke.value.likeContent,tagIds:t,status:-1===Ke.value.approve?null:Ke.value.approve,type:1===Ke.value.source||3===Ke.value.source?"0":"1"};3===Ke.value.source&&(l.createUser=Q.userInfo.id);const s=await P(l);if(la.value=!1,200===s.code){const{rows:e,totalRows:a,totalPage:t}=s.data;ea.value.total=a,sa.value=e||[]}else x("error",s.message)},na=()=>{ea.value.total=0,ea.value.current=1,ea.value.pageSize=12,oa()};return(e,a)=>{var t,l;const w=J,I=T,P=L,O=q,U=F,Q=H,ia=V,ra=B,ua=D,ca=s("exception-outlined"),va=K,pa=M;return n(),o("div",W,[i("div",X,[(null==(t=Qe.value)?void 0:t.length)?(n(),o("div",Z,[(n(!0),o(u,null,c(Qe.value,(e=>{var a,t;return n(),o("div",{key:e.id,class:"tag-group"},[(null==(a=null==e?void 0:e.tags)?void 0:a.length)?(n(),o("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const t=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(Me.value[e.id])===JSON.stringify(t)?Me.value[e.id]=[]:Me.value[e.id]=e.tags.map((e=>e.id))})(e)},p(e.groupName),9,Y)):v("",!0),(null==(t=null==e?void 0:e.tags)?void 0:t.length)?(n(),o("div",ee,[r(I,{value:Me.value[e.id],"onUpdate:value":a=>Me.value[e.id]=a,style:{width:"100%"}},{default:d((()=>[(n(!0),o(u,null,c(e.tags,(e=>(n(),o("div",{key:e.id,class:"tag-item-name"},[r(w,{value:e.id},{default:d((()=>[m(p(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):v("",!0)])})),128))])):(n(),o("div",ae,[We.value?(n(),g(P,{key:0,class:"loading-icon",spinning:We.value},null,8,["spinning"])):v("",!0),We.value?v("",!0):(n(),g(C,{key:1,title:"请绑定标签"}))]))]),i("div",te,[i("div",le,[i("div",se,[i("div",oe,[a[8]||(a[8]=i("span",{class:"search-label"},"图表名称",-1)),i("div",null,[r(O,{value:Ke.value.likeContent,"onUpdate:value":a[0]||(a[0]=e=>Ke.value.likeContent=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入图表名称",class:"search-input",onKeyup:a[1]||(a[1]=f((e=>na()),["enter"]))},null,8,["value"])])]),i("div",ne,[a[11]||(a[11]=i("span",{class:"search-label"},"数据源",-1)),i("div",null,[r(Q,{value:Ke.value.source,"onUpdate:value":a[2]||(a[2]=e=>Ke.value.source=e),placeholder:"请选择数据源",class:"search-select",onChange:a[3]||(a[3]=e=>(Ke.value.approve=-1,void na()))},{default:d((()=>[r(U,{value:1},{default:d((()=>a[9]||(a[9]=[m("公共资源")]))),_:1}),r(U,{value:2},{default:d((()=>a[10]||(a[10]=[m("项目资源")]))),_:1})])),_:1},8,["value"])])]),h(i("div",ie,[a[16]||(a[16]=i("span",{class:"search-label"},"审批状态",-1)),i("div",null,[r(Q,{value:Ke.value.approve,"onUpdate:value":a[4]||(a[4]=e=>Ke.value.approve=e),"allow-clear":"",placeholder:"请选择审批状态",class:"search-select",onChange:a[5]||(a[5]=e=>na())},{default:d((()=>[r(U,{value:-1},{default:d((()=>a[12]||(a[12]=[m("全部")]))),_:1}),r(U,{value:0},{default:d((()=>a[13]||(a[13]=[m("审批通过")]))),_:1}),r(U,{value:1},{default:d((()=>a[14]||(a[14]=[m("待审批")]))),_:1}),r(U,{value:2},{default:d((()=>a[15]||(a[15]=[m("未通过")]))),_:1})])),_:1},8,["value"])])],512),[[j,1===Ke.value.source]]),i("div",re,[r(ia,{type:"primary",class:"search-btn",onClick:a[6]||(a[6]=e=>oa())},{default:d((()=>a[17]||(a[17]=[m(" 查询 ")]))),_:1}),xe.value?(n(),g(ra,{key:0,trigger:"click",destroyTooltipOnHide:!0,placement:"bottom"},{content:d((()=>[r(_,{onAiSearch:Ae,url:xe.value},null,8,["url"])])),default:d((()=>[r(b)])),_:1})):v("",!0)])]),i("div",ue,[e.hasPerm("annotate-component:upload-component")?(n(),g(ia,{key:0,type:"primary",class:"handle-btn",onClick:Fe},{default:d((()=>a[18]||(a[18]=[m(" 导入图表模板 ")]))),_:1})):v("",!0)])]),i("div",ce,[e.hasPerm("annotate-component:page-component")?(n(),o("div",ve,[(n(!0),o(u,null,c(sa.value,(t=>{var l,s,m;return h((n(),o("div",{key:t.id,class:"item"},[i("div",pe,[i("div",de,[i("img",{src:(s=t.filePath,m=t.snapShot,Ze.value?`${window.config.baseUrl}${m}?width=400`:`${window.config.baseUrl}${s}${m}?width=400`),loading:"lazy",alt:"图片",class:"img",onError:a[7]||(a[7]=e=>(e.target.src=Ue.value,e.target.style.width="auto"))},null,40,me),i("div",ge,[i("div",he,[i("div",{class:k(["status",{fail:2===t.status,normal:0===t.status,unpush:null===t.status}])},p(Be(t.status)),3)])]),i("div",fe,[i("div",{class:"btn",onClick:e=>(e=>{Ze.value?(S(),window.open(`${Ze.value}/nanshan/preview/component/${e.id}?random=${Date.now()}`,"_blank",`width=${window.innerWidth},height=${window.innerHeight-100},top=100,left=100,z-look=yes`)):window.open("_blank").location=`/screen/index.html?path=${e.filePath}&type=chart`})(t)},"预览",8,je),e.hasPerm("annotate-component:scene-bind")?(n(),o("div",{key:0,class:"btn",onClick:e=>(e=>{const a={...e};Ye.value.init(a)})(t)},"编辑",8,ke)):v("",!0),e.hasPerm("annotate-component:modify-component")&&0===t.status?(n(),o("div",{key:1,class:"btn",onClick:e=>(e=>{De.value.init(e)})(t)},"下架",8,ye)):v("",!0),e.hasPerm("annotate-component:component-bind")&&e.hasPerm("annotate-component:modify-component")&&1===t.status?(n(),o("div",{key:2,class:"btn",onClick:e=>Ve(t)},"审批",8,we)):v("",!0),e.hasPerm("annotate-component:component-bind")&&e.hasPerm("annotate-component:modify-component")&&2===t.status?(n(),o("div",{key:3,class:"btn",onClick:e=>Ve(t)}," 重新审批 ",8,be)):v("",!0),!e.hasPerm("annotate-component:delete-component")||1!==t.status&&2!==t.status?v("",!0):(n(),g(ua,{key:4,placement:"topRight",title:"确认删除？",onConfirm:e=>(async e=>{200===(await $([e.id])).code&&(x("success","图表删除成功"),oa())})(t)},{default:d((()=>a[19]||(a[19]=[i("div",{class:"btn"},"删除",-1)]))),_:2},1032,["onConfirm"])),i("div",_e,[r(va,{placement:"top"},{title:d((()=>[i("span",null,p(t.approveRemark),1)])),default:d((()=>[2===t.status?(n(),g(ca,{key:0,title:"未通过原因",style:{"margin-right":"12px"}})):v("",!0)])),_:2},1024)])])]),i("div",Ce,[i("div",ze,[i("div",{class:"name",title:t.componentName},p(t.componentName),9,Se),i("div",{class:"user",title:t.ownerName},p(t.ownerName),9,Ne)]),i("div",Ie,[i("div",{id:t.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(n(!0),o(u,null,c(t.tags,((e,a)=>(n(),o("div",{key:a,title:e.tagName,class:"tag-item",style:y({backgroundColor:e.color})},p(e.tagName),13,$e)))),128))],8,Pe)])])])])),[[j,!la.value&&(null==(l=sa.value)?void 0:l.length)]])})),128)),la.value?(n(),g(P,{key:0,class:"loading-icon",spinning:la.value},null,8,["spinning"])):v("",!0),la.value||(null==(l=sa.value)?void 0:l.length)?v("",!0):(n(),g(C,{key:1}))])):(n(),o("div",Re,[r(C,{title:"暂无权限"})])),i("div",Oe,[r(pa,{total:ea.value.total,"page-size-options":["12","20","30","40"],current:ea.value.current,"page-size":ea.value.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:aa},null,8,["total","current","page-size","show-total"])])])]),r(R,{ref_key:"editChartRef",ref:Ye,onOk:ta},null,512),r(z,{ref_key:"imgVideoPreviewRef",ref:Xe},null,512),r(A,{ref_key:"uploadFileRef",ref:qe,onOk:ta},null,512),r(E,{ref_key:"checkRef",ref:He,onOk:ta},null,512),r(G,{ref_key:"reasonRef",ref:De,onOk:ta},null,512),r(N,{ref_key:"viewerArrowRef",ref:Ee,current:Ge.value,total:Je.value,onLeft:Te,onRight:Le},null,8,["current","total"])])}}}),[["__scopeId","data-v-a9bf6d4d"]]);export{Ue as default};
