import{d as e,r as a,f as l,p as t,j as s,w as o,a as i,am as r,b as n,o as u,e as c,c as d,F as v,ag as p,ab as m,ad as g,aa as y,J as j,a9 as f,ae as h,y as k,G as w,a5 as b,a4 as C,E as _,n as x}from"./@vue-HScy-mz9.js";import I from"./Nodata-mmdoiDH6.js";import{p as N}from"./projectGallery-DFHuwUAq.js";import{V as T}from"./viewerjs-_Br7E8dP.js";import{V as $}from"./ViewerArrow-r3R4USz-.js";import{s as z,e as E,u as P,x as S,b as U}from"./main-Djn9RDyT.js";import A from"./Detail-B5ZgblyY.js";import R from"./AddProject-C7_pW05F.js";import O from"./EditProject-DdFvyiRW.js";import V from"./ImgVideoPreview-De6J7GQx.js";import{b as G,k as L}from"./projectGallery-xT8wgNPG.js";import{q as J,r as M,S as B,I as K,x as q,w as D,B as F,e as H,g as X,M as Z}from"./ant-design-vue-DYY9BtJq.js";import{_ as Q}from"./vue-qr-CB2aNKv5.js";import"./no-data-DShY7eqz.js";import"./js-binary-schema-parser-G48GG52R.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";const W={class:"project-gallery"},Y={class:"content-wrap"},ee={class:"tag-search"},ae={key:0,class:"tag-content"},le=["onClick"],te={key:1,class:"tag-item"},se={key:1,class:"no-tag-content"},oe={class:"content-list"},ie={class:"search"},re={class:"search-wrap"},ne={key:0,class:"list"},ue={class:"contain"},ce={class:"img-box"},de=["src"],ve={class:"bottom-wrapper"},pe={class:"bottom-content"},me={class:"author"},ge={class:"hover-box"},ye=["onClick"],je=["onClick"],fe={key:0,class:"btn perview"},he=["onClick"],ke=["onClick"],we={class:"control-icon"},be={key:0,style:{display:"inline-block",width:"16px",height:"16px","margin-right":"12px"}},Ce=["id"],_e=["src"],xe={class:"item-bottom"},Ie={class:"title"},Ne=["title"],Te={class:"tag-wrapper"},$e=["id"],ze=["onClick"],Ee={class:"type-list"},Pe={key:1,class:"list"},Se={class:"pagination-box"},Ue=Q(e({__name:"Index",setup(e){const Q=a(sessionStorage.getItem("XI_TONG_LOGO")||N),Ue=a("");l((async()=>{var e,a;const l=await E({code:"KAI_FA_SHE_JI_TU"});Ue.value=null==(a=null==(e=l.data)?void 0:e.rows[0])?void 0:a.value}));const Ae=P(),Re=t((()=>Ae.userInfo.id));let Oe=null;const Ve=a(),Ge=a(0),Le=a(0),Je=()=>{Oe&&Oe.prev()},Me=()=>{Oe&&Oe.next()},Be=()=>{S(Ue.value)?window.open(`${Ue.value}&keyword=${oa.value.contractCode} ${oa.value.productName} ${oa.value.industry} ${oa.value.techPlatform}`):window.open(`${Ue.value}?keyword=${oa.value.contractCode} ${oa.value.productName} ${oa.value.industry} ${oa.value.techPlatform}`)};s((()=>{Oe&&Oe.destroy()}));const Ke=a(),qe=a(!0),De=a({}),Fe=a([]);o((()=>De.value),(()=>{We(1,12)}),{deep:!0});const He=a(!0);He.value=!0,z({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"4"}}).then((e=>{He.value=!1,200===e.code?(e.data.length?e.data.forEach((e=>{De.value[e.id]=[]})):De.value={},Fe.value=e.data||[]):Fe.value=[]})).catch((()=>{De.value={},Fe.value=[],He.value=!1}));const Xe=a([]),Ze=a([]),Qe=i({total:0,current:1,pageSize:12,likeContent:"",tagIds:[],source:1,checkedType:-1}),We=(e,a)=>{Qe.current=e,Qe.pageSize=a,Ye()},Ye=()=>{var e;qe.value=!0;const a=(null==(e=Object.values(De.value))?void 0:e.flat(Infinity))||[],l={pageNo:Qe.current,pageSize:Qe.pageSize,likeContent:Qe.likeContent,tagIds:a,sort:"modifyTime,desc"};var t;1!==Qe.source&&(l.owner=Re.value),-1!==Qe.checkedType&&(l.approve=Qe.checkedType),1===Qe.source&&(l.approve=2),(t=l,z({url:"/edtap/projectExampleNew/page",method:"post",data:t})).then((e=>{qe.value=!1,200===e.code&&(Qe.total=e.data.totalRows,Xe.value=[...e.data.rows],ea(),Xe.value.forEach((e=>{e.id===oa.value.id&&(oa.value=e)})))})).catch((()=>{qe.value=!1}))},ea=async()=>{Ze.value=[];for(let e=0;e<Xe.value.length;e++){const a=Xe.value[e].cover||(Xe.value[e].deliverablesImages?Xe.value[e].deliverablesImages[0]:"");a?Ze.value.push(`${window.config.baseUrl}${a}?width=400`):Ze.value.push(Q.value)}},aa=a(),la=a(),ta=()=>{aa.value.init()},sa=a(!1),oa=a({}),ia=()=>{sa.value=!1,oa.value={}},ra=e=>{var a;a=e.id,z({url:`/annotate/projectExample/addPreviewTimes/${a}`,method:"post"}),Ye(),setTimeout((()=>{window.open(e.projectAccessLinker)}),100)},na=e=>{let a="";return a=2===e?"正常":1===e?"待审批":3===e?"未通过":"未提交",a},ua=()=>{Ye()};return(e,a)=>{var l;const t=J,s=M,o=B,i=r("search-outlined"),N=K,z=q,E=D,P=F,S=H,Ae=r("picture-outlined"),Re=r("video-camera-outlined"),ea=r("ellipsis-outlined"),ca=X,da=Z;return u(),n("div",W,[c("div",Y,[c("div",ee,[(null==(l=Fe.value)?void 0:l.length)?(u(),n("div",ae,[(u(!0),n(v,null,p(Fe.value,(e=>{var a,l;return u(),n("div",{key:e.id,class:"tag-group"},[(null==(a=e.tags)?void 0:a.length)?(u(),n("div",{key:0,class:"tag-group-name",onClick:a=>(e=>{var a;const l=null==(a=e.tags)?void 0:a.map((e=>e.id));JSON.stringify(De.value[e.id])===JSON.stringify(l)?De.value[e.id]=[]:De.value[e.id]=e.tags.map((e=>e.id))})(e)},g(e.groupName),9,le)):m("",!0),(null==(l=e.tags)?void 0:l.length)?(u(),n("div",te,[d(s,{value:De.value[e.id],"onUpdate:value":a=>De.value[e.id]=a,style:{width:"100%"}},{default:y((()=>[(u(!0),n(v,null,p(e.tags,(e=>(u(),n("div",{key:e.id,class:"tag-item-name"},[d(t,{value:e.id},{default:y((()=>[j(g(e.tagName),1)])),_:2},1032,["value"])])))),128))])),_:2},1032,["value","onUpdate:value"])])):m("",!0)])})),128))])):(u(),n("div",se,[He.value?(u(),f(o,{key:0,class:"loading-icon",spinning:He.value},null,8,["spinning"])):m("",!0),He.value?m("",!0):(u(),f(I,{key:1,title:"请绑定标签"}))]))]),c("div",oe,[c("div",ie,[c("div",re,[d(N,{value:Qe.likeContent,"onUpdate:value":a[1]||(a[1]=e=>Qe.likeContent=e),valueModifiers:{trim:!0},class:"search-wrapper",placeholder:"搜索",onKeyup:a[2]||(a[2]=h((e=>We(1,12)),["enter"]))},{suffix:y((()=>[d(i,{style:{cursor:"pointer"},onClick:a[0]||(a[0]=e=>We(1,12))})])),_:1},8,["value"]),d(E,{value:Qe.source,"onUpdate:value":a[3]||(a[3]=e=>Qe.source=e),placeholder:"请选择数据源",class:"search-select",onChange:a[4]||(a[4]=e=>(Qe.checkedType=-1,void We(1,12)))},{default:y((()=>[d(z,{value:1},{default:y((()=>a[10]||(a[10]=[j("公共资源")]))),_:1}),d(z,{value:3},{default:y((()=>a[11]||(a[11]=[j("我的资源")]))),_:1})])),_:1},8,["value"]),3===Qe.source?(u(),f(E,{key:0,value:Qe.checkedType,"onUpdate:value":a[5]||(a[5]=e=>Qe.checkedType=e),placeholder:"请选择状态",class:"search-select",onChange:a[6]||(a[6]=e=>We(1,12))},{default:y((()=>[d(z,{value:-1},{default:y((()=>a[12]||(a[12]=[j("全部")]))),_:1}),d(z,{value:0},{default:y((()=>a[13]||(a[13]=[j("未提交")]))),_:1}),d(z,{value:1},{default:y((()=>a[14]||(a[14]=[j("待审批")]))),_:1}),d(z,{value:2},{default:y((()=>a[15]||(a[15]=[j("审批通过")]))),_:1}),d(z,{value:3},{default:y((()=>a[16]||(a[16]=[j("审批不通过")]))),_:1})])),_:1},8,["value"])):m("",!0),d(P,{type:"primary",class:"search-btn",onClick:a[7]||(a[7]=e=>We(1,12))},{default:y((()=>a[17]||(a[17]=[j(" 查询 ")]))),_:1})]),e.hasPerm("projectExampleNew:add")?(u(),f(P,{key:0,type:"primary",onClick:ta},{default:y((()=>a[18]||(a[18]=[j("新增项目案例")]))),_:1})):m("",!0)]),e.hasPerm("projectExampleNew:page")?(u(),n("div",ne,[(u(!0),n(v,null,p(Xe.value,((e,l)=>{var t,s;return k((u(),n("div",{key:e.id,class:"item"},[c("div",ue,[c("div",ce,[k(c("img",{src:Ze.value[l],alt:"图片",class:"img",onError:a[8]||(a[8]=e=>(e.target.src=Q.value,e.target.style.width="auto"))},null,40,de),[[w,Ze.value[l]]]),c("div",ve,[c("div",pe,[c("span",me,"项目经理："+g(e.projectManager),1),3===Qe.source?(u(),n("div",{key:0,class:b(["status",{fail:3===e.approve,check:1===e.approve,normal:2===e.approve,unpush:!e.approve}])},g(na(e.approve)),3)):m("",!0)])]),c("div",ge,[c("div",{class:"btn",onClick:a=>(e=>{oa.value=e,sa.value=!0})(e)},"详情",8,ye),3===Qe.source&&3===e.approve?(u(),n("div",{key:0,class:"btn perview",onClick:a=>(e=>{la.value.init(e)})(e)},"编辑",8,je)):m("",!0),d(S,{placement:"topRight",title:"确认删除？",onConfirm:a=>(async e=>{const a=await L({projExampleId:e.id});200===a.code?(U("success","删除成功！"),Ye()):U("error",a.message)})(e)},{default:y((()=>[3===Qe.source&&3===e.approve?(u(),n("div",fe,"删除")):m("",!0)])),_:2},1032,["onConfirm"]),3===Qe.source&&3===e.approve?(u(),n("div",{key:1,class:"btn perview",onClick:a=>(async e=>{const a=await G({id:e.id,approve:1});200===a.code?(U("success","提交成功，请等待管理员审批！"),Ye()):U("error",a.message)})(e)},"提交审批",8,he)):m("",!0),e.projectAccessLinker?(u(),n("div",{key:2,class:"btn perview",onClick:a=>ra(e)},"预览",8,ke)):m("",!0),c("div",we,[(null==(t=e.deliverablesImages)?void 0:t.length)?(u(),n("div",be,[d(Ae,{title:"图片预览",style:{"margin-right":"5px"},onClick:a=>(e=>{e.showImgPreview=!0,x((()=>{Oe=new T(document.getElementById(`img-pre-wrap${e.id}`),{toolbar:!1,navbar:!0,title:!1,transition:!1,hidden:()=>{null==Oe||Oe.destroy(),Oe=null,Le.value=0},viewed(){Ge.value=Oe.index,Le.value=Oe.length,Oe.viewer.appendChild(Ve.value.$el)}}),Oe.show()}))})(e)},null,8,["onClick"]),e.showImgPreview?(u(),n("div",{key:0,id:"img-pre-wrap"+e.id,style:{display:"none"}},[(u(!0),n(v,null,p(e.deliverablesImages,((e,a)=>{return u(),n("img",{key:a,src:(l=e,window.config.baseUrl+l),alt:""},null,8,_e);var l})),128))],8,Ce)):m("",!0)])):m("",!0),(null==(s=e.deliverablesVideo)?void 0:s.length)?(u(),f(Re,{key:1,title:"视频预览",onClick:a=>{return l="video",t=e.deliverablesVideo,void Ke.value.init(l,t);var l,t}},null,8,["onClick"])):m("",!0)])])]),c("div",xe,[c("div",Ie,[c("div",{class:"name",title:e.projectName},g(e.projectName),9,Ne)]),c("div",Te,[c("div",{id:e.id,ref_for:!0,ref:"tagList",class:"tag-list"},[(u(!0),n(v,null,p(e.exTags,((e,a)=>(u(),n("div",{key:a,class:"tag-item",style:C({backgroundColor:e.color})},g(e.tagName),5)))),128))],8,$e),k(c("div",{class:"tag-opera",onClick:_((a=>e.visibleTags=!e.visibleTags),["stop"])},[d(ea)],8,ze),[[w,e.moreTags]]),k(c("div",Ee,[(u(!0),n(v,null,p(e.exTags,((e,a)=>(u(),n("div",{key:a,class:"type-item",style:C({backgroundColor:e.color})},g(e.tagName),5)))),128))],512),[[w,e.visibleTags]])])])])])),[[w,Xe.value.length]])})),128)),qe.value?(u(),f(o,{key:0,class:"loading-icon",spinning:qe.value},null,8,["spinning"])):m("",!0),qe.value||Xe.value.length?m("",!0):(u(),f(I,{key:1}))])):(u(),n("div",Pe,[d(I,{title:"暂无权限"})])),c("div",Se,[d(ca,{total:Qe.total,"page-size-options":["12","20","30","40"],current:Qe.current,"page-size":Qe.pageSize,size:"small","show-total":e=>`共 ${e} 条`,"show-size-changer":"",onChange:We},null,8,["total","current","page-size","show-total"])])])]),d(da,{open:sa.value,"onUpdate:open":a[9]||(a[9]=e=>sa.value=e),"wrap-class-name":"cus-modal detail-modal",width:"80%","body-style":{maxHeight:"600px",overflow:"auto"},footer:null,onCancel:ia},{title:y((()=>[a[20]||(a[20]=c("span",null,"详情",-1)),k(d(P,{type:"primary",style:{"margin-left":"10px"},onClick:Be},{default:y((()=>a[19]||(a[19]=[j("相关设计图")]))),_:1},512),[[w,"none"!==Ue.value]])])),default:y((()=>[sa.value?(u(),f(A,{key:0,"gallery-info":oa.value},null,8,["gallery-info"])):m("",!0)])),_:1},8,["open"]),d(R,{ref_key:"addProjectRef",ref:aa},null,512),d(O,{ref_key:"editProjectRef",ref:la,onOk:ua},null,512),d(V,{ref_key:"imgVideoPreviewRef",ref:Ke},null,512),d($,{ref_key:"viewerArrowRef",ref:Ve,current:Ge.value,total:Le.value,onLeft:Je,onRight:Me},null,8,["current","total"])])}}}),[["__scopeId","data-v-1b2bebe3"]]);export{Ue as default};
