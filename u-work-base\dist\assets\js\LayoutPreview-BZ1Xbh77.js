import{_ as t}from"./AsyncComponent-DgqhDfCx.js";import{C as s}from"./main-Djn9RDyT.js";import{d as e,r as o,n as i,b as r,a9 as m,aa as n,aI as l,c as a,e as p,o as j}from"./@vue-HScy-mz9.js";import{S as c}from"./ant-design-vue-DYY9BtJq.js";import"./vue-BVcE9bLt.js";import"./vue3-sfc-loader-BPS_8u5b.js";import"./swiper-CfJ8YdI9.js";import"./echarts-PHL8p6cd.js";import"./zrender-DJ-1imfX.js";import"./tslib-DITk-L2-.js";import"./echarts-gl-Cssen4G3.js";import"./claygl-CnLhJ-kv.js";import"./echarts-liquidfill-CFZelQOA.js";import"./sass-C4uXr8HK.js";import"./immutable-Dastkchk.js";import"./crypto-js-Duvj5un5.js";import"./@babel-B4rXMRun.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./lodash-Cz2B5noN.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";const u={class:"screen-preview"},d=e({__name:"LayoutPreview",setup(e){const{query:d}=s.currentRoute.value,{id:f,path:y,width:v,height:h}=d,w=o("");y&&v&&h&&i((()=>{b(d)}));const g=o(),b=t=>{const s=t.width||400,e=t.height||300,o=g.value;if(o.style.position="absolute",o.style.top="50%",o.style.left="50%",o.style.width=s+"px",o.style.height=e+"px",o.style.overflow="hidden",o.style.backgroundColor="#000",o.style.transform="translateX(-50%) translateY(-50%)",s>window.document.documentElement.clientWidth){const t=window.document.documentElement.clientWidth/s;o.style.transform=`scale(${t}, ${t}) translateX(-50%) translateY(-50%)`,o.style.transformOrigin="0 0"}else if(e>window.document.documentElement.clientHeight){const t=window.document.documentElement.clientHeight/e;o.style.transform=`scale(${t}, ${t}) translateX(-50%) translateY(-50%)`,o.style.transformOrigin="0 0"}w.value=window.config.previewUrl+t.path+"/Index.vue"};return(s,e)=>{const o=c;return j(),r("div",u,[(j(),m(l,null,{default:n((()=>[p("div",{class:"content",ref_key:"contentRef",ref:g},[a(t,{url:w.value},null,8,["url"])],512)])),fallback:n((()=>[a(o)])),_:1}))])}}});export{d as default};
