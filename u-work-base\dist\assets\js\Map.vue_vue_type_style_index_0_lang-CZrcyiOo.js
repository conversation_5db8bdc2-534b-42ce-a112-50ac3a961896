import{d as e,r as a,f as t,y as s,G as l,b as n,e as o,c as r,am as c,F as i,ag as u,J as v,ad as d,aa as p,n as g,o as m}from"./@vue-HScy-mz9.js";import{e as L,b as y,L as w}from"./main-Djn9RDyT.js";import{I as f,B as O}from"./ant-design-vue-DYY9BtJq.js";const _={class:"map-container"},k={id:"suggestId",class:"suggest-list"},A=["onClick"],C={class:"name"},h={id:"searchResultPanel",ref:"SearchResult"},M=e({__name:"Map",emits:["getMapPlace"],setup(e,{expose:M,emit:S}){const P=S,I=a(!1),b=a(""),j=()=>{I.value=!1,b.value="",E.value=[],J.value&&J.value.clearOverLays()},x=a("");t((async()=>{const e=await L({pageNo:1,pageSize:9,code:"TIAN_DI_TU_MI_YAO"});x.value=e.data.rows[0].value,x.value?window.T||w(`http://api.tianditu.gov.cn/api?v=4.0&tk=${x.value}`,(()=>{})):y("error","获取天地图Key失败，无法加载地图")}));const N=a({contry:"",province:"",city:"",district:"",street:"",lnglat:"",formattedAddress:""}),G=a({contry:"",province:"",city:"",district:"",street:"",lnglat:"",formattedAddress:""});let J=a(null),K=a(null);const Z=e=>{b.value?K.value.search(b.value,4):E.value=[]},E=a([]),R=e=>{E.value=e.suggests||[]},U=()=>{G.value=N.value,P("getMapPlace",G.value),j()};return M({init:(e,a)=>{x.value?(I.value=!0,g((()=>{((e,a)=>{if(J.value=new T.Map("map-wrapper",{projection:"EPSG:4326"}),e){const t=JSON.parse(e);J.value.centerAndZoom(new T.LngLat(t[0],t[1]),12),J.value.clearOverLays();const s=new T.Marker(new T.LngLat(t[0],t[1]));J.value.addOverLay(s);const l=new T.Label({text:a,position:s.getLngLat(),offset:new T.Point(3,-30)});J.value.addOverLay(l)}else J.value.centerAndZoom(new T.LngLat(116.40769,39.89945),12);const t={pageCapacity:10,onSearchComplete:R};K.value=new T.LocalSearch(J.value,t);const s=new T.Geocoder;J.value.addEventListener("click",(function(e){s.getLocation(e.lnglat,(a=>{if("0"===a.status){const{nation:t,province:s,county:l,town:n,city:o}=a.addressComponent;N.value={country:t,province:s,city:o,district:l,street:n},N.value.formattedAddress=a.formatted_address,N.value.lnglat=JSON.stringify([e.lnglat.lng,e.lnglat.lat]),J.value.clearOverLays();const r=new T.Marker(new T.LngLat(e.lnglat.lng,e.lnglat.lat));J.value.addOverLay(r);const c=new T.Label({text:a.formatted_address,position:r.getLngLat(),offset:new T.Point(3,-30)});J.value.addOverLay(c)}}))}))})(e,a)}))):y("error","获取天地图Key失败，无法加载地图")},close:j}),(e,a)=>{const t=c("CloseOutlined"),g=f,L=O;return s((m(),n("div",_,[a[2]||(a[2]=o("div",{id:"map-wrapper"},null,-1)),o("span",{class:"close",onClick:j},[r(t,{class:"close-icon"})]),r(g,{onKeyup:Z,value:b.value,"onUpdate:value":a[0]||(a[0]=e=>b.value=e),class:"input",placeholder:"请输入需要查询的地点（需联网）"},null,8,["value"]),o("div",k,[(m(!0),n(i,null,u(E.value,((e,a)=>(m(),n("div",{class:"suggest-item",onClick:a=>(e=>{const a=e.lonlat.split(",");J.value.centerAndZoom(new T.LngLat(a[0],a[1]),12)})(e),key:a},[o("div",C,d(e.name),1),v(" "+d(e.address),1)],8,A)))),128))]),r(L,{type:"primary",class:"save",onClick:U},{default:p((()=>a[1]||(a[1]=[v("保存")]))),_:1}),o("div",h,null,512)],512)),[[l,I.value]])}}});export{M as _};
