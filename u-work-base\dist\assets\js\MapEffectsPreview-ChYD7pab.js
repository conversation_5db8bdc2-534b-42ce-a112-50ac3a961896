import{d as e,r as a,w as i,b as t,e as s,y as o,ab as n,c as r,am as l,G as p,a5 as c,o as m,n as u}from"./@vue-HScy-mz9.js";import{a as d,b as w,C as v,ao as g}from"./main-Djn9RDyT.js";import{s as j}from"./index-GENaTOlC.js";import{_ as f}from"./vue-qr-CB2aNKv5.js";import"./ant-design-vue-DYY9BtJq.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./js-binary-schema-parser-G48GG52R.js";const h={class:"map-effect"},y={id:"earth-container",ref:"earthIns"},b={class:"control-wrap"},x={class:"icon-wrap"},_={class:"map-tile-wrap"},T=f(e({__name:"MapEffectsPreview",setup(e){const f=d(),T=a([]),k=a(0),E=a(!1),C=a(!1),G=()=>{const e=new FormData;e.append("id",H.value);let a=null;if(1===f.thingjsVersion)a=window.app.captureScreenshotToImage(window.app.domElement.clientWidth,window.app.domElement.clientHeight,"png",.5),e.append("file",I(a));else{a=window.app.camera.captureToImage(window.app.container.clientWidth,window.app.container.clientHeight).src,e.append("file",I(a))}C.value||(C.value=!0,j(e).then((e=>{C.value=!1,200===e.code?w("success","截图保存成功"):w("error",e.message)})).catch((()=>{C.value=!1})))},I=e=>{const a=e.split(",")[1],i=atob(a),t=[];for(let o=0;o<i.length;o+=1)t.push(i.charCodeAt(o));const s=new Blob([new Uint8Array(t)],{type:"image/png"});return new File([s],"preview.png",{type:"image/png"})},z=e=>{if(e!==k.value)switch(k.value=e,e){case 1:A("http://webst0{1,2,3,4}.is.autonavi.com/appmaptile?style=7&x={x}&y={y}&z={z}");break;case 2:A("http://webst0{1,2,3,4}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}");break;case 3:A("https://t{0,1,2,3,4,5,6,7}.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=a70a3ada606b48ecfacecaa4c76b0403");break;case 4:A("https://t{0,1,2,3,4,5,6,7}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=a70a3ada606b48ecfacecaa4c76b0403")}else P()},A=e=>{const a=window.app.query(".TileLayer");a.length&&a.forEach((a=>{a.url=e}))},P=()=>{k.value=0;const e=window.app.query(".TileLayer");e.length&&e.forEach(((e,a)=>{e.url=T.value[a]}))},$=a(),H=a(),N=e=>{g((()=>{e&&e()}),["thingjs","uearth","thing.campus"])},U=async()=>{let e=null;1===f.thingjsVersion?(window.THING.disableLoginRequested=!0,1===f.thingjsNoAuth&&(THING.__auth_server_URL__=`${window.config.appApi}/licensex/license-info/thing-js-api`,THING.__force_check_auth_result__=!0),e=new window.THING.App({container:"earth-container",background:"#000000"})):(await THING.Utils.login({method:"POST",url:`${window.config.appApi}/licensex/license-info/thing-js-2-api`,wasmRootPath:"/libs/wasm"}),e=new THING.App({container:document.getElementById("earth-container")||void 0,compatibleOptions:{rendering:!0},isEditor:!1})),window.app=e,1===f.thingjsVersion?window.map=e.create({type:"Map",url:`${window.config.previewEffectUrl}${$.value}/map.json`,resourceConfig:{resourcePrefix:`${window.config.previewEffectUrl}${$.value}`,isProxima:!1,ajaxAsync:!1,loadDataFirst:!0,externalConfigUrl:""},attribution:"none",localPosition:[0,0,0],complete:()=>{const e=window.app.query(".TileLayer");e.length&&e.forEach((e=>{T.value.push(e.url)}))}}):window.map=new THING.EARTH.Map({url:`${window.config.previewEffectUrl}${$.value}/map.json`,resourceConfig:{resourcePrefix:`${window.config.previewEffectUrl}${$.value}`,isProxima:!1,ajaxAsync:!1,loadDataFirst:!0,externalConfigUrl:""},attribution:"none",localPosition:[0,0,0],complete:()=>{const e=window.app.query(".TileLayer");e.length&&e.forEach((e=>{T.value.push(e.url)}))}})};return i((()=>f.adminToken),(e=>{e&&u((()=>{(()=>{const{query:e}=v.currentRoute.value,{code:a,id:i}=e;a&&($.value=a,H.value=i,u((()=>{N((()=>{U()}))})))})()}))}),{immediate:!0}),(e,a)=>{const i=l("CameraOutlined"),u=l("GlobalOutlined");return m(),t("div",h,[s("div",y,null,512),s("div",b,[s("div",x,[H.value&&e.hasPerm("effect-package:editEffectSnap")?(m(),t("div",{key:0,class:"icon-item",onClick:G,title:"封面截图"},[r(i,{title:"封面截图"})])):n("",!0),s("div",{class:"icon-item",onClick:a[0]||(a[0]=e=>E.value=!E.value),title:"切换底图"},[r(u,{title:"切换底图"})])]),o(s("div",_,[s("div",{class:c(["map-tile-item",{active:1===k.value}]),onClick:a[1]||(a[1]=e=>z(1))},a[5]||(a[5]=[s("img",{src:"/assets/png/gd-road-DngYgOhl.png",alt:"高德-街道"},null,-1),s("span",null,"高德-街道(GCJ02)",-1)]),2),s("div",{class:c(["map-tile-item",{active:2===k.value}]),onClick:a[2]||(a[2]=e=>z(2))},a[6]||(a[6]=[s("img",{src:"/assets/png/gd-star-BpQbbG64.png",alt:"高德-卫星"},null,-1),s("span",null,"高德-卫星(GCJ02)",-1)]),2),s("div",{class:c(["map-tile-item",{active:3===k.value}]),onClick:a[3]||(a[3]=e=>z(3))},a[7]||(a[7]=[s("img",{src:"/assets/png/td-road-k8dSqPhD.png",alt:"天地图-街道"},null,-1),s("span",null,"天地图-街道(WGS84)",-1)]),2),s("div",{class:c(["map-tile-item",{active:4===k.value}]),onClick:a[4]||(a[4]=e=>z(4))},a[8]||(a[8]=[s("img",{src:"/assets/png/td-star-vFv9ojTw.png",alt:"天地图-卫星"},null,-1),s("span",null,"天地图-卫星(WGS84)",-1)]),2)],512),[[p,E.value]])])])}}}),[["__scopeId","data-v-04113986"]]);export{T as default};
