import{i as a}from"./no-data-DShY7eqz.js";import{d as s,b as t,e,ad as o,o as r}from"./@vue-HScy-mz9.js";import{_ as d}from"./vue-qr-CB2aNKv5.js";import"./js-binary-schema-parser-G48GG52R.js";const i={class:"no-data"},p=d(s({__name:"Nodata",props:{title:{type:String,default:"暂无数据"}},setup:s=>(d,p)=>(r(),t("div",i,[p[0]||(p[0]=e("img",{src:a,alt:"no-data"},null,-1)),e("div",null,o(s.title),1)]))}),[["__scopeId","data-v-5578ee32"]]);export{p as default};
