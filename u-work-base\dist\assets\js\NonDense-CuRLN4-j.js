import{c as e,d as s}from"./userManage-D6iEBY45.js";import{i as t,B as a,d as o,M as i,j as r}from"./ant-design-vue-DYY9BtJq.js";import{Q as l}from"./@ant-design-CA72ad83.js";import{d as p,r as m,a9 as n,o as c,aa as u,e as j,c as d,J as v,ad as y,u as f}from"./@vue-HScy-mz9.js";import{_ as b}from"./vue-qr-CB2aNKv5.js";import"./main-Djn9RDyT.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const h={class:"secretKey"},k={class:"row"},w={class:"btns"},_=b(p({__name:"NonDense",emits:["ok"],setup(p,{expose:b,emit:_}){const g=m(!1),x=m(""),z=m("N"),C=m(),N=m({}),Y=_,K=()=>{C.value.$el.select(),document.execCommand("Copy"),r.success("已复制密钥")},S=(e,t)=>{const a={create:e,userAccount:t.account,userId:t.id};s(a).then((e=>{if(200===e.code){const{data:s}=e;null===s||""===s?S(!0,t):x.value=s}}))};return b({showSecret:e=>{N.value=e,z.value=e.tokenSwitch,g.value=!0,"Y"===z.value&&S(!1,e)}}),(s,r)=>{const p=o,m=t,b=a,_=i;return c(),n(_,{"wrap-class-name":"cus-modal",footer:null,open:g.value,onCancel:r[3]||(r[3]=e=>g.value=!1)},{title:u((()=>[r[4]||(r[4]=j("span",{style:{"margin-right":"10px"}},"免密登录",-1)),d(p,{title:"免密登录的密钥有效时长与BaseX系统的授权时间相关，当系统重新授权后需要团队成员生成新的密钥",placement:"right"},{default:u((()=>[d(f(l),{style:{color:"#ef7b1a"}})])),_:1})])),default:u((()=>[j("div",h,[j("div",k,[r[5]||(r[5]=j("label",{class:"label"},"密钥：",-1)),"Y"!==z.value?(c(),n(m,{key:0,class:"text",readonly:"","auto-size":!0})):(c(),n(m,{key:1,ref_key:"secretKeyRef",ref:C,value:x.value,"onUpdate:value":r[0]||(r[0]=e=>x.value=e),class:"text",readonly:"","auto-size":!0},null,8,["value"]))]),j("div",w,[d(b,{class:"btn",type:"primary",disabled:"N"===z.value,onClick:K},{default:u((()=>r[6]||(r[6]=[v("复制")]))),_:1},8,["disabled"]),d(b,{class:"btn",type:"primary",onClick:r[1]||(r[1]=s=>(async s=>{const t={id:s.id,tokenSwitch:"Y"===z.value?"N":"Y"};try{200===(await e(t)).code&&(Y("ok"),z.value=t.tokenSwitch,"Y"===z.value&&S(!1,s))}catch(a){}})(N.value))},{default:u((()=>[v(y("Y"===z.value?"关闭":"开启")+"免密登录 ",1)])),_:1}),d(b,{class:"btn",type:"primary",disabled:"N"===z.value,onClick:r[2]||(r[2]=e=>S(!0,N.value))},{default:u((()=>r[7]||(r[7]=[v(" 生成新密钥 ")]))),_:1},8,["disabled"])])])])),_:1},8,["open"])}}}),[["__scopeId","data-v-0074b088"]]);export{_ as default};
