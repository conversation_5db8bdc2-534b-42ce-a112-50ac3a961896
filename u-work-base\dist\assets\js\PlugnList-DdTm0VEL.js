import{ag as e}from"./main-Djn9RDyT.js";import{f as s,g as a,M as t}from"./ant-design-vue-DYY9BtJq.js";import{d as i,r as o,a9 as l,o as r,aa as n,e as p,c as m,b as u,ab as d,ad as j,a7 as c}from"./@vue-HScy-mz9.js";import{_ as v}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const g={ref:"table",class:"table-content"},h={key:0},w={class:"pagination"},y=v(i({__name:"PlugnList",emits:["handlePlugin"],setup(i,{expose:v,emit:y}){const f=y,b=o(!1),z=o(!1),x=[{title:"插件名称",dataIndex:"name",ellipsis:!0,width:150},{title:"插件编码",dataIndex:"code",ellipsis:!0,width:100},{title:"版本",dataIndex:"version",ellipsis:!0,width:100},{title:"状态",dataIndex:"status",ellipsis:!0,width:100},{title:"访问地址",dataIndex:"url",ellipsis:!0,width:200},{title:"运行时间",dataIndex:"runTime",ellipsis:!0,width:180}],k=o(["启用","停用","删除"]),I=o({total:0,current:1,pageSize:10,showTotal:e=>`共有${e}条`,showSizeChanger:!0,showQuickJumper:!0,size:"small"}),_=(e,s)=>{I.value=Object.assign(I.value,{current:e,pageSize:s}),P()},C=o([]),S=()=>{b.value=!1},X=o(""),P=()=>{z.value=!0;const s={pageNo:I.value.current,pageSize:I.value.pageSize,canBind:!0};"XXV"===X.value?s.type=1:X.value&&"XXV"!==X.value&&(s.type=0),e(s).then((e=>{200===e.code&&(C.value=e.data.rows,I.value.total=e.data.totalRows),z.value=!1}))},T=e=>({onClick:()=>{b.value=!1,f("handlePlugin",e)}});return v({init:e=>{X.value=e,b.value=!0,P()}}),(e,i)=>{const o=s,v=a,y=t;return r(),l(y,{width:900,title:"插件列表","wrap-class-name":"cus-modal",open:b.value,"confirm-loading":z.value,"mask-closable":!1,footer:null,onCancel:S},{default:n((()=>[p("div",g,[m(o,{class:"table",scroll:{y:450},pagination:!1,size:"small","custom-row":T,loading:z.value,"row-key":e=>e.id,columns:x,"data-source":C.value},{bodyCell:n((({column:e,record:s})=>["status"===e.dataIndex?(r(),u("div",h,j(k.value[s.status]),1)):d("",!0)])),_:1},8,["loading","row-key","data-source"]),p("div",w,[C.value.length>0?(r(),l(v,c({key:0},I.value,{onChange:_}),null,16)):d("",!0)])],512)])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-7a687667"]]);export{y as default};
