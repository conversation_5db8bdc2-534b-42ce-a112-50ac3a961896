import{d as e,p as s,r as t,b as i,o as l,e as a,c as o,ad as r,F as c,ag as n,y as d,G as v}from"./@vue-HScy-mz9.js";import{V as m}from"./viewerjs-_Br7E8dP.js";import{V as p}from"./ViewerArrow-r3R4USz-.js";import{b as f}from"./main-Djn9RDyT.js";import{e as u}from"./projectGallery-xT8wgNPG.js";import{_ as g}from"./vue-qr-CB2aNKv5.js";import"./@ant-design-CA72ad83.js";import"./@ctrl-B2IeE8ye.js";import"./ant-design-vue-DYY9BtJq.js";import"./@babel-B4rXMRun.js";import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./lodash-es-CsK89k5Z.js";import"./dayjs-CA7qlNSr.js";import"./async-validator-BTKOuuO-.js";import"./throttle-debounce-w9OM8Bxz.js";import"./element-plus-DGm4IBRH.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./js-binary-schema-parser-G48GG52R.js";const j={class:"gallery-preview"},y={class:"projectName"},x={class:"info",style:{"margin-bottom":"20px"}},I={class:"info-item"},w={class:"text"},h={class:"info-item"},b={class:"text"},k={class:"info"},S={class:"info-item"},C={class:"text"},U={class:"info-item"},D={class:"text"},N={class:"info-item"},_={class:"text"},A={class:"info-item"},z={class:"text"},E={class:"info-item"},F={class:"text"},M={class:"info-item"},V={class:"text"},$={class:"info-item"},P={class:"text"},J={class:"info-item"},L={class:"text"},O={class:"info-item"},R={class:"text"},q={class:"info-item"},B={class:"text"},G={class:"info-item"},T={class:"text"},K={class:"info-item"},Z={class:"text"},H={class:"info-item"},Q={class:"text"},W={class:"info-item"},X={class:"text"},Y={class:"info-item"},ee={class:"text"},se={class:"info-text"},te={class:"text text-erea"},ie={class:"info-text"},le={class:"text text-erea"},ae={class:"info-text"},oe={class:"text text-erea"},re={class:"info-text"},ce={class:"text text-erea"},ne={class:"info-text"},de={class:"text-wrapper"},ve={class:"text-more text-erea"},me={class:"text-more text-erea"},pe={class:"video"},fe={id:"gallary-img-list",class:"img-list"},ue=["src"],ge={class:"btn-wtap"},je=["onClick"],ye=["onClick"],xe={class:"video"},Ie={class:"video-list"},we=["src"],he=g(e({__name:"Preview",props:{galleryInfo:{type:Object,required:!0}},emits:["ok"],setup(e,{emit:g}){const he=g,be=e,ke=e=>e?`${e}`:"",Se=s((()=>{let e=["",""];try{e=Array.isArray(JSON.parse(be.galleryInfo.projectPracticalSummary))?JSON.parse(be.galleryInfo.projectPracticalSummary):["",""]}catch(s){e=["",""]}return e})),Ce=t([]),Ue=t([]);t(0);(async()=>{const e=be.galleryInfo.deliverablesImages||[],s=be.galleryInfo.deliverablesVideo||[];for(let t=0;t<e.length;t++){const s=e[t];if(s.includes("https://www.mingdao.com")){const e=s.split("excelfile")[1],t=await fetch(`/mingdaoapi/api/File/ExcelFile${e}`);Ce.value.push({showUrl:t.url,originalUrl:s})}else Ce.value.push({showUrl:ke(s),originalUrl:s})}for(let t=0;t<s.length;t++){const e=s[t];if(e.includes("https://www.mingdao.com")){const s=e.split("excelfile")[1],t=await fetch(`/mingdaoapi/api/File/ExcelFile${s}`);Ue.value.push(t.url)}else Ue.value.push(ke(e))}})();let De=null;const Ne=t(),_e=t(0),Ae=t(0),ze=()=>{De&&De.prev()},Ee=()=>{De&&De.next()};return(s,t)=>(l(),i("div",j,[a("div",y,r(e.galleryInfo.projectName),1),a("div",x,[a("div",I,[t[2]||(t[2]=a("div",{class:"title"},"预览数",-1)),a("div",w,r(e.galleryInfo.previewTimes)+"人预览",1)]),a("div",h,[t[3]||(t[3]=a("div",{class:"title"},"合同编号",-1)),a("div",b,r(e.galleryInfo.contractCode||"-"),1)])]),t[26]||(t[26]=a("div",{class:"projectName"},"项目信息",-1)),a("div",k,[a("div",S,[t[4]||(t[4]=a("div",{class:"title"},"产品信息（合同）",-1)),a("div",C,r(e.galleryInfo.productName||"-"),1)]),a("div",U,[t[5]||(t[5]=a("div",{class:"title"},"产品版本",-1)),a("div",D,r(e.galleryInfo.productVersion||"-"),1)]),a("div",N,[t[6]||(t[6]=a("div",{class:"title"},"采纳的技术平台",-1)),a("div",_,r(e.galleryInfo.techPlatform||"-"),1)]),a("div",A,[t[7]||(t[7]=a("div",{class:"title"},"所属部门（销售）",-1)),a("div",z,r(e.galleryInfo.saleDepartment||"-"),1)]),a("div",E,[t[8]||(t[8]=a("div",{class:"title"},"项目经理",-1)),a("div",F,r(e.galleryInfo.projectManger||"-"),1)]),a("div",M,[t[9]||(t[9]=a("div",{class:"title"},"销售经理",-1)),a("div",V,r(e.galleryInfo.salesManager||"-"),1)]),a("div",$,[t[10]||(t[10]=a("div",{class:"title"},"支持售前",-1)),a("div",P,r(e.galleryInfo.saleSupporter||"-"),1)]),a("div",J,[t[11]||(t[11]=a("div",{class:"title"},"行业",-1)),a("div",L,r(e.galleryInfo.industry||"-"),1)]),a("div",O,[t[12]||(t[12]=a("div",{class:"title"},"最终用户名称",-1)),a("div",R,r(e.galleryInfo.ultimateCustomer||"-"),1)]),a("div",q,[t[13]||(t[13]=a("div",{class:"title"},"签约客户名称",-1)),a("div",B,r(e.galleryInfo.contractedCustomer||"-"),1)]),a("div",G,[t[14]||(t[14]=a("div",{class:"title"},"项目启动时间",-1)),a("div",T,r(e.galleryInfo.projectStartDate||"-"),1)]),a("div",K,[t[15]||(t[15]=a("div",{class:"title"},"项目完成时间",-1)),a("div",Z,r(e.galleryInfo.projectEndDate||"-"),1)]),a("div",H,[t[16]||(t[16]=a("div",{class:"title"},"项目状态",-1)),a("div",Q,r(e.galleryInfo.projectStatus||"-"),1)]),a("div",W,[t[17]||(t[17]=a("div",{class:"title"},"屏幕分辨率",-1)),a("div",X,r(e.galleryInfo.screenResolution||"-"),1)]),a("div",Y,[t[18]||(t[18]=a("div",{class:"title"},"项目地址",-1)),a("div",ee,r(e.galleryInfo.projectAccessLinker||"-"),1)])]),a("div",se,[t[19]||(t[19]=a("div",{class:"title"},"一句话简介",-1)),a("div",te,r(e.galleryInfo.projectSimpleDescription||"-"),1)]),a("div",ie,[t[20]||(t[20]=a("div",{class:"title"},"项目整体需求情况",-1)),a("div",le,r(e.galleryInfo.overallDemand||"-"),1)]),a("div",ae,[t[21]||(t[21]=a("div",{class:"title"},"项目范围",-1)),a("div",oe,r(e.galleryInfo.projectScope||"-"),1)]),a("div",re,[t[22]||(t[22]=a("div",{class:"title"},"功能介绍分析",-1)),a("div",ce,r(e.galleryInfo.functionalDecompositio||"-"),1)]),a("div",ne,[t[23]||(t[23]=a("div",{class:"title"},"项目实战经验总结",-1)),a("div",de,[a("div",ve,r(Se.value[0]||"-"),1),a("div",me,r(Se.value[1]||"-"),1)])]),a("div",pe,[t[24]||(t[24]=a("div",{class:"title"},"项目成果图片",-1)),a("div",fe,[(l(!0),i(c,null,n(Ce.value,((e,s)=>(l(),i("div",{key:e,class:"img-wrapper"},[a("img",{src:e.showUrl,alt:""},null,8,ue),a("div",ge,[a("div",{class:"btn",style:{"margin-right":"10px"},onClick:e=>(e=>{De=new m(document.getElementById("gallary-img-list"),{toolbar:!1,navbar:!0,title:!1,transition:!1,hidden:()=>{null==De||De.destroy(),De=null,Ae.value=0},viewed(){_e.value=De.index,Ae.value=De.length,De.viewer.appendChild(Ne.value.$el)}}),De.view(e)})(s)},"预览",8,je),d(a("div",{class:"btn",onClick:s=>(async e=>{200===(await u({...be.galleryInfo,cover:e})).code?(f("success","封面设置成功"),be.galleryInfo.cover=e,he("ok")):f("error","封面设置失败")})(e.originalUrl)},"设为封面",8,ye),[[v,e.originalUrl!==be.galleryInfo.cover]])])])))),128))])]),a("div",xe,[t[25]||(t[25]=a("div",{class:"title"},"项目成果视频",-1)),a("div",Ie,[(l(!0),i(c,null,n(Ue.value,(e=>(l(),i("video",{key:e,controls:"",muted:"",onMouseenter:t[0]||(t[0]=e=>(e=>{e.target.play()})(e)),onMouseleave:t[1]||(t[1]=e=>(e=>{e.target.pause()})(e))},[a("source",{src:e,type:"video/mp4"},null,8,we)],32)))),128))])]),o(p,{ref_key:"viewerArrowRef",ref:Ne,current:_e.value,total:Ae.value,onLeft:ze,onRight:Ee},null,8,["current","total"])]))}}),[["__scopeId","data-v-4427f264"]]);export{he as default};
