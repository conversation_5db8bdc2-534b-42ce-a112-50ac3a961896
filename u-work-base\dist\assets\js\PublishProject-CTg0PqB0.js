import"./dayjs-CA7qlNSr.js";import{a as e,u as a,O as l,b as t,M as s,e as u,v as o}from"./main-Djn9RDyT.js";import{e as r,b as d,v as i,i as n,g as c}from"./projectGallery-xT8wgNPG.js";import{u as p}from"./vue-router-BEwRlUkF.js";import{M as v,F as m,b as f,c as b,w as h,x as g,I as j,B as _,y as k,z as y,T as w,_ as D,i as C,P as M,Q as S,d as I,S as U}from"./ant-design-vue-DYY9BtJq.js";import{d as x,r as N,f as Y,a9 as P,aa as q,c as E,e as L,ab as V,J as O,a4 as F,u as $,ad as z,b as A,ag as R,am as T,F as B,n as G,o as H}from"./@vue-HScy-mz9.js";import{_ as J}from"./vue-qr-CB2aNKv5.js";import"./@babel-B4rXMRun.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const K={class:"i-box"},Q={style:{display:"flex","align-items":"center"}},W={class:"i-box"},Z={class:"upload-wrap"},X=["accept"],ee={class:"file-list"},ae=["onClick"],le={class:"form-item-notice keep-px"},te={class:"upload-wrap"},se=["accept"],ue={class:"file-list"},oe=["onClick"],re={class:"form-item-notice keep-px"},de=J(x({__name:"PublishProject",emits:["ok"],setup(x,{expose:J,emit:de}){const{params:ie}=p(),{id:ne}=ie;e(),a();const ce=de,pe=N(!1),ve=N(!1),me=N(),fe=N(),be=N(!1),he=N({projectName:"",contractCode:"",productName:"",productVersion:"",techPlatform:"",saleDepartment:"",projectManager:"",salesManager:"",saleSupporter:"",industry:"",ultimateCustomer:"",contractedCustomer:"",projectStartDate:"",projectEndDate:"",projectStatus:"",screenResolution:"",projectAccessLinker:"",projectSimpleDescription:"",overallDemand:"",projectScope:"",functionalDecompositio:"",projectPracticalSummary:"",deliverablesImages:[],deliverablesVideo:[],tagIds:[]});Y((()=>{}));const ge={contractCode:[{required:!0,message:"请输入合同编号"}],projectName:[{required:!0,message:"请输入项目名称"}],techPlatform:[{required:!0,message:"请输入采纳的技术平台"}],saleDepartment:[{required:!0,message:"请输入所属部门"}],projectManager:[{required:!0,message:"请输入项目经理"}],salesManager:[{required:!0,message:"请输入销售经理"}],projectStatus:[{required:!0,message:"请输入项目状态"}],projectAccessLinker:[{required:!1,validator:s}],deliverablesImages:[{required:!0,message:"请上传封面图",trigger:"change"}],tagIds:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let t=0;return a.forEach((e=>{if(1===e.length)if(ke.value.includes(e[0])){const a=je.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+e.length-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}]},je=N([]),_e=N(new Map),ke=N([]),ye=N(new Map),we=N(new Map),De=(e,a)=>a.some((a=>a.label.toLowerCase().indexOf(e.toLowerCase())>-1)),Ce=N(["mp4","MP4"]),Me=async e=>{const a=e.target;if(he.value.deliverablesVideo.length>=10)return t("warning","最多支持上传10个文件"),a.value="",!1;const l=a.files[0],{size:s,name:u}=l,o=u.lastIndexOf("."),r=u.slice(o+1).toLocaleLowerCase();if(!Ce.value.includes(r))return t("warning",`文件后缀必须是${Ce.value.join("/")}`),a.value="",!1;if(s>1073741824)return t("warning","文件大小不能超过1024M"),a.value="",!1;a.value="",ve.value=!0;const d=new FormData;d.append("file",l),d.append("bucket","edtp-source"),d.append("replaceName","false");const n=await i(d);200===n.code?(ve.value=!1,he.value.deliverablesVideo.push(n.data),t("success","上传成功")):(ve.value=!1,t("error",n.message))},Se=N(!1),Ie=N(),Ue=(e,a)=>{Se.value=e,"string"==typeof a&&(Ie.value=xe(a))},xe=e=>`${e}`,Ne=N(["png","jpg","jpeg","gif"]),Ye=async e=>{const a=e.target;if(he.value.deliverablesImages.length>=10)return t("warning","最多支持上传10个文件"),a.value="",!1;const l=a.files[0],{size:s,name:u}=l,o=u.lastIndexOf("."),r=u.slice(o+1).toLocaleLowerCase();if(!Ne.value.includes(r))return t("warning",`文件后缀必须是${Ne.value.join("/")}`),a.value="",!1;if(s>5242880)return t("warning","文件大小不能超过5M"),a.value="",!1;a.value="",ve.value=!0;const d=new FormData;d.append("file",l),d.append("bucket","edtp-source"),d.append("replaceName","false");const i=await n(d);200===i.code?(he.value.deliverablesImages.push(i.data),me.value.validateFields(["deliverablesImages"]),t("success","上传成功"),ve.value=!1):(t("error",i.message),ve.value=!1)},Pe=N(!1),qe=((e,a)=>{let l;return function(...t){l||(e.apply(this,t),l=setTimeout((()=>{l=null}),a))}})((()=>{c(he.value.contractCode).then((e=>{200===e.code?(Object.keys(e.data).forEach((a=>{e.data[a]&&(he.value[a]=e.data[a])})),e.data.contractCode?Pe.value=!0:t("error","合同编号不存在！")):t("error",e.message)}))}),1e3),Ee=N(["",""]);let Le={};N({});const Ve=async e=>{await new Promise((e=>{o().then((a=>{Le={};const l=a.data.map((e=>{var a;return Le[e.id]=e.tags,ke.value.push(e.id),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(_e.value.set(`${a.tagName}`,a.color),we.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));je.value=l.filter((e=>e.children)),e(je.value)}))}));const a=[];e.tagIds.forEach((e=>{a.push([we.value.get(e),e])})),G((()=>{he.value.tagIds=a}))},Oe=()=>{ve.value||(me.value.resetFields(),pe.value=!1,ve.value=!1,Pe.value=!1)},Fe=()=>{let e=[];return he.value.tagIds.forEach((a=>{1===a.length?e=e.concat(ye.value.get(a[0])):e.push(a[1])})),e};return J({init:async e=>{const a=await l(e.exampleId),{data:s}=a;s?(Pe.value=!0,pe.value=!0,(null==s?void 0:s.tagIds.length)&&Ve(s),me.value&&me.value.resetFields(),Object.keys(s).forEach((e=>{he.value[e]=s[e]}))):t("error","数据错误"),u({code:"MING_DAO_YUN_TONG_BU"}).then((e=>{var a,l;"是"===(null==(l=null==(a=e.data)?void 0:a.rows[0])?void 0:l.value)?be.value=!0:(be.value=!1,fe.value=0)}))}}),(e,a)=>{const l=g,s=h,u=b,o=j,i=_,n=f,c=k,p=w,x=y,N=C,Y=M,G=S,J=T("DeleteOutlined"),de=T("question-circle-outlined"),ie=I,ne=D,ke=m,ye=U,we=v;return H(),P(we,{width:1200,title:"编辑项目案例","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:pe.value,"confirm-loading":ve.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[25]||(a[25]=e=>{ve.value||(ve.value=!0,me.value.validate().then((()=>{const e={...he.value,projectPracticalSummary:JSON.stringify(Ee.value),tagIds:Fe()};r(e).then((async e=>{var a;ve.value=!1,200===e.code?(t("success","项目案例编辑成功"),he.value.tagIds.forEach((e=>{if(e[1]){const{id:a,tagName:l,color:t}=Le[e[0]].find((a=>a.id===e[1]))}else e[0]&&Le[e[0]].forEach((e=>{const{id:a,tagName:l,color:t}=e}))})),await d({id:null==(a=e.data)?void 0:a.id,approve:1,approveRemark:""}),Oe(),ce("ok")):t("error",e.message)}))})).catch((e=>{ve.value=!1})))}),onCancel:Oe},{default:q((()=>[E(ye,{spinning:ve.value,style:{position:"fixed",top:"250px"}},{default:q((()=>[E(ke,{ref_key:"formRef",ref:me,model:he.value,rules:ge,"label-align":"left"},{default:q((()=>[L("header",null,[L("div",K,[E(n,{md:24,sm:24},{default:q((()=>[E(u,{class:"project-code-item keep-px",name:"contractCode",label:"合同编号","has-feedback":""},{default:q((()=>[L("div",Q,[E(u,{style:{"margin-bottom":"-24px"}},{default:q((()=>[be.value?(H(),P(s,{key:0,placeholder:"请选择",value:fe.value,"onUpdate:value":a[0]||(a[0]=e=>fe.value=e),style:{width:"100px",padding:"0"}},{default:q((()=>[E(l,{value:1},{default:q((()=>a[26]||(a[26]=[O("同步")]))),_:1}),E(l,{value:0},{default:q((()=>a[27]||(a[27]=[O("自定义")]))),_:1})])),_:1},8,["value"])):V("",!0)])),_:1}),E(o,{value:he.value.contractCode,"onUpdate:value":a[1]||(a[1]=e=>he.value.contractCode=e),disabled:Pe.value,style:F({width:be.value&&1===fe.value?"calc(100% - 180px)":be.value&&0===fe.value?"calc(100% - 80px)":"100%"}),placeholder:"请输入合同编号"},null,8,["value","disabled","style"]),be.value&&1===fe.value?(H(),P(i,{key:0,disabled:!he.value.contractCode,style:{"min-width":"80px",padding:"0"},type:"primary",onClick:$(qe)},{default:q((()=>a[28]||(a[28]=[O("同步")]))),_:1},8,["disabled","onClick"])):V("",!0)])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"productName",label:"产品信息(合同)","has-feedback":""},{default:q((()=>[E(o,{value:he.value.productName,"onUpdate:value":a[2]||(a[2]=e=>he.value.productName=e),placeholder:"请输入产品信息(合同)"},null,8,["value"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"techPlatform",label:"采纳的技术平台","has-feedback":""},{default:q((()=>[E(o,{value:he.value.techPlatform,"onUpdate:value":a[3]||(a[3]=e=>he.value.techPlatform=e),placeholder:"请输入采纳的技术平台"},null,8,["value"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"projectManager",label:"项目经理","has-feedback":""},{default:q((()=>[E(o,{value:he.value.projectManager,"onUpdate:value":a[4]||(a[4]=e=>he.value.projectManager=e),disabled:1===fe.value,placeholder:"请输入项目经理"},null,8,["value","disabled"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"saleSupporter",label:"支持售前","has-feedback":""},{default:q((()=>[E(o,{value:he.value.saleSupporter,"onUpdate:value":a[5]||(a[5]=e=>he.value.saleSupporter=e),placeholder:"请输入支持售前"},null,8,["value"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"ultimateCustomer",label:"最终客户名称","has-feedback":""},{default:q((()=>[E(o,{value:he.value.ultimateCustomer,"onUpdate:value":a[6]||(a[6]=e=>he.value.ultimateCustomer=e),placeholder:"请输入最终客户名称"},null,8,["value"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"projectStartDate",label:"项目启动时间","has-feedback":""},{default:q((()=>[E(c,{value:he.value.projectStartDate,"onUpdate:value":a[7]||(a[7]=e=>he.value.projectStartDate=e),disabled:1===fe.value,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",placeholder:"请选择项目启动时间"},null,8,["value","disabled"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"projectStatus",label:"项目状态","has-feedback":""},{default:q((()=>[E(o,{value:he.value.projectStatus,"onUpdate:value":a[8]||(a[8]=e=>he.value.projectStatus=e),disabled:1===fe.value,placeholder:"请输入项目状态"},null,8,["value","disabled"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"tagIds",label:"资源标签","has-feedback":""},{default:q((()=>[E(x,{value:he.value.tagIds,"onUpdate:value":a[9]||(a[9]=e=>he.value.tagIds=e),defaultValue:he.value.tagIds,"show-checked-strategy":$(y).SHOW_CHILD,style:{width:"100%"},multiple:"","show-search":{filter:De},"max-tag-count":"responsive",options:je.value,placeholder:"请选择标签",checkStrictly:!0},{tagRender:q((e=>{return[(H(),P(p,{key:e.value,color:(a=e.label,_e.value.get(a)||"blue")},{default:q((()=>[O(z(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","show-search","options"])])),_:1})])),_:1})]),L("div",W,[E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"projectName",label:"项目名称","has-feedback":""},{default:q((()=>[E(o,{value:he.value.projectName,"onUpdate:value":a[10]||(a[10]=e=>he.value.projectName=e),disabled:1===fe.value,placeholder:"请输入项目名称"},null,8,["value","disabled"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"productVersion",label:"产品版本","has-feedback":""},{default:q((()=>[E(o,{value:he.value.productVersion,"onUpdate:value":a[11]||(a[11]=e=>he.value.productVersion=e),placeholder:"请输入产品版本"},null,8,["value"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"saleDepartment",label:"所属部门(销售)","has-feedback":""},{default:q((()=>[E(o,{value:he.value.saleDepartment,"onUpdate:value":a[12]||(a[12]=e=>he.value.saleDepartment=e),disabled:1===fe.value,placeholder:"请输入所属部门(销售)"},null,8,["value","disabled"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"salesManager",label:"销售经理","has-feedback":""},{default:q((()=>[E(o,{value:he.value.salesManager,"onUpdate:value":a[13]||(a[13]=e=>he.value.salesManager=e),disabled:1===fe.value,placeholder:"请输入销售经理"},null,8,["value","disabled"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"industry",label:"行业","has-feedback":""},{default:q((()=>[E(o,{value:he.value.industry,"onUpdate:value":a[14]||(a[14]=e=>he.value.industry=e),disabled:1===fe.value,placeholder:"请输入行业"},null,8,["value","disabled"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"contractedCustomer",label:"签约客户名称","has-feedback":""},{default:q((()=>[E(o,{value:he.value.contractedCustomer,"onUpdate:value":a[15]||(a[15]=e=>he.value.contractedCustomer=e),disabled:1===fe.value,placeholder:"请输入签约客户名称"},null,8,["value","disabled"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"projectEndDate",label:"项目完成时间","has-feedback":""},{default:q((()=>[E(c,{value:he.value.projectEndDate,"onUpdate:value":a[16]||(a[16]=e=>he.value.projectEndDate=e),disabled:1===fe.value,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",placeholder:"请选择项目完成时间"},null,8,["value","disabled"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"screenResolution",label:"屏幕分辨率","has-feedback":""},{default:q((()=>[E(o,{value:he.value.screenResolution,"onUpdate:value":a[17]||(a[17]=e=>he.value.screenResolution=e),placeholder:"请输入屏幕分辨率"},null,8,["value"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"projectAccessLinker",label:"项目地址","has-feedback":""},{default:q((()=>[E(o,{value:he.value.projectAccessLinker,"onUpdate:value":a[18]||(a[18]=e=>he.value.projectAccessLinker=e),placeholder:"请输入项目地址"},null,8,["value"])])),_:1})])),_:1})])]),E(ne,{gutter:24},{default:q((()=>[E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"projectSimpleDescription",label:"一句话简介","has-feedback":""},{default:q((()=>[E(N,{value:he.value.projectSimpleDescription,"onUpdate:value":a[19]||(a[19]=e=>he.value.projectSimpleDescription=e),rows:4,placeholder:"(在什么样的背景下)利用了什么产品，整合了什么资源，形成了什么类型的平台，为用户带来了什么样的价值。"},null,8,["value"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"proSituation",label:"项目整体需求情况","has-feedback":""},{default:q((()=>[E(N,{value:he.value.overallDemand,"onUpdate:value":a[20]||(a[20]=e=>he.value.overallDemand=e),disabled:"",rows:4},null,8,["value"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"projectScope",label:"项目范围","has-feedback":""},{default:q((()=>[E(N,{value:he.value.projectScope,"onUpdate:value":a[21]||(a[21]=e=>he.value.projectScope=e),rows:4,placeholder:"请输入项目的交付范围（精炼版sow）"},null,8,["value"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"functionalDecompositio",label:"功能介绍分析","has-feedback":""},{default:q((()=>[E(N,{value:he.value.functionalDecompositio,"onUpdate:value":a[22]||(a[22]=e=>he.value.functionalDecompositio=e),rows:4,placeholder:"业务场景分解描述，成果需确认无敏感内容"},null,8,["value"])])),_:1})])),_:1}),E(n,{md:24,sm:24},{default:q((()=>[E(u,{name:"successM",label:"项目实战经验总结","has-feedback":""},{default:q((()=>[E(Y,null,{default:q((()=>[E(N,{value:Ee.value[0],"onUpdate:value":a[23]||(a[23]=e=>Ee.value[0]=e),style:{width:"49.5%","margin-right":"1%"},rows:4,placeholder:"项目最成功的1-5个因素"},null,8,["value"]),E(N,{value:Ee.value[1],"onUpdate:value":a[24]||(a[24]=e=>Ee.value[1]=e),style:{width:"49.5%"},rows:4,placeholder:"项目最痛苦的1-5个因素"},null,8,["value"])])),_:1})])),_:1})])),_:1}),E(n,{md:24,sm:24,class:"form-item"},{default:q((()=>[E(u,{name:"deliverablesImages",label:"上传图片","has-feedback":""},{default:q((()=>[L("div",Z,[E(i,{class:"upload-btn",type:"primary"},{default:q((()=>[a[29]||(a[29]=O(" 点击上传 ")),L("input",{type:"file",class:"input-file",accept:Ne.value.map((e=>`.${e}`)).join(","),onChange:Ye},null,40,X)])),_:1})]),L("div",ee,[(H(!0),A(B,null,R(he.value.deliverablesImages,((e,a)=>(H(),A("div",{key:e,class:"file-item"},[L("div",{class:"file-name",onClick:a=>Ue(!0,e)},[E(G,{width:48,height:48,src:xe(e)},null,8,["src"]),O(" "+z(e),1)],8,ae),E(J,{class:"delete-icon",onClick:e=>(e=>{he.value.deliverablesImages.splice(e,1)})(a)},null,8,["onClick"])])))),128)),E(G,{width:200,style:{display:"none"},preview:{visible:Se.value,onVisibleChange:Ue},src:Ie.value},null,8,["preview","src"])])])),_:1}),L("span",le,[E(ie,{title:"支持文件格式: png、jpeg、jpg、gif, 大小限制: 5M",placement:"right"},{default:q((()=>[E(de,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),E(n,{md:24,sm:24,class:"form-item"},{default:q((()=>[E(u,{name:"deliverablesVideo",label:"上传视频","has-feedback":""},{default:q((()=>[L("div",te,[E(i,{class:"upload-btn",type:"primary"},{default:q((()=>[a[30]||(a[30]=O(" 点击上传 ")),L("input",{type:"file",class:"input-file",accept:Ce.value.map((e=>`.${e}`)).join(","),onChange:Me},null,40,se)])),_:1})]),L("div",ue,[(H(!0),A(B,null,R(he.value.deliverablesVideo,((e,a)=>(H(),A("div",{key:e,class:"file-item"},[L("div",{class:"file-name",onClick:a=>(e=>{window.open(xe(e),"_blank")})(e)},z(e),9,oe),E(J,{class:"delete-icon",onClick:e=>(e=>{he.value.deliverablesVideo.splice(e,1)})(a)},null,8,["onClick"])])))),128))])])),_:1}),L("span",re,[E(ie,{title:"支持文件格式: mp4, 大小限制:1024M",placement:"right"},{default:q((()=>[E(de,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-1a5cf0b5"]]);export{de as default};
