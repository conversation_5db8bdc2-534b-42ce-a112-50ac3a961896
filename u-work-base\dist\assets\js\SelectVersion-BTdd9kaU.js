import{s as e,b as s}from"./main-Djn9RDyT.js";import{d as a}from"./systemAuthoriza-D4YElOeQ.js";import{S as o,F as t,c as i,q as r,d as l,w as n,x as m,M as p}from"./ant-design-vue-DYY9BtJq.js";import{d as u,r as c,a as d,am as j,a9 as v,o as f,aa as h,c as b,e as g,b as y,F as _,ag as k,J as w,ad as x,n as D}from"./@vue-HScy-mz9.js";import{_ as z}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const q="/edtap/data-migration/version";const F={class:"form-item-notice"},A=z(u({__name:"SelectVersion",setup(u,{expose:z}){const A=c(!1),E=c(!1),J=c(),M=d({businessData:!0,version:"",code:""}),S=c([]),U={version:[{required:!0,message:"请选择数据迁移版本！"}]},$=()=>{J.value.resetFields(),A.value=!1,E.value=!1},B=()=>{S.value=[];const s=[];var a;(a={businessData:M.businessData},e({url:q,method:"get",params:a})).then((e=>{e.success&&(e.data.forEach((e=>{s.push({id:e,name:e})})),S.value=s,D((()=>{S.value.length&&(M.version=S.value.id)})))}))};return z({init:e=>{A.value=!0,D((()=>{J.value.resetFields(),M.code=e,B()}))}}),(e,u)=>{const c=r,d=j("question-circle-outlined"),D=l,z=i,q=m,B=n,C=t,H=o,I=p;return f(),v(I,{width:678,title:"选择版本","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:A.value,"confirm-loading":E.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:u[2]||(u[2]=e=>(E.value=!0,void J.value.validate().then((()=>{try{a({code:M.code,tag:M.version,businessData:M.businessData}).then((e=>{e.success?(s("success",`系统正在打包项目【${M.code}】系统数据，这可能需要一段时间，现在您可以进行其他操作，完成后将通知您`),$()):e.message&&s("error",e.message),E.value=!1}))}catch(e){E.value=!1}})).catch((e=>{E.value=!1})))),onCancel:$},{default:h((()=>[b(H,{spinning:E.value},{default:h((()=>[b(C,{ref_key:"formRef",ref:J,model:M,rules:U,"label-align":"left"},{default:h((()=>[b(z,{label:"是否导出业务数据","has-feedback":"",class:"form-item"},{default:h((()=>[b(c,{checked:M.businessData,"onUpdate:checked":u[0]||(u[0]=e=>M.businessData=e)},null,8,["checked"]),g("span",F,[b(D,{title:"业务数据具体指：孪生体点位数据、告警和性能指标、视点动画、部门和用户信息、日志和系统配置（除字典管理）相关数据",placement:"right"},{default:h((()=>[b(d,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),b(z,{name:"version",label:"请选择版本","has-feedback":""},{default:h((()=>[b(B,{value:M.version,"onUpdate:value":u[1]||(u[1]=e=>M.version=e),placeholder:"请选择版本"},{default:h((()=>[(f(!0),y(_,null,k(S.value,((e,s)=>(f(),v(q,{key:s,value:e.id},{default:h((()=>[w(x(e.name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-311ca368"]]);export{A as default};
