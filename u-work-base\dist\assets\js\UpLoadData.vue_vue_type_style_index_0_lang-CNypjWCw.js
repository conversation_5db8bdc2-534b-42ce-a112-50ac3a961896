import{d as e,r as a,a as t,a9 as l,o as s,aa as o,c as n,y as i,e as r,u as p,J as c,G as u}from"./@vue-HScy-mz9.js";import{s as d}from"./store2-5Qza9FG8.js";import{b as m}from"./main-Djn9RDyT.js";import{a as f,b as v}from"./systemAuthoriza-D4YElOeQ.js";import{F as g,c as h,U as y,k as _,M as z}from"./ant-design-vue-DYY9BtJq.js";import{ab as x}from"./@ant-design-CA72ad83.js";const b={class:"ant-upload-drag-icon"},w={class:"prompt"},k={class:"prompt_item"},j={class:"prompt_item"},D=e({__name:"UpLoadData",emits:["ok"],setup(e,{expose:D,emit:N}){const O=N,E={authorization:"authorization-text"},F=a(!1),T=a(!1),A=a([]),C=e=>{const{file:a}=e;A.value=[a]},U=t({title:"",type:""}),q=()=>{A.value=[]},G=()=>{U.title="",U.type="",F.value=!1,A.value=[],T.value=!1},I=t({flag:!1,percent:0}),J=e=>{e&&e.loaded&&e.total&&(I.percent=Number((e.loaded/e.total*90).toFixed(2)))},L=()=>{const e=new FormData;e.set("file",A.value[0]),e.set("code",d.get("TENANT_CODE")),I.flag=!0,I.percent=0,f(e,J).then((e=>{200===e.code?e.data.length&&O("ok",e.data):m("warning",e.message),T.value=!1,I.flag=!1,I.percent=0,G()})).catch((()=>{T.value=!1,I.flag=!1,I.percent=0,G()}))},M=()=>{const e=new FormData;e.set("file",A.value[0]),e.set("code",d.get("TENANT_CODE")),I.flag=!0,I.percent=0,v(e,J).then((e=>{200!==e.code&&m("warning",e.message),T.value=!1,I.flag=!1,I.percent=0,G()})).catch((()=>{T.value=!1,I.flag=!1,I.percent=0,G()}))},P=e=>{const a=e.name;return".zip"===a.substring(a.lastIndexOf("."))||(A.value=[],m("error","请上传.zip格式的文件"),!1)};return D({init:(e,a)=>{U.title=e,U.type=a,F.value=!0,A.value=[]}}),(e,a)=>{const t=y,d=_,f=h,v=g,D=z;return s(),l(D,{width:528,title:U.title,"wrap-class-name":"cus-modal",open:F.value,"confirm-loading":T.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[0]||(a[0]=e=>(T.value=!0,void(A.value.length>0?"data"===U.type?L():"file"===U.type&&M():(m("warning","请选择要上传的zip文件"),T.value=!1)))),onCancel:G},{default:o((()=>[n(v,{ref:"formRef"},{default:o((()=>[n(f,{"has-feedback":""},{default:o((()=>[n(t,{name:"file","custom-request":C,"before-upload":P,"show-upload-list":!0,multiple:!1,headers:E,accept:".zip","file-list":A.value,remove:q},{default:o((()=>[r("a",b,[n(p(x),{class:"UploadOutlined",style:{"font-size":"40px",color:"var(--upload-icon-color)"}})]),a[1]||(a[1]=r("p",null,[c("将文件拖至此处，或点击 "),r("a",null,"上传数据")],-1)),a[2]||(a[2]=r("p",{class:"ant-upload-hint",style:{"padding-top":"6px",color:"var(--upload-icon-color)"}},"支持文件格式: .zip",-1))])),_:1},8,["file-list"]),i(r("div",null,[n(d,{percent:I.percent,size:"small"},null,8,["percent"])],512),[[u,I.flag]]),i(r("div",w,[i(r("span",k,"小锘正努力迁移数据中，请耐心等待迁移完成",512),[[u,"data"===U.type]]),i(r("span",j,"小锘正努力迁移文件中，请耐心等待迁移完成",512),[[u,"file"===U.type]])],512),[[u,I.flag]])])),_:1})])),_:1},512)])),_:1},8,["title","open","confirm-loading"])}}});export{D as _};
