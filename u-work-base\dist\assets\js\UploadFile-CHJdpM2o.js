import{d as e,r as a,a as s,a9 as l,o as t,aa as o,c as r,ab as i,y as u,e as p,u as n,J as m,G as d}from"./@vue-HScy-mz9.js";import{b as v}from"./main-Djn9RDyT.js";import{u as c,h as j}from"./operationAnalysis-D3RTU-GI.js";import{S as f,F as g,c as h,U as b,k as z,I as y,h as _,R as N,M as k}from"./ant-design-vue-DYY9BtJq.js";import{ab as x}from"./@ant-design-CA72ad83.js";import{_ as w}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const F={class:"ant-upload-drag-icon"},I=w(e({__name:"UploadFile",emits:["ok"],setup(e,{expose:w,emit:I}){const q=I,U=a(!1),M=a(!1),R=a(),A=s({name:"",assetsName:"",authorized:1}),C=a(),D={xs:{span:18},sm:{span:6}},G={assetsName:[{required:!0,message:"请输入文件名称!"}],authorized:[{required:!0,message:"请选择是否授权!"}]};a("");const J=()=>{C.value=[],A.assetsName=""},O=()=>{R.value.resetFields(),C.value=[],U.value=!1,M.value=!1},S=a(),$=e=>{if(C.value=[e.file],C.value&&C.value.length){M.value=!0;const a=new FormData;a.append("file",C.value[0]),a.append("bucketName","webassets"),B.value.percent=0,B.value.progressFlag=!0,c(a,E).then((a=>{200===a.code?(R.value.assetsName=e.file.name,R.value.fileName=e.file.name,R.value.fileId=a.data,A.assetsName=e.file.name,M.value=!1):(M.value=!1,v("error",a.message)),B.value.percent=0,B.value.progressFlag=!1})).catch((()=>{M.value=!1,B.value.percent=0,B.value.progressFlag=!1}))}},B=a({percent:0,progressFlag:!1}),E=e=>{e&&e.loaded&&e.total&&(B.value.percent=Math.round(100*e.loaded/e.total))},H=e=>{const a=e.name,s=a.substring(a.lastIndexOf("."));return".js"===s||".zip"===s||(C.value=[],v("error","请上传.zip,.js格式的文件"),!1)};return w({init:e=>{U.value=!0,S.value=e.code}}),(e,a)=>{const s=b,c=z,w=h,I=y,E=N,K=_,L=g,Q=f,T=k;return t(),l(T,{title:"上传文件","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:U.value,"confirm-loading":M.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[2]||(a[2]=e=>(()=>{const e={assetsCode:S.value,assetsFileId:R.value.fileId,id:R.value.fileId,assetsName:R.value.assetsName,authorized:A.authorized,enableStatus:"0"};R.value.validate().then((()=>{C.value&&C.value.length&&(M.value=!0,j(e).then((e=>{200===e.code?(v("success",`${R.value.assetsName}上传成功`),O(),q("ok")):(v("success",e.message),M.value=!1)})).catch((()=>{M.value=!1})))})).catch((e=>{M.value=!1}))})()),onCancel:O},{default:o((()=>[r(Q,{spinning:M.value},{default:o((()=>[r(L,{ref_key:"formRef",ref:R,model:A,rules:G,"label-align":"left"},{default:o((()=>[r(w,{label:" "},{default:o((()=>[r(s,{name:"pictre","custom-request":$,"show-upload-list":!0,multiple:!1,"before-upload":H,accept:".js,.zip","file-list":C.value,onRemove:J},{default:o((()=>[p("p",F,[r(n(x),{style:{"font-size":"40px",color:"var(--upload-icon-color)"}})]),a[3]||(a[3]=p("p",{style:{"font-size":"14px"}},[m("将文件拖至此处，或点击 "),p("a",null,"上传文件")],-1)),a[4]||(a[4]=p("p",{class:"ant-upload-hint",style:{"padding-top":"6px","font-size":"12px",color:"var(--upload-icon-color)"}},"支持.js和.zip格式",-1))])),_:1},8,["file-list"]),u(p("div",null,[r(c,{percent:B.value.percent,size:"small"},null,8,["percent"])],512),[[d,B.value.progressFlag]])])),_:1}),r(w,{name:"assetsName",label:"文件名称","label-col":D,"has-feedback":""},{default:o((()=>[r(I,{value:A.assetsName,"onUpdate:value":a[0]||(a[0]=e=>A.assetsName=e),placeholder:"请输入文件名称"},null,8,["value"])])),_:1}),"thingjs"===S.value?(t(),l(w,{key:0,name:"authorized",label:"是否需授权","label-col":D,"has-feedback":""},{default:o((()=>[r(K,{value:A.authorized,"onUpdate:value":a[1]||(a[1]=e=>A.authorized=e),name:"radioGroup"},{default:o((()=>[r(E,{value:1},{default:o((()=>a[5]||(a[5]=[m("是")]))),_:1}),r(E,{value:0},{default:o((()=>a[6]||(a[6]=[m("否")]))),_:1})])),_:1},8,["value"])])),_:1})):i("",!0)])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-855390b9"]]);export{I as default};
