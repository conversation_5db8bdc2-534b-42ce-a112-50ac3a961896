import{d as e,r as o,a9 as s,o as a,aa as r,c as t,y as l,e as i,u as p,J as n,G as m}from"./@vue-HScy-mz9.js";import{b as u}from"./main-Djn9RDyT.js";import{u as c}from"./projectGallery-xT8wgNPG.js";import{ab as j}from"./@ant-design-CA72ad83.js";import{U as d,k as v,B as f,M as g}from"./ant-design-vue-DYY9BtJq.js";import{_ as y}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const b={class:"ant-upload-drag-icon"},z=y(e({__name:"UploadFile",emits:["ok"],setup(e,{expose:y,emit:z}){const h=z,x=o(!1),_=o(!1),w=o(),k=()=>{w.value=[]},F=()=>{_.value||(w.value=[],x.value=!1,_.value=!1)},M=e=>{const o=e.name;return".zip"===o.substring(o.lastIndexOf("."))||(w.value=[],u("error","请上传.zip格式的文件"),!1)},q=o({percent:0,progressFlag:!1}),B=e=>{e&&e.loaded&&e.total&&(q.value.percent=Math.round(100*e.loaded/e.total))},C=e=>{if(w.value=[e.file],w.value&&w.value.length){_.value=!0;const e=new FormData;e.append("file",w.value[0]),e.append("approve","1"),q.value.percent=0,q.value.progressFlag=!0,c(e,B).then((e=>{_.value=!1,200===e.code?(u("success","提交成功，请等待管理员审批"),F(),h("ok")):u("error",e.message),q.value.percent=0,q.value.progressFlag=!1})).catch((()=>{_.value=!1,q.value.percent=0,q.value.progressFlag=!1}))}else u("error","请上传文件")};return y({init:()=>{x.value=!0}}),(e,o)=>{const u=d,c=v,y=f,z=g;return a(),s(z,{title:"上传模板","body-style":{maxHeight:"600px"},"wrap-class-name":"cus-modal",open:x.value,"confirm-loading":_.value,"mask-closable":!1,onCancel:F},{footer:r((()=>[t(y,{disabled:_.value,onClick:F},{default:r((()=>o[2]||(o[2]=[n("关闭")]))),_:1},8,["disabled"])])),default:r((()=>[t(u,{name:"pictre","before-upload":M,"custom-request":C,"show-upload-list":!0,multiple:!1,"max-count":1,accept:".zip","file-list":w.value,onRemove:k},{default:r((()=>[i("p",b,[t(p(j),{style:{"font-size":"40px",color:"var(--upload-icon-color)"}})]),o[0]||(o[0]=i("p",{style:{"font-size":"14px"}},[n("将文件拖至此处，或点击 "),i("a",null,"上传文件")],-1)),o[1]||(o[1]=i("p",{class:"ant-upload-hint",style:{"padding-top":"6px","font-size":"12px",color:"var(--upload-icon-color)"}},"支持.zip格式",-1))])),_:1},8,["file-list"]),l(i("div",null,[t(c,{percent:q.value.percent,size:"small"},null,8,["percent"])],512),[[m,q.value.progressFlag]])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-fa1978a6"]]);export{z as default};
