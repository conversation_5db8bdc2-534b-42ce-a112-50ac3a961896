import{g as e,c as a,d as s,r as o}from"./screenTemplate-Brx_fHCI.js";import{u as t,b as r}from"./main-Djn9RDyT.js";import{S as i,F as l,b as m,c as n,w as c,x as p,d as u,M as d}from"./ant-design-vue-DYY9BtJq.js";import{d as j,r as v,p as f,am as g,a9 as h,o as k,aa as b,c as y,e as _,b as w,F as x,ag as P,u as I,J as z,ad as T}from"./@vue-HScy-mz9.js";import{_ as q}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const C={class:"form-item-notice keep-px"},F=q(j({__name:"UseTemp",emits:["ok"],setup(j,{expose:q,emit:F}){const J=t(),M=F,N=v(!1),U=v(!1),A=v(),B=v({checkedProject:null}),D={checkedProject:[{required:!0,message:"请选择项目",trigger:"change"}]},E=v(),K=v([]),L=async()=>{const a=await e({userId:J.userInfo.id});200===a.code&&(K.value=a.data.rows||[])},O=f((()=>K.value.length&&null!==K.value[0]?K.value.map((e=>({label:e.name,value:e.code}))):[])),R=()=>{N.value=!1,B.value.checkedProject=null,U.value=!1},S=e=>{B.value.checkedProject=e};return q({init:e=>{E.value=e,N.value=!0,L()}}),(e,t)=>{const j=p,v=c,f=n,q=g("question-circle-outlined"),F=u,J=m,K=l,L=i,W=d;return k(),h(W,{width:400,title:"使用模板","body-style":{minWidth:"400px",height:"80px",overflow:"hidden"},"wrap-class-name":"cus-modal keep-px",open:N.value,centered:"","confirm-loading":U.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:t[1]||(t[1]=e=>{A.value.validate().then((async()=>{var e,t;const i=await a({id:(null==(e=E.value.sceneTemplate)?void 0:e.sceneId)||E.value.id});if(200!==i.code)return void r("error",i.message);const l=i.data.id,m=await s((null==(t=E.value.sceneTemplate)?void 0:t.id)||E.value.id);if(200!==m.code)return void r("error",m.message);const n=await o({groupIds:[B.value.checkedProject],sceneId:Number(l),componentId:"",toPro:!0});200===n.code?(r("success","大屏创建成功，请进入项目中查看"),N.value=!1,U.value=!1,M("ok")):r("error",n.message)}))}),onCancel:R},{default:b((()=>[y(L,{spinning:U.value},{default:b((()=>[y(K,{ref_key:"formRef",ref:A,model:B.value,rules:D,"label-align":"left"},{default:b((()=>[y(J,{md:24,sm:24,class:"form-item"},{default:b((()=>[y(f,{name:"checkedProject",label:"项目名称","has-feedback":""},{default:b((()=>[y(v,{ref:"select",value:B.value.checkedProject,"onUpdate:value":t[0]||(t[0]=e=>B.value.checkedProject=e),placeholder:"请选择项目",onChange:S},{default:b((()=>[(k(!0),w(x,null,P(I(O),((e,a)=>(k(),h(j,{key:a,value:e.value},{default:b((()=>[z(T(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1}),_("span",C,[y(F,{title:"创建资源模板前, 请先加入项目",placement:"right"},{default:b((()=>[y(q,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-d90c1b4b"]]);export{F as default};
