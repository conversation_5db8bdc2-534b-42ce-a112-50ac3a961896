import{g as e,r as a}from"./screenTemplate-Brx_fHCI.js";import{s as t,u as o,b as s}from"./main-Djn9RDyT.js";import{S as l,F as n,b as c,c as r,w as d,x as u,d as m,M as p}from"./ant-design-vue-DYY9BtJq.js";import{d as i,r as v,p as f,am as h,a9 as g,o as k,aa as _,c as b,e as j,b as y,F as P,ag as x,u as w,J as I,ad as q}from"./@vue-HScy-mz9.js";import{_ as O}from"./vue-qr-CB2aNKv5.js";const S=e=>t({url:"/edtap/annotate-component/page-component",method:"post",data:e});function T(e){return t({url:"/edtap/annotate-component/delete-component",method:"post",data:e})}function U(){return t({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"3"}})}const C=e=>t({url:"/edtap/annotate-component/modify-component",method:"post",data:e}),F={class:"form-item-notice keep-px"},M=O(i({__name:"UseTemp",emits:["ok"],setup(i,{expose:O,emit:S}){const T=o(),U=S,C=v(!1),M=v(!1),z=v(),G=v({checkedProject:null}),J={checkedProject:[{required:!0,message:"请选择项目",trigger:"change"}]},N=v(),R=v([]),W=async()=>{const a=await e({userId:T.userInfo.id});200===a.code&&(R.value=a.data.rows||[])},Z=f((()=>R.value.length&&null!==R.value[0]?R.value.map((e=>({label:e.name,value:e.code}))):[])),$=()=>{C.value=!1,G.value.checkedProject="",M.value=!1},A=e=>{G.value.checkedProject=e},B=async()=>{z.value.validate().then((async()=>{const e=await(o={id:N.value.publishId,name:`${N.value.name}副本`},t({url:"/edtap/component/copy",method:"post",data:o}));var o;if(200!==e.code)return void s("error",e.message);const l=e.data,n=await a({groupIds:[G.value.checkedProject],componentId:l,toPro:!0});200===n.code?(s("success","图表创建成功，请进入项目中查看"),C.value=!1,M.value=!1,U("ok")):s("error",n.message)}))};return O({init:e=>{N.value=e,C.value=!0,W()}}),(e,a)=>{const t=u,o=d,s=r,i=h("question-circle-outlined"),v=m,f=c,O=n,S=l,T=p;return k(),g(T,{width:400,title:"使用模板","body-style":{minWidth:"400px",height:"80px",overflow:"hidden"},"wrap-class-name":"cus-modal keep-px",open:C.value,centered:"","confirm-loading":M.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[1]||(a[1]=e=>B()),onCancel:$},{default:_((()=>[b(S,{spinning:M.value},{default:_((()=>[b(O,{ref_key:"formRef",ref:z,model:G.value,rules:J,"label-align":"left"},{default:_((()=>[b(f,{md:24,sm:24,class:"form-item"},{default:_((()=>[b(s,{name:"checkedProject",label:"项目名称","has-feedback":""},{default:_((()=>[b(o,{ref:"select",value:G.value.checkedProject,"onUpdate:value":a[0]||(a[0]=e=>G.value.checkedProject=e),placeholder:"请选择项目",onChange:A},{default:_((()=>[(k(!0),y(P,null,x(w(Z),((e,a)=>(k(),g(t,{key:a,value:e.value},{default:_((()=>[I(q(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1}),j("span",F,[b(v,{title:"创建资源模板前, 请先加入项目",placement:"right"},{default:_((()=>[b(i,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-77a701f2"]]),z=Object.freeze(Object.defineProperty({__proto__:null,default:M},Symbol.toStringTag,{value:"Module"}));export{M as U,S as a,z as b,U as c,C as p,T as s};
