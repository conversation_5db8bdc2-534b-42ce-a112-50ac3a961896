import{a as e,q as s}from"./main-Djn9RDyT.js";import{I as t,B as a,f as o}from"./ant-design-vue-DYY9BtJq.js";import{d as i,a as r,b as l,e as n,c as d,aa as p,J as c,ab as m,ad as u,o as j}from"./@vue-HScy-mz9.js";import{_ as y}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const g={class:"user-manage keep-px"},v={class:"right"},h={class:"search-wrap"},w={class:"search-content"},f={class:"search-item"},b={class:"table-wrap"},R={ref:"table",class:"table-content"},S=["title"],D=y(i({__name:"UserList",emits:["userSelectChange"],setup(i,{expose:y,emit:D}){e();const K=[{title:"账号",dataIndex:"account",sortDirections:["descend","ascend"],sorter:!0,ellipsis:!0},{title:"姓名",dataIndex:"name",sortDirections:["descend","ascend"],sorter:!0,ellipsis:!0}],I=r({pageNo:1,pageSize:9999,searchValue:"",searchStatus:null,tableData:[],selectedRowKeys:[],enterpriseId:"",total:0,deptId:"",deptName:"",loading:!1}),z=()=>{I.pageNo=1,I.pageSize=9999,N()},N=(e={})=>{I.loading=!0,I.tableData=[],I.total=0;const t={pageNo:I.pageNo,pageSize:I.pageSize,name:I.searchValue,enterpriseId:I.enterpriseId};s(t).then((e=>{I.tableData=e.data.rows||[],I.total=e.data.totalRows})).finally((()=>{I.loading=!1})).catch((()=>{I.loading=!1}))},k=D,C=(e,s)=>{s?(I.selectedRowKeys=[...I.selectedRowKeys,e.id],k("userSelectChange",{id:e.id,name:e.name,account:e.account,type:"add"})):(I.selectedRowKeys=I.selectedRowKeys.filter((s=>s!==e.id)),k("userSelectChange",{id:e.id,name:e.name,account:e.account,type:"delete"}))},_=(e,s,t)=>{let a={};a=t.order?{sortField:t.field,sortRule:"descend"===t.order?"DESC":"ASC"}:{sortField:"",sortRule:""},N(a)};return y({changeData:(e,s)=>{if("delete"===s)I.selectedRowKeys=I.selectedRowKeys.filter((s=>s!==e.id));else if("init"===s){const s=e.userList||[];I.selectedRowKeys=null==s?void 0:s.map((e=>e.id)),I.enterpriseId=e.enterpriseId,z()}}}),(e,s)=>{const i=t,r=a,y=o;return j(),l("div",g,[n("div",v,[n("div",h,[n("div",w,[n("div",f,[s[2]||(s[2]=n("span",{class:"search-label"},"关键词",-1)),d(i,{value:I.searchValue,"onUpdate:value":s[0]||(s[0]=e=>I.searchValue=e),valueModifiers:{trim:!0},class:"search-input","allow-clear":"",placeholder:"请输入账号、姓名",onPressEnter:z},null,8,["value"]),d(r,{type:"primary",class:"search-btn",onClick:z},{default:p((()=>s[1]||(s[1]=[c("搜索")]))),_:1})])])]),n("div",b,[n("div",R,[d(y,{class:"table",scroll:{y:"330px"},pagination:!1,loading:I.loading,columns:K,"data-source":I.tableData,"row-selection":{selectedRowKeys:I.selectedRowKeys,hideSelectAll:!0,onSelect:C},"row-key":e=>e.id,onChange:_},{bodyCell:p((({column:e,record:s})=>{var t,a;return["deptName"===e.key?(j(),l("span",{key:0,title:(null==(t=s.sysUserDetailVo)?void 0:t.deptName)||""},u((null==(a=s.sysUserDetailVo)?void 0:a.deptName)||""),9,S)):m("",!0)]})),_:1},8,["loading","data-source","row-selection","row-key"])],512)])])])}}}),[["__scopeId","data-v-913f5ca4"]]);export{D as default};
