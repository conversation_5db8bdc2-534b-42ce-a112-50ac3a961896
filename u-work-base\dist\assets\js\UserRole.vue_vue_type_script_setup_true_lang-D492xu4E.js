import{s as e,a,e as s,b as o}from"./main-Djn9RDyT.js";import{i as l,j as t}from"./userManage-D6iEBY45.js";import{f as r,M as n}from"./ant-design-vue-DYY9BtJq.js";import{d,r as i,a9 as u,o as c,aa as v,e as m,ab as p}from"./@vue-HScy-mz9.js";const g="/edtap/sys-role/page";const h={style:{"margin-bottom":"24px"}},y=d({__name:"UserRole",emits:["ok"],setup(d,{expose:y,emit:k}){const f=a(),I=k,w=[{title:"角色名称",dataIndex:"name"},{title:"唯一编码",dataIndex:"code"}],x=i("checkbox");s({code:"JUE_SE_FEN_PEI_MO_SHI"}).then((e=>{var a,s;const o=null==(s=null==(a=e.data)?void 0:a.rows[0])?void 0:s.value;x.value="ROLE_RADIO"===o?"radio":"checkbox"})).catch((()=>{x.value="checkbox"}));const _=i(!1),b=i(!1),E=i(!1),R=i({}),j=i([]),C=i([]),O=()=>{E.value=!0,l({id:R.value.id,enterpriseId:f.checkedEnterprise.id}).then((e=>{C.value=e.data,E.value=!1}))},M=()=>{b.value=!0,t({id:R.value.id,grantRoleIdList:C.value,source:"development",enterpriseId:f.checkedEnterprise.id}).then((e=>{b.value=!1,e.success?(o("success","用户授权成功"),I("ok"),K()):o("error",`授权失败：${e.message}`)})).finally((()=>{b.value=!1}))},H=e=>{C.value=e},K=()=>{b.value||(R.value={},C.value=[],_.value=!1,b.value=!1)};return y({userRole:a=>{var s;R.value=a,_.value=!0,O(),(s={sysCategoryId:f.sysCategoryId,enterpriseId:f.checkedEnterprise.id},e({url:g,method:"get",params:s})).then((e=>{j.value=e.data.rows}))}}),(e,a)=>{const s=r,o=n;return c(),u(o,{"wrap-class-name":"cus-modal",title:"授权角色","body-style":{maxHeight:"600px",overflow:"auto"},width:800,open:_.value,"confirm-loading":b.value,"mask-closable":!1,onOk:M,onCancel:K},{default:v((()=>[m("div",h,[e.hasPerm("sys-role:page")?(c(),u(s,{key:0,size:"middle","row-selection":{selectedRowKeys:C.value,onChange:H,type:x.value},columns:w,"data-source":j.value,pagination:!1,loading:E.value,"row-key":e=>e.id},null,8,["row-selection","data-source","loading","row-key"])):p("",!0)])])),_:1},8,["open","confirm-loading"])}}});export{y as _};
