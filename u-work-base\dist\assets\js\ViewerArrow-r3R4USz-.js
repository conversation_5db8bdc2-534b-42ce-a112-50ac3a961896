import{d as e,b as r,o as t,a4 as s,e as a,y as o,G as l,c as i,u as n}from"./@vue-HScy-mz9.js";import{j as u,R as d}from"./@ant-design-CA72ad83.js";import{_ as c}from"./vue-qr-CB2aNKv5.js";const p=c(e({__name:"ViewerArrow",props:{current:{type:Number,default:0},total:{type:Number,default:0}},emits:["left","right"],setup(e,{expose:c,emit:p}){const m=e,f=p,v=()=>f("left"),w=()=>f("right");return c({goLeft:v,goRight:w}),(e,c)=>(t(),r("div",{class:"viewer-arrow",style:s({display:m.total?"flex":"none"})},[a("div",{class:"viewer-arrow-left",onClick:v},[o(i(n(u),null,null,512),[[l,m.current>0]])]),a("div",{class:"viewer-arrow-right",onClick:w},[o(i(n(d),null,null,512),[[l,m.current<m.total-1]])])],4))}}),[["__scopeId","data-v-dd05b13d"]]);export{p as V};
