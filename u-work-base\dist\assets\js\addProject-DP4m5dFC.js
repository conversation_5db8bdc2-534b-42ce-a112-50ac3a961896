import{d as e,r as a,f as l,am as t,a9 as s,o as u,aa as o,c as r,y as d,e as i,ab as n,J as c,a4 as v,u as p,ad as m,G as f,b as g,F as h,ag as b,n as j}from"./@vue-HScy-mz9.js";import{a as _}from"./axios-7z2hFSF6.js";import{M as y,e as k,N as w,g as C,b as D}from"./main-Djn9RDyT.js";import{v as x,i as M,a as S,b as U,g as I,l as N}from"./projectGallery-xT8wgNPG.js";import{S as Y,F as $,b as F,c as q,w as L,x as V,I as P,B as z,y as O,z as A,T as E,O as R,_ as T,i as B,P as G,Q as H,d as J,M as K}from"./ant-design-vue-DYY9BtJq.js";import{_ as Q}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const W={class:"step-item"},X={class:"i-box"},Z={style:{display:"flex","align-items":"center"}},ee={class:"i-box"},ae={class:"step-item"},le={style:{display:"flex","justify-content":"space-between"}},te={class:"step-item"},se={class:"upload-wrap"},ue=["accept"],oe={class:"file-list"},re=["onClick"],de={class:"form-item-notice keep-px"},ie={class:"upload-wrap"},ne=["accept"],ce={class:"file-list"},ve=["onClick"],pe={class:"form-item-notice keep-px"},me=Q(e({__name:"addProject",emits:["ok"],setup(e,{expose:Q,emit:me}){const fe=me,ge=a(!1),he=()=>{He.value=!1,Se.value.resetFields()},be=a(1),je=a(!1),_e=a(1),ye=a([]),ke=async()=>{const e=Ne.value.contractCode;if(e)try{const a=await w({tenantCode:e}),{tenant:l,token:t}=a.data;_.get(`${window.config.appApi}/systemx/project-pack/page`,{params:{pageNo:1,pageSize:99},headers:{Tenant:e,Authorization:`Bearer ${t}`,"X-Security-FreshToken":C()}}).then((e=>{var a,l;const t=(null==(l=null==(a=e.data)?void 0:a.data)?void 0:l.rows)||[];t.length&&(ye.value=t.map((e=>({label:e.name,value:we(e.url)}))))})).catch((()=>{ye.value=[]}))}catch(a){ye.value=[]}},we=e=>`${window.location.origin}/osr/resource/${e}index.html`;l((()=>{k({code:"MING_DAO_YUN_TONG_BU"}).then((e=>{var a,l;"是"===(null==(l=null==(a=e.data)?void 0:a.rows[0])?void 0:l.value)?je.value=!0:(je.value=!1,be.value=0)}))}));const Ce=((e,a)=>{let l;return function(...t){l||(e.apply(this,t),l=setTimeout((()=>{l=null}),a))}})((()=>{I(Ne.value.contractCode).then((e=>{200===e.code?(Object.keys(e.data).forEach((a=>{if(e.data[a]&&(Ne.value[a]=e.data[a]),("productName"===a||"industry"===a)&&e.data[a]&&qe.value.size){const l=qe.value.get(e.data[a]);l&&Ne.value.tagIds.push(l.split("-"))}})),e.data.contractCode?(He.value=!0,Se.value.clearValidate()):D("error","合同编号不存在")):D("error",e.message)}))}),1e3),De=()=>{_e.value--},xe=()=>{1===_e.value&&Se.value.validate().then((()=>{_e.value++})).catch((()=>{})),2===_e.value&&Ue.value.validate().then((()=>{_e.value++})).catch((()=>{}))},Me=a(!1),Se=a(),Ue=a(),Ie=a(),Ne=a({projectName:"",contractCode:"",productName:"",productVersion:"",techPlatform:"",saleDepartment:"",projectManager:"",salesManager:"",saleSupporter:"",industry:"",ultimateCustomer:"",contractedCustomer:"",projectStartDate:"",projectEndDate:"",projectStatus:"",screenResolution:"",projectAccessLinker:"",projectSimpleDescription:"",overallDemand:"",projectScope:"",functionalDecompositio:"",projectPracticalSummary:"",deliverablesImages:[],deliverablesVideo:[],tagIds:[]}),Ye={contractCode:[{required:!0,message:"请输入合同编号"}],projectName:[{required:!0,message:"请输入项目名称"}],techPlatform:[{required:!0,message:"请输入采纳的技术平台"}],saleDepartment:[{required:!0,message:"请输入所属部门"}],projectManager:[{required:!0,message:"请输入项目经理"}],salesManager:[{required:!0,message:"请输入销售经理"}],projectStatus:[{required:!0,message:"请输入项目状态"}],projectAccessLinker:[{required:!1,validator:y}],deliverablesImages:[{required:!0,message:"请上传图片",trigger:"change"}],tagIds:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let t=0;return a.forEach((e=>{if(1===e.length)if(Le.value.includes(e[0])){const a=$e.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+e.length-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}]},$e=a([]),Fe=a(new Map),qe=a(new Map),Le=a([]),Ve=a(new Map),Pe=(e,a)=>a.some((a=>a.label.toLowerCase().indexOf(e.toLowerCase())>-1)),ze=a(["mp4","MP4"]),Oe=async e=>{const a=e.target;if(Ne.value.deliverablesVideo.length>=10)return D("warning","最多支持上传10个文件"),a.value="",!1;const l=a.files[0],{size:t,name:s}=l,u=s.lastIndexOf("."),o=s.slice(u+1).toLocaleLowerCase();if(!ze.value.includes(o))return D("warning",`文件后缀必须是${ze.value.join("/")}`),a.value="",!1;if(t>1073741824)return D("warning","文件大小不能超过1024M"),a.value="",!1;a.value="",Me.value=!0;const r=new FormData;r.append("file",l),r.append("bucket","edtp-source"),r.append("replaceName","false");const d=await x(r);200===d.code?(Me.value=!1,Ne.value.deliverablesVideo.push(d.data),D("success","视频上传成功")):(Me.value=!1,D("error",d.message))},Ae=a(!1),Ee=a(),Re=(e,a)=>{Ae.value=e,"string"==typeof a&&(Ee.value=Te(a))},Te=e=>e?`${e}`:"",Be=a(["png","jpg","jpeg","gif"]),Ge=async e=>{const a=e.target;if(Ne.value.deliverablesImages.length>=10)return D("warning","最多支持上传10个文件"),a.value="",!1;const l=a.files[0],{size:t,name:s}=l,u=s.lastIndexOf("."),o=s.slice(u+1).toLocaleLowerCase();if(!Be.value.includes(o))return D("warning",`文件后缀必须是${Be.value.join("/")}`),a.value="",!1;if(t>5242880)return D("warning","文件大小不能超过5M"),a.value="",!1;a.value="",Me.value=!0;const r=new FormData;r.append("file",l),r.append("bucket","edtp-source"),r.append("replaceName","false");const d=await M(r);200===d.code?(Ne.value.deliverablesImages.push(d.data),Ie.value.validateFields(["deliverablesImages"]),D("success","图片上传成功"),Me.value=!1):(D("error",d.message),Me.value=!1)},He=a(!1),Je=a(["",""]),Ke=()=>{Me.value||(Se.value.resetFields(),Ue.value.resetFields(),Ie.value.resetFields(),ye.value=[],ge.value=!1,He.value=!1)},Qe=()=>{let e=[];return Ne.value.tagIds.forEach((a=>{1===a.length?e=e.concat(Ve.value.get(a[0])):e.push(a[1])})),e},We=()=>{Me.value||Ie.value.validate().then((async()=>{var e;const a={...Ne.value,projectPracticalSummary:JSON.stringify(Je.value),tagIds:Qe()},l=await S(a);if(200!==l.code)return D("error",l.message),void(Me.value=!1);const t=await U({id:null==(e=l.data)?void 0:e.id,approve:2,approveRemark:""});if(200!==t.code)return D("error",t.message),void(Me.value=!1);Me.value=!1,D("success","新增项目案例成功"),Ke(),fe("ok")})).catch((e=>{Me.value=!1}))};return Q({init:()=>{ge.value=!0,_e.value=1,N().then((e=>{const a=e.data.map((e=>{var a;return Le.value.push(e.id),Ve.value.set(e.id,[]),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(Ve.value.get(e.id).push(a.id),Fe.value.set(`${a.tagName}`,a.color),qe.value.set(`${a.tagName}`,`${e.id}-${a.id}`),{value:a.id,label:`${a.tagName}`})))}}));$e.value=a.filter((e=>e.children))})),j((()=>{Se.value.resetFields(),Ue.value.resetFields(),Ie.value.resetFields(),Ne.value.tagIds=[],ye.value=[],Ne.value.deliverablesImages=[],Ne.value.deliverablesVideo=[],Je.value=["",""]}))}}),(e,a)=>{const l=V,j=L,_=q,y=P,k=z,w=F,C=O,D=E,x=A,M=R,S=$,U=B,I=G,N=T,Q=H,me=t("DeleteOutlined"),fe=t("question-circle-outlined"),we=J,qe=Y,Le=K;return u(),s(Le,{width:1200,title:"新增项目案例","body-style":{maxHeight:"700px",overflow:"auto"},"wrap-class-name":"cus-modal",open:ge.value,"confirm-loading":Me.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[25]||(a[25]=e=>We()),onCancel:Ke},{footer:o((()=>[i("div",null,[d(r(k,{type:"primary",onClick:De},{default:o((()=>a[31]||(a[31]=[c(" 上一步 ")]))),_:1},512),[[f,_e.value>1]]),d(r(k,{type:"primary",onClick:xe},{default:o((()=>a[32]||(a[32]=[c(" 下一步 ")]))),_:1},512),[[f,_e.value<3]]),r(k,{onClick:Ke},{default:o((()=>a[33]||(a[33]=[c(" 取消 ")]))),_:1}),d(r(k,{type:"primary",onClick:We},{default:o((()=>a[34]||(a[34]=[c(" 确认 ")]))),_:1},512),[[f,3===_e.value]])])])),default:o((()=>[r(qe,{spinning:Me.value,style:{position:"fixed",top:"250px"}},{default:o((()=>[r(S,{ref_key:"form1Ref",ref:Se,model:Ne.value,rules:Ye,"label-align":"left"},{default:o((()=>[d(i("div",W,[i("div",X,[r(w,{md:24,sm:24},{default:o((()=>[r(_,{class:"project-code-item keep-px",name:"contractCode",label:"合同编号","has-feedback":""},{default:o((()=>[i("div",Z,[r(_,{style:{"margin-bottom":"-24px"}},{default:o((()=>[je.value?(u(),s(j,{key:0,placeholder:"请选择",onChange:he,value:be.value,"onUpdate:value":a[0]||(a[0]=e=>be.value=e),style:{width:"100px",padding:"0"}},{default:o((()=>[r(l,{value:1},{default:o((()=>a[26]||(a[26]=[c("同步")]))),_:1}),r(l,{value:0},{default:o((()=>a[27]||(a[27]=[c("自定义")]))),_:1})])),_:1},8,["value"])):n("",!0)])),_:1}),r(y,{value:Ne.value.contractCode,"onUpdate:value":a[1]||(a[1]=e=>Ne.value.contractCode=e),maxlength:20,disabled:He.value,onBlur:ke,style:v({width:je.value&&1===be.value?"calc(100% - 180px)":je.value&&0===be.value?"calc(100% - 80px)":"100%"}),placeholder:"请输入合同编号"},null,8,["value","disabled","style"]),je.value&&1===be.value?(u(),s(k,{key:0,disabled:!!Ne.value.contractCode&&He.value,style:{"min-width":"80px",padding:"0"},type:"primary",onClick:p(Ce)},{default:o((()=>a[28]||(a[28]=[c("同步")]))),_:1},8,["disabled","onClick"])):n("",!0)])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"productName",label:"产品信息(合同)","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.productName,"onUpdate:value":a[2]||(a[2]=e=>Ne.value.productName=e),maxlength:30,placeholder:"请输入产品信息(合同)"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"techPlatform",label:"采纳的技术平台","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.techPlatform,"onUpdate:value":a[3]||(a[3]=e=>Ne.value.techPlatform=e),maxlength:30,placeholder:"请输入采纳的技术平台"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"projectManager",label:"项目经理","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.projectManager,"onUpdate:value":a[4]||(a[4]=e=>Ne.value.projectManager=e),disabled:1===be.value,placeholder:"请输入项目经理"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"saleSupporter",label:"支持售前","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.saleSupporter,"onUpdate:value":a[5]||(a[5]=e=>Ne.value.saleSupporter=e),maxlength:30,placeholder:"请输入支持售前"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"ultimateCustomer",label:"最终客户名称","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.ultimateCustomer,"onUpdate:value":a[6]||(a[6]=e=>Ne.value.ultimateCustomer=e),maxlength:30,placeholder:"请输入最终客户名称"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"projectStartDate",label:"项目启动时间","has-feedback":""},{default:o((()=>[r(C,{value:Ne.value.projectStartDate,"onUpdate:value":a[7]||(a[7]=e=>Ne.value.projectStartDate=e),disabled:1===be.value,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",placeholder:"请选择项目启动时间"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"projectStatus",label:"项目状态","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.projectStatus,"onUpdate:value":a[8]||(a[8]=e=>Ne.value.projectStatus=e),disabled:1===be.value,placeholder:"请输入项目状态"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"tagIds",label:"资源标签","has-feedback":""},{default:o((()=>[r(x,{value:Ne.value.tagIds,"onUpdate:value":a[9]||(a[9]=e=>Ne.value.tagIds=e),"show-checked-strategy":p(A).SHOW_CHILD,"show-search":{filter:Pe},style:{width:"100%"},multiple:"","max-tag-count":"responsive",options:$e.value,placeholder:"请选择标签"},{tagRender:o((e=>{return[(u(),s(D,{key:e.value,color:(a=e.label,Fe.value.get(a)||"blue")},{default:o((()=>[c(m(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","show-checked-strategy","show-search","options"])])),_:1})])),_:1})]),i("div",ee,[r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"projectName",label:"项目名称","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.projectName,"onUpdate:value":a[10]||(a[10]=e=>Ne.value.projectName=e),disabled:1===be.value,title:Ne.value.projectName,placeholder:"请输入项目名称"},null,8,["value","disabled","title"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"productVersion",label:"产品版本","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.productVersion,"onUpdate:value":a[11]||(a[11]=e=>Ne.value.productVersion=e),maxlength:30,placeholder:"请输入产品版本"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"saleDepartment",label:"所属部门(销售)","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.saleDepartment,"onUpdate:value":a[12]||(a[12]=e=>Ne.value.saleDepartment=e),disabled:1===be.value,placeholder:"请输入所属部门(销售)"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"salesManager",label:"销售经理","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.salesManager,"onUpdate:value":a[13]||(a[13]=e=>Ne.value.salesManager=e),disabled:1===be.value,placeholder:"请输入销售经理"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"industry",label:"行业","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.industry,"onUpdate:value":a[14]||(a[14]=e=>Ne.value.industry=e),disabled:1===be.value,placeholder:"请输入行业"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"contractedCustomer",label:"签约客户名称","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.contractedCustomer,"onUpdate:value":a[15]||(a[15]=e=>Ne.value.contractedCustomer=e),disabled:1===be.value,placeholder:"请输入签约客户名称"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"projectEndDate",label:"项目完成时间","has-feedback":""},{default:o((()=>[r(C,{value:Ne.value.projectEndDate,"onUpdate:value":a[16]||(a[16]=e=>Ne.value.projectEndDate=e),disabled:1===be.value,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",placeholder:"请选择项目完成时间"},null,8,["value","disabled"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"screenResolution",label:"屏幕分辨率","has-feedback":""},{default:o((()=>[r(y,{value:Ne.value.screenResolution,"onUpdate:value":a[17]||(a[17]=e=>Ne.value.screenResolution=e),maxlength:30,placeholder:"请输入屏幕分辨率"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"projectAccessLinker",label:"项目地址","has-feedback":""},{default:o((()=>[r(M,{value:Ne.value.projectAccessLinker,"onUpdate:value":a[18]||(a[18]=e=>Ne.value.projectAccessLinker=e),options:ye.value,placeholder:"请输入项目地址"},null,8,["value","options"])])),_:1})])),_:1})])],512),[[f,1===_e.value]])])),_:1},8,["model"]),r(S,{ref_key:"form2Ref",ref:Ue,model:Ne.value,rules:Ye,"label-align":"left"},{default:o((()=>[d(i("div",ae,[r(N,{gutter:24},{default:o((()=>[r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"projectSimpleDescription",label:"一句话简介"},{default:o((()=>[r(U,{value:Ne.value.projectSimpleDescription,"onUpdate:value":a[19]||(a[19]=e=>Ne.value.projectSimpleDescription=e),rows:4,placeholder:"(在什么样的背景下)利用了什么产品，整合了什么资源，形成了什么类型的平台，为用户带来了什么样的价值。"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"proSituation",label:"项目整体需求情况"},{default:o((()=>[r(U,{value:Ne.value.overallDemand,"onUpdate:value":a[20]||(a[20]=e=>Ne.value.overallDemand=e),rows:4},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"projectScope",label:"项目范围"},{default:o((()=>[r(U,{value:Ne.value.projectScope,"onUpdate:value":a[21]||(a[21]=e=>Ne.value.projectScope=e),rows:4,placeholder:"请输入项目的交付范围（精炼版sow）"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"functionalDecompositio",label:"功能介绍分析"},{default:o((()=>[r(U,{value:Ne.value.functionalDecompositio,"onUpdate:value":a[22]||(a[22]=e=>Ne.value.functionalDecompositio=e),rows:4,placeholder:"业务场景分解描述，成果需确认无敏感内容"},null,8,["value"])])),_:1})])),_:1}),r(w,{md:24,sm:24},{default:o((()=>[r(_,{name:"successM",label:"项目实战经验总结"},{default:o((()=>[r(I,null,{default:o((()=>[i("div",le,[r(U,{value:Je.value[0],"onUpdate:value":a[23]||(a[23]=e=>Je.value[0]=e),style:{width:"49.5%","margin-right":"1%"},rows:4,placeholder:"项目最成功的1-5个因素"},null,8,["value"]),r(U,{value:Je.value[1],"onUpdate:value":a[24]||(a[24]=e=>Je.value[1]=e),style:{width:"49.5%"},rows:4,placeholder:"项目最痛苦的1-5个因素"},null,8,["value"])])])),_:1})])),_:1})])),_:1})])),_:1})],512),[[f,2===_e.value]])])),_:1},8,["model"]),r(S,{ref_key:"form3Ref",ref:Ie,model:Ne.value,rules:Ye,"label-align":"left"},{default:o((()=>[d(i("div",te,[r(N,{gutter:24},{default:o((()=>[r(w,{md:24,sm:24,class:"form-item"},{default:o((()=>[r(_,{name:"deliverablesImages",label:"上传图片","has-feedback":""},{default:o((()=>[i("div",se,[r(k,{class:"upload-btn",type:"primary"},{default:o((()=>[a[29]||(a[29]=c(" 点击上传 ")),i("input",{type:"file",class:"input-file",accept:Be.value.map((e=>`.${e}`)).join(","),onChange:Ge},null,40,ue)])),_:1})]),i("div",oe,[(u(!0),g(h,null,b(Ne.value.deliverablesImages,((e,a)=>(u(),g("div",{key:e,class:"file-item"},[i("div",{class:"file-name",onClick:a=>Re(!0,e)},[r(Q,{width:48,height:48,src:Te(e)},null,8,["src"]),c(" "+m(e),1)],8,re),r(me,{class:"file-icon",onClick:e=>(e=>{Ne.value.deliverablesImages.splice(e,1)})(a)},null,8,["onClick"])])))),128)),r(Q,{width:200,style:{display:"none"},preview:{visible:Ae.value,onVisibleChange:Re},src:Ee.value},null,8,["preview","src"])])])),_:1}),i("span",de,[r(we,{title:"支持文件格式: png、jpeg、jpg、gif, 大小限制: 5M",placement:"right"},{default:o((()=>[r(fe,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),r(w,{md:24,sm:24,class:"form-item"},{default:o((()=>[r(_,{name:"deliverablesVideo",label:"上传视频","has-feedback":""},{default:o((()=>[i("div",ie,[r(k,{class:"upload-btn",type:"primary"},{default:o((()=>[a[30]||(a[30]=c(" 点击上传 ")),i("input",{type:"file",class:"input-file",accept:ze.value.map((e=>`.${e}`)).join(","),onChange:Oe},null,40,ne)])),_:1})]),i("div",ce,[(u(!0),g(h,null,b(Ne.value.deliverablesVideo,((e,a)=>(u(),g("div",{key:e,class:"file-item"},[i("div",{class:"file-name",onClick:a=>(e=>{window.open(Te(e),"_blank")})(e)},m(e),9,ve),r(me,{class:"file-icon",onClick:e=>(e=>{Ne.value.deliverablesVideo.splice(e,1)})(a)},null,8,["onClick"])])))),128))])])),_:1}),i("span",pe,[r(we,{title:"支持文件格式: mp4, 大小限制:1024M",placement:"right"},{default:o((()=>[r(fe,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1})],512),[[f,3===_e.value]])])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-33095783"]]);export{me as default};
