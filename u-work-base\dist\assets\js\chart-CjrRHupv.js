import{s as t}from"./main-Djn9RDyT.js";function a(){return t({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"9"}})}const s=a=>t({url:"/edtap/sys-chart/page",method:"post",data:a}),d=(a,s)=>t({url:"/edtap/sys-chart/add",method:"post",data:a,onUploadProgress(t){s&&s(t)}});function o(a){return t({url:"/edtap/sys-chart/batch-delete",method:"post",data:a})}const e=(a,s)=>t({url:"/edtap/sys-chart/edit",method:"post",data:a,onUploadProgress(t){s&&s(t)}}),r=a=>t({url:"/edtap/sys-chart/download-chart",method:"post",data:a}),p=a=>t({url:"/edtap/sys-chart/myChartPage",method:"post",data:a});function h(a){return t({url:"/edtap/sys-chart/change-status",method:"post",data:a})}export{d as a,h as b,a as c,r as d,e,o as f,s as g,p as h};
