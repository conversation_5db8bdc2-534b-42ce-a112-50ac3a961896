import{d as e,r as s,p as a,a as t,a9 as o,o as i,aa as l,c as r,e as n,ae as p,ab as m,J as c,b as d,F as u,ad as j,n as v}from"./@vue-HScy-mz9.js";import{a as y,a0 as h,b as g}from"./main-Djn9RDyT.js";import{k as f,l as k}from"./role-OrQ5c9FV.js";import{S as b,I as w,e as x,B as I,f as _,M as z}from"./ant-design-vue-DYY9BtJq.js";import{_ as C}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./axios-7z2hFSF6.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const E={class:"search-wrap"},S={class:"search-content"},L={class:"search-item"},J={class:"table-handle"},N={class:"btns-wrap"},R={key:1,class:"table-actions"},T={key:0},D=C(e({__name:"checkRoleForm",setup(e,{expose:C}){const D=y(),K=s(!1),M=s(!1),B=a((()=>1===D.adminType)),F=s(!1),G=e=>U.enterpriseId?h(e):!!B.value,q=t([]);let A=s("");const H=()=>{f({pageNo:1,pageSize:999,roleId:A.value,searchValue:U.name}).then((e=>{200===e.code&&v((()=>{e.data.rows?q.splice(0,q.length,...e.data.rows):q.splice(0,q.length)})),F.value=!0,K.value=!1,M.value=!1}))},U=t({name:"",code:"",pageNo:1,pageSize:10,sysCategoryId:"",enterpriseId:""}),V=s([]),Z=e=>{V.value=e},$=e=>{let s=[];s=e?[e.id]:V.value,k({roleId:A.value,userIds:s}).then((e=>{200===e.code?g("success","用户移除成功"):g("error",e.error),H(),V.value=[]}))},O=t([{title:"账号",dataIndex:"account",key:"account",ellipsis:!0},{title:"姓名",dataIndex:"name",key:"name",ellipsis:!0},{title:"状态",dataIndex:"status",key:"status",width:80},{title:"操作",key:"action",width:80}]),P=e=>{const{ueStatus:s,status:a}=e;let t="";return 0===a?t=1===s?"冻结":"正常":1===a?t="停用":2===a?t="删除":3===a?t="待审批":4===a&&(t="未通过"),1!==s&&3!==s||(t="正常"),t},Q=s(),W=()=>{U.pageNo=1,U.pageSize=10,U.sysCategoryId="",U.enterpriseId="",U.name="",V.value=[],F.value=!1};return C({userListInit:e=>{A.value=e.id,H()}}),(e,s)=>{const a=w,t=I,v=x,y=_,h=b,g=z;return i(),o(g,{"mask-closable":!1,title:"用户列表","body-style":{maxHeight:"600px",height:"500px"},"wrap-class-name":"cus-modal",width:800,open:F.value,"confirm-loading":K.value,footer:null,onCancel:W},{default:l((()=>[r(h,{spinning:K.value},{default:l((()=>[n("div",E,[n("div",S,[n("div",L,[s[2]||(s[2]=n("span",{class:"search-label"},"用户名称",-1)),r(a,{value:U.name,"onUpdate:value":s[0]||(s[0]=e=>U.name=e),valueModifiers:{trim:!0},"allow-clear":"",placeholder:"请输入姓名或账号",class:"search-input",onKeyup:p(H,["enter"])},null,8,["value"])])]),n("div",J,[n("div",N,[V.value.length?(i(),o(v,{key:0,placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:s[1]||(s[1]=e=>$())},{title:l((()=>s[3]||(s[3]=[n("p",null,"确定要批量移除所有勾选人员吗?",-1)]))),default:l((()=>[G("JSGL:DELETE")?(i(),o(t,{key:0,class:"search-btn",type:"primary"},{default:l((()=>s[4]||(s[4]=[c("批量移除")]))),_:1})):m("",!0)])),_:1})):(i(),o(t,{key:1,disabled:"",class:"search-btn",type:"primary"},{default:l((()=>s[5]||(s[5]=[c("批量移除")]))),_:1}))])])]),n("div",{ref_key:"table",ref:Q,class:"table-content"},[r(y,{class:"table",pagination:!1,"row-key":e=>e.id,size:"small",columns:O,loading:M.value,"row-selection":{selectedRowKeys:V.value,onChange:Z},"data-source":q,scroll:{y:"300px"}},{bodyCell:l((({column:e,record:a})=>["status"===e.key?(i(),d(u,{key:0},[c(j(P(a)),1)],64)):m("",!0),"action"===e.key?(i(),d("div",R,[r(v,{placement:"topRight","ok-text":"是","cancel-text":"否",onConfirm:e=>$(a)},{title:l((()=>s[6]||(s[6]=[n("p",null,"确定要移除吗?",-1)]))),default:l((()=>[G("JSGL:DELETE")?(i(),d("a",T,"移除")):m("",!0)])),_:2},1032,["onConfirm"])])):m("",!0)])),_:1},8,["row-key","columns","loading","row-selection","data-source"])],512)])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-6ccee698"]]);export{D as default};
