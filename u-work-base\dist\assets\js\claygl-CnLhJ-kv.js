function t(t,e,n){"object"==typeof e&&(n=e,e=null);var i,a=this;if(!(t instanceof Function))for(var o in i=[],t)t.hasOwnProperty(o)&&i.push(o);var s=function(e){if(a.apply(this,arguments),t instanceof Function?r(this,t.call(this,e)):function(t,r,e){for(var n=0;n<e.length;n++){var i=e[n];t[i]=r[i]}}(this,t,i),this.constructor===s)for(var n=s.__initializers__,o=0;o<n.length;o++)n[o].apply(this,arguments)};s.__super__=a,a.__initializers__?s.__initializers__=a.__initializers__.slice():s.__initializers__=[],e&&s.__initializers__.push(e);var u=function(){};return u.prototype=a.prototype,s.prototype=new u,s.prototype.constructor=s,r(s.prototype,n),s.extend=a.extend,s.derive=a.extend,s}function r(t,r){if(r)for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])}const e={extend:t,derive:t};function n(t,r){this.action=t,this.context=r}var i={trigger:function(t){if(this.hasOwnProperty("__handlers__")&&this.__handlers__.hasOwnProperty(t)){var r=this.__handlers__[t],e=r.length,n=-1,i=arguments;switch(i.length){case 1:for(;++n<e;)r[n].action.call(r[n].context);return;case 2:for(;++n<e;)r[n].action.call(r[n].context,i[1]);return;case 3:for(;++n<e;)r[n].action.call(r[n].context,i[1],i[2]);return;case 4:for(;++n<e;)r[n].action.call(r[n].context,i[1],i[2],i[3]);return;case 5:for(;++n<e;)r[n].action.call(r[n].context,i[1],i[2],i[3],i[4]);return;default:for(;++n<e;)r[n].action.apply(r[n].context,Array.prototype.slice.call(i,1));return}}},on:function(t,r,e){if(t&&r){var i=this.__handlers__||(this.__handlers__={});if(i[t]){if(this.has(t,r))return}else i[t]=[];var a=new n(r,e||this);return i[t].push(a),this}},once:function(t,r,e){if(t&&r){var n=this;return this.on(t,(function e(){n.off(t,e),r.apply(this,arguments)}),e)}},before:function(t,r,e){if(t&&r)return t="before"+t,this.on(t,r,e)},after:function(t,r,e){if(t&&r)return t="after"+t,this.on(t,r,e)},success:function(t,r){return this.once("success",t,r)},error:function(t,r){return this.once("error",t,r)},off:function(t,r){var e=this.__handlers__||(this.__handlers__={});if(r){if(e[t]){for(var n=e[t],i=[],a=0;a<n.length;a++)r&&n[a].action!==r&&i.push(n[a]);e[t]=i}return this}e[t]=[]},has:function(t,r){var e=this.__handlers__;if(!e||!e[t])return!1;for(var n=e[t],i=0;i<n.length;i++)if(n[i].action===r)return!0}},a=0,o=Array.prototype.forEach,s={genGUID:function(){return++a},relative2absolute:function(t,r){if(!r||t.match(/^\//))return t;for(var e=t.split("/"),n=r.split("/"),i=e[0];"."===i||".."===i;)".."===i&&n.pop(),e.shift(),i=e[0];return n.join("/")+"/"+e.join("/")},extend:function(t,r){if(r)for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e]);return t},defaults:function(t,r){if(r)for(var e in r)void 0===t[e]&&(t[e]=r[e]);return t},extendWithPropList:function(t,r,e){if(r)for(var n=0;n<e.length;n++){var i=e[n];t[i]=r[i]}return t},defaultsWithPropList:function(t,r,e){if(r)for(var n=0;n<e.length;n++){var i=e[n];null==t[i]&&(t[i]=r[i])}return t},each:function(t,r,e){if(t&&r)if(t.forEach&&t.forEach===o)t.forEach(r,e);else if(t.length===+t.length)for(var n=0,i=t.length;n<i;n++)r.call(e,t[n],n,t);else for(var a in t)t.hasOwnProperty(a)&&r.call(e,t[a],a,t)},isObject:function(t){return t===Object(t)},isArray:function(t){return Array.isArray(t)},isArrayLike:function(t){return!!t&&t.length===+t.length},clone:function(t){if(s.isObject(t)){if(s.isArray(t))return t.slice();if(s.isArrayLike(t)){for(var r=new t.constructor(t.length),e=0;e<t.length;e++)r[e]=t[e];return r}return s.extend({},t)}return t}},u=function(){this.__uid__=s.genGUID()};u.__initializers__=[function(t){s.extend(this,t)}],s.extend(u,e),s.extend(u.prototype,i);var h=["OES_texture_float","OES_texture_half_float","OES_texture_float_linear","OES_texture_half_float_linear","OES_standard_derivatives","OES_vertex_array_object","OES_element_index_uint","WEBGL_compressed_texture_s3tc","WEBGL_depth_texture","EXT_texture_filter_anisotropic","EXT_shader_texture_lod","WEBGL_draw_buffers","EXT_frag_depth","EXT_sRGB","ANGLE_instanced_arrays"],c=["MAX_TEXTURE_SIZE","MAX_CUBE_MAP_TEXTURE_SIZE"];function l(t){for(var r={},e={},n=0;n<h.length;n++){a(h[n])}for(n=0;n<c.length;n++){var i=c[n];e[i]=t.getParameter(t[i])}function a(e){if(t.getExtension){var n=t.getExtension(e);n||(n=t.getExtension("MOZ_"+e)),n||(n=t.getExtension("WEBKIT_"+e)),r[e]=n}}this.getExtension=function(t){return t in r||a(t),r[t]},this.getParameter=function(t){return e[t]}}const f=256,d=1024,m=16384,p=0,y=1,_=2,v=3,g=4,x=5,T=6,b=35040,E=35044,w=35048,A=1028,S=1029,M=1032,C=2304,R=2305,D=5120,I=5121,L=5122,O=5123,P=5124,N=5125,B=5126,U=6402,F=6406,k=6407,H=6408,V=6409,W=6410,G=9728,z=9729,X=9984,j=9985,q=9986,Y=9987,K=3553,Z=34067,J=10497,Q=33071,$=33648,tt=36160,rt=36161,et=34041,nt=36064,it=36096,at=36128,ot=33306;const st={get:function(t){var r=new XMLHttpRequest;r.open("get",t.url),r.responseType=t.responseType||"text",t.onprogress&&(r.onprogress=function(r){if(r.lengthComputable){var e=r.loaded/r.total;t.onprogress(e,r.loaded,r.total)}else t.onprogress(null)}),r.onload=function(e){r.status>=400?t.onerror&&t.onerror():t.onload&&t.onload(r.response)},t.onerror&&(r.onerror=t.onerror),r.send(null)}};var ut,ht={supportWebGL:function(){if(null==ut)try{var t=document.createElement("canvas");if(!(t.getContext("webgl")||t.getContext("experimental-webgl")))throw new Error}catch(r){ut=!1}return ut}};ht.Int8Array="undefined"==typeof Int8Array?Array:Int8Array,ht.Uint8Array="undefined"==typeof Uint8Array?Array:Uint8Array,ht.Uint16Array="undefined"==typeof Uint16Array?Array:Uint16Array,ht.Uint32Array="undefined"==typeof Uint32Array?Array:Uint32Array,ht.Int16Array="undefined"==typeof Int16Array?Array:Int16Array,ht.Float32Array="undefined"==typeof Float32Array?Array:Float32Array,ht.Float64Array="undefined"==typeof Float64Array?Array:Float64Array;var ct={};"undefined"!=typeof window?ct=window:"undefined"!=typeof global&&(ct=global),ht.requestAnimationFrame=ct.requestAnimationFrame||ct.msRequestAnimationFrame||ct.mozRequestAnimationFrame||ct.webkitRequestAnimationFrame||function(t){setTimeout(t,16)},ht.createCanvas=function(){return document.createElement("canvas")},ht.createImage=function(){return new ct.Image},ht.request={get:st.get},ht.addEventListener=function(t,r,e,n){t.addEventListener(r,e,n)},ht.removeEventListener=function(t,r,e){t.removeEventListener(r,e)};var lt=function(){this.head=null,this.tail=null,this._length=0};lt.prototype.insert=function(t){var r=new lt.Entry(t);return this.insertEntry(r),r},lt.prototype.insertAt=function(t,r){if(!(t<0)){for(var e=this.head,n=0;e&&n!=t;)e=e.next,n++;if(e){var i=new lt.Entry(r),a=e.prev;a?(a.next=i,i.prev=a):this.head=i,i.next=e,e.prev=i}else this.insert(r)}},lt.prototype.insertBeforeEntry=function(t,r){var e=new lt.Entry(t),n=r.prev;n?(n.next=e,e.prev=n):this.head=e,e.next=r,r.prev=e,this._length++},lt.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,this.tail=t):this.head=this.tail=t,this._length++},lt.prototype.remove=function(t){var r=t.prev,e=t.next;r?r.next=e:this.head=e,e?e.prev=r:this.tail=r,t.next=t.prev=null,this._length--},lt.prototype.removeAt=function(t){if(!(t<0)){for(var r=this.head,e=0;r&&e!=t;)r=r.next,e++;return r?(this.remove(r),r.value):void 0}},lt.prototype.getHead=function(){if(this.head)return this.head.value},lt.prototype.getTail=function(){if(this.tail)return this.tail.value},lt.prototype.getAt=function(t){if(!(t<0)){for(var r=this.head,e=0;r&&e!=t;)r=r.next,e++;return r.value}},lt.prototype.indexOf=function(t){for(var r=this.head,e=0;r;){if(r.value===t)return e;r=r.next,e++}},lt.prototype.length=function(){return this._length},lt.prototype.isEmpty=function(){return 0===this._length},lt.prototype.forEach=function(t,r){for(var e=this.head,n=0,i=void 0!==r;e;)i?t.call(r,e.value,n):t(e.value,n),e=e.next,n++},lt.prototype.clear=function(){this.tail=this.head=null,this._length=0},lt.Entry=function(t){this.value=t,this.next=null,this.prev=null};var ft=function(t){this._list=new lt,this._map={},this._maxSize=t||10};ft.prototype.setMaxSize=function(t){this._maxSize=t},ft.prototype.put=function(t,r){if(!this._map.hasOwnProperty(t)){var e=this._list.length();if(e>=this._maxSize&&e>0){var n=this._list.head;this._list.remove(n),delete this._map[n.key]}var i=this._list.insert(r);i.key=t,this._map[t]=i}},ft.prototype.get=function(t){var r=this._map[t];if(this._map.hasOwnProperty(t))return r!==this._list.tail&&(this._list.remove(r),this._list.insertEntry(r)),r.value},ft.prototype.remove=function(t){var r=this._map[t];void 0!==r&&(delete this._map[t],this._list.remove(r))},ft.prototype.clear=function(){this._list.clear(),this._map={}};var dt={},mt={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function pt(t){return(t=Math.round(t))<0?0:t>255?255:t}function yt(t){return t<0?0:t>1?1:t}function _t(t){return t.length&&"%"===t.charAt(t.length-1)?pt(parseFloat(t)/100*255):pt(parseInt(t,10))}function vt(t){return t.length&&"%"===t.charAt(t.length-1)?yt(parseFloat(t)/100):yt(parseFloat(t))}function gt(t,r,e){return e<0?e+=1:e>1&&(e-=1),6*e<1?t+(r-t)*e*6:2*e<1?r:3*e<2?t+(r-t)*(2/3-e)*6:t}function xt(t,r,e){return t+(r-t)*e}function Tt(t,r,e,n,i){return t[0]=r,t[1]=e,t[2]=n,t[3]=i,t}function bt(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t}var Et=new ft(20),wt=null;function At(t,r){wt&&bt(wt,r),wt=Et.put(t,wt||r.slice())}function St(t,r){var e=(parseFloat(t[0])%360+360)%360/360,n=vt(t[1]),i=vt(t[2]),a=i<=.5?i*(n+1):i+n-i*n,o=2*i-a;return Tt(r=r||[],pt(255*gt(o,a,e+1/3)),pt(255*gt(o,a,e)),pt(255*gt(o,a,e-1/3)),1),4===t.length&&(r[3]=t[3]),r}dt.parse=function(t,r){if(t){r=r||[];var e=Et.get(t);if(e)return bt(r,e);var n,i=(t+="").replace(/ /g,"").toLowerCase();if(i in mt)return bt(r,mt[i]),At(t,r),r;if("#"===i.charAt(0))return 4===i.length?(n=parseInt(i.substr(1),16))>=0&&n<=4095?(Tt(r,(3840&n)>>4|(3840&n)>>8,240&n|(240&n)>>4,15&n|(15&n)<<4,1),At(t,r),r):void Tt(r,0,0,0,1):7===i.length?(n=parseInt(i.substr(1),16))>=0&&n<=16777215?(Tt(r,(16711680&n)>>16,(65280&n)>>8,255&n,1),At(t,r),r):void Tt(r,0,0,0,1):void 0;var a=i.indexOf("("),o=i.indexOf(")");if(-1!==a&&o+1===i.length){var s=i.substr(0,a),u=i.substr(a+1,o-(a+1)).split(","),h=1;switch(s){case"rgba":if(4!==u.length)return void Tt(r,0,0,0,1);h=vt(u.pop());case"rgb":return 3!==u.length?void Tt(r,0,0,0,1):(Tt(r,_t(u[0]),_t(u[1]),_t(u[2]),h),At(t,r),r);case"hsla":return 4!==u.length?void Tt(r,0,0,0,1):(u[3]=vt(u[3]),St(u,r),At(t,r),r);case"hsl":return 3!==u.length?void Tt(r,0,0,0,1):(St(u,r),At(t,r),r);default:return}}Tt(r,0,0,0,1)}},dt.parseToFloat=function(t,r){if(r=dt.parse(t,r))return r[0]/=255,r[1]/=255,r[2]/=255,r},dt.lift=function(t,r){var e=dt.parse(t);if(e){for(var n=0;n<3;n++)e[n]=r<0?e[n]*(1-r)|0:(255-e[n])*r+e[n]|0;return dt.stringify(e,4===e.length?"rgba":"rgb")}},dt.toHex=function(t){var r=dt.parse(t);if(r)return((1<<24)+(r[0]<<16)+(r[1]<<8)+ +r[2]).toString(16).slice(1)},dt.fastLerp=function(t,r,e){if(r&&r.length&&t>=0&&t<=1){e=e||[];var n=t*(r.length-1),i=Math.floor(n),a=Math.ceil(n),o=r[i],s=r[a],u=n-i;return e[0]=pt(xt(o[0],s[0],u)),e[1]=pt(xt(o[1],s[1],u)),e[2]=pt(xt(o[2],s[2],u)),e[3]=yt(xt(o[3],s[3],u)),e}},dt.fastMapToColor=dt.fastLerp,dt.lerp=function(t,r,e){if(r&&r.length&&t>=0&&t<=1){var n=t*(r.length-1),i=Math.floor(n),a=Math.ceil(n),o=dt.parse(r[i]),s=dt.parse(r[a]),u=n-i,h=dt.stringify([pt(xt(o[0],s[0],u)),pt(xt(o[1],s[1],u)),pt(xt(o[2],s[2],u)),yt(xt(o[3],s[3],u))],"rgba");return e?{color:h,leftIndex:i,rightIndex:a,value:n}:h}},dt.mapToColor=dt.lerp,dt.modifyHSL=function(t,r,e,n){if(t=dt.parse(t))return t=function(t){if(t){var r,e,n=t[0]/255,i=t[1]/255,a=t[2]/255,o=Math.min(n,i,a),s=Math.max(n,i,a),u=s-o,h=(s+o)/2;if(0===u)r=0,e=0;else{e=h<.5?u/(s+o):u/(2-s-o);var c=((s-n)/6+u/2)/u,l=((s-i)/6+u/2)/u,f=((s-a)/6+u/2)/u;n===s?r=f-l:i===s?r=1/3+c-f:a===s&&(r=2/3+l-c),r<0&&(r+=1),r>1&&(r-=1)}var d=[360*r,e,h];return null!=t[3]&&d.push(t[3]),d}}(t),null!=r&&(t[0]=(i=r,(i=Math.round(i))<0?0:i>360?360:i)),null!=e&&(t[1]=vt(e)),null!=n&&(t[2]=vt(n)),dt.stringify(St(t),"rgba");var i},dt.modifyAlpha=function(t,r){if((t=dt.parse(t))&&null!=r)return t[3]=yt(r),dt.stringify(t,"rgba")},dt.stringify=function(t,r){if(t&&t.length){var e=t[0]+","+t[1]+","+t[2];return"rgba"!==r&&"hsva"!==r&&"hsla"!==r||(e+=","+t[3]),r+"("+e+")"}};var Mt=dt.parseToFloat,Ct={};function Rt(t){var r=Object.keys(t);r.sort();for(var e=[],n=0;n<r.length;n++){var i=r[n],a=t[i];null===a?e.push(i):e.push(i+" "+a.toString())}return e.join("\n")}var Dt,It=u.extend((function(){return{name:"",depthTest:!0,depthMask:!0,transparent:!1,blend:null,autoUpdateTextureStatus:!0,uniforms:{},vertexDefines:{},fragmentDefines:{},_textureStatus:{},_enabledUniforms:null}}),(function(){this.name||(this.name="MATERIAL_"+this.__uid__),this.shader&&this.attachShader(this.shader,!0)}),{precision:"highp",setUniform:function(t,r){var e=this.uniforms[t];e&&("string"==typeof r&&(r=Mt(r)||r),e.value=r,this.autoUpdateTextureStatus&&"t"===e.type&&(r?this.enableTexture(t):this.disableTexture(t)))},setUniforms:function(t){for(var r in t){var e=t[r];this.setUniform(r,e)}},isUniformEnabled:function(t){return this._enabledUniforms.indexOf(t)>=0},getEnabledUniforms:function(){return this._enabledUniforms},getTextureUniforms:function(){return this._textureUniforms},set:function(t,r){if("object"==typeof t)for(var e in t){var n=t[e];this.setUniform(e,n)}else this.setUniform(t,r)},get:function(t){var r=this.uniforms[t];if(r)return r.value},attachShader:function(t,r){var e=this.uniforms;this.uniforms=t.createUniforms(),this.shader=t;var n=this.uniforms;this._enabledUniforms=Object.keys(n),this._enabledUniforms.sort(),this._textureUniforms=this._enabledUniforms.filter((function(t){var r=this.uniforms[t].type;return"t"===r||"tv"===r}),this);var i=this.vertexDefines,a=this.fragmentDefines;if(this.vertexDefines=s.clone(t.vertexDefines),this.fragmentDefines=s.clone(t.fragmentDefines),r){for(var o in e)n[o]&&(n[o].value=e[o].value);s.defaults(this.vertexDefines,i),s.defaults(this.fragmentDefines,a)}var u={};for(var h in t.textures)u[h]={shaderType:t.textures[h].shaderType,type:t.textures[h].type,enabled:!(!r||!this._textureStatus[h])&&this._textureStatus[h].enabled};this._textureStatus=u,this._programKey=""},clone:function(){var t=new this.constructor({name:this.name,shader:this.shader});for(var r in this.uniforms)t.uniforms[r].value=this.uniforms[r].value;return t.depthTest=this.depthTest,t.depthMask=this.depthMask,t.transparent=this.transparent,t.blend=this.blend,t.vertexDefines=s.clone(this.vertexDefines),t.fragmentDefines=s.clone(this.fragmentDefines),t.enableTexture(this.getEnabledTextures()),t.precision=this.precision,t},define:function(t,r,e){var n=this.vertexDefines,i=this.fragmentDefines;"vertex"!==t&&"fragment"!==t&&"both"!==t&&arguments.length<3&&(e=r,r=t,t="both"),e=null!=e?e:null,"vertex"!==t&&"both"!==t||n[r]!==e&&(n[r]=e,this._programKey=""),"fragment"!==t&&"both"!==t||i[r]!==e&&(i[r]=e,"both"!==t&&(this._programKey=""))},undefine:function(t,r){"vertex"!==t&&"fragment"!==t&&"both"!==t&&arguments.length<2&&(r=t,t="both"),"vertex"!==t&&"both"!==t||this.isDefined("vertex",r)&&(delete this.vertexDefines[r],this._programKey=""),"fragment"!==t&&"both"!==t||this.isDefined("fragment",r)&&(delete this.fragmentDefines[r],"both"!==t&&(this._programKey=""))},isDefined:function(t,r){switch(t){case"vertex":return void 0!==this.vertexDefines[r];case"fragment":return void 0!==this.fragmentDefines[r]}},getDefine:function(t,r){switch(t){case"vertex":return this.vertexDefines[r];case"fragment":return this.fragmentDefines[r]}},enableTexture:function(t){if(Array.isArray(t))for(var r=0;r<t.length;r++)this.enableTexture(t[r]);else{var e=this._textureStatus[t];if(e)e.enabled||(e.enabled=!0,this._programKey="")}},enableTexturesAll:function(){var t=this._textureStatus;for(var r in t)t[r].enabled=!0;this._programKey=""},disableTexture:function(t){if(Array.isArray(t))for(var r=0;r<t.length;r++)this.disableTexture(t[r]);else{var e=this._textureStatus[t];if(e)!e.enabled||(e.enabled=!1,this._programKey="")}},disableTexturesAll:function(){var t=this._textureStatus;for(var r in t)t[r].enabled=!1;this._programKey=""},isTextureEnabled:function(t){var r=this._textureStatus;return!!r[t]&&r[t].enabled},getEnabledTextures:function(){var t=[],r=this._textureStatus;for(var e in r)r[e].enabled&&t.push(e);return t},dirtyDefines:function(){this._programKey=""},getProgramKey:function(){return this._programKey||(this._programKey=function(t,r,e){e.sort();for(var n=[],i=0;i<e.length;i++){var a=e[i];n.push(a)}var o=Rt(t)+"\n"+Rt(r)+"\n"+n.join("\n");if(Ct[o])return Ct[o];var u=s.genGUID();return Ct[o]=u,u}(this.vertexDefines,this.fragmentDefines,this.getEnabledTextures())),this._programKey}}),Lt=1e-6,Ot=Array,Pt=Math.random,Nt={};Nt.create=function(){var t=new Ot(2);return t[0]=0,t[1]=0,t},Nt.clone=function(t){var r=new Ot(2);return r[0]=t[0],r[1]=t[1],r},Nt.fromValues=function(t,r){var e=new Ot(2);return e[0]=t,e[1]=r,e},Nt.copy=function(t,r){return t[0]=r[0],t[1]=r[1],t},Nt.set=function(t,r,e){return t[0]=r,t[1]=e,t},Nt.add=function(t,r,e){return t[0]=r[0]+e[0],t[1]=r[1]+e[1],t},Nt.subtract=function(t,r,e){return t[0]=r[0]-e[0],t[1]=r[1]-e[1],t},Nt.sub=Nt.subtract,Nt.multiply=function(t,r,e){return t[0]=r[0]*e[0],t[1]=r[1]*e[1],t},Nt.mul=Nt.multiply,Nt.divide=function(t,r,e){return t[0]=r[0]/e[0],t[1]=r[1]/e[1],t},Nt.div=Nt.divide,Nt.min=function(t,r,e){return t[0]=Math.min(r[0],e[0]),t[1]=Math.min(r[1],e[1]),t},Nt.max=function(t,r,e){return t[0]=Math.max(r[0],e[0]),t[1]=Math.max(r[1],e[1]),t},Nt.scale=function(t,r,e){return t[0]=r[0]*e,t[1]=r[1]*e,t},Nt.scaleAndAdd=function(t,r,e,n){return t[0]=r[0]+e[0]*n,t[1]=r[1]+e[1]*n,t},Nt.distance=function(t,r){var e=r[0]-t[0],n=r[1]-t[1];return Math.sqrt(e*e+n*n)},Nt.dist=Nt.distance,Nt.squaredDistance=function(t,r){var e=r[0]-t[0],n=r[1]-t[1];return e*e+n*n},Nt.sqrDist=Nt.squaredDistance,Nt.length=function(t){var r=t[0],e=t[1];return Math.sqrt(r*r+e*e)},Nt.len=Nt.length,Nt.squaredLength=function(t){var r=t[0],e=t[1];return r*r+e*e},Nt.sqrLen=Nt.squaredLength,Nt.negate=function(t,r){return t[0]=-r[0],t[1]=-r[1],t},Nt.inverse=function(t,r){return t[0]=1/r[0],t[1]=1/r[1],t},Nt.normalize=function(t,r){var e=r[0],n=r[1],i=e*e+n*n;return i>0&&(i=1/Math.sqrt(i),t[0]=r[0]*i,t[1]=r[1]*i),t},Nt.dot=function(t,r){return t[0]*r[0]+t[1]*r[1]},Nt.cross=function(t,r,e){var n=r[0]*e[1]-r[1]*e[0];return t[0]=t[1]=0,t[2]=n,t},Nt.lerp=function(t,r,e,n){var i=r[0],a=r[1];return t[0]=i+n*(e[0]-i),t[1]=a+n*(e[1]-a),t},Nt.random=function(t,r){r=r||1;var e=2*GLMAT_RANDOM()*Math.PI;return t[0]=Math.cos(e)*r,t[1]=Math.sin(e)*r,t},Nt.transformMat2=function(t,r,e){var n=r[0],i=r[1];return t[0]=e[0]*n+e[2]*i,t[1]=e[1]*n+e[3]*i,t},Nt.transformMat2d=function(t,r,e){var n=r[0],i=r[1];return t[0]=e[0]*n+e[2]*i+e[4],t[1]=e[1]*n+e[3]*i+e[5],t},Nt.transformMat3=function(t,r,e){var n=r[0],i=r[1];return t[0]=e[0]*n+e[3]*i+e[6],t[1]=e[1]*n+e[4]*i+e[7],t},Nt.transformMat4=function(t,r,e){var n=r[0],i=r[1];return t[0]=e[0]*n+e[4]*i+e[12],t[1]=e[1]*n+e[5]*i+e[13],t},Nt.forEach=(Dt=Nt.create(),function(t,r,e,n,i,a){var o,s;for(r||(r=2),e||(e=0),s=n?Math.min(n*r+e,t.length):t.length,o=e;o<s;o+=r)Dt[0]=t[o],Dt[1]=t[o+1],i(Dt,Dt,a),t[o]=Dt[0],t[o+1]=Dt[1];return t});var Bt=function(t,r){t=t||0,r=r||0,this.array=Nt.fromValues(t,r),this._dirty=!0};if(Bt.prototype={constructor:Bt,add:function(t){return Nt.add(this.array,this.array,t.array),this._dirty=!0,this},set:function(t,r){return this.array[0]=t,this.array[1]=r,this._dirty=!0,this},setArray:function(t){return this.array[0]=t[0],this.array[1]=t[1],this._dirty=!0,this},clone:function(){return new Bt(this.x,this.y)},copy:function(t){return Nt.copy(this.array,t.array),this._dirty=!0,this},cross:function(t,r){return Nt.cross(t.array,this.array,r.array),t._dirty=!0,this},dist:function(t){return Nt.dist(this.array,t.array)},distance:function(t){return Nt.distance(this.array,t.array)},div:function(t){return Nt.div(this.array,this.array,t.array),this._dirty=!0,this},divide:function(t){return Nt.divide(this.array,this.array,t.array),this._dirty=!0,this},dot:function(t){return Nt.dot(this.array,t.array)},len:function(){return Nt.len(this.array)},length:function(){return Nt.length(this.array)},lerp:function(t,r,e){return Nt.lerp(this.array,t.array,r.array,e),this._dirty=!0,this},min:function(t){return Nt.min(this.array,this.array,t.array),this._dirty=!0,this},max:function(t){return Nt.max(this.array,this.array,t.array),this._dirty=!0,this},mul:function(t){return Nt.mul(this.array,this.array,t.array),this._dirty=!0,this},multiply:function(t){return Nt.multiply(this.array,this.array,t.array),this._dirty=!0,this},negate:function(){return Nt.negate(this.array,this.array),this._dirty=!0,this},normalize:function(){return Nt.normalize(this.array,this.array),this._dirty=!0,this},random:function(t){return Nt.random(this.array,t),this._dirty=!0,this},scale:function(t){return Nt.scale(this.array,this.array,t),this._dirty=!0,this},scaleAndAdd:function(t,r){return Nt.scaleAndAdd(this.array,this.array,t.array,r),this._dirty=!0,this},sqrDist:function(t){return Nt.sqrDist(this.array,t.array)},squaredDistance:function(t){return Nt.squaredDistance(this.array,t.array)},sqrLen:function(){return Nt.sqrLen(this.array)},squaredLength:function(){return Nt.squaredLength(this.array)},sub:function(t){return Nt.sub(this.array,this.array,t.array),this._dirty=!0,this},subtract:function(t){return Nt.subtract(this.array,this.array,t.array),this._dirty=!0,this},transformMat2:function(t){return Nt.transformMat2(this.array,this.array,t.array),this._dirty=!0,this},transformMat2d:function(t){return Nt.transformMat2d(this.array,this.array,t.array),this._dirty=!0,this},transformMat3:function(t){return Nt.transformMat3(this.array,this.array,t.array),this._dirty=!0,this},transformMat4:function(t){return Nt.transformMat4(this.array,this.array,t.array),this._dirty=!0,this},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}},Object.defineProperty){var Ut=Bt.prototype;Object.defineProperty(Ut,"x",{get:function(){return this.array[0]},set:function(t){this.array[0]=t,this._dirty=!0}}),Object.defineProperty(Ut,"y",{get:function(){return this.array[1]},set:function(t){this.array[1]=t,this._dirty=!0}})}Bt.add=function(t,r,e){return Nt.add(t.array,r.array,e.array),t._dirty=!0,t},Bt.set=function(t,r,e){return Nt.set(t.array,r,e),t._dirty=!0,t},Bt.copy=function(t,r){return Nt.copy(t.array,r.array),t._dirty=!0,t},Bt.cross=function(t,r,e){return Nt.cross(t.array,r.array,e.array),t._dirty=!0,t},Bt.distance=Bt.dist=function(t,r){return Nt.distance(t.array,r.array)},Bt.divide=Bt.div=function(t,r,e){return Nt.divide(t.array,r.array,e.array),t._dirty=!0,t},Bt.dot=function(t,r){return Nt.dot(t.array,r.array)},Bt.len=function(t){return Nt.length(t.array)},Bt.lerp=function(t,r,e,n){return Nt.lerp(t.array,r.array,e.array,n),t._dirty=!0,t},Bt.min=function(t,r,e){return Nt.min(t.array,r.array,e.array),t._dirty=!0,t},Bt.max=function(t,r,e){return Nt.max(t.array,r.array,e.array),t._dirty=!0,t},Bt.multiply=Bt.mul=function(t,r,e){return Nt.multiply(t.array,r.array,e.array),t._dirty=!0,t},Bt.negate=function(t,r){return Nt.negate(t.array,r.array),t._dirty=!0,t},Bt.normalize=function(t,r){return Nt.normalize(t.array,r.array),t._dirty=!0,t},Bt.random=function(t,r){return Nt.random(t.array,r),t._dirty=!0,t},Bt.scale=function(t,r,e){return Nt.scale(t.array,r.array,e),t._dirty=!0,t},Bt.scaleAndAdd=function(t,r,e,n){return Nt.scaleAndAdd(t.array,r.array,e.array,n),t._dirty=!0,t},Bt.squaredDistance=Bt.sqrDist=function(t,r){return Nt.sqrDist(t.array,r.array)},Bt.squaredLength=Bt.sqrLen=function(t){return Nt.sqrLen(t.array)},Bt.subtract=Bt.sub=function(t,r,e){return Nt.subtract(t.array,r.array,e.array),t._dirty=!0,t},Bt.transformMat2=function(t,r,e){return Nt.transformMat2(t.array,r.array,e.array),t._dirty=!0,t},Bt.transformMat2d=function(t,r,e){return Nt.transformMat2d(t.array,r.array,e.array),t._dirty=!0,t},Bt.transformMat3=function(t,r,e){return Nt.transformMat3(t.array,r.array,e.array),t._dirty=!0,t},Bt.transformMat4=function(t,r,e){return Nt.transformMat4(t.array,r.array,e.array),t._dirty=!0,t};var Ft={};function kt(t){for(var r=t.split("\n"),e=0,n=r.length;e<n;e++)r[e]=e+1+": "+r[e];return r.join("\n")}function Ht(t,r,e){if(!t.getShaderParameter(r,t.COMPILE_STATUS))return[t.getShaderInfoLog(r),kt(e)].join("\n")}var Vt=new ht.Float32Array(16),Wt=u.extend({uniformSemantics:{},attributes:{}},(function(){this._locations={},this._textureSlot=0,this._program=null}),{bind:function(t){this._textureSlot=0,t.gl.useProgram(this._program)},hasUniform:function(t){var r=this._locations[t];return null!=r},useTextureSlot:function(t,r,e){r&&(t.gl.activeTexture(t.gl.TEXTURE0+e),r.isRenderable()?r.bind(t):r.unbind(t))},currentTextureSlot:function(){return this._textureSlot},resetTextureSlot:function(t){this._textureSlot=t||0},takeCurrentTextureSlot:function(t,r){var e=this._textureSlot;return this.useTextureSlot(t,r,e),this._textureSlot++,e},setUniform:function(t,r,e,n){var i=this._locations[e];if(null==i)return!1;switch(r){case"m4":if(!(n instanceof Float32Array)){for(var a=0;a<n.length;a++)Vt[a]=n[a];n=Vt}t.uniformMatrix4fv(i,!1,n);break;case"2i":t.uniform2i(i,n[0],n[1]);break;case"2f":t.uniform2f(i,n[0],n[1]);break;case"3i":t.uniform3i(i,n[0],n[1],n[2]);break;case"3f":t.uniform3f(i,n[0],n[1],n[2]);break;case"4i":t.uniform4i(i,n[0],n[1],n[2],n[3]);break;case"4f":t.uniform4f(i,n[0],n[1],n[2],n[3]);break;case"1i":t.uniform1i(i,n);break;case"1f":t.uniform1f(i,n);break;case"1fv":t.uniform1fv(i,n);break;case"1iv":t.uniform1iv(i,n);break;case"2iv":t.uniform2iv(i,n);break;case"2fv":t.uniform2fv(i,n);break;case"3iv":t.uniform3iv(i,n);break;case"3fv":t.uniform3fv(i,n);break;case"4iv":t.uniform4iv(i,n);break;case"4fv":t.uniform4fv(i,n);break;case"m2":case"m2v":t.uniformMatrix2fv(i,!1,n);break;case"m3":case"m3v":t.uniformMatrix3fv(i,!1,n);break;case"m4v":if(Array.isArray(n)&&Array.isArray(n[0])){var o=new ht.Float32Array(16*n.length),s=0;for(a=0;a<n.length;a++)for(var u=n[a],h=0;h<16;h++)o[s++]=u[h];t.uniformMatrix4fv(i,!1,o)}else t.uniformMatrix4fv(i,!1,n)}return!0},setUniformOfSemantic:function(t,r,e){var n=this.uniformSemantics[r];return!!n&&this.setUniform(t,n.type,n.symbol,e)},enableAttributes:function(t,r,e){var n,i=t.gl,a=this._program,o=this._locations;(n=e?e.__enabledAttributeList:Ft[t.__uid__])||(n=e?e.__enabledAttributeList=[]:Ft[t.__uid__]=[]);for(var s=[],u=0;u<r.length;u++){var h=r[u];if(this.attributes[h]){var c=o[h];if(null==c){if(-1===(c=i.getAttribLocation(a,h))){s[u]=-1;continue}o[h]=c}s[u]=c,n[c]?n[c]=2:n[c]=1}else s[u]=-1}for(u=0;u<n.length;u++)switch(n[u]){case 1:i.enableVertexAttribArray(u),n[u]=3;break;case 2:n[u]=3;break;case 3:i.disableVertexAttribArray(u),n[u]=0}return s},getAttribLocation:function(t,r){var e=this._locations,n=e[r];return null==n&&(n=t.getAttribLocation(this._program,r),e[r]=n),n},buildProgram:function(t,r,e,n){var i=t.createShader(t.VERTEX_SHADER),a=t.createProgram();t.shaderSource(i,e),t.compileShader(i);var o=t.createShader(t.FRAGMENT_SHADER);t.shaderSource(o,n),t.compileShader(o);var s=Ht(t,i,e);if(s)return s;if(s=Ht(t,o,n))return s;if(t.attachShader(a,i),t.attachShader(a,o),r.attributeSemantics.POSITION)t.bindAttribLocation(a,0,r.attributeSemantics.POSITION.symbol);else{var u=Object.keys(this.attributes);t.bindAttribLocation(a,0,u[0])}if(t.linkProgram(a),t.deleteShader(i),t.deleteShader(o),this._program=a,this.vertexCode=e,this.fragmentCode=n,!t.getProgramParameter(a,t.LINK_STATUS))return"Could not link program\n"+t.getProgramInfoLog(a);for(var h=0;h<r.uniforms.length;h++){var c=r.uniforms[h];this._locations[c]=t.getUniformLocation(a,c)}}}),Gt=/for\s*?\(int\s*?_idx_\s*\=\s*([\w-]+)\;\s*_idx_\s*<\s*([\w-]+);\s*_idx_\s*\+\+\s*\)\s*\{\{([\s\S]+?)(?=\}\})\}\}/g;function zt(t,r,e){var n={};for(var i in e)n[i+"_COUNT"]=e[i];return t.replace(Gt,(function(t,e,i,a){var o="";isNaN(e)&&(e=e in r?r[e]:n[e]),isNaN(i)&&(i=i in r?r[i]:n[i]);for(var s=parseInt(e);s<parseInt(i);s++)o+="{"+a.replace(/float\s*\(\s*_idx_\s*\)/g,s.toFixed(1)).replace(/_idx_/g,s)+"}";return o}))}function Xt(t,r,e){var n=[];if(r)for(var i in r){var a=r[i];a>0&&n.push("#define "+i.toUpperCase()+"_COUNT "+a)}if(e)for(var o=0;o<e.length;o++){var s=e[o];n.push("#define "+s.toUpperCase()+"_ENABLED")}for(var s in t){var u=t[s];null===u?n.push("#define "+s):n.push("#define "+s+" "+u.toString())}return n.join("\n")}function jt(t){this._renderer=t,this._cache={}}jt.prototype.getProgram=function(t,r,e){var n=this._cache,i=t.isSkinnedMesh&&t.isSkinnedMesh(),a=t.isInstancedMesh&&t.isInstancedMesh(),o="s"+r.shader.shaderID+"m"+r.getProgramKey();if(e&&(o+="se"+e.getProgramKey(t.lightGroup)),i&&(o+=",sk"+t.joints.length),a&&(o+=",is"),v=n[o])return v;var s=e?e.getLightsNumbers(t.lightGroup):{},u=this._renderer,h=u.gl,c=r.getEnabledTextures(),l="";if(i){var f={SKINNING:null,JOINT_COUNT:t.joints.length};t.joints.length>u.getMaxJointNumber()&&(f.USE_SKIN_MATRICES_TEXTURE=null),l+="\n"+Xt(f)+"\n"}a&&(l+="\n#define INSTANCING\n");var d=l+Xt(r.vertexDefines,s,c),m=l+Xt(r.fragmentDefines,s,c),p=d+"\n"+r.shader.vertex,y=["OES_standard_derivatives","EXT_shader_texture_lod"].filter((function(t){return null!=u.getGLExtension(t)}));y.indexOf("EXT_shader_texture_lod")>=0&&(m+="\n#define SUPPORT_TEXTURE_LOD"),y.indexOf("OES_standard_derivatives")>=0&&(m+="\n#define SUPPORT_STANDARD_DERIVATIVES");var _,v,g=function(t){for(var r=[],e=0;e<t.length;e++)r.push("#extension GL_"+t[e]+" : enable");return r.join("\n")}(y)+"\n"+(["precision",_=r.precision,"float"].join(" ")+";\n"+["precision",_,"int"].join(" ")+";\n"+["precision",_,"sampler2D"].join(" ")+";\n\n")+m+"\n"+r.shader.fragment,x=zt(p,r.vertexDefines,s),T=zt(g,r.fragmentDefines,s);(v=new Wt).uniformSemantics=r.shader.uniformSemantics,v.attributes=r.shader.attributes;var b=v.buildProgram(h,r.shader,x,T);return v.__error=b,n[o]=v,v};var qt=/uniform\s+(bool|float|int|vec2|vec3|vec4|ivec2|ivec3|ivec4|mat2|mat3|mat4|sampler2D|samplerCube)\s+([\s\S]*?);/g,Yt=/attribute\s+(float|int|vec2|vec3|vec4)\s+([\s\S]*?);/g,Kt=/#define\s+(\w+)?(\s+[\d-.]+)?\s*;?\s*\n/g,Zt={bool:"1i",int:"1i",sampler2D:"t",samplerCube:"t",float:"1f",vec2:"2f",vec3:"3f",vec4:"4f",ivec2:"2i",ivec3:"3i",ivec4:"4i",mat2:"m2",mat3:"m3",mat4:"m4"};function Jt(t){for(var r=[],e=0;e<t;e++)r[e]=0;return r}var Qt={bool:function(){return!0},int:function(){return 0},float:function(){return 0},sampler2D:function(){return null},samplerCube:function(){return null},vec2:function(){return Jt(2)},vec3:function(){return Jt(3)},vec4:function(){return Jt(4)},ivec2:function(){return Jt(2)},ivec3:function(){return Jt(3)},ivec4:function(){return Jt(4)},mat2:function(){return Jt(4)},mat3:function(){return Jt(9)},mat4:function(){return Jt(16)},array:function(){return[]}},$t=["POSITION","NORMAL","BINORMAL","TANGENT","TEXCOORD","TEXCOORD_0","TEXCOORD_1","COLOR","JOINT","WEIGHT"],tr=["SKIN_MATRIX","VIEWPORT_SIZE","VIEWPORT","DEVICEPIXELRATIO","WINDOW_SIZE","NEAR","FAR","TIME"],rr=["WORLD","VIEW","PROJECTION","WORLDVIEW","VIEWPROJECTION","WORLDVIEWPROJECTION","WORLDINVERSE","VIEWINVERSE","PROJECTIONINVERSE","WORLDVIEWINVERSE","VIEWPROJECTIONINVERSE","WORLDVIEWPROJECTIONINVERSE","WORLDTRANSPOSE","VIEWTRANSPOSE","PROJECTIONTRANSPOSE","WORLDVIEWTRANSPOSE","VIEWPROJECTIONTRANSPOSE","WORLDVIEWPROJECTIONTRANSPOSE","WORLDINVERSETRANSPOSE","VIEWINVERSETRANSPOSE","PROJECTIONINVERSETRANSPOSE","WORLDVIEWINVERSETRANSPOSE","VIEWPROJECTIONINVERSETRANSPOSE","WORLDVIEWPROJECTIONINVERSETRANSPOSE"],er={vec4:4,vec3:3,vec2:2,float:1},nr={},ir={};function ar(t){return t.replace(/[ \t]*\/\/.*\n/g,"").replace(/[ \t]*\/\*[\s\S]*?\*\//g,"")}function or(t,r){for(var e=/[,=\(\):]/,n=r.replace(/:\s*\[\s*(.*)\s*\]/g,"="+t+"($1)").replace(/\s+/g,"").split(/(?=[,=\(\):])/g),i=[],a=0;a<n.length;a++)n[a].match(e)?i.push(n[a].charAt(0),n[a].slice(1)):i.push(n[a]);var o,s=0,u={},h=null;function c(t){var r=t.match(/\[(.*?)\]/);o=t.replace(/\[(.*?)\]/,""),u[o]={},r&&(u[o].isArray=!0,u[o].arraySize=r[1])}c((n=i)[0]);for(a=1;a<n.length;a++){var l=n[a];if(l)if("="!==l)if(":"!==l)if(","!==l)if(")"!==l)if("("!==l)if(l.indexOf("vec")>=0){if(1!==s&&4!==s)break;s=2,h=[]}else if(1!==s)if(4!==s)c(l),s=0;else{var f=l;$t.indexOf(f)>=0||tr.indexOf(f)>=0||rr.indexOf(f)>=0?u[o].semantic=f:"ignore"===f||"unconfigurable"===f?u[o].ignore=!0:u[o].value="bool"===t?"true"===f:parseFloat(f)}else u[o].value="bool"===t?"true"===l:parseFloat(l),h=null;else{if(2!==s)break;if(!(h instanceof Array))break;h.push(+n[++a])}else u[o].value=new ht.Float32Array(h),h=null,s=5;else if(2===s){if(!(h instanceof Array))break;h.push(+n[++a])}else s=5;else s=4;else{if(0!==s&&3!==s)break;s=1}}return u}function sr(t,r){"object"==typeof t&&(r=t.fragment,t=t.vertex),t=ar(t),r=ar(r),this._shaderID=function(t,r){var e="vertex:"+t+"fragment:"+r;if(nr[e])return nr[e];var n=s.genGUID();return nr[e]=n,ir[n]={vertex:t,fragment:r},n}(t,r),this._vertexCode=sr.parseImport(t),this._fragmentCode=sr.parseImport(r),this.attributeSemantics={},this.matrixSemantics={},this.uniformSemantics={},this.matrixSemanticKeys=[],this.uniformTemplates={},this.attributes={},this.textures={},this.vertexDefines={},this.fragmentDefines={},this._parseAttributes(),this._parseUniforms(),this._parseDefines()}sr.prototype={constructor:sr,createUniforms:function(){var t={};for(var r in this.uniformTemplates){var e=this.uniformTemplates[r];t[r]={type:e.type,value:e.value()}}return t},_parseImport:function(){this._vertexCode=sr.parseImport(this.vertex),this._fragmentCode=sr.parseImport(this.fragment)},_addSemanticUniform:function(t,r,e){if($t.indexOf(e)>=0)this.attributeSemantics[e]={symbol:t,type:r};else if(rr.indexOf(e)>=0){var n=!1,i=e;e.match(/TRANSPOSE$/)&&(n=!0,i=e.slice(0,-9)),this.matrixSemantics[e]={symbol:t,type:r,isTranspose:n,semanticNoTranspose:i}}else tr.indexOf(e)>=0&&(this.uniformSemantics[e]={symbol:t,type:r})},_addMaterialUniform:function(t,r,e,n,i,a){a[t]={type:e,value:i?Qt.array:n||Qt[r],semantic:null}},_parseUniforms:function(){var t={},r=this,e="vertex";function n(t){return null!=t?function(){return t}:null}function i(i,a,o){var s=or(a,o),u=[];for(var h in s){var c=s[h],l=c.semantic,f=h,d=Zt[a],m=n(s[h].value);s[h].isArray&&(f+="["+s[h].arraySize+"]",d+="v"),u.push(f),r._uniformList.push(h),c.ignore||("sampler2D"!==a&&"samplerCube"!==a||(r.textures[h]={shaderType:e,type:a}),l?r._addSemanticUniform(h,d,l):r._addMaterialUniform(h,a,d,m,s[h].isArray,t))}return u.length>0?"uniform "+a+" "+u.join(",")+";\n":""}this._uniformList=[],this._vertexCode=this._vertexCode.replace(qt,i),e="fragment",this._fragmentCode=this._fragmentCode.replace(qt,i),r.matrixSemanticKeys=Object.keys(this.matrixSemantics),this.uniformTemplates=t},_parseAttributes:function(){var t={},r=this;this._vertexCode=this._vertexCode.replace(Yt,(function(e,n,i){var a=or(n,i),o=er[n]||1,s=[];for(var u in a){var h=a[u].semantic;if(t[u]={type:"float",size:o,semantic:h||null},h){if($t.indexOf(h)<0)throw new Error('Unkown semantic "'+h+'"');r.attributeSemantics[h]={symbol:u,type:n}}s.push(u)}return"attribute "+n+" "+s.join(",")+";\n"})),this.attributes=t},_parseDefines:function(){var t=this,r="vertex";function e(e,n,i){var a="vertex"===r?t.vertexDefines:t.fragmentDefines;return a[n]||(a[n]="false"!==i&&("true"===i||(i?isNaN(parseFloat(i))?i.trim():parseFloat(i):null))),""}this._vertexCode=this._vertexCode.replace(Kt,e),r="fragment",this._fragmentCode=this._fragmentCode.replace(Kt,e)},clone:function(){var t=ir[this._shaderID];return new sr(t.vertex,t.fragment)}},Object.defineProperty&&(Object.defineProperty(sr.prototype,"shaderID",{get:function(){return this._shaderID}}),Object.defineProperty(sr.prototype,"vertex",{get:function(){return this._vertexCode}}),Object.defineProperty(sr.prototype,"fragment",{get:function(){return this._fragmentCode}}),Object.defineProperty(sr.prototype,"uniforms",{get:function(){return this._uniformList}}));var ur=/(@import)\s*([0-9a-zA-Z_\-\.]*)/g;sr.parseImport=function(t){return t=t.replace(ur,(function(t,r,e){return(t=sr.source(e))?sr.parseImport(t):""}))};var hr=/(@export)\s*([0-9a-zA-Z_\-\.]*)\s*\n([\s\S]*?)@end/g;sr.import=function(t){t.replace(hr,(function(t,r,e,n){if(n=n.replace(/(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+\x24)/g,"")){for(var i,a=e.split("."),o=sr.codes,s=0;s<a.length-1;)o[i=a[s++]]||(o[i]={}),o=o[i];o[i=a[s]]=n}return n}))},sr.codes={},sr.source=function(t){for(var r=t.split("."),e=sr.codes,n=0;e&&n<r.length;){e=e[r[n++]]}return"string"!=typeof e?"":e};const cr="@export clay.prez.vertex\nuniform mat4 WVP : WORLDVIEWPROJECTION;\nattribute vec3 pos : POSITION;\nattribute vec2 uv : TEXCOORD_0;\nuniform vec2 uvRepeat : [1.0, 1.0];\nuniform vec2 uvOffset : [0.0, 0.0];\n@import clay.chunk.skinning_header\n@import clay.chunk.instancing_header\nvarying vec2 v_Texcoord;\nvoid main()\n{\n vec4 P = vec4(pos, 1.0);\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n P = skinMatrixWS * P;\n#endif\n#ifdef INSTANCING\n @import clay.chunk.instancing_matrix\n P = instanceMat * P;\n#endif\n gl_Position = WVP * P;\n v_Texcoord = uv * uvRepeat + uvOffset;\n}\n@end\n@export clay.prez.fragment\nuniform sampler2D alphaMap;\nuniform float alphaCutoff: 0.0;\nvarying vec2 v_Texcoord;\nvoid main()\n{\n if (alphaCutoff > 0.0) {\n if (texture2D(alphaMap, v_Texcoord).a <= alphaCutoff) {\n discard;\n }\n }\n gl_FragColor = vec4(0.0,0.0,0.0,1.0);\n}\n@end";var lr={create:function(){var t=new Ot(16);return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},clone:function(t){var r=new Ot(16);return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r[6]=t[6],r[7]=t[7],r[8]=t[8],r[9]=t[9],r[10]=t[10],r[11]=t[11],r[12]=t[12],r[13]=t[13],r[14]=t[14],r[15]=t[15],r},copy:function(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t[4]=r[4],t[5]=r[5],t[6]=r[6],t[7]=r[7],t[8]=r[8],t[9]=r[9],t[10]=r[10],t[11]=r[11],t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15],t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},transpose:function(t,r){if(t===r){var e=r[1],n=r[2],i=r[3],a=r[6],o=r[7],s=r[11];t[1]=r[4],t[2]=r[8],t[3]=r[12],t[4]=e,t[6]=r[9],t[7]=r[13],t[8]=n,t[9]=a,t[11]=r[14],t[12]=i,t[13]=o,t[14]=s}else t[0]=r[0],t[1]=r[4],t[2]=r[8],t[3]=r[12],t[4]=r[1],t[5]=r[5],t[6]=r[9],t[7]=r[13],t[8]=r[2],t[9]=r[6],t[10]=r[10],t[11]=r[14],t[12]=r[3],t[13]=r[7],t[14]=r[11],t[15]=r[15];return t},invert:function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=r[4],s=r[5],u=r[6],h=r[7],c=r[8],l=r[9],f=r[10],d=r[11],m=r[12],p=r[13],y=r[14],_=r[15],v=e*s-n*o,g=e*u-i*o,x=e*h-a*o,T=n*u-i*s,b=n*h-a*s,E=i*h-a*u,w=c*p-l*m,A=c*y-f*m,S=c*_-d*m,M=l*y-f*p,C=l*_-d*p,R=f*_-d*y,D=v*R-g*C+x*M+T*S-b*A+E*w;return D?(D=1/D,t[0]=(s*R-u*C+h*M)*D,t[1]=(i*C-n*R-a*M)*D,t[2]=(p*E-y*b+_*T)*D,t[3]=(f*b-l*E-d*T)*D,t[4]=(u*S-o*R-h*A)*D,t[5]=(e*R-i*S+a*A)*D,t[6]=(y*x-m*E-_*g)*D,t[7]=(c*E-f*x+d*g)*D,t[8]=(o*C-s*S+h*w)*D,t[9]=(n*S-e*C-a*w)*D,t[10]=(m*b-p*x+_*v)*D,t[11]=(l*x-c*b-d*v)*D,t[12]=(s*A-o*M-u*w)*D,t[13]=(e*M-n*A+i*w)*D,t[14]=(p*g-m*T-y*v)*D,t[15]=(c*T-l*g+f*v)*D,t):null},adjoint:function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=r[4],s=r[5],u=r[6],h=r[7],c=r[8],l=r[9],f=r[10],d=r[11],m=r[12],p=r[13],y=r[14],_=r[15];return t[0]=s*(f*_-d*y)-l*(u*_-h*y)+p*(u*d-h*f),t[1]=-(n*(f*_-d*y)-l*(i*_-a*y)+p*(i*d-a*f)),t[2]=n*(u*_-h*y)-s*(i*_-a*y)+p*(i*h-a*u),t[3]=-(n*(u*d-h*f)-s*(i*d-a*f)+l*(i*h-a*u)),t[4]=-(o*(f*_-d*y)-c*(u*_-h*y)+m*(u*d-h*f)),t[5]=e*(f*_-d*y)-c*(i*_-a*y)+m*(i*d-a*f),t[6]=-(e*(u*_-h*y)-o*(i*_-a*y)+m*(i*h-a*u)),t[7]=e*(u*d-h*f)-o*(i*d-a*f)+c*(i*h-a*u),t[8]=o*(l*_-d*p)-c*(s*_-h*p)+m*(s*d-h*l),t[9]=-(e*(l*_-d*p)-c*(n*_-a*p)+m*(n*d-a*l)),t[10]=e*(s*_-h*p)-o*(n*_-a*p)+m*(n*h-a*s),t[11]=-(e*(s*d-h*l)-o*(n*d-a*l)+c*(n*h-a*s)),t[12]=-(o*(l*y-f*p)-c*(s*y-u*p)+m*(s*f-u*l)),t[13]=e*(l*y-f*p)-c*(n*y-i*p)+m*(n*f-i*l),t[14]=-(e*(s*y-u*p)-o*(n*y-i*p)+m*(n*u-i*s)),t[15]=e*(s*f-u*l)-o*(n*f-i*l)+c*(n*u-i*s),t},determinant:function(t){var r=t[0],e=t[1],n=t[2],i=t[3],a=t[4],o=t[5],s=t[6],u=t[7],h=t[8],c=t[9],l=t[10],f=t[11],d=t[12],m=t[13],p=t[14],y=t[15];return(r*o-e*a)*(l*y-f*p)-(r*s-n*a)*(c*y-f*m)+(r*u-i*a)*(c*p-l*m)+(e*s-n*o)*(h*y-f*d)-(e*u-i*o)*(h*p-l*d)+(n*u-i*s)*(h*m-c*d)},multiply:function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],u=r[5],h=r[6],c=r[7],l=r[8],f=r[9],d=r[10],m=r[11],p=r[12],y=r[13],_=r[14],v=r[15],g=e[0],x=e[1],T=e[2],b=e[3];return t[0]=g*n+x*s+T*l+b*p,t[1]=g*i+x*u+T*f+b*y,t[2]=g*a+x*h+T*d+b*_,t[3]=g*o+x*c+T*m+b*v,g=e[4],x=e[5],T=e[6],b=e[7],t[4]=g*n+x*s+T*l+b*p,t[5]=g*i+x*u+T*f+b*y,t[6]=g*a+x*h+T*d+b*_,t[7]=g*o+x*c+T*m+b*v,g=e[8],x=e[9],T=e[10],b=e[11],t[8]=g*n+x*s+T*l+b*p,t[9]=g*i+x*u+T*f+b*y,t[10]=g*a+x*h+T*d+b*_,t[11]=g*o+x*c+T*m+b*v,g=e[12],x=e[13],T=e[14],b=e[15],t[12]=g*n+x*s+T*l+b*p,t[13]=g*i+x*u+T*f+b*y,t[14]=g*a+x*h+T*d+b*_,t[15]=g*o+x*c+T*m+b*v,t},multiplyAffine:function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[4],s=r[5],u=r[6],h=r[8],c=r[9],l=r[10],f=r[12],d=r[13],m=r[14],p=e[0],y=e[1],_=e[2];return t[0]=p*n+y*o+_*h,t[1]=p*i+y*s+_*c,t[2]=p*a+y*u+_*l,p=e[4],y=e[5],_=e[6],t[4]=p*n+y*o+_*h,t[5]=p*i+y*s+_*c,t[6]=p*a+y*u+_*l,p=e[8],y=e[9],_=e[10],t[8]=p*n+y*o+_*h,t[9]=p*i+y*s+_*c,t[10]=p*a+y*u+_*l,p=e[12],y=e[13],_=e[14],t[12]=p*n+y*o+_*h+f,t[13]=p*i+y*s+_*c+d,t[14]=p*a+y*u+_*l+m,t}};lr.mul=lr.multiply,lr.mulAffine=lr.multiplyAffine,lr.translate=function(t,r,e){var n,i,a,o,s,u,h,c,l,f,d,m,p=e[0],y=e[1],_=e[2];return r===t?(t[12]=r[0]*p+r[4]*y+r[8]*_+r[12],t[13]=r[1]*p+r[5]*y+r[9]*_+r[13],t[14]=r[2]*p+r[6]*y+r[10]*_+r[14],t[15]=r[3]*p+r[7]*y+r[11]*_+r[15]):(n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],u=r[5],h=r[6],c=r[7],l=r[8],f=r[9],d=r[10],m=r[11],t[0]=n,t[1]=i,t[2]=a,t[3]=o,t[4]=s,t[5]=u,t[6]=h,t[7]=c,t[8]=l,t[9]=f,t[10]=d,t[11]=m,t[12]=n*p+s*y+l*_+r[12],t[13]=i*p+u*y+f*_+r[13],t[14]=a*p+h*y+d*_+r[14],t[15]=o*p+c*y+m*_+r[15]),t},lr.scale=function(t,r,e){var n=e[0],i=e[1],a=e[2];return t[0]=r[0]*n,t[1]=r[1]*n,t[2]=r[2]*n,t[3]=r[3]*n,t[4]=r[4]*i,t[5]=r[5]*i,t[6]=r[6]*i,t[7]=r[7]*i,t[8]=r[8]*a,t[9]=r[9]*a,t[10]=r[10]*a,t[11]=r[11]*a,t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15],t},lr.rotate=function(t,r,e,n){var i,a,o,s,u,h,c,l,f,d,m,p,y,_,v,g,x,T,b,E,w,A,S,M,C=n[0],R=n[1],D=n[2],I=Math.sqrt(C*C+R*R+D*D);return Math.abs(I)<Lt?null:(C*=I=1/I,R*=I,D*=I,i=Math.sin(e),o=1-(a=Math.cos(e)),s=r[0],u=r[1],h=r[2],c=r[3],l=r[4],f=r[5],d=r[6],m=r[7],p=r[8],y=r[9],_=r[10],v=r[11],g=C*C*o+a,x=R*C*o+D*i,T=D*C*o-R*i,b=C*R*o-D*i,E=R*R*o+a,w=D*R*o+C*i,A=C*D*o+R*i,S=R*D*o-C*i,M=D*D*o+a,t[0]=s*g+l*x+p*T,t[1]=u*g+f*x+y*T,t[2]=h*g+d*x+_*T,t[3]=c*g+m*x+v*T,t[4]=s*b+l*E+p*w,t[5]=u*b+f*E+y*w,t[6]=h*b+d*E+_*w,t[7]=c*b+m*E+v*w,t[8]=s*A+l*S+p*M,t[9]=u*A+f*S+y*M,t[10]=h*A+d*S+_*M,t[11]=c*A+m*S+v*M,r!==t&&(t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15]),t)},lr.rotateX=function(t,r,e){var n=Math.sin(e),i=Math.cos(e),a=r[4],o=r[5],s=r[6],u=r[7],h=r[8],c=r[9],l=r[10],f=r[11];return r!==t&&(t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15]),t[4]=a*i+h*n,t[5]=o*i+c*n,t[6]=s*i+l*n,t[7]=u*i+f*n,t[8]=h*i-a*n,t[9]=c*i-o*n,t[10]=l*i-s*n,t[11]=f*i-u*n,t},lr.rotateY=function(t,r,e){var n=Math.sin(e),i=Math.cos(e),a=r[0],o=r[1],s=r[2],u=r[3],h=r[8],c=r[9],l=r[10],f=r[11];return r!==t&&(t[4]=r[4],t[5]=r[5],t[6]=r[6],t[7]=r[7],t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15]),t[0]=a*i-h*n,t[1]=o*i-c*n,t[2]=s*i-l*n,t[3]=u*i-f*n,t[8]=a*n+h*i,t[9]=o*n+c*i,t[10]=s*n+l*i,t[11]=u*n+f*i,t},lr.rotateZ=function(t,r,e){var n=Math.sin(e),i=Math.cos(e),a=r[0],o=r[1],s=r[2],u=r[3],h=r[4],c=r[5],l=r[6],f=r[7];return r!==t&&(t[8]=r[8],t[9]=r[9],t[10]=r[10],t[11]=r[11],t[12]=r[12],t[13]=r[13],t[14]=r[14],t[15]=r[15]),t[0]=a*i+h*n,t[1]=o*i+c*n,t[2]=s*i+l*n,t[3]=u*i+f*n,t[4]=h*i-a*n,t[5]=c*i-o*n,t[6]=l*i-s*n,t[7]=f*i-u*n,t},lr.fromRotationTranslation=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=n+n,u=i+i,h=a+a,c=n*s,l=n*u,f=n*h,d=i*u,m=i*h,p=a*h,y=o*s,_=o*u,v=o*h;return t[0]=1-(d+p),t[1]=l+v,t[2]=f-_,t[3]=0,t[4]=l-v,t[5]=1-(c+p),t[6]=m+y,t[7]=0,t[8]=f+_,t[9]=m-y,t[10]=1-(c+d),t[11]=0,t[12]=e[0],t[13]=e[1],t[14]=e[2],t[15]=1,t},lr.fromQuat=function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=e+e,s=n+n,u=i+i,h=e*o,c=n*o,l=n*s,f=i*o,d=i*s,m=i*u,p=a*o,y=a*s,_=a*u;return t[0]=1-l-m,t[1]=c+_,t[2]=f-y,t[3]=0,t[4]=c-_,t[5]=1-h-m,t[6]=d+p,t[7]=0,t[8]=f+y,t[9]=d-p,t[10]=1-h-l,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t},lr.frustum=function(t,r,e,n,i,a,o){var s=1/(e-r),u=1/(i-n),h=1/(a-o);return t[0]=2*a*s,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=2*a*u,t[6]=0,t[7]=0,t[8]=(e+r)*s,t[9]=(i+n)*u,t[10]=(o+a)*h,t[11]=-1,t[12]=0,t[13]=0,t[14]=o*a*2*h,t[15]=0,t},lr.perspective=function(t,r,e,n,i){var a=1/Math.tan(r/2),o=1/(n-i);return t[0]=a/e,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=a,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=(i+n)*o,t[11]=-1,t[12]=0,t[13]=0,t[14]=2*i*n*o,t[15]=0,t},lr.ortho=function(t,r,e,n,i,a,o){var s=1/(r-e),u=1/(n-i),h=1/(a-o);return t[0]=-2*s,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*u,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=2*h,t[11]=0,t[12]=(r+e)*s,t[13]=(i+n)*u,t[14]=(o+a)*h,t[15]=1,t},lr.lookAt=function(t,r,e,n){var i,a,o,s,u,h,c,l,f,d,m=r[0],p=r[1],y=r[2],_=n[0],v=n[1],g=n[2],x=e[0],T=e[1],b=e[2];return Math.abs(m-x)<Lt&&Math.abs(p-T)<Lt&&Math.abs(y-b)<Lt?lr.identity(t):(c=m-x,l=p-T,f=y-b,i=v*(f*=d=1/Math.sqrt(c*c+l*l+f*f))-g*(l*=d),a=g*(c*=d)-_*f,o=_*l-v*c,(d=Math.sqrt(i*i+a*a+o*o))?(i*=d=1/d,a*=d,o*=d):(i=0,a=0,o=0),s=l*o-f*a,u=f*i-c*o,h=c*a-l*i,(d=Math.sqrt(s*s+u*u+h*h))?(s*=d=1/d,u*=d,h*=d):(s=0,u=0,h=0),t[0]=i,t[1]=s,t[2]=c,t[3]=0,t[4]=a,t[5]=u,t[6]=l,t[7]=0,t[8]=o,t[9]=h,t[10]=f,t[11]=0,t[12]=-(i*m+a*p+o*y),t[13]=-(s*m+u*p+h*y),t[14]=-(c*m+l*p+f*y),t[15]=1,t)},lr.frob=function(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2)+Math.pow(t[2],2)+Math.pow(t[3],2)+Math.pow(t[4],2)+Math.pow(t[5],2)+Math.pow(t[6],2)+Math.pow(t[7],2)+Math.pow(t[8],2)+Math.pow(t[9],2)+Math.pow(t[10],2)+Math.pow(t[11],2)+Math.pow(t[12],2)+Math.pow(t[13],2)+Math.pow(t[14],2)+Math.pow(t[15],2))};var fr={create:function(){var t=new Ot(3);return t[0]=0,t[1]=0,t[2]=0,t},clone:function(t){var r=new Ot(3);return r[0]=t[0],r[1]=t[1],r[2]=t[2],r},fromValues:function(t,r,e){var n=new Ot(3);return n[0]=t,n[1]=r,n[2]=e,n},copy:function(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t},set:function(t,r,e,n){return t[0]=r,t[1]=e,t[2]=n,t},add:function(t,r,e){return t[0]=r[0]+e[0],t[1]=r[1]+e[1],t[2]=r[2]+e[2],t},subtract:function(t,r,e){return t[0]=r[0]-e[0],t[1]=r[1]-e[1],t[2]=r[2]-e[2],t}};fr.sub=fr.subtract,fr.multiply=function(t,r,e){return t[0]=r[0]*e[0],t[1]=r[1]*e[1],t[2]=r[2]*e[2],t},fr.mul=fr.multiply,fr.divide=function(t,r,e){return t[0]=r[0]/e[0],t[1]=r[1]/e[1],t[2]=r[2]/e[2],t},fr.div=fr.divide,fr.min=function(t,r,e){return t[0]=Math.min(r[0],e[0]),t[1]=Math.min(r[1],e[1]),t[2]=Math.min(r[2],e[2]),t},fr.max=function(t,r,e){return t[0]=Math.max(r[0],e[0]),t[1]=Math.max(r[1],e[1]),t[2]=Math.max(r[2],e[2]),t},fr.scale=function(t,r,e){return t[0]=r[0]*e,t[1]=r[1]*e,t[2]=r[2]*e,t},fr.scaleAndAdd=function(t,r,e,n){return t[0]=r[0]+e[0]*n,t[1]=r[1]+e[1]*n,t[2]=r[2]+e[2]*n,t},fr.distance=function(t,r){var e=r[0]-t[0],n=r[1]-t[1],i=r[2]-t[2];return Math.sqrt(e*e+n*n+i*i)},fr.dist=fr.distance,fr.squaredDistance=function(t,r){var e=r[0]-t[0],n=r[1]-t[1],i=r[2]-t[2];return e*e+n*n+i*i},fr.sqrDist=fr.squaredDistance,fr.length=function(t){var r=t[0],e=t[1],n=t[2];return Math.sqrt(r*r+e*e+n*n)},fr.len=fr.length,fr.squaredLength=function(t){var r=t[0],e=t[1],n=t[2];return r*r+e*e+n*n},fr.sqrLen=fr.squaredLength,fr.negate=function(t,r){return t[0]=-r[0],t[1]=-r[1],t[2]=-r[2],t},fr.inverse=function(t,r){return t[0]=1/r[0],t[1]=1/r[1],t[2]=1/r[2],t},fr.normalize=function(t,r){var e=r[0],n=r[1],i=r[2],a=e*e+n*n+i*i;return a>0&&(a=1/Math.sqrt(a),t[0]=r[0]*a,t[1]=r[1]*a,t[2]=r[2]*a),t},fr.dot=function(t,r){return t[0]*r[0]+t[1]*r[1]+t[2]*r[2]},fr.cross=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=e[0],s=e[1],u=e[2];return t[0]=i*u-a*s,t[1]=a*o-n*u,t[2]=n*s-i*o,t},fr.lerp=function(t,r,e,n){var i=r[0],a=r[1],o=r[2];return t[0]=i+n*(e[0]-i),t[1]=a+n*(e[1]-a),t[2]=o+n*(e[2]-o),t},fr.random=function(t,r){r=r||1;var e=2*Pt()*Math.PI,n=2*Pt()-1,i=Math.sqrt(1-n*n)*r;return t[0]=Math.cos(e)*i,t[1]=Math.sin(e)*i,t[2]=n*r,t},fr.transformMat4=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=e[3]*n+e[7]*i+e[11]*a+e[15];return o=o||1,t[0]=(e[0]*n+e[4]*i+e[8]*a+e[12])/o,t[1]=(e[1]*n+e[5]*i+e[9]*a+e[13])/o,t[2]=(e[2]*n+e[6]*i+e[10]*a+e[14])/o,t},fr.transformMat3=function(t,r,e){var n=r[0],i=r[1],a=r[2];return t[0]=n*e[0]+i*e[3]+a*e[6],t[1]=n*e[1]+i*e[4]+a*e[7],t[2]=n*e[2]+i*e[5]+a*e[8],t},fr.transformQuat=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=e[0],s=e[1],u=e[2],h=e[3],c=h*n+s*a-u*i,l=h*i+u*n-o*a,f=h*a+o*i-s*n,d=-o*n-s*i-u*a;return t[0]=c*h+d*-o+l*-u-f*-s,t[1]=l*h+d*-s+f*-o-c*-u,t[2]=f*h+d*-u+c*-s-l*-o,t},fr.rotateX=function(t,r,e,n){var i=[],a=[];return i[0]=r[0]-e[0],i[1]=r[1]-e[1],i[2]=r[2]-e[2],a[0]=i[0],a[1]=i[1]*Math.cos(n)-i[2]*Math.sin(n),a[2]=i[1]*Math.sin(n)+i[2]*Math.cos(n),t[0]=a[0]+e[0],t[1]=a[1]+e[1],t[2]=a[2]+e[2],t},fr.rotateY=function(t,r,e,n){var i=[],a=[];return i[0]=r[0]-e[0],i[1]=r[1]-e[1],i[2]=r[2]-e[2],a[0]=i[2]*Math.sin(n)+i[0]*Math.cos(n),a[1]=i[1],a[2]=i[2]*Math.cos(n)-i[0]*Math.sin(n),t[0]=a[0]+e[0],t[1]=a[1]+e[1],t[2]=a[2]+e[2],t},fr.rotateZ=function(t,r,e,n){var i=[],a=[];return i[0]=r[0]-e[0],i[1]=r[1]-e[1],i[2]=r[2]-e[2],a[0]=i[0]*Math.cos(n)-i[1]*Math.sin(n),a[1]=i[0]*Math.sin(n)+i[1]*Math.cos(n),a[2]=i[2],t[0]=a[0]+e[0],t[1]=a[1]+e[1],t[2]=a[2]+e[2],t},fr.forEach=function(){var t=fr.create();return function(r,e,n,i,a,o){var s,u;for(e||(e=3),n||(n=0),u=i?Math.min(i*e+n,r.length):r.length,s=n;s<u;s+=e)t[0]=r[s],t[1]=r[s+1],t[2]=r[s+2],a(t,t,o),r[s]=t[0],r[s+1]=t[1],r[s+2]=t[2];return r}}(),fr.angle=function(t,r){var e=fr.fromValues(t[0],t[1],t[2]),n=fr.fromValues(r[0],r[1],r[2]);fr.normalize(e,e),fr.normalize(n,n);var i=fr.dot(e,n);return i>1?0:Math.acos(i)},sr.import(cr);var dr=lr.create,mr={};function pr(t){return t.material}function yr(t,r,e){return r.uniforms[e].value}function _r(t,r,e,n){return e!==n}function vr(t){return!0}function gr(){}var xr={float:B,byte:D,ubyte:I,short:L,ushort:O};function Tr(t,r,e){this.availableAttributes=t,this.availableAttributeSymbols=r,this.indicesBuffer=e,this.vao=null}function br(t){var r,e;this.bind=function(t){r||((r=ht.createCanvas()).width=r.height=1,r.getContext("2d"));var n=t.gl,i=!e;i&&(e=n.createTexture()),n.bindTexture(n.TEXTURE_2D,e),i&&n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,r)},this.unbind=function(t){t.gl.bindTexture(t.gl.TEXTURE_2D,null)},this.isRenderable=function(){return!0}}var Er=u.extend((function(){return{canvas:null,_width:100,_height:100,devicePixelRatio:"undefined"!=typeof window&&window.devicePixelRatio||1,clearColor:[0,0,0,0],clearBit:17664,alpha:!0,depth:!0,stencil:!1,antialias:!0,premultipliedAlpha:!0,preserveDrawingBuffer:!1,throwError:!0,gl:null,viewport:{},maxJointNumber:20,__currentFrameBuffer:null,_viewportStack:[],_clearStack:[],_sceneRendering:null}}),(function(){this.canvas||(this.canvas=ht.createCanvas());var t=this.canvas;try{var r={alpha:this.alpha,depth:this.depth,stencil:this.stencil,antialias:this.antialias,premultipliedAlpha:this.premultipliedAlpha,preserveDrawingBuffer:this.preserveDrawingBuffer};if(this.gl=t.getContext("webgl",r)||t.getContext("experimental-webgl",r),!this.gl)throw new Error;this._glinfo=new l(this.gl),this.gl.targetRenderer,this.gl.targetRenderer=this,this.resize()}catch(e){throw"Error creating WebGL Context "+e}this._programMgr=new jt(this),this._placeholderTexture=new br}),{resize:function(t,r){var e=this.canvas,n=this.devicePixelRatio;null!=t?(e.style&&(e.style.width=t+"px",e.style.height=r+"px"),e.width=t*n,e.height=r*n,this._width=t,this._height=r):(this._width=e.width/n,this._height=e.height/n),this.setViewport(0,0,this._width,this._height)},getWidth:function(){return this._width},getHeight:function(){return this._height},getViewportAspect:function(){var t=this.viewport;return t.width/t.height},setDevicePixelRatio:function(t){this.devicePixelRatio=t,this.resize(this._width,this._height)},getDevicePixelRatio:function(){return this.devicePixelRatio},getGLExtension:function(t){return this._glinfo.getExtension(t)},getGLParameter:function(t){return this._glinfo.getParameter(t)},setViewport:function(t,r,e,n,i){if("object"==typeof t){var a=t;t=a.x,r=a.y,e=a.width,n=a.height,i=a.devicePixelRatio}i=i||this.devicePixelRatio,this.gl.viewport(t*i,r*i,e*i,n*i),this.viewport={x:t,y:r,width:e,height:n,devicePixelRatio:i}},saveViewport:function(){this._viewportStack.push(this.viewport)},restoreViewport:function(){this._viewportStack.length>0&&this.setViewport(this._viewportStack.pop())},saveClear:function(){this._clearStack.push({clearBit:this.clearBit,clearColor:this.clearColor})},restoreClear:function(){if(this._clearStack.length>0){var t=this._clearStack.pop();this.clearColor=t.clearColor,this.clearBit=t.clearBit}},bindSceneRendering:function(t){this._sceneRendering=t},render:function(t,r,e,n){var i=this.gl,a=this.clearColor;if(this.clearBit){i.colorMask(!0,!0,!0,!0),i.depthMask(!0);var o=this.viewport,s=!1,u=o.devicePixelRatio;(o.width!==this._width||o.height!==this._height||u&&u!==this.devicePixelRatio||o.x||o.y)&&(s=!0,i.enable(i.SCISSOR_TEST),i.scissor(o.x*u,o.y*u,o.width*u,o.height*u)),i.clearColor(a[0],a[1],a[2],a[3]),i.clear(this.clearBit),s&&i.disable(i.SCISSOR_TEST)}if(e||t.update(!1),t.updateLights(),r=r||t.getMainCamera()){r.update();var h=t.updateRenderList(r,!0);this._sceneRendering=t;var c=h.opaque,l=h.transparent,f=t.material;t.trigger("beforerender",this,t,r,h),n?(this.renderPreZ(c,t,r),i.depthFunc(i.LEQUAL)):i.depthFunc(i.LESS);for(var d=dr(),m=fr.create(),p=0;p<l.length;p++){var y=l[p];lr.multiplyAffine(d,r.viewMatrix.array,y.worldTransform.array),fr.transformMat4(m,y.position.array,d),y.__depth=m[2]}this.renderPass(c,r,{getMaterial:function(t){return f||t.material},sortCompare:this.opaqueSortCompare}),this.renderPass(l,r,{getMaterial:function(t){return f||t.material},sortCompare:this.transparentSortCompare}),t.trigger("afterrender",this,t,r,h),this._sceneRendering=null}},getProgram:function(t,r,e){return r=r||t.material,this._programMgr.getProgram(t,r,e)},validateProgram:function(t){if(t.__error){var r=t.__error;if(mr[t.__uid__])return;if(mr[t.__uid__]=!0,this.throwError)throw new Error(r);this.trigger("error",r)}},updatePrograms:function(t,r,e){var n=e&&e.getMaterial||pr;r=r||null;for(var i=0;i<t.length;i++){var a=t[i],o=n.call(this,a);if(i>0){var s=t[i-1],u=s.joints?s.joints.length:0;if((a.joints?a.joints.length:0)===u&&a.material===s.material&&a.lightGroup===s.lightGroup){a.__program=s.__program;continue}}var h=this._programMgr.getProgram(a,o,r);this.validateProgram(h),a.__program=h}},renderPass:function(t,r,e){this.trigger("beforerenderpass",this,t,r,e),(e=e||{}).getMaterial=e.getMaterial||pr,e.getUniform=e.getUniform||yr,e.isMaterialChanged=e.isMaterialChanged||_r,e.beforeRender=e.beforeRender||gr,e.afterRender=e.afterRender||gr;var n=e.ifRender||vr;this.updatePrograms(t,this._sceneRendering,e),e.sortCompare&&t.sort(e.sortCompare);var i=this.viewport,a=i.devicePixelRatio,o=[i.x*a,i.y*a,i.width*a,i.height*a],s=this.devicePixelRatio,u=this.__currentFrameBuffer?[this.__currentFrameBuffer.getTextureWidth(),this.__currentFrameBuffer.getTextureHeight()]:[this._width*s,this._height*s],h=[o[2],o[3]],c=Date.now();r?(lr.copy(wr.VIEW,r.viewMatrix.array),lr.copy(wr.PROJECTION,r.projectionMatrix.array),lr.copy(wr.VIEWINVERSE,r.worldTransform.array)):(lr.identity(wr.VIEW),lr.identity(wr.PROJECTION),lr.identity(wr.VIEWINVERSE)),lr.multiply(wr.VIEWPROJECTION,wr.PROJECTION,wr.VIEW),lr.invert(wr.PROJECTIONINVERSE,wr.PROJECTION),lr.invert(wr.VIEWPROJECTIONINVERSE,wr.VIEWPROJECTION);for(var l,f,d,m,p,y,_,v,g,x,T,b,E=this.gl,w=this._sceneRendering,A=0;A<t.length;A++){var S,M=t[A],C=null!=M.worldTransform;if(n(M)){C&&(S=M.isSkinnedMesh&&M.isSkinnedMesh()?M.offsetMatrix?M.offsetMatrix.array:wr.IDENTITY:M.worldTransform.array);var R=M.geometry,D=e.getMaterial.call(this,M),I=M.__program,L=D.shader,O=R.__uid__+"-"+I.__uid__,P=O!==x;x=O,C&&(lr.copy(wr.WORLD,S),lr.multiply(wr.WORLDVIEWPROJECTION,wr.VIEWPROJECTION,S),lr.multiplyAffine(wr.WORLDVIEW,wr.VIEW,S),(L.matrixSemantics.WORLDINVERSE||L.matrixSemantics.WORLDINVERSETRANSPOSE)&&lr.invert(wr.WORLDINVERSE,S),(L.matrixSemantics.WORLDVIEWINVERSE||L.matrixSemantics.WORLDVIEWINVERSETRANSPOSE)&&lr.invert(wr.WORLDVIEWINVERSE,wr.WORLDVIEW),(L.matrixSemantics.WORLDVIEWPROJECTIONINVERSE||L.matrixSemantics.WORLDVIEWPROJECTIONINVERSETRANSPOSE)&&lr.invert(wr.WORLDVIEWPROJECTIONINVERSE,wr.WORLDVIEWPROJECTION)),M.beforeRender&&M.beforeRender(this),e.beforeRender.call(this,M,D,l);var N=I!==f;N?(I.bind(this),I.setUniformOfSemantic(E,"VIEWPORT",o),I.setUniformOfSemantic(E,"WINDOW_SIZE",u),r&&(I.setUniformOfSemantic(E,"NEAR",r.near),I.setUniformOfSemantic(E,"FAR",r.far)),I.setUniformOfSemantic(E,"DEVICEPIXELRATIO",a),I.setUniformOfSemantic(E,"TIME",c),I.setUniformOfSemantic(E,"VIEWPORT_SIZE",h),w&&w.setLightUniforms(I,M.lightGroup,this)):I=f,(N||e.isMaterialChanged(M,d,D,l))&&(D.depthTest!==m&&(D.depthTest?E.enable(E.DEPTH_TEST):E.disable(E.DEPTH_TEST),m=D.depthTest),D.depthMask!==p&&(E.depthMask(D.depthMask),p=D.depthMask),D.transparent!==g&&(D.transparent?E.enable(E.BLEND):E.disable(E.BLEND),g=D.transparent),D.transparent&&(D.blend?D.blend(E):(E.blendEquationSeparate(E.FUNC_ADD,E.FUNC_ADD),E.blendFuncSeparate(E.SRC_ALPHA,E.ONE_MINUS_SRC_ALPHA,E.ONE,E.ONE_MINUS_SRC_ALPHA))),b=this._bindMaterial(M,D,I,d||null,l||null,f||null,e.getUniform),l=D);var B=L.matrixSemanticKeys;if(C)for(var U=0;U<B.length;U++){var F=B[U],k=L.matrixSemantics[F],H=wr[F];if(k.isTranspose){var V=wr[k.semanticNoTranspose];lr.transpose(H,V)}I.setUniform(E,k.type,k.symbol,H)}M.cullFace!==_&&(_=M.cullFace,E.cullFace(_)),M.frontFace!==v&&(v=M.frontFace,E.frontFace(v)),M.culling!==y&&((y=M.culling)?E.enable(E.CULL_FACE):E.disable(E.CULL_FACE)),this._updateSkeleton(M,I,b),P&&(T=this._bindVAO(null,L,R,I)),this._renderObject(M,T,I),e.afterRender(this,M),M.afterRender&&M.afterRender(this),f=I,d=M}}this.trigger("afterrenderpass",this,t,r,e)},getMaxJointNumber:function(){return this.maxJointNumber},_updateSkeleton:function(t,r,e){var n=this.gl,i=t.skeleton;if(i)if(i.update(),t.joints.length>this.getMaxJointNumber()){var a=i.getSubSkinMatricesTexture(t.__uid__,t.joints);r.useTextureSlot(this,a,e),r.setUniform(n,"1i","skinMatricesTexture",e),r.setUniform(n,"1f","skinMatricesTextureSize",a.width)}else{var o=i.getSubSkinMatrices(t.__uid__,t.joints);r.setUniformOfSemantic(n,"SKIN_MATRIX",o)}},_renderObject:function(t,r,e){var n=this.gl,i=t.geometry,a=t.mode;null==a&&(a=4);var o=null,s=t.isInstancedMesh&&t.isInstancedMesh();if(!s||(o=this.getGLExtension("ANGLE_instanced_arrays"))){var u;if(s&&(u=this._bindInstancedAttributes(t,e,o)),r.indicesBuffer){var h=this.getGLExtension("OES_element_index_uint")&&i.indices instanceof Uint32Array?n.UNSIGNED_INT:n.UNSIGNED_SHORT;s?o.drawElementsInstancedANGLE(a,r.indicesBuffer.count,h,0,t.getInstanceCount()):n.drawElements(a,r.indicesBuffer.count,h,0)}else s?o.drawArraysInstancedANGLE(a,0,i.vertexCount,t.getInstanceCount()):n.drawArrays(a,0,i.vertexCount);if(s)for(var c=0;c<u.length;c++)n.disableVertexAttribArray(u[c])}},_bindInstancedAttributes:function(t,r,e){for(var n=this.gl,i=t.getInstancedAttributesBuffers(this),a=[],o=0;o<i.length;o++){var s=i[o],u=r.getAttribLocation(n,s.symbol);if(!(u<0)){var h=xr[s.type]||n.FLOAT;n.enableVertexAttribArray(u),n.bindBuffer(n.ARRAY_BUFFER,s.buffer),n.vertexAttribPointer(u,s.size,h,!1,0,0),e.vertexAttribDivisorANGLE(u,s.divisor),a.push(u)}}return a},_bindMaterial:function(t,r,e,n,i,a,o){for(var s=this.gl,u=a===e,h=e.currentTextureSlot(),c=r.getEnabledUniforms(),l=r.getTextureUniforms(),f=this._placeholderTexture,d=0;d<l.length;d++){var m=o(t,r,_=l[d]);if("t"===(y=r.uniforms[_].type)&&m)m.__slot=-1;else if("tv"===y)for(var p=0;p<m.length;p++)m[p]&&(m[p].__slot=-1)}f.__slot=-1;for(d=0;d<c.length;d++){var y,_=c[d],v=r.uniforms[_],g=(m=o(t,r,_),"t"===(y=v.type));if(g&&(m&&m.isRenderable()||(m=f)),i&&u){var x=o(n,i,_);if(g&&(x&&x.isRenderable()||(x=f)),x===m){if(g)e.takeCurrentTextureSlot(this,null);else if("tv"===y&&m)for(p=0;p<m.length;p++)e.takeCurrentTextureSlot(this,null);continue}}if(null!=m)if(g)if(m.__slot<0){var T=e.currentTextureSlot();e.setUniform(s,"1i",_,T)&&(e.takeCurrentTextureSlot(this,m),m.__slot=T)}else e.setUniform(s,"1i",_,m.__slot);else if(Array.isArray(m)){if(0===m.length)continue;if("tv"===y){if(!e.hasUniform(_))continue;var b=[];for(p=0;p<m.length;p++){var E=m[p];if(E.__slot<0){T=e.currentTextureSlot();b.push(T),e.takeCurrentTextureSlot(this,E),E.__slot=T}else b.push(E.__slot)}e.setUniform(s,"1iv",_,b)}else e.setUniform(s,v.type,_,m)}else e.setUniform(s,v.type,_,m)}var w=e.currentTextureSlot();return e.resetTextureSlot(h),w},_bindVAO:function(t,r,e,n){var i=!e.dynamic,a=this.gl,o=this.__uid__+"-"+n.__uid__,s=e.__vaoCache[o];if(!s){var u=e.getBufferChunks(this);if(!u||!u.length)return;for(var h=u[0],c=h.attributeBuffers,l=h.indicesBuffer,f=[],d=[],m=0;m<c.length;m++){var p,y=(b=c[m]).name,_=b.semantic;if(_){var v=r.attributeSemantics[_];p=v&&v.symbol}else p=y;p&&n.attributes[p]&&(f.push(b),d.push(p))}s=new Tr(f,d,l),i&&(e.__vaoCache[o]=s)}var g=!0;t&&i&&(null==s.vao?s.vao=t.createVertexArrayOES():g=!1,t.bindVertexArrayOES(s.vao));f=s.availableAttributes,l=s.indicesBuffer;if(g){var x=n.enableAttributes(this,s.availableAttributeSymbols,t&&i&&s);for(m=0;m<f.length;m++){var T=x[m];if(-1!==T){var b,E=(b=f[m]).buffer,w=b.size,A=xr[b.type]||a.FLOAT;a.bindBuffer(a.ARRAY_BUFFER,E),a.vertexAttribPointer(T,w,A,!1,0,0)}}e.isUseIndices()&&a.bindBuffer(a.ELEMENT_ARRAY_BUFFER,l.buffer)}return s},renderPreZ:function(t,r,e){var n=this.gl,i=this._prezMaterial||new It({shader:new sr(sr.source("clay.prez.vertex"),sr.source("clay.prez.fragment"))});this._prezMaterial=i,n.colorMask(!1,!1,!1,!1),n.depthMask(!0),this.renderPass(t,e,{ifRender:function(t){return!t.ignorePreZ},isMaterialChanged:function(t,r){var e=t.material,n=r.material;return e.get("diffuseMap")!==n.get("diffuseMap")||(e.get("alphaCutoff")||0)!==(n.get("alphaCutoff")||0)},getUniform:function(t,r,e){return"alphaMap"===e?t.material.get("diffuseMap"):"alphaCutoff"===e?t.material.isDefined("fragment","ALPHA_TEST")&&t.material.get("diffuseMap")&&t.material.get("alphaCutoff")||0:"uvRepeat"===e?t.material.get("uvRepeat"):"uvOffset"===e?t.material.get("uvOffset"):r.get(e)},getMaterial:function(){return i},sort:this.opaqueSortCompare}),n.colorMask(!0,!0,!0,!0),n.depthMask(!0)},disposeScene:function(t){this.disposeNode(t,!0,!0),t.dispose()},disposeNode:function(t,r,e){t.getParent()&&t.getParent().remove(t);var n={};t.traverse((function(t){var i=t.material;if(t.geometry&&r&&t.geometry.dispose(this),e&&i&&!n[i.__uid__]){for(var a=i.getTextureUniforms(),o=0;o<a.length;o++){var s=a[o],u=i.uniforms[s].value,h=i.uniforms[s].type;if(u)if("t"===h)u.dispose&&u.dispose(this);else if("tv"===h)for(var c=0;c<u.length;c++)u[c]&&u[c].dispose&&u[c].dispose(this)}n[i.__uid__]=!0}t.dispose&&t.dispose(this)}),this)},disposeGeometry:function(t){t.dispose(this)},disposeTexture:function(t){t.dispose(this)},disposeFrameBuffer:function(t){t.dispose(this)},dispose:function(){},screenToNDC:function(t,r,e){e||(e=new Bt),r=this._height-r;var n=this.viewport,i=e.array;return i[0]=(t-n.x)/n.width,i[0]=2*i[0]-1,i[1]=(r-n.y)/n.height,i[1]=2*i[1]-1,e}});Er.opaqueSortCompare=Er.prototype.opaqueSortCompare=function(t,r){return t.renderOrder===r.renderOrder?t.__program===r.__program?t.material===r.material?t.geometry.__uid__-r.geometry.__uid__:t.material.__uid__-r.material.__uid__:t.__program&&r.__program?t.__program.__uid__-r.__program.__uid__:0:t.renderOrder-r.renderOrder},Er.transparentSortCompare=Er.prototype.transparentSortCompare=function(t,r){return t.renderOrder===r.renderOrder?t.__depth===r.__depth?t.__program===r.__program?t.material===r.material?t.geometry.__uid__-r.geometry.__uid__:t.material.__uid__-r.material.__uid__:t.__program&&r.__program?t.__program.__uid__-r.__program.__uid__:0:t.__depth-r.__depth:t.renderOrder-r.renderOrder};var wr={IDENTITY:dr(),WORLD:dr(),VIEW:dr(),PROJECTION:dr(),WORLDVIEW:dr(),VIEWPROJECTION:dr(),WORLDVIEWPROJECTION:dr(),WORLDINVERSE:dr(),VIEWINVERSE:dr(),PROJECTIONINVERSE:dr(),WORLDVIEWINVERSE:dr(),VIEWPROJECTIONINVERSE:dr(),WORLDVIEWPROJECTIONINVERSE:dr(),WORLDTRANSPOSE:dr(),VIEWTRANSPOSE:dr(),PROJECTIONTRANSPOSE:dr(),WORLDVIEWTRANSPOSE:dr(),VIEWPROJECTIONTRANSPOSE:dr(),WORLDVIEWPROJECTIONTRANSPOSE:dr(),WORLDINVERSETRANSPOSE:dr(),VIEWINVERSETRANSPOSE:dr(),PROJECTIONINVERSETRANSPOSE:dr(),WORLDVIEWINVERSETRANSPOSE:dr(),VIEWPROJECTIONINVERSETRANSPOSE:dr(),WORLDVIEWPROJECTIONINVERSETRANSPOSE:dr()};Er.COLOR_BUFFER_BIT=m,Er.DEPTH_BUFFER_BIT=f,Er.STENCIL_BUFFER_BIT=d;var Ar=function(t,r,e){t=t||0,r=r||0,e=e||0,this.array=fr.fromValues(t,r,e),this._dirty=!0};Ar.prototype={constructor:Ar,add:function(t){return fr.add(this.array,this.array,t.array),this._dirty=!0,this},set:function(t,r,e){return this.array[0]=t,this.array[1]=r,this.array[2]=e,this._dirty=!0,this},setArray:function(t){return this.array[0]=t[0],this.array[1]=t[1],this.array[2]=t[2],this._dirty=!0,this},clone:function(){return new Ar(this.x,this.y,this.z)},copy:function(t){return fr.copy(this.array,t.array),this._dirty=!0,this},cross:function(t,r){return fr.cross(this.array,t.array,r.array),this._dirty=!0,this},dist:function(t){return fr.dist(this.array,t.array)},distance:function(t){return fr.distance(this.array,t.array)},div:function(t){return fr.div(this.array,this.array,t.array),this._dirty=!0,this},divide:function(t){return fr.divide(this.array,this.array,t.array),this._dirty=!0,this},dot:function(t){return fr.dot(this.array,t.array)},len:function(){return fr.len(this.array)},length:function(){return fr.length(this.array)},lerp:function(t,r,e){return fr.lerp(this.array,t.array,r.array,e),this._dirty=!0,this},min:function(t){return fr.min(this.array,this.array,t.array),this._dirty=!0,this},max:function(t){return fr.max(this.array,this.array,t.array),this._dirty=!0,this},mul:function(t){return fr.mul(this.array,this.array,t.array),this._dirty=!0,this},multiply:function(t){return fr.multiply(this.array,this.array,t.array),this._dirty=!0,this},negate:function(){return fr.negate(this.array,this.array),this._dirty=!0,this},normalize:function(){return fr.normalize(this.array,this.array),this._dirty=!0,this},random:function(t){return fr.random(this.array,t),this._dirty=!0,this},scale:function(t){return fr.scale(this.array,this.array,t),this._dirty=!0,this},scaleAndAdd:function(t,r){return fr.scaleAndAdd(this.array,this.array,t.array,r),this._dirty=!0,this},sqrDist:function(t){return fr.sqrDist(this.array,t.array)},squaredDistance:function(t){return fr.squaredDistance(this.array,t.array)},sqrLen:function(){return fr.sqrLen(this.array)},squaredLength:function(){return fr.squaredLength(this.array)},sub:function(t){return fr.sub(this.array,this.array,t.array),this._dirty=!0,this},subtract:function(t){return fr.subtract(this.array,this.array,t.array),this._dirty=!0,this},transformMat3:function(t){return fr.transformMat3(this.array,this.array,t.array),this._dirty=!0,this},transformMat4:function(t){return fr.transformMat4(this.array,this.array,t.array),this._dirty=!0,this},transformQuat:function(t){return fr.transformQuat(this.array,this.array,t.array),this._dirty=!0,this},applyProjection:function(t){var r=this.array;if(0===(t=t.array)[15]){var e=-1/r[2];r[0]=t[0]*r[0]*e,r[1]=t[5]*r[1]*e,r[2]=(t[10]*r[2]+t[14])*e}else r[0]=t[0]*r[0]+t[12],r[1]=t[5]*r[1]+t[13],r[2]=t[10]*r[2]+t[14];return this._dirty=!0,this},eulerFromQuat:function(t,r){Ar.eulerFromQuat(this,t,r)},eulerFromMat3:function(t,r){Ar.eulerFromMat3(this,t,r)},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}};var Sr=Object.defineProperty;if(Sr){var Mr=Ar.prototype;Sr(Mr,"x",{get:function(){return this.array[0]},set:function(t){this.array[0]=t,this._dirty=!0}}),Sr(Mr,"y",{get:function(){return this.array[1]},set:function(t){this.array[1]=t,this._dirty=!0}}),Sr(Mr,"z",{get:function(){return this.array[2]},set:function(t){this.array[2]=t,this._dirty=!0}})}function Cr(t,r,e){return t<r?r:t>e?e:t}Ar.add=function(t,r,e){return fr.add(t.array,r.array,e.array),t._dirty=!0,t},Ar.set=function(t,r,e,n){fr.set(t.array,r,e,n),t._dirty=!0},Ar.copy=function(t,r){return fr.copy(t.array,r.array),t._dirty=!0,t},Ar.cross=function(t,r,e){return fr.cross(t.array,r.array,e.array),t._dirty=!0,t},Ar.distance=Ar.dist=function(t,r){return fr.distance(t.array,r.array)},Ar.divide=Ar.div=function(t,r,e){return fr.divide(t.array,r.array,e.array),t._dirty=!0,t},Ar.dot=function(t,r){return fr.dot(t.array,r.array)},Ar.len=function(t){return fr.length(t.array)},Ar.lerp=function(t,r,e,n){return fr.lerp(t.array,r.array,e.array,n),t._dirty=!0,t},Ar.min=function(t,r,e){return fr.min(t.array,r.array,e.array),t._dirty=!0,t},Ar.max=function(t,r,e){return fr.max(t.array,r.array,e.array),t._dirty=!0,t},Ar.multiply=Ar.mul=function(t,r,e){return fr.multiply(t.array,r.array,e.array),t._dirty=!0,t},Ar.negate=function(t,r){return fr.negate(t.array,r.array),t._dirty=!0,t},Ar.normalize=function(t,r){return fr.normalize(t.array,r.array),t._dirty=!0,t},Ar.random=function(t,r){return fr.random(t.array,r),t._dirty=!0,t},Ar.scale=function(t,r,e){return fr.scale(t.array,r.array,e),t._dirty=!0,t},Ar.scaleAndAdd=function(t,r,e,n){return fr.scaleAndAdd(t.array,r.array,e.array,n),t._dirty=!0,t},Ar.squaredDistance=Ar.sqrDist=function(t,r){return fr.sqrDist(t.array,r.array)},Ar.squaredLength=Ar.sqrLen=function(t){return fr.sqrLen(t.array)},Ar.subtract=Ar.sub=function(t,r,e){return fr.subtract(t.array,r.array,e.array),t._dirty=!0,t},Ar.transformMat3=function(t,r,e){return fr.transformMat3(t.array,r.array,e.array),t._dirty=!0,t},Ar.transformMat4=function(t,r,e){return fr.transformMat4(t.array,r.array,e.array),t._dirty=!0,t},Ar.transformQuat=function(t,r,e){return fr.transformQuat(t.array,r.array,e.array),t._dirty=!0,t};var Rr=Math.atan2,Dr=Math.asin,Ir=Math.abs;Ar.eulerFromQuat=function(t,r,e){t._dirty=!0,r=r.array;var n=t.array,i=r[0],a=r[1],o=r[2],s=r[3],u=i*i,h=a*a,c=o*o,l=s*s;switch(e=(e||"XYZ").toUpperCase()){case"XYZ":n[0]=Rr(2*(i*s-a*o),l-u-h+c),n[1]=Dr(Cr(2*(i*o+a*s),-1,1)),n[2]=Rr(2*(o*s-i*a),l+u-h-c);break;case"YXZ":n[0]=Dr(Cr(2*(i*s-a*o),-1,1)),n[1]=Rr(2*(i*o+a*s),l-u-h+c),n[2]=Rr(2*(i*a+o*s),l-u+h-c);break;case"ZXY":n[0]=Dr(Cr(2*(i*s+a*o),-1,1)),n[1]=Rr(2*(a*s-o*i),l-u-h+c),n[2]=Rr(2*(o*s-i*a),l-u+h-c);break;case"ZYX":n[0]=Rr(2*(i*s+o*a),l-u-h+c),n[1]=Dr(Cr(2*(a*s-i*o),-1,1)),n[2]=Rr(2*(i*a+o*s),l+u-h-c);break;case"YZX":n[0]=Rr(2*(i*s-o*a),l-u+h-c),n[1]=Rr(2*(a*s-i*o),l+u-h-c),n[2]=Dr(Cr(2*(i*a+o*s),-1,1));break;case"XZY":n[0]=Rr(2*(i*s+a*o),l-u+h-c),n[1]=Rr(2*(i*o+a*s),l+u-h-c),n[2]=Dr(Cr(2*(o*s-i*a),-1,1))}return t},Ar.eulerFromMat3=function(t,r,e){var n=r.array,i=n[0],a=n[3],o=n[6],s=n[1],u=n[4],h=n[7],c=n[2],l=n[5],f=n[8],d=t.array;switch(e=(e||"XYZ").toUpperCase()){case"XYZ":d[1]=Dr(Cr(o,-1,1)),Ir(o)<.99999?(d[0]=Rr(-h,f),d[2]=Rr(-a,i)):(d[0]=Rr(l,u),d[2]=0);break;case"YXZ":d[0]=Dr(-Cr(h,-1,1)),Ir(h)<.99999?(d[1]=Rr(o,f),d[2]=Rr(s,u)):(d[1]=Rr(-c,i),d[2]=0);break;case"ZXY":d[0]=Dr(Cr(l,-1,1)),Ir(l)<.99999?(d[1]=Rr(-c,f),d[2]=Rr(-a,u)):(d[1]=0,d[2]=Rr(s,i));break;case"ZYX":d[1]=Dr(-Cr(c,-1,1)),Ir(c)<.99999?(d[0]=Rr(l,f),d[2]=Rr(s,i)):(d[0]=0,d[2]=Rr(-a,u));break;case"YZX":d[2]=Dr(Cr(s,-1,1)),Ir(s)<.99999?(d[0]=Rr(-h,u),d[1]=Rr(-c,i)):(d[0]=0,d[1]=Rr(o,f));break;case"XZY":d[2]=Dr(-Cr(a,-1,1)),Ir(a)<.99999?(d[0]=Rr(l,u),d[1]=Rr(o,i)):(d[0]=Rr(-h,f),d[1]=0)}return t._dirty=!0,t},Object.defineProperties(Ar,{POSITIVE_X:{get:function(){return new Ar(1,0,0)}},NEGATIVE_X:{get:function(){return new Ar(-1,0,0)}},POSITIVE_Y:{get:function(){return new Ar(0,1,0)}},NEGATIVE_Y:{get:function(){return new Ar(0,-1,0)}},POSITIVE_Z:{get:function(){return new Ar(0,0,1)}},NEGATIVE_Z:{get:function(){return new Ar(0,0,-1)}},UP:{get:function(){return new Ar(0,1,0)}},ZERO:{get:function(){return new Ar}}});var Lr,Or,Pr,Nr,Br,Ur=function(t,r){this.origin=t||new Ar,this.direction=r||new Ar};Ur.prototype={constructor:Ur,intersectPlane:function(t,r){var e=t.normal.array,n=t.distance,i=this.origin.array,a=this.direction.array,o=fr.dot(e,a);if(0===o)return null;r||(r=new Ar);var s=(fr.dot(e,i)-n)/o;return fr.scaleAndAdd(r.array,i,a,-s),r._dirty=!0,r},mirrorAgainstPlane:function(t){var r=fr.dot(t.normal.array,this.direction.array);fr.scaleAndAdd(this.direction.array,this.direction.array,t.normal.array,2*-r),this.direction._dirty=!0},distanceToPoint:(Br=fr.create(),function(t){fr.sub(Br,t,this.origin.array);var r=fr.dot(Br,this.direction.array);if(r<0)return fr.distance(this.origin.array,t);var e=fr.lenSquared(Br);return Math.sqrt(e-r*r)}),intersectSphere:function(){var t=fr.create();return function(r,e,n){var i=this.origin.array,a=this.direction.array;r=r.array,fr.sub(t,r,i);var o=fr.dot(t,a),s=fr.squaredLength(t)-o*o,u=e*e;if(!(s>u)){var h=Math.sqrt(u-s),c=o-h,l=o+h;return n||(n=new Ar),c<0?l<0?null:(fr.scaleAndAdd(n.array,i,a,l),n):(fr.scaleAndAdd(n.array,i,a,c),n)}}}(),intersectBoundingBox:function(t,r){var e,n,i,a,o,s,u=this.direction.array,h=this.origin.array,c=t.min.array,l=t.max.array,f=1/u[0],d=1/u[1],m=1/u[2];if(f>=0?(e=(c[0]-h[0])*f,n=(l[0]-h[0])*f):(n=(c[0]-h[0])*f,e=(l[0]-h[0])*f),d>=0?(i=(c[1]-h[1])*d,a=(l[1]-h[1])*d):(a=(c[1]-h[1])*d,i=(l[1]-h[1])*d),e>a||i>n)return null;if((i>e||e!=e)&&(e=i),(a<n||n!=n)&&(n=a),m>=0?(o=(c[2]-h[2])*m,s=(l[2]-h[2])*m):(s=(c[2]-h[2])*m,o=(l[2]-h[2])*m),e>s||o>n)return null;if((o>e||e!=e)&&(e=o),(s<n||n!=n)&&(n=s),n<0)return null;var p=e>=0?e:n;return r||(r=new Ar),fr.scaleAndAdd(r.array,h,u,p),r},intersectTriangle:(Lr=fr.create(),Or=fr.create(),Pr=fr.create(),Nr=fr.create(),function(t,r,e,n,i,a){var o=this.direction.array,s=this.origin.array;t=t.array,r=r.array,e=e.array,fr.sub(Lr,r,t),fr.sub(Or,e,t),fr.cross(Nr,Or,o);var u=fr.dot(Lr,Nr);if(n){if(u>-1e-5)return null}else if(u>-1e-5&&u<1e-5)return null;fr.sub(Pr,s,t);var h=fr.dot(Nr,Pr)/u;if(h<0||h>1)return null;fr.cross(Nr,Lr,Pr);var c=fr.dot(o,Nr)/u;if(c<0||c>1||h+c>1)return null;fr.cross(Nr,Lr,Or);var l=-fr.dot(Pr,Nr)/u;return l<0?null:(i||(i=new Ar),a&&Ar.set(a,1-h-c,h,c),fr.scaleAndAdd(i.array,s,o,l),i)}),applyTransform:function(t){Ar.add(this.direction,this.direction,this.origin),Ar.transformMat4(this.origin,this.origin,t),Ar.transformMat4(this.direction,this.direction,t),Ar.sub(this.direction,this.direction,this.origin),Ar.normalize(this.direction,this.direction)},copy:function(t){Ar.copy(this.origin,t.origin),Ar.copy(this.direction,t.direction)},clone:function(){var t=new Ur;return t.copy(this),t}};var Fr={create:function(){var t=new Ot(4);return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t},clone:function(t){var r=new Ot(4);return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r},fromValues:function(t,r,e,n){var i=new Ot(4);return i[0]=t,i[1]=r,i[2]=e,i[3]=n,i},copy:function(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t},set:function(t,r,e,n,i){return t[0]=r,t[1]=e,t[2]=n,t[3]=i,t},add:function(t,r,e){return t[0]=r[0]+e[0],t[1]=r[1]+e[1],t[2]=r[2]+e[2],t[3]=r[3]+e[3],t},subtract:function(t,r,e){return t[0]=r[0]-e[0],t[1]=r[1]-e[1],t[2]=r[2]-e[2],t[3]=r[3]-e[3],t}};Fr.sub=Fr.subtract,Fr.multiply=function(t,r,e){return t[0]=r[0]*e[0],t[1]=r[1]*e[1],t[2]=r[2]*e[2],t[3]=r[3]*e[3],t},Fr.mul=Fr.multiply,Fr.divide=function(t,r,e){return t[0]=r[0]/e[0],t[1]=r[1]/e[1],t[2]=r[2]/e[2],t[3]=r[3]/e[3],t},Fr.div=Fr.divide,Fr.min=function(t,r,e){return t[0]=Math.min(r[0],e[0]),t[1]=Math.min(r[1],e[1]),t[2]=Math.min(r[2],e[2]),t[3]=Math.min(r[3],e[3]),t},Fr.max=function(t,r,e){return t[0]=Math.max(r[0],e[0]),t[1]=Math.max(r[1],e[1]),t[2]=Math.max(r[2],e[2]),t[3]=Math.max(r[3],e[3]),t},Fr.scale=function(t,r,e){return t[0]=r[0]*e,t[1]=r[1]*e,t[2]=r[2]*e,t[3]=r[3]*e,t},Fr.scaleAndAdd=function(t,r,e,n){return t[0]=r[0]+e[0]*n,t[1]=r[1]+e[1]*n,t[2]=r[2]+e[2]*n,t[3]=r[3]+e[3]*n,t},Fr.distance=function(t,r){var e=r[0]-t[0],n=r[1]-t[1],i=r[2]-t[2],a=r[3]-t[3];return Math.sqrt(e*e+n*n+i*i+a*a)},Fr.dist=Fr.distance,Fr.squaredDistance=function(t,r){var e=r[0]-t[0],n=r[1]-t[1],i=r[2]-t[2],a=r[3]-t[3];return e*e+n*n+i*i+a*a},Fr.sqrDist=Fr.squaredDistance,Fr.length=function(t){var r=t[0],e=t[1],n=t[2],i=t[3];return Math.sqrt(r*r+e*e+n*n+i*i)},Fr.len=Fr.length,Fr.squaredLength=function(t){var r=t[0],e=t[1],n=t[2],i=t[3];return r*r+e*e+n*n+i*i},Fr.sqrLen=Fr.squaredLength,Fr.negate=function(t,r){return t[0]=-r[0],t[1]=-r[1],t[2]=-r[2],t[3]=-r[3],t},Fr.inverse=function(t,r){return t[0]=1/r[0],t[1]=1/r[1],t[2]=1/r[2],t[3]=1/r[3],t},Fr.normalize=function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=e*e+n*n+i*i+a*a;return o>0&&(o=1/Math.sqrt(o),t[0]=r[0]*o,t[1]=r[1]*o,t[2]=r[2]*o,t[3]=r[3]*o),t},Fr.dot=function(t,r){return t[0]*r[0]+t[1]*r[1]+t[2]*r[2]+t[3]*r[3]},Fr.lerp=function(t,r,e,n){var i=r[0],a=r[1],o=r[2],s=r[3];return t[0]=i+n*(e[0]-i),t[1]=a+n*(e[1]-a),t[2]=o+n*(e[2]-o),t[3]=s+n*(e[3]-s),t},Fr.random=function(t,r){return r=r||1,t[0]=Pt(),t[1]=Pt(),t[2]=Pt(),t[3]=Pt(),Fr.normalize(t,t),Fr.scale(t,t,r),t},Fr.transformMat4=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3];return t[0]=e[0]*n+e[4]*i+e[8]*a+e[12]*o,t[1]=e[1]*n+e[5]*i+e[9]*a+e[13]*o,t[2]=e[2]*n+e[6]*i+e[10]*a+e[14]*o,t[3]=e[3]*n+e[7]*i+e[11]*a+e[15]*o,t},Fr.transformQuat=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=e[0],s=e[1],u=e[2],h=e[3],c=h*n+s*a-u*i,l=h*i+u*n-o*a,f=h*a+o*i-s*n,d=-o*n-s*i-u*a;return t[0]=c*h+d*-o+l*-u-f*-s,t[1]=l*h+d*-s+f*-o-c*-u,t[2]=f*h+d*-u+c*-s-l*-o,t},Fr.forEach=function(){var t=Fr.create();return function(r,e,n,i,a,o){var s,u;for(e||(e=4),n||(n=0),u=i?Math.min(i*e+n,r.length):r.length,s=n;s<u;s+=e)t[0]=r[s],t[1]=r[s+1],t[2]=r[s+2],t[3]=r[s+3],a(t,t,o),r[s]=t[0],r[s+1]=t[1],r[s+2]=t[2],r[s+3]=t[3];return r}}();var kr={create:function(){var t=new Ot(9);return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},fromMat4:function(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[4],t[4]=r[5],t[5]=r[6],t[6]=r[8],t[7]=r[9],t[8]=r[10],t},clone:function(t){var r=new Ot(9);return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r[6]=t[6],r[7]=t[7],r[8]=t[8],r},copy:function(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t[4]=r[4],t[5]=r[5],t[6]=r[6],t[7]=r[7],t[8]=r[8],t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t},transpose:function(t,r){if(t===r){var e=r[1],n=r[2],i=r[5];t[1]=r[3],t[2]=r[6],t[3]=e,t[5]=r[7],t[6]=n,t[7]=i}else t[0]=r[0],t[1]=r[3],t[2]=r[6],t[3]=r[1],t[4]=r[4],t[5]=r[7],t[6]=r[2],t[7]=r[5],t[8]=r[8];return t},invert:function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=r[4],s=r[5],u=r[6],h=r[7],c=r[8],l=c*o-s*h,f=-c*a+s*u,d=h*a-o*u,m=e*l+n*f+i*d;return m?(m=1/m,t[0]=l*m,t[1]=(-c*n+i*h)*m,t[2]=(s*n-i*o)*m,t[3]=f*m,t[4]=(c*e-i*u)*m,t[5]=(-s*e+i*a)*m,t[6]=d*m,t[7]=(-h*e+n*u)*m,t[8]=(o*e-n*a)*m,t):null},adjoint:function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=r[4],s=r[5],u=r[6],h=r[7],c=r[8];return t[0]=o*c-s*h,t[1]=i*h-n*c,t[2]=n*s-i*o,t[3]=s*u-a*c,t[4]=e*c-i*u,t[5]=i*a-e*s,t[6]=a*h-o*u,t[7]=n*u-e*h,t[8]=e*o-n*a,t},determinant:function(t){var r=t[0],e=t[1],n=t[2],i=t[3],a=t[4],o=t[5],s=t[6],u=t[7],h=t[8];return r*(h*a-o*u)+e*(-h*i+o*s)+n*(u*i-a*s)},multiply:function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],u=r[5],h=r[6],c=r[7],l=r[8],f=e[0],d=e[1],m=e[2],p=e[3],y=e[4],_=e[5],v=e[6],g=e[7],x=e[8];return t[0]=f*n+d*o+m*h,t[1]=f*i+d*s+m*c,t[2]=f*a+d*u+m*l,t[3]=p*n+y*o+_*h,t[4]=p*i+y*s+_*c,t[5]=p*a+y*u+_*l,t[6]=v*n+g*o+x*h,t[7]=v*i+g*s+x*c,t[8]=v*a+g*u+x*l,t}};kr.mul=kr.multiply,kr.translate=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],u=r[5],h=r[6],c=r[7],l=r[8],f=e[0],d=e[1];return t[0]=n,t[1]=i,t[2]=a,t[3]=o,t[4]=s,t[5]=u,t[6]=f*n+d*o+h,t[7]=f*i+d*s+c,t[8]=f*a+d*u+l,t},kr.rotate=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],u=r[5],h=r[6],c=r[7],l=r[8],f=Math.sin(e),d=Math.cos(e);return t[0]=d*n+f*o,t[1]=d*i+f*s,t[2]=d*a+f*u,t[3]=d*o-f*n,t[4]=d*s-f*i,t[5]=d*u-f*a,t[6]=h,t[7]=c,t[8]=l,t},kr.scale=function(t,r,e){var n=e[0],i=e[1];return t[0]=n*r[0],t[1]=n*r[1],t[2]=n*r[2],t[3]=i*r[3],t[4]=i*r[4],t[5]=i*r[5],t[6]=r[6],t[7]=r[7],t[8]=r[8],t},kr.fromMat2d=function(t,r){return t[0]=r[0],t[1]=r[1],t[2]=0,t[3]=r[2],t[4]=r[3],t[5]=0,t[6]=r[4],t[7]=r[5],t[8]=1,t},kr.fromQuat=function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=e+e,s=n+n,u=i+i,h=e*o,c=n*o,l=n*s,f=i*o,d=i*s,m=i*u,p=a*o,y=a*s,_=a*u;return t[0]=1-l-m,t[3]=c-_,t[6]=f+y,t[1]=c+_,t[4]=1-h-m,t[7]=d-p,t[2]=f-y,t[5]=d+p,t[8]=1-h-l,t},kr.normalFromMat4=function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=r[4],s=r[5],u=r[6],h=r[7],c=r[8],l=r[9],f=r[10],d=r[11],m=r[12],p=r[13],y=r[14],_=r[15],v=e*s-n*o,g=e*u-i*o,x=e*h-a*o,T=n*u-i*s,b=n*h-a*s,E=i*h-a*u,w=c*p-l*m,A=c*y-f*m,S=c*_-d*m,M=l*y-f*p,C=l*_-d*p,R=f*_-d*y,D=v*R-g*C+x*M+T*S-b*A+E*w;return D?(D=1/D,t[0]=(s*R-u*C+h*M)*D,t[1]=(u*S-o*R-h*A)*D,t[2]=(o*C-s*S+h*w)*D,t[3]=(i*C-n*R-a*M)*D,t[4]=(e*R-i*S+a*A)*D,t[5]=(n*S-e*C-a*w)*D,t[6]=(p*E-y*b+_*T)*D,t[7]=(y*x-m*E-_*g)*D,t[8]=(m*b-p*x+_*v)*D,t):null},kr.frob=function(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2)+Math.pow(t[2],2)+Math.pow(t[3],2)+Math.pow(t[4],2)+Math.pow(t[5],2)+Math.pow(t[6],2)+Math.pow(t[7],2)+Math.pow(t[8],2))};var Hr,Vr,Wr,Gr,zr={};zr.create=function(){var t=new Ot(4);return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t},zr.rotationTo=(Hr=fr.create(),Vr=fr.fromValues(1,0,0),Wr=fr.fromValues(0,1,0),function(t,r,e){var n=fr.dot(r,e);return n<-.999999?(fr.cross(Hr,Vr,r),fr.length(Hr)<1e-6&&fr.cross(Hr,Wr,r),fr.normalize(Hr,Hr),zr.setAxisAngle(t,Hr,Math.PI),t):n>.999999?(t[0]=0,t[1]=0,t[2]=0,t[3]=1,t):(fr.cross(Hr,r,e),t[0]=Hr[0],t[1]=Hr[1],t[2]=Hr[2],t[3]=1+n,zr.normalize(t,t))}),zr.setAxes=(Gr=kr.create(),function(t,r,e,n){return Gr[0]=e[0],Gr[3]=e[1],Gr[6]=e[2],Gr[1]=n[0],Gr[4]=n[1],Gr[7]=n[2],Gr[2]=-r[0],Gr[5]=-r[1],Gr[8]=-r[2],zr.normalize(t,zr.fromMat3(t,Gr))}),zr.clone=Fr.clone,zr.fromValues=Fr.fromValues,zr.copy=Fr.copy,zr.set=Fr.set,zr.identity=function(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t},zr.setAxisAngle=function(t,r,e){e*=.5;var n=Math.sin(e);return t[0]=n*r[0],t[1]=n*r[1],t[2]=n*r[2],t[3]=Math.cos(e),t},zr.add=Fr.add,zr.multiply=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=e[0],u=e[1],h=e[2],c=e[3];return t[0]=n*c+o*s+i*h-a*u,t[1]=i*c+o*u+a*s-n*h,t[2]=a*c+o*h+n*u-i*s,t[3]=o*c-n*s-i*u-a*h,t},zr.mul=zr.multiply,zr.scale=Fr.scale,zr.rotateX=function(t,r,e){e*=.5;var n=r[0],i=r[1],a=r[2],o=r[3],s=Math.sin(e),u=Math.cos(e);return t[0]=n*u+o*s,t[1]=i*u+a*s,t[2]=a*u-i*s,t[3]=o*u-n*s,t},zr.rotateY=function(t,r,e){e*=.5;var n=r[0],i=r[1],a=r[2],o=r[3],s=Math.sin(e),u=Math.cos(e);return t[0]=n*u-a*s,t[1]=i*u+o*s,t[2]=a*u+n*s,t[3]=o*u-i*s,t},zr.rotateZ=function(t,r,e){e*=.5;var n=r[0],i=r[1],a=r[2],o=r[3],s=Math.sin(e),u=Math.cos(e);return t[0]=n*u+i*s,t[1]=i*u-n*s,t[2]=a*u+o*s,t[3]=o*u-a*s,t},zr.calculateW=function(t,r){var e=r[0],n=r[1],i=r[2];return t[0]=e,t[1]=n,t[2]=i,t[3]=Math.sqrt(Math.abs(1-e*e-n*n-i*i)),t},zr.dot=Fr.dot,zr.lerp=Fr.lerp,zr.slerp=function(t,r,e,n){var i,a,o,s,u,h=r[0],c=r[1],l=r[2],f=r[3],d=e[0],m=e[1],p=e[2],y=e[3];return(a=h*d+c*m+l*p+f*y)<0&&(a=-a,d=-d,m=-m,p=-p,y=-y),1-a>1e-6?(i=Math.acos(a),o=Math.sin(i),s=Math.sin((1-n)*i)/o,u=Math.sin(n*i)/o):(s=1-n,u=n),t[0]=s*h+u*d,t[1]=s*c+u*m,t[2]=s*l+u*p,t[3]=s*f+u*y,t},zr.invert=function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=e*e+n*n+i*i+a*a,s=o?1/o:0;return t[0]=-e*s,t[1]=-n*s,t[2]=-i*s,t[3]=a*s,t},zr.conjugate=function(t,r){return t[0]=-r[0],t[1]=-r[1],t[2]=-r[2],t[3]=r[3],t},zr.length=Fr.length,zr.len=zr.length,zr.squaredLength=Fr.squaredLength,zr.sqrLen=zr.squaredLength,zr.normalize=Fr.normalize,zr.fromMat3=function(t,r){var e,n=r[0]+r[4]+r[8];if(n>0)e=Math.sqrt(n+1),t[3]=.5*e,e=.5/e,t[0]=(r[5]-r[7])*e,t[1]=(r[6]-r[2])*e,t[2]=(r[1]-r[3])*e;else{var i=0;r[4]>r[0]&&(i=1),r[8]>r[3*i+i]&&(i=2);var a=(i+1)%3,o=(i+2)%3;e=Math.sqrt(r[3*i+i]-r[3*a+a]-r[3*o+o]+1),t[i]=.5*e,e=.5/e,t[3]=(r[3*a+o]-r[3*o+a])*e,t[a]=(r[3*a+i]+r[3*i+a])*e,t[o]=(r[3*o+i]+r[3*i+o])*e}return t};var Xr,jr,qr,Yr,Kr=function(){this._axisX=new Ar,this._axisY=new Ar,this._axisZ=new Ar,this.array=lr.create(),this._dirty=!0};Kr.prototype={constructor:Kr,setArray:function(t){for(var r=0;r<this.array.length;r++)this.array[r]=t[r];return this._dirty=!0,this},adjoint:function(){return lr.adjoint(this.array,this.array),this._dirty=!0,this},clone:function(){return(new Kr).copy(this)},copy:function(t){return lr.copy(this.array,t.array),this._dirty=!0,this},determinant:function(){return lr.determinant(this.array)},fromQuat:function(t){return lr.fromQuat(this.array,t.array),this._dirty=!0,this},fromRotationTranslation:function(t,r){return lr.fromRotationTranslation(this.array,t.array,r.array),this._dirty=!0,this},fromMat2d:function(t){return Kr.fromMat2d(this,t),this},frustum:function(t,r,e,n,i,a){return lr.frustum(this.array,t,r,e,n,i,a),this._dirty=!0,this},identity:function(){return lr.identity(this.array),this._dirty=!0,this},invert:function(){return lr.invert(this.array,this.array),this._dirty=!0,this},lookAt:function(t,r,e){return lr.lookAt(this.array,t.array,r.array,e.array),this._dirty=!0,this},mul:function(t){return lr.mul(this.array,this.array,t.array),this._dirty=!0,this},mulLeft:function(t){return lr.mul(this.array,t.array,this.array),this._dirty=!0,this},multiply:function(t){return lr.multiply(this.array,this.array,t.array),this._dirty=!0,this},multiplyLeft:function(t){return lr.multiply(this.array,t.array,this.array),this._dirty=!0,this},ortho:function(t,r,e,n,i,a){return lr.ortho(this.array,t,r,e,n,i,a),this._dirty=!0,this},perspective:function(t,r,e,n){return lr.perspective(this.array,t,r,e,n),this._dirty=!0,this},rotate:function(t,r){return lr.rotate(this.array,this.array,t,r.array),this._dirty=!0,this},rotateX:function(t){return lr.rotateX(this.array,this.array,t),this._dirty=!0,this},rotateY:function(t){return lr.rotateY(this.array,this.array,t),this._dirty=!0,this},rotateZ:function(t){return lr.rotateZ(this.array,this.array,t),this._dirty=!0,this},scale:function(t){return lr.scale(this.array,this.array,t.array),this._dirty=!0,this},translate:function(t){return lr.translate(this.array,this.array,t.array),this._dirty=!0,this},transpose:function(){return lr.transpose(this.array,this.array),this._dirty=!0,this},decomposeMatrix:(Xr=fr.create(),jr=fr.create(),qr=fr.create(),Yr=kr.create(),function(t,r,e){var n=this.array;fr.set(Xr,n[0],n[1],n[2]),fr.set(jr,n[4],n[5],n[6]),fr.set(qr,n[8],n[9],n[10]);var i=fr.length(Xr),a=fr.length(jr),o=fr.length(qr);this.determinant()<0&&(i=-i),t&&t.set(i,a,o),e.set(n[12],n[13],n[14]),kr.fromMat4(Yr,n),Yr[0]/=i,Yr[1]/=i,Yr[2]/=i,Yr[3]/=a,Yr[4]/=a,Yr[5]/=a,Yr[6]/=o,Yr[7]/=o,Yr[8]/=o,zr.fromMat3(r.array,Yr),zr.normalize(r.array,r.array),r._dirty=!0,e._dirty=!0}),toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}};var Zr=Object.defineProperty;if(Zr){var Jr=Kr.prototype;Zr(Jr,"z",{get:function(){var t=this.array;return this._axisZ.set(t[8],t[9],t[10]),this._axisZ},set:function(t){var r=this.array;t=t.array,r[8]=t[0],r[9]=t[1],r[10]=t[2],this._dirty=!0}}),Zr(Jr,"y",{get:function(){var t=this.array;return this._axisY.set(t[4],t[5],t[6]),this._axisY},set:function(t){var r=this.array;t=t.array,r[4]=t[0],r[5]=t[1],r[6]=t[2],this._dirty=!0}}),Zr(Jr,"x",{get:function(){var t=this.array;return this._axisX.set(t[0],t[1],t[2]),this._axisX},set:function(t){var r=this.array;t=t.array,r[0]=t[0],r[1]=t[1],r[2]=t[2],this._dirty=!0}})}Kr.adjoint=function(t,r){return lr.adjoint(t.array,r.array),t._dirty=!0,t},Kr.copy=function(t,r){return lr.copy(t.array,r.array),t._dirty=!0,t},Kr.determinant=function(t){return lr.determinant(t.array)},Kr.identity=function(t){return lr.identity(t.array),t._dirty=!0,t},Kr.ortho=function(t,r,e,n,i,a,o){return lr.ortho(t.array,r,e,n,i,a,o),t._dirty=!0,t},Kr.perspective=function(t,r,e,n,i){return lr.perspective(t.array,r,e,n,i),t._dirty=!0,t},Kr.lookAt=function(t,r,e,n){return lr.lookAt(t.array,r.array,e.array,n.array),t._dirty=!0,t},Kr.invert=function(t,r){return lr.invert(t.array,r.array),t._dirty=!0,t},Kr.multiply=Kr.mul=function(t,r,e){return lr.mul(t.array,r.array,e.array),t._dirty=!0,t},Kr.fromQuat=function(t,r){return lr.fromQuat(t.array,r.array),t._dirty=!0,t},Kr.fromRotationTranslation=function(t,r,e){return lr.fromRotationTranslation(t.array,r.array,e.array),t._dirty=!0,t},Kr.fromMat2d=function(t,r){t._dirty=!0;r=r.array;return(t=t.array)[0]=r[0],t[4]=r[2],t[12]=r[4],t[1]=r[1],t[5]=r[3],t[13]=r[5],t},Kr.rotate=function(t,r,e,n){return lr.rotate(t.array,r.array,e,n.array),t._dirty=!0,t},Kr.rotateX=function(t,r,e){return lr.rotateX(t.array,r.array,e),t._dirty=!0,t},Kr.rotateY=function(t,r,e){return lr.rotateY(t.array,r.array,e),t._dirty=!0,t},Kr.rotateZ=function(t,r,e){return lr.rotateZ(t.array,r.array,e),t._dirty=!0,t},Kr.scale=function(t,r,e){return lr.scale(t.array,r.array,e.array),t._dirty=!0,t},Kr.transpose=function(t,r){return lr.transpose(t.array,r.array),t._dirty=!0,t},Kr.translate=function(t,r,e){return lr.translate(t.array,r.array,e.array),t._dirty=!0,t};var Qr=function(t,r,e,n){t=t||0,r=r||0,e=e||0,n=void 0===n?1:n,this.array=zr.fromValues(t,r,e,n),this._dirty=!0};Qr.prototype={constructor:Qr,add:function(t){return zr.add(this.array,this.array,t.array),this._dirty=!0,this},calculateW:function(){return zr.calculateW(this.array,this.array),this._dirty=!0,this},set:function(t,r,e,n){return this.array[0]=t,this.array[1]=r,this.array[2]=e,this.array[3]=n,this._dirty=!0,this},setArray:function(t){return this.array[0]=t[0],this.array[1]=t[1],this.array[2]=t[2],this.array[3]=t[3],this._dirty=!0,this},clone:function(){return new Qr(this.x,this.y,this.z,this.w)},conjugate:function(){return zr.conjugate(this.array,this.array),this._dirty=!0,this},copy:function(t){return zr.copy(this.array,t.array),this._dirty=!0,this},dot:function(t){return zr.dot(this.array,t.array)},fromMat3:function(t){return zr.fromMat3(this.array,t.array),this._dirty=!0,this},fromMat4:function(){var t=kr.create();return function(r){return kr.fromMat4(t,r.array),kr.transpose(t,t),zr.fromMat3(this.array,t),this._dirty=!0,this}}(),identity:function(){return zr.identity(this.array),this._dirty=!0,this},invert:function(){return zr.invert(this.array,this.array),this._dirty=!0,this},len:function(){return zr.len(this.array)},length:function(){return zr.length(this.array)},lerp:function(t,r,e){return zr.lerp(this.array,t.array,r.array,e),this._dirty=!0,this},mul:function(t){return zr.mul(this.array,this.array,t.array),this._dirty=!0,this},mulLeft:function(t){return zr.multiply(this.array,t.array,this.array),this._dirty=!0,this},multiply:function(t){return zr.multiply(this.array,this.array,t.array),this._dirty=!0,this},multiplyLeft:function(t){return zr.multiply(this.array,t.array,this.array),this._dirty=!0,this},normalize:function(){return zr.normalize(this.array,this.array),this._dirty=!0,this},rotateX:function(t){return zr.rotateX(this.array,this.array,t),this._dirty=!0,this},rotateY:function(t){return zr.rotateY(this.array,this.array,t),this._dirty=!0,this},rotateZ:function(t){return zr.rotateZ(this.array,this.array,t),this._dirty=!0,this},rotationTo:function(t,r){return zr.rotationTo(this.array,t.array,r.array),this._dirty=!0,this},setAxes:function(t,r,e){return zr.setAxes(this.array,t.array,r.array,e.array),this._dirty=!0,this},setAxisAngle:function(t,r){return zr.setAxisAngle(this.array,t.array,r),this._dirty=!0,this},slerp:function(t,r,e){return zr.slerp(this.array,t.array,r.array,e),this._dirty=!0,this},sqrLen:function(){return zr.sqrLen(this.array)},squaredLength:function(){return zr.squaredLength(this.array)},fromEuler:function(t,r){return Qr.fromEuler(this,t,r)},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}};var $r=Object.defineProperty;if($r){var te=Qr.prototype;$r(te,"x",{get:function(){return this.array[0]},set:function(t){this.array[0]=t,this._dirty=!0}}),$r(te,"y",{get:function(){return this.array[1]},set:function(t){this.array[1]=t,this._dirty=!0}}),$r(te,"z",{get:function(){return this.array[2]},set:function(t){this.array[2]=t,this._dirty=!0}}),$r(te,"w",{get:function(){return this.array[3]},set:function(t){this.array[3]=t,this._dirty=!0}})}Qr.add=function(t,r,e){return zr.add(t.array,r.array,e.array),t._dirty=!0,t},Qr.set=function(t,r,e,n,i){zr.set(t.array,r,e,n,i),t._dirty=!0},Qr.copy=function(t,r){return zr.copy(t.array,r.array),t._dirty=!0,t},Qr.calculateW=function(t,r){return zr.calculateW(t.array,r.array),t._dirty=!0,t},Qr.conjugate=function(t,r){return zr.conjugate(t.array,r.array),t._dirty=!0,t},Qr.identity=function(t){return zr.identity(t.array),t._dirty=!0,t},Qr.invert=function(t,r){return zr.invert(t.array,r.array),t._dirty=!0,t},Qr.dot=function(t,r){return zr.dot(t.array,r.array)},Qr.len=function(t){return zr.length(t.array)},Qr.lerp=function(t,r,e,n){return zr.lerp(t.array,r.array,e.array,n),t._dirty=!0,t},Qr.slerp=function(t,r,e,n){return zr.slerp(t.array,r.array,e.array,n),t._dirty=!0,t},Qr.multiply=Qr.mul=function(t,r,e){return zr.multiply(t.array,r.array,e.array),t._dirty=!0,t},Qr.rotateX=function(t,r,e){return zr.rotateX(t.array,r.array,e),t._dirty=!0,t},Qr.rotateY=function(t,r,e){return zr.rotateY(t.array,r.array,e),t._dirty=!0,t},Qr.rotateZ=function(t,r,e){return zr.rotateZ(t.array,r.array,e),t._dirty=!0,t},Qr.setAxisAngle=function(t,r,e){return zr.setAxisAngle(t.array,r.array,e),t._dirty=!0,t},Qr.normalize=function(t,r){return zr.normalize(t.array,r.array),t._dirty=!0,t},Qr.squaredLength=Qr.sqrLen=function(t){return zr.sqrLen(t.array)},Qr.fromMat3=function(t,r){return zr.fromMat3(t.array,r.array),t._dirty=!0,t},Qr.setAxes=function(t,r,e,n){return zr.setAxes(t.array,r.array,e.array,n.array),t._dirty=!0,t},Qr.rotationTo=function(t,r,e){return zr.rotationTo(t.array,r.array,e.array),t._dirty=!0,t},Qr.fromEuler=function(t,r,e){t._dirty=!0,r=r.array;var n=t.array,i=Math.cos(r[0]/2),a=Math.cos(r[1]/2),o=Math.cos(r[2]/2),s=Math.sin(r[0]/2),u=Math.sin(r[1]/2),h=Math.sin(r[2]/2);switch(e=(e||"XYZ").toUpperCase()){case"XYZ":n[0]=s*a*o+i*u*h,n[1]=i*u*o-s*a*h,n[2]=i*a*h+s*u*o,n[3]=i*a*o-s*u*h;break;case"YXZ":n[0]=s*a*o+i*u*h,n[1]=i*u*o-s*a*h,n[2]=i*a*h-s*u*o,n[3]=i*a*o+s*u*h;break;case"ZXY":n[0]=s*a*o-i*u*h,n[1]=i*u*o+s*a*h,n[2]=i*a*h+s*u*o,n[3]=i*a*o-s*u*h;break;case"ZYX":n[0]=s*a*o-i*u*h,n[1]=i*u*o+s*a*h,n[2]=i*a*h-s*u*o,n[3]=i*a*o+s*u*h;break;case"YZX":n[0]=s*a*o+i*u*h,n[1]=i*u*o+s*a*h,n[2]=i*a*h-s*u*o,n[3]=i*a*o-s*u*h;break;case"XZY":n[0]=s*a*o-i*u*h,n[1]=i*u*o-s*a*h,n[2]=i*a*h+s*u*o,n[3]=i*a*o+s*u*h}};var re,ee,ne,ie,ae,oe,se=fr.set,ue=fr.copy,he=function(t,r){this.min=t||new Ar(Infinity,Infinity,Infinity),this.max=r||new Ar(-Infinity,-Infinity,-Infinity),this.vertices=null};he.prototype={constructor:he,updateFromVertices:function(t){if(t.length>0){var r=this.min,e=this.max,n=r.array,i=e.array;ue(n,t[0]),ue(i,t[0]);for(var a=1;a<t.length;a++){var o=t[a];o[0]<n[0]&&(n[0]=o[0]),o[1]<n[1]&&(n[1]=o[1]),o[2]<n[2]&&(n[2]=o[2]),o[0]>i[0]&&(i[0]=o[0]),o[1]>i[1]&&(i[1]=o[1]),o[2]>i[2]&&(i[2]=o[2])}r._dirty=!0,e._dirty=!0}},union:function(t){var r=this.min,e=this.max;return fr.min(r.array,r.array,t.min.array),fr.max(e.array,e.array,t.max.array),r._dirty=!0,e._dirty=!0,this},intersection:function(t){var r=this.min,e=this.max;return fr.max(r.array,r.array,t.min.array),fr.min(e.array,e.array,t.max.array),r._dirty=!0,e._dirty=!0,this},intersectBoundingBox:function(t){var r=this.min.array,e=this.max.array,n=t.min.array,i=t.max.array;return!(r[0]>i[0]||r[1]>i[1]||r[2]>i[2]||e[0]<n[0]||e[1]<n[1]||e[2]<n[2])},containBoundingBox:function(t){var r=this.min.array,e=this.max.array,n=t.min.array,i=t.max.array;return r[0]<=n[0]&&r[1]<=n[1]&&r[2]<=n[2]&&e[0]>=i[0]&&e[1]>=i[1]&&e[2]>=i[2]},containPoint:function(t){var r=this.min.array,e=this.max.array,n=t.array;return r[0]<=n[0]&&r[1]<=n[1]&&r[2]<=n[2]&&e[0]>=n[0]&&e[1]>=n[1]&&e[2]>=n[2]},isFinite:function(){var t=this.min.array,r=this.max.array;return isFinite(t[0])&&isFinite(t[1])&&isFinite(t[2])&&isFinite(r[0])&&isFinite(r[1])&&isFinite(r[2])},applyTransform:function(t){this.transformFrom(this,t)},transformFrom:(re=fr.create(),ee=fr.create(),ne=fr.create(),ie=fr.create(),ae=fr.create(),oe=fr.create(),function(t,r){var e=t.min.array,n=t.max.array,i=r.array;return re[0]=i[0]*e[0],re[1]=i[1]*e[0],re[2]=i[2]*e[0],ee[0]=i[0]*n[0],ee[1]=i[1]*n[0],ee[2]=i[2]*n[0],ne[0]=i[4]*e[1],ne[1]=i[5]*e[1],ne[2]=i[6]*e[1],ie[0]=i[4]*n[1],ie[1]=i[5]*n[1],ie[2]=i[6]*n[1],ae[0]=i[8]*e[2],ae[1]=i[9]*e[2],ae[2]=i[10]*e[2],oe[0]=i[8]*n[2],oe[1]=i[9]*n[2],oe[2]=i[10]*n[2],e=this.min.array,n=this.max.array,e[0]=Math.min(re[0],ee[0])+Math.min(ne[0],ie[0])+Math.min(ae[0],oe[0])+i[12],e[1]=Math.min(re[1],ee[1])+Math.min(ne[1],ie[1])+Math.min(ae[1],oe[1])+i[13],e[2]=Math.min(re[2],ee[2])+Math.min(ne[2],ie[2])+Math.min(ae[2],oe[2])+i[14],n[0]=Math.max(re[0],ee[0])+Math.max(ne[0],ie[0])+Math.max(ae[0],oe[0])+i[12],n[1]=Math.max(re[1],ee[1])+Math.max(ne[1],ie[1])+Math.max(ae[1],oe[1])+i[13],n[2]=Math.max(re[2],ee[2])+Math.max(ne[2],ie[2])+Math.max(ae[2],oe[2])+i[14],this.min._dirty=!0,this.max._dirty=!0,this}),applyProjection:function(t){var r=this.min.array,e=this.max.array,n=t.array,i=r[0],a=r[1],o=r[2],s=e[0],u=e[1],h=r[2],c=e[0],l=e[1],f=e[2];if(1===n[15])r[0]=n[0]*i+n[12],r[1]=n[5]*a+n[13],e[2]=n[10]*o+n[14],e[0]=n[0]*c+n[12],e[1]=n[5]*l+n[13],r[2]=n[10]*f+n[14];else{var d=-1/o;r[0]=n[0]*i*d,r[1]=n[5]*a*d,e[2]=(n[10]*o+n[14])*d,d=-1/h,e[0]=n[0]*s*d,e[1]=n[5]*u*d,d=-1/f,r[2]=(n[10]*f+n[14])*d}return this.min._dirty=!0,this.max._dirty=!0,this},updateVertices:function(){var t=this.vertices;if(!t){t=[];for(var r=0;r<8;r++)t[r]=fr.fromValues(0,0,0);this.vertices=t}var e=this.min.array,n=this.max.array;return se(t[0],e[0],e[1],e[2]),se(t[1],e[0],n[1],e[2]),se(t[2],n[0],e[1],e[2]),se(t[3],n[0],n[1],e[2]),se(t[4],e[0],e[1],n[2]),se(t[5],e[0],n[1],n[2]),se(t[6],n[0],e[1],n[2]),se(t[7],n[0],n[1],n[2]),this},copy:function(t){var r=this.min,e=this.max;return ue(r.array,t.min.array),ue(e.array,t.max.array),r._dirty=!0,e._dirty=!0,this},clone:function(){var t=new he;return t.copy(this),t}};var ce,le,fe=0,de=u.extend({name:"",position:null,rotation:null,scale:null,worldTransform:null,localTransform:null,autoUpdateLocalTransform:!0,_parent:null,_scene:null,_needsUpdateWorldTransform:!0,_inIterating:!1,__depth:0},(function(){this.name||(this.name=(this.type||"NODE")+"_"+fe++),this.position||(this.position=new Ar),this.rotation||(this.rotation=new Qr),this.scale||(this.scale=new Ar(1,1,1)),this.worldTransform=new Kr,this.localTransform=new Kr,this._children=[]}),{target:null,invisible:!1,isSkinnedMesh:function(){return!1},isRenderable:function(){return!1},setName:function(t){var r=this._scene;if(r){var e=r._nodeRepository;delete e[this.name],e[t]=this}this.name=t},add:function(t){var r=t._parent;if(r!==this){r&&r.remove(t),t._parent=this,this._children.push(t);var e=this._scene;e&&e!==t.scene&&t.traverse(this._addSelfToScene,this),t._needsUpdateWorldTransform=!0}},remove:function(t){var r=this._children,e=r.indexOf(t);e<0||(r.splice(e,1),t._parent=null,this._scene&&t.traverse(this._removeSelfFromScene,this))},removeAll:function(){for(var t=this._children,r=0;r<t.length;r++)t[r]._parent=null,this._scene&&t[r].traverse(this._removeSelfFromScene,this);this._children=[]},getScene:function(){return this._scene},getParent:function(){return this._parent},_removeSelfFromScene:function(t){t._scene.removeFromScene(t),t._scene=null},_addSelfToScene:function(t){this._scene.addToScene(t),t._scene=this._scene},isAncestor:function(t){for(var r=t._parent;r;){if(r===this)return!0;r=r._parent}return!1},children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},getChildByName:function(t){for(var r=this._children,e=0;e<r.length;e++)if(r[e].name===t)return r[e]},getDescendantByName:function(t){for(var r=this._children,e=0;e<r.length;e++){var n=r[e];if(n.name===t)return n;var i=n.getDescendantByName(t);if(i)return i}},queryNode:function(t){if(t){for(var r=t.split("/"),e=this,n=0;n<r.length;n++){var i=r[n];if(i){for(var a=!1,o=e._children,s=0;s<o.length;s++){var u=o[s];if(u.name===i){e=u,a=!0;break}}if(!a)return}}return e}},getPath:function(t){if(!this._parent)return"/";for(var r=this._parent,e=this.name;r._parent&&(e=r.name+"/"+e,r._parent!=t);)r=r._parent;return!r._parent&&t?null:e},traverse:function(t,r){t.call(r,this);for(var e=this._children,n=0,i=e.length;n<i;n++)e[n].traverse(t,r)},eachChild:function(t,r){for(var e=this._children,n=0,i=e.length;n<i;n++){var a=e[n];t.call(r,a,n)}},setLocalTransform:function(t){lr.copy(this.localTransform.array,t.array),this.decomposeLocalTransform()},decomposeLocalTransform:function(t){var r=t?null:this.scale;this.localTransform.decomposeMatrix(r,this.rotation,this.position)},setWorldTransform:function(t){lr.copy(this.worldTransform.array,t.array),this.decomposeWorldTransform()},decomposeWorldTransform:(le=lr.create(),function(t){var r=this.localTransform,e=this.worldTransform;this._parent?(lr.invert(le,this._parent.worldTransform.array),lr.multiply(r.array,le,e.array)):lr.copy(r.array,e.array);var n=t?null:this.scale;r.decomposeMatrix(n,this.rotation,this.position)}),transformNeedsUpdate:function(){return this.position._dirty||this.rotation._dirty||this.scale._dirty},updateLocalTransform:function(){var t=this.position,r=this.rotation,e=this.scale;if(this.transformNeedsUpdate()){var n=this.localTransform.array;lr.fromRotationTranslation(n,r.array,t.array),lr.scale(n,n,e.array),r._dirty=!1,e._dirty=!1,t._dirty=!1,this._needsUpdateWorldTransform=!0}},_updateWorldTransformTopDown:function(){var t=this.localTransform.array,r=this.worldTransform.array;this._parent?lr.multiplyAffine(r,this._parent.worldTransform.array,t):lr.copy(r,t)},updateWorldTransform:function(){for(var t=this;t&&t.getParent()&&t.getParent().transformNeedsUpdate();)t=t.getParent();t.update()},update:function(t){this.autoUpdateLocalTransform?this.updateLocalTransform():t=!0,(t||this._needsUpdateWorldTransform)&&(this._updateWorldTransformTopDown(),t=!0,this._needsUpdateWorldTransform=!1);for(var r=this._children,e=0,n=r.length;e<n;e++)r[e].update(t)},getBoundingBox:function(){function t(t){return!t.invisible&&t.geometry}var r=new he,e=new Kr,n=new Kr;return function(i,a){return a=a||new he,this._parent?Kr.invert(n,this._parent.worldTransform):Kr.identity(n),this.traverse((function(t){t.geometry&&t.geometry.boundingBox&&(r.copy(t.geometry.boundingBox),Kr.multiply(e,n,t.worldTransform),r.applyTransform(e),a.union(r))}),this,t),a}}(),getWorldPosition:function(t){this.transformNeedsUpdate()&&this.updateWorldTransform();var r=this.worldTransform.array;if(t){var e=t.array;return e[0]=r[12],e[1]=r[13],e[2]=r[14],t}return new Ar(r[12],r[13],r[14])},clone:function(){var t=new this.constructor,r=this._children;t.setName(this.name),t.position.copy(this.position),t.rotation.copy(this.rotation),t.scale.copy(this.scale);for(var e=0;e<r.length;e++)t.add(r[e].clone());return t},rotateAround:function(){var t=new Ar,r=new Kr;return function(e,n,i){t.copy(this.position).subtract(e);var a=this.localTransform;a.identity(),a.translate(e),a.rotate(i,n),r.fromRotationTranslation(this.rotation,t),a.multiply(r),a.scale(this.scale),this.decomposeLocalTransform(),this._needsUpdateWorldTransform=!0}}(),lookAt:(ce=new Kr,function(t,r){ce.lookAt(this.position,t,r||this.localTransform.y).invert(),this.setLocalTransform(ce),this.target=t})}),me=de.extend({material:null,geometry:null,mode:g,_renderInfo:null},{__program:null,lightGroup:0,renderOrder:0,culling:!0,cullFace:S,frontFace:R,frustumCulling:!0,receiveShadow:!0,castShadow:!0,ignorePicking:!1,ignorePreZ:!1,ignoreGBuffer:!1,isRenderable:function(){return this.geometry&&this.material&&this.material.shader&&!this.invisible&&this.geometry.vertexCount>0},beforeRender:function(t){},afterRender:function(t,r){},getBoundingBox:function(t,r){return r=de.prototype.getBoundingBox.call(this,t,r),this.geometry&&this.geometry.boundingBox&&r.union(this.geometry.boundingBox),r},clone:function(){var t=["castShadow","receiveShadow","mode","culling","cullFace","frontFace","frustumCulling","renderOrder","lineWidth","ignorePicking","ignorePreZ","ignoreGBuffer"];return function(){var r=de.prototype.clone.call(this);r.geometry=this.geometry,r.material=this.material;for(var e=0;e<t.length;e++){var n=t[e];r[n]!==this[n]&&(r[n]=this[n])}return r}}()});me.POINTS=p,me.LINES=y,me.LINE_LOOP=_,me.LINE_STRIP=v,me.TRIANGLES=g,me.TRIANGLE_STRIP=x,me.TRIANGLE_FAN=T,me.BACK=S,me.FRONT=A,me.FRONT_AND_BACK=M,me.CW=C,me.CCW=R;var pe,ye,_e,ve,ge,xe=u.extend({scene:null,camera:null,renderer:null},(function(){this._ray=new Ur,this._ndc=new Bt}),{pick:function(t,r,e){return this.pickAll(t,r,[],e)[0]||null},pickAll:function(t,r,e,n){return this.renderer.screenToNDC(t,r,this._ndc),this.camera.castRay(this._ndc,this._ray),e=e||[],this._intersectNode(this.scene,e,n||!1),e.sort(this._intersectionCompareFunc),e},_intersectNode:function(t,r,e){t instanceof me&&t.isRenderable()&&(t.ignorePicking&&!e||!(t.mode===g&&t.geometry.isUseIndices()||t.geometry.pickByRay||t.geometry.pick)||this._intersectRenderable(t,r));for(var n=0;n<t._children.length;n++)this._intersectNode(t._children[n],r,e)},_intersectRenderable:(pe=new Ar,ye=new Ar,_e=new Ar,ve=new Ur,ge=new Kr,function(t,r){var e=t.isSkinnedMesh();ve.copy(this._ray),Kr.invert(ge,t.worldTransform),e||ve.applyTransform(ge);var n=t.geometry,i=e?t.skeleton.boundingBox:n.boundingBox;if(!i||ve.intersectBoundingBox(i))if(n.pick)n.pick(this._ndc.x,this._ndc.y,this.renderer,this.camera,t,r);else if(n.pickByRay)n.pickByRay(ve,t,r);else{var a,o,s=t.cullFace===S&&t.frontFace===R||t.cullFace===A&&t.frontFace===C,u=n.indices,h=n.attributes.position,c=n.attributes.weight,l=n.attributes.joint,f=[];if(h&&h.value&&u){if(e){o=t.skeleton.getSubSkinMatrices(t.__uid__,t.joints);for(var d=0;d<t.joints.length;d++){f[d]=f[d]||[];for(var m=0;m<16;m++)f[d][m]=o[16*d+m]}var p=[],y=[],_=[],v=[],g=[],x=n.attributes.skinnedPosition;for(x&&x.value||(n.createAttribute("skinnedPosition","f",3),(x=n.attributes.skinnedPosition).init(n.vertexCount)),d=0;d<n.vertexCount;d++){for(h.get(d,p),c.get(d,y),l.get(d,_),y[3]=1-y[0]-y[1]-y[2],fr.set(v,0,0,0),m=0;m<4;m++)_[m]>=0&&y[m]>1e-4&&(fr.transformMat4(g,p,f[_[m]]),fr.scaleAndAdd(v,v,g,y[m]));x.set(d,v)}}for(d=0;d<u.length;d+=3){var T=u[d],b=u[d+1],E=u[d+2],w=e?n.attributes.skinnedPosition:h;if(w.get(T,pe.array),w.get(b,ye.array),w.get(E,_e.array),a=s?ve.intersectTriangle(pe,ye,_e,t.culling):ve.intersectTriangle(pe,_e,ye,t.culling)){var M=new Ar;e?Ar.copy(M,a):Ar.transformMat4(M,a,t.worldTransform),r.push(new xe.Intersection(a,M,t,[T,b,E],d/3,Ar.dist(M,this._ray.origin)))}}}}}),_intersectionCompareFunc:function(t,r){return t.distance-r.distance}});xe.Intersection=function(t,r,e,n,i,a){this.point=t,this.pointWorld=r,this.target=e,this.triangle=n,this.triangleIndex=i,this.distance=a};var Te="__dt__",be=function(){this._contextId=0,this._caches=[],this._context={}};(be.prototype={use:function(t,r){var e=this._caches;e[t]||(e[t]={},r&&(e[t]=r())),this._contextId=t,this._context=e[t]},put:function(t,r){this._context[t]=r},get:function(t){return this._context[t]},dirty:function(t){var r=Te+(t=t||"");this.put(r,!0)},dirtyAll:function(t){for(var r=Te+(t=t||""),e=this._caches,n=0;n<e.length;n++)e[n]&&(e[n][r]=!0)},fresh:function(t){var r=Te+(t=t||"");this.put(r,!1)},freshAll:function(t){for(var r=Te+(t=t||""),e=this._caches,n=0;n<e.length;n++)e[n]&&(e[n][r]=!1)},isDirty:function(t){var r=Te+(t=t||""),e=this._context;return!e.hasOwnProperty(r)||!0===e[r]},deleteContext:function(t){delete this._caches[t],this._context={}},delete:function(t){delete this._context[t]},clearAll:function(){this._caches={}},getContext:function(){return this._context},eachContext:function(t,r){Object.keys(this._caches).forEach((function(e){t&&t.call(r,e)}))},miss:function(t){return!this._context.hasOwnProperty(t)}}).constructor=be;var Ee=u.extend({width:512,height:512,type:I,format:H,wrapS:J,wrapT:J,minFilter:Y,magFilter:z,useMipmap:!0,anisotropic:1,flipY:!0,sRGB:!0,unpackAlignment:4,premultiplyAlpha:!1,dynamic:!1,NPOT:!1,__used:0},(function(){this._cache=new be}),{getWebGLTexture:function(t){var r=t.gl,e=this._cache;return e.use(t.__uid__),e.miss("webgl_texture")&&e.put("webgl_texture",r.createTexture()),this.dynamic?this.update(t):e.isDirty()&&(this.update(t),e.fresh()),e.get("webgl_texture")},bind:function(){},unbind:function(){},dirty:function(){this._cache&&this._cache.dirtyAll()},update:function(t){},updateCommon:function(t){var r=t.gl;r.pixelStorei(r.UNPACK_FLIP_Y_WEBGL,this.flipY),r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,this.premultiplyAlpha),r.pixelStorei(r.UNPACK_ALIGNMENT,this.unpackAlignment),this.format===U&&(this.useMipmap=!1);var e=t.getGLExtension("EXT_sRGB");this.format!==Ee.SRGB||e||(this.format=Ee.RGB),this.format!==Ee.SRGB_ALPHA||e||(this.format=Ee.RGBA),this.NPOT=!this.isPowerOfTwo()},getAvailableWrapS:function(){return this.NPOT?Q:this.wrapS},getAvailableWrapT:function(){return this.NPOT?Q:this.wrapT},getAvailableMinFilter:function(){var t=this.minFilter;return this.NPOT||!this.useMipmap?t===X||t===q?G:t===Y||t===j?z:t:t},getAvailableMagFilter:function(){return this.magFilter},nextHighestPowerOfTwo:function(t){--t;for(var r=1;r<32;r<<=1)t|=t>>r;return t+1},dispose:function(t){var r=this._cache;r.use(t.__uid__);var e=r.get("webgl_texture");e&&t.gl.deleteTexture(e),r.deleteContext(t.__uid__)},isRenderable:function(){},isPowerOfTwo:function(){}});Object.defineProperty(Ee.prototype,"width",{get:function(){return this._width},set:function(t){this._width=t}}),Object.defineProperty(Ee.prototype,"height",{get:function(){return this._height},set:function(t){this._height=t}}),Ee.BYTE=D,Ee.UNSIGNED_BYTE=I,Ee.SHORT=L,Ee.UNSIGNED_SHORT=O,Ee.INT=P,Ee.UNSIGNED_INT=N,Ee.FLOAT=B,Ee.HALF_FLOAT=36193,Ee.UNSIGNED_INT_24_8_WEBGL=34042,Ee.DEPTH_COMPONENT=U,Ee.DEPTH_STENCIL=et,Ee.ALPHA=F,Ee.RGB=k,Ee.RGBA=H,Ee.LUMINANCE=V,Ee.LUMINANCE_ALPHA=W,Ee.SRGB=35904,Ee.SRGB_ALPHA=35906,Ee.COMPRESSED_RGB_S3TC_DXT1_EXT=33776,Ee.COMPRESSED_RGBA_S3TC_DXT1_EXT=33777,Ee.COMPRESSED_RGBA_S3TC_DXT3_EXT=33778,Ee.COMPRESSED_RGBA_S3TC_DXT5_EXT=33779,Ee.NEAREST=G,Ee.LINEAR=z,Ee.NEAREST_MIPMAP_NEAREST=X,Ee.LINEAR_MIPMAP_NEAREST=j,Ee.NEAREST_MIPMAP_LINEAR=q,Ee.LINEAR_MIPMAP_LINEAR=Y,Ee.REPEAT=J,Ee.CLAMP_TO_EDGE=Q,Ee.MIRRORED_REPEAT=$;var we=me.extend({skeleton:null,joints:null},(function(){this.joints||(this.joints=[])}),{offsetMatrix:null,isInstancedMesh:function(){return!1},isSkinnedMesh:function(){return!!(this.skeleton&&this.joints&&this.joints.length>0)},clone:function(){var t=me.prototype.clone.call(this);return t.skeleton=this.skeleton,this.joints&&(t.joints=this.joints.slice()),t}});we.POINTS=p,we.LINES=y,we.LINE_LOOP=_,we.LINE_STRIP=v,we.TRIANGLES=g,we.TRIANGLE_STRIP=x,we.TRIANGLE_FAN=T,we.BACK=S,we.FRONT=A,we.FRONT_AND_BACK=M,we.CW=C,we.CCW=R;var Ae={isPowerOfTwo:function(t){return!(t&t-1)},nextPowerOfTwo:function(t){return t--,t|=t>>1,t|=t>>2,t|=t>>4,t|=t>>8,t|=t>>16,++t},nearestPowerOfTwo:function(t){return Math.pow(2,Math.round(Math.log(t)/Math.LN2))}},Se=Ae.isPowerOfTwo;function Me(t){return Math.pow(2,Math.round(Math.log(t)/Math.LN2))}var Ce=Ee.extend((function(){return{image:null,pixels:null,mipmaps:[],convertToPOT:!1}}),{textureType:"texture2D",update:function(t){var r=t.gl;r.bindTexture(r.TEXTURE_2D,this._cache.get("webgl_texture")),this.updateCommon(t);var e=this.format,n=this.type,i=!(!this.convertToPOT||this.mipmaps.length||!this.image||this.wrapS!==Ee.REPEAT&&this.wrapT!==Ee.REPEAT||!this.NPOT);r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_S,i?this.wrapS:this.getAvailableWrapS()),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_T,i?this.wrapT:this.getAvailableWrapT()),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MAG_FILTER,i?this.magFilter:this.getAvailableMagFilter()),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MIN_FILTER,i?this.minFilter:this.getAvailableMinFilter());var a=t.getGLExtension("EXT_texture_filter_anisotropic");(a&&this.anisotropic>1&&r.texParameterf(r.TEXTURE_2D,a.TEXTURE_MAX_ANISOTROPY_EXT,this.anisotropic),36193===n)&&(t.getGLExtension("OES_texture_half_float")||(n=B));if(this.mipmaps.length)for(var o=this.width,s=this.height,u=0;u<this.mipmaps.length;u++){var h=this.mipmaps[u];this._updateTextureData(r,h,u,o,s,e,n,!1),o/=2,s/=2}else this._updateTextureData(r,this,0,this.width,this.height,e,n,i),!this.useMipmap||this.NPOT&&!i||r.generateMipmap(r.TEXTURE_2D);r.bindTexture(r.TEXTURE_2D,null)},_updateTextureData:function(t,r,e,n,i,a,o,s){if(r.image){var u=r.image;s&&(this._potCanvas=function(t,r){var e=Me(t.width),n=Me(t.height);return(r=r||document.createElement("canvas")).width=e,r.height=n,r.getContext("2d").drawImage(t.image,0,0,e,n),r}(this,this._potCanvas),u=this._potCanvas),t.texImage2D(t.TEXTURE_2D,e,a,a,o,u)}else a<=Ee.COMPRESSED_RGBA_S3TC_DXT5_EXT&&a>=Ee.COMPRESSED_RGB_S3TC_DXT1_EXT?t.compressedTexImage2D(t.TEXTURE_2D,e,a,n,i,0,r.pixels):t.texImage2D(t.TEXTURE_2D,e,a,n,i,0,a,o,r.pixels)},generateMipmap:function(t){var r=t.gl;this.useMipmap&&!this.NPOT&&(r.bindTexture(r.TEXTURE_2D,this._cache.get("webgl_texture")),r.generateMipmap(r.TEXTURE_2D))},isPowerOfTwo:function(){return Se(this.width)&&Se(this.height)},isRenderable:function(){return this.image?this.image.width>0&&this.image.height>0:!(!this.width||!this.height)},bind:function(t){t.gl.bindTexture(t.gl.TEXTURE_2D,this.getWebGLTexture(t))},unbind:function(t){t.gl.bindTexture(t.gl.TEXTURE_2D,null)},load:function(t,r){var e=ht.createImage();r&&(e.crossOrigin=r);var n=this;return e.onload=function(){n.dirty(),n.trigger("success",n)},e.onerror=function(){n.trigger("error",n)},e.src=t,this.image=e,this}});function Re(t){return{byte:ht.Int8Array,ubyte:ht.Uint8Array,short:ht.Int16Array,ushort:ht.Uint16Array}[t]||ht.Float32Array}function De(t){return"attr_"+t}function Ie(t,r,e,n){switch(this.name=t,this.type=r,this.size=e,this.semantic=n||"",this.value=null,e){case 1:this.get=function(t){return this.value[t]},this.set=function(t,r){this.value[t]=r},this.copy=function(t,r){this.value[t]=this.value[t]};break;case 2:this.get=function(t,r){var e=this.value;return r[0]=e[2*t],r[1]=e[2*t+1],r},this.set=function(t,r){var e=this.value;e[2*t]=r[0],e[2*t+1]=r[1]},this.copy=function(t,r){var e=this.value;r*=2,e[t*=2]=e[r],e[t+1]=e[r+1]};break;case 3:this.get=function(t,r){var e=3*t,n=this.value;return r[0]=n[e],r[1]=n[e+1],r[2]=n[e+2],r},this.set=function(t,r){var e=3*t,n=this.value;n[e]=r[0],n[e+1]=r[1],n[e+2]=r[2]},this.copy=function(t,r){var e=this.value;r*=3,e[t*=3]=e[r],e[t+1]=e[r+1],e[t+2]=e[r+2]};break;case 4:this.get=function(t,r){var e=this.value,n=4*t;return r[0]=e[n],r[1]=e[n+1],r[2]=e[n+2],r[3]=e[n+3],r},this.set=function(t,r){var e=this.value,n=4*t;e[n]=r[0],e[n+1]=r[1],e[n+2]=r[2],e[n+3]=r[3]},this.copy=function(t,r){var e=this.value;r*=4,e[t*=4]=e[r],e[t+1]=e[r+1],e[t+2]=e[r+2],e[t+3]=e[r+3]}}}function Le(t,r,e,n,i){this.name=t,this.type=r,this.buffer=e,this.size=n,this.semantic=i,this.symbol="",this.needsRemove=!1}function Oe(t){this.buffer=t,this.count=0}Object.defineProperty(Ce.prototype,"width",{get:function(){return this.image?this.image.width:this._width},set:function(t){this.image||(this._width!==t&&this.dirty(),this._width=t)}}),Object.defineProperty(Ce.prototype,"height",{get:function(){return this.image?this.image.height:this._height},set:function(t){this.image||(this._height!==t&&this.dirty(),this._height=t)}}),Ie.prototype.init=function(t){if(!this.value||this.value.length!==t*this.size){var r=Re(this.type);this.value=new r(t*this.size)}},Ie.prototype.fromArray=function(t){var r,e=Re(this.type);if(t[0]&&t[0].length){var n=0,i=this.size;r=new e(t.length*i);for(var a=0;a<t.length;a++)for(var o=0;o<i;o++)r[n++]=t[a][o]}else r=new e(t);this.value=r},Ie.prototype.clone=function(t){return new Ie(this.name,this.type,this.size,this.semantic)};var Pe=u.extend((function(){return{attributes:{},indices:null,dynamic:!0,_enabledAttributes:null,__used:0}}),(function(){this._cache=new be,this._attributeList=Object.keys(this.attributes),this.__vaoCache={}}),{mainAttribute:"",pick:null,pickByRay:null,dirty:function(){for(var t=this.getEnabledAttributes(),r=0;r<t.length;r++)this.dirtyAttribute(t[r]);this.dirtyIndices(),this._enabledAttributes=null,this._cache.dirty("any")},dirtyIndices:function(){this._cache.dirtyAll("indices")},dirtyAttribute:function(t){this._cache.dirtyAll(De(t)),this._cache.dirtyAll("attributes")},getTriangleIndices:function(t,r){if(t<this.triangleCount&&t>=0){r||(r=[]);var e=this.indices;return r[0]=e[3*t],r[1]=e[3*t+1],r[2]=e[3*t+2],r}},setTriangleIndices:function(t,r){var e=this.indices;e[3*t]=r[0],e[3*t+1]=r[1],e[3*t+2]=r[2]},isUseIndices:function(){return!!this.indices},initIndicesFromArray:function(t){var r,e=this.vertexCount>65535?ht.Uint32Array:ht.Uint16Array;if(t[0]&&t[0].length){var n=0;r=new e(3*t.length);for(var i=0;i<t.length;i++)for(var a=0;a<3;a++)r[n++]=t[i][a]}else r=new e(t);this.indices=r},createAttribute:function(t,r,e,n){var i=new Ie(t,r,e,n);return this.attributes[t]&&this.removeAttribute(t),this.attributes[t]=i,this._attributeList.push(t),i},removeAttribute:function(t){var r=this._attributeList,e=r.indexOf(t);return e>=0&&(r.splice(e,1),delete this.attributes[t],!0)},getAttribute:function(t){return this.attributes[t]},getEnabledAttributes:function(){var t=this._enabledAttributes,r=this._attributeList;if(t)return t;for(var e=[],n=this.vertexCount,i=0;i<r.length;i++){var a=r[i],o=this.attributes[a];o.value&&o.value.length===n*o.size&&e.push(a)}return this._enabledAttributes=e,e},getBufferChunks:function(t){var r=this._cache;r.use(t.__uid__);var e=r.isDirty("attributes"),n=r.isDirty("indices");if(e||n){this._updateBuffer(t.gl,e,n);for(var i=this.getEnabledAttributes(),a=0;a<i.length;a++)r.fresh(De(i[a]));r.fresh("attributes"),r.fresh("indices")}return r.fresh("any"),r.get("chunks")},_updateBuffer:function(t,r,e){var n=this._cache,i=n.get("chunks"),a=!1;i||((i=[])[0]={attributeBuffers:[],indicesBuffer:null},n.put("chunks",i),a=!0);var o=i[0],s=o.attributeBuffers,u=o.indicesBuffer;if(r||a){var h=this.getEnabledAttributes(),c={};if(!a)for(var l=0;l<s.length;l++)c[s[l].name]=s[l];for(var f=0;f<h.length;f++){var d,m,p=h[f],y=this.attributes[p];a||(d=c[p]),m=d?d.buffer:t.createBuffer(),n.isDirty(De(p))&&(t.bindBuffer(t.ARRAY_BUFFER,m),t.bufferData(t.ARRAY_BUFFER,y.value,this.dynamic?t.DYNAMIC_DRAW:t.STATIC_DRAW)),s[f]=new Le(p,y.type,m,y.size,y.semantic)}for(l=f;l<s.length;l++)t.deleteBuffer(s[l].buffer);s.length=f}this.isUseIndices()&&(e||a)&&(u||(u=new Oe(t.createBuffer()),o.indicesBuffer=u),u.count=this.indices.length,t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,u.buffer),t.bufferData(t.ELEMENT_ARRAY_BUFFER,this.indices,this.dynamic?t.DYNAMIC_DRAW:t.STATIC_DRAW))},dispose:function(t){var r=this._cache;r.use(t.__uid__);var e=r.get("chunks");if(e)for(var n=0;n<e.length;n++){for(var i=e[n],a=0;a<i.attributeBuffers.length;a++){var o=i.attributeBuffers[a];t.gl.deleteBuffer(o.buffer)}i.indicesBuffer&&t.gl.deleteBuffer(i.indicesBuffer.buffer)}if(this.__vaoCache){var s=t.getGLExtension("OES_vertex_array_object");for(var u in this.__vaoCache){var h=this.__vaoCache[u].vao;h&&s.deleteVertexArrayOES(h)}}this.__vaoCache={},r.deleteContext(t.__uid__)}});Object.defineProperty&&(Object.defineProperty(Pe.prototype,"vertexCount",{enumerable:!1,get:function(){var t=this.attributes[this.mainAttribute];return t||(t=this.attributes[this._attributeList[0]]),t&&t.value?t.value.length/t.size:0}}),Object.defineProperty(Pe.prototype,"triangleCount",{enumerable:!1,get:function(){var t=this.indices;return t?t.length/3:0}})),Pe.STATIC_DRAW=E,Pe.DYNAMIC_DRAW=w,Pe.STREAM_DRAW=b,Pe.AttributeBuffer=Le,Pe.IndicesBuffer=Oe,Pe.Attribute=Ie;var Ne=fr.create,Be=fr.add,Ue=fr.set,Fe=Pe.Attribute,ke=Pe.extend((function(){return{attributes:{position:new Fe("position","float",3,"POSITION"),texcoord0:new Fe("texcoord0","float",2,"TEXCOORD_0"),texcoord1:new Fe("texcoord1","float",2,"TEXCOORD_1"),normal:new Fe("normal","float",3,"NORMAL"),tangent:new Fe("tangent","float",4,"TANGENT"),color:new Fe("color","float",4,"COLOR"),weight:new Fe("weight","float",3,"WEIGHT"),joint:new Fe("joint","float",4,"JOINT"),barycentric:new Fe("barycentric","float",3,null)},boundingBox:null}}),{mainAttribute:"position",updateBoundingBox:function(){var t=this.boundingBox;t||(t=this.boundingBox=new he);var r=this.attributes.position.value;if(r&&r.length){var e=t.min,n=t.max,i=e.array,a=n.array;fr.set(i,r[0],r[1],r[2]),fr.set(a,r[0],r[1],r[2]);for(var o=3;o<r.length;){var s=r[o++],u=r[o++],h=r[o++];s<i[0]&&(i[0]=s),u<i[1]&&(i[1]=u),h<i[2]&&(i[2]=h),s>a[0]&&(a[0]=s),u>a[1]&&(a[1]=u),h>a[2]&&(a[2]=h)}e._dirty=!0,n._dirty=!0}},generateVertexNormals:function(){if(this.vertexCount){var t=this.indices,r=this.attributes,e=r.position.value,n=r.normal.value;if(n&&n.length===e.length)for(var i=0;i<n.length;i++)n[i]=0;else n=r.normal.value=new ht.Float32Array(e.length);for(var a,o,s,u=Ne(),h=Ne(),c=Ne(),l=Ne(),f=Ne(),d=Ne(),m=t?t.length:this.vertexCount,p=0;p<m;){t?(a=t[p++],o=t[p++],s=t[p++]):(a=p++,o=p++,s=p++),Ue(u,e[3*a],e[3*a+1],e[3*a+2]),Ue(h,e[3*o],e[3*o+1],e[3*o+2]),Ue(c,e[3*s],e[3*s+1],e[3*s+2]),fr.sub(l,u,h),fr.sub(f,h,c),fr.cross(d,l,f);for(i=0;i<3;i++)n[3*a+i]=n[3*a+i]+d[i],n[3*o+i]=n[3*o+i]+d[i],n[3*s+i]=n[3*s+i]+d[i]}for(i=0;i<n.length;)Ue(d,n[i],n[i+1],n[i+2]),fr.normalize(d,d),n[i++]=d[0],n[i++]=d[1],n[i++]=d[2];this.dirty()}},generateFaceNormals:function(){if(this.vertexCount){this.isUniqueVertex()||this.generateUniqueVertex();var t=this.indices,r=this.attributes,e=r.position.value,n=r.normal.value,i=Ne(),a=Ne(),o=Ne(),s=Ne(),u=Ne(),h=Ne();n||(n=r.normal.value=new Float32Array(e.length));for(var c,l,f,d=t?t.length:this.vertexCount,m=0;m<d;){t?(c=t[m++],l=t[m++],f=t[m++]):(c=m++,l=m++,f=m++),Ue(i,e[3*c],e[3*c+1],e[3*c+2]),Ue(a,e[3*l],e[3*l+1],e[3*l+2]),Ue(o,e[3*f],e[3*f+1],e[3*f+2]),fr.sub(s,i,a),fr.sub(u,a,o),fr.cross(h,s,u),fr.normalize(h,h);for(var p=0;p<3;p++)n[3*c+p]=h[p],n[3*l+p]=h[p],n[3*f+p]=h[p]}this.dirty()}},generateTangents:function(){if(this.vertexCount){var t=this.vertexCount,r=this.attributes;r.tangent.value||(r.tangent.value=new Float32Array(4*t));var e=r.texcoord0.value,n=r.position.value,i=r.tangent.value,a=r.normal.value;if(e){for(var o=[],s=[],u=0;u<t;u++)o[u]=[0,0,0],s[u]=[0,0,0];var h,c,l,f=[0,0,0],d=[0,0,0],m=this.indices,p=m?m.length:this.vertexCount;for(u=0;u<p;){m?(h=m[u++],c=m[u++],l=m[u++]):(h=u++,c=u++,l=u++);var y=e[2*h],_=e[2*c],v=e[2*l],g=e[2*h+1],x=e[2*c+1],T=e[2*l+1],b=n[3*h],E=n[3*c],w=n[3*l],A=n[3*h+1],S=n[3*c+1],M=n[3*l+1],C=n[3*h+2],R=E-b,D=w-b,I=S-A,L=M-A,O=n[3*c+2]-C,P=n[3*l+2]-C,N=_-y,B=v-y,U=x-g,F=T-g,k=1/(N*F-U*B);f[0]=(F*R-U*D)*k,f[1]=(F*I-U*L)*k,f[2]=(F*O-U*P)*k,d[0]=(N*D-B*R)*k,d[1]=(N*L-B*I)*k,d[2]=(N*P-B*O)*k,Be(o[h],o[h],f),Be(o[c],o[c],f),Be(o[l],o[l],f),Be(s[h],s[h],d),Be(s[c],s[c],d),Be(s[l],s[l],d)}var H=Ne(),V=Ne(),W=Ne();for(u=0;u<t;u++){W[0]=a[3*u],W[1]=a[3*u+1],W[2]=a[3*u+2];var G=o[u];fr.scale(H,W,fr.dot(W,G)),fr.sub(H,G,H),fr.normalize(H,H),fr.cross(V,W,G),i[4*u]=H[0],i[4*u+1]=H[1],i[4*u+2]=H[2],i[4*u+3]=fr.dot(V,s[u])<0?-1:1}this.dirty()}}},isUniqueVertex:function(){return!this.isUseIndices()||this.vertexCount===this.indices.length},generateUniqueVertex:function(){if(this.vertexCount&&this.indices){this.indices.length>65535&&(this.indices=new ht.Uint32Array(this.indices));for(var t=this.attributes,r=this.indices,e=this.getEnabledAttributes(),n={},i=0;i<e.length;i++){n[u=e[i]]=t[u].value,t[u].init(this.indices.length)}for(var a=0,o=0;o<r.length;o++){var s=r[o];for(i=0;i<e.length;i++)for(var u,h=t[u=e[i]].value,c=t[u].size,l=0;l<c;l++)h[a*c+l]=n[u][s*c+l];r[o]=a,a++}this.dirty()}},generateBarycentric:function(){if(this.vertexCount){this.isUniqueVertex()||this.generateUniqueVertex();var t=this.attributes,r=t.barycentric.value,e=this.indices;if(!r||r.length!==3*e.length){r=t.barycentric.value=new Float32Array(3*e.length);for(var n=0;n<(e?e.length:this.vertexCount/3);)for(var i=0;i<3;i++){r[3*(e?e[n++]:3*n+i)+i]=1}this.dirty()}}},applyTransform:function(t){var r=this.attributes,e=r.position.value,n=r.normal.value,i=r.tangent.value;t=t.array;var a=lr.create();lr.invert(a,t),lr.transpose(a,a);var o=fr.transformMat4,s=fr.forEach;s(e,3,0,null,o,t),n&&s(n,3,0,null,o,a),i&&s(i,4,0,null,o,a),this.boundingBox&&this.updateBoundingBox()},dispose:function(t){var r=this._cache;r.use(t.__uid__);var e=r.get("chunks");if(e)for(var n=0;n<e.length;n++){for(var i=e[n],a=0;a<i.attributeBuffers.length;a++){var o=i.attributeBuffers[a];t.gl.deleteBuffer(o.buffer)}i.indicesBuffer&&t.gl.deleteBuffer(i.indicesBuffer.buffer)}if(this.__vaoCache){var s=t.getGLExtension("OES_vertex_array_object");for(var u in this.__vaoCache){var h=this.__vaoCache[u].vao;h&&s.deleteVertexArrayOES(h)}}this.__vaoCache={},r.deleteContext(t.__uid__)}});ke.STATIC_DRAW=Pe.STATIC_DRAW,ke.DYNAMIC_DRAW=Pe.DYNAMIC_DRAW,ke.STREAM_DRAW=Pe.STREAM_DRAW,ke.AttributeBuffer=Pe.AttributeBuffer,ke.IndicesBuffer=Pe.IndicesBuffer,ke.Attribute=Fe;var He="uniform vec3 ",Ve="uniform float ",We="@export clay.header.",Ge="@end",ze=":unconfigurable;";const Xe=[We+"directional_light",He+"directionalLightDirection[DIRECTIONAL_LIGHT_COUNT]"+ze,He+"directionalLightColor[DIRECTIONAL_LIGHT_COUNT]"+ze,Ge,We+"ambient_light",He+"ambientLightColor[AMBIENT_LIGHT_COUNT]"+ze,Ge,We+"ambient_sh_light",He+"ambientSHLightColor[AMBIENT_SH_LIGHT_COUNT]"+ze,He+"ambientSHLightCoefficients[AMBIENT_SH_LIGHT_COUNT * 9]"+ze,"vec3 calcAmbientSHLight(int idx, vec3 N) {\n int offset = 9 * idx;\n return ambientSHLightCoefficients[0]\n + ambientSHLightCoefficients[1] * N.x\n + ambientSHLightCoefficients[2] * N.y\n + ambientSHLightCoefficients[3] * N.z\n + ambientSHLightCoefficients[4] * N.x * N.z\n + ambientSHLightCoefficients[5] * N.z * N.y\n + ambientSHLightCoefficients[6] * N.y * N.x\n + ambientSHLightCoefficients[7] * (3.0 * N.z * N.z - 1.0)\n + ambientSHLightCoefficients[8] * (N.x * N.x - N.y * N.y);\n}",Ge,We+"ambient_cubemap_light",He+"ambientCubemapLightColor[AMBIENT_CUBEMAP_LIGHT_COUNT]"+ze,"uniform samplerCube ambientCubemapLightCubemap[AMBIENT_CUBEMAP_LIGHT_COUNT]"+ze,"uniform sampler2D ambientCubemapLightBRDFLookup[AMBIENT_CUBEMAP_LIGHT_COUNT]"+ze,Ge,We+"point_light",He+"pointLightPosition[POINT_LIGHT_COUNT]"+ze,Ve+"pointLightRange[POINT_LIGHT_COUNT]"+ze,He+"pointLightColor[POINT_LIGHT_COUNT]"+ze,Ge,We+"spot_light",He+"spotLightPosition[SPOT_LIGHT_COUNT]"+ze,He+"spotLightDirection[SPOT_LIGHT_COUNT]"+ze,Ve+"spotLightRange[SPOT_LIGHT_COUNT]"+ze,Ve+"spotLightUmbraAngleCosine[SPOT_LIGHT_COUNT]"+ze,Ve+"spotLightPenumbraAngleCosine[SPOT_LIGHT_COUNT]"+ze,Ve+"spotLightFalloffFactor[SPOT_LIGHT_COUNT]"+ze,He+"spotLightColor[SPOT_LIGHT_COUNT]"+ze,Ge].join("\n");sr.import(Xe);var je,qe,Ye,Ke,Ze=de.extend((function(){return{color:[1,1,1],intensity:1,castShadow:!0,shadowResolution:512,group:0}}),{type:"",clone:function(){var t=de.prototype.clone.call(this);return t.color=Array.prototype.slice.call(this.color),t.intensity=this.intensity,t.castShadow=this.castShadow,t.shadowResolution=this.shadowResolution,t}}),Je=function(t,r){this.normal=t||new Ar(0,1,0),this.distance=r||0};Je.prototype={constructor:Je,distanceToPoint:function(t){return fr.dot(t.array,this.normal.array)-this.distance},projectPoint:function(t,r){r||(r=new Ar);var e=this.distanceToPoint(t);return fr.scaleAndAdd(r.array,t.array,this.normal.array,-e),r._dirty=!0,r},normalize:function(){var t=1/fr.len(this.normal.array);fr.scale(this.normal.array,t),this.distance*=t},intersectFrustum:function(t){for(var r=t.vertices,e=this.normal.array,n=fr.dot(r[0].array,e)>this.distance,i=1;i<8;i++)if(fr.dot(r[i].array,e)>this.distance!=n)return!0},intersectLine:(Ke=fr.create(),function(t,r,e){var n=this.distanceToPoint(t),i=this.distanceToPoint(r);if(n>0&&i>0||n<0&&i<0)return null;var a=this.normal.array,o=this.distance,s=t.array;fr.sub(Ke,r.array,t.array),fr.normalize(Ke,Ke);var u=fr.dot(a,Ke);if(0===u)return null;e||(e=new Ar);var h=(fr.dot(a,s)-o)/u;return fr.scaleAndAdd(e.array,s,Ke,-h),e._dirty=!0,e}),applyTransform:(je=lr.create(),qe=Fr.create(),Ye=Fr.create(),Ye[3]=1,function(t){t=t.array,fr.scale(Ye,this.normal.array,this.distance),Fr.transformMat4(Ye,Ye,t),this.distance=fr.dot(Ye,this.normal.array),lr.invert(je,t),lr.transpose(je,je),qe[3]=0,fr.copy(qe,this.normal.array),Fr.transformMat4(qe,qe,je),fr.copy(this.normal.array,qe)}),copy:function(t){fr.copy(this.normal.array,t.normal.array),this.normal._dirty=!0,this.distance=t.distance},clone:function(){var t=new Je;return t.copy(this),t}};var Qe,$e=fr.set,tn=fr.copy,rn=fr.transformMat4,en=Math.min,nn=Math.max,an=function(){this.planes=[];for(var t=0;t<6;t++)this.planes.push(new Je);this.boundingBox=new he,this.vertices=[];for(t=0;t<8;t++)this.vertices[t]=fr.fromValues(0,0,0)};an.prototype={setFromProjection:function(t){var r=this.planes,e=t.array,n=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],h=e[6],c=e[7],l=e[8],f=e[9],d=e[10],m=e[11],p=e[12],y=e[13],_=e[14],v=e[15];$e(r[0].normal.array,o-n,c-s,m-l),r[0].distance=-(v-p),r[0].normalize(),$e(r[1].normal.array,o+n,c+s,m+l),r[1].distance=-(v+p),r[1].normalize(),$e(r[2].normal.array,o+i,c+u,m+f),r[2].distance=-(v+y),r[2].normalize(),$e(r[3].normal.array,o-i,c-u,m-f),r[3].distance=-(v-y),r[3].normalize(),$e(r[4].normal.array,o-a,c-h,m-d),r[4].distance=-(v-_),r[4].normalize(),$e(r[5].normal.array,o+a,c+h,m+d),r[5].distance=-(v+_),r[5].normalize();var g=this.boundingBox,x=this.vertices;if(0===v){var T=u/n,b=-_/(d-1),E=-_/(d+1),w=-E/u,A=-b/u;g.min.set(-w*T,-w,E),g.max.set(w*T,w,b),$e(x[0],-w*T,-w,E),$e(x[1],-w*T,w,E),$e(x[2],w*T,-w,E),$e(x[3],w*T,w,E),$e(x[4],-A*T,-A,b),$e(x[5],-A*T,A,b),$e(x[6],A*T,-A,b),$e(x[7],A*T,A,b)}else{var S=(-1-p)/n,M=(1-p)/n,C=(1-y)/u,R=(-1-y)/u,D=(-1-_)/d,I=(1-_)/d;g.min.set(Math.min(S,M),Math.min(R,C),Math.min(I,D)),g.max.set(Math.max(M,S),Math.max(C,R),Math.max(D,I));var L=g.min.array,O=g.max.array;$e(x[0],L[0],L[1],L[2]),$e(x[1],L[0],O[1],L[2]),$e(x[2],O[0],L[1],L[2]),$e(x[3],O[0],O[1],L[2]),$e(x[4],L[0],L[1],O[2]),$e(x[5],L[0],O[1],O[2]),$e(x[6],O[0],L[1],O[2]),$e(x[7],O[0],O[1],O[2])}},getTransformedBoundingBox:(Qe=fr.create(),function(t,r){var e=this.vertices,n=r.array,i=t.min,a=t.max,o=i.array,s=a.array,u=e[0];rn(Qe,u,n),tn(o,Qe),tn(s,Qe);for(var h=1;h<8;h++)u=e[h],rn(Qe,u,n),o[0]=en(Qe[0],o[0]),o[1]=en(Qe[1],o[1]),o[2]=en(Qe[2],o[2]),s[0]=nn(Qe[0],s[0]),s[1]=nn(Qe[1],s[1]),s[2]=nn(Qe[2],s[2]);return i._dirty=!0,a._dirty=!0,t})};var on,sn=de.extend((function(){return{projectionMatrix:new Kr,invProjectionMatrix:new Kr,viewMatrix:new Kr,frustum:new an}}),(function(){this.update(!0)}),{update:function(t){de.prototype.update.call(this,t),Kr.invert(this.viewMatrix,this.worldTransform),this.updateProjectionMatrix(),Kr.invert(this.invProjectionMatrix,this.projectionMatrix),this.frustum.setFromProjection(this.projectionMatrix)},setViewMatrix:function(t){Kr.copy(this.viewMatrix,t),Kr.invert(this.worldTransform,t),this.decomposeWorldTransform()},decomposeProjectionMatrix:function(){},setProjectionMatrix:function(t){Kr.copy(this.projectionMatrix,t),Kr.invert(this.invProjectionMatrix,t),this.decomposeProjectionMatrix()},updateProjectionMatrix:function(){},castRay:(on=Fr.create(),function(t,r){var e=void 0!==r?r:new Ur,n=t.array[0],i=t.array[1];return Fr.set(on,n,i,-1,1),Fr.transformMat4(on,on,this.invProjectionMatrix.array),Fr.transformMat4(on,on,this.worldTransform.array),fr.scale(e.origin.array,on,1/on[3]),Fr.set(on,n,i,1,1),Fr.transformMat4(on,on,this.invProjectionMatrix.array),Fr.transformMat4(on,on,this.worldTransform.array),fr.scale(on,on,1/on[3]),fr.sub(e.direction.array,on,e.origin.array),fr.normalize(e.direction.array,e.direction.array),e.direction._dirty=!0,e.origin._dirty=!0,e})}),un=lr.create(),hn=lr.create(),cn={};function ln(t){var r=[],e=Object.keys(t);e.sort();for(var n=0;n<e.length;n++){var i=e[n];r.push(i+" "+t[i])}var a=r.join("\n");if(cn[a])return cn[a];var o=s.genGUID();return cn[a]=o,o}function fn(){this.opaque=[],this.transparent=[],this._opaqueCount=0,this._transparentCount=0}fn.prototype.startCount=function(){this._opaqueCount=0,this._transparentCount=0},fn.prototype.add=function(t,r){r?this.transparent[this._transparentCount++]=t:this.opaque[this._opaqueCount++]=t},fn.prototype.endCount=function(){this.transparent.length=this._transparentCount,this.opaque.length=this._opaqueCount};var dn,mn,pn=de.extend((function(){return{material:null,lights:[],viewBoundingBoxLastFrame:new he,shadowUniforms:{},_cameraList:[],_lightUniforms:{},_previousLightNumber:{},_lightNumber:{},_lightProgramKeys:{},_nodeRepository:{},_renderLists:new ft(20)}}),(function(){this._scene=this}),{addToScene:function(t){t instanceof sn?(this._cameraList.length,this._cameraList.push(t)):t instanceof Ze&&this.lights.push(t),t.name&&(this._nodeRepository[t.name]=t)},removeFromScene:function(t){var r;t instanceof sn?(r=this._cameraList.indexOf(t))>=0&&this._cameraList.splice(r,1):t instanceof Ze&&(r=this.lights.indexOf(t))>=0&&this.lights.splice(r,1),t.name&&delete this._nodeRepository[t.name]},getNode:function(t){return this._nodeRepository[t]},setMainCamera:function(t){var r=this._cameraList.indexOf(t);r>=0&&this._cameraList.splice(r,1),this._cameraList.unshift(t)},getMainCamera:function(){return this._cameraList[0]},getLights:function(){return this.lights},updateLights:function(){var t=this.lights;this._previousLightNumber=this._lightNumber;for(var r={},e=0;e<t.length;e++){var n=t[e];if(!n.invisible){var i=n.group;r[i]||(r[i]={}),r[i][n.type]=r[i][n.type]||0,r[i][n.type]++}}for(var a in this._lightNumber=r,r)this._lightProgramKeys[a]=ln(r[a]);this._updateLightUniforms()},cloneNode:function(t){var r=t.clone(),e={};return function t(r,n){e[r.__uid__]=n;for(var i=0;i<r._children.length;i++){t(r._children[i],n._children[i])}}(t,r),r.traverse((function(t){t.skeleton&&(t.skeleton=t.skeleton.clone(e)),t.material&&(t.material=t.material.clone())})),r},updateRenderList:function(t,r){var e=t.__uid__,n=this._renderLists.get(e);n||(n=new fn,this._renderLists.put(e,n)),n.startCount(),r&&(this.viewBoundingBoxLastFrame.min.set(Infinity,Infinity,Infinity),this.viewBoundingBoxLastFrame.max.set(-Infinity,-Infinity,-Infinity));var i=this.material&&this.material.transparent||!1;return this._doUpdateRenderList(this,t,i,n,r),n.endCount(),n},getRenderList:function(t){return this._renderLists.get(t.__uid__)},_doUpdateRenderList:function(t,r,e,n,i){if(!t.invisible)for(var a=0;a<t._children.length;a++){var o=t._children[a];if(o.isRenderable()){var s=o.isSkinnedMesh()?un:o.worldTransform.array,u=o.geometry;lr.multiplyAffine(hn,r.viewMatrix.array,s),(i&&!u.boundingBox||!this.isFrustumCulled(o,r,hn))&&n.add(o,o.material.transparent||e)}o._children.length>0&&this._doUpdateRenderList(o,r,e,n,i)}},isFrustumCulled:(dn=new he,mn=new Kr,function(t,r,e){var n=t.boundingBox;if(n||(n=t.skeleton&&t.skeleton.boundingBox?t.skeleton.boundingBox:t.geometry.boundingBox),!n)return!1;if(mn.array=e,dn.transformFrom(n,mn),t.castShadow&&this.viewBoundingBoxLastFrame.union(dn),t.frustumCulling){if(!dn.intersectBoundingBox(r.frustum.boundingBox))return!0;mn.array=r.projectionMatrix.array,dn.max.array[2]>0&&dn.min.array[2]<0&&(dn.max.array[2]=-1e-20),dn.applyProjection(mn);var i=dn.min.array,a=dn.max.array;if(a[0]<-1||i[0]>1||a[1]<-1||i[1]>1||a[2]<-1||i[2]>1)return!0}return!1}),_updateLightUniforms:function(){var t=this.lights;t.sort(yn);var r=this._lightUniforms;for(var e in r)for(var n in r[e])r[e][n].value.length=0;for(var i=0;i<t.length;i++){var a=t[i];if(!a.invisible){e=a.group;for(var n in a.uniformTemplates){var o=a.uniformTemplates[n],s=o.value(a);if(null!=s){r[e]||(r[e]={}),r[e][n]||(r[e][n]={type:"",value:[]});var u=r[e][n];switch(u.type=o.type+"v",o.type){case"1i":case"1f":case"t":u.value.push(s);break;case"2f":case"3f":case"4f":for(var h=0;h<s.length;h++)u.value.push(s[h])}}}}}},getLightGroups:function(){var t=[];for(var r in this._lightNumber)t.push(r);return t},getNumberChangedLightGroups:function(){var t=[];for(var r in this._lightNumber)this.isLightNumberChanged(r)&&t.push(r);return t},isLightNumberChanged:function(t){var r=this._previousLightNumber,e=this._lightNumber;for(var n in e[t]){if(!r[t])return!0;if(e[t][n]!==r[t][n])return!0}for(var n in r[t]){if(!e[t])return!0;if(e[t][n]!==r[t][n])return!0}return!1},getLightsNumbers:function(t){return this._lightNumber[t]},getProgramKey:function(t){return this._lightProgramKeys[t]},setLightUniforms:function(){function t(t,r,e){for(var n in t){var i=t[n];if("tv"===i.type){if(!r.hasUniform(n))continue;for(var a=[],o=0;o<i.value.length;o++){var s=i.value[o],u=r.takeCurrentTextureSlot(e,s);a.push(u)}r.setUniform(e.gl,"1iv",n,a)}else r.setUniform(e.gl,i.type,n,i.value)}}return function(r,e,n){t(this._lightUniforms[e],r,n),t(this.shadowUniforms,r,n)}}(),dispose:function(){this.material=null,this._opaqueList=[],this._transparentList=[],this.lights=[],this._lightUniforms={},this._lightNumber={},this._nodeRepository={}}});function yn(t,r){if(r.castShadow&&!t.castShadow)return!0}var _n=Ae.isPowerOfTwo,vn=["px","nx","py","ny","pz","nz"],gn=Ee.extend((function(){return{image:{px:null,nx:null,py:null,ny:null,pz:null,nz:null},pixels:{px:null,nx:null,py:null,ny:null,pz:null,nz:null},mipmaps:[]}}),{textureType:"textureCube",update:function(t){var r=t.gl;r.bindTexture(r.TEXTURE_CUBE_MAP,this._cache.get("webgl_texture")),this.updateCommon(t);var e=this.format,n=this.type;r.texParameteri(r.TEXTURE_CUBE_MAP,r.TEXTURE_WRAP_S,this.getAvailableWrapS()),r.texParameteri(r.TEXTURE_CUBE_MAP,r.TEXTURE_WRAP_T,this.getAvailableWrapT()),r.texParameteri(r.TEXTURE_CUBE_MAP,r.TEXTURE_MAG_FILTER,this.getAvailableMagFilter()),r.texParameteri(r.TEXTURE_CUBE_MAP,r.TEXTURE_MIN_FILTER,this.getAvailableMinFilter());var i=t.getGLExtension("EXT_texture_filter_anisotropic");(i&&this.anisotropic>1&&r.texParameterf(r.TEXTURE_CUBE_MAP,i.TEXTURE_MAX_ANISOTROPY_EXT,this.anisotropic),36193===n)&&(t.getGLExtension("OES_texture_half_float")||(n=B));if(this.mipmaps.length)for(var a=this.width,o=this.height,s=0;s<this.mipmaps.length;s++){var u=this.mipmaps[s];this._updateTextureData(r,u,s,a,o,e,n),a/=2,o/=2}else this._updateTextureData(r,this,0,this.width,this.height,e,n),!this.NPOT&&this.useMipmap&&r.generateMipmap(r.TEXTURE_CUBE_MAP);r.bindTexture(r.TEXTURE_CUBE_MAP,null)},_updateTextureData:function(t,r,e,n,i,a,o){for(var s=0;s<6;s++){var u=vn[s],h=r.image&&r.image[u];h?t.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+s,e,a,a,o,h):t.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+s,e,a,n,i,0,a,o,r.pixels&&r.pixels[u])}},generateMipmap:function(t){var r=t.gl;this.useMipmap&&!this.NPOT&&(r.bindTexture(r.TEXTURE_CUBE_MAP,this._cache.get("webgl_texture")),r.generateMipmap(r.TEXTURE_CUBE_MAP))},bind:function(t){t.gl.bindTexture(t.gl.TEXTURE_CUBE_MAP,this.getWebGLTexture(t))},unbind:function(t){t.gl.bindTexture(t.gl.TEXTURE_CUBE_MAP,null)},isPowerOfTwo:function(){return this.image.px?_n(this.image.px.width)&&_n(this.image.px.height):_n(this.width)&&_n(this.height)},isRenderable:function(){return this.image.px?xn(this.image.px)&&xn(this.image.nx)&&xn(this.image.py)&&xn(this.image.ny)&&xn(this.image.pz)&&xn(this.image.nz):!(!this.width||!this.height)},load:function(t,r){var e=0,n=this;return s.each(t,(function(t,i){var a=ht.createImage();r&&(a.crossOrigin=r),a.onload=function(){0===--e&&(n.dirty(),n.trigger("success",n))},a.onerror=function(){e--},e++,a.src=t,n.image[i]=a})),this}});function xn(t){return t.width>0&&t.height>0}Object.defineProperty(gn.prototype,"width",{get:function(){return this.image&&this.image.px?this.image.px.width:this._width},set:function(t){this.image&&this.image.px||(this._width!==t&&this.dirty(),this._width=t)}}),Object.defineProperty(gn.prototype,"height",{get:function(){return this.image&&this.image.px?this.image.px.height:this._height},set:function(t){this.image&&this.image.px||(this._height!==t&&this.dirty(),this._height=t)}});var Tn=sn.extend({fov:50,aspect:1,near:.1,far:2e3},{updateProjectionMatrix:function(){var t=this.fov/180*Math.PI;this.projectionMatrix.perspective(t,this.aspect,this.near,this.far)},decomposeProjectionMatrix:function(){var t=this.projectionMatrix.array,r=2*Math.atan(1/t[5]);this.fov=r/Math.PI*180,this.aspect=t[5]/t[0],this.near=t[14]/(t[10]-1),this.far=t[14]/(t[10]+1)},clone:function(){var t=sn.prototype.clone.call(this);return t.fov=this.fov,t.aspect=this.aspect,t.near=this.near,t.far=this.far,t}}),bn="framebuffer",En="renderbuffer",wn=En+"_width",An=En+"_height",Sn=En+"_attached",Mn="depthtexture_attached",Cn=tt,Rn=rt,Dn=it,In=nt,Ln=u.extend({depthBuffer:!0,viewport:null,_width:0,_height:0,_textures:null,_boundRenderer:null},(function(){this._cache=new be,this._textures={}}),{getTextureWidth:function(){return this._width},getTextureHeight:function(){return this._height},bind:function(t){if(!t.__currentFrameBuffer||t.__currentFrameBuffer!==this){t.__currentFrameBuffer=this;var r=t.gl;r.bindFramebuffer(Cn,this._getFrameBufferGL(t)),this._boundRenderer=t;var e=this._cache;e.put("viewport",t.viewport);var n,i,a=!1;for(var o in this._textures){a=!0;var s=this._textures[o];s&&(n=s.texture.width,i=s.texture.height,this._doAttach(t,s.texture,o,s.target))}this._width=n,this._height=i,!a&&this.depthBuffer,this.viewport?t.setViewport(this.viewport):t.setViewport(0,0,n,i,1);var u=e.get("attached_textures");if(u)for(var o in u)if(!this._textures[o]){var h=u[o];this._doDetach(r,o,h)}if(!e.get(Mn)&&this.depthBuffer){e.miss(En)&&e.put(En,r.createRenderbuffer());var c=e.get(En);n===e.get(wn)&&i===e.get(An)||(r.bindRenderbuffer(Rn,c),r.renderbufferStorage(Rn,r.DEPTH_COMPONENT16,n,i),e.put(wn,n),e.put(An,i),r.bindRenderbuffer(Rn,null)),e.get(Sn)||(r.framebufferRenderbuffer(Cn,Dn,Rn,c),e.put(Sn,!0))}}},unbind:function(t){t.__currentFrameBuffer=null,t.gl.bindFramebuffer(Cn,null),this._boundRenderer=null,this._cache.use(t.__uid__);var r=this._cache.get("viewport");r&&t.setViewport(r),this.updateMipmap(t)},updateMipmap:function(t){var r=t.gl;for(var e in this._textures){var n=this._textures[e];if(n){var i=n.texture;if(!i.NPOT&&i.useMipmap&&i.minFilter===Ee.LINEAR_MIPMAP_LINEAR){var a="textureCube"===i.textureType?Z:K;r.bindTexture(a,i.getWebGLTexture(t)),r.generateMipmap(a),r.bindTexture(a,null)}}}},checkStatus:function(t){return t.checkFramebufferStatus(Cn)},_getFrameBufferGL:function(t){var r=this._cache;return r.use(t.__uid__),r.miss(bn)&&r.put(bn,t.gl.createFramebuffer()),r.get(bn)},attach:function(t,r,e){if(!t.width)throw new Error("The texture attached to color buffer is not a valid.");r=r||In,e=e||K;var n,i=this._boundRenderer;if(i&&i.gl){var a=this._cache;a.use(i.__uid__),n=a.get("attached_textures")}var o=this._textures[r];if(!o||o.target!==e||o.texture!==t||!n||null==n[r]){var s=!0;i&&(s=this._doAttach(i,t,r,e),this.viewport||i.setViewport(0,0,t.width,t.height,1)),s&&(this._textures[r]=this._textures[r]||{},this._textures[r].texture=t,this._textures[r].target=e)}},_doAttach:function(t,r,e,n){var i=t.gl,a=r.getWebGLTexture(t),o=this._cache.get("attached_textures");if(o&&o[e]){var s=o[e];if(s.texture===r&&s.target===n)return}var u=!0;if(((e=+e)===Dn||e===ot)&&(t.getGLExtension("WEBGL_depth_texture")||(u=!1),r.format!==U&&r.format!==et&&(u=!1),u)){var h=this._cache.get(En);h&&(i.framebufferRenderbuffer(Cn,Dn,Rn,null),i.deleteRenderbuffer(h),this._cache.put(En,!1)),this._cache.put(Sn,!1),this._cache.put(Mn,!0)}return i.framebufferTexture2D(Cn,e,n,a,0),o||(o={},this._cache.put("attached_textures",o)),o[e]=o[e]||{},o[e].texture=r,o[e].target=n,u},_doDetach:function(t,r,e){t.framebufferTexture2D(Cn,r,e,null,0);var n=this._cache.get("attached_textures");n&&n[r]&&(n[r]=null),r!==Dn&&r!==ot||this._cache.put(Mn,!1)},detach:function(t,r){(this._textures[t]=null,this._boundRenderer)&&(this._cache.use(this._boundRenderer.__uid__),this._doDetach(this._boundRenderer.gl,t,r))},dispose:function(t){var r=t.gl,e=this._cache;e.use(t.__uid__);var n=e.get(En);n&&r.deleteRenderbuffer(n);var i=e.get(bn);i&&r.deleteFramebuffer(i),e.deleteContext(t.__uid__),this._textures={}}});Ln.DEPTH_ATTACHMENT=Dn,Ln.COLOR_ATTACHMENT0=In,Ln.STENCIL_ATTACHMENT=at,Ln.DEPTH_STENCIL_ATTACHMENT=ot;var On=["px","nx","py","ny","pz","nz"],Pn=u.extend((function(){var t={position:new Ar,far:1e3,near:.1,texture:null,shadowMapPass:null},r=t._cameras={px:new Tn({fov:90}),nx:new Tn({fov:90}),py:new Tn({fov:90}),ny:new Tn({fov:90}),pz:new Tn({fov:90}),nz:new Tn({fov:90})};return r.px.lookAt(Ar.POSITIVE_X,Ar.NEGATIVE_Y),r.nx.lookAt(Ar.NEGATIVE_X,Ar.NEGATIVE_Y),r.py.lookAt(Ar.POSITIVE_Y,Ar.POSITIVE_Z),r.ny.lookAt(Ar.NEGATIVE_Y,Ar.NEGATIVE_Z),r.pz.lookAt(Ar.POSITIVE_Z,Ar.NEGATIVE_Y),r.nz.lookAt(Ar.NEGATIVE_Z,Ar.NEGATIVE_Y),t._frameBuffer=new Ln,t}),{getCamera:function(t){return this._cameras[t]},render:function(t,r,e){var n=t.gl;e||r.update();for(var i=this.texture.width,a=2*Math.atan(i/(i-.5))/Math.PI*180,o=0;o<6;o++){var s=On[o],u=this._cameras[s];if(Ar.copy(u.position,this.position),u.far=this.far,u.near=this.near,u.fov=a,this.shadowMapPass){u.update();var h=r.getBoundingBox();h.applyTransform(u.viewMatrix),r.viewBoundingBoxLastFrame.copy(h),this.shadowMapPass.render(t,r,u,!0)}this._frameBuffer.attach(this.texture,n.COLOR_ATTACHMENT0,n.TEXTURE_CUBE_MAP_POSITIVE_X+o),this._frameBuffer.bind(t),t.render(r,u,!0),this._frameBuffer.unbind(t)}},dispose:function(t){this._frameBuffer.dispose(t)}}),Nn=ke.extend({dynamic:!1,widthSegments:1,heightSegments:1},(function(){this.build()}),{build:function(){for(var t=this.heightSegments,r=this.widthSegments,e=this.attributes,n=[],i=[],a=[],o=[],s=0;s<=t;s++)for(var u=s/t,h=0;h<=r;h++){var c=h/r;if(n.push([2*c-1,2*u-1,0]),i&&i.push([c,u]),a&&a.push([0,0,1]),h<r&&s<t){var l=h+s*(r+1);o.push([l,l+1,l+r+1]),o.push([l+r+1,l+1,l+r+2])}}e.position.fromArray(n),e.texcoord0.fromArray(i),e.normal.fromArray(a),this.initIndicesFromArray(o),this.boundingBox=new he,this.boundingBox.min.set(-1,-1,0),this.boundingBox.max.set(1,1,0)}}),Bn=new Kr,Un=ke.extend({dynamic:!1,widthSegments:1,heightSegments:1,depthSegments:1,inside:!1},(function(){this.build()}),{build:function(){var t={px:Fn("px",this.depthSegments,this.heightSegments),nx:Fn("nx",this.depthSegments,this.heightSegments),py:Fn("py",this.widthSegments,this.depthSegments),ny:Fn("ny",this.widthSegments,this.depthSegments),pz:Fn("pz",this.widthSegments,this.heightSegments),nz:Fn("nz",this.widthSegments,this.heightSegments)},r=["position","texcoord0","normal"],e=0,n=0;for(var i in t)e+=t[i].vertexCount,n+=t[i].indices.length;for(var a=0;a<r.length;a++)this.attributes[r[a]].init(e);this.indices=new ht.Uint16Array(n);var o=0,s=0;for(var i in t){var u=t[i];for(a=0;a<r.length;a++)for(var h=r[a],c=u.attributes[h].value,l=u.attributes[h].size,f="normal"===h,d=0;d<c.length;d++){var m=c[d];this.inside&&f&&(m=-m),this.attributes[h].value[d+l*s]=m}var p=u.indices.length;for(d=0;d<u.indices.length;d++)this.indices[d+o]=s+u.indices[this.inside?p-d-1:d];o+=u.indices.length,s+=u.vertexCount}this.boundingBox=new he,this.boundingBox.max.set(1,1,1),this.boundingBox.min.set(-1,-1,-1)}});function Fn(t,r,e){Bn.identity();var n=new Nn({widthSegments:r,heightSegments:e});switch(t){case"px":Kr.translate(Bn,Bn,Ar.POSITIVE_X),Kr.rotateY(Bn,Bn,Math.PI/2);break;case"nx":Kr.translate(Bn,Bn,Ar.NEGATIVE_X),Kr.rotateY(Bn,Bn,-Math.PI/2);break;case"py":Kr.translate(Bn,Bn,Ar.POSITIVE_Y),Kr.rotateX(Bn,Bn,-Math.PI/2);break;case"ny":Kr.translate(Bn,Bn,Ar.NEGATIVE_Y),Kr.rotateX(Bn,Bn,Math.PI/2);break;case"pz":Kr.translate(Bn,Bn,Ar.POSITIVE_Z);break;case"nz":Kr.translate(Bn,Bn,Ar.NEGATIVE_Z),Kr.rotateY(Bn,Bn,Math.PI)}return n.applyTransform(Bn),n}sr.import("@export clay.skybox.vertex\n#define SHADER_NAME skybox\nuniform mat4 world : WORLD;\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nattribute vec3 position : POSITION;\nvarying vec3 v_WorldPosition;\nvoid main()\n{\n v_WorldPosition = (world * vec4(position, 1.0)).xyz;\n gl_Position = worldViewProjection * vec4(position, 1.0);\n}\n@end\n@export clay.skybox.fragment\n#define PI 3.1415926\nuniform mat4 viewInverse : VIEWINVERSE;\n#ifdef EQUIRECTANGULAR\nuniform sampler2D environmentMap;\n#else\nuniform samplerCube environmentMap;\n#endif\nuniform float lod: 0.0;\nvarying vec3 v_WorldPosition;\n@import clay.util.rgbm\n@import clay.util.srgb\n@import clay.util.ACES\nvoid main()\n{\n vec3 eyePos = viewInverse[3].xyz;\n vec3 V = normalize(v_WorldPosition - eyePos);\n#ifdef EQUIRECTANGULAR\n float phi = acos(V.y);\n float theta = atan(-V.x, V.z) + PI * 0.5;\n vec2 uv = vec2(theta / 2.0 / PI, phi / PI);\n vec4 texel = decodeHDR(texture2D(environmentMap, fract(uv)));\n#else\n #if defined(LOD) || defined(SUPPORT_TEXTURE_LOD)\n vec4 texel = decodeHDR(textureCubeLodEXT(environmentMap, V, lod));\n #else\n vec4 texel = decodeHDR(textureCube(environmentMap, V));\n #endif\n#endif\n#ifdef SRGB_DECODE\n texel = sRGBToLinear(texel);\n#endif\n#ifdef TONEMAPPING\n texel.rgb = ACESToneMapping(texel.rgb);\n#endif\n#ifdef SRGB_ENCODE\n texel = linearTosRGB(texel);\n#endif\n gl_FragColor = encodeHDR(vec4(texel.rgb, 1.0));\n}\n@end");var kn=we.extend((function(){var t=new sr({vertex:sr.source("clay.skybox.vertex"),fragment:sr.source("clay.skybox.fragment")}),r=new It({shader:t,depthMask:!1});return{scene:null,geometry:new Un,material:r,environmentMap:null,culling:!1,_dummyCamera:new Tn}}),(function(){var t=this.scene;t&&this.attachScene(t),this.environmentMap&&this.setEnvironmentMap(this.environmentMap)}),{attachScene:function(t){this.scene&&this.detachScene(),t.skybox=this,this.scene=t,t.on("beforerender",this._beforeRenderScene,this)},detachScene:function(){this.scene&&(this.scene.off("beforerender",this._beforeRenderScene),this.scene.skybox=null),this.scene=null},dispose:function(t){this.detachScene(),this.geometry.dispose(t)},setEnvironmentMap:function(t){"texture2D"===t.textureType?(this.material.define("EQUIRECTANGULAR"),t.minFilter=Ee.LINEAR):this.material.undefine("EQUIRECTANGULAR"),this.material.set("environmentMap",t)},getEnvironmentMap:function(){return this.material.get("environmentMap")},_beforeRenderScene:function(t,r,e){this.renderSkybox(t,e)},renderSkybox:function(t,r){var e=this._dummyCamera;e.aspect=t.getViewportAspect(),e.fov=r.fov||50,e.updateProjectionMatrix(),Kr.invert(e.invProjectionMatrix,e.projectionMatrix),e.worldTransform.copy(r.worldTransform),e.viewMatrix.copy(r.viewMatrix),this.position.copy(r.getWorldPosition()),this.update(),t.gl.disable(t.gl.BLEND),this.material.get("lod")>0?this.material.define("fragment","LOD"):this.material.undefine("fragment","LOD"),t.renderPass([this],e)}});function Hn(t){return t.charCodeAt(0)+(t.charCodeAt(1)<<8)+(t.charCodeAt(2)<<16)+(t.charCodeAt(3)<<24)}var Vn=Hn("DXT1"),Wn=Hn("DXT3"),Gn=Hn("DXT5"),zn=function(t,r){var e=new Int32Array(t,0,31);if(542327876!==e[0])return null;if(4&!e(20))return null;var n,i,a=e(21),o=e[4],s=e[3],u=512&e[28],h=131072&e[2];switch(a){case Vn:n=8,i=Ee.COMPRESSED_RGB_S3TC_DXT1_EXT;break;case Wn:n=16,i=Ee.COMPRESSED_RGBA_S3TC_DXT3_EXT;break;case Gn:n=16,i=Ee.COMPRESSED_RGBA_S3TC_DXT5_EXT;break;default:return null}var c=e[1]+4,l=u?6:1,f=1;h&&(f=Math.max(1,e[7]));for(var d=[],m=0;m<l;m++){var p=o,y=s;d[m]=new Ce({width:p,height:y,format:i});for(var _=[],v=0;v<f;v++){var g=Math.max(4,p)/4*Math.max(4,y)/4*n,x=new Uint8Array(t,c,g);c+=g,p*=.5,y*=.5,_[v]=x}d[m].pixels=_[0],h&&(d[m].mipmaps=_)}if(!r)return d[0];r.width=d[0].width,r.height=d[0].height,r.format=d[0].format,r.pixels=d[0].pixels,r.mipmaps=d[0].mipmaps},Xn=String.fromCharCode;function jn(t,r,e,n){if(t[3]>0){var i=Math.pow(2,t[3]-128-8+n);r[e+0]=t[0]*i,r[e+1]=t[1]*i,r[e+2]=t[2]*i}else r[e+0]=0,r[e+1]=0,r[e+2]=0;return r[e+3]=1,r}function qn(t,r,e,n){for(var i,a,o=0,s=0,u=n;u>0;)if(t[s][0]=r[e++],t[s][1]=r[e++],t[s][2]=r[e++],t[s][3]=r[e++],1===t[s][0]&&1===t[s][1]&&1===t[s][2]){for(var h=t[s][3]<<o>>>0;h>0;h--)i=t[s-1],(a=t[s])[0]=i[0],a[1]=i[1],a[2]=i[2],a[3]=i[3],s++,u--;o+=8}else s++,u--,o=0;return e}function Yn(t,r,e,n){if(n<8|n>32767)return qn(t,r,e,n);if(2!=(i=r[e++]))return qn(t,r,e-1,n);if(t[0][1]=r[e++],t[0][2]=r[e++],i=r[e++],(t[0][2]<<8>>>0|i)>>>0!==n)return null;for(var i=0;i<4;i++)for(var a=0;a<n;){var o=r[e++];if(o>128){o=(127&o)>>>0;for(var s=r[e++];o--;)t[a++][i]=s}else for(;o--;)t[a++][i]=r[e++]}return e}var Kn=function(t,r,e){null==e&&(e=0);var n=new Uint8Array(t),i=n.length;if("#?"===function(t,r,e){for(var n="",i=r;i<e;i++)n+=Xn(t[i]);return n}(n,0,2)){for(var a=2;a<i&&("\n"!==Xn(n[a])||"\n"!==Xn(n[a+1]));a++);if(!(a>=i)){a+=2;for(var o="";a<i;a++){var s=Xn(n[a]);if("\n"===s)break;o+=s}var u=o.split(" "),h=parseInt(u[1]),c=parseInt(u[3]);if(c&&h){for(var l=a+1,f=[],d=0;d<c;d++){f[d]=[];for(var m=0;m<4;m++)f[d][m]=0}for(var p=new Float32Array(c*h*4),y=0,_=0;_<h;_++){if(!(l=Yn(f,n,l,c)))return null;for(d=0;d<c;d++)jn(f[d],p,y,e),y+=4}return r||(r=new Ce),r.width=c,r.height=h,r.pixels=p,r.type=Ee.FLOAT,r}}}},Zn={loadTexture:function(t,r,e,n){var i;if("function"==typeof r?(n=e=r,r={}):r=r||{},"string"==typeof t){if(t.match(/.hdr$/)||"hdr"===r.fileType)return i=new Ce({width:0,height:0,sRGB:!1}),Zn._fetchTexture(t,(function(t){Kn(t,i,r.exposure),i.dirty(),e&&e(i)}),n),i;t.match(/.dds$/)||"dds"===r.fileType?(i=new Ce({width:0,height:0}),Zn._fetchTexture(t,(function(t){zn(t,i),i.dirty(),e&&e(i)}),n)):((i=new Ce).load(t),i.success(e),i.error(n))}else"object"==typeof t&&void 0!==t.px&&((i=new gn).load(t),i.success(e),i.error(n));return i},loadPanorama:function(t,r,e,n,i,a){var o=this;"function"==typeof n?(a=i=n,n={}):n=n||{},Zn.loadTexture(r,n,(function(r){r.flipY=n.flipY||!1,o.panoramaToCubeMap(t,r,e,n),r.dispose(t),i&&i(e)}),a)},panoramaToCubeMap:function(t,r,e,n){var i=new Pn,a=new kn({scene:new pn});return a.setEnvironmentMap(r),(n=n||{}).encodeRGBM&&a.material.define("fragment","RGBM_ENCODE"),e.sRGB=r.sRGB,i.texture=e,i.render(t,a.scene),i.texture=null,i.dispose(t),e},heightToNormal:function(t,r){var e=document.createElement("canvas"),n=e.width=t.width,i=e.height=t.height,a=e.getContext("2d");a.drawImage(t,0,0,n,i),r=r||!1;for(var o=a.getImageData(0,0,n,i),s=a.createImageData(n,i),u=0;u<o.data.length;u+=4){if(r){var h=o.data[u],c=o.data[u+1],l=o.data[u+2];if(Math.abs(h-c)+Math.abs(c-l)>20)return t}var f,d,m,p;u%(4*n)==0?(f=o.data[u],m=o.data[u+4]):u%(4*n)==4*(n-1)?(f=o.data[u-4],m=o.data[u]):(f=o.data[u-4],m=o.data[u+4]),u<4*n?(d=o.data[u],p=o.data[u+4*n]):u>n*(i-1)*4?(d=o.data[u-4*n],p=o.data[u]):(d=o.data[u-4*n],p=o.data[u+4*n]),s.data[u]=f-m+127,s.data[u+1]=d-p+127,s.data[u+2]=255,s.data[u+3]=255}return a.putImageData(s,0,0),e},isHeightImage:function(t,r,e){if(!t||!t.width||!t.height)return!1;var n=document.createElement("canvas"),i=n.getContext("2d"),a=r||32;e=e||20,n.width=n.height=a,i.drawImage(t,0,0,a,a);for(var o=i.getImageData(0,0,a,a),s=0;s<o.data.length;s+=4){var u=o.data[s],h=o.data[s+1],c=o.data[s+2];if(Math.abs(u-h)+Math.abs(h-c)>e)return!1}return!0},_fetchTexture:function(t,r,e){ht.request.get({url:t,responseType:"arraybuffer",onload:r,onerror:e})},createChessboard:function(t,r,e,n){t=t||512,r=r||64,e=e||"black",n=n||"white";var i=Math.ceil(t/r),a=document.createElement("canvas");a.width=t,a.height=t;var o=a.getContext("2d");o.fillStyle=n,o.fillRect(0,0,t,t),o.fillStyle=e;for(var s=0;s<i;s++)for(var u=0;u<i;u++){(u%2?s%2:s%2-1)&&o.fillRect(s*r,u*r,r,r)}return new Ce({image:a,anisotropic:8})},createBlank:function(t){var r=document.createElement("canvas");r.width=1,r.height=1;var e=r.getContext("2d");return e.fillStyle=t,e.fillRect(0,0,1,1),new Ce({image:r})}},Jn=sn.extend({left:-1,right:1,near:-1,far:1,top:1,bottom:-1},{updateProjectionMatrix:function(){this.projectionMatrix.ortho(this.left,this.right,this.bottom,this.top,this.near,this.far)},decomposeProjectionMatrix:function(){var t=this.projectionMatrix.array;this.left=(-1-t[12])/t[0],this.right=(1-t[12])/t[0],this.top=(1-t[13])/t[5],this.bottom=(-1-t[13])/t[5],this.near=-(-1-t[14])/t[10],this.far=-(1-t[14])/t[10]},clone:function(){var t=sn.prototype.clone.call(this);return t.left=this.left,t.right=this.right,t.near=this.near,t.far=this.far,t.top=this.top,t.bottom=this.bottom,t}});sr.import("\n@export clay.compositor.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nattribute vec3 position : POSITION;\nattribute vec2 texcoord : TEXCOORD_0;\nvarying vec2 v_Texcoord;\nvoid main()\n{\n v_Texcoord = texcoord;\n gl_Position = worldViewProjection * vec4(position, 1.0);\n}\n@end");var Qn=new we({geometry:new Nn,frustumCulling:!1}),$n=new Jn,ti=u.extend((function(){return{fragment:"",outputs:null,material:null,blendWithPrevious:!1,clearColor:!1,clearDepth:!0}}),(function(){var t=new sr(sr.source("clay.compositor.vertex"),this.fragment),r=new It({shader:t});r.enableTexturesAll(),this.material=r}),{setUniform:function(t,r){this.material.setUniform(t,r)},getUniform:function(t){var r=this.material.uniforms[t];if(r)return r.value},attachOutput:function(t,r){this.outputs||(this.outputs={}),r=r||nt,this.outputs[r]=t},detachOutput:function(t){for(var r in this.outputs)this.outputs[r]===t&&(this.outputs[r]=null)},bind:function(t,r){if(this.outputs)for(var e in this.outputs){var n=this.outputs[e];n&&r.attach(n,e)}r&&r.bind(t)},unbind:function(t,r){r.unbind(t)},render:function(t,r){var e=t.gl;if(r){this.bind(t,r);var n=t.getGLExtension("EXT_draw_buffers");if(n&&this.outputs){var i=[];for(var a in this.outputs)(a=+a)>=e.COLOR_ATTACHMENT0&&a<=e.COLOR_ATTACHMENT0+8&&i.push(a);n.drawBuffersEXT(i)}}this.trigger("beforerender",this,t);var o=this.clearDepth?e.DEPTH_BUFFER_BIT:0;if(e.depthMask(!0),this.clearColor){o|=e.COLOR_BUFFER_BIT,e.colorMask(!0,!0,!0,!0);var s=this.clearColor;Array.isArray(s)&&e.clearColor(s[0],s[1],s[2],s[3])}e.clear(o),this.blendWithPrevious?(e.enable(e.BLEND),this.material.transparent=!0):(e.disable(e.BLEND),this.material.transparent=!1),this.renderQuad(t),this.trigger("afterrender",this,t),r&&this.unbind(t,r)},renderQuad:function(t){Qn.material=this.material,t.renderPass([Qn],$n)},dispose:function(t){}});var ri={},ei=["px","nx","py","ny","pz","nz"];ri.prefilterEnvironmentMap=function(t,r,e,n,i){i&&n||(n=ri.generateNormalDistribution(),i=ri.integrateBRDF(t,n));var a=(e=e||{}).width||64,o=e.height||64,s=e.type||r.type,u=new gn({width:a,height:o,type:s,flipY:!1,mipmaps:[]});u.isPowerOfTwo();var h=Math.min(a,o),c=Math.log(h)/Math.log(2)+1,l=new It({shader:new sr({vertex:sr.source("clay.skybox.vertex"),fragment:"#define SHADER_NAME prefilter\n#define SAMPLE_NUMBER 1024\n#define PI 3.14159265358979\nuniform mat4 viewInverse : VIEWINVERSE;\nuniform samplerCube environmentMap;\nuniform sampler2D normalDistribution;\nuniform float roughness : 0.5;\nvarying vec2 v_Texcoord;\nvarying vec3 v_WorldPosition;\n@import clay.util.rgbm\nvec3 importanceSampleNormal(float i, float roughness, vec3 N) {\n vec3 H = texture2D(normalDistribution, vec2(roughness, i)).rgb;\n vec3 upVector = abs(N.y) > 0.999 ? vec3(1.0, 0.0, 0.0) : vec3(0.0, 1.0, 0.0);\n vec3 tangentX = normalize(cross(N, upVector));\n vec3 tangentZ = cross(N, tangentX);\n return normalize(tangentX * H.x + N * H.y + tangentZ * H.z);\n}\nvoid main() {\n vec3 eyePos = viewInverse[3].xyz;\n vec3 V = normalize(v_WorldPosition - eyePos);\n vec3 N = V;\n vec3 prefilteredColor = vec3(0.0);\n float totalWeight = 0.0;\n float fMaxSampleNumber = float(SAMPLE_NUMBER);\n for (int i = 0; i < SAMPLE_NUMBER; i++) {\n vec3 H = importanceSampleNormal(float(i) / fMaxSampleNumber, roughness, N);\n vec3 L = reflect(-V, H);\n float NoL = clamp(dot(N, L), 0.0, 1.0);\n if (NoL > 0.0) {\n prefilteredColor += decodeHDR(textureCube(environmentMap, L)).rgb * NoL;\n totalWeight += NoL;\n }\n }\n gl_FragColor = encodeHDR(vec4(prefilteredColor / totalWeight, 1.0));\n}\n"})});l.set("normalDistribution",n),e.encodeRGBM&&l.define("fragment","RGBM_ENCODE"),e.decodeRGBM&&l.define("fragment","RGBM_DECODE");var f,d=new pn;if("texture2D"===r.textureType){var m=new gn({width:a,height:o,type:s===Ee.FLOAT?Ee.HALF_FLOAT:s});Zn.panoramaToCubeMap(t,r,m,{encodeRGBM:e.decodeRGBM}),r=m}(f=new kn({scene:d,material:l})).material.set("environmentMap",r);var p=new Pn({texture:u});e.encodeRGBM&&(s=u.type=Ee.UNSIGNED_BYTE);for(var y=new Ce({width:a,height:o,type:s}),_=new Ln({depthBuffer:!1}),v=ht[s===Ee.UNSIGNED_BYTE?"Uint8Array":"Float32Array"],g=0;g<c;g++){u.mipmaps[g]={pixels:{}},f.material.set("roughness",g/(c-1));for(var x=y.width,T=2*Math.atan(x/(x-.5))/Math.PI*180,b=0;b<ei.length;b++){var E=new v(y.width*y.height*4);_.attach(y),_.bind(t);var w=p.getCamera(ei[b]);w.fov=T,t.render(d,w),t.gl.readPixels(0,0,y.width,y.height,Ee.RGBA,s,E),_.unbind(t),u.mipmaps[g].pixels[ei[b]]=E}y.width/=2,y.height/=2,y.dirty()}return _.dispose(t),y.dispose(t),f.dispose(t),n.dispose(t),{environmentMap:u,brdfLookup:i,normalDistribution:n,maxMipmapLevel:c}},ri.integrateBRDF=function(t,r){r=r||ri.generateNormalDistribution();var e=new Ln({depthBuffer:!1}),n=new ti({fragment:"#define SAMPLE_NUMBER 1024\n#define PI 3.14159265358979\nuniform sampler2D normalDistribution;\nuniform vec2 viewportSize : [512, 256];\nconst vec3 N = vec3(0.0, 0.0, 1.0);\nconst float fSampleNumber = float(SAMPLE_NUMBER);\nvec3 importanceSampleNormal(float i, float roughness, vec3 N) {\n vec3 H = texture2D(normalDistribution, vec2(roughness, i)).rgb;\n vec3 upVector = abs(N.y) > 0.999 ? vec3(1.0, 0.0, 0.0) : vec3(0.0, 1.0, 0.0);\n vec3 tangentX = normalize(cross(N, upVector));\n vec3 tangentZ = cross(N, tangentX);\n return normalize(tangentX * H.x + N * H.y + tangentZ * H.z);\n}\nfloat G_Smith(float roughness, float NoV, float NoL) {\n float k = roughness * roughness / 2.0;\n float G1V = NoV / (NoV * (1.0 - k) + k);\n float G1L = NoL / (NoL * (1.0 - k) + k);\n return G1L * G1V;\n}\nvoid main() {\n vec2 uv = gl_FragCoord.xy / viewportSize;\n float NoV = uv.x;\n float roughness = uv.y;\n vec3 V;\n V.x = sqrt(1.0 - NoV * NoV);\n V.y = 0.0;\n V.z = NoV;\n float A = 0.0;\n float B = 0.0;\n for (int i = 0; i < SAMPLE_NUMBER; i++) {\n vec3 H = importanceSampleNormal(float(i) / fSampleNumber, roughness, N);\n vec3 L = reflect(-V, H);\n float NoL = clamp(L.z, 0.0, 1.0);\n float NoH = clamp(H.z, 0.0, 1.0);\n float VoH = clamp(dot(V, H), 0.0, 1.0);\n if (NoL > 0.0) {\n float G = G_Smith(roughness, NoV, NoL);\n float G_Vis = G * VoH / (NoH * NoV);\n float Fc = pow(1.0 - VoH, 5.0);\n A += (1.0 - Fc) * G_Vis;\n B += Fc * G_Vis;\n }\n }\n gl_FragColor = vec4(vec2(A, B) / fSampleNumber, 0.0, 1.0);\n}\n"}),i=new Ce({width:512,height:256,type:Ee.HALF_FLOAT,wrapS:Ee.CLAMP_TO_EDGE,wrapT:Ee.CLAMP_TO_EDGE,minFilter:Ee.NEAREST,magFilter:Ee.NEAREST,useMipmap:!1});return n.setUniform("normalDistribution",r),n.setUniform("viewportSize",[512,256]),n.attachOutput(i),n.render(t,e),e.dispose(t),i},ri.generateNormalDistribution=function(t,r){for(var e=new Ce({width:t=t||256,height:r=r||1024,type:Ee.FLOAT,minFilter:Ee.NEAREST,magFilter:Ee.NEAREST,wrapS:Ee.CLAMP_TO_EDGE,wrapT:Ee.CLAMP_TO_EDGE,useMipmap:!1}),n=new Float32Array(r*t*4),i=[],a=0;a<t;a++){for(var o=a/t,s=o*o,u=0;u<r;u++){var h=(u<<16|u>>>16)>>>0;h=(((16711935&(h=((252645135&(h=((858993459&(h=((1431655765&h)<<1|(2863311530&h)>>>1)>>>0))<<2|(3435973836&h)>>>2)>>>0))<<4|(4042322160&h)>>>4)>>>0))<<8|(4278255360&h)>>>8)>>>0)/4294967296;var c=Math.sqrt((1-h)/(1+(s*s-1)*h));i[u]=c}for(u=0;u<r;u++){var l=4*(u*t+a),f=(c=i[u],Math.sqrt(1-c*c)),d=u/r,m=2*Math.PI*d;n[l]=f*Math.cos(m),n[l+1]=c,n[l+2]=f*Math.sin(m),n[l+3]=1}}return e.pixels=n,e};var ni=Ze.extend({cubemap:null,castShadow:!1,_normalDistribution:null,_brdfLookup:null},{type:"AMBIENT_CUBEMAP_LIGHT",prefilter:function(t,r){if(t.getGLExtension("EXT_shader_texture_lod")){this._brdfLookup||(this._normalDistribution=ri.generateNormalDistribution(),this._brdfLookup=ri.integrateBRDF(t,this._normalDistribution));var e=this.cubemap;if(!e.__prefiltered){var n=ri.prefilterEnvironmentMap(t,e,{encodeRGBM:!0,width:r,height:r},this._normalDistribution,this._brdfLookup);this.cubemap=n.environmentMap,this.cubemap.__prefiltered=!0,e.dispose(t)}}},getBRDFLookup:function(){return this._brdfLookup},uniformTemplates:{ambientCubemapLightColor:{type:"3f",value:function(t){var r=t.color,e=t.intensity;return[r[0]*e,r[1]*e,r[2]*e]}},ambientCubemapLightCubemap:{type:"t",value:function(t){return t.cubemap}},ambientCubemapLightBRDFLookup:{type:"t",value:function(t){return t._brdfLookup}}}}),ii=Ze.extend({castShadow:!1,coefficients:[]},(function(){this._coefficientsTmpArr=new ht.Float32Array(27)}),{type:"AMBIENT_SH_LIGHT",uniformTemplates:{ambientSHLightColor:{type:"3f",value:function(t){var r=t.color,e=t.intensity;return[r[0]*e,r[1]*e,r[2]*e]}},ambientSHLightCoefficients:{type:"3f",value:function(t){for(var r=t._coefficientsTmpArr,e=0;e<t.coefficients.length;e++)r[e]=t.coefficients[e];return r}}}}),ai={},oi=["px","nx","py","ny","pz","nz"];function si(t,r){var e=t[0],n=t[1],i=t[2];return 0===r?1:1===r?e:2===r?n:3===r?i:4===r?e*i:5===r?n*i:6===r?e*n:7===r?3*i*i-1:e*e-n*n}var ui={px:[2,1,0,-1,-1,1],nx:[2,1,0,1,-1,-1],py:[0,2,1,1,-1,-1],ny:[0,2,1,1,1,1],pz:[0,1,2,-1,-1,-1],nz:[0,1,2,1,-1,1]};ai.projectEnvironmentMap=function(t,r,e){var n;(e=e||{}).lod=e.lod||0;var i=new pn,a=64;"texture2D"===r.textureType||(a=r.image&&r.image.px?r.image.px.width:r.width),n=new kn({scene:i,environmentMap:r});var o=Math.ceil(a/Math.pow(2,e.lod)),s=Math.ceil(a/Math.pow(2,e.lod)),u=new Ce({width:o,height:s}),h=new Ln;n.material.define("fragment","RGBM_ENCODE"),e.decodeRGBM&&n.material.define("fragment","RGBM_DECODE"),n.material.set("lod",e.lod);for(var c=new Pn({texture:u}),l={},f=0;f<oi.length;f++){l[oi[f]]=new Uint8Array(o*s*4);var d=c.getCamera(oi[f]);d.fov=90,h.attach(u),h.bind(t),t.render(i,d),t.gl.readPixels(0,0,o,s,Ee.RGBA,Ee.UNSIGNED_BYTE,l[oi[f]]),h.unbind(t)}return n.dispose(t),h.dispose(t),u.dispose(t),function(t,r,e,n){for(var i=new ht.Float32Array(27),a=fr.create(),o=fr.create(),s=fr.create(),u=0;u<9;u++){for(var h=fr.create(),c=0;c<oi.length;c++){for(var l=r[oi[c]],f=fr.create(),d=0,m=0,p=ui[oi[c]],y=0;y<n;y++)for(var _=0;_<e;_++){a[0]=_/(e-1)*2-1,a[1]=y/(n-1)*2-1,a[2]=-1,fr.normalize(a,a),s[0]=a[p[0]]*p[3],s[1]=a[p[1]]*p[4],s[2]=a[p[2]]*p[5],o[0]=l[m++]/255,o[1]=l[m++]/255,o[2]=l[m++]/255;var v=l[m++]/255*8.12;o[0]*=v,o[1]*=v,o[2]*=v,fr.scaleAndAdd(f,f,o,si(s,u)*-a[2]),d+=-a[2]}fr.scaleAndAdd(h,h,f,1/d)}i[3*u]=h[0]/6,i[3*u+1]=h[1]/6,i[3*u+2]=h[2]/6}return i}(0,l,o,s)};var hi=ke.extend({dynamic:!1,widthSegments:40,heightSegments:20,phiStart:0,phiLength:2*Math.PI,thetaStart:0,thetaLength:Math.PI,radius:1},(function(){this.build()}),{build:function(){var t=this.heightSegments,r=this.widthSegments,e=this.attributes.position,n=this.attributes.texcoord0,i=this.attributes.normal,a=(r+1)*(t+1);e.init(a),n.init(a),i.init(a);var o,s,u,h,c,l,f,d,m,p,y,_=a>65535?Uint32Array:Uint16Array,v=this.indices=new _(r*t*6),g=this.radius,x=this.phiStart,T=this.phiLength,b=this.thetaStart,E=this.thetaLength,w=[],A=[],S=0,M=1/(g=this.radius);for(f=0;f<=t;f++)for(l=0;l<=r;l++)h=l/r,c=f/t,o=-g*Math.cos(x+h*T)*Math.sin(b+c*E),s=g*Math.cos(b+c*E),u=g*Math.sin(x+h*T)*Math.sin(b+c*E),w[0]=o,w[1]=s,w[2]=u,A[0]=h,A[1]=c,e.set(S,w),n.set(S,A),w[0]*=M,w[1]*=M,w[2]*=M,i.set(S,w),S++;var C=r+1,R=0;for(f=0;f<t;f++)for(l=0;l<r;l++)m=f*C+l,d=f*C+l+1,y=(f+1)*C+l+1,p=(f+1)*C+l,v[R++]=d,v[R++]=m,v[R++]=y,v[R++]=m,v[R++]=p,v[R++]=y;this.boundingBox=new he,this.boundingBox.max.set(g,g,g),this.boundingBox.min.set(-g,-g,-g)}}),ci=Ze.extend({castShadow:!1},{type:"AMBIENT_LIGHT",uniformTemplates:{ambientLightColor:{type:"3f",value:function(t){var r=t.color,e=t.intensity;return[r[0]*e,r[1]*e,r[2]*e]}}}}),li=Ze.extend({shadowBias:.001,shadowSlopeScale:2,shadowCascade:1,cascadeSplitLogFactor:.2},{type:"DIRECTIONAL_LIGHT",uniformTemplates:{directionalLightDirection:{type:"3f",value:function(t){return t.__dir=t.__dir||new Ar,t.__dir.copy(t.worldTransform.z).normalize().negate().array}},directionalLightColor:{type:"3f",value:function(t){var r=t.color,e=t.intensity;return[r[0]*e,r[1]*e,r[2]*e]}}},clone:function(){var t=Ze.prototype.clone.call(this);return t.shadowBias=this.shadowBias,t.shadowSlopeScale=this.shadowSlopeScale,t}}),fi=Ze.extend({range:100,castShadow:!1},{type:"POINT_LIGHT",uniformTemplates:{pointLightPosition:{type:"3f",value:function(t){return t.getWorldPosition().array}},pointLightRange:{type:"1f",value:function(t){return t.range}},pointLightColor:{type:"3f",value:function(t){var r=t.color,e=t.intensity;return[r[0]*e,r[1]*e,r[2]*e]}}},clone:function(){var t=Ze.prototype.clone.call(this);return t.range=this.range,t}}),di=Ze.extend({range:20,umbraAngle:30,penumbraAngle:45,falloffFactor:2,shadowBias:.001,shadowSlopeScale:2},{type:"SPOT_LIGHT",uniformTemplates:{spotLightPosition:{type:"3f",value:function(t){return t.getWorldPosition().array}},spotLightRange:{type:"1f",value:function(t){return t.range}},spotLightUmbraAngleCosine:{type:"1f",value:function(t){return Math.cos(t.umbraAngle*Math.PI/180)}},spotLightPenumbraAngleCosine:{type:"1f",value:function(t){return Math.cos(t.penumbraAngle*Math.PI/180)}},spotLightFalloffFactor:{type:"1f",value:function(t){return t.falloffFactor}},spotLightDirection:{type:"3f",value:function(t){return t.__dir=t.__dir||new Ar,t.__dir.copy(t.worldTransform.z).negate().array}},spotLightColor:{type:"3f",value:function(t){var r=t.color,e=t.intensity;return[r[0]*e,r[1]*e,r[2]*e]}}},clone:function(){var t=Ze.prototype.clone.call(this);return t.range=this.range,t.umbraAngle=this.umbraAngle,t.penumbraAngle=this.penumbraAngle,t.falloffFactor=this.falloffFactor,t.shadowBias=this.shadowBias,t.shadowSlopeScale=this.shadowSlopeScale,t}}),mi=function(t,r,e,n){t=t||0,r=r||0,e=e||0,n=n||0,this.array=Fr.fromValues(t,r,e,n),this._dirty=!0};mi.prototype={constructor:mi,add:function(t){return Fr.add(this.array,this.array,t.array),this._dirty=!0,this},set:function(t,r,e,n){return this.array[0]=t,this.array[1]=r,this.array[2]=e,this.array[3]=n,this._dirty=!0,this},setArray:function(t){return this.array[0]=t[0],this.array[1]=t[1],this.array[2]=t[2],this.array[3]=t[3],this._dirty=!0,this},clone:function(){return new mi(this.x,this.y,this.z,this.w)},copy:function(t){return Fr.copy(this.array,t.array),this._dirty=!0,this},dist:function(t){return Fr.dist(this.array,t.array)},distance:function(t){return Fr.distance(this.array,t.array)},div:function(t){return Fr.div(this.array,this.array,t.array),this._dirty=!0,this},divide:function(t){return Fr.divide(this.array,this.array,t.array),this._dirty=!0,this},dot:function(t){return Fr.dot(this.array,t.array)},len:function(){return Fr.len(this.array)},length:function(){return Fr.length(this.array)},lerp:function(t,r,e){return Fr.lerp(this.array,t.array,r.array,e),this._dirty=!0,this},min:function(t){return Fr.min(this.array,this.array,t.array),this._dirty=!0,this},max:function(t){return Fr.max(this.array,this.array,t.array),this._dirty=!0,this},mul:function(t){return Fr.mul(this.array,this.array,t.array),this._dirty=!0,this},multiply:function(t){return Fr.multiply(this.array,this.array,t.array),this._dirty=!0,this},negate:function(){return Fr.negate(this.array,this.array),this._dirty=!0,this},normalize:function(){return Fr.normalize(this.array,this.array),this._dirty=!0,this},random:function(t){return Fr.random(this.array,t),this._dirty=!0,this},scale:function(t){return Fr.scale(this.array,this.array,t),this._dirty=!0,this},scaleAndAdd:function(t,r){return Fr.scaleAndAdd(this.array,this.array,t.array,r),this._dirty=!0,this},sqrDist:function(t){return Fr.sqrDist(this.array,t.array)},squaredDistance:function(t){return Fr.squaredDistance(this.array,t.array)},sqrLen:function(){return Fr.sqrLen(this.array)},squaredLength:function(){return Fr.squaredLength(this.array)},sub:function(t){return Fr.sub(this.array,this.array,t.array),this._dirty=!0,this},subtract:function(t){return Fr.subtract(this.array,this.array,t.array),this._dirty=!0,this},transformMat4:function(t){return Fr.transformMat4(this.array,this.array,t.array),this._dirty=!0,this},transformQuat:function(t){return Fr.transformQuat(this.array,this.array,t.array),this._dirty=!0,this},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}};var pi=Object.defineProperty;if(pi){var yi=mi.prototype;pi(yi,"x",{get:function(){return this.array[0]},set:function(t){this.array[0]=t,this._dirty=!0}}),pi(yi,"y",{get:function(){return this.array[1]},set:function(t){this.array[1]=t,this._dirty=!0}}),pi(yi,"z",{get:function(){return this.array[2]},set:function(t){this.array[2]=t,this._dirty=!0}}),pi(yi,"w",{get:function(){return this.array[3]},set:function(t){this.array[3]=t,this._dirty=!0}})}mi.add=function(t,r,e){return Fr.add(t.array,r.array,e.array),t._dirty=!0,t},mi.set=function(t,r,e,n,i){Fr.set(t.array,r,e,n,i),t._dirty=!0},mi.copy=function(t,r){return Fr.copy(t.array,r.array),t._dirty=!0,t},mi.distance=mi.dist=function(t,r){return Fr.distance(t.array,r.array)},mi.divide=mi.div=function(t,r,e){return Fr.divide(t.array,r.array,e.array),t._dirty=!0,t},mi.dot=function(t,r){return Fr.dot(t.array,r.array)},mi.len=function(t){return Fr.length(t.array)},mi.lerp=function(t,r,e,n){return Fr.lerp(t.array,r.array,e.array,n),t._dirty=!0,t},mi.min=function(t,r,e){return Fr.min(t.array,r.array,e.array),t._dirty=!0,t},mi.max=function(t,r,e){return Fr.max(t.array,r.array,e.array),t._dirty=!0,t},mi.multiply=mi.mul=function(t,r,e){return Fr.multiply(t.array,r.array,e.array),t._dirty=!0,t},mi.negate=function(t,r){return Fr.negate(t.array,r.array),t._dirty=!0,t},mi.normalize=function(t,r){return Fr.normalize(t.array,r.array),t._dirty=!0,t},mi.random=function(t,r){return Fr.random(t.array,r),t._dirty=!0,t},mi.scale=function(t,r,e){return Fr.scale(t.array,r.array,e),t._dirty=!0,t},mi.scaleAndAdd=function(t,r,e,n){return Fr.scaleAndAdd(t.array,r.array,e.array,n),t._dirty=!0,t},mi.squaredDistance=mi.sqrDist=function(t,r){return Fr.sqrDist(t.array,r.array)},mi.squaredLength=mi.sqrLen=function(t){return Fr.sqrLen(t.array)},mi.subtract=mi.sub=function(t,r,e){return Fr.subtract(t.array,r.array,e.array),t._dirty=!0,t},mi.transformMat4=function(t,r,e){return Fr.transformMat4(t.array,r.array,e.array),t._dirty=!0,t},mi.transformQuat=function(t,r,e){return Fr.transformQuat(t.array,r.array,e.array),t._dirty=!0,t};var _i={create:function(){var t=new Ot(4);return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t},clone:function(t){var r=new Ot(4);return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r},copy:function(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t},transpose:function(t,r){if(t===r){var e=r[1];t[1]=r[2],t[2]=e}else t[0]=r[0],t[1]=r[2],t[2]=r[1],t[3]=r[3];return t},invert:function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=e*a-i*n;return o?(o=1/o,t[0]=a*o,t[1]=-n*o,t[2]=-i*o,t[3]=e*o,t):null},adjoint:function(t,r){var e=r[0];return t[0]=r[3],t[1]=-r[1],t[2]=-r[2],t[3]=e,t},determinant:function(t){return t[0]*t[3]-t[2]*t[1]},multiply:function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=e[0],u=e[1],h=e[2],c=e[3];return t[0]=n*s+a*u,t[1]=i*s+o*u,t[2]=n*h+a*c,t[3]=i*h+o*c,t}};_i.mul=_i.multiply,_i.rotate=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=Math.sin(e),u=Math.cos(e);return t[0]=n*u+a*s,t[1]=i*u+o*s,t[2]=n*-s+a*u,t[3]=i*-s+o*u,t},_i.scale=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=e[0],u=e[1];return t[0]=n*s,t[1]=i*s,t[2]=a*u,t[3]=o*u,t},_i.frob=function(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2)+Math.pow(t[2],2)+Math.pow(t[3],2))},_i.LDU=function(t,r,e,n){return t[2]=n[2]/n[0],e[0]=n[0],e[1]=n[1],e[3]=n[3]-t[2]*e[1],[t,r,e]};var vi=function(){this.array=_i.create(),this._dirty=!0};vi.prototype={constructor:vi,setArray:function(t){for(var r=0;r<this.array.length;r++)this.array[r]=t[r];return this._dirty=!0,this},clone:function(){return(new vi).copy(this)},copy:function(t){return _i.copy(this.array,t.array),this._dirty=!0,this},adjoint:function(){return _i.adjoint(this.array,this.array),this._dirty=!0,this},determinant:function(){return _i.determinant(this.array)},identity:function(){return _i.identity(this.array),this._dirty=!0,this},invert:function(){return _i.invert(this.array,this.array),this._dirty=!0,this},mul:function(t){return _i.mul(this.array,this.array,t.array),this._dirty=!0,this},mulLeft:function(t){return _i.mul(this.array,t.array,this.array),this._dirty=!0,this},multiply:function(t){return _i.multiply(this.array,this.array,t.array),this._dirty=!0,this},multiplyLeft:function(t){return _i.multiply(this.array,t.array,this.array),this._dirty=!0,this},rotate:function(t){return _i.rotate(this.array,this.array,t),this._dirty=!0,this},scale:function(t){return _i.scale(this.array,this.array,t.array),this._dirty=!0,this},transpose:function(){return _i.transpose(this.array,this.array),this._dirty=!0,this},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}},vi.adjoint=function(t,r){return _i.adjoint(t.array,r.array),t._dirty=!0,t},vi.copy=function(t,r){return _i.copy(t.array,r.array),t._dirty=!0,t},vi.determinant=function(t){return _i.determinant(t.array)},vi.identity=function(t){return _i.identity(t.array),t._dirty=!0,t},vi.invert=function(t,r){return _i.invert(t.array,r.array),t._dirty=!0,t},vi.multiply=vi.mul=function(t,r,e){return _i.mul(t.array,r.array,e.array),t._dirty=!0,t},vi.rotate=function(t,r,e){return _i.rotate(t.array,r.array,e),t._dirty=!0,t},vi.scale=function(t,r,e){return _i.scale(t.array,r.array,e.array),t._dirty=!0,t},vi.transpose=function(t,r){return _i.transpose(t.array,r.array),t._dirty=!0,t};var gi={create:function(){var t=new Ot(6);return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},clone:function(t){var r=new Ot(6);return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r},copy:function(t,r){return t[0]=r[0],t[1]=r[1],t[2]=r[2],t[3]=r[3],t[4]=r[4],t[5]=r[5],t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},invert:function(t,r){var e=r[0],n=r[1],i=r[2],a=r[3],o=r[4],s=r[5],u=e*a-n*i;return u?(u=1/u,t[0]=a*u,t[1]=-n*u,t[2]=-i*u,t[3]=e*u,t[4]=(i*s-a*o)*u,t[5]=(n*o-e*s)*u,t):null},determinant:function(t){return t[0]*t[3]-t[1]*t[2]},multiply:function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],u=r[5],h=e[0],c=e[1],l=e[2],f=e[3],d=e[4],m=e[5];return t[0]=n*h+a*c,t[1]=i*h+o*c,t[2]=n*l+a*f,t[3]=i*l+o*f,t[4]=n*d+a*m+s,t[5]=i*d+o*m+u,t}};gi.mul=gi.multiply,gi.rotate=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],u=r[5],h=Math.sin(e),c=Math.cos(e);return t[0]=n*c+a*h,t[1]=i*c+o*h,t[2]=n*-h+a*c,t[3]=i*-h+o*c,t[4]=s,t[5]=u,t},gi.scale=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],u=r[5],h=e[0],c=e[1];return t[0]=n*h,t[1]=i*h,t[2]=a*c,t[3]=o*c,t[4]=s,t[5]=u,t},gi.translate=function(t,r,e){var n=r[0],i=r[1],a=r[2],o=r[3],s=r[4],u=r[5],h=e[0],c=e[1];return t[0]=n,t[1]=i,t[2]=a,t[3]=o,t[4]=n*h+a*c+s,t[5]=i*h+o*c+u,t},gi.frob=function(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2)+Math.pow(t[2],2)+Math.pow(t[3],2)+Math.pow(t[4],2)+Math.pow(t[5],2)+1)};var xi=function(){this.array=gi.create(),this._dirty=!0};xi.prototype={constructor:xi,setArray:function(t){for(var r=0;r<this.array.length;r++)this.array[r]=t[r];return this._dirty=!0,this},clone:function(){return(new xi).copy(this)},copy:function(t){return gi.copy(this.array,t.array),this._dirty=!0,this},determinant:function(){return gi.determinant(this.array)},identity:function(){return gi.identity(this.array),this._dirty=!0,this},invert:function(){return gi.invert(this.array,this.array),this._dirty=!0,this},mul:function(t){return gi.mul(this.array,this.array,t.array),this._dirty=!0,this},mulLeft:function(t){return gi.mul(this.array,t.array,this.array),this._dirty=!0,this},multiply:function(t){return gi.multiply(this.array,this.array,t.array),this._dirty=!0,this},multiplyLeft:function(t){return gi.multiply(this.array,t.array,this.array),this._dirty=!0,this},rotate:function(t){return gi.rotate(this.array,this.array,t),this._dirty=!0,this},scale:function(t){return gi.scale(this.array,this.array,t.array),this._dirty=!0,this},translate:function(t){return gi.translate(this.array,this.array,t.array),this._dirty=!0,this},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}},xi.copy=function(t,r){return gi.copy(t.array,r.array),t._dirty=!0,t},xi.determinant=function(t){return gi.determinant(t.array)},xi.identity=function(t){return gi.identity(t.array),t._dirty=!0,t},xi.invert=function(t,r){return gi.invert(t.array,r.array),t._dirty=!0,t},xi.multiply=xi.mul=function(t,r,e){return gi.mul(t.array,r.array,e.array),t._dirty=!0,t},xi.rotate=function(t,r,e){return gi.rotate(t.array,r.array,e),t._dirty=!0,t},xi.scale=function(t,r,e){return gi.scale(t.array,r.array,e.array),t._dirty=!0,t},xi.translate=function(t,r,e){return gi.translate(t.array,r.array,e.array),t._dirty=!0,t};var Ti=function(){this.array=kr.create(),this._dirty=!0};Ti.prototype={constructor:Ti,setArray:function(t){for(var r=0;r<this.array.length;r++)this.array[r]=t[r];return this._dirty=!0,this},adjoint:function(){return kr.adjoint(this.array,this.array),this._dirty=!0,this},clone:function(){return(new Ti).copy(this)},copy:function(t){return kr.copy(this.array,t.array),this._dirty=!0,this},determinant:function(){return kr.determinant(this.array)},fromMat2d:function(t){return kr.fromMat2d(this.array,t.array),this._dirty=!0,this},fromMat4:function(t){return kr.fromMat4(this.array,t.array),this._dirty=!0,this},fromQuat:function(t){return kr.fromQuat(this.array,t.array),this._dirty=!0,this},identity:function(){return kr.identity(this.array),this._dirty=!0,this},invert:function(){return kr.invert(this.array,this.array),this._dirty=!0,this},mul:function(t){return kr.mul(this.array,this.array,t.array),this._dirty=!0,this},mulLeft:function(t){return kr.mul(this.array,t.array,this.array),this._dirty=!0,this},multiply:function(t){return kr.multiply(this.array,this.array,t.array),this._dirty=!0,this},multiplyLeft:function(t){return kr.multiply(this.array,t.array,this.array),this._dirty=!0,this},rotate:function(t){return kr.rotate(this.array,this.array,t),this._dirty=!0,this},scale:function(t){return kr.scale(this.array,this.array,t.array),this._dirty=!0,this},translate:function(t){return kr.translate(this.array,this.array,t.array),this._dirty=!0,this},normalFromMat4:function(t){return kr.normalFromMat4(this.array,t.array),this._dirty=!0,this},transpose:function(){return kr.transpose(this.array,this.array),this._dirty=!0,this},toString:function(){return"["+Array.prototype.join.call(this.array,",")+"]"},toArray:function(){return Array.prototype.slice.call(this.array)}},Ti.adjoint=function(t,r){return kr.adjoint(t.array,r.array),t._dirty=!0,t},Ti.copy=function(t,r){return kr.copy(t.array,r.array),t._dirty=!0,t},Ti.determinant=function(t){return kr.determinant(t.array)},Ti.identity=function(t){return kr.identity(t.array),t._dirty=!0,t},Ti.invert=function(t,r){return kr.invert(t.array,r.array),t},Ti.multiply=Ti.mul=function(t,r,e){return kr.mul(t.array,r.array,e.array),t._dirty=!0,t},Ti.fromMat2d=function(t,r){return kr.fromMat2d(t.array,r.array),t._dirty=!0,t},Ti.fromMat4=function(t,r){return kr.fromMat4(t.array,r.array),t._dirty=!0,t},Ti.fromQuat=function(t,r){return kr.fromQuat(t.array,r.array),t._dirty=!0,t},Ti.normalFromMat4=function(t,r){return kr.normalFromMat4(t.array,r.array),t._dirty=!0,t},Ti.rotate=function(t,r,e){return kr.rotate(t.array,r.array,e),t._dirty=!0,t},Ti.scale=function(t,r,e){return kr.scale(t.array,r.array,e.array),t._dirty=!0,t},Ti.transpose=function(t,r){return kr.transpose(t.array,r.array),t._dirty=!0,t},Ti.translate=function(t,r,e){return kr.translate(t.array,r.array,e.array),t._dirty=!0,t};const bi="\n@export clay.util.rand\nhighp float rand(vec2 uv) {\n const highp float a = 12.9898, b = 78.233, c = 43758.5453;\n highp float dt = dot(uv.xy, vec2(a,b)), sn = mod(dt, 3.141592653589793);\n return fract(sin(sn) * c);\n}\n@end\n@export clay.util.calculate_attenuation\nuniform float attenuationFactor : 5.0;\nfloat lightAttenuation(float dist, float range)\n{\n float attenuation = 1.0;\n attenuation = dist*dist/(range*range+1.0);\n float att_s = attenuationFactor;\n attenuation = 1.0/(attenuation*att_s+1.0);\n att_s = 1.0/(att_s+1.0);\n attenuation = attenuation - att_s;\n attenuation /= 1.0 - att_s;\n return clamp(attenuation, 0.0, 1.0);\n}\n@end\n@export clay.util.edge_factor\n#ifdef SUPPORT_STANDARD_DERIVATIVES\nfloat edgeFactor(float width)\n{\n vec3 d = fwidth(v_Barycentric);\n vec3 a3 = smoothstep(vec3(0.0), d * width, v_Barycentric);\n return min(min(a3.x, a3.y), a3.z);\n}\n#else\nfloat edgeFactor(float width)\n{\n return 1.0;\n}\n#endif\n@end\n@export clay.util.encode_float\nvec4 encodeFloat(const in float depth)\n{\n const vec4 bitShifts = vec4(256.0*256.0*256.0, 256.0*256.0, 256.0, 1.0);\n const vec4 bit_mask = vec4(0.0, 1.0/256.0, 1.0/256.0, 1.0/256.0);\n vec4 res = fract(depth * bitShifts);\n res -= res.xxyz * bit_mask;\n return res;\n}\n@end\n@export clay.util.decode_float\nfloat decodeFloat(const in vec4 color)\n{\n const vec4 bitShifts = vec4(1.0/(256.0*256.0*256.0), 1.0/(256.0*256.0), 1.0/256.0, 1.0);\n return dot(color, bitShifts);\n}\n@end\n@export clay.util.float\n@import clay.util.encode_float\n@import clay.util.decode_float\n@end\n@export clay.util.rgbm_decode\nvec3 RGBMDecode(vec4 rgbm, float range) {\n return range * rgbm.rgb * rgbm.a;\n}\n@end\n@export clay.util.rgbm_encode\nvec4 RGBMEncode(vec3 color, float range) {\n if (dot(color, color) == 0.0) {\n return vec4(0.0);\n }\n vec4 rgbm;\n color /= range;\n rgbm.a = clamp(max(max(color.r, color.g), max(color.b, 1e-6)), 0.0, 1.0);\n rgbm.a = ceil(rgbm.a * 255.0) / 255.0;\n rgbm.rgb = color / rgbm.a;\n return rgbm;\n}\n@end\n@export clay.util.rgbm\n@import clay.util.rgbm_decode\n@import clay.util.rgbm_encode\nvec4 decodeHDR(vec4 color)\n{\n#if defined(RGBM_DECODE) || defined(RGBM)\n return vec4(RGBMDecode(color, 8.12), 1.0);\n#else\n return color;\n#endif\n}\nvec4 encodeHDR(vec4 color)\n{\n#if defined(RGBM_ENCODE) || defined(RGBM)\n return RGBMEncode(color.xyz, 8.12);\n#else\n return color;\n#endif\n}\n@end\n@export clay.util.srgb\nvec4 sRGBToLinear(in vec4 value) {\n return vec4(mix(pow(value.rgb * 0.9478672986 + vec3(0.0521327014), vec3(2.4)), value.rgb * 0.0773993808, vec3(lessThanEqual(value.rgb, vec3(0.04045)))), value.w);\n}\nvec4 linearTosRGB(in vec4 value) {\n return vec4(mix(pow(value.rgb, vec3(0.41666)) * 1.055 - vec3(0.055), value.rgb * 12.92, vec3(lessThanEqual(value.rgb, vec3(0.0031308)))), value.w);\n}\n@end\n@export clay.chunk.skinning_header\n#ifdef SKINNING\nattribute vec3 weight : WEIGHT;\nattribute vec4 joint : JOINT;\n#ifdef USE_SKIN_MATRICES_TEXTURE\nuniform sampler2D skinMatricesTexture : ignore;\nuniform float skinMatricesTextureSize: ignore;\nmat4 getSkinMatrix(sampler2D tex, float idx) {\n float j = idx * 4.0;\n float x = mod(j, skinMatricesTextureSize);\n float y = floor(j / skinMatricesTextureSize) + 0.5;\n vec2 scale = vec2(skinMatricesTextureSize);\n return mat4(\n texture2D(tex, vec2(x + 0.5, y) / scale),\n texture2D(tex, vec2(x + 1.5, y) / scale),\n texture2D(tex, vec2(x + 2.5, y) / scale),\n texture2D(tex, vec2(x + 3.5, y) / scale)\n );\n}\nmat4 getSkinMatrix(float idx) {\n return getSkinMatrix(skinMatricesTexture, idx);\n}\n#else\nuniform mat4 skinMatrix[JOINT_COUNT] : SKIN_MATRIX;\nmat4 getSkinMatrix(float idx) {\n return skinMatrix[int(idx)];\n}\n#endif\n#endif\n@end\n@export clay.chunk.skin_matrix\nmat4 skinMatrixWS = getSkinMatrix(joint.x) * weight.x;\nif (weight.y > 1e-4)\n{\n skinMatrixWS += getSkinMatrix(joint.y) * weight.y;\n}\nif (weight.z > 1e-4)\n{\n skinMatrixWS += getSkinMatrix(joint.z) * weight.z;\n}\nfloat weightW = 1.0-weight.x-weight.y-weight.z;\nif (weightW > 1e-4)\n{\n skinMatrixWS += getSkinMatrix(joint.w) * weightW;\n}\n@end\n@export clay.chunk.instancing_header\n#ifdef INSTANCING\nattribute vec4 instanceMat1;\nattribute vec4 instanceMat2;\nattribute vec4 instanceMat3;\n#endif\n@end\n@export clay.chunk.instancing_matrix\nmat4 instanceMat = mat4(\n vec4(instanceMat1.xyz, 0.0),\n vec4(instanceMat2.xyz, 0.0),\n vec4(instanceMat3.xyz, 0.0),\n vec4(instanceMat1.w, instanceMat2.w, instanceMat3.w, 1.0)\n);\n@end\n@export clay.util.parallax_correct\nvec3 parallaxCorrect(in vec3 dir, in vec3 pos, in vec3 boxMin, in vec3 boxMax) {\n vec3 first = (boxMax - pos) / dir;\n vec3 second = (boxMin - pos) / dir;\n vec3 further = max(first, second);\n float dist = min(further.x, min(further.y, further.z));\n vec3 fixedPos = pos + dir * dist;\n vec3 boxCenter = (boxMax + boxMin) * 0.5;\n return normalize(fixedPos - boxCenter);\n}\n@end\n@export clay.util.clamp_sample\nvec4 clampSample(const in sampler2D texture, const in vec2 coord)\n{\n#ifdef STEREO\n float eye = step(0.5, coord.x) * 0.5;\n vec2 coordClamped = clamp(coord, vec2(eye, 0.0), vec2(0.5 + eye, 1.0));\n#else\n vec2 coordClamped = clamp(coord, vec2(0.0), vec2(1.0));\n#endif\n return texture2D(texture, coordClamped);\n}\n@end\n@export clay.util.ACES\nvec3 ACESToneMapping(vec3 color)\n{\n const float A = 2.51;\n const float B = 0.03;\n const float C = 2.43;\n const float D = 0.59;\n const float E = 0.14;\n return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\n@end",Ei={vec2:Nt,vec3:fr,vec4:Fr,mat3:kr,mat4:lr};var wi=function(){this._pool={},this._allocatedTextures=[]};wi.prototype={constructor:wi,get:function(t){var r=Mi(t);this._pool.hasOwnProperty(r)||(this._pool[r]=[]);var e=this._pool[r];if(!e.length){var n=new Ce(t);return this._allocatedTextures.push(n),n}return e.pop()},put:function(t){var r=Mi(t);this._pool.hasOwnProperty(r)||(this._pool[r]=[]),this._pool[r].push(t)},clear:function(t){for(var r=0;r<this._allocatedTextures.length;r++)this._allocatedTextures[r].dispose(t);this._pool={},this._allocatedTextures=[]}};var Ai={width:512,height:512,type:I,format:H,wrapS:Q,wrapT:Q,minFilter:Y,magFilter:z,useMipmap:!0,anisotropic:1,flipY:!0,unpackAlignment:4,premultiplyAlpha:!1},Si=Object.keys(Ai);function Mi(t){s.defaultsWithPropList(t,Ai,Si),function(t){var r=(e=t.width,n=t.height,!(e&e-1||n&n-1));var e,n;t.format===U&&(t.useMipmap=!1);r&&t.useMipmap||(t.minFilter==X||t.minFilter==q?t.minFilter=G:t.minFilter!=Y&&t.minFilter!=j||(t.minFilter=z));r||(t.wrapS=Q,t.wrapT=Q)}(t);for(var r="",e=0;e<Si.length;e++){r+=t[Si[e]].toString()}return r}var Ci=["px","nx","py","ny","pz","nz"];function Ri(t,r,e){return"alphaMap"===e?t.material.get("diffuseMap"):"alphaCutoff"===e?t.material.isDefined("fragment","ALPHA_TEST")&&t.material.get("diffuseMap")&&t.material.get("alphaCutoff")||0:"uvRepeat"===e?t.material.get("uvRepeat"):"uvOffset"===e?t.material.get("uvOffset"):r.get(e)}function Di(t,r){var e=t.material,n=r.material;return e.get("diffuseMap")!==n.get("diffuseMap")||(e.get("alphaCutoff")||0)!==(n.get("alphaCutoff")||0)}sr.import("@export clay.sm.depth.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nattribute vec3 position : POSITION;\nattribute vec2 texcoord : TEXCOORD_0;\nuniform vec2 uvRepeat = vec2(1.0, 1.0);\nuniform vec2 uvOffset = vec2(0.0, 0.0);\n@import clay.chunk.skinning_header\n@import clay.chunk.instancing_header\nvarying vec4 v_ViewPosition;\nvarying vec2 v_Texcoord;\nvoid main(){\n vec4 P = vec4(position, 1.0);\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n P = skinMatrixWS * P;\n#endif\n#ifdef INSTANCING\n @import clay.chunk.instancing_matrix\n P = instanceMat * P;\n#endif\n v_ViewPosition = worldViewProjection * P;\n gl_Position = v_ViewPosition;\n v_Texcoord = texcoord * uvRepeat + uvOffset;\n}\n@end\n@export clay.sm.depth.fragment\nvarying vec4 v_ViewPosition;\nvarying vec2 v_Texcoord;\nuniform float bias : 0.001;\nuniform float slopeScale : 1.0;\nuniform sampler2D alphaMap;\nuniform float alphaCutoff: 0.0;\n@import clay.util.encode_float\nvoid main(){\n float depth = v_ViewPosition.z / v_ViewPosition.w;\n if (alphaCutoff > 0.0) {\n if (texture2D(alphaMap, v_Texcoord).a <= alphaCutoff) {\n discard;\n }\n }\n#ifdef USE_VSM\n depth = depth * 0.5 + 0.5;\n float moment1 = depth;\n float moment2 = depth * depth;\n #ifdef SUPPORT_STANDARD_DERIVATIVES\n float dx = dFdx(depth);\n float dy = dFdy(depth);\n moment2 += 0.25*(dx*dx+dy*dy);\n #endif\n gl_FragColor = vec4(moment1, moment2, 0.0, 1.0);\n#else\n #ifdef SUPPORT_STANDARD_DERIVATIVES\n float dx = dFdx(depth);\n float dy = dFdy(depth);\n depth += sqrt(dx*dx + dy*dy) * slopeScale + bias;\n #else\n depth += bias;\n #endif\n gl_FragColor = encodeFloat(depth * 0.5 + 0.5);\n#endif\n}\n@end\n@export clay.sm.debug_depth\nuniform sampler2D depthMap;\nvarying vec2 v_Texcoord;\n@import clay.util.decode_float\nvoid main() {\n vec4 tex = texture2D(depthMap, v_Texcoord);\n#ifdef USE_VSM\n gl_FragColor = vec4(tex.rgb, 1.0);\n#else\n float depth = decodeFloat(tex);\n gl_FragColor = vec4(depth, depth, depth, 1.0);\n#endif\n}\n@end\n@export clay.sm.distance.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform mat4 world : WORLD;\nattribute vec3 position : POSITION;\n@import clay.chunk.skinning_header\nvarying vec3 v_WorldPosition;\nvoid main (){\n vec4 P = vec4(position, 1.0);\n#ifdef SKINNING\n @import clay.chunk.skin_matrix\n P = skinMatrixWS * P;\n#endif\n#ifdef INSTANCING\n @import clay.chunk.instancing_matrix\n P = instanceMat * P;\n#endif\n gl_Position = worldViewProjection * P;\n v_WorldPosition = (world * P).xyz;\n}\n@end\n@export clay.sm.distance.fragment\nuniform vec3 lightPosition;\nuniform float range : 100;\nvarying vec3 v_WorldPosition;\n@import clay.util.encode_float\nvoid main(){\n float dist = distance(lightPosition, v_WorldPosition);\n#ifdef USE_VSM\n gl_FragColor = vec4(dist, dist * dist, 0.0, 0.0);\n#else\n dist = dist / range;\n gl_FragColor = encodeFloat(dist);\n#endif\n}\n@end\n@export clay.plugin.shadow_map_common\n@import clay.util.decode_float\nfloat tapShadowMap(sampler2D map, vec2 uv, float z){\n vec4 tex = texture2D(map, uv);\n return step(z, decodeFloat(tex) * 2.0 - 1.0);\n}\nfloat pcf(sampler2D map, vec2 uv, float z, float textureSize, vec2 scale) {\n float shadowContrib = tapShadowMap(map, uv, z);\n vec2 offset = vec2(1.0 / textureSize) * scale;\n#ifdef PCF_KERNEL_SIZE\n for (int _idx_ = 0; _idx_ < PCF_KERNEL_SIZE; _idx_++) {{\n shadowContrib += tapShadowMap(map, uv + offset * pcfKernel[_idx_], z);\n }}\n return shadowContrib / float(PCF_KERNEL_SIZE + 1);\n#else\n shadowContrib += tapShadowMap(map, uv+vec2(offset.x, 0.0), z);\n shadowContrib += tapShadowMap(map, uv+vec2(offset.x, offset.y), z);\n shadowContrib += tapShadowMap(map, uv+vec2(-offset.x, offset.y), z);\n shadowContrib += tapShadowMap(map, uv+vec2(0.0, offset.y), z);\n shadowContrib += tapShadowMap(map, uv+vec2(-offset.x, 0.0), z);\n shadowContrib += tapShadowMap(map, uv+vec2(-offset.x, -offset.y), z);\n shadowContrib += tapShadowMap(map, uv+vec2(offset.x, -offset.y), z);\n shadowContrib += tapShadowMap(map, uv+vec2(0.0, -offset.y), z);\n return shadowContrib / 9.0;\n#endif\n}\nfloat pcf(sampler2D map, vec2 uv, float z, float textureSize) {\n return pcf(map, uv, z, textureSize, vec2(1.0));\n}\nfloat chebyshevUpperBound(vec2 moments, float z){\n float p = 0.0;\n z = z * 0.5 + 0.5;\n if (z <= moments.x) {\n p = 1.0;\n }\n float variance = moments.y - moments.x * moments.x;\n variance = max(variance, 0.0000001);\n float mD = moments.x - z;\n float pMax = variance / (variance + mD * mD);\n pMax = clamp((pMax-0.4)/(1.0-0.4), 0.0, 1.0);\n return max(p, pMax);\n}\nfloat computeShadowContrib(\n sampler2D map, mat4 lightVPM, vec3 position, float textureSize, vec2 scale, vec2 offset\n) {\n vec4 posInLightSpace = lightVPM * vec4(position, 1.0);\n posInLightSpace.xyz /= posInLightSpace.w;\n float z = posInLightSpace.z;\n if(all(greaterThan(posInLightSpace.xyz, vec3(-0.99, -0.99, -1.0))) &&\n all(lessThan(posInLightSpace.xyz, vec3(0.99, 0.99, 1.0)))){\n vec2 uv = (posInLightSpace.xy+1.0) / 2.0;\n #ifdef USE_VSM\n vec2 moments = texture2D(map, uv * scale + offset).xy;\n return chebyshevUpperBound(moments, z);\n #else\n return pcf(map, uv * scale + offset, z, textureSize, scale);\n #endif\n }\n return 1.0;\n}\nfloat computeShadowContrib(sampler2D map, mat4 lightVPM, vec3 position, float textureSize) {\n return computeShadowContrib(map, lightVPM, position, textureSize, vec2(1.0), vec2(0.0));\n}\nfloat computeShadowContribOmni(samplerCube map, vec3 direction, float range)\n{\n float dist = length(direction);\n vec4 shadowTex = textureCube(map, direction);\n#ifdef USE_VSM\n vec2 moments = shadowTex.xy;\n float variance = moments.y - moments.x * moments.x;\n float mD = moments.x - dist;\n float p = variance / (variance + mD * mD);\n if(moments.x + 0.001 < dist){\n return clamp(p, 0.0, 1.0);\n }else{\n return 1.0;\n }\n#else\n return step(dist, (decodeFloat(shadowTex) + 0.0002) * range);\n#endif\n}\n@end\n@export clay.plugin.compute_shadow_map\n#if defined(SPOT_LIGHT_SHADOWMAP_COUNT) || defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT) || defined(POINT_LIGHT_SHADOWMAP_COUNT)\n#ifdef SPOT_LIGHT_SHADOWMAP_COUNT\nuniform sampler2D spotLightShadowMaps[SPOT_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\nuniform mat4 spotLightMatrices[SPOT_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\nuniform float spotLightShadowMapSizes[SPOT_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\n#endif\n#ifdef DIRECTIONAL_LIGHT_SHADOWMAP_COUNT\n#if defined(SHADOW_CASCADE)\nuniform sampler2D directionalLightShadowMaps[1]:unconfigurable;\nuniform mat4 directionalLightMatrices[SHADOW_CASCADE]:unconfigurable;\nuniform float directionalLightShadowMapSizes[1]:unconfigurable;\nuniform float shadowCascadeClipsNear[SHADOW_CASCADE]:unconfigurable;\nuniform float shadowCascadeClipsFar[SHADOW_CASCADE]:unconfigurable;\n#else\nuniform sampler2D directionalLightShadowMaps[DIRECTIONAL_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\nuniform mat4 directionalLightMatrices[DIRECTIONAL_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\nuniform float directionalLightShadowMapSizes[DIRECTIONAL_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\n#endif\n#endif\n#ifdef POINT_LIGHT_SHADOWMAP_COUNT\nuniform samplerCube pointLightShadowMaps[POINT_LIGHT_SHADOWMAP_COUNT]:unconfigurable;\n#endif\nuniform bool shadowEnabled : true;\n#ifdef PCF_KERNEL_SIZE\nuniform vec2 pcfKernel[PCF_KERNEL_SIZE];\n#endif\n@import clay.plugin.shadow_map_common\n#if defined(SPOT_LIGHT_SHADOWMAP_COUNT)\nvoid computeShadowOfSpotLights(vec3 position, inout float shadowContribs[SPOT_LIGHT_COUNT] ) {\n float shadowContrib;\n for(int _idx_ = 0; _idx_ < SPOT_LIGHT_SHADOWMAP_COUNT; _idx_++) {{\n shadowContrib = computeShadowContrib(\n spotLightShadowMaps[_idx_], spotLightMatrices[_idx_], position,\n spotLightShadowMapSizes[_idx_]\n );\n shadowContribs[_idx_] = shadowContrib;\n }}\n for(int _idx_ = SPOT_LIGHT_SHADOWMAP_COUNT; _idx_ < SPOT_LIGHT_COUNT; _idx_++){{\n shadowContribs[_idx_] = 1.0;\n }}\n}\n#endif\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n#ifdef SHADOW_CASCADE\nvoid computeShadowOfDirectionalLights(vec3 position, inout float shadowContribs[DIRECTIONAL_LIGHT_COUNT]){\n float depth = (2.0 * gl_FragCoord.z - gl_DepthRange.near - gl_DepthRange.far)\n / (gl_DepthRange.far - gl_DepthRange.near);\n float shadowContrib;\n shadowContribs[0] = 1.0;\n for (int _idx_ = 0; _idx_ < SHADOW_CASCADE; _idx_++) {{\n if (\n depth >= shadowCascadeClipsNear[_idx_] &&\n depth <= shadowCascadeClipsFar[_idx_]\n ) {\n shadowContrib = computeShadowContrib(\n directionalLightShadowMaps[0], directionalLightMatrices[_idx_], position,\n directionalLightShadowMapSizes[0],\n vec2(1.0 / float(SHADOW_CASCADE), 1.0),\n vec2(float(_idx_) / float(SHADOW_CASCADE), 0.0)\n );\n shadowContribs[0] = shadowContrib;\n }\n }}\n for(int _idx_ = DIRECTIONAL_LIGHT_SHADOWMAP_COUNT; _idx_ < DIRECTIONAL_LIGHT_COUNT; _idx_++) {{\n shadowContribs[_idx_] = 1.0;\n }}\n}\n#else\nvoid computeShadowOfDirectionalLights(vec3 position, inout float shadowContribs[DIRECTIONAL_LIGHT_COUNT]){\n float shadowContrib;\n for(int _idx_ = 0; _idx_ < DIRECTIONAL_LIGHT_SHADOWMAP_COUNT; _idx_++) {{\n shadowContrib = computeShadowContrib(\n directionalLightShadowMaps[_idx_], directionalLightMatrices[_idx_], position,\n directionalLightShadowMapSizes[_idx_]\n );\n shadowContribs[_idx_] = shadowContrib;\n }}\n for(int _idx_ = DIRECTIONAL_LIGHT_SHADOWMAP_COUNT; _idx_ < DIRECTIONAL_LIGHT_COUNT; _idx_++) {{\n shadowContribs[_idx_] = 1.0;\n }}\n}\n#endif\n#endif\n#if defined(POINT_LIGHT_SHADOWMAP_COUNT)\nvoid computeShadowOfPointLights(vec3 position, inout float shadowContribs[POINT_LIGHT_COUNT] ){\n vec3 lightPosition;\n vec3 direction;\n for(int _idx_ = 0; _idx_ < POINT_LIGHT_SHADOWMAP_COUNT; _idx_++) {{\n lightPosition = pointLightPosition[_idx_];\n direction = position - lightPosition;\n shadowContribs[_idx_] = computeShadowContribOmni(pointLightShadowMaps[_idx_], direction, pointLightRange[_idx_]);\n }}\n for(int _idx_ = POINT_LIGHT_SHADOWMAP_COUNT; _idx_ < POINT_LIGHT_COUNT; _idx_++) {{\n shadowContribs[_idx_] = 1.0;\n }}\n}\n#endif\n#endif\n@end");var Ii,Li,Oi,Pi,Ni,Bi,Ui,Fi=u.extend((function(){return{softShadow:Fi.PCF,shadowBlur:1,lightFrustumBias:"auto",kernelPCF:new Float32Array([1,0,1,1,-1,1,0,1,-1,0,-1,-1,1,-1,0,-1]),precision:"highp",_lastRenderNotCastShadow:!1,_frameBuffer:new Ln,_textures:{},_shadowMapNumber:{POINT_LIGHT:0,DIRECTIONAL_LIGHT:0,SPOT_LIGHT:0},_depthMaterials:{},_distanceMaterials:{},_receivers:[],_lightsCastShadow:[],_lightCameras:{},_lightMaterials:{},_texturePool:new wi}}),(function(){this._gaussianPassH=new ti({fragment:sr.source("clay.compositor.gaussian_blur")}),this._gaussianPassV=new ti({fragment:sr.source("clay.compositor.gaussian_blur")}),this._gaussianPassH.setUniform("blurSize",this.shadowBlur),this._gaussianPassH.setUniform("blurDir",0),this._gaussianPassV.setUniform("blurSize",this.shadowBlur),this._gaussianPassV.setUniform("blurDir",1),this._outputDepthPass=new ti({fragment:sr.source("clay.sm.debug_depth")})}),{render:function(t,r,e,n){e||(e=r.getMainCamera()),this.trigger("beforerender",this,t,r,e),this._renderShadowPass(t,r,e,n),this.trigger("afterrender",this,t,r,e)},renderDebug:function(t,r){t.saveClear();var e=t.viewport,n=0,i=r||e.width/4,a=i;for(var o in this.softShadow===Fi.VSM?this._outputDepthPass.material.define("fragment","USE_VSM"):this._outputDepthPass.material.undefine("fragment","USE_VSM"),this._textures){var s=this._textures[o];t.setViewport(n,0,i*s.width/s.height,a),this._outputDepthPass.setUniform("depthMap",s),this._outputDepthPass.render(t),n+=i*s.width/s.height}t.setViewport(e),t.restoreClear()},_updateReceivers:function(t,r){if(r.receiveShadow?(this._receivers.push(r),r.material.set("shadowEnabled",1),r.material.set("pcfKernel",this.kernelPCF)):r.material.set("shadowEnabled",0),this.softShadow===Fi.VSM)r.material.define("fragment","USE_VSM"),r.material.undefine("fragment","PCF_KERNEL_SIZE");else{r.material.undefine("fragment","USE_VSM");var e=this.kernelPCF;e&&e.length?r.material.define("fragment","PCF_KERNEL_SIZE",e.length/2):r.material.undefine("fragment","PCF_KERNEL_SIZE")}},_update:function(t,r){var e=this;r.traverse((function(r){r.isRenderable()&&e._updateReceivers(t,r)}));for(var n=0;n<r.lights.length;n++){var i=r.lights[n];i.castShadow&&!i.invisible&&this._lightsCastShadow.push(i)}},_renderShadowPass:function(t,r,e,n){for(var i in this._shadowMapNumber)this._shadowMapNumber[i]=0;this._lightsCastShadow.length=0,this._receivers.length=0;var a=t.gl;if(n||r.update(),e&&e.update(),r.updateLights(),this._update(t,r),this._lightsCastShadow.length||!this._lastRenderNotCastShadow){this._lastRenderNotCastShadow=0===this._lightsCastShadow,a.enable(a.DEPTH_TEST),a.depthMask(!0),a.disable(a.BLEND),a.clearColor(1,1,1,1);for(var o,s=[],u=[],h=[],c=[],l=[],f=[],d=0;d<this._lightsCastShadow.length;d++){var m=this._lightsCastShadow[d];if("DIRECTIONAL_LIGHT"===m.type){if(o)continue;if(m.shadowCascade>4)continue;m.shadowCascade>1&&(o=m),this.renderDirectionalLightShadow(t,r,e,m,l,c,h)}else"SPOT_LIGHT"===m.type?this.renderSpotLightShadow(t,r,m,u,s):"POINT_LIGHT"===m.type&&this.renderPointLightShadow(t,r,m,f);this._shadowMapNumber[m.type]++}for(var p in this._shadowMapNumber){var y=this._shadowMapNumber[p],_=p+"_SHADOWMAP_COUNT";for(d=0;d<this._receivers.length;d++){(v=this._receivers[d].material).fragmentDefines[_]!==y&&(y>0?v.define("fragment",_,y):v.isDefined("fragment",_)&&v.undefine("fragment",_))}}for(d=0;d<this._receivers.length;d++){var v=this._receivers[d].material;o?v.define("fragment","SHADOW_CASCADE",o.shadowCascade):v.undefine("fragment","SHADOW_CASCADE")}var g=r.shadowUniforms;if(h.length>0){var x=h.map(w);if(g.directionalLightShadowMaps={value:h,type:"tv"},g.directionalLightMatrices={value:c,type:"m4v"},g.directionalLightShadowMapSizes={value:x,type:"1fv"},o){var T=l.slice(),b=l.slice();T.pop(),b.shift(),T.reverse(),b.reverse(),c.reverse(),g.shadowCascadeClipsNear={value:T,type:"1fv"},g.shadowCascadeClipsFar={value:b,type:"1fv"}}}if(s.length>0){var E=s.map(w);(g=r.shadowUniforms).spotLightShadowMaps={value:s,type:"tv"},g.spotLightMatrices={value:u,type:"m4v"},g.spotLightShadowMapSizes={value:E,type:"1fv"}}f.length>0&&(g.pointLightShadowMaps={value:f,type:"tv"})}function w(t){return t.height}},renderDirectionalLightShadow:(Ii=new an,Li=new Kr,Oi=new he,Pi=new Kr,Ni=new Kr,Bi=new Kr,Ui=new Kr,function(t,r,e,n,i,a,o){var s=this._getDepthMaterial(n),u={getMaterial:function(t){return t.shadowDepthMaterial||s},isMaterialChanged:Di,getUniform:Ri,ifRender:function(t){return t.castShadow},sortCompare:Er.opaqueSortCompare};if(!r.viewBoundingBoxLastFrame.isFinite()){var h=r.getBoundingBox();r.viewBoundingBoxLastFrame.copy(h).applyTransform(e.viewMatrix)}var c=Math.min(-r.viewBoundingBoxLastFrame.min.z,e.far),l=Math.max(-r.viewBoundingBoxLastFrame.max.z,e.near),f=this._getDirectionalLightCamera(n,r,e),d=Bi.array;Ui.copy(f.projectionMatrix),lr.invert(Ni.array,f.worldTransform.array),lr.multiply(Ni.array,Ni.array,e.worldTransform.array),lr.multiply(d,Ui.array,Ni.array);for(var m=[],p=e instanceof Tn,y=(e.near+e.far)/(e.near-e.far),_=2*e.near*e.far/(e.near-e.far),v=0;v<=n.shadowCascade;v++){var g=l*Math.pow(c/l,v/n.shadowCascade),x=l+(c-l)*v/n.shadowCascade,T=g*n.cascadeSplitLogFactor+x*(1-n.cascadeSplitLogFactor);m.push(T),i.push(-(-T*y+_)/-T)}var b=this._getTexture(n,n.shadowCascade);o.push(b);var E=t.viewport,w=t.gl;for(this._frameBuffer.attach(b),this._frameBuffer.bind(t),w.clear(w.COLOR_BUFFER_BIT|w.DEPTH_BUFFER_BIT),v=0;v<n.shadowCascade;v++){var A=m[v],S=m[v+1];p?lr.perspective(Li.array,e.fov/180*Math.PI,e.aspect,A,S):lr.ortho(Li.array,e.left,e.right,e.bottom,e.top,A,S),Ii.setFromProjection(Li),Ii.getTransformedBoundingBox(Oi,Ni),Oi.applyProjection(Ui);var M=Oi.min.array,C=Oi.max.array;M[0]=Math.max(M[0],-1),M[1]=Math.max(M[1],-1),C[0]=Math.min(C[0],1),C[1]=Math.min(C[1],1),Pi.ortho(M[0],C[0],M[1],C[1],1,-1),f.projectionMatrix.multiplyLeft(Pi);var R=n.shadowResolution||512;t.setViewport((n.shadowCascade-v-1)*R,0,R,R,1);var D=r.updateRenderList(f);t.renderPass(D.opaque,f,u),this.softShadow===Fi.VSM&&this._gaussianFilter(t,b,b.width);var I=new Kr;I.copy(f.viewMatrix).multiplyLeft(f.projectionMatrix),a.push(I.array),f.projectionMatrix.copy(Ui)}this._frameBuffer.unbind(t),t.setViewport(E)}),renderSpotLightShadow:function(t,r,e,n,i){var a=this._getTexture(e),o=this._getSpotLightCamera(e),s=t.gl;this._frameBuffer.attach(a),this._frameBuffer.bind(t),s.clear(s.COLOR_BUFFER_BIT|s.DEPTH_BUFFER_BIT);var u=this._getDepthMaterial(e),h={getMaterial:function(t){return t.shadowDepthMaterial||u},isMaterialChanged:Di,getUniform:Ri,ifRender:function(t){return t.castShadow},sortCompare:Er.opaqueSortCompare},c=r.updateRenderList(o);t.renderPass(c.opaque,o,h),this._frameBuffer.unbind(t),this.softShadow===Fi.VSM&&this._gaussianFilter(t,a,a.width);var l=new Kr;l.copy(o.worldTransform).invert().multiplyLeft(o.projectionMatrix),i.push(a),n.push(l.array)},renderPointLightShadow:function(t,r,e,n){var i=this._getTexture(e),a=t.gl;n.push(i);var o=this._getDepthMaterial(e),s={getMaterial:function(t){return t.shadowDepthMaterial||o},getUniform:Ri,sortCompare:Er.opaqueSortCompare},u={px:[],py:[],pz:[],nx:[],ny:[],nz:[]},h=new he,c=e.getWorldPosition().array,l=new he,f=e.range;l.min.setArray(c),l.max.setArray(c);var d=new Ar(f,f,f);l.max.add(d),l.min.sub(d);var m={px:!1,py:!1,pz:!1,nx:!1,ny:!1,nz:!1};r.traverse((function(t){if(t.isRenderable()&&t.castShadow){var r=t.geometry;if(!r.boundingBox){for(var e=0;e<Ci.length;e++)u[Ci[e]].push(t);return}if(h.transformFrom(r.boundingBox,t.worldTransform),!h.intersectBoundingBox(l))return;h.updateVertices();for(e=0;e<Ci.length;e++)m[Ci[e]]=!1;for(e=0;e<8;e++){var n=h.vertices[e],i=n[0]-c[0],a=n[1]-c[1],o=n[2]-c[2],s=Math.abs(i),f=Math.abs(a),d=Math.abs(o);s>f?s>d?m[i>0?"px":"nx"]=!0:m[o>0?"pz":"nz"]=!0:f>d?m[a>0?"py":"ny"]=!0:m[o>0?"pz":"nz"]=!0}for(e=0;e<Ci.length;e++)m[Ci[e]]&&u[Ci[e]].push(t)}}));for(var p=0;p<6;p++){var y=Ci[p],_=this._getPointLightCamera(e,y);this._frameBuffer.attach(i,a.COLOR_ATTACHMENT0,a.TEXTURE_CUBE_MAP_POSITIVE_X+p),this._frameBuffer.bind(t),a.clear(a.COLOR_BUFFER_BIT|a.DEPTH_BUFFER_BIT),t.renderPass(u[y],_,s)}this._frameBuffer.unbind(t)},_getDepthMaterial:function(t){var r=this._lightMaterials[t.__uid__],e="POINT_LIGHT"===t.type;if(!r){var n=e?"clay.sm.distance.":"clay.sm.depth.";r=new It({precision:this.precision,shader:new sr(sr.source(n+"vertex"),sr.source(n+"fragment"))}),this._lightMaterials[t.__uid__]=r}return null!=t.shadowSlopeScale&&r.setUniform("slopeScale",t.shadowSlopeScale),null!=t.shadowBias&&r.setUniform("bias",t.shadowBias),this.softShadow===Fi.VSM?r.define("fragment","USE_VSM"):r.undefine("fragment","USE_VSM"),e&&(r.set("lightPosition",t.getWorldPosition().array),r.set("range",t.range)),r},_gaussianFilter:function(t,r,e){var n={width:e,height:e,type:Ee.FLOAT},i=this._texturePool.get(n);this._frameBuffer.attach(i),this._frameBuffer.bind(t),this._gaussianPassH.setUniform("texture",r),this._gaussianPassH.setUniform("textureWidth",e),this._gaussianPassH.render(t),this._frameBuffer.attach(r),this._gaussianPassV.setUniform("texture",i),this._gaussianPassV.setUniform("textureHeight",e),this._gaussianPassV.render(t),this._frameBuffer.unbind(t),this._texturePool.put(i)},_getTexture:function(t,r){var e=t.__uid__,n=this._textures[e],i=t.shadowResolution||512;return r=r||1,n||((n="POINT_LIGHT"===t.type?new gn:new Ce).width=i*r,n.height=i,this.softShadow===Fi.VSM?(n.type=Ee.FLOAT,n.anisotropic=4):(n.minFilter=G,n.magFilter=G,n.useMipmap=!1),this._textures[e]=n),n},_getPointLightCamera:function(t,r){this._lightCameras.point||(this._lightCameras.point={px:new Tn,nx:new Tn,py:new Tn,ny:new Tn,pz:new Tn,nz:new Tn});var e=this._lightCameras.point[r];switch(e.far=t.range,e.fov=90,e.position.set(0,0,0),r){case"px":e.lookAt(Ar.POSITIVE_X,Ar.NEGATIVE_Y);break;case"nx":e.lookAt(Ar.NEGATIVE_X,Ar.NEGATIVE_Y);break;case"py":e.lookAt(Ar.POSITIVE_Y,Ar.POSITIVE_Z);break;case"ny":e.lookAt(Ar.NEGATIVE_Y,Ar.NEGATIVE_Z);break;case"pz":e.lookAt(Ar.POSITIVE_Z,Ar.NEGATIVE_Y);break;case"nz":e.lookAt(Ar.NEGATIVE_Z,Ar.NEGATIVE_Y)}return t.getWorldPosition(e.position),e.update(),e},_getDirectionalLightCamera:function(){var t=new Kr,r=new he,e=new he;return function(n,i,a){this._lightCameras.directional||(this._lightCameras.directional=new Jn);var o=this._lightCameras.directional;r.copy(i.viewBoundingBoxLastFrame),r.intersection(a.frustum.boundingBox),o.position.copy(r.min).add(r.max).scale(.5).transformMat4(a.worldTransform),o.rotation.copy(n.rotation),o.scale.copy(n.scale),o.updateWorldTransform(),Kr.invert(t,o.worldTransform),Kr.multiply(t,t,a.worldTransform),e.copy(r).applyTransform(t);var s=e.min.array,u=e.max.array;return o.position.set((s[0]+u[0])/2,(s[1]+u[1])/2,u[2]).transformMat4(o.worldTransform),o.near=0,o.far=-s[2]+u[2],isNaN(this.lightFrustumBias)?o.far*=4:o.far+=this.lightFrustumBias,o.left=s[0],o.right=u[0],o.top=u[1],o.bottom=s[1],o.update(!0),o}}(),_getSpotLightCamera:function(t){this._lightCameras.spot||(this._lightCameras.spot=new Tn);var r=this._lightCameras.spot;return r.fov=2*t.penumbraAngle,r.far=t.range,r.worldTransform.copy(t.worldTransform),r.updateProjectionMatrix(),lr.invert(r.viewMatrix.array,r.worldTransform.array),r},dispose:function(t){var r=t.gl||t;for(var e in this._frameBuffer&&this._frameBuffer.dispose(r),this._textures)this._textures[e].dispose(r);this._texturePool.clear(t.gl),this._depthMaterials={},this._distanceMaterials={},this._textures={},this._lightCameras={},this._shadowMapNumber={POINT_LIGHT:0,DIRECTIONAL_LIGHT:0,SPOT_LIGHT:0},this._meshMaterials={};for(var n=0;n<this._receivers.length;n++){var i=this._receivers[n];if(i.material){var a=i.material;a.undefine("fragment","POINT_LIGHT_SHADOW_COUNT"),a.undefine("fragment","DIRECTIONAL_LIGHT_SHADOW_COUNT"),a.undefine("fragment","AMBIENT_LIGHT_SHADOW_COUNT"),a.set("shadowEnabled",0)}}this._receivers=[],this._lightsCastShadow=[]}});Fi.VSM=1,Fi.PCF=2;var ki=u.extend((function(){return{name:"",inputLinks:{},outputLinks:{},_prevOutputTextures:{},_outputTextures:{},_outputReferences:{},_rendering:!1,_rendered:!1,_compositor:null}}),{updateParameter:function(t,r){var e,n,i=this.outputs[t],a=i.parameters,o=i._parametersCopy;if(o||(o=i._parametersCopy={}),a)for(var s in a)"width"!==s&&"height"!==s&&(o[s]=a[s]);return e=a.width instanceof Function?a.width.call(this,r):a.width,n=a.height instanceof Function?a.height.call(this,r):a.height,o.width===e&&o.height===n||this._outputTextures[t]&&this._outputTextures[t].dispose(r.gl),o.width=e,o.height=n,o},setParameter:function(t,r){},getParameter:function(t){},setParameters:function(t){for(var r in t)this.setParameter(r,t[r])},render:function(){},getOutput:function(t,r){if(null==r)return r=t,this._outputTextures[r];var e=this.outputs[r];return e?this._rendered?e.outputLastFrame?this._prevOutputTextures[r]:this._outputTextures[r]:this._rendering?(this._prevOutputTextures[r]||(this._prevOutputTextures[r]=this._compositor.allocateTexture(e.parameters||{})),this._prevOutputTextures[r]):(this.render(t),this._outputTextures[r]):void 0},removeReference:function(t){(this._outputReferences[t]--,0===this._outputReferences[t])&&(this.outputs[t].keepLastFrame?(this._prevOutputTextures[t]&&this._compositor.releaseTexture(this._prevOutputTextures[t]),this._prevOutputTextures[t]=this._outputTextures[t]):this._compositor.releaseTexture(this._outputTextures[t]))},link:function(t,r,e){this.inputLinks[t]={node:r,pin:e},r.outputLinks[e]||(r.outputLinks[e]=[]),r.outputLinks[e].push({node:this,pin:t}),this.pass.material.enableTexture(t)},clear:function(){this.inputLinks={},this.outputLinks={}},updateReference:function(t){if(!this._rendering){for(var r in this._rendering=!0,this.inputLinks){var e=this.inputLinks[r];e.node.updateReference(e.pin)}this._rendering=!1}t&&this._outputReferences[t]++},beforeFrame:function(){for(var t in this._rendered=!1,this.outputLinks)this._outputReferences[t]=0},afterFrame:function(){for(var t in this.outputLinks){if(this._outputReferences[t]>0)this.outputs[t].keepLastFrame?(this._prevOutputTextures[t]&&this._compositor.releaseTexture(this._prevOutputTextures[t]),this._prevOutputTextures[t]=this._outputTextures[t]):this._compositor.releaseTexture(this._outputTextures[t])}}}),Hi=u.extend((function(){return{nodes:[]}}),{dirty:function(){this._dirty=!0},addNode:function(t){this.nodes.indexOf(t)>=0||(this.nodes.push(t),this._dirty=!0)},removeNode:function(t){"string"==typeof t&&(t=this.getNodeByName(t));var r=this.nodes.indexOf(t);r>=0&&(this.nodes.splice(r,1),this._dirty=!0)},getNodeByName:function(t){for(var r=0;r<this.nodes.length;r++)if(this.nodes[r].name===t)return this.nodes[r]},update:function(){for(var t=0;t<this.nodes.length;t++)this.nodes[t].clear();for(t=0;t<this.nodes.length;t++){var r=this.nodes[t];if(r.inputs)for(var e in r.inputs)if(r.inputs[e]&&(!r.pass||r.pass.material.isUniformEnabled(e))){var n=r.inputs[e],i=this.findPin(n);i&&r.link(e,i.node,i.pin)}}},findPin:function(t){var r;if(("string"==typeof t||t instanceof ki)&&(t={node:t}),"string"==typeof t.node)for(var e=0;e<this.nodes.length;e++){var n=this.nodes[e];n.name===t.node&&(r=n)}else r=t.node;if(r){var i=t.pin;if(i||r.outputs&&(i=Object.keys(r.outputs)[0]),r.outputs[i])return{node:r,pin:i}}}}),Vi=Hi.extend((function(){return{_outputs:[],_texturePool:new wi,_frameBuffer:new Ln({depthBuffer:!1})}}),{addNode:function(t){Hi.prototype.addNode.call(this,t),t._compositor=this},render:function(t,r){if(this._dirty){this.update(),this._dirty=!1,this._outputs.length=0;for(var e=0;e<this.nodes.length;e++)this.nodes[e].outputs||this._outputs.push(this.nodes[e])}for(e=0;e<this.nodes.length;e++)this.nodes[e].beforeFrame();for(e=0;e<this._outputs.length;e++)this._outputs[e].updateReference();for(e=0;e<this._outputs.length;e++)this._outputs[e].render(t,r);for(e=0;e<this.nodes.length;e++)this.nodes[e].afterFrame()},allocateTexture:function(t){return this._texturePool.get(t)},releaseTexture:function(t){this._texturePool.put(t)},getFrameBuffer:function(){return this._frameBuffer},dispose:function(t){this._texturePool.clear(t)}}),Wi=ki.extend({name:"scene",scene:null,camera:null,autoUpdateScene:!0,preZ:!1},(function(){this.frameBuffer=new Ln}),{render:function(t){this._rendering=!0;var r,e=t.gl;if(this.trigger("beforerender"),this.outputs){var n=this.frameBuffer;for(var i in this.outputs){var a=this.updateParameter(i,t),o=this.outputs[i],s=this._compositor.allocateTexture(a);this._outputTextures[i]=s,"string"==typeof(c=o.attachment||e.COLOR_ATTACHMENT0)&&(c=e[c]),n.attach(s,c)}n.bind(t);var u=t.getGLExtension("EXT_draw_buffers");if(u){var h=[];for(var c in this.outputs)(c=parseInt(c))>=e.COLOR_ATTACHMENT0&&c<=e.COLOR_ATTACHMENT0+8&&h.push(c);u.drawBuffersEXT(h)}t.saveClear(),t.clearBit=f|m,r=t.render(this.scene,this.camera,!this.autoUpdateScene,this.preZ),t.restoreClear(),n.unbind(t)}else r=t.render(this.scene,this.camera,!this.autoUpdateScene,this.preZ);this.trigger("afterrender",r),this._rendering=!1,this._rendered=!0}}),Gi=ki.extend((function(){return{texture:null,outputs:{color:{}}}}),(function(){}),{getOutput:function(t,r){return this.texture},beforeFrame:function(){},afterFrame:function(){}}),zi=ki.extend((function(){return{name:"",inputs:{},outputs:null,shader:"",inputLinks:{},outputLinks:{},pass:null,_prevOutputTextures:{},_outputTextures:{},_outputReferences:{},_rendering:!1,_rendered:!1,_compositor:null}}),(function(){var t=new ti({fragment:this.shader});this.pass=t}),{render:function(t,r){this.trigger("beforerender",t),this._rendering=!0;var e=t.gl;for(var n in this.inputLinks){var i=(l=this.inputLinks[n]).node.getOutput(t,l.pin);this.pass.setUniform(n,i)}if(this.outputs){this.pass.outputs={};var a={};for(var o in this.outputs){var s=this.updateParameter(o,t);isNaN(s.width)&&this.updateParameter(o,t);var u=this.outputs[o],h=this._compositor.allocateTexture(s);this._outputTextures[o]=h,"string"==typeof(c=u.attachment||e.COLOR_ATTACHMENT0)&&(c=e[c]),a[c]=h}for(var c in this._compositor.getFrameBuffer().bind(t),a)this._compositor.getFrameBuffer().attach(a[c],c);this.pass.render(t),this._compositor.getFrameBuffer().updateMipmap(t)}else this.pass.outputs=null,this._compositor.getFrameBuffer().unbind(t),this.pass.render(t,r);for(var n in this.inputLinks){var l;(l=this.inputLinks[n]).node.removeReference(l.pin)}this._rendering=!1,this._rendered=!0,this.trigger("afterrender",t)},updateParameter:function(t,r){var e,n,i=this.outputs[t],a=i.parameters,o=i._parametersCopy;if(o||(o=i._parametersCopy={}),a)for(var s in a)"width"!==s&&"height"!==s&&(o[s]=a[s]);return e="function"==typeof a.width?a.width.call(this,r):a.width,n="function"==typeof a.height?a.height.call(this,r):a.height,e=Math.ceil(e),n=Math.ceil(n),o.width===e&&o.height===n||this._outputTextures[t]&&this._outputTextures[t].dispose(r),o.width=e,o.height=n,o},setParameter:function(t,r){this.pass.setUniform(t,r)},getParameter:function(t){return this.pass.getUniform(t)},setParameters:function(t){for(var r in t)this.setParameter(r,t[r])},define:function(t,r){this.pass.material.define("fragment",t,r)},undefine:function(t){this.pass.material.undefine("fragment",t)},removeReference:function(t){(this._outputReferences[t]--,0===this._outputReferences[t])&&(this.outputs[t].keepLastFrame?(this._prevOutputTextures[t]&&this._compositor.releaseTexture(this._prevOutputTextures[t]),this._prevOutputTextures[t]=this._outputTextures[t]):this._compositor.releaseTexture(this._outputTextures[t]))},clear:function(){ki.prototype.clear.call(this),this.pass.material.disableTexturesAll()}});const Xi="@export clay.compositor.kernel.gaussian_9\nfloat gaussianKernel[9];\ngaussianKernel[0] = 0.07;\ngaussianKernel[1] = 0.09;\ngaussianKernel[2] = 0.12;\ngaussianKernel[3] = 0.14;\ngaussianKernel[4] = 0.16;\ngaussianKernel[5] = 0.14;\ngaussianKernel[6] = 0.12;\ngaussianKernel[7] = 0.09;\ngaussianKernel[8] = 0.07;\n@end\n@export clay.compositor.kernel.gaussian_13\nfloat gaussianKernel[13];\ngaussianKernel[0] = 0.02;\ngaussianKernel[1] = 0.03;\ngaussianKernel[2] = 0.06;\ngaussianKernel[3] = 0.08;\ngaussianKernel[4] = 0.11;\ngaussianKernel[5] = 0.13;\ngaussianKernel[6] = 0.14;\ngaussianKernel[7] = 0.13;\ngaussianKernel[8] = 0.11;\ngaussianKernel[9] = 0.08;\ngaussianKernel[10] = 0.06;\ngaussianKernel[11] = 0.03;\ngaussianKernel[12] = 0.02;\n@end\n@export clay.compositor.gaussian_blur\n#define SHADER_NAME gaussian_blur\nuniform sampler2D texture;varying vec2 v_Texcoord;\nuniform float blurSize : 2.0;\nuniform vec2 textureSize : [512.0, 512.0];\nuniform float blurDir : 0.0;\n@import clay.util.rgbm\n@import clay.util.clamp_sample\nvoid main (void)\n{\n @import clay.compositor.kernel.gaussian_9\n vec2 off = blurSize / textureSize;\n off *= vec2(1.0 - blurDir, blurDir);\n vec4 sum = vec4(0.0);\n float weightAll = 0.0;\n for (int i = 0; i < 9; i++) {\n float w = gaussianKernel[i];\n vec4 texel = decodeHDR(clampSample(texture, v_Texcoord + float(i - 4) * off));\n sum += texel * w;\n weightAll += w;\n }\n gl_FragColor = encodeHDR(sum / max(weightAll, 0.01));\n}\n@end\n",ji="\n@export clay.compositor.lut\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform sampler2D lookup;\nvoid main()\n{\n vec4 tex = texture2D(texture, v_Texcoord);\n float blueColor = tex.b * 63.0;\n vec2 quad1;\n quad1.y = floor(floor(blueColor) / 8.0);\n quad1.x = floor(blueColor) - (quad1.y * 8.0);\n vec2 quad2;\n quad2.y = floor(ceil(blueColor) / 8.0);\n quad2.x = ceil(blueColor) - (quad2.y * 8.0);\n vec2 texPos1;\n texPos1.x = (quad1.x * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * tex.r);\n texPos1.y = (quad1.y * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * tex.g);\n vec2 texPos2;\n texPos2.x = (quad2.x * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * tex.r);\n texPos2.y = (quad2.y * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * tex.g);\n vec4 newColor1 = texture2D(lookup, texPos1);\n vec4 newColor2 = texture2D(lookup, texPos2);\n vec4 newColor = mix(newColor1, newColor2, fract(blueColor));\n gl_FragColor = vec4(newColor.rgb, tex.w);\n}\n@end",qi="@export clay.compositor.output\n#define OUTPUT_ALPHA\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\n@import clay.util.rgbm\nvoid main()\n{\n vec4 tex = decodeHDR(texture2D(texture, v_Texcoord));\n gl_FragColor.rgb = tex.rgb;\n#ifdef OUTPUT_ALPHA\n gl_FragColor.a = tex.a;\n#else\n gl_FragColor.a = 1.0;\n#endif\n gl_FragColor = encodeHDR(gl_FragColor);\n#ifdef PREMULTIPLY_ALPHA\n gl_FragColor.rgb *= gl_FragColor.a;\n#endif\n}\n@end",Yi="@export clay.compositor.bright\nuniform sampler2D texture;\nuniform float threshold : 1;\nuniform float scale : 1.0;\nuniform vec2 textureSize: [512, 512];\nvarying vec2 v_Texcoord;\nconst vec3 lumWeight = vec3(0.2125, 0.7154, 0.0721);\n@import clay.util.rgbm\nvec4 median(vec4 a, vec4 b, vec4 c)\n{\n return a + b + c - min(min(a, b), c) - max(max(a, b), c);\n}\nvoid main()\n{\n vec4 texel = decodeHDR(texture2D(texture, v_Texcoord));\n#ifdef ANTI_FLICKER\n vec3 d = 1.0 / textureSize.xyx * vec3(1.0, 1.0, 0.0);\n vec4 s1 = decodeHDR(texture2D(texture, v_Texcoord - d.xz));\n vec4 s2 = decodeHDR(texture2D(texture, v_Texcoord + d.xz));\n vec4 s3 = decodeHDR(texture2D(texture, v_Texcoord - d.zy));\n vec4 s4 = decodeHDR(texture2D(texture, v_Texcoord + d.zy));\n texel = median(median(texel, s1, s2), s3, s4);\n#endif\n float lum = dot(texel.rgb , lumWeight);\n vec4 color;\n if (lum > threshold && texel.a > 0.0)\n {\n color = vec4(texel.rgb * scale, texel.a * scale);\n }\n else\n {\n color = vec4(0.0);\n }\n gl_FragColor = encodeHDR(color);\n}\n@end\n",Ki="@export clay.compositor.downsample\nuniform sampler2D texture;\nuniform vec2 textureSize : [512, 512];\nvarying vec2 v_Texcoord;\n@import clay.util.rgbm\nfloat brightness(vec3 c)\n{\n return max(max(c.r, c.g), c.b);\n}\n@import clay.util.clamp_sample\nvoid main()\n{\n vec4 d = vec4(-1.0, -1.0, 1.0, 1.0) / textureSize.xyxy;\n#ifdef ANTI_FLICKER\n vec3 s1 = decodeHDR(clampSample(texture, v_Texcoord + d.xy)).rgb;\n vec3 s2 = decodeHDR(clampSample(texture, v_Texcoord + d.zy)).rgb;\n vec3 s3 = decodeHDR(clampSample(texture, v_Texcoord + d.xw)).rgb;\n vec3 s4 = decodeHDR(clampSample(texture, v_Texcoord + d.zw)).rgb;\n float s1w = 1.0 / (brightness(s1) + 1.0);\n float s2w = 1.0 / (brightness(s2) + 1.0);\n float s3w = 1.0 / (brightness(s3) + 1.0);\n float s4w = 1.0 / (brightness(s4) + 1.0);\n float oneDivideSum = 1.0 / (s1w + s2w + s3w + s4w);\n vec4 color = vec4(\n (s1 * s1w + s2 * s2w + s3 * s3w + s4 * s4w) * oneDivideSum,\n 1.0\n );\n#else\n vec4 color = decodeHDR(clampSample(texture, v_Texcoord + d.xy));\n color += decodeHDR(clampSample(texture, v_Texcoord + d.zy));\n color += decodeHDR(clampSample(texture, v_Texcoord + d.xw));\n color += decodeHDR(clampSample(texture, v_Texcoord + d.zw));\n color *= 0.25;\n#endif\n gl_FragColor = encodeHDR(color);\n}\n@end",Zi="\n@export clay.compositor.upsample\n#define HIGH_QUALITY\nuniform sampler2D texture;\nuniform vec2 textureSize : [512, 512];\nuniform float sampleScale: 0.5;\nvarying vec2 v_Texcoord;\n@import clay.util.rgbm\n@import clay.util.clamp_sample\nvoid main()\n{\n#ifdef HIGH_QUALITY\n vec4 d = vec4(1.0, 1.0, -1.0, 0.0) / textureSize.xyxy * sampleScale;\n vec4 s;\n s = decodeHDR(clampSample(texture, v_Texcoord - d.xy));\n s += decodeHDR(clampSample(texture, v_Texcoord - d.wy)) * 2.0;\n s += decodeHDR(clampSample(texture, v_Texcoord - d.zy));\n s += decodeHDR(clampSample(texture, v_Texcoord + d.zw)) * 2.0;\n s += decodeHDR(clampSample(texture, v_Texcoord )) * 4.0;\n s += decodeHDR(clampSample(texture, v_Texcoord + d.xw)) * 2.0;\n s += decodeHDR(clampSample(texture, v_Texcoord + d.zy));\n s += decodeHDR(clampSample(texture, v_Texcoord + d.wy)) * 2.0;\n s += decodeHDR(clampSample(texture, v_Texcoord + d.xy));\n gl_FragColor = encodeHDR(s / 16.0);\n#else\n vec4 d = vec4(-1.0, -1.0, +1.0, +1.0) / textureSize.xyxy;\n vec4 s;\n s = decodeHDR(clampSample(texture, v_Texcoord + d.xy));\n s += decodeHDR(clampSample(texture, v_Texcoord + d.zy));\n s += decodeHDR(clampSample(texture, v_Texcoord + d.xw));\n s += decodeHDR(clampSample(texture, v_Texcoord + d.zw));\n gl_FragColor = encodeHDR(s / 4.0);\n#endif\n}\n@end",Ji="@export clay.compositor.hdr.composite\n#define TONEMAPPING\nuniform sampler2D texture;\n#ifdef BLOOM_ENABLED\nuniform sampler2D bloom;\n#endif\n#ifdef LENSFLARE_ENABLED\nuniform sampler2D lensflare;\nuniform sampler2D lensdirt;\n#endif\n#ifdef LUM_ENABLED\nuniform sampler2D lum;\n#endif\n#ifdef LUT_ENABLED\nuniform sampler2D lut;\n#endif\n#ifdef COLOR_CORRECTION\nuniform float brightness : 0.0;\nuniform float contrast : 1.0;\nuniform float saturation : 1.0;\n#endif\n#ifdef VIGNETTE\nuniform float vignetteDarkness: 1.0;\nuniform float vignetteOffset: 1.0;\n#endif\nuniform float exposure : 1.0;\nuniform float bloomIntensity : 0.25;\nuniform float lensflareIntensity : 1;\nvarying vec2 v_Texcoord;\n@import clay.util.srgb\nvec3 ACESToneMapping(vec3 color)\n{\n const float A = 2.51;\n const float B = 0.03;\n const float C = 2.43;\n const float D = 0.59;\n const float E = 0.14;\n return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nfloat eyeAdaption(float fLum)\n{\n return mix(0.2, fLum, 0.5);\n}\n#ifdef LUT_ENABLED\nvec3 lutTransform(vec3 color) {\n float blueColor = color.b * 63.0;\n vec2 quad1;\n quad1.y = floor(floor(blueColor) / 8.0);\n quad1.x = floor(blueColor) - (quad1.y * 8.0);\n vec2 quad2;\n quad2.y = floor(ceil(blueColor) / 8.0);\n quad2.x = ceil(blueColor) - (quad2.y * 8.0);\n vec2 texPos1;\n texPos1.x = (quad1.x * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * color.r);\n texPos1.y = (quad1.y * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * color.g);\n vec2 texPos2;\n texPos2.x = (quad2.x * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * color.r);\n texPos2.y = (quad2.y * 0.125) + 0.5/512.0 + ((0.125 - 1.0/512.0) * color.g);\n vec4 newColor1 = texture2D(lut, texPos1);\n vec4 newColor2 = texture2D(lut, texPos2);\n vec4 newColor = mix(newColor1, newColor2, fract(blueColor));\n return newColor.rgb;\n}\n#endif\n@import clay.util.rgbm\nvoid main()\n{\n vec4 texel = vec4(0.0);\n vec4 originalTexel = vec4(0.0);\n#ifdef TEXTURE_ENABLED\n texel = decodeHDR(texture2D(texture, v_Texcoord));\n originalTexel = texel;\n#endif\n#ifdef BLOOM_ENABLED\n vec4 bloomTexel = decodeHDR(texture2D(bloom, v_Texcoord));\n texel.rgb += bloomTexel.rgb * bloomIntensity;\n texel.a += bloomTexel.a * bloomIntensity;\n#endif\n#ifdef LENSFLARE_ENABLED\n texel += decodeHDR(texture2D(lensflare, v_Texcoord)) * texture2D(lensdirt, v_Texcoord) * lensflareIntensity;\n#endif\n texel.a = min(texel.a, 1.0);\n#ifdef LUM_ENABLED\n float fLum = texture2D(lum, vec2(0.5, 0.5)).r;\n float adaptedLumDest = 3.0 / (max(0.1, 1.0 + 10.0*eyeAdaption(fLum)));\n float exposureBias = adaptedLumDest * exposure;\n#else\n float exposureBias = exposure;\n#endif\n#ifdef TONEMAPPING\n texel.rgb *= exposureBias;\n texel.rgb = ACESToneMapping(texel.rgb);\n#endif\n texel = linearTosRGB(texel);\n#ifdef LUT_ENABLED\n texel.rgb = lutTransform(clamp(texel.rgb,vec3(0.0),vec3(1.0)));\n#endif\n#ifdef COLOR_CORRECTION\n texel.rgb = clamp(texel.rgb + vec3(brightness), 0.0, 1.0);\n texel.rgb = clamp((texel.rgb - vec3(0.5))*contrast+vec3(0.5), 0.0, 1.0);\n float lum = dot(texel.rgb, vec3(0.2125, 0.7154, 0.0721));\n texel.rgb = mix(vec3(lum), texel.rgb, saturation);\n#endif\n#ifdef VIGNETTE\n vec2 uv = (v_Texcoord - vec2(0.5)) * vec2(vignetteOffset);\n texel.rgb = mix(texel.rgb, vec3(1.0 - vignetteDarkness), dot(uv, uv));\n#endif\n gl_FragColor = encodeHDR(texel);\n#ifdef DEBUG\n #if DEBUG == 1\n gl_FragColor = encodeHDR(decodeHDR(texture2D(texture, v_Texcoord)));\n #elif DEBUG == 2\n gl_FragColor = encodeHDR(decodeHDR(texture2D(bloom, v_Texcoord)) * bloomIntensity);\n #elif DEBUG == 3\n gl_FragColor = encodeHDR(decodeHDR(texture2D(lensflare, v_Texcoord) * lensflareIntensity));\n #endif\n#endif\n if (originalTexel.a <= 0.01 && gl_FragColor.a > 1e-5) {\n gl_FragColor.a = dot(gl_FragColor.rgb, vec3(0.2125, 0.7154, 0.0721));\n }\n#ifdef PREMULTIPLY_ALPHA\n gl_FragColor.rgb *= gl_FragColor.a;\n#endif\n}\n@end",Qi="@export clay.compositor.blend\n#define SHADER_NAME blend\n#ifdef TEXTURE1_ENABLED\nuniform sampler2D texture1;\nuniform float weight1 : 1.0;\n#endif\n#ifdef TEXTURE2_ENABLED\nuniform sampler2D texture2;\nuniform float weight2 : 1.0;\n#endif\n#ifdef TEXTURE3_ENABLED\nuniform sampler2D texture3;\nuniform float weight3 : 1.0;\n#endif\n#ifdef TEXTURE4_ENABLED\nuniform sampler2D texture4;\nuniform float weight4 : 1.0;\n#endif\n#ifdef TEXTURE5_ENABLED\nuniform sampler2D texture5;\nuniform float weight5 : 1.0;\n#endif\n#ifdef TEXTURE6_ENABLED\nuniform sampler2D texture6;\nuniform float weight6 : 1.0;\n#endif\nvarying vec2 v_Texcoord;\n@import clay.util.rgbm\nvoid main()\n{\n vec4 tex = vec4(0.0);\n#ifdef TEXTURE1_ENABLED\n tex += decodeHDR(texture2D(texture1, v_Texcoord)) * weight1;\n#endif\n#ifdef TEXTURE2_ENABLED\n tex += decodeHDR(texture2D(texture2, v_Texcoord)) * weight2;\n#endif\n#ifdef TEXTURE3_ENABLED\n tex += decodeHDR(texture2D(texture3, v_Texcoord)) * weight3;\n#endif\n#ifdef TEXTURE4_ENABLED\n tex += decodeHDR(texture2D(texture4, v_Texcoord)) * weight4;\n#endif\n#ifdef TEXTURE5_ENABLED\n tex += decodeHDR(texture2D(texture5, v_Texcoord)) * weight5;\n#endif\n#ifdef TEXTURE6_ENABLED\n tex += decodeHDR(texture2D(texture6, v_Texcoord)) * weight6;\n#endif\n gl_FragColor = encodeHDR(tex);\n}\n@end",$i="@export clay.compositor.fxaa\nuniform sampler2D texture;\nuniform vec4 viewport : VIEWPORT;\nvarying vec2 v_Texcoord;\n#define FXAA_REDUCE_MIN (1.0/128.0)\n#define FXAA_REDUCE_MUL (1.0/8.0)\n#define FXAA_SPAN_MAX 8.0\n@import clay.util.rgbm\nvoid main()\n{\n vec2 resolution = 1.0 / viewport.zw;\n vec3 rgbNW = decodeHDR( texture2D( texture, ( gl_FragCoord.xy + vec2( -1.0, -1.0 ) ) * resolution ) ).xyz;\n vec3 rgbNE = decodeHDR( texture2D( texture, ( gl_FragCoord.xy + vec2( 1.0, -1.0 ) ) * resolution ) ).xyz;\n vec3 rgbSW = decodeHDR( texture2D( texture, ( gl_FragCoord.xy + vec2( -1.0, 1.0 ) ) * resolution ) ).xyz;\n vec3 rgbSE = decodeHDR( texture2D( texture, ( gl_FragCoord.xy + vec2( 1.0, 1.0 ) ) * resolution ) ).xyz;\n vec4 rgbaM = decodeHDR( texture2D( texture, gl_FragCoord.xy * resolution ) );\n vec3 rgbM = rgbaM.xyz;\n float opacity = rgbaM.w;\n vec3 luma = vec3( 0.299, 0.587, 0.114 );\n float lumaNW = dot( rgbNW, luma );\n float lumaNE = dot( rgbNE, luma );\n float lumaSW = dot( rgbSW, luma );\n float lumaSE = dot( rgbSE, luma );\n float lumaM = dot( rgbM, luma );\n float lumaMin = min( lumaM, min( min( lumaNW, lumaNE ), min( lumaSW, lumaSE ) ) );\n float lumaMax = max( lumaM, max( max( lumaNW, lumaNE) , max( lumaSW, lumaSE ) ) );\n vec2 dir;\n dir.x = -((lumaNW + lumaNE) - (lumaSW + lumaSE));\n dir.y = ((lumaNW + lumaSW) - (lumaNE + lumaSE));\n float dirReduce = max( ( lumaNW + lumaNE + lumaSW + lumaSE ) * ( 0.25 * FXAA_REDUCE_MUL ), FXAA_REDUCE_MIN );\n float rcpDirMin = 1.0 / ( min( abs( dir.x ), abs( dir.y ) ) + dirReduce );\n dir = min( vec2( FXAA_SPAN_MAX, FXAA_SPAN_MAX),\n max( vec2(-FXAA_SPAN_MAX, -FXAA_SPAN_MAX),\n dir * rcpDirMin)) * resolution;\n vec3 rgbA = decodeHDR( texture2D( texture, gl_FragCoord.xy * resolution + dir * ( 1.0 / 3.0 - 0.5 ) ) ).xyz;\n rgbA += decodeHDR( texture2D( texture, gl_FragCoord.xy * resolution + dir * ( 2.0 / 3.0 - 0.5 ) ) ).xyz;\n rgbA *= 0.5;\n vec3 rgbB = decodeHDR( texture2D( texture, gl_FragCoord.xy * resolution + dir * -0.5 ) ).xyz;\n rgbB += decodeHDR( texture2D( texture, gl_FragCoord.xy * resolution + dir * 0.5 ) ).xyz;\n rgbB *= 0.25;\n rgbB += rgbA * 0.5;\n float lumaB = dot( rgbB, luma );\n if ( ( lumaB < lumaMin ) || ( lumaB > lumaMax ) )\n {\n gl_FragColor = vec4( rgbA, opacity );\n }\n else {\n gl_FragColor = vec4( rgbB, opacity );\n }\n}\n@end";var ta;(ta=sr).import("@export clay.compositor.coloradjust\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float brightness : 0.0;\nuniform float contrast : 1.0;\nuniform float exposure : 0.0;\nuniform float gamma : 1.0;\nuniform float saturation : 1.0;\nconst vec3 w = vec3(0.2125, 0.7154, 0.0721);\nvoid main()\n{\n vec4 tex = texture2D( texture, v_Texcoord);\n vec3 color = clamp(tex.rgb + vec3(brightness), 0.0, 1.0);\n color = clamp( (color-vec3(0.5))*contrast+vec3(0.5), 0.0, 1.0);\n color = clamp( color * pow(2.0, exposure), 0.0, 1.0);\n color = clamp( pow(color, vec3(gamma)), 0.0, 1.0);\n float luminance = dot( color, w );\n color = mix(vec3(luminance), color, saturation);\n gl_FragColor = vec4(color, tex.a);\n}\n@end\n@export clay.compositor.brightness\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float brightness : 0.0;\nvoid main()\n{\n vec4 tex = texture2D( texture, v_Texcoord);\n vec3 color = tex.rgb + vec3(brightness);\n gl_FragColor = vec4(color, tex.a);\n}\n@end\n@export clay.compositor.contrast\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float contrast : 1.0;\nvoid main()\n{\n vec4 tex = texture2D( texture, v_Texcoord);\n vec3 color = (tex.rgb-vec3(0.5))*contrast+vec3(0.5);\n gl_FragColor = vec4(color, tex.a);\n}\n@end\n@export clay.compositor.exposure\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float exposure : 0.0;\nvoid main()\n{\n vec4 tex = texture2D(texture, v_Texcoord);\n vec3 color = tex.rgb * pow(2.0, exposure);\n gl_FragColor = vec4(color, tex.a);\n}\n@end\n@export clay.compositor.gamma\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float gamma : 1.0;\nvoid main()\n{\n vec4 tex = texture2D(texture, v_Texcoord);\n vec3 color = pow(tex.rgb, vec3(gamma));\n gl_FragColor = vec4(color, tex.a);\n}\n@end\n@export clay.compositor.saturation\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float saturation : 1.0;\nconst vec3 w = vec3(0.2125, 0.7154, 0.0721);\nvoid main()\n{\n vec4 tex = texture2D(texture, v_Texcoord);\n vec3 color = tex.rgb;\n float luminance = dot(color, w);\n color = mix(vec3(luminance), color, saturation);\n gl_FragColor = vec4(color, tex.a);\n}\n@end"),ta.import(Xi),ta.import("@export clay.compositor.hdr.log_lum\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nconst vec3 w = vec3(0.2125, 0.7154, 0.0721);\n@import clay.util.rgbm\nvoid main()\n{\n vec4 tex = decodeHDR(texture2D(texture, v_Texcoord));\n float luminance = dot(tex.rgb, w);\n luminance = log(luminance + 0.001);\n gl_FragColor = encodeHDR(vec4(vec3(luminance), 1.0));\n}\n@end\n@export clay.compositor.hdr.lum_adaption\nvarying vec2 v_Texcoord;\nuniform sampler2D adaptedLum;\nuniform sampler2D currentLum;\nuniform float frameTime : 0.02;\n@import clay.util.rgbm\nvoid main()\n{\n float fAdaptedLum = decodeHDR(texture2D(adaptedLum, vec2(0.5, 0.5))).r;\n float fCurrentLum = exp(encodeHDR(texture2D(currentLum, vec2(0.5, 0.5))).r);\n fAdaptedLum += (fCurrentLum - fAdaptedLum) * (1.0 - pow(0.98, 30.0 * frameTime));\n gl_FragColor = encodeHDR(vec4(vec3(fAdaptedLum), 1.0));\n}\n@end\n@export clay.compositor.lum\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nconst vec3 w = vec3(0.2125, 0.7154, 0.0721);\nvoid main()\n{\n vec4 tex = texture2D( texture, v_Texcoord );\n float luminance = dot(tex.rgb, w);\n gl_FragColor = vec4(vec3(luminance), 1.0);\n}\n@end"),ta.import(ji),ta.import("@export clay.compositor.vignette\n#define OUTPUT_ALPHA\nvarying vec2 v_Texcoord;\nuniform sampler2D texture;\nuniform float darkness: 1;\nuniform float offset: 1;\n@import clay.util.rgbm\nvoid main()\n{\n vec4 texel = decodeHDR(texture2D(texture, v_Texcoord));\n gl_FragColor.rgb = texel.rgb;\n vec2 uv = (v_Texcoord - vec2(0.5)) * vec2(offset);\n gl_FragColor = encodeHDR(vec4(mix(texel.rgb, vec3(1.0 - darkness), dot(uv, uv)), texel.a));\n}\n@end"),ta.import(qi),ta.import(Yi),ta.import(Ki),ta.import(Zi),ta.import(Ji),ta.import("@export clay.compositor.lensflare\n#define SAMPLE_NUMBER 8\nuniform sampler2D texture;\nuniform sampler2D lenscolor;\nuniform vec2 textureSize : [512, 512];\nuniform float dispersal : 0.3;\nuniform float haloWidth : 0.4;\nuniform float distortion : 1.0;\nvarying vec2 v_Texcoord;\n@import clay.util.rgbm\nvec4 textureDistorted(\n in vec2 texcoord,\n in vec2 direction,\n in vec3 distortion\n) {\n return vec4(\n decodeHDR(texture2D(texture, texcoord + direction * distortion.r)).r,\n decodeHDR(texture2D(texture, texcoord + direction * distortion.g)).g,\n decodeHDR(texture2D(texture, texcoord + direction * distortion.b)).b,\n 1.0\n );\n}\nvoid main()\n{\n vec2 texcoord = -v_Texcoord + vec2(1.0); vec2 textureOffset = 1.0 / textureSize;\n vec2 ghostVec = (vec2(0.5) - texcoord) * dispersal;\n vec2 haloVec = normalize(ghostVec) * haloWidth;\n vec3 distortion = vec3(-textureOffset.x * distortion, 0.0, textureOffset.x * distortion);\n vec4 result = vec4(0.0);\n for (int i = 0; i < SAMPLE_NUMBER; i++)\n {\n vec2 offset = fract(texcoord + ghostVec * float(i));\n float weight = length(vec2(0.5) - offset) / length(vec2(0.5));\n weight = pow(1.0 - weight, 10.0);\n result += textureDistorted(offset, normalize(ghostVec), distortion) * weight;\n }\n result *= texture2D(lenscolor, vec2(length(vec2(0.5) - texcoord)) / length(vec2(0.5)));\n float weight = length(vec2(0.5) - fract(texcoord + haloVec)) / length(vec2(0.5));\n weight = pow(1.0 - weight, 10.0);\n vec2 offset = fract(texcoord + haloVec);\n result += textureDistorted(offset, normalize(ghostVec), distortion) * weight;\n gl_FragColor = result;\n}\n@end"),ta.import(Qi),ta.import($i);var ra=/^#source\((.*?)\)/;function ea(t,r){var e=new Vi;r=r||{};var n={textures:{},parameters:{}};for(var i in t.parameters){var a=t.parameters[i];n.parameters[i]=oa(a)}return function(t,r,e,n){if(!t.textures)return void n({});var i={},a=0,o=!1,u=e.textureRootPath;s.each(t.textures,(function(t,r){var e,h=t.path,c=oa(t.parameters);if(Array.isArray(h)&&6===h.length)u&&(h=h.map((function(t){return s.relative2absolute(t,u)}))),e=new gn(c);else{if("string"!=typeof h)return;u&&(h=s.relative2absolute(h,u)),e=new Ce(c)}e.load(h),a++,e.once("success",(function(){i[r]=e,0===--a&&(n(i),o=!0)}))})),0!==a||o||n(i)}(t,0,r,(function(i){n.textures=i,function(){for(var i=0;i<t.nodes.length;i++){var a=na(t.nodes[i],n,r);a&&e.addNode(a)}}()})),e}function na(t,r,e){var n,i,a,o,s=t.type||"filter";if("filter"===s){var u=t.shader.trim(),h=ra.exec(u);if(h?n=sr.source(h[1].trim()):"#"===u.charAt(0)&&(n=r.shaders[u.substr(1)]),n||(n=u),!n)return}if(t.inputs)for(var c in i={},t.inputs)"string"==typeof t.inputs[c]?i[c]=t.inputs[c]:i[c]={node:t.inputs[c].node,pin:t.inputs[c].pin};if(t.outputs)for(var c in a={},t.outputs){var l=t.outputs[c];a[c]={},null!=l.attachment&&(a[c].attachment=l.attachment),null!=l.keepLastFrame&&(a[c].keepLastFrame=l.keepLastFrame),null!=l.outputLastFrame&&(a[c].outputLastFrame=l.outputLastFrame),l.parameters&&(a[c].parameters=oa(l.parameters))}if(o="scene"===s?new Wi({name:t.name,scene:e.scene,camera:e.camera,outputs:a}):"texture"===s?new Gi({name:t.name,outputs:a}):new zi({name:t.name,shader:n,inputs:i,outputs:a})){if(t.parameters)for(var c in t.parameters){"string"==typeof(f=t.parameters[c])?"#"===(f=f.trim()).charAt(0)?f=r.textures[f.substr(1)]:o.on("beforerender",sa(c,ua(f))):"function"==typeof f&&o.on("beforerender",f),o.setParameter(c,f)}if(t.defines&&o.pass)for(var c in t.defines){var f=t.defines[c];o.pass.material.define("fragment",c,f)}}return o}function ia(t,r){return t}function aa(t,r){return r}function oa(t){var r={};if(!t)return r;["type","minFilter","magFilter","wrapS","wrapT","flipY","useMipmap"].forEach((function(e){var n=t[e];null!=n&&("string"==typeof n&&(n=Ee[n]),r[e]=n)}));var e=t.scale||1;return["width","height"].forEach((function(n){if(null!=t[n]){var i=t[n];"string"==typeof i?(i=i.trim(),r[n]=(a=ua(i),o=(o=e)||1,function(t){var r=t.getDevicePixelRatio(),e=t.getWidth()*o,n=t.getHeight()*o;return a(e,n,r)})):r[n]=i}var a,o})),r.width||(r.width=ia),r.height||(r.height=aa),null!=t.useMipmap&&(r.useMipmap=t.useMipmap),r}function sa(t,r){return function(e){var n=e.getDevicePixelRatio(),i=e.getWidth(),a=e.getHeight(),o=r(i,a,n);this.setParameter(t,o)}}function ua(t){var r=/^expr\((.*)\)$/.exec(t);if(r)try{var e=new Function("width","height","dpr","return "+r[1]);return e(1,1),e}catch(n){throw new Error("Invalid expression.")}}export{$i as $,ci as A,he as B,Un as C,li as D,ti as E,an as F,ke as G,Ln as H,ri as I,ea as J,Xi as K,ji as L,It as M,de as N,Jn as O,Nn as P,Qr as Q,Er as R,sr as S,Ce as T,qi as U,Ar as V,Yi as W,Ki as X,Zi as Y,Ji as Z,Qi as _,Bt as a,Fi as a0,pn as b,we as c,Ee as d,hi as e,fi as f,di as g,Tn as h,mi as i,vi as j,xi as k,Ti as l,Kr as m,Je as n,Ur as o,cr as p,ni as q,ii as r,ai as s,Zn as t,bi as u,xe as v,i as w,u as x,Ei as y,kn as z};
