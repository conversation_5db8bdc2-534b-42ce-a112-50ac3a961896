import{s as t}from"./main-Djn9RDyT.js";const e="/edtap/sys-dept/tree",r="/edtap/sys-dept/page",a="/edtap/sys-dept/add",s="/edtap/sys-dept/edit",n="/edtap/sys-dept/delete",d="/edtap/sys-dept/delete-batch",u="/edtap/enterprise/get-login-enterprise",o="/edtap/sys-menu/queryUserPermission",p="/edtap/enterprise/new_query_enterprise_list",i="/edtap/enterprise/get-individual-enterprise",m="/edtap/enterprise/set-default";function l(e){return t({url:o,method:"post",data:e})}function c(e){return t({url:u,method:"post",data:e})}function f(e){return t({url:u,method:"post",data:e})}function h(e){return t({url:i,method:"post",data:e})}function y(r){return t({url:e,method:"get",params:r})}function g(e){return t({url:r,method:"get",params:e})}function q(e){return t({url:a,method:"post",data:e})}function _(e){return t({url:s,method:"post",data:e})}function b(e){return t({url:n,method:"post",data:e})}function j(e){return t({url:d,method:"post",data:e})}function v(e){return t({url:p,method:"post",data:e})}function w(e){return t({url:m,method:"post",data:e})}export{f as a,q as b,g as c,b as d,_ as e,j as f,y as g,h,v as n,l as q,w as s,c as u};
