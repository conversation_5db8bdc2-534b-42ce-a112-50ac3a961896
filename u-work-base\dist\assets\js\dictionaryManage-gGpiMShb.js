import{s as t}from"./main-Djn9RDyT.js";const a="/edtap/sys-dict-type/page",e="/edtap/sys-dict-type/add",d="/edtap/sys-dict-type/edit",s="/edtap/sys-dict-type/delete",r="/edtap/sys-dict-type/list",n="/edtap/sys-dict-data/page",o="/edtap/sys-dict-data/add",p="/edtap/sys-dict-data/edit",u="/edtap/sys-dict-data/delete",i="/edtap/sys-dict-type/drop-down";function c(e){return t({url:a,method:"get",params:e})}function m(){return t({url:r,method:"get"})}function y(a){return t({url:e,method:"post",data:a})}function l(a){return t({url:d,method:"post",data:a})}function f(a){return t({url:s,method:"post",data:a})}function h(a){return t({url:n,method:"get",params:a})}function g(a){return t({url:o,method:"post",data:a})}function b(a){return t({url:p,method:"post",data:a})}function j(a){return t({url:u,method:"post",data:a})}function w(a){return t({url:i,method:"get",params:a})}export{g as a,y as b,l as c,j as d,b as e,c as f,h as g,f as h,m as i,w as s};
