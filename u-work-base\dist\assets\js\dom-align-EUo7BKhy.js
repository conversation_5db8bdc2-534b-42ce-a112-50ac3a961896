function t(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,n)}return o}function e(e){for(var o=1;o<arguments.length;o++){var r=null!=arguments[o]?arguments[o]:{};o%2?t(Object(r),!0).forEach((function(t){n(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}var r,i={Webkit:"-webkit-",Moz:"-moz-",ms:"-ms-",O:"-o-"};function a(){if(void 0!==r)return r;r="";var t=document.createElement("p").style;for(var e in i)e+"Transform"in t&&(r=e);return r}function f(){return a()?"".concat(a(),"TransitionProperty"):"transitionProperty"}function l(){return a()?"".concat(a(),"Transform"):"transform"}function u(t,e){var o=f();o&&(t.style[o]=e,"transitionProperty"!==o&&(t.style.transitionProperty=e))}function c(t,e){var o=l();o&&(t.style[o]=e,"transform"!==o&&(t.style.transform=e))}var s,p=/matrix\((.*)\)/,d=/matrix3d\((.*)\)/;function h(t){var e=t.style.display;t.style.display="none",t.offsetHeight,t.style.display=e}function g(t,e,n){var r=n;if("object"!==o(e))return void 0!==r?("number"==typeof r&&(r="".concat(r,"px")),void(t.style[e]=r)):s(t,e);for(var i in e)e.hasOwnProperty(i)&&g(t,i,e[i])}function v(t,e){var o=t["page".concat(e?"Y":"X","Offset")],n="scroll".concat(e?"Top":"Left");if("number"!=typeof o){var r=t.document;"number"!=typeof(o=r.documentElement[n])&&(o=r.body[n])}return o}function m(t){return v(t)}function y(t){return v(t,!0)}function w(t){var e=function(t){var e,o,n,r=t.ownerDocument,i=r.body,a=r&&r.documentElement;return e=t.getBoundingClientRect(),o=Math.floor(e.left),n=Math.floor(e.top),{left:o-=a.clientLeft||i.clientLeft||0,top:n-=a.clientTop||i.clientTop||0}}(t),o=t.ownerDocument,n=o.defaultView||o.parentWindow;return e.left+=m(n),e.top+=y(n),e}function b(t){return null!=t&&t==t.window}function x(t){return b(t)?t.document:9===t.nodeType?t:t.ownerDocument}var W=new RegExp("^(".concat(/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source,")(?!px)[a-z%]+$"),"i"),O=/^(top|right|bottom|left)$/,P="currentStyle",S="runtimeStyle",j="left";function T(t,e){return"left"===t?e.useCssRight?"right":t:e.useCssBottom?"bottom":t}function M(t){return"left"===t?"right":"right"===t?"left":"top"===t?"bottom":"bottom"===t?"top":void 0}function C(t,e,o){"static"===g(t,"position")&&(t.style.position="relative");var n=-999,r=-999,i=T("left",o),a=T("top",o),l=M(i),c=M(a);"left"!==i&&(n=999),"top"!==a&&(r=999);var s,p="",d=w(t);("left"in e||"top"in e)&&(p=(s=t).style.transitionProperty||s.style[f()]||"",u(t,"none")),"left"in e&&(t.style[l]="",t.style[i]="".concat(n,"px")),"top"in e&&(t.style[c]="",t.style[a]="".concat(r,"px")),h(t);var v=w(t),m={};for(var y in e)if(e.hasOwnProperty(y)){var b=T(y,o),x="left"===y?n:r,W=d[y]-v[y];m[b]=b===y?x+W:x-W}g(t,m),h(t),("left"in e||"top"in e)&&u(t,p);var O={};for(var P in e)if(e.hasOwnProperty(P)){var S=T(P,o),j=e[P]-d[P];O[S]=P===S?m[S]+j:m[S]-j}g(t,O)}function D(t,e){var o=w(t),n=function(t){var e=window.getComputedStyle(t,null),o=e.getPropertyValue("transform")||e.getPropertyValue(l());if(o&&"none"!==o){var n=o.replace(/[^0-9\-.,]/g,"").split(",");return{x:parseFloat(n[12]||n[4],0),y:parseFloat(n[13]||n[5],0)}}return{x:0,y:0}}(t),r={x:n.x,y:n.y};"left"in e&&(r.x=n.x+e.left-o.left),"top"in e&&(r.y=n.y+e.top-o.top),function(t,e){var o=window.getComputedStyle(t,null),n=o.getPropertyValue("transform")||o.getPropertyValue(l());if(n&&"none"!==n){var r,i=n.match(p);i?((r=(i=i[1]).split(",").map((function(t){return parseFloat(t,10)})))[4]=e.x,r[5]=e.y,c(t,"matrix(".concat(r.join(","),")"))):((r=n.match(d)[1].split(",").map((function(t){return parseFloat(t,10)})))[12]=e.x,r[13]=e.y,c(t,"matrix3d(".concat(r.join(","),")")))}else c(t,"translateX(".concat(e.x,"px) translateY(").concat(e.y,"px) translateZ(0)"))}(t,r)}function H(t,e){for(var o=0;o<t.length;o++)e(t[o])}function V(t){return"border-box"===s(t,"boxSizing")}"undefined"!=typeof window&&(s=window.getComputedStyle?function(t,e,o){var n=o,r="",i=x(t);return(n=n||i.defaultView.getComputedStyle(t,null))&&(r=n.getPropertyValue(e)||n[e]),r}:function(t,e){var o=t[P]&&t[P][e];if(W.test(o)&&!O.test(e)){var n=t.style,r=n[j],i=t[S][j];t[S][j]=t[P][j],n[j]="fontSize"===e?"1em":o||0,o=n.pixelLeft+"px",n[j]=r,t[S][j]=i}return""===o?"auto":o});var X=["margin","border","padding"];function Y(t,e,o){var n,r,i,a=0;for(r=0;r<e.length;r++)if(n=e[r])for(i=0;i<o.length;i++){var f=void 0;f="border"===n?"".concat(n).concat(o[i],"Width"):n+o[i],a+=parseFloat(s(t,f))||0}return a}var E={getParent:function(t){var e=t;do{e=11===e.nodeType&&e.host?e.host:e.parentNode}while(e&&1!==e.nodeType&&9!==e.nodeType);return e}};function L(t,e,o){var n=o;if(b(t))return"width"===e?E.viewportWidth(t):E.viewportHeight(t);if(9===t.nodeType)return"width"===e?E.docWidth(t):E.docHeight(t);var r="width"===e?["Left","Right"]:["Top","Bottom"],i="width"===e?Math.floor(t.getBoundingClientRect().width):Math.floor(t.getBoundingClientRect().height),a=V(t),f=0;(null==i||i<=0)&&(i=void 0,(null==(f=s(t,e))||Number(f)<0)&&(f=t.style[e]||0),f=Math.floor(parseFloat(f))||0),void 0===n&&(n=a?1:-1);var l=void 0!==i||a,u=i||f;return-1===n?l?u-Y(t,["border","padding"],r):f:l?1===n?u:u+(2===n?-Y(t,["border"],r):Y(t,["margin"],r)):f+Y(t,X.slice(n),r)}H(["Width","Height"],(function(t){E["doc".concat(t)]=function(e){var o=e.document;return Math.max(o.documentElement["scroll".concat(t)],o.body["scroll".concat(t)],E["viewport".concat(t)](o))},E["viewport".concat(t)]=function(e){var o="client".concat(t),n=e.document,r=n.body,i=n.documentElement[o];return"CSS1Compat"===n.compatMode&&i||r&&r[o]||i}}));var B={position:"absolute",visibility:"hidden",display:"block"};function F(){for(var t=arguments.length,e=new Array(t),o=0;o<t;o++)e[o]=arguments[o];var n,r=e[0];return 0!==r.offsetWidth?n=L.apply(void 0,e):function(t,e,o){var n,r={},i=t.style;for(n in e)e.hasOwnProperty(n)&&(r[n]=i[n],i[n]=e[n]);for(n in o.call(t),e)e.hasOwnProperty(n)&&(i[n]=r[n])}(r,B,(function(){n=L.apply(void 0,e)})),n}function R(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o]);return t}H(["width","height"],(function(t){var e=t.charAt(0).toUpperCase()+t.slice(1);E["outer".concat(e)]=function(e,o){return e&&F(e,t,o?0:1)};var o="width"===t?["Left","Right"]:["Top","Bottom"];E[t]=function(e,n){var r=n;return void 0!==r?e?(V(e)&&(r+=Y(e,["padding","border"],o)),g(e,t,r)):void 0:e&&F(e,t,-1)}}));var k={getWindow:function(t){if(t&&t.document&&t.setTimeout)return t;var e=t.ownerDocument||t;return e.defaultView||e.parentWindow},getDocument:x,offset:function(t,e,o){if(void 0===e)return w(t);!function(t,e,o){if(o.ignoreShake){var n=w(t),r=n.left.toFixed(0),i=n.top.toFixed(0),a=e.left.toFixed(0),f=e.top.toFixed(0);if(r===a&&i===f)return}o.useCssRight||o.useCssBottom?C(t,e,o):o.useCssTransform&&l()in document.body.style?D(t,e):C(t,e,o)}(t,e,o||{})},isWindow:b,each:H,css:g,clone:function(t){var e,o={};for(e in t)t.hasOwnProperty(e)&&(o[e]=t[e]);if(t.overflow)for(e in t)t.hasOwnProperty(e)&&(o.overflow[e]=t.overflow[e]);return o},mix:R,getWindowScrollLeft:function(t){return m(t)},getWindowScrollTop:function(t){return y(t)},merge:function(){for(var t={},e=0;e<arguments.length;e++)k.mix(t,e<0||arguments.length<=e?void 0:arguments[e]);return t},viewportWidth:0,viewportHeight:0};R(k,E);var z=k.getParent;function A(t){if(k.isWindow(t)||9===t.nodeType)return null;var e,o=k.getDocument(t).body,n=k.css(t,"position");if(!("fixed"===n||"absolute"===n))return"html"===t.nodeName.toLowerCase()?null:z(t);for(e=z(t);e&&e!==o&&9!==e.nodeType;e=z(e))if("static"!==(n=k.css(e,"position")))return e;return null}var I=k.getParent;function _(t,e){for(var o={left:0,right:Infinity,top:0,bottom:Infinity},n=A(t),r=k.getDocument(t),i=r.defaultView||r.parentWindow,a=r.body,f=r.documentElement;n;){if(-1!==navigator.userAgent.indexOf("MSIE")&&0===n.clientWidth||n===a||n===f||"visible"===k.css(n,"overflow")){if(n===a||n===f)break}else{var l=k.offset(n);l.left+=n.clientLeft,l.top+=n.clientTop,o.top=Math.max(o.top,l.top),o.right=Math.min(o.right,l.left+n.clientWidth),o.bottom=Math.min(o.bottom,l.top+n.clientHeight),o.left=Math.max(o.left,l.left)}n=A(n)}var u=null;k.isWindow(t)||9===t.nodeType||(u=t.style.position,"absolute"===k.css(t,"position")&&(t.style.position="fixed"));var c=k.getWindowScrollLeft(i),s=k.getWindowScrollTop(i),p=k.viewportWidth(i),d=k.viewportHeight(i),h=f.scrollWidth,g=f.scrollHeight,v=window.getComputedStyle(a);if("hidden"===v.overflowX&&(h=i.innerWidth),"hidden"===v.overflowY&&(g=i.innerHeight),t.style&&(t.style.position=u),e||function(t){if(k.isWindow(t)||9===t.nodeType)return!1;var e=k.getDocument(t),o=e.body,n=null;for(n=I(t);n&&n!==o&&n!==e;n=I(n))if("fixed"===k.css(n,"position"))return!0;return!1}(t))o.left=Math.max(o.left,c),o.top=Math.max(o.top,s),o.right=Math.min(o.right,c+p),o.bottom=Math.min(o.bottom,s+d);else{var m=Math.max(h,c+p);o.right=Math.min(o.right,m);var y=Math.max(g,s+d);o.bottom=Math.min(o.bottom,y)}return o.top>=0&&o.left>=0&&o.bottom>o.top&&o.right>o.left?o:null}function N(t){var e,o,n;if(k.isWindow(t)||9===t.nodeType){var r=k.getWindow(t);e={left:k.getWindowScrollLeft(r),top:k.getWindowScrollTop(r)},o=k.viewportWidth(r),n=k.viewportHeight(r)}else e=k.offset(t),o=k.outerWidth(t),n=k.outerHeight(t);return e.width=o,e.height=n,e}function $(t,e){var o=e.charAt(0),n=e.charAt(1),r=t.width,i=t.height,a=t.left,f=t.top;return"c"===o?f+=i/2:"b"===o&&(f+=i),"c"===n?a+=r/2:"r"===n&&(a+=r),{left:a,top:f}}function U(t,e,o,n,r){var i=$(e,o[1]),a=$(t,o[0]),f=[a.left-i.left,a.top-i.top];return{left:Math.round(t.left-f[0]+n[0]-r[0]),top:Math.round(t.top-f[1]+n[1]-r[1])}}function Z(t,e,o){return t.left<o.left||t.left+e.width>o.right}function q(t,e,o){return t.top<o.top||t.top+e.height>o.bottom}function G(t,e,o){var n=[];return k.each(t,(function(t){n.push(t.replace(e,(function(t){return o[t]})))})),n}function J(t,e){return t[e]=-t[e],t}function K(t,e){return(/%$/.test(t)?parseInt(t.substring(0,t.length-1),10)/100*e:parseInt(t,10))||0}function Q(t,e){t[0]=K(t[0],e.width),t[1]=K(t[1],e.height)}function tt(t,e,o,n){var r=o.points,i=o.offset||[0,0],a=o.targetOffset||[0,0],f=o.overflow,l=o.source||t;i=[].concat(i),a=[].concat(a);var u={},c=0,s=_(l,!(!(f=f||{})||!f.alwaysByViewport)),p=N(l);Q(i,p),Q(a,e);var d=U(p,e,r,i,a),h=k.merge(p,d);if(s&&(f.adjustX||f.adjustY)&&n){if(f.adjustX&&Z(d,p,s)){var g=G(r,/[lr]/gi,{l:"r",r:"l"}),v=J(i,0),m=J(a,0);(function(t,e,o){return t.left>o.right||t.left+e.width<o.left})(U(p,e,g,v,m),p,s)||(c=1,r=g,i=v,a=m)}if(f.adjustY&&q(d,p,s)){var y=G(r,/[tb]/gi,{t:"b",b:"t"}),w=J(i,1),b=J(a,1);(function(t,e,o){return t.top>o.bottom||t.top+e.height<o.top})(U(p,e,y,w,b),p,s)||(c=1,r=y,i=w,a=b)}c&&(d=U(p,e,r,i,a),k.mix(h,d));var x=Z(d,p,s),W=q(d,p,s);if(x||W){var O=r;x&&(O=G(r,/[lr]/gi,{l:"r",r:"l"})),W&&(O=G(r,/[tb]/gi,{t:"b",b:"t"})),r=O,i=o.offset||[0,0],a=o.targetOffset||[0,0]}u.adjustX=f.adjustX&&x,u.adjustY=f.adjustY&&W,(u.adjustX||u.adjustY)&&(h=function(t,e,o,n){var r=k.clone(t),i={width:e.width,height:e.height};return n.adjustX&&r.left<o.left&&(r.left=o.left),n.resizeWidth&&r.left>=o.left&&r.left+i.width>o.right&&(i.width-=r.left+i.width-o.right),n.adjustX&&r.left+i.width>o.right&&(r.left=Math.max(o.right-i.width,o.left)),n.adjustY&&r.top<o.top&&(r.top=o.top),n.resizeHeight&&r.top>=o.top&&r.top+i.height>o.bottom&&(i.height-=r.top+i.height-o.bottom),n.adjustY&&r.top+i.height>o.bottom&&(r.top=Math.max(o.bottom-i.height,o.top)),k.mix(r,i)}(d,p,s,u))}return h.width!==p.width&&k.css(l,"width",k.width(l)+h.width-p.width),h.height!==p.height&&k.css(l,"height",k.height(l)+h.height-p.height),k.offset(l,{left:h.left,top:h.top},{useCssRight:o.useCssRight,useCssBottom:o.useCssBottom,useCssTransform:o.useCssTransform,ignoreShake:o.ignoreShake}),{points:r,offset:i,targetOffset:a,overflow:u}}function et(t,e,o){var n=o.target||e,r=N(n),i=!function(t,e){var o=_(t,e),n=N(t);return!o||n.left+n.width<=o.left||n.top+n.height<=o.top||n.left>=o.right||n.top>=o.bottom}(n,o.overflow&&o.overflow.alwaysByViewport);return tt(t,r,o,i)}function ot(t,o,n){var r,i,a=k.getDocument(t),f=a.defaultView||a.parentWindow,l=k.getWindowScrollLeft(f),u=k.getWindowScrollTop(f),c=k.viewportWidth(f),s=k.viewportHeight(f),p={left:r="pageX"in o?o.pageX:l+o.clientX,top:i="pageY"in o?o.pageY:u+o.clientY,width:0,height:0},d=r>=0&&r<=l+c&&i>=0&&i<=u+s,h=[n.points[0],"cc"];return tt(t,p,e(e({},n),{},{points:h}),d)}et.__getOffsetParent=A,et.__getVisibleRectForElement=_;export{et as a,ot as b};
