import{g as e,r as t,a as i,b as n,C as r,M as o,c as a,d as s,e as l,f as h,A as d,h as c,i as u,m as f,O as p,u as m,j as g,k as _,S as v,p as x,l as y,n as b,o as w,q as T,s as S,t as M,v as D,w as L,x as C,y as A,z as P,B as E,D as N,E as O,G as I,F as R,H as z}from"./echarts-PHL8p6cd.js";import{T as F,V as B,a as G,N as H,S as U,u as V,p as k,b as W,M as j,R as Z,c as X,d as Y,G as q,e as Q,P as K,C as J,A as $,D as ee,f as te,g as ie,h as ne,O as re,i as oe,Q as ae,j as se,k as le,l as he,m as de,n as ce,o as ue,B as fe,F as pe,t as me,q as ge,r as _e,s as ve,v as xe,w as ye,x as be,y as we,z as Te,E as Se,H as Me,I as De,J as Le,K as Ce,L as Ae,U as Pe,W as Ee,X as Ne,Y as Oe,Z as Ie,_ as Re,$ as ze,a0 as Fe}from"./claygl-CnLhJ-kv.js";import{m as Be,b as Ge,by as He,ae as Ue,aY as Ve,J as ke,bz as We,e as je,f as Ze,Z as Xe,a0 as Ye,x as qe,ai as Qe,ag as Ke,_ as Je,k as $e,a1 as et,a5 as tt,bg as it,N as nt,r as rt,a4 as ot,c as at,be as st,aS as lt,y as ht,z as dt,l as ct,aa as ut}from"./zrender-DJ-1imfX.js";var ft=["mousedown","mouseup","mousemove","mouseover","mouseout","click","dblclick","contextmenu"];function pt(e){return"_on"+e}var mt,gt,_t,vt,xt,yt,bt,wt,Tt=function(e){var t=this;this._texture=new F({anisotropic:32,flipY:!1,surface:this,dispose:function(e){t.dispose(),F.prototype.dispose.call(this,e)}}),ft.forEach((function(e){this[pt(e)]=function(t){t.triangle&&this._meshes.forEach((function(i){this.dispatchEvent(e,i,t.triangle,t.point)}),this)}}),this),this._meshes=[],e&&this.setECharts(e),this.onupdate=null};Tt.prototype={constructor:Tt,getTexture:function(){return this._texture},setECharts:function(e){this._chart=e;var t=e.getDom();if(t instanceof HTMLCanvasElement){var i=this,n=e.getZr(),r=n.__oldRefreshImmediately||n.refreshImmediately;n.refreshImmediately=function(){r.call(this),i._texture.dirty(),i.onupdate&&i.onupdate()},n.__oldRefreshImmediately=r}else t=document.createElement("canvas");this._texture.image=t,this._texture.dirty(),this.onupdate&&this.onupdate()},dispatchEvent:(mt=new B,gt=new B,_t=new B,vt=new G,xt=new G,yt=new G,bt=new G,wt=new B,function(e,t,i,n){var r=t.geometry,o=r.attributes.position,a=r.attributes.texcoord0,s=B.dot,l=B.cross;o.get(i[0],mt.array),o.get(i[1],gt.array),o.get(i[2],_t.array),a.get(i[0],vt.array),a.get(i[1],xt.array),a.get(i[2],yt.array),l(wt,gt,_t);var h=s(mt,wt),d=s(n,wt)/h;l(wt,_t,mt);var c=s(n,wt)/h;l(wt,mt,gt);var u=s(n,wt)/h;G.scale(bt,vt,d),G.scaleAndAdd(bt,bt,xt,c),G.scaleAndAdd(bt,bt,yt,u);var f=bt.x*this._chart.getWidth(),p=bt.y*this._chart.getHeight();this._chart.getZr().handler.dispatch(e,{zrX:f,zrY:p})}),attachToMesh:function(e){this._meshes.indexOf(e)>=0||(ft.forEach((function(t){e.on(t,this[pt(t)],this)}),this),this._meshes.push(e))},detachFromMesh:function(e){var t=this._meshes.indexOf(e);t>=0&&this._meshes.splice(t,1),ft.forEach((function(t){e.off(t,this[pt(t)])}),this)},dispose:function(){this._meshes.forEach((function(e){this.detachFromMesh(e)}),this)}};var St=function(){for(var e=0,t=arguments.length;e<t;e++)if(null!=arguments[e])return arguments[e]},Mt=function(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?Ge(t.dataIndex)?Be(t.dataIndex,(function(t){return e.indexOfRawIndex(t)})):e.indexOfRawIndex(t.dataIndex):null!=t.name?Ge(t.name)?Be(t.name,(function(t){return e.indexOfName(t)})):e.indexOfName(t.name):void 0},Dt={_animators:null,getAnimators:function(){return this._animators=this._animators||[],this._animators},animate:function(e,t){this._animators=this._animators||[];var i;if(e){for(var n=e.split("."),r=this,o=0,a=n.length;o<a;o++)r&&(r=r[n[o]]);r&&(i=r)}else i=this;if(null==i)throw new Error("Target "+e+" not exists");var s=this._animators,l=new He(i,t),h=this;return l.during((function(){h.__zr&&h.__zr.refresh()})).done((function(){var e=s.indexOf(l);e>=0&&s.splice(e,1)})),s.push(l),this.__zr&&this.__zr.animation.addAnimator(l),l},stopAnimation:function(e){this._animators=this._animators||[];for(var t=this._animators,i=t.length,n=0;n<i;n++)t[n].stop(e);return t.length=0,this},addAnimatorsToZr:function(e){if(this._animators)for(var t=0;t<this._animators.length;t++)e.animation.addAnimator(this._animators[t])},removeAnimatorsFromZr:function(e){if(this._animators)for(var t=0;t<this._animators.length;t++)e.animation.removeAnimator(this._animators[t])}};function Lt(e){return e instanceof HTMLCanvasElement||e instanceof HTMLImageElement||e instanceof Image}Object.assign(H.prototype,Dt),U.import(V),U.import(k),U.import("\n@export ecgl.common.transformUniforms\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform mat4 worldInverseTranspose : WORLDINVERSETRANSPOSE;\nuniform mat4 world : WORLD;\n@end\n\n@export ecgl.common.attributes\nattribute vec3 position : POSITION;\nattribute vec2 texcoord : TEXCOORD_0;\nattribute vec3 normal : NORMAL;\n@end\n\n@export ecgl.common.uv.header\nuniform vec2 uvRepeat : [1.0, 1.0];\nuniform vec2 uvOffset : [0.0, 0.0];\nuniform vec2 detailUvRepeat : [1.0, 1.0];\nuniform vec2 detailUvOffset : [0.0, 0.0];\n\nvarying vec2 v_Texcoord;\nvarying vec2 v_DetailTexcoord;\n@end\n\n@export ecgl.common.uv.main\nv_Texcoord = texcoord * uvRepeat + uvOffset;\nv_DetailTexcoord = texcoord * detailUvRepeat + detailUvOffset;\n@end\n\n@export ecgl.common.uv.fragmentHeader\nvarying vec2 v_Texcoord;\nvarying vec2 v_DetailTexcoord;\n@end\n\n\n@export ecgl.common.albedo.main\n\n vec4 albedoTexel = vec4(1.0);\n#ifdef DIFFUSEMAP_ENABLED\n albedoTexel = texture2D(diffuseMap, v_Texcoord);\n #ifdef SRGB_DECODE\n albedoTexel = sRGBToLinear(albedoTexel);\n #endif\n#endif\n\n#ifdef DETAILMAP_ENABLED\n vec4 detailTexel = texture2D(detailMap, v_DetailTexcoord);\n #ifdef SRGB_DECODE\n detailTexel = sRGBToLinear(detailTexel);\n #endif\n albedoTexel.rgb = mix(albedoTexel.rgb, detailTexel.rgb, detailTexel.a);\n albedoTexel.a = detailTexel.a + (1.0 - detailTexel.a) * albedoTexel.a;\n#endif\n\n@end\n\n@export ecgl.common.wireframe.vertexHeader\n\n#ifdef WIREFRAME_QUAD\nattribute vec4 barycentric;\nvarying vec4 v_Barycentric;\n#elif defined(WIREFRAME_TRIANGLE)\nattribute vec3 barycentric;\nvarying vec3 v_Barycentric;\n#endif\n\n@end\n\n@export ecgl.common.wireframe.vertexMain\n\n#if defined(WIREFRAME_QUAD) || defined(WIREFRAME_TRIANGLE)\n v_Barycentric = barycentric;\n#endif\n\n@end\n\n\n@export ecgl.common.wireframe.fragmentHeader\n\nuniform float wireframeLineWidth : 1;\nuniform vec4 wireframeLineColor: [0, 0, 0, 0.5];\n\n#ifdef WIREFRAME_QUAD\nvarying vec4 v_Barycentric;\nfloat edgeFactor () {\n vec4 d = fwidth(v_Barycentric);\n vec4 a4 = smoothstep(vec4(0.0), d * wireframeLineWidth, v_Barycentric);\n return min(min(min(a4.x, a4.y), a4.z), a4.w);\n}\n#elif defined(WIREFRAME_TRIANGLE)\nvarying vec3 v_Barycentric;\nfloat edgeFactor () {\n vec3 d = fwidth(v_Barycentric);\n vec3 a3 = smoothstep(vec3(0.0), d * wireframeLineWidth, v_Barycentric);\n return min(min(a3.x, a3.y), a3.z);\n}\n#endif\n\n@end\n\n\n@export ecgl.common.wireframe.fragmentMain\n\n#if defined(WIREFRAME_QUAD) || defined(WIREFRAME_TRIANGLE)\n if (wireframeLineWidth > 0.) {\n vec4 lineColor = wireframeLineColor;\n#ifdef SRGB_DECODE\n lineColor = sRGBToLinear(lineColor);\n#endif\n\n gl_FragColor.rgb = mix(gl_FragColor.rgb, lineColor.rgb, (1.0 - edgeFactor()) * lineColor.a);\n }\n#endif\n@end\n\n\n\n\n@export ecgl.common.bumpMap.header\n\n#ifdef BUMPMAP_ENABLED\nuniform sampler2D bumpMap;\nuniform float bumpScale : 1.0;\n\n\nvec3 bumpNormal(vec3 surfPos, vec3 surfNormal, vec3 baseNormal)\n{\n vec2 dSTdx = dFdx(v_Texcoord);\n vec2 dSTdy = dFdy(v_Texcoord);\n\n float Hll = bumpScale * texture2D(bumpMap, v_Texcoord).x;\n float dHx = bumpScale * texture2D(bumpMap, v_Texcoord + dSTdx).x - Hll;\n float dHy = bumpScale * texture2D(bumpMap, v_Texcoord + dSTdy).x - Hll;\n\n vec3 vSigmaX = dFdx(surfPos);\n vec3 vSigmaY = dFdy(surfPos);\n vec3 vN = surfNormal;\n\n vec3 R1 = cross(vSigmaY, vN);\n vec3 R2 = cross(vN, vSigmaX);\n\n float fDet = dot(vSigmaX, R1);\n\n vec3 vGrad = sign(fDet) * (dHx * R1 + dHy * R2);\n return normalize(abs(fDet) * baseNormal - vGrad);\n\n}\n#endif\n\n@end\n\n@export ecgl.common.normalMap.vertexHeader\n\n#ifdef NORMALMAP_ENABLED\nattribute vec4 tangent : TANGENT;\nvarying vec3 v_Tangent;\nvarying vec3 v_Bitangent;\n#endif\n\n@end\n\n@export ecgl.common.normalMap.vertexMain\n\n#ifdef NORMALMAP_ENABLED\n if (dot(tangent, tangent) > 0.0) {\n v_Tangent = normalize((worldInverseTranspose * vec4(tangent.xyz, 0.0)).xyz);\n v_Bitangent = normalize(cross(v_Normal, v_Tangent) * tangent.w);\n }\n#endif\n\n@end\n\n\n@export ecgl.common.normalMap.fragmentHeader\n\n#ifdef NORMALMAP_ENABLED\nuniform sampler2D normalMap;\nvarying vec3 v_Tangent;\nvarying vec3 v_Bitangent;\n#endif\n\n@end\n\n@export ecgl.common.normalMap.fragmentMain\n#ifdef NORMALMAP_ENABLED\n if (dot(v_Tangent, v_Tangent) > 0.0) {\n vec3 normalTexel = texture2D(normalMap, v_DetailTexcoord).xyz;\n if (dot(normalTexel, normalTexel) > 0.0) { N = normalTexel * 2.0 - 1.0;\n mat3 tbn = mat3(v_Tangent, v_Bitangent, v_Normal);\n N = normalize(tbn * N);\n }\n }\n#endif\n@end\n\n\n\n@export ecgl.common.vertexAnimation.header\n\n#ifdef VERTEX_ANIMATION\nattribute vec3 prevPosition;\nattribute vec3 prevNormal;\nuniform float percent;\n#endif\n\n@end\n\n@export ecgl.common.vertexAnimation.main\n\n#ifdef VERTEX_ANIMATION\n vec3 pos = mix(prevPosition, position, percent);\n vec3 norm = mix(prevNormal, normal, percent);\n#else\n vec3 pos = position;\n vec3 norm = normal;\n#endif\n\n@end\n\n\n@export ecgl.common.ssaoMap.header\n#ifdef SSAOMAP_ENABLED\nuniform sampler2D ssaoMap;\nuniform vec4 viewport : VIEWPORT;\n#endif\n@end\n\n@export ecgl.common.ssaoMap.main\n float ao = 1.0;\n#ifdef SSAOMAP_ENABLED\n ao = texture2D(ssaoMap, (gl_FragCoord.xy - viewport.xy) / viewport.zw).r;\n#endif\n@end\n\n\n\n\n@export ecgl.common.diffuseLayer.header\n\n#if (LAYER_DIFFUSEMAP_COUNT > 0)\nuniform float layerDiffuseIntensity[LAYER_DIFFUSEMAP_COUNT];\nuniform sampler2D layerDiffuseMap[LAYER_DIFFUSEMAP_COUNT];\n#endif\n\n@end\n\n@export ecgl.common.emissiveLayer.header\n\n#if (LAYER_EMISSIVEMAP_COUNT > 0)\nuniform float layerEmissionIntensity[LAYER_EMISSIVEMAP_COUNT];\nuniform sampler2D layerEmissiveMap[LAYER_EMISSIVEMAP_COUNT];\n#endif\n\n@end\n\n@export ecgl.common.layers.header\n@import ecgl.common.diffuseLayer.header\n@import ecgl.common.emissiveLayer.header\n@end\n\n@export ecgl.common.diffuseLayer.main\n\n#if (LAYER_DIFFUSEMAP_COUNT > 0)\n for (int _idx_ = 0; _idx_ < LAYER_DIFFUSEMAP_COUNT; _idx_++) {{\n float intensity = layerDiffuseIntensity[_idx_];\n vec4 texel2 = texture2D(layerDiffuseMap[_idx_], v_Texcoord);\n #ifdef SRGB_DECODE\n texel2 = sRGBToLinear(texel2);\n #endif\n albedoTexel.rgb = mix(albedoTexel.rgb, texel2.rgb * intensity, texel2.a);\n albedoTexel.a = texel2.a + (1.0 - texel2.a) * albedoTexel.a;\n }}\n#endif\n\n@end\n\n@export ecgl.common.emissiveLayer.main\n\n#if (LAYER_EMISSIVEMAP_COUNT > 0)\n for (int _idx_ = 0; _idx_ < LAYER_EMISSIVEMAP_COUNT; _idx_++)\n {{\n vec4 texel2 = texture2D(layerEmissiveMap[_idx_], v_Texcoord) * layerEmissionIntensity[_idx_];\n #ifdef SRGB_DECODE\n texel2 = sRGBToLinear(texel2);\n #endif\n float intensity = layerEmissionIntensity[_idx_];\n gl_FragColor.rgb += texel2.rgb * texel2.a * intensity;\n }}\n#endif\n\n@end\n"),U.import("@export ecgl.color.vertex\n\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\n\n@import ecgl.common.uv.header\n\nattribute vec2 texcoord : TEXCOORD_0;\nattribute vec3 position: POSITION;\n\n@import ecgl.common.wireframe.vertexHeader\n\n#ifdef VERTEX_COLOR\nattribute vec4 a_Color : COLOR;\nvarying vec4 v_Color;\n#endif\n\n#ifdef VERTEX_ANIMATION\nattribute vec3 prevPosition;\nuniform float percent : 1.0;\n#endif\n\n#ifdef ATMOSPHERE_ENABLED\nattribute vec3 normal: NORMAL;\nuniform mat4 worldInverseTranspose : WORLDINVERSETRANSPOSE;\nvarying vec3 v_Normal;\n#endif\n\nvoid main()\n{\n#ifdef VERTEX_ANIMATION\n vec3 pos = mix(prevPosition, position, percent);\n#else\n vec3 pos = position;\n#endif\n\n gl_Position = worldViewProjection * vec4(pos, 1.0);\n\n @import ecgl.common.uv.main\n\n#ifdef VERTEX_COLOR\n v_Color = a_Color;\n#endif\n\n#ifdef ATMOSPHERE_ENABLED\n v_Normal = normalize((worldInverseTranspose * vec4(normal, 0.0)).xyz);\n#endif\n\n @import ecgl.common.wireframe.vertexMain\n\n}\n\n@end\n\n@export ecgl.color.fragment\n\n#define LAYER_DIFFUSEMAP_COUNT 0\n#define LAYER_EMISSIVEMAP_COUNT 0\n\nuniform sampler2D diffuseMap;\nuniform sampler2D detailMap;\n\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\n\n#ifdef ATMOSPHERE_ENABLED\nuniform mat4 viewTranspose: VIEWTRANSPOSE;\nuniform vec3 glowColor;\nuniform float glowPower;\nvarying vec3 v_Normal;\n#endif\n\n#ifdef VERTEX_COLOR\nvarying vec4 v_Color;\n#endif\n\n@import ecgl.common.layers.header\n\n@import ecgl.common.uv.fragmentHeader\n\n@import ecgl.common.wireframe.fragmentHeader\n\n@import clay.util.srgb\n\nvoid main()\n{\n#ifdef SRGB_DECODE\n gl_FragColor = sRGBToLinear(color);\n#else\n gl_FragColor = color;\n#endif\n\n#ifdef VERTEX_COLOR\n gl_FragColor *= v_Color;\n#endif\n\n @import ecgl.common.albedo.main\n\n @import ecgl.common.diffuseLayer.main\n\n gl_FragColor *= albedoTexel;\n\n#ifdef ATMOSPHERE_ENABLED\n float atmoIntensity = pow(1.0 - dot(v_Normal, (viewTranspose * vec4(0.0, 0.0, 1.0, 0.0)).xyz), glowPower);\n gl_FragColor.rgb += glowColor * atmoIntensity;\n#endif\n\n @import ecgl.common.emissiveLayer.main\n\n @import ecgl.common.wireframe.fragmentMain\n\n}\n@end"),U.import("/**\n * http: */\n\n@export ecgl.lambert.vertex\n\n@import ecgl.common.transformUniforms\n\n@import ecgl.common.uv.header\n\n\n@import ecgl.common.attributes\n\n@import ecgl.common.wireframe.vertexHeader\n\n#ifdef VERTEX_COLOR\nattribute vec4 a_Color : COLOR;\nvarying vec4 v_Color;\n#endif\n\n\n@import ecgl.common.vertexAnimation.header\n\n\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\n\nvoid main()\n{\n @import ecgl.common.uv.main\n\n @import ecgl.common.vertexAnimation.main\n\n\n gl_Position = worldViewProjection * vec4(pos, 1.0);\n\n v_Normal = normalize((worldInverseTranspose * vec4(norm, 0.0)).xyz);\n v_WorldPosition = (world * vec4(pos, 1.0)).xyz;\n\n#ifdef VERTEX_COLOR\n v_Color = a_Color;\n#endif\n\n @import ecgl.common.wireframe.vertexMain\n}\n\n@end\n\n\n@export ecgl.lambert.fragment\n\n#define LAYER_DIFFUSEMAP_COUNT 0\n#define LAYER_EMISSIVEMAP_COUNT 0\n\n#define NORMAL_UP_AXIS 1\n#define NORMAL_FRONT_AXIS 2\n\n@import ecgl.common.uv.fragmentHeader\n\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\n\nuniform sampler2D diffuseMap;\nuniform sampler2D detailMap;\n\n@import ecgl.common.layers.header\n\nuniform float emissionIntensity: 1.0;\n\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\n\nuniform mat4 viewInverse : VIEWINVERSE;\n\n#ifdef ATMOSPHERE_ENABLED\nuniform mat4 viewTranspose: VIEWTRANSPOSE;\nuniform vec3 glowColor;\nuniform float glowPower;\n#endif\n\n#ifdef AMBIENT_LIGHT_COUNT\n@import clay.header.ambient_light\n#endif\n#ifdef AMBIENT_SH_LIGHT_COUNT\n@import clay.header.ambient_sh_light\n#endif\n\n#ifdef DIRECTIONAL_LIGHT_COUNT\n@import clay.header.directional_light\n#endif\n\n#ifdef VERTEX_COLOR\nvarying vec4 v_Color;\n#endif\n\n\n@import ecgl.common.ssaoMap.header\n\n@import ecgl.common.bumpMap.header\n\n@import clay.util.srgb\n\n@import ecgl.common.wireframe.fragmentHeader\n\n@import clay.plugin.compute_shadow_map\n\nvoid main()\n{\n#ifdef SRGB_DECODE\n gl_FragColor = sRGBToLinear(color);\n#else\n gl_FragColor = color;\n#endif\n\n#ifdef VERTEX_COLOR\n #ifdef SRGB_DECODE\n gl_FragColor *= sRGBToLinear(v_Color);\n #else\n gl_FragColor *= v_Color;\n #endif\n#endif\n\n @import ecgl.common.albedo.main\n\n @import ecgl.common.diffuseLayer.main\n\n gl_FragColor *= albedoTexel;\n\n vec3 N = v_Normal;\n#ifdef DOUBLE_SIDED\n vec3 eyePos = viewInverse[3].xyz;\n vec3 V = normalize(eyePos - v_WorldPosition);\n\n if (dot(N, V) < 0.0) {\n N = -N;\n }\n#endif\n\n float ambientFactor = 1.0;\n\n#ifdef BUMPMAP_ENABLED\n N = bumpNormal(v_WorldPosition, v_Normal, N);\n ambientFactor = dot(v_Normal, N);\n#endif\n\n vec3 N2 = vec3(N.x, N[NORMAL_UP_AXIS], N[NORMAL_FRONT_AXIS]);\n\n vec3 diffuseColor = vec3(0.0, 0.0, 0.0);\n\n @import ecgl.common.ssaoMap.main\n\n#ifdef AMBIENT_LIGHT_COUNT\n for(int i = 0; i < AMBIENT_LIGHT_COUNT; i++)\n {\n diffuseColor += ambientLightColor[i] * ambientFactor * ao;\n }\n#endif\n#ifdef AMBIENT_SH_LIGHT_COUNT\n for(int _idx_ = 0; _idx_ < AMBIENT_SH_LIGHT_COUNT; _idx_++)\n {{\n diffuseColor += calcAmbientSHLight(_idx_, N2) * ambientSHLightColor[_idx_] * ao;\n }}\n#endif\n#ifdef DIRECTIONAL_LIGHT_COUNT\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n float shadowContribsDir[DIRECTIONAL_LIGHT_COUNT];\n if(shadowEnabled)\n {\n computeShadowOfDirectionalLights(v_WorldPosition, shadowContribsDir);\n }\n#endif\n for(int i = 0; i < DIRECTIONAL_LIGHT_COUNT; i++)\n {\n vec3 lightDirection = -directionalLightDirection[i];\n vec3 lightColor = directionalLightColor[i];\n\n float shadowContrib = 1.0;\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n if (shadowEnabled)\n {\n shadowContrib = shadowContribsDir[i];\n }\n#endif\n\n float ndl = dot(N, normalize(lightDirection)) * shadowContrib;\n\n diffuseColor += lightColor * clamp(ndl, 0.0, 1.0);\n }\n#endif\n\n gl_FragColor.rgb *= diffuseColor;\n\n#ifdef ATMOSPHERE_ENABLED\n float atmoIntensity = pow(1.0 - dot(v_Normal, (viewTranspose * vec4(0.0, 0.0, 1.0, 0.0)).xyz), glowPower);\n gl_FragColor.rgb += glowColor * atmoIntensity;\n#endif\n\n @import ecgl.common.emissiveLayer.main\n\n @import ecgl.common.wireframe.fragmentMain\n}\n\n@end"),U.import("@export ecgl.realistic.vertex\n\n@import ecgl.common.transformUniforms\n\n@import ecgl.common.uv.header\n\n@import ecgl.common.attributes\n\n\n@import ecgl.common.wireframe.vertexHeader\n\n#ifdef VERTEX_COLOR\nattribute vec4 a_Color : COLOR;\nvarying vec4 v_Color;\n#endif\n\n#ifdef NORMALMAP_ENABLED\nattribute vec4 tangent : TANGENT;\nvarying vec3 v_Tangent;\nvarying vec3 v_Bitangent;\n#endif\n\n@import ecgl.common.vertexAnimation.header\n\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\n\nvoid main()\n{\n\n @import ecgl.common.uv.main\n\n @import ecgl.common.vertexAnimation.main\n\n gl_Position = worldViewProjection * vec4(pos, 1.0);\n\n v_Normal = normalize((worldInverseTranspose * vec4(norm, 0.0)).xyz);\n v_WorldPosition = (world * vec4(pos, 1.0)).xyz;\n\n#ifdef VERTEX_COLOR\n v_Color = a_Color;\n#endif\n\n#ifdef NORMALMAP_ENABLED\n v_Tangent = normalize((worldInverseTranspose * vec4(tangent.xyz, 0.0)).xyz);\n v_Bitangent = normalize(cross(v_Normal, v_Tangent) * tangent.w);\n#endif\n\n @import ecgl.common.wireframe.vertexMain\n\n}\n\n@end\n\n\n\n@export ecgl.realistic.fragment\n\n#define LAYER_DIFFUSEMAP_COUNT 0\n#define LAYER_EMISSIVEMAP_COUNT 0\n#define PI 3.14159265358979\n#define ROUGHNESS_CHANEL 0\n#define METALNESS_CHANEL 1\n\n#define NORMAL_UP_AXIS 1\n#define NORMAL_FRONT_AXIS 2\n\n#ifdef VERTEX_COLOR\nvarying vec4 v_Color;\n#endif\n\n@import ecgl.common.uv.fragmentHeader\n\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\n\nuniform sampler2D diffuseMap;\n\nuniform sampler2D detailMap;\nuniform sampler2D metalnessMap;\nuniform sampler2D roughnessMap;\n\n@import ecgl.common.layers.header\n\nuniform float emissionIntensity: 1.0;\n\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\n\nuniform float metalness : 0.0;\nuniform float roughness : 0.5;\n\nuniform mat4 viewInverse : VIEWINVERSE;\n\n#ifdef ATMOSPHERE_ENABLED\nuniform mat4 viewTranspose: VIEWTRANSPOSE;\nuniform vec3 glowColor;\nuniform float glowPower;\n#endif\n\n#ifdef AMBIENT_LIGHT_COUNT\n@import clay.header.ambient_light\n#endif\n\n#ifdef AMBIENT_SH_LIGHT_COUNT\n@import clay.header.ambient_sh_light\n#endif\n\n#ifdef AMBIENT_CUBEMAP_LIGHT_COUNT\n@import clay.header.ambient_cubemap_light\n#endif\n\n#ifdef DIRECTIONAL_LIGHT_COUNT\n@import clay.header.directional_light\n#endif\n\n@import ecgl.common.normalMap.fragmentHeader\n\n@import ecgl.common.ssaoMap.header\n\n@import ecgl.common.bumpMap.header\n\n@import clay.util.srgb\n\n@import clay.util.rgbm\n\n@import ecgl.common.wireframe.fragmentHeader\n\n@import clay.plugin.compute_shadow_map\n\nvec3 F_Schlick(float ndv, vec3 spec) {\n return spec + (1.0 - spec) * pow(1.0 - ndv, 5.0);\n}\n\nfloat D_Phong(float g, float ndh) {\n float a = pow(8192.0, g);\n return (a + 2.0) / 8.0 * pow(ndh, a);\n}\n\nvoid main()\n{\n vec4 albedoColor = color;\n\n vec3 eyePos = viewInverse[3].xyz;\n vec3 V = normalize(eyePos - v_WorldPosition);\n#ifdef VERTEX_COLOR\n #ifdef SRGB_DECODE\n albedoColor *= sRGBToLinear(v_Color);\n #else\n albedoColor *= v_Color;\n #endif\n#endif\n\n @import ecgl.common.albedo.main\n\n @import ecgl.common.diffuseLayer.main\n\n albedoColor *= albedoTexel;\n\n float m = metalness;\n\n#ifdef METALNESSMAP_ENABLED\n float m2 = texture2D(metalnessMap, v_DetailTexcoord)[METALNESS_CHANEL];\n m = clamp(m2 + (m - 0.5) * 2.0, 0.0, 1.0);\n#endif\n\n vec3 baseColor = albedoColor.rgb;\n albedoColor.rgb = baseColor * (1.0 - m);\n vec3 specFactor = mix(vec3(0.04), baseColor, m);\n\n float g = 1.0 - roughness;\n\n#ifdef ROUGHNESSMAP_ENABLED\n float g2 = 1.0 - texture2D(roughnessMap, v_DetailTexcoord)[ROUGHNESS_CHANEL];\n g = clamp(g2 + (g - 0.5) * 2.0, 0.0, 1.0);\n#endif\n\n vec3 N = v_Normal;\n\n#ifdef DOUBLE_SIDED\n if (dot(N, V) < 0.0) {\n N = -N;\n }\n#endif\n\n float ambientFactor = 1.0;\n\n#ifdef BUMPMAP_ENABLED\n N = bumpNormal(v_WorldPosition, v_Normal, N);\n ambientFactor = dot(v_Normal, N);\n#endif\n\n@import ecgl.common.normalMap.fragmentMain\n\n vec3 N2 = vec3(N.x, N[NORMAL_UP_AXIS], N[NORMAL_FRONT_AXIS]);\n\n vec3 diffuseTerm = vec3(0.0);\n vec3 specularTerm = vec3(0.0);\n\n float ndv = clamp(dot(N, V), 0.0, 1.0);\n vec3 fresnelTerm = F_Schlick(ndv, specFactor);\n\n @import ecgl.common.ssaoMap.main\n\n#ifdef AMBIENT_LIGHT_COUNT\n for(int _idx_ = 0; _idx_ < AMBIENT_LIGHT_COUNT; _idx_++)\n {{\n diffuseTerm += ambientLightColor[_idx_] * ambientFactor * ao;\n }}\n#endif\n\n#ifdef AMBIENT_SH_LIGHT_COUNT\n for(int _idx_ = 0; _idx_ < AMBIENT_SH_LIGHT_COUNT; _idx_++)\n {{\n diffuseTerm += calcAmbientSHLight(_idx_, N2) * ambientSHLightColor[_idx_] * ao;\n }}\n#endif\n\n#ifdef DIRECTIONAL_LIGHT_COUNT\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n float shadowContribsDir[DIRECTIONAL_LIGHT_COUNT];\n if(shadowEnabled)\n {\n computeShadowOfDirectionalLights(v_WorldPosition, shadowContribsDir);\n }\n#endif\n for(int _idx_ = 0; _idx_ < DIRECTIONAL_LIGHT_COUNT; _idx_++)\n {{\n vec3 L = -directionalLightDirection[_idx_];\n vec3 lc = directionalLightColor[_idx_];\n\n vec3 H = normalize(L + V);\n float ndl = clamp(dot(N, normalize(L)), 0.0, 1.0);\n float ndh = clamp(dot(N, H), 0.0, 1.0);\n\n float shadowContrib = 1.0;\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n if (shadowEnabled)\n {\n shadowContrib = shadowContribsDir[_idx_];\n }\n#endif\n\n vec3 li = lc * ndl * shadowContrib;\n\n diffuseTerm += li;\n specularTerm += li * fresnelTerm * D_Phong(g, ndh);\n }}\n#endif\n\n\n#ifdef AMBIENT_CUBEMAP_LIGHT_COUNT\n vec3 L = reflect(-V, N);\n L = vec3(L.x, L[NORMAL_UP_AXIS], L[NORMAL_FRONT_AXIS]);\n float rough2 = clamp(1.0 - g, 0.0, 1.0);\n float bias2 = rough2 * 5.0;\n vec2 brdfParam2 = texture2D(ambientCubemapLightBRDFLookup[0], vec2(rough2, ndv)).xy;\n vec3 envWeight2 = specFactor * brdfParam2.x + brdfParam2.y;\n vec3 envTexel2;\n for(int _idx_ = 0; _idx_ < AMBIENT_CUBEMAP_LIGHT_COUNT; _idx_++)\n {{\n envTexel2 = RGBMDecode(textureCubeLodEXT(ambientCubemapLightCubemap[_idx_], L, bias2), 8.12);\n specularTerm += ambientCubemapLightColor[_idx_] * envTexel2 * envWeight2 * ao;\n }}\n#endif\n\n gl_FragColor.rgb = albedoColor.rgb * diffuseTerm + specularTerm;\n gl_FragColor.a = albedoColor.a;\n\n#ifdef ATMOSPHERE_ENABLED\n float atmoIntensity = pow(1.0 - dot(v_Normal, (viewTranspose * vec4(0.0, 0.0, 1.0, 0.0)).xyz), glowPower);\n gl_FragColor.rgb += glowColor * atmoIntensity;\n#endif\n\n#ifdef SRGB_ENCODE\n gl_FragColor = linearTosRGB(gl_FragColor);\n#endif\n\n @import ecgl.common.emissiveLayer.main\n\n @import ecgl.common.wireframe.fragmentMain\n}\n\n@end"),U.import("@export ecgl.hatching.vertex\n\n@import ecgl.realistic.vertex\n\n@end\n\n\n@export ecgl.hatching.fragment\n\n#define NORMAL_UP_AXIS 1\n#define NORMAL_FRONT_AXIS 2\n\n@import ecgl.common.uv.fragmentHeader\n\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\n\nuniform vec4 color : [0.0, 0.0, 0.0, 1.0];\nuniform vec4 paperColor : [1.0, 1.0, 1.0, 1.0];\n\nuniform mat4 viewInverse : VIEWINVERSE;\n\n#ifdef AMBIENT_LIGHT_COUNT\n@import clay.header.ambient_light\n#endif\n#ifdef AMBIENT_SH_LIGHT_COUNT\n@import clay.header.ambient_sh_light\n#endif\n\n#ifdef DIRECTIONAL_LIGHT_COUNT\n@import clay.header.directional_light\n#endif\n\n#ifdef VERTEX_COLOR\nvarying vec4 v_Color;\n#endif\n\n\n@import ecgl.common.ssaoMap.header\n\n@import ecgl.common.bumpMap.header\n\n@import clay.util.srgb\n\n@import ecgl.common.wireframe.fragmentHeader\n\n@import clay.plugin.compute_shadow_map\n\nuniform sampler2D hatch1;\nuniform sampler2D hatch2;\nuniform sampler2D hatch3;\nuniform sampler2D hatch4;\nuniform sampler2D hatch5;\nuniform sampler2D hatch6;\n\nfloat shade(in float tone) {\n vec4 c = vec4(1. ,1., 1., 1.);\n float step = 1. / 6.;\n vec2 uv = v_DetailTexcoord;\n if (tone <= step / 2.0) {\n c = mix(vec4(0.), texture2D(hatch6, uv), 12. * tone);\n }\n else if (tone <= step) {\n c = mix(texture2D(hatch6, uv), texture2D(hatch5, uv), 6. * tone);\n }\n if(tone > step && tone <= 2. * step){\n c = mix(texture2D(hatch5, uv), texture2D(hatch4, uv) , 6. * (tone - step));\n }\n if(tone > 2. * step && tone <= 3. * step){\n c = mix(texture2D(hatch4, uv), texture2D(hatch3, uv), 6. * (tone - 2. * step));\n }\n if(tone > 3. * step && tone <= 4. * step){\n c = mix(texture2D(hatch3, uv), texture2D(hatch2, uv), 6. * (tone - 3. * step));\n }\n if(tone > 4. * step && tone <= 5. * step){\n c = mix(texture2D(hatch2, uv), texture2D(hatch1, uv), 6. * (tone - 4. * step));\n }\n if(tone > 5. * step){\n c = mix(texture2D(hatch1, uv), vec4(1.), 6. * (tone - 5. * step));\n }\n\n return c.r;\n}\n\nconst vec3 w = vec3(0.2125, 0.7154, 0.0721);\n\nvoid main()\n{\n#ifdef SRGB_DECODE\n vec4 inkColor = sRGBToLinear(color);\n#else\n vec4 inkColor = color;\n#endif\n\n#ifdef VERTEX_COLOR\n #ifdef SRGB_DECODE\n inkColor *= sRGBToLinear(v_Color);\n #else\n inkColor *= v_Color;\n #endif\n#endif\n\n vec3 N = v_Normal;\n#ifdef DOUBLE_SIDED\n vec3 eyePos = viewInverse[3].xyz;\n vec3 V = normalize(eyePos - v_WorldPosition);\n\n if (dot(N, V) < 0.0) {\n N = -N;\n }\n#endif\n\n float tone = 0.0;\n\n float ambientFactor = 1.0;\n\n#ifdef BUMPMAP_ENABLED\n N = bumpNormal(v_WorldPosition, v_Normal, N);\n ambientFactor = dot(v_Normal, N);\n#endif\n\n vec3 N2 = vec3(N.x, N[NORMAL_UP_AXIS], N[NORMAL_FRONT_AXIS]);\n\n @import ecgl.common.ssaoMap.main\n\n#ifdef AMBIENT_LIGHT_COUNT\n for(int i = 0; i < AMBIENT_LIGHT_COUNT; i++)\n {\n tone += dot(ambientLightColor[i], w) * ambientFactor * ao;\n }\n#endif\n#ifdef AMBIENT_SH_LIGHT_COUNT\n for(int _idx_ = 0; _idx_ < AMBIENT_SH_LIGHT_COUNT; _idx_++)\n {{\n tone += dot(calcAmbientSHLight(_idx_, N2) * ambientSHLightColor[_idx_], w) * ao;\n }}\n#endif\n#ifdef DIRECTIONAL_LIGHT_COUNT\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n float shadowContribsDir[DIRECTIONAL_LIGHT_COUNT];\n if(shadowEnabled)\n {\n computeShadowOfDirectionalLights(v_WorldPosition, shadowContribsDir);\n }\n#endif\n for(int i = 0; i < DIRECTIONAL_LIGHT_COUNT; i++)\n {\n vec3 lightDirection = -directionalLightDirection[i];\n float lightTone = dot(directionalLightColor[i], w);\n\n float shadowContrib = 1.0;\n#if defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n if (shadowEnabled)\n {\n shadowContrib = shadowContribsDir[i];\n }\n#endif\n\n float ndl = dot(N, normalize(lightDirection)) * shadowContrib;\n\n tone += lightTone * clamp(ndl, 0.0, 1.0);\n }\n#endif\n\n gl_FragColor = mix(inkColor, paperColor, shade(clamp(tone, 0.0, 1.0)));\n }\n@end\n"),U.import("@export ecgl.sm.depth.vertex\n\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\n\nattribute vec3 position : POSITION;\nattribute vec2 texcoord : TEXCOORD_0;\n\n#ifdef VERTEX_ANIMATION\nattribute vec3 prevPosition;\nuniform float percent : 1.0;\n#endif\n\nvarying vec4 v_ViewPosition;\nvarying vec2 v_Texcoord;\n\nvoid main(){\n\n#ifdef VERTEX_ANIMATION\n vec3 pos = mix(prevPosition, position, percent);\n#else\n vec3 pos = position;\n#endif\n\n v_ViewPosition = worldViewProjection * vec4(pos, 1.0);\n gl_Position = v_ViewPosition;\n\n v_Texcoord = texcoord;\n\n}\n@end\n\n\n\n@export ecgl.sm.depth.fragment\n\n@import clay.sm.depth.fragment\n\n@end");var Ct=W.prototype.addToScene,At=W.prototype.removeFromScene;W.prototype.addToScene=function(e){if(Ct.call(this,e),this.__zr){var t=this.__zr;e.traverse((function(e){e.__zr=t,e.addAnimatorsToZr&&e.addAnimatorsToZr(t)}))}},W.prototype.removeFromScene=function(e){At.call(this,e),e.traverse((function(e){var t=e.__zr;e.__zr=null,t&&e.removeAnimatorsFromZr&&e.removeAnimatorsFromZr(t)}))},j.prototype.setTextureImage=function(e,t,i,n){if(this.shader){var r,o,a=i.getZr(),s=this;return s.autoUpdateTextureStatus=!1,s.disableTexture(e),(o=t)&&"none"!==o&&(r=Pt.loadTexture(t,i,n,(function(t){s.enableTexture(e),a&&a.refresh()})),s.set(e,r)),r}};var Pt={};Pt.Renderer=Z,Pt.Node=H,Pt.Mesh=X,Pt.Shader=U,Pt.Material=j,Pt.Texture=Y,Pt.Texture2D=F,Pt.Geometry=q,Pt.SphereGeometry=Q,Pt.PlaneGeometry=K,Pt.CubeGeometry=J,Pt.AmbientLight=$,Pt.DirectionalLight=ee,Pt.PointLight=te,Pt.SpotLight=ie,Pt.PerspectiveCamera=ne,Pt.OrthographicCamera=re,Pt.Vector2=G,Pt.Vector3=B,Pt.Vector4=oe,Pt.Quaternion=ae,Pt.Matrix2=se,Pt.Matrix2d=le,Pt.Matrix3=he,Pt.Matrix4=de,Pt.Plane=ce,Pt.Ray=ue,Pt.BoundingBox=fe,Pt.Frustum=pe;var Et=null;function Nt(e){return Math.pow(2,Math.round(Math.log(e)/Math.LN2))}function Ot(e){if((e.wrapS===Y.REPEAT||e.wrapT===Y.REPEAT)&&e.image){var t=Nt(e.width),i=Nt(e.height);if(t!==e.width||i!==e.height){var n=document.createElement("canvas");n.width=t,n.height=i,n.getContext("2d").drawImage(e.image,0,0,t,i),e.image=n}}}Pt.loadTexture=function(e,t,i,n){"function"==typeof i&&(n=i,i={}),i=i||{};for(var r=Object.keys(i).sort(),o="",a=0;a<r.length;a++)o+=r[a]+"_"+i[r[a]]+"_";var s,l=t.__textureCache=t.__textureCache||new Ue(20);if((s=e).getZr&&s.setOption){var h=e.__textureid__;if(c=l.get(o+h))c.texture.surface.setECharts(e),n&&n(c.texture);else{var d=new Tt(e);d.onupdate=function(){t.getZr().refresh()},c={texture:d.getTexture()};for(a=0;a<r.length;a++)c.texture[r[a]]=i[r[a]];h=e.__textureid__||"__ecgl_ec__"+c.texture.__uid__,e.__textureid__=h,l.put(o+h,c),n&&n(c.texture)}return c.texture}if(Lt(e)){var c;h=e.__textureid__;if(!(c=l.get(o+h))){c={texture:new Pt.Texture2D({image:e})};for(a=0;a<r.length;a++)c.texture[r[a]]=i[r[a]];h=e.__textureid__||"__ecgl_image__"+c.texture.__uid__,e.__textureid__=h,l.put(o+h,c),Ot(c.texture),n&&n(c.texture)}return c.texture}if(c=l.get(o+e))c.callbacks?c.callbacks.push(n):n&&n(c.texture);else if(e.match(/.hdr$|^data:application\/octet-stream/)){c={callbacks:[n]};var u=me.loadTexture(e,{exposure:i.exposure,fileType:"hdr"},(function(){u.dirty(),c.callbacks.forEach((function(e){e&&e(u)})),c.callbacks=null}));c.texture=u,l.put(o+e,c)}else{for(u=new Pt.Texture2D({image:new Image}),a=0;a<r.length;a++)u[r[a]]=i[r[a]];c={texture:u,callbacks:[n]};var f=u.image;f.onload=function(){u.image=f,Ot(u),u.dirty(),c.callbacks.forEach((function(e){e&&e(u)})),c.callbacks=null},f.crossOrigin="Anonymous",f.src=e,u.image=null!==Et?Et:Et=me.createBlank("rgba(255,255,255,0)").image,l.put(o+e,c)}return c.texture},Pt.createAmbientCubemap=function(e,t,i,n){var r=(e=e||{}).texture,o=St(e.exposure,1),a=new ge({intensity:St(e.specularIntensity,1)}),s=new _e({intensity:St(e.diffuseIntensity,1),coefficients:[.844,.712,.691,-.037,.083,.167,.343,.288,.299,-.041,-.021,-.009,-.003,-.041,-.064,-.011,-.007,-.004,-.031,.034,.081,-.06,-.049,-.06,.046,.056,.05]});return a.cubemap=Pt.loadTexture(r,i,{exposure:o},(function(){a.cubemap.flipY=!1,a.prefilter(t,32),s.coefficients=ve.projectEnvironmentMap(t,a.cubemap,{lod:1}),n&&n()})),{specular:a,diffuse:s}},Pt.createBlankTexture=me.createBlank,Pt.isImage=Lt,Pt.additiveBlend=function(e){e.blendEquation(e.FUNC_ADD),e.blendFunc(e.SRC_ALPHA,e.ONE)},Pt.parseColor=function(e,t){return e instanceof Array?(t||(t=[]),t[0]=e[0],t[1]=e[1],t[2]=e[2],e.length>3?t[3]=e[3]:t[3]=1,t):((t=Ve(e||"#000",t)||[0,0,0,0])[0]/=255,t[1]/=255,t[2]/=255,t)},Pt.directionFromAlphaBeta=function(e,t){var i=e/180*Math.PI+Math.PI/2,n=-t/180*Math.PI+Math.PI/2,r=[],o=Math.sin(i);return r[0]=o*Math.cos(n),r[1]=-Math.cos(i),r[2]=o*Math.sin(n),r},Pt.getShadowResolution=function(e){var t=1024;switch(e){case"low":t=512;break;case"medium":break;case"high":t=2048;break;case"ultra":t=4096}return t},Pt.COMMON_SHADERS=["lambert","color","realistic","hatching","shadow"],Pt.createShader=function(e){"ecgl.shadow"===e&&(e="ecgl.displayShadow");var t=U.source(e+".vertex"),i=U.source(e+".fragment"),n=new U(t,i);return n.name=e,n},Pt.createMaterial=function(e,t){t instanceof Array||(t=[t]);var i=Pt.createShader(e),n=new j({shader:i});return t.forEach((function(e){"string"==typeof e&&n.define(e)})),n},Pt.setMaterialFromModel=function(e,t,i,n){t.autoUpdateTextureStatus=!1;var r=i.getModel(e+"Material"),o=r.get("detailTexture"),a=St(r.get("textureTiling"),1),s=St(r.get("textureOffset"),0);"number"==typeof a&&(a=[a,a]),"number"==typeof s&&(s=[s,s]);var l=a[0]>1||a[1]>1?Pt.Texture.REPEAT:Pt.Texture.CLAMP_TO_EDGE,h={anisotropic:8,wrapS:l,wrapT:l};if("realistic"===e){var d=r.get("roughness"),c=r.get("metalness");null!=c?isNaN(c)&&(t.setTextureImage("metalnessMap",c,n,h),c=St(r.get("metalnessAdjust"),.5)):c=0,null!=d?isNaN(d)&&(t.setTextureImage("roughnessMap",d,n,h),d=St(r.get("roughnessAdjust"),.5)):d=.5;var u=r.get("normalTexture");t.setTextureImage("detailMap",o,n,h),t.setTextureImage("normalMap",u,n,h),t.set({roughness:d,metalness:c,detailUvRepeat:a,detailUvOffset:s})}else if("lambert"===e)t.setTextureImage("detailMap",o,n,h),t.set({detailUvRepeat:a,detailUvOffset:s});else if("color"===e)t.setTextureImage("detailMap",o,n,h),t.set({detailUvRepeat:a,detailUvOffset:s});else if("hatching"===e){var f=r.get("hatchingTextures")||[];f.length;for(var p=0;p<6;p++)t.setTextureImage("hatch"+(p+1),f[p],n,{anisotropic:8,wrapS:Pt.Texture.REPEAT,wrapT:Pt.Texture.REPEAT});t.set({detailUvRepeat:a,detailUvOffset:s})}},Pt.updateVertexAnimation=function(e,t,i,n){var r=n.get("animation"),o=n.get("animationDurationUpdate"),a=n.get("animationEasingUpdate"),s=i.shadowDepthMaterial;if(r&&t&&o>0&&t.geometry.vertexCount===i.geometry.vertexCount){i.material.define("vertex","VERTEX_ANIMATION"),i.ignorePreZ=!0,s&&s.define("vertex","VERTEX_ANIMATION");for(var l=0;l<e.length;l++)i.geometry.attributes[e[l][0]].value=t.geometry.attributes[e[l][1]].value;i.geometry.dirty(),i.__percent=0,i.material.set("percent",0),i.stopAnimation(),i.animate().when(o,{__percent:1}).during((function(){i.material.set("percent",i.__percent),s&&s.set("percent",i.__percent)})).done((function(){i.ignorePreZ=!1,i.material.undefine("vertex","VERTEX_ANIMATION"),s&&s.undefine("vertex","VERTEX_ANIMATION")})).start(a)}else i.material.undefine("vertex","VERTEX_ANIMATION"),s&&s.undefine("vertex","VERTEX_ANIMATION")};var It=function(e,t){this.id=e,this.zr=t;try{this.renderer=new Z({clearBit:0,devicePixelRatio:t.painter.dpr,preserveDrawingBuffer:!0,premultipliedAlpha:!0}),this.renderer.resize(t.painter.getWidth(),t.painter.getHeight())}catch(n){return this.renderer=null,this.dom=document.createElement("div"),this.dom.style.cssText="position:absolute; left: 0; top: 0; right: 0; bottom: 0;",this.dom.className="ecgl-nowebgl",void(this.dom.innerHTML="Sorry, your browser does not support WebGL")}this.onglobalout=this.onglobalout.bind(this),t.on("globalout",this.onglobalout),this.dom=this.renderer.canvas;var i=this.dom.style;i.position="absolute",i.left="0",i.top="0",this.views=[],this._picking=new xe({renderer:this.renderer}),this._viewsToDispose=[],this._accumulatingId=0,this._zrEventProxy=new ke({shape:{x:-1,y:-1,width:2,height:2},__isGLToZRProxy:!0}),this._backgroundColor=null,this._disposed=!1};function Rt(e){var t=e.__zr;e.__zr=null,t&&e.removeAnimatorsFromZr&&e.removeAnimatorsFromZr(t)}It.prototype.setUnpainted=function(){},It.prototype.addView=function(e){if(e.layer!==this){var t=this._viewsToDispose.indexOf(e);t>=0&&this._viewsToDispose.splice(t,1),this.views.push(e),e.layer=this;var i=this.zr;e.scene.traverse((function(e){e.__zr=i,e.addAnimatorsToZr&&e.addAnimatorsToZr(i)}))}},It.prototype.removeView=function(e){if(e.layer===this){var t=this.views.indexOf(e);t>=0&&(this.views.splice(t,1),e.scene.traverse(Rt,this),e.layer=null,this._viewsToDispose.push(e))}},It.prototype.removeViewsAll=function(){this.views.forEach((function(e){e.scene.traverse(Rt,this),e.layer=null,this._viewsToDispose.push(e)}),this),this.views.length=0},It.prototype.resize=function(e,t){this.renderer.resize(e,t)},It.prototype.clear=function(){var e=this.renderer.gl,t=this._backgroundColor||[0,0,0,0];e.clearColor(t[0],t[1],t[2],t[3]),e.depthMask(!0),e.colorMask(!0,!0,!0,!0),e.clear(e.DEPTH_BUFFER_BIT|e.COLOR_BUFFER_BIT)},It.prototype.clearDepth=function(){var e=this.renderer.gl;e.clear(e.DEPTH_BUFFER_BIT)},It.prototype.clearColor=function(){var e=this.renderer.gl;e.clearColor(0,0,0,0),e.clear(e.COLOR_BUFFER_BIT)},It.prototype.needsRefresh=function(){this.zr.refresh()},It.prototype.refresh=function(e){this._backgroundColor=e?Pt.parseColor(e):[0,0,0,0],this.renderer.clearColor=this._backgroundColor;for(var t=0;t<this.views.length;t++)this.views[t].prepareRender(this.renderer);this._doRender(!1),this._trackAndClean();for(t=0;t<this._viewsToDispose.length;t++)this._viewsToDispose[t].dispose(this.renderer);this._viewsToDispose.length=0,this._startAccumulating()},It.prototype.renderToCanvas=function(e){this._startAccumulating(!0),e.drawImage(this.dom,0,0,e.canvas.width,e.canvas.height)},It.prototype._doRender=function(e){this.clear(),this.renderer.saveViewport();for(var t=0;t<this.views.length;t++)this.views[t].render(this.renderer,e);this.renderer.restoreViewport()},It.prototype._stopAccumulating=function(){this._accumulatingId=0,clearTimeout(this._accumulatingTimeout)};var zt=1;function Ft(e){for(var t=0;t<e.length;t++)e[t].__used__=0}function Bt(e,t){for(var i=0;i<t.length;i++)t[i].__used__||t[i].dispose(e)}function Gt(e,t){e.__used__=e.__used__||0,e.__used__++,1===e.__used__&&t.push(e)}function Ht(e,t,i){var n,r;e.traverse((function(e){if(e.isRenderable()){var o=e.geometry,a=e.material;if(a!==n)for(var s=a.getTextureUniforms(),l=0;l<s.length;l++){var h=s[l],d=a.uniforms[h].value;if(d)if(d instanceof Y)Gt(d,t);else if(d instanceof Array)for(var c=0;c<d.length;c++)d[c]instanceof Y&&Gt(d[c],t)}o!==r&&Gt(o,i),n=a,r=o}}));for(var o=0;o<e.lights.length;o++)e.lights[o].cubemap&&Gt(e.lights[o].cubemap,t)}It.prototype._startAccumulating=function(e){var t=this;this._stopAccumulating();for(var i=!1,n=0;n<this.views.length;n++)i=this.views[n].needsAccumulate()||i;function r(n){if(t._accumulatingId&&n===t._accumulatingId){for(var o=!0,a=0;a<t.views.length;a++)o=t.views[a].isAccumulateFinished()&&i;o||(t._doRender(!0),e?r(n):We((function(){r(n)})))}}i&&(this._accumulatingId=zt++,e?r(t._accumulatingId):this._accumulatingTimeout=setTimeout((function(){r(t._accumulatingId)}),50))},It.prototype._trackAndClean=function(){var e=[],t=[];this._textureList&&(Ft(this._textureList),Ft(this._geometriesList));for(var i=0;i<this.views.length;i++)Ht(this.views[i].scene,e,t);this._textureList&&(Bt(this.renderer,this._textureList),Bt(this.renderer,this._geometriesList)),this._textureList=e,this._geometriesList=t},It.prototype.dispose=function(){this._disposed||(this._stopAccumulating(),this._textureList&&(Ft(this._textureList),Ft(this._geometriesList),Bt(this.renderer,this._textureList),Bt(this.renderer,this._geometriesList)),this.zr.off("globalout",this.onglobalout),this._disposed=!0)},It.prototype.onmousedown=function(e){if(!e.target||!e.target.__isGLToZRProxy){e=e.event;var t=this.pickObject(e.offsetX,e.offsetY);t&&(this._dispatchEvent("mousedown",e,t),this._dispatchDataEvent("mousedown",e,t)),this._downX=e.offsetX,this._downY=e.offsetY}},It.prototype.onmousemove=function(e){if(!e.target||!e.target.__isGLToZRProxy){e=e.event;var t=this.pickObject(e.offsetX,e.offsetY),i=t&&t.target,n=this._hovered;this._hovered=t,n&&i!==n.target&&(n.relatedTarget=i,this._dispatchEvent("mouseout",e,n),this.zr.setCursorStyle("default")),this._dispatchEvent("mousemove",e,t),t&&(this.zr.setCursorStyle("pointer"),n&&i===n.target||this._dispatchEvent("mouseover",e,t)),this._dispatchDataEvent("mousemove",e,t)}},It.prototype.onmouseup=function(e){if(!e.target||!e.target.__isGLToZRProxy){e=e.event;var t=this.pickObject(e.offsetX,e.offsetY);t&&(this._dispatchEvent("mouseup",e,t),this._dispatchDataEvent("mouseup",e,t)),this._upX=e.offsetX,this._upY=e.offsetY}},It.prototype.onclick=It.prototype.dblclick=function(e){if(!e.target||!e.target.__isGLToZRProxy){var t=this._upX-this._downX,i=this._upY-this._downY;if(!(Math.sqrt(t*t+i*i)>20)){e=e.event;var n=this.pickObject(e.offsetX,e.offsetY);n&&(this._dispatchEvent(e.type,e,n),this._dispatchDataEvent(e.type,e,n));var r=this._clickToSetFocusPoint(e);if(r)r.view.setDOFFocusOnPoint(r.distance)&&this.zr.refresh()}}},It.prototype._clickToSetFocusPoint=function(e){for(var t=this.renderer,i=t.viewport,n=this.views.length-1;n>=0;n--){var r=this.views[n];if(r.hasDOF()&&r.containPoint(e.offsetX,e.offsetY)){this._picking.scene=r.scene,this._picking.camera=r.camera,t.viewport=r.viewport;var o=this._picking.pick(e.offsetX,e.offsetY,!0);if(o)return o.view=r,o}}t.viewport=i},It.prototype.onglobalout=function(e){var t=this._hovered;t&&this._dispatchEvent("mouseout",e,{target:t.target})},It.prototype.pickObject=function(e,t){for(var i=[],n=this.renderer,r=n.viewport,o=0;o<this.views.length;o++){var a=this.views[o];a.containPoint(e,t)&&(this._picking.scene=a.scene,this._picking.camera=a.camera,n.viewport=a.viewport,this._picking.pickAll(e,t,i))}return n.viewport=r,i.sort((function(e,t){return e.distance-t.distance})),i[0]},It.prototype._dispatchEvent=function(e,t,i){i||(i={});var n=i.target;for(i.cancelBubble=!1,i.event=t,i.type=e,i.offsetX=t.offsetX,i.offsetY=t.offsetY;n&&(n.trigger(e,i),n=n.getParent(),!i.cancelBubble););this._dispatchToView(e,i)},It.prototype._dispatchDataEvent=function(t,i,n){var r=n&&n.target,o=r&&r.dataIndex,a=r&&r.seriesIndex,s=r&&r.eventData,l=!1,h=this._zrEventProxy;h.x=i.offsetX,h.y=i.offsetY,h.update();var d={target:h};const c=e(h);"mousemove"===t&&(null!=o?o!==this._lastDataIndex&&(parseInt(this._lastDataIndex,10)>=0&&(c.dataIndex=this._lastDataIndex,c.seriesIndex=this._lastSeriesIndex,this.zr.handler.dispatchToElement(d,"mouseout",i)),l=!0):null!=s&&s!==this._lastEventData&&(null!=this._lastEventData&&(c.eventData=this._lastEventData,this.zr.handler.dispatchToElement(d,"mouseout",i)),l=!0),this._lastEventData=s,this._lastDataIndex=o,this._lastSeriesIndex=a),c.eventData=s,c.dataIndex=o,c.seriesIndex=a,(null!=s||parseInt(o,10)>=0&&parseInt(a,10)>=0)&&(this.zr.handler.dispatchToElement(d,t,i),l&&this.zr.handler.dispatchToElement(d,"mouseover",i))},It.prototype._dispatchToView=function(e,t){for(var i=0;i<this.views.length;i++)this.views[i].containPoint(t.offsetX,t.offsetY)&&this.views[i].trigger(e,t)},Object.assign(It.prototype,ye);var Ut=["bar3D","line3D","map3D","scatter3D","surface","lines3D","scatterGL","scatter3D"];function Vt(e,t){if(e&&e[t]&&(e[t].normal||e[t].emphasis)){var i=e[t].normal,n=e[t].emphasis;i&&(e[t]=i),n&&(e.emphasis=e.emphasis||{},e.emphasis[t]=n)}}function kt(e){e&&(e instanceof Array||(e=[e]),je(e,(function(e){if(e.axisLabel){var t=e.axisLabel;Object.assign(t,t.textStyle),t.textStyle=null}})))}function Wt(e){this._layers={},this._zr=e}Wt.prototype.update=function(e,t){var i=this,n=t.getZr();if(n.getWidth()&&n.getHeight()){for(var r in this._layers)this._layers[r].removeViewsAll();e.eachComponent((function(i,n){if("series"!==i){var r=t.getViewOfComponentModel(n),s=n.coordinateSystem;if(r.__ecgl__){if(s){if(!s.viewGL)return;l=s.viewGL}else{if(!n.viewGL)return;l=s.viewGL}var l=s.viewGL,h=o(n);h.addView(l),r.afterRender&&r.afterRender(n,e,t,h),a(r.groupGL,n.get("silent"))}}})),e.eachSeries((function(i){var n=t.getViewOfSeriesModel(i),r=i.coordinateSystem;if(n.__ecgl__){if(r&&!r.viewGL&&!n.viewGL)return;var s=r&&r.viewGL||n.viewGL,l=o(i);l.addView(s),n.afterRender&&n.afterRender(i,e,t,l),a(n.groupGL,i.get("silent"))}}))}function o(e){var t;n.setSleepAfterStill(0),t=(e.coordinateSystem&&e.coordinateSystem.model,e.get("zlevel"));var r=i._layers,o=r[t];if(!o){if(o=r[t]=new It("gl-"+t,n),n.painter.isSingleCanvas()){o.virtual=!0;var a=new Xe({z:1e4,style:{image:o.renderer.canvas},silent:!0});o.__hostImage=a,n.add(a)}n.painter.insertLayer(t,o)}return o.__hostImage&&o.__hostImage.setStyle({width:o.renderer.getWidth(),height:o.renderer.getHeight()}),o}function a(e,t){e&&e.traverse((function(e){e.isRenderable&&e.isRenderable()&&(e.ignorePicking=null!=e.$ignorePicking?e.$ignorePicking:t)}))}},t((function(e){var t=e.getZr(),i=t.painter.dispose;t.painter.dispose=function(){"function"==typeof this.eachOtherLayer&&this.eachOtherLayer((function(e){e instanceof It&&e.dispose()})),i.call(this)},t.painter.getRenderedCanvas=function(e){if(e=e||{},this._singleCanvas)return this._layers[0].dom;var t=document.createElement("canvas"),i=e.pixelRatio||this.dpr;t.width=this.getWidth()*i,t.height=this.getHeight()*i;var n=t.getContext("2d");n.dpr=i,n.clearRect(0,0,t.width,t.height),e.backgroundColor&&(n.fillStyle=e.backgroundColor,n.fillRect(0,0,t.width,t.height));var r,o=this.storage.getDisplayList(!0),a={},s=this;function l(e,t){var i,r=s._zlevelList;null==e&&(e=-Infinity);for(var o=0;o<r.length;o++){var a=r[o],l=s._layers[a];if(!l.__builtin__&&a>e&&a<t){i=l;break}}i&&i.renderToCanvas&&(n.save(),i.renderToCanvas(n),n.restore())}for(var h={ctx:n},d=0;d<o.length;d++){var c=o[d];c.zlevel!==r&&(l(r,c.zlevel),r=c.zlevel),this._doPaintEl(c,h,!0,null,a)}return l(r,Infinity),t}})),i((function(e,t){var i=t.getZr();(i.__egl=i.__egl||new Wt(i)).update(e,t)})),n((function(e){je(e.series,(function(t){Ze(Ut,t.type)>=0&&(!function(e){Vt(e,"itemStyle"),Vt(e,"lineStyle"),Vt(e,"areaStyle"),Vt(e,"label")}(t),"mapbox"===t.coordinateSystem&&(t.coordinateSystem="mapbox3D",e.mapbox3D=e.mapbox))})),kt(e.xAxis3D),kt(e.yAxis3D),kt(e.zAxis3D),kt(e.grid3D),Vt(e.geo3D)}));const jt={defaultOption:{viewControl:{projection:"perspective",autoRotate:!1,autoRotateDirection:"cw",autoRotateSpeed:10,autoRotateAfterStill:3,damping:.8,rotateSensitivity:1,zoomSensitivity:1,panSensitivity:1,panMouseButton:"middle",rotateMouseButton:"left",distance:150,minDistance:40,maxDistance:400,orthographicSize:150,maxOrthographicSize:400,minOrthographicSize:20,center:[0,0,0],alpha:0,beta:0,minAlpha:-90,maxAlpha:90}},setView:function(e){e=e||{},this.option.viewControl=this.option.viewControl||{},null!=e.alpha&&(this.option.viewControl.alpha=e.alpha),null!=e.beta&&(this.option.viewControl.beta=e.beta),null!=e.distance&&(this.option.viewControl.distance=e.distance),null!=e.center&&(this.option.viewControl.center=e.center)}},Zt={defaultOption:{postEffect:{enable:!1,bloom:{enable:!0,intensity:.1},depthOfField:{enable:!1,focalRange:20,focalDistance:50,blurRadius:10,fstop:2.8,quality:"medium"},screenSpaceAmbientOcclusion:{enable:!1,radius:2,quality:"medium",intensity:1},screenSpaceReflection:{enable:!1,quality:"medium",maxRoughness:.8},colorCorrection:{enable:!0,exposure:0,brightness:0,contrast:1,saturation:1,lookupTexture:""},edge:{enable:!1},FXAA:{enable:!1}},temporalSuperSampling:{enable:"auto"}}},Xt={defaultOption:{light:{main:{shadow:!1,shadowQuality:"high",color:"#fff",intensity:1,alpha:0,beta:0},ambient:{color:"#fff",intensity:.2},ambientCubemap:{texture:null,exposure:1,diffuseIntensity:.5,specularIntensity:.5}}}};var Yt=r.extend({type:"grid3D",dependencies:["xAxis3D","yAxis3D","zAxis3D"],defaultOption:{show:!0,zlevel:-10,left:0,top:0,width:"100%",height:"100%",environment:"auto",boxWidth:100,boxHeight:100,boxDepth:100,axisPointer:{show:!0,lineStyle:{color:"rgba(0, 0, 0, 0.8)",width:1},label:{show:!0,formatter:null,margin:8,textStyle:{fontSize:14,color:"#fff",backgroundColor:"rgba(0,0,0,0.5)",padding:3,borderRadius:3}}},axisLine:{show:!0,lineStyle:{color:"#333",width:2,type:"solid"}},axisTick:{show:!0,inside:!1,length:3,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,margin:8,textStyle:{fontSize:12}},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}},light:{main:{alpha:30,beta:40},ambient:{intensity:.4}},viewControl:{alpha:20,beta:40,autoRotate:!1,distance:200,minDistance:40,maxDistance:400}}});Ye(Yt.prototype,jt),Ye(Yt.prototype,Zt),Ye(Yt.prototype,Xt);var qt=St,Qt={left:0,middle:1,right:2};function Kt(e){return e instanceof Array||(e=[e,e]),e}var Jt=be.extend((function(){return{zr:null,viewGL:null,_center:new B,minDistance:.5,maxDistance:1.5,maxOrthographicSize:300,minOrthographicSize:30,minAlpha:-90,maxAlpha:90,minBeta:-Infinity,maxBeta:Infinity,autoRotateAfterStill:0,autoRotateDirection:"cw",autoRotateSpeed:60,damping:.8,rotateSensitivity:1,zoomSensitivity:1,panSensitivity:1,panMouseButton:"middle",rotateMouseButton:"left",_mode:"rotate",_camera:null,_needsUpdate:!1,_rotating:!1,_phi:0,_theta:0,_mouseX:0,_mouseY:0,_rotateVelocity:new G,_panVelocity:new G,_distance:500,_zoomSpeed:0,_stillTimeout:0,_animators:[]}}),(function(){["_mouseDownHandler","_mouseWheelHandler","_mouseMoveHandler","_mouseUpHandler","_pinchHandler","_contextMenuHandler","_update"].forEach((function(e){this[e]=this[e].bind(this)}),this)}),{init:function(){var e=this.zr;e&&(e.on("mousedown",this._mouseDownHandler),e.on("globalout",this._mouseUpHandler),e.on("mousewheel",this._mouseWheelHandler),e.on("pinch",this._pinchHandler),e.animation.on("frame",this._update),e.dom.addEventListener("contextmenu",this._contextMenuHandler))},dispose:function(){var e=this.zr;e&&(e.off("mousedown",this._mouseDownHandler),e.off("mousemove",this._mouseMoveHandler),e.off("mouseup",this._mouseUpHandler),e.off("mousewheel",this._mouseWheelHandler),e.off("pinch",this._pinchHandler),e.off("globalout",this._mouseUpHandler),e.dom.removeEventListener("contextmenu",this._contextMenuHandler),e.animation.off("frame",this._update)),this.stopAllAnimation()},getDistance:function(){return this._distance},setDistance:function(e){this._distance=e,this._needsUpdate=!0},getOrthographicSize:function(){return this._orthoSize},setOrthographicSize:function(e){this._orthoSize=e,this._needsUpdate=!0},getAlpha:function(){return this._theta/Math.PI*180},getBeta:function(){return-this._phi/Math.PI*180},getCenter:function(){return this._center.toArray()},setAlpha:function(e){e=Math.max(Math.min(this.maxAlpha,e),this.minAlpha),this._theta=e/180*Math.PI,this._needsUpdate=!0},setBeta:function(e){e=Math.max(Math.min(this.maxBeta,e),this.minBeta),this._phi=-e/180*Math.PI,this._needsUpdate=!0},setCenter:function(e){this._center.setArray(e)},setViewGL:function(e){this.viewGL=e},getCamera:function(){return this.viewGL.camera},setFromViewControlModel:function(e,t){var i=(t=t||{}).baseDistance||0,n=t.baseOrthoSize||1,r=e.get("projection");"perspective"!==r&&"orthographic"!==r&&"isometric"!==r&&(r="perspective"),this._projection=r,this.viewGL.setProjection(r);var o=e.get("distance")+i,a=e.get("orthographicSize")+n;[["damping",.8],["autoRotate",!1],["autoRotateAfterStill",3],["autoRotateDirection","cw"],["autoRotateSpeed",10],["minDistance",30],["maxDistance",400],["minOrthographicSize",30],["maxOrthographicSize",300],["minAlpha",-90],["maxAlpha",90],["minBeta",-Infinity],["maxBeta",Infinity],["rotateSensitivity",1],["zoomSensitivity",1],["panSensitivity",1],["panMouseButton","left"],["rotateMouseButton","middle"]].forEach((function(t){this[t[0]]=qt(e.get(t[0]),t[1])}),this),this.minDistance+=i,this.maxDistance+=i,this.minOrthographicSize+=n,this.maxOrthographicSize+=n;var s=e.ecModel,l={};["animation","animationDurationUpdate","animationEasingUpdate"].forEach((function(t){l[t]=qt(e.get(t),s&&s.get(t))}));var h=qt(t.alpha,e.get("alpha"))||0,d=qt(t.beta,e.get("beta"))||0,c=qt(t.center,e.get("center"))||[0,0,0];l.animation&&l.animationDurationUpdate>0&&this._notFirst?this.animateTo({alpha:h,beta:d,center:c,distance:o,orthographicSize:a,easing:l.animationEasingUpdate,duration:l.animationDurationUpdate}):(this.setDistance(o),this.setAlpha(h),this.setBeta(d),this.setCenter(c),this.setOrthographicSize(a)),this._notFirst=!0,this._validateProperties()},_validateProperties:function(){},animateTo:function(e){var t=this.zr,i=this,n={},r={};return null!=e.distance&&(n.distance=this.getDistance(),r.distance=e.distance),null!=e.orthographicSize&&(n.orthographicSize=this.getOrthographicSize(),r.orthographicSize=e.orthographicSize),null!=e.alpha&&(n.alpha=this.getAlpha(),r.alpha=e.alpha),null!=e.beta&&(n.beta=this.getBeta(),r.beta=e.beta),null!=e.center&&(n.center=this.getCenter(),r.center=e.center),this._addAnimator(t.animation.animate(n).when(e.duration||1e3,r).during((function(){null!=n.alpha&&i.setAlpha(n.alpha),null!=n.beta&&i.setBeta(n.beta),null!=n.distance&&i.setDistance(n.distance),null!=n.center&&i.setCenter(n.center),null!=n.orthographicSize&&i.setOrthographicSize(n.orthographicSize),i._needsUpdate=!0}))).start(e.easing||"linear")},stopAllAnimation:function(){for(var e=0;e<this._animators.length;e++)this._animators[e].stop();this._animators.length=0},update:function(){this._needsUpdate=!0,this._update(20)},_isAnimating:function(){return this._animators.length>0},_update:function(e){if(this._rotating){var t=("cw"===this.autoRotateDirection?1:-1)*this.autoRotateSpeed/180*Math.PI;this._phi-=t*e/1e3,this._needsUpdate=!0}else this._rotateVelocity.len()>0&&(this._needsUpdate=!0);(Math.abs(this._zoomSpeed)>.1||this._panVelocity.len()>0)&&(this._needsUpdate=!0),this._needsUpdate&&(e=Math.min(e,50),this._updateDistanceOrSize(e),this._updatePan(e),this._updateRotate(e),this._updateTransform(),this.getCamera().update(),this.zr&&this.zr.refresh(),this.trigger("update"),this._needsUpdate=!1)},_updateRotate:function(e){var t=this._rotateVelocity;this._phi=t.y*e/20+this._phi,this._theta=t.x*e/20+this._theta,this.setAlpha(this.getAlpha()),this.setBeta(this.getBeta()),this._vectorDamping(t,Math.pow(this.damping,e/16))},_updateDistanceOrSize:function(e){"perspective"===this._projection?this._setDistance(this._distance+this._zoomSpeed*e/20):this._setOrthoSize(this._orthoSize+this._zoomSpeed*e/20),this._zoomSpeed*=Math.pow(this.damping,e/16)},_setDistance:function(e){this._distance=Math.max(Math.min(e,this.maxDistance),this.minDistance)},_setOrthoSize:function(e){this._orthoSize=Math.max(Math.min(e,this.maxOrthographicSize),this.minOrthographicSize);var t=this.getCamera(),i=this._orthoSize,n=i/this.viewGL.viewport.height*this.viewGL.viewport.width;t.left=-n/2,t.right=n/2,t.top=i/2,t.bottom=-i/2},_updatePan:function(e){var t=this._panVelocity,i=this._distance,n=this.getCamera(),r=n.worldTransform.y,o=n.worldTransform.x;this._center.scaleAndAdd(o,-t.x*i/200).scaleAndAdd(r,-t.y*i/200),this._vectorDamping(t,0)},_updateTransform:function(){var e=this.getCamera(),t=new B,i=this._theta+Math.PI/2,n=this._phi+Math.PI/2,r=Math.sin(i);t.x=r*Math.cos(n),t.y=-Math.cos(i),t.z=r*Math.sin(n),e.position.copy(this._center).scaleAndAdd(t,this._distance),e.rotation.identity().rotateY(-this._phi).rotateX(-this._theta)},_startCountingStill:function(){clearTimeout(this._stillTimeout);var e=this.autoRotateAfterStill,t=this;!isNaN(e)&&e>0&&(this._stillTimeout=setTimeout((function(){t._rotating=!0}),1e3*e))},_vectorDamping:function(e,t){var i=e.len();(i*=t)<1e-4&&(i=0),e.normalize().scale(i)},_decomposeTransform:function(){if(this.getCamera()){this.getCamera().updateWorldTransform();var e=this.getCamera().worldTransform.z,t=Math.asin(e.y),i=Math.atan2(e.x,e.z);this._theta=t,this._phi=-i,this.setBeta(this.getBeta()),this.setAlpha(this.getAlpha()),this.getCamera().aspect?this._setDistance(this.getCamera().position.dist(this._center)):this._setOrthoSize(this.getCamera().top-this.getCamera().bottom)}},_mouseDownHandler:function(e){if(!e.target&&!this._isAnimating()){var t=e.offsetX,i=e.offsetY;this.viewGL&&!this.viewGL.containPoint(t,i)||(this.zr.on("mousemove",this._mouseMoveHandler),this.zr.on("mouseup",this._mouseUpHandler),e.event.targetTouches?1===e.event.targetTouches.length&&(this._mode="rotate"):e.event.button===Qt[this.rotateMouseButton]?this._mode="rotate":e.event.button===Qt[this.panMouseButton]?this._mode="pan":this._mode="",this._rotateVelocity.set(0,0),this._rotating=!1,this.autoRotate&&this._startCountingStill(),this._mouseX=e.offsetX,this._mouseY=e.offsetY)}},_mouseMoveHandler:function(e){if(!(e.target&&e.target.__isGLToZRProxy||this._isAnimating())){var t=Kt(this.panSensitivity),i=Kt(this.rotateSensitivity);"rotate"===this._mode?(this._rotateVelocity.y=(e.offsetX-this._mouseX)/this.zr.getHeight()*2*i[0],this._rotateVelocity.x=(e.offsetY-this._mouseY)/this.zr.getWidth()*2*i[1]):"pan"===this._mode&&(this._panVelocity.x=(e.offsetX-this._mouseX)/this.zr.getWidth()*t[0]*400,this._panVelocity.y=(-e.offsetY+this._mouseY)/this.zr.getHeight()*t[1]*400),this._mouseX=e.offsetX,this._mouseY=e.offsetY,e.event.preventDefault()}},_mouseWheelHandler:function(e){if(!this._isAnimating()){var t=e.event.wheelDelta||-e.event.detail;this._zoomHandler(e,t)}},_pinchHandler:function(e){this._isAnimating()||(this._zoomHandler(e,e.pinchScale>1?1:-1),this._mode="")},_zoomHandler:function(e,t){if(0!==t){var i,n=e.offsetX,r=e.offsetY;if(!this.viewGL||this.viewGL.containPoint(n,r))i="perspective"===this._projection?Math.max(Math.max(Math.min(this._distance-this.minDistance,this.maxDistance-this._distance))/20,.5):Math.max(Math.max(Math.min(this._orthoSize-this.minOrthographicSize,this.maxOrthographicSize-this._orthoSize))/20,.5),this._zoomSpeed=(t>0?-1:1)*i*this.zoomSensitivity,this._rotating=!1,this.autoRotate&&"rotate"===this._mode&&this._startCountingStill(),e.event.preventDefault()}},_mouseUpHandler:function(){this.zr.off("mousemove",this._mouseMoveHandler),this.zr.off("mouseup",this._mouseUpHandler)},_isRightMouseButtonUsed:function(){return"right"===this.rotateMouseButton||"right"===this.panMouseButton},_contextMenuHandler:function(e){this._isRightMouseButtonUsed()&&e.preventDefault()},_addAnimator:function(e){var t=this._animators;return t.push(e),e.done((function(){var i=t.indexOf(e);i>=0&&t.splice(i,1)})),e}});Object.defineProperty(Jt.prototype,"autoRotate",{get:function(e){return this._autoRotate},set:function(e){this._autoRotate=e,this._rotating=e}});const $t={convertToDynamicArray:function(e){e&&this.resetOffset();var t=this.attributes;for(var i in t)e||!t[i].value?t[i].value=[]:t[i].value=Array.prototype.slice.call(t[i].value);e||!this.indices?this.indices=[]:this.indices=Array.prototype.slice.call(this.indices)},convertToTypedArray:function(){var e=this.attributes;for(var t in e)e[t].value&&e[t].value.length>0?e[t].value=new Float32Array(e[t].value):e[t].value=null;this.indices&&this.indices.length>0&&(this.indices=this.vertexCount>65535?new Uint32Array(this.indices):new Uint16Array(this.indices)),this.dirty()}};var ei=we.vec3,ti=[[0,0],[1,1]],ii=q.extend((function(){return{segmentScale:1,dynamic:!0,useNativeLine:!0,attributes:{position:new q.Attribute("position","float",3,"POSITION"),positionPrev:new q.Attribute("positionPrev","float",3),positionNext:new q.Attribute("positionNext","float",3),prevPositionPrev:new q.Attribute("prevPositionPrev","float",3),prevPosition:new q.Attribute("prevPosition","float",3),prevPositionNext:new q.Attribute("prevPositionNext","float",3),offset:new q.Attribute("offset","float",1),color:new q.Attribute("color","float",4,"COLOR")}}}),{resetOffset:function(){this._vertexOffset=0,this._triangleOffset=0,this._itemVertexOffsets=[]},setVertexCount:function(e){var t=this.attributes;this.vertexCount!==e&&(t.position.init(e),t.color.init(e),this.useNativeLine||(t.positionPrev.init(e),t.positionNext.init(e),t.offset.init(e)),e>65535?this.indices instanceof Uint16Array&&(this.indices=new Uint32Array(this.indices)):this.indices instanceof Uint32Array&&(this.indices=new Uint16Array(this.indices)))},setTriangleCount:function(e){this.triangleCount!==e&&(this.indices=0===e?null:this.vertexCount>65535?new Uint32Array(3*e):new Uint16Array(3*e))},_getCubicCurveApproxStep:function(e,t,i,n){return 1/(ei.dist(e,t)+ei.dist(i,t)+ei.dist(n,i)+1)*this.segmentScale},getCubicCurveVertexCount:function(e,t,i,n){var r=this._getCubicCurveApproxStep(e,t,i,n),o=Math.ceil(1/r);return this.useNativeLine?2*o:2*o+2},getCubicCurveTriangleCount:function(e,t,i,n){var r=this._getCubicCurveApproxStep(e,t,i,n),o=Math.ceil(1/r);return this.useNativeLine?0:2*o},getLineVertexCount:function(){return this.getPolylineVertexCount(ti)},getLineTriangleCount:function(){return this.getPolylineTriangleCount(ti)},getPolylineVertexCount:function(e){var t;"number"==typeof e?t=e:t="number"!=typeof e[0]?e.length:e.length/3;return this.useNativeLine?2*(t-1):2*(t-1)+2},getPolylineTriangleCount:function(e){var t;"number"==typeof e?t=e:t="number"!=typeof e[0]?e.length:e.length/3;return this.useNativeLine?0:2*Math.max(t-1,0)},addCubicCurve:function(e,t,i,n,r,o){null==o&&(o=1);var a=e[0],s=e[1],l=e[2],h=t[0],d=t[1],c=t[2],u=i[0],f=i[1],p=i[2],m=n[0],g=n[1],_=n[2],v=this._getCubicCurveApproxStep(e,t,i,n),x=v*v,y=x*v,b=3*v,w=3*x,T=6*x,S=6*y,M=a-2*h+u,D=s-2*d+f,L=l-2*c+p,C=3*(h-u)-a+m,A=3*(d-f)-s+g,P=3*(c-p)-l+_,E=a,N=s,O=l,I=(h-a)*b+M*w+C*y,R=(d-s)*b+D*w+A*y,z=(c-l)*b+L*w+P*y,F=M*T+C*S,B=D*T+A*S,G=L*T+P*S,H=C*S,U=A*S,V=P*S,k=0,W=0,j=Math.ceil(1/v),Z=new Float32Array(3*(j+1)),X=(Z=[],0);for(W=0;W<j+1;W++)Z[X++]=E,Z[X++]=N,Z[X++]=O,E+=I,N+=R,O+=z,I+=F,R+=B,z+=G,F+=H,B+=U,G+=V,(k+=v)>1&&(E=I>0?Math.min(E,m):Math.max(E,m),N=R>0?Math.min(N,g):Math.max(N,g),O=z>0?Math.min(O,_):Math.max(O,_));return this.addPolyline(Z,r,o)},addLine:function(e,t,i,n){return this.addPolyline([e,t],i,n)},addPolyline:function(e,t,i,n,r){if(e.length){var o="number"!=typeof e[0];if(null==r&&(r=o?e.length:e.length/3),!(r<2)){null==n&&(n=0),null==i&&(i=1),this._itemVertexOffsets.push(this._vertexOffset);var a,s,l=(o="number"!=typeof e[0])?"number"!=typeof t[0]:t.length/4===r,h=this.attributes.position,d=this.attributes.positionPrev,c=this.attributes.positionNext,u=this.attributes.color,f=this.attributes.offset,p=this.indices,m=this._vertexOffset;i=Math.max(i,.01);for(var g=n;g<r;g++){if(o)a=e[g],s=l?t[g]:t;else{var _=3*g;if((a=a||[])[0]=e[_],a[1]=e[_+1],a[2]=e[_+2],l){var v=4*g;(s=s||[])[0]=t[v],s[1]=t[v+1],s[2]=t[v+2],s[3]=t[v+3]}else s=t}if(this.useNativeLine?g>1&&(h.copy(m,m-1),u.copy(m,m-1),m++):(g<r-1&&(d.set(m+2,a),d.set(m+3,a)),g>0&&(c.set(m-2,a),c.set(m-1,a)),h.set(m,a),h.set(m+1,a),u.set(m,s),u.set(m+1,s),f.set(m,i/2),f.set(m+1,-i/2),m+=2),this.useNativeLine)u.set(m,s),h.set(m,a),m++;else if(g>0){var x=3*this._triangleOffset;(p=this.indices)[x]=m-4,p[x+1]=m-3,p[x+2]=m-2,p[x+3]=m-3,p[x+4]=m-1,p[x+5]=m-2,this._triangleOffset+=2}}if(!this.useNativeLine){var y=this._vertexOffset,b=this._vertexOffset+2*r;d.copy(y,y+2),d.copy(y+1,y+3),c.copy(b-1,b-3),c.copy(b-2,b-4)}return this._vertexOffset=m,this._vertexOffset}}},setItemColor:function(e,t){for(var i=this._itemVertexOffsets[e],n=e<this._itemVertexOffsets.length-1?this._itemVertexOffsets[e+1]:this._vertexOffset,r=i;r<n;r++)this.attributes.color.set(r,t);this.dirty("color")},currentTriangleOffset:function(){return this._triangleOffset},currentVertexOffset:function(){return this._vertexOffset}});function ni(e,t,i,n,r,o,a){this._zr=e,this._x=0,this._y=0,this._rowHeight=0,this.width=n,this.height=r,this.offsetX=t,this.offsetY=i,this.dpr=a,this.gap=o}function ri(e){(e=e||{}).width=e.width||512,e.height=e.height||512,e.devicePixelRatio=e.devicePixelRatio||1,e.gap=null==e.gap?2:e.gap;var t=document.createElement("canvas");t.width=e.width*e.devicePixelRatio,t.height=e.height*e.devicePixelRatio,this._canvas=t,this._texture=new F({image:t,flipY:!1});var i=this;this._zr=Qe(t);var n=this._zr.refreshImmediately;this._zr.refreshImmediately=function(){n.call(this),i._texture.dirty(),i.onupdate&&i.onupdate()},this._dpr=e.devicePixelRatio,this._coords={},this.onupdate=e.onupdate,this._gap=e.gap,this._textureAtlasNodes=[new ni(this._zr,0,0,e.width,e.height,this._gap,this._dpr)],this._nodeWidth=e.width,this._nodeHeight=e.height,this._currentNodeIdx=0}function oi(){}qe(ii.prototype,$t),ni.prototype={constructor:ni,clear:function(){this._x=0,this._y=0,this._rowHeight=0},add:function(e,t,i){var n=e.getBoundingRect();null==t&&(t=n.width),null==i&&(i=n.height),t*=this.dpr,i*=this.dpr,this._fitElement(e,t,i);var r=this._x,o=this._y,a=this.width*this.dpr,s=this.height*this.dpr,l=this.gap;if(r+t+l>a&&(r=this._x=0,o+=this._rowHeight+l,this._y=o,this._rowHeight=0),this._x+=t+l,this._rowHeight=Math.max(this._rowHeight,i),o+i+l>s)return null;e.x+=this.offsetX*this.dpr+r,e.y+=this.offsetY*this.dpr+o,this._zr.add(e);var h=[this.offsetX/this.width,this.offsetY/this.height];return[[r/a+h[0],o/s+h[1]],[(r+t)/a+h[0],(o+i)/s+h[1]]]},_fitElement:function(e,t,i){var n=e.getBoundingRect(),r=t/n.width,o=i/n.height;e.x=-n.x*r,e.y=-n.y*o,e.scaleX=r,e.scaleY=o,e.update()}},ri.prototype={clear:function(){for(var e=0;e<this._textureAtlasNodes.length;e++)this._textureAtlasNodes[e].clear();this._currentNodeIdx=0,this._zr.clear(),this._coords={}},getWidth:function(){return this._width},getHeight:function(){return this._height},getTexture:function(){return this._texture},getDevicePixelRatio:function(){return this._dpr},getZr:function(){return this._zr},_getCurrentNode:function(){return this._textureAtlasNodes[this._currentNodeIdx]},_expand:function(){if(this._currentNodeIdx++,this._textureAtlasNodes[this._currentNodeIdx])return this._textureAtlasNodes[this._currentNodeIdx];var e=4096/this._dpr,t=this._textureAtlasNodes.length,i=t*this._nodeWidth%e,n=Math.floor(t*this._nodeWidth/e)*this._nodeHeight;if(!(n>=e)){var r=(i+this._nodeWidth)*this._dpr,o=(n+this._nodeHeight)*this._dpr;try{this._zr.resize({width:r,height:o})}catch(s){this._canvas.width=r,this._canvas.height=o}var a=new ni(this._zr,i,n,this._nodeWidth,this._nodeHeight,this._gap,this._dpr);return this._textureAtlasNodes.push(a),a}},add:function(e,t,i){if(this._coords[e.id])return this._coords[e.id];var n=this._getCurrentNode().add(e,t,i);if(!n){var r=this._expand();if(!r)return;n=r.add(e,t,i)}return this._coords[e.id]=n,n},getCoordsScale:function(){var e=this._dpr;return[this._nodeWidth/this._canvas.width*e,this._nodeHeight/this._canvas.height*e]},getCoords:function(e){return this._coords[e]},dispose:function(){this._zr.dispose()}},oi.prototype={constructor:oi,setScene:function(e){this._scene=e,this._skybox&&this._skybox.attachScene(this._scene)},initLight:function(e){this._lightRoot=e,this.mainLight=new Pt.DirectionalLight({shadowBias:.005}),this.ambientLight=new Pt.AmbientLight,e.add(this.mainLight),e.add(this.ambientLight)},dispose:function(){this._lightRoot&&(this._lightRoot.remove(this.mainLight),this._lightRoot.remove(this.ambientLight))},updateLight:function(e){var t=this.mainLight,i=this.ambientLight,n=e.getModel("light"),r=n.getModel("main"),o=n.getModel("ambient");t.intensity=r.get("intensity"),i.intensity=o.get("intensity"),t.color=Pt.parseColor(r.get("color")).slice(0,3),i.color=Pt.parseColor(o.get("color")).slice(0,3);var a=r.get("alpha")||0,s=r.get("beta")||0;t.position.setArray(Pt.directionFromAlphaBeta(a,s)),t.lookAt(Pt.Vector3.ZERO),t.castShadow=r.get("shadow"),t.shadowResolution=Pt.getShadowResolution(r.get("shadowQuality"))},updateAmbientCubemap:function(e,t,i){var n=t.getModel("light.ambientCubemap"),r=n.get("texture");if(r){this._cubemapLightsCache=this._cubemapLightsCache||{};var o=this._cubemapLightsCache[r];if(!o){var a=this;o=this._cubemapLightsCache[r]=Pt.createAmbientCubemap(n.option,e,i,(function(){a._isSkyboxFromAmbientCubemap&&a._skybox.setEnvironmentMap(o.specular.cubemap),i.getZr().refresh()}))}this._lightRoot.add(o.diffuse),this._lightRoot.add(o.specular),this._currentCubemapLights=o}else this._currentCubemapLights&&(this._lightRoot.remove(this._currentCubemapLights.diffuse),this._lightRoot.remove(this._currentCubemapLights.specular),this._currentCubemapLights=null)},updateSkybox:function(e,t,i){var n=t.get("environment"),r=this;var o=(r._skybox=r._skybox||new Te,r._skybox);if(n&&"none"!==n)if("auto"===n)if(this._isSkyboxFromAmbientCubemap=!0,this._currentCubemapLights){var a=this._currentCubemapLights.specular.cubemap;o.setEnvironmentMap(a),this._scene&&o.attachScene(this._scene),o.material.set("lod",3)}else this._skybox&&this._skybox.detachScene();else if("object"==typeof n&&n.colorStops||"string"==typeof n&&Ve(n)){this._isSkyboxFromAmbientCubemap=!1;var s=new Pt.Texture2D({anisotropic:8,flipY:!1});o.setEnvironmentMap(s);var l=s.image=document.createElement("canvas");l.width=l.height=16;var h=l.getContext("2d"),d=new ke({shape:{x:0,y:0,width:16,height:16},style:{fill:n}});Ke(h,d),o.attachScene(this._scene)}else{this._isSkyboxFromAmbientCubemap=!1;s=Pt.loadTexture(n,i,{anisotropic:8,flipY:!1});o.setEnvironmentMap(s),o.attachScene(this._scene)}else this._skybox&&this._skybox.detachScene(this._scene),this._skybox=null;var c=t.coordinateSystem;if(this._skybox)if(!c||!c.viewGL||"auto"===n||n.match&&n.match(/.hdr$/))this._skybox.material.undefine("fragment","SRGB_DECODE");else{var u=c.viewGL.isLinearSpace()?"define":"undefine";this._skybox.material[u]("fragment","SRGB_DECODE")}}};var ai,si,li,hi,di=we.vec3,ci=q.extend((function(){return{segmentScale:1,useNativeLine:!0,attributes:{position:new q.Attribute("position","float",3,"POSITION"),normal:new q.Attribute("normal","float",3,"NORMAL"),color:new q.Attribute("color","float",4,"COLOR")}}}),{resetOffset:function(){this._vertexOffset=0,this._faceOffset=0},setQuadCount:function(e){var t=this.attributes,i=this.getQuadVertexCount()*e,n=this.getQuadTriangleCount()*e;this.vertexCount!==i&&(t.position.init(i),t.normal.init(i),t.color.init(i)),this.triangleCount!==n&&(this.indices=i>65535?new Uint32Array(3*n):new Uint16Array(3*n))},getQuadVertexCount:function(){return 4},getQuadTriangleCount:function(){return 2},addQuad:(ai=di.create(),si=di.create(),li=di.create(),hi=[0,3,1,3,2,1],function(e,t){var i=this.attributes.position,n=this.attributes.normal,r=this.attributes.color;di.sub(ai,e[1],e[0]),di.sub(si,e[2],e[1]),di.cross(li,ai,si),di.normalize(li,li);for(var o=0;o<4;o++)i.set(this._vertexOffset+o,e[o]),r.set(this._vertexOffset+o,t),n.set(this._vertexOffset+o,li);var a=3*this._faceOffset;for(o=0;o<6;o++)this.indices[a+o]=hi[o]+this._vertexOffset;this._vertexOffset+=4,this._faceOffset+=2})});qe(ci.prototype,$t);var ui=St,fi={x:0,y:2,z:1};function pi(e,t,i){this.rootNode=new Pt.Node;var n=new Pt.Mesh({geometry:new ii({useNativeLine:!1}),material:t,castShadow:!1,ignorePicking:!0,$ignorePicking:!0,renderOrder:1}),r=new Pt.Mesh({geometry:new ci,material:i,castShadow:!1,culling:!1,ignorePicking:!0,$ignorePicking:!0,renderOrder:0});this.rootNode.add(r),this.rootNode.add(n),this.faceInfo=e,this.plane=new Pt.Plane,this.linesMesh=n,this.quadsMesh=r}pi.prototype.update=function(e,t,i){var n=e.coordinateSystem,r=[n.getAxis(this.faceInfo[0]),n.getAxis(this.faceInfo[1])],o=this.linesMesh.geometry,a=this.quadsMesh.geometry;o.convertToDynamicArray(!0),a.convertToDynamicArray(!0),this._updateSplitLines(o,r,e,i),this._udpateSplitAreas(a,r,e,i),o.convertToTypedArray(),a.convertToTypedArray();var s=n.getAxis(this.faceInfo[2]);!function(e,t,i,n){var r=[0,0,0],o=n<0?i.getExtentMin():i.getExtentMax();r[fi[i.dim]]=o,e.position.setArray(r),e.rotation.identity(),t.distance=-Math.abs(o),t.normal.set(0,0,0),"x"===i.dim?(e.rotation.rotateY(n*Math.PI/2),t.normal.x=-n):"z"===i.dim?(e.rotation.rotateX(-n*Math.PI/2),t.normal.y=-n):(n>0&&e.rotation.rotateY(Math.PI),t.normal.z=-n)}(this.rootNode,this.plane,s,this.faceInfo[3])},pi.prototype._updateSplitLines=function(e,t,i,n){var r=n.getDevicePixelRatio();t.forEach((function(n,o){var a=n.model,s=t[1-o].getExtent();if(!n.scale.isBlank()){var l=a.getModel("splitLine",i.getModel("splitLine"));if(l.get("show")){var h=l.getModel("lineStyle"),d=h.get("color"),c=ui(h.get("opacity"),1),u=ui(h.get("width"),1);d=Ge(d)?d:[d];for(var f=n.getTicksCoords({tickModel:l}),p=0,m=0;m<f.length;m++){var g=f[m].coord,_=Pt.parseColor(d[p%d.length]);_[3]*=c;var v=[0,0,0],x=[0,0,0];v[o]=x[o]=g,v[1-o]=s[0],x[1-o]=s[1],e.addLine(v,x,_,u*r),p++}}}}))},pi.prototype._udpateSplitAreas=function(e,t,i,n){t.forEach((function(n,r){var o=n.model,a=t[1-r].getExtent();if(!n.scale.isBlank()){var s=o.getModel("splitArea",i.getModel("splitArea"));if(s.get("show")){var l=s.getModel("areaStyle"),h=l.get("color"),d=ui(l.get("opacity"),1);h=Ge(h)?h:[h];for(var c=n.getTicksCoords({tickModel:s,clamp:!0}),u=0,f=[0,0,0],p=[0,0,0],m=0;m<c.length;m++){var g=c[m].coord,_=[0,0,0],v=[0,0,0];if(_[r]=v[r]=g,_[1-r]=a[0],v[1-r]=a[1],0!==m){var x=Pt.parseColor(h[u%h.length]);x[3]*=d,e.addQuad([f,_,v,p],x),f=_,p=v,u++}else f=_,p=v}}}}))};var mi=[0,1,2,0,2,3],gi=q.extend((function(){return{attributes:{position:new q.Attribute("position","float",3,"POSITION"),texcoord:new q.Attribute("texcoord","float",2,"TEXCOORD_0"),offset:new q.Attribute("offset","float",2),color:new q.Attribute("color","float",4,"COLOR")}}}),{resetOffset:function(){this._vertexOffset=0,this._faceOffset=0},setSpriteCount:function(e){this._spriteCount=e;var t=4*e,i=2*e;this.vertexCount!==t&&(this.attributes.position.init(t),this.attributes.offset.init(t),this.attributes.color.init(t)),this.triangleCount!==i&&(this.indices=t>65535?new Uint32Array(3*i):new Uint16Array(3*i))},setSpriteAlign:function(e,t,i,n,r){var o,a,s,l;switch(null==i&&(i="left"),null==n&&(n="top"),r=r||0,i){case"left":o=r,s=t[0]+r;break;case"center":case"middle":o=-t[0]/2,s=t[0]/2;break;case"right":o=-t[0]-r,s=-r}switch(n){case"bottom":a=r,l=t[1]+r;break;case"middle":a=-t[1]/2,l=t[1]/2;break;case"top":a=-t[1]-r,l=-r}var h=4*e,d=this.attributes.offset;d.set(h,[o,l]),d.set(h+1,[s,l]),d.set(h+2,[s,a]),d.set(h+3,[o,a])},addSprite:function(e,t,i,n,r,o){var a=this._vertexOffset;this.setSprite(this._vertexOffset/4,e,t,i,n,r,o);for(var s=0;s<mi.length;s++)this.indices[3*this._faceOffset+s]=mi[s]+a;return this._faceOffset+=2,this._vertexOffset+=4,a/4},setSprite:function(e,t,i,n,r,o,a){for(var s=4*e,l=this.attributes,h=0;h<4;h++)l.position.set(s+h,t);var d=l.texcoord;d.set(s,[n[0][0],n[0][1]]),d.set(s+1,[n[1][0],n[0][1]]),d.set(s+2,[n[1][0],n[1][1]]),d.set(s+3,[n[0][0],n[1][1]]),this.setSpriteAlign(e,i,r,o,a)}});qe(gi.prototype,$t);Pt.Shader.import("@export ecgl.labels.vertex\n\nattribute vec3 position: POSITION;\nattribute vec2 texcoord: TEXCOORD_0;\nattribute vec2 offset;\n#ifdef VERTEX_COLOR\nattribute vec4 a_Color : COLOR;\nvarying vec4 v_Color;\n#endif\n\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform vec4 viewport : VIEWPORT;\n\nvarying vec2 v_Texcoord;\n\nvoid main()\n{\n vec4 proj = worldViewProjection * vec4(position, 1.0);\n\n vec2 screen = (proj.xy / abs(proj.w) + 1.0) * 0.5 * viewport.zw;\n\n screen += offset;\n\n proj.xy = (screen / viewport.zw - 0.5) * 2.0 * abs(proj.w);\n gl_Position = proj;\n#ifdef VERTEX_COLOR\n v_Color = a_Color;\n#endif\n v_Texcoord = texcoord;\n}\n@end\n\n\n@export ecgl.labels.fragment\n\nuniform vec3 color : [1.0, 1.0, 1.0];\nuniform float alpha : 1.0;\nuniform sampler2D textureAtlas;\nuniform vec2 uvScale: [1.0, 1.0];\n\n#ifdef VERTEX_COLOR\nvarying vec4 v_Color;\n#endif\nvarying float v_Miter;\n\nvarying vec2 v_Texcoord;\n\nvoid main()\n{\n gl_FragColor = vec4(color, alpha) * texture2D(textureAtlas, v_Texcoord * uvScale);\n#ifdef VERTEX_COLOR\n gl_FragColor *= v_Color;\n#endif\n}\n\n@end");const _i=Pt.Mesh.extend((function(){return{geometry:new gi({dynamic:!0}),material:new Pt.Material({shader:Pt.createShader("ecgl.labels"),transparent:!0,depthMask:!1}),culling:!1,castShadow:!1,ignorePicking:!0}}));var vi=St,xi={x:0,y:2,z:1};function yi(e,t){var i=new Pt.Mesh({geometry:new ii({useNativeLine:!1}),material:t,castShadow:!1,ignorePicking:!0,renderOrder:2}),n=new _i;n.material.depthMask=!1;var r=new Pt.Node;r.add(i),r.add(n),this.rootNode=r,this.dim=e,this.linesMesh=i,this.labelsMesh=n,this.axisLineCoords=null,this.labelElements=[]}var bi={x:"y",y:"x",z:"y"};yi.prototype.update=function(e,t,i){var n=e.coordinateSystem.getAxis(this.dim),r=this.linesMesh.geometry,s=this.labelsMesh.geometry;r.convertToDynamicArray(!0),s.convertToDynamicArray(!0);var l=n.model,h=n.getExtent(),d=i.getDevicePixelRatio(),c=l.getModel("axisLine",e.getModel("axisLine")),u=l.getModel("axisTick",e.getModel("axisTick")),f=l.getModel("axisLabel",e.getModel("axisLabel")),p=c.get("lineStyle.color");if(c.get("show")){var m=c.getModel("lineStyle"),g=[0,0,0];(D=[0,0,0])[L=xi[n.dim]]=h[0],g[L]=h[1],this.axisLineCoords=[D,g];var _=Pt.parseColor(p),v=vi(m.get("width"),1),x=vi(m.get("opacity"),1);_[3]*=x,r.addLine(D,g,_,v*d)}if(u.get("show")){var y=u.getModel("lineStyle"),b=Pt.parseColor(vi(y.get("color"),p));v=vi(y.get("width"),1);b[3]*=vi(y.get("opacity"),1);for(var w=n.getTicksCoords(),T=u.get("length"),S=0;S<w.length;S++){var M=w[S].coord,D=[0,0,0],L=(g=[0,0,0],xi[n.dim]),C=xi[bi[n.dim]];D[L]=g[L]=M,g[C]=T,r.addLine(D,g,b,v*d)}}this.labelElements=[];d=i.getDevicePixelRatio();if(f.get("show")){w=n.getTicksCoords();var A=l.get("data"),P=f.get("margin"),E=n.getViewLabels();for(S=0;S<E.length;S++){var N=E[S].tickValue,O=E[S].formattedLabel,I=E[S].rawLabel,R=(M=n.dataToCoord(N),[0,0,0]);L=xi[n.dim],C=xi[bi[n.dim]];R[L]=R[L]=M,R[C]=P;var z=f;A&&A[N]&&A[N].textStyle&&(z=new o(A[N].textStyle,f,l.ecModel));var F=vi(z.get("color"),p),B=new Je({style:a(z,{text:O,fill:"function"==typeof F?F("category"===n.type?I:"value"===n.type?N+"":N,S):F,verticalAlign:"top",align:"left"})}),G=t.add(B),H=B.getBoundingRect();s.addSprite(R,[H.width*d,H.height*d],G),this.labelElements.push(B)}}if(l.get("name")){var U=l.getModel("nameTextStyle"),V=(R=[0,0,0],L=xi[n.dim],C=xi[bi[n.dim]],vi(U.get("color"),p)),k=U.get("borderColor");v=U.get("borderWidth");R[L]=R[L]=(h[0]+h[1])/2,R[C]=l.get("nameGap");B=new Je({style:a(U,{text:l.get("name"),fill:V,stroke:k,lineWidth:v})}),G=t.add(B),H=B.getBoundingRect();s.addSprite(R,[H.width*d,H.height*d],G),B.__idx=this.labelElements.length,this.nameLabelElement=B}this.labelsMesh.material.set("textureAtlas",t.getTexture()),this.labelsMesh.material.set("uvScale",t.getCoordsScale()),r.convertToTypedArray(),s.convertToTypedArray()},yi.prototype.setSpriteAlign=function(e,t,i){for(var n=i.getDevicePixelRatio(),r=this.labelsMesh.geometry,o=0;o<this.labelElements.length;o++){var a=this.labelElements[o].getBoundingRect();r.setSpriteAlign(o,[a.width*n,a.height*n],e,t)}var s=this.nameLabelElement;if(s){a=s.getBoundingRect();r.setSpriteAlign(s.__idx,[a.width*n,a.height*n],e,t),r.dirty()}this.textAlign=e,this.textVerticalAlign=t};const wi="@export ecgl.lines3D.vertex\n\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\n\nattribute vec3 position: POSITION;\nattribute vec4 a_Color : COLOR;\nvarying vec4 v_Color;\n\nvoid main()\n{\n gl_Position = worldViewProjection * vec4(position, 1.0);\n v_Color = a_Color;\n}\n\n@end\n\n@export ecgl.lines3D.fragment\n\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\n\nvarying vec4 v_Color;\n\n@import clay.util.srgb\n\nvoid main()\n{\n#ifdef SRGB_DECODE\n gl_FragColor = sRGBToLinear(color * v_Color);\n#else\n gl_FragColor = color * v_Color;\n#endif\n}\n@end\n\n\n\n@export ecgl.lines3D.clipNear\n\nvec4 clipNear(vec4 p1, vec4 p2) {\n float n = (p1.w - near) / (p1.w - p2.w);\n return vec4(mix(p1.xy, p2.xy, n), -near, near);\n}\n\n@end\n\n@export ecgl.lines3D.expandLine\n#ifdef VERTEX_ANIMATION\n vec4 prevProj = worldViewProjection * vec4(mix(prevPositionPrev, positionPrev, percent), 1.0);\n vec4 currProj = worldViewProjection * vec4(mix(prevPosition, position, percent), 1.0);\n vec4 nextProj = worldViewProjection * vec4(mix(prevPositionNext, positionNext, percent), 1.0);\n#else\n vec4 prevProj = worldViewProjection * vec4(positionPrev, 1.0);\n vec4 currProj = worldViewProjection * vec4(position, 1.0);\n vec4 nextProj = worldViewProjection * vec4(positionNext, 1.0);\n#endif\n\n if (currProj.w < 0.0) {\n if (nextProj.w > 0.0) {\n currProj = clipNear(currProj, nextProj);\n }\n else if (prevProj.w > 0.0) {\n currProj = clipNear(currProj, prevProj);\n }\n }\n\n vec2 prevScreen = (prevProj.xy / abs(prevProj.w) + 1.0) * 0.5 * viewport.zw;\n vec2 currScreen = (currProj.xy / abs(currProj.w) + 1.0) * 0.5 * viewport.zw;\n vec2 nextScreen = (nextProj.xy / abs(nextProj.w) + 1.0) * 0.5 * viewport.zw;\n\n vec2 dir;\n float len = offset;\n if (position == positionPrev) {\n dir = normalize(nextScreen - currScreen);\n }\n else if (position == positionNext) {\n dir = normalize(currScreen - prevScreen);\n }\n else {\n vec2 dirA = normalize(currScreen - prevScreen);\n vec2 dirB = normalize(nextScreen - currScreen);\n\n vec2 tanget = normalize(dirA + dirB);\n\n float miter = 1.0 / max(dot(tanget, dirA), 0.5);\n len *= miter;\n dir = tanget;\n }\n\n dir = vec2(-dir.y, dir.x) * len;\n currScreen += dir;\n\n currProj.xy = (currScreen / viewport.zw - 0.5) * 2.0 * abs(currProj.w);\n@end\n\n\n@export ecgl.meshLines3D.vertex\n\nattribute vec3 position: POSITION;\nattribute vec3 positionPrev;\nattribute vec3 positionNext;\nattribute float offset;\nattribute vec4 a_Color : COLOR;\n\n#ifdef VERTEX_ANIMATION\nattribute vec3 prevPosition;\nattribute vec3 prevPositionPrev;\nattribute vec3 prevPositionNext;\nuniform float percent : 1.0;\n#endif\n\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform vec4 viewport : VIEWPORT;\nuniform float near : NEAR;\n\nvarying vec4 v_Color;\n\n@import ecgl.common.wireframe.vertexHeader\n\n@import ecgl.lines3D.clipNear\n\nvoid main()\n{\n @import ecgl.lines3D.expandLine\n\n gl_Position = currProj;\n\n v_Color = a_Color;\n\n @import ecgl.common.wireframe.vertexMain\n}\n@end\n\n\n@export ecgl.meshLines3D.fragment\n\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\n\nvarying vec4 v_Color;\n\n@import ecgl.common.wireframe.fragmentHeader\n\n@import clay.util.srgb\n\nvoid main()\n{\n#ifdef SRGB_DECODE\n gl_FragColor = sRGBToLinear(color * v_Color);\n#else\n gl_FragColor = color * v_Color;\n#endif\n\n @import ecgl.common.wireframe.fragmentMain\n}\n\n@end";var Ti=St;Pt.Shader.import(wi);var Si={x:0,y:2,z:1};const Mi=s.extend({type:"grid3D",__ecgl__:!0,init:function(e,t){var i=new Pt.Material({shader:Pt.createShader("ecgl.color"),depthMask:!1,transparent:!0}),n=new Pt.Material({shader:Pt.createShader("ecgl.meshLines3D"),depthMask:!1,transparent:!0});i.define("fragment","DOUBLE_SIDED"),i.define("both","VERTEX_COLOR"),this.groupGL=new Pt.Node,this._control=new Jt({zr:t.getZr()}),this._control.init(),this._faces=[["y","z","x",-1,"left"],["y","z","x",1,"right"],["x","y","z",-1,"bottom"],["x","y","z",1,"top"],["x","z","y",-1,"far"],["x","z","y",1,"near"]].map((function(e){var t=new pi(e,n,i);return this.groupGL.add(t.rootNode),t}),this),this._axes=["x","y","z"].map((function(e){var t=new yi(e,n);return this.groupGL.add(t.rootNode),t}),this);var r=t.getDevicePixelRatio();this._axisLabelSurface=new ri({width:256,height:256,devicePixelRatio:r}),this._axisLabelSurface.onupdate=function(){t.getZr().refresh()},this._axisPointerLineMesh=new Pt.Mesh({geometry:new ii({useNativeLine:!1}),material:n,castShadow:!1,ignorePicking:!0,renderOrder:3}),this.groupGL.add(this._axisPointerLineMesh),this._axisPointerLabelsSurface=new ri({width:128,height:128,devicePixelRatio:r}),this._axisPointerLabelsMesh=new _i({ignorePicking:!0,renderOrder:4,castShadow:!1}),this._axisPointerLabelsMesh.material.set("textureAtlas",this._axisPointerLabelsSurface.getTexture()),this.groupGL.add(this._axisPointerLabelsMesh),this._lightRoot=new Pt.Node,this._sceneHelper=new oi,this._sceneHelper.initLight(this._lightRoot)},render:function(e,t,i){this._model=e,this._api=i;var n=e.coordinateSystem;n.viewGL.add(this._lightRoot),e.get("show")?n.viewGL.add(this.groupGL):n.viewGL.remove(this.groupGL);var r=this._control;r.setViewGL(n.viewGL);var o=e.getModel("viewControl");r.setFromViewControlModel(o,0),this._axisLabelSurface.clear(),r.off("update"),e.get("show")&&(this._faces.forEach((function(n){n.update(e,t,i)}),this),this._axes.forEach((function(t){t.update(e,this._axisLabelSurface,i)}),this)),r.on("update",this._onCameraChange.bind(this,e,i),this),this._sceneHelper.setScene(n.viewGL.scene),this._sceneHelper.updateLight(e),n.viewGL.setPostEffect(e.getModel("postEffect"),i),n.viewGL.setTemporalSuperSampling(e.getModel("temporalSuperSampling")),this._initMouseHandler(e)},afterRender:function(e,t,i,n){var r=n.renderer;this._sceneHelper.updateAmbientCubemap(r,e,i),this._sceneHelper.updateSkybox(r,e,i)},showAxisPointer:function(e,t,i,n){this._doShowAxisPointer(),this._updateAxisPointer(n.value)},hideAxisPointer:function(e,t,i,n){this._doHideAxisPointer()},_initMouseHandler:function(e){var t=e.coordinateSystem.viewGL;e.get("show")&&e.get("axisPointer.show")?t.on("mousemove",this._updateAxisPointerOnMousePosition,this):t.off("mousemove",this._updateAxisPointerOnMousePosition)},_updateAxisPointerOnMousePosition:function(e){if(!e.target){for(var t,i=this._model.coordinateSystem,n=i.viewGL,r=n.castRay(e.offsetX,e.offsetY,new Pt.Ray),o=0;o<this._faces.length;o++){var a=this._faces[o];if(!a.rootNode.invisible){a.plane.normal.dot(n.camera.worldTransform.z)<0&&a.plane.normal.negate();var s=r.intersectPlane(a.plane);if(s){var l=i.getAxis(a.faceInfo[0]),h=i.getAxis(a.faceInfo[1]),d=Si[a.faceInfo[0]],c=Si[a.faceInfo[1]];l.contain(s.array[d])&&h.contain(s.array[c])&&(t=s)}}}if(t){var u=i.pointToData(t.array,[],!0);this._updateAxisPointer(u),this._doShowAxisPointer()}else this._doHideAxisPointer()}},_onCameraChange:function(e,t){e.get("show")&&(this._updateFaceVisibility(),this._updateAxisLinePosition());var i=this._control;t.dispatchAction({type:"grid3DChangeCamera",alpha:i.getAlpha(),beta:i.getBeta(),distance:i.getDistance(),center:i.getCenter(),from:this.uid,grid3DId:e.id})},_updateFaceVisibility:function(){var e=this._control.getCamera(),t=new Pt.Vector3;e.update();for(var i=0;i<this._faces.length/2;i++){for(var n=[],r=0;r<2;r++){this._faces[2*i+r].rootNode.getWorldPosition(t),t.transformMat4(e.viewMatrix),n[r]=t.z}var o=n[0]>n[1]?0:1,a=this._faces[2*i+o],s=this._faces[2*i+1-o];a.rootNode.invisible=!0,s.rootNode.invisible=!1}},_updateAxisLinePosition:function(){var e=this._model.coordinateSystem,t=e.getAxis("x"),i=e.getAxis("y"),n=e.getAxis("z"),r=n.getExtentMax(),o=n.getExtentMin(),a=t.getExtentMin(),s=t.getExtentMax(),l=i.getExtentMax(),h=i.getExtentMin(),d=this._axes[0].rootNode,c=this._axes[1].rootNode,u=this._axes[2].rootNode,f=this._faces,p=f[4].rootNode.invisible?h:l,m=f[2].rootNode.invisible?r:o,g=f[0].rootNode.invisible?a:s,_=f[2].rootNode.invisible?r:o,v=f[0].rootNode.invisible?s:a,x=f[4].rootNode.invisible?h:l;d.rotation.identity(),c.rotation.identity(),u.rotation.identity(),f[4].rootNode.invisible&&(this._axes[0].flipped=!0,d.rotation.rotateX(Math.PI)),f[0].rootNode.invisible&&(this._axes[1].flipped=!0,c.rotation.rotateZ(Math.PI)),f[4].rootNode.invisible&&(this._axes[2].flipped=!0,u.rotation.rotateY(Math.PI)),d.position.set(0,m,p),c.position.set(g,_,0),u.position.set(v,0,x),d.update(),c.update(),u.update(),this._updateAxisLabelAlign()},_updateAxisLabelAlign:function(){var e=this._control.getCamera(),t=[new Pt.Vector4,new Pt.Vector4],i=new Pt.Vector4;this.groupGL.getWorldPosition(i),i.w=1,i.transformMat4(e.viewMatrix).transformMat4(e.projectionMatrix),i.x/=i.w,i.y/=i.w,this._axes.forEach((function(n){var r=n.axisLineCoords;n.labelsMesh.geometry;for(var o=0;o<t.length;o++)t[o].setArray(r[o]),t[o].w=1,t[o].transformMat4(n.rootNode.worldTransform).transformMat4(e.viewMatrix).transformMat4(e.projectionMatrix),t[o].x/=t[o].w,t[o].y/=t[o].w;var a,s,l=t[1].x-t[0].x,h=t[1].y-t[0].y,d=(t[1].x+t[0].x)/2,c=(t[1].y+t[0].y)/2;Math.abs(h/l)<.5?(a="center",s=c>i.y?"bottom":"top"):(s="middle",a=d>i.x?"left":"right"),n.setSpriteAlign(a,s,this._api)}),this)},_doShowAxisPointer:function(){this._axisPointerLineMesh.invisible&&(this._axisPointerLineMesh.invisible=!1,this._axisPointerLabelsMesh.invisible=!1,this._api.getZr().refresh())},_doHideAxisPointer:function(){this._axisPointerLineMesh.invisible||(this._axisPointerLineMesh.invisible=!0,this._axisPointerLabelsMesh.invisible=!0,this._api.getZr().refresh())},_updateAxisPointer:function(e){var t=this._model.coordinateSystem,i=t.dataToPoint(e),n=this._axisPointerLineMesh.geometry,r=this._model.getModel("axisPointer"),o=this._api.getDevicePixelRatio();function a(e){return St(e.model.get("axisPointer.show"),r.get("show"))}function s(e){var t=e.model.getModel("axisPointer",r).getModel("lineStyle"),i=Pt.parseColor(t.get("color")),n=Ti(t.get("width"),1),o=Ti(t.get("opacity"),1);return i[3]*=o,{color:i,lineWidth:n}}n.convertToDynamicArray(!0);for(var l=0;l<this._faces.length;l++){var h=this._faces[l];if(!h.rootNode.invisible){for(var d=h.faceInfo,c=d[3]<0?t.getAxis(d[2]).getExtentMin():t.getAxis(d[2]).getExtentMax(),u=Si[d[2]],f=0;f<2;f++){var p=d[f],m=d[1-f],g=t.getAxis(p),_=t.getAxis(m);if(a(g)){var v=Si[p],x=Si[m];(w=[0,0,0])[v]=(b=[0,0,0])[v]=i[v],w[u]=b[u]=c,w[x]=_.getExtentMin(),b[x]=_.getExtentMax();var y=s(g);n.addLine(w,b,y.color,y.lineWidth*o)}}if(a(t.getAxis(d[2]))){var b,w=i.slice();(b=i.slice())[u]=c;y=s(t.getAxis(d[2]));n.addLine(w,b,y.color,y.lineWidth*o)}}}n.convertToTypedArray(),this._updateAxisPointerLabelsMesh(e),this._api.getZr().refresh()},_updateAxisPointerLabelsMesh:function(e){var t=this._model,i=this._axisPointerLabelsMesh,n=this._axisPointerLabelsSurface,r=t.coordinateSystem,o=t.getModel("axisPointer");i.geometry.convertToDynamicArray(!0),n.clear();var s={x:"y",y:"x",z:"y"};this._axes.forEach((function(t,h){var d=r.getAxis(t.dim),c=d.model.getModel("axisPointer",o),u=c.getModel("label"),f=c.get("lineStyle.color");if(u.get("show")&&c.get("show")){var p=e[h],m=u.get("formatter"),g=d.scale.getLabel({value:p});if(null!=m)g=m(g,e);else if("interval"===d.scale.type||"log"===d.scale.type){var _=l(d.scale.getTicks()[0]);g=p.toFixed(_+2)}var v=u.get("color"),x=new Je({style:a(u,{text:g,fill:v||f,align:"left",verticalAlign:"top"})}),y=n.add(x),b=x.getBoundingRect(),w=this._api.getDevicePixelRatio(),T=t.rootNode.position.toArray();T[Si[s[t.dim]]]+=(t.flipped?-1:1)*u.get("margin"),T[Si[t.dim]]=d.dataToCoord(e[h]),i.geometry.addSprite(T,[b.width*w,b.height*w],y,t.textAlign,t.textVerticalAlign)}}),this),n.getZr().refreshImmediately(),i.material.set("uvScale",n.getCoordsScale()),i.geometry.convertToTypedArray()},dispose:function(){this.groupGL.removeAll(),this._control.dispose(),this._axisLabelSurface.dispose(),this._axisPointerLabelsSurface.dispose()}});function Di(e){h.call(this,e),this.type="cartesian3D",this.dimensions=["x","y","z"],this.size=[0,0,0]}function Li(e,t,i){d.call(this,e,t,i)}function Ci(e,t){for(var i=0,n=1/t,r=e;r>0;)i+=n*(r%t),r=Math.floor(r/t),n/=t;return i}Di.prototype={constructor:Di,model:null,containPoint:function(e){return this.getAxis("x").contain(e[0])&&this.getAxis("y").contain(e[2])&&this.getAxis("z").contain(e[1])},containData:function(e){return this.getAxis("x").containData(e[0])&&this.getAxis("y").containData(e[1])&&this.getAxis("z").containData(e[2])},dataToPoint:function(e,t,i){return(t=t||[])[0]=this.getAxis("x").dataToCoord(e[0],i),t[2]=this.getAxis("y").dataToCoord(e[1],i),t[1]=this.getAxis("z").dataToCoord(e[2],i),t},pointToData:function(e,t,i){return(t=t||[])[0]=this.getAxis("x").coordToData(e[0],i),t[1]=this.getAxis("y").coordToData(e[2],i),t[2]=this.getAxis("z").coordToData(e[1],i),t}},$e(Di,h),Li.prototype={constructor:Li,getExtentMin:function(){var e=this._extent;return Math.min(e[0],e[1])},getExtentMax:function(){var e=this._extent;return Math.max(e[0],e[1])},calculateCategoryInterval:function(){return Math.floor(this.scale.count()/8)}},$e(Li,d);function Ai(e){for(var t=new Uint8Array(e*e*4),i=0,n=new B,r=0;r<e;r++)for(var o=0;o<e;o++)n.set(2*Math.random()-1,2*Math.random()-1,0).normalize(),t[i++]=255*(.5*n.x+.5),t[i++]=255*(.5*n.y+.5),t[i++]=0,t[i++]=255;return t}function Pi(e){return new F({pixels:Ai(e),wrapS:Y.REPEAT,wrapT:Y.REPEAT,width:e,height:e})}function Ei(e,t,i){var n=new Float32Array(3*e);t=t||0;for(var r=0;r<e;r++){var o=Ci(r+t,2)*(i?1:2)*Math.PI,a=Ci(r+t,3)*Math.PI,s=Math.random(),l=Math.cos(o)*Math.sin(a)*s,h=Math.cos(a)*s,d=Math.sin(o)*Math.sin(a)*s;n[3*r]=l,n[3*r+1]=h,n[3*r+2]=d}return n}function Ni(e){e=e||{},this._ssaoPass=new Se({fragment:U.source("ecgl.ssao.estimate")}),this._blurPass=new Se({fragment:U.source("ecgl.ssao.blur")}),this._framebuffer=new Me({depthBuffer:!1}),this._ssaoTexture=new F,this._blurTexture=new F,this._blurTexture2=new F,this._depthTex=e.depthTexture,this._normalTex=e.normalTexture,this.setNoiseSize(4),this.setKernelSize(e.kernelSize||12),null!=e.radius&&this.setParameter("radius",e.radius),null!=e.power&&this.setParameter("power",e.power),this._normalTex||(this._ssaoPass.material.disableTexture("normalTex"),this._blurPass.material.disableTexture("normalTex")),this._depthTex||this._blurPass.material.disableTexture("depthTex"),this._blurPass.material.setUniform("normalTex",this._normalTex),this._blurPass.material.setUniform("depthTex",this._depthTex)}U.import("@export ecgl.ssao.estimate\n\nuniform sampler2D depthTex;\n\nuniform sampler2D normalTex;\n\nuniform sampler2D noiseTex;\n\nuniform vec2 depthTexSize;\n\nuniform vec2 noiseTexSize;\n\nuniform mat4 projection;\n\nuniform mat4 projectionInv;\n\nuniform mat4 viewInverseTranspose;\n\nuniform vec3 kernel[KERNEL_SIZE];\n\nuniform float radius : 1;\n\nuniform float power : 1;\n\nuniform float bias: 1e-2;\n\nuniform float intensity: 1.0;\n\nvarying vec2 v_Texcoord;\n\nfloat ssaoEstimator(in vec3 originPos, in mat3 kernelBasis) {\n float occlusion = 0.0;\n\n for (int i = 0; i < KERNEL_SIZE; i++) {\n vec3 samplePos = kernel[i];\n#ifdef NORMALTEX_ENABLED\n samplePos = kernelBasis * samplePos;\n#endif\n samplePos = samplePos * radius + originPos;\n\n vec4 texCoord = projection * vec4(samplePos, 1.0);\n texCoord.xy /= texCoord.w;\n\n vec4 depthTexel = texture2D(depthTex, texCoord.xy * 0.5 + 0.5);\n\n float sampleDepth = depthTexel.r * 2.0 - 1.0;\n if (projection[3][3] == 0.0) {\n sampleDepth = projection[3][2] / (sampleDepth * projection[2][3] - projection[2][2]);\n }\n else {\n sampleDepth = (sampleDepth - projection[3][2]) / projection[2][2];\n }\n \n float rangeCheck = smoothstep(0.0, 1.0, radius / abs(originPos.z - sampleDepth));\n occlusion += rangeCheck * step(samplePos.z, sampleDepth - bias);\n }\n#ifdef NORMALTEX_ENABLED\n occlusion = 1.0 - occlusion / float(KERNEL_SIZE);\n#else\n occlusion = 1.0 - clamp((occlusion / float(KERNEL_SIZE) - 0.6) * 2.5, 0.0, 1.0);\n#endif\n return pow(occlusion, power);\n}\n\nvoid main()\n{\n\n vec4 depthTexel = texture2D(depthTex, v_Texcoord);\n\n#ifdef NORMALTEX_ENABLED\n vec4 tex = texture2D(normalTex, v_Texcoord);\n if (dot(tex.rgb, tex.rgb) == 0.0) {\n gl_FragColor = vec4(1.0);\n return;\n }\n vec3 N = tex.rgb * 2.0 - 1.0;\n N = (viewInverseTranspose * vec4(N, 0.0)).xyz;\n\n vec2 noiseTexCoord = depthTexSize / vec2(noiseTexSize) * v_Texcoord;\n vec3 rvec = texture2D(noiseTex, noiseTexCoord).rgb * 2.0 - 1.0;\n vec3 T = normalize(rvec - N * dot(rvec, N));\n vec3 BT = normalize(cross(N, T));\n mat3 kernelBasis = mat3(T, BT, N);\n#else\n if (depthTexel.r > 0.99999) {\n gl_FragColor = vec4(1.0);\n return;\n }\n mat3 kernelBasis;\n#endif\n\n float z = depthTexel.r * 2.0 - 1.0;\n\n vec4 projectedPos = vec4(v_Texcoord * 2.0 - 1.0, z, 1.0);\n vec4 p4 = projectionInv * projectedPos;\n\n vec3 position = p4.xyz / p4.w;\n\n float ao = ssaoEstimator(position, kernelBasis);\n ao = clamp(1.0 - (1.0 - ao) * intensity, 0.0, 1.0);\n gl_FragColor = vec4(vec3(ao), 1.0);\n}\n\n@end\n\n\n@export ecgl.ssao.blur\n#define SHADER_NAME SSAO_BLUR\n\nuniform sampler2D ssaoTexture;\n\n#ifdef NORMALTEX_ENABLED\nuniform sampler2D normalTex;\n#endif\n\nvarying vec2 v_Texcoord;\n\nuniform vec2 textureSize;\nuniform float blurSize : 1.0;\n\nuniform int direction: 0.0;\n\n#ifdef DEPTHTEX_ENABLED\nuniform sampler2D depthTex;\nuniform mat4 projection;\nuniform float depthRange : 0.5;\n\nfloat getLinearDepth(vec2 coord)\n{\n float depth = texture2D(depthTex, coord).r * 2.0 - 1.0;\n return projection[3][2] / (depth * projection[2][3] - projection[2][2]);\n}\n#endif\n\nvoid main()\n{\n float kernel[5];\n kernel[0] = 0.122581;\n kernel[1] = 0.233062;\n kernel[2] = 0.288713;\n kernel[3] = 0.233062;\n kernel[4] = 0.122581;\n\n vec2 off = vec2(0.0);\n if (direction == 0) {\n off[0] = blurSize / textureSize.x;\n }\n else {\n off[1] = blurSize / textureSize.y;\n }\n\n vec2 coord = v_Texcoord;\n\n float sum = 0.0;\n float weightAll = 0.0;\n\n#ifdef NORMALTEX_ENABLED\n vec3 centerNormal = texture2D(normalTex, v_Texcoord).rgb * 2.0 - 1.0;\n#endif\n#if defined(DEPTHTEX_ENABLED)\n float centerDepth = getLinearDepth(v_Texcoord);\n#endif\n\n for (int i = 0; i < 5; i++) {\n vec2 coord = clamp(v_Texcoord + vec2(float(i) - 2.0) * off, vec2(0.0), vec2(1.0));\n\n float w = kernel[i];\n#ifdef NORMALTEX_ENABLED\n vec3 normal = texture2D(normalTex, coord).rgb * 2.0 - 1.0;\n w *= clamp(dot(normal, centerNormal), 0.0, 1.0);\n#endif\n#ifdef DEPTHTEX_ENABLED\n float d = getLinearDepth(coord);\n w *= (1.0 - smoothstep(abs(centerDepth - d) / depthRange, 0.0, 1.0));\n#endif\n\n weightAll += w;\n sum += texture2D(ssaoTexture, coord).r * w;\n }\n\n gl_FragColor = vec4(vec3(sum / weightAll), 1.0);\n}\n\n@end\n"),Ni.prototype.setDepthTexture=function(e){this._depthTex=e},Ni.prototype.setNormalTexture=function(e){this._normalTex=e,this._ssaoPass.material[e?"enableTexture":"disableTexture"]("normalTex"),this.setKernelSize(this._kernelSize)},Ni.prototype.update=function(e,t,i){var n=e.getWidth(),r=e.getHeight(),o=this._ssaoPass,a=this._blurPass;o.setUniform("kernel",this._kernels[i%this._kernels.length]),o.setUniform("depthTex",this._depthTex),null!=this._normalTex&&o.setUniform("normalTex",this._normalTex),o.setUniform("depthTexSize",[this._depthTex.width,this._depthTex.height]);var s=new de;de.transpose(s,t.worldTransform),o.setUniform("projection",t.projectionMatrix.array),o.setUniform("projectionInv",t.invProjectionMatrix.array),o.setUniform("viewInverseTranspose",s.array);var l=this._ssaoTexture,h=this._blurTexture,d=this._blurTexture2;l.width=n/2,l.height=r/2,h.width=n,h.height=r,d.width=n,d.height=r,this._framebuffer.attach(l),this._framebuffer.bind(e),e.gl.clearColor(1,1,1,1),e.gl.clear(e.gl.COLOR_BUFFER_BIT),o.render(e),a.setUniform("textureSize",[n/2,r/2]),a.setUniform("projection",t.projectionMatrix.array),this._framebuffer.attach(h),a.setUniform("direction",0),a.setUniform("ssaoTexture",l),a.render(e),this._framebuffer.attach(d),a.setUniform("textureSize",[n,r]),a.setUniform("direction",1),a.setUniform("ssaoTexture",h),a.render(e),this._framebuffer.unbind(e);var c=e.clearColor;e.gl.clearColor(c[0],c[1],c[2],c[3])},Ni.prototype.getTargetTexture=function(){return this._blurTexture2},Ni.prototype.setParameter=function(e,t){"noiseTexSize"===e?this.setNoiseSize(t):"kernelSize"===e?this.setKernelSize(t):"intensity"===e?this._ssaoPass.material.set("intensity",t):this._ssaoPass.setUniform(e,t)},Ni.prototype.setKernelSize=function(e){this._kernelSize=e,this._ssaoPass.material.define("fragment","KERNEL_SIZE",e),this._kernels=this._kernels||[];for(var t=0;t<30;t++)this._kernels[t]=Ei(e,t*e,!!this._normalTex)},Ni.prototype.setNoiseSize=function(e){var t=this._ssaoPass.getUniform("noiseTex");t?(t.data=Ai(e),t.width=t.height=e,t.dirty()):(t=Pi(e),this._ssaoPass.setUniform("noiseTex",Pi(e))),this._ssaoPass.setUniform("noiseTexSize",[e,e])},Ni.prototype.dispose=function(e){this._blurTexture.dispose(e),this._ssaoTexture.dispose(e),this._blurTexture2.dispose(e)};function Oi(e){e=e||{},this._ssrPass=new Se({fragment:U.source("ecgl.ssr.main"),clearColor:[0,0,0,0]}),this._blurPass1=new Se({fragment:U.source("ecgl.ssr.blur"),clearColor:[0,0,0,0]}),this._blurPass2=new Se({fragment:U.source("ecgl.ssr.blur"),clearColor:[0,0,0,0]}),this._blendPass=new Se({fragment:U.source("clay.compositor.blend")}),this._blendPass.material.disableTexturesAll(),this._blendPass.material.enableTexture(["texture1","texture2"]),this._ssrPass.setUniform("gBufferTexture1",e.normalTexture),this._ssrPass.setUniform("gBufferTexture2",e.depthTexture),this._blurPass1.setUniform("gBufferTexture1",e.normalTexture),this._blurPass1.setUniform("gBufferTexture2",e.depthTexture),this._blurPass2.setUniform("gBufferTexture1",e.normalTexture),this._blurPass2.setUniform("gBufferTexture2",e.depthTexture),this._blurPass2.material.define("fragment","VERTICAL"),this._blurPass2.material.define("fragment","BLEND"),this._ssrTexture=new F({type:Y.HALF_FLOAT}),this._texture2=new F({type:Y.HALF_FLOAT}),this._texture3=new F({type:Y.HALF_FLOAT}),this._prevTexture=new F({type:Y.HALF_FLOAT}),this._currentTexture=new F({type:Y.HALF_FLOAT}),this._frameBuffer=new Me({depthBuffer:!1}),this._normalDistribution=null,this._totalSamples=256,this._samplePerFrame=4,this._ssrPass.material.define("fragment","SAMPLE_PER_FRAME",this._samplePerFrame),this._ssrPass.material.define("fragment","TOTAL_SAMPLES",this._totalSamples),this._downScale=1}U.import("@export ecgl.ssr.main\n\n#define SHADER_NAME SSR\n#define MAX_ITERATION 20;\n#define SAMPLE_PER_FRAME 5;\n#define TOTAL_SAMPLES 128;\n\nuniform sampler2D sourceTexture;\nuniform sampler2D gBufferTexture1;\nuniform sampler2D gBufferTexture2;\nuniform sampler2D gBufferTexture3;\nuniform samplerCube specularCubemap;\nuniform float specularIntensity: 1;\n\nuniform mat4 projection;\nuniform mat4 projectionInv;\nuniform mat4 toViewSpace;\nuniform mat4 toWorldSpace;\n\nuniform float maxRayDistance: 200;\n\nuniform float pixelStride: 16;\nuniform float pixelStrideZCutoff: 50; \nuniform float screenEdgeFadeStart: 0.9; \nuniform float eyeFadeStart : 0.2; uniform float eyeFadeEnd: 0.8; \nuniform float minGlossiness: 0.2; uniform float zThicknessThreshold: 1;\n\nuniform float nearZ;\nuniform vec2 viewportSize : VIEWPORT_SIZE;\n\nuniform float jitterOffset: 0;\n\nvarying vec2 v_Texcoord;\n\n#ifdef DEPTH_DECODE\n@import clay.util.decode_float\n#endif\n\n#ifdef PHYSICALLY_CORRECT\nuniform sampler2D normalDistribution;\nuniform float sampleOffset: 0;\nuniform vec2 normalDistributionSize;\n\nvec3 transformNormal(vec3 H, vec3 N) {\n vec3 upVector = N.y > 0.999 ? vec3(1.0, 0.0, 0.0) : vec3(0.0, 1.0, 0.0);\n vec3 tangentX = normalize(cross(N, upVector));\n vec3 tangentZ = cross(N, tangentX);\n return normalize(tangentX * H.x + N * H.y + tangentZ * H.z);\n}\nvec3 importanceSampleNormalGGX(float i, float roughness, vec3 N) {\n float p = fract((i + sampleOffset) / float(TOTAL_SAMPLES));\n vec3 H = texture2D(normalDistribution,vec2(roughness, p)).rgb;\n return transformNormal(H, N);\n}\nfloat G_Smith(float g, float ndv, float ndl) {\n float roughness = 1.0 - g;\n float k = roughness * roughness / 2.0;\n float G1V = ndv / (ndv * (1.0 - k) + k);\n float G1L = ndl / (ndl * (1.0 - k) + k);\n return G1L * G1V;\n}\nvec3 F_Schlick(float ndv, vec3 spec) {\n return spec + (1.0 - spec) * pow(1.0 - ndv, 5.0);\n}\n#endif\n\nfloat fetchDepth(sampler2D depthTexture, vec2 uv)\n{\n vec4 depthTexel = texture2D(depthTexture, uv);\n return depthTexel.r * 2.0 - 1.0;\n}\n\nfloat linearDepth(float depth)\n{\n if (projection[3][3] == 0.0) {\n return projection[3][2] / (depth * projection[2][3] - projection[2][2]);\n }\n else {\n return (depth - projection[3][2]) / projection[2][2];\n }\n}\n\nbool rayIntersectDepth(float rayZNear, float rayZFar, vec2 hitPixel)\n{\n if (rayZFar > rayZNear)\n {\n float t = rayZFar; rayZFar = rayZNear; rayZNear = t;\n }\n float cameraZ = linearDepth(fetchDepth(gBufferTexture2, hitPixel));\n return rayZFar <= cameraZ && rayZNear >= cameraZ - zThicknessThreshold;\n}\n\n\nbool traceScreenSpaceRay(\n vec3 rayOrigin, vec3 rayDir, float jitter,\n out vec2 hitPixel, out vec3 hitPoint, out float iterationCount\n)\n{\n float rayLength = ((rayOrigin.z + rayDir.z * maxRayDistance) > -nearZ)\n ? (-nearZ - rayOrigin.z) / rayDir.z : maxRayDistance;\n\n vec3 rayEnd = rayOrigin + rayDir * rayLength;\n\n vec4 H0 = projection * vec4(rayOrigin, 1.0);\n vec4 H1 = projection * vec4(rayEnd, 1.0);\n\n float k0 = 1.0 / H0.w, k1 = 1.0 / H1.w;\n\n vec3 Q0 = rayOrigin * k0, Q1 = rayEnd * k1;\n\n vec2 P0 = (H0.xy * k0 * 0.5 + 0.5) * viewportSize;\n vec2 P1 = (H1.xy * k1 * 0.5 + 0.5) * viewportSize;\n\n P1 += dot(P1 - P0, P1 - P0) < 0.0001 ? 0.01 : 0.0;\n vec2 delta = P1 - P0;\n\n bool permute = false;\n if (abs(delta.x) < abs(delta.y)) {\n permute = true;\n delta = delta.yx;\n P0 = P0.yx;\n P1 = P1.yx;\n }\n float stepDir = sign(delta.x);\n float invdx = stepDir / delta.x;\n\n vec3 dQ = (Q1 - Q0) * invdx;\n float dk = (k1 - k0) * invdx;\n\n vec2 dP = vec2(stepDir, delta.y * invdx);\n\n float strideScaler = 1.0 - min(1.0, -rayOrigin.z / pixelStrideZCutoff);\n float pixStride = 1.0 + strideScaler * pixelStride;\n\n dP *= pixStride; dQ *= pixStride; dk *= pixStride;\n\n vec4 pqk = vec4(P0, Q0.z, k0);\n vec4 dPQK = vec4(dP, dQ.z, dk);\n\n pqk += dPQK * jitter;\n float rayZFar = (dPQK.z * 0.5 + pqk.z) / (dPQK.w * 0.5 + pqk.w);\n float rayZNear;\n\n bool intersect = false;\n\n vec2 texelSize = 1.0 / viewportSize;\n\n iterationCount = 0.0;\n\n for (int i = 0; i < MAX_ITERATION; i++)\n {\n pqk += dPQK;\n\n rayZNear = rayZFar;\n rayZFar = (dPQK.z * 0.5 + pqk.z) / (dPQK.w * 0.5 + pqk.w);\n\n hitPixel = permute ? pqk.yx : pqk.xy;\n hitPixel *= texelSize;\n\n intersect = rayIntersectDepth(rayZNear, rayZFar, hitPixel);\n\n iterationCount += 1.0;\n\n dPQK *= 1.2;\n\n if (intersect) {\n break;\n }\n }\n\n Q0.xy += dQ.xy * iterationCount;\n Q0.z = pqk.z;\n hitPoint = Q0 / pqk.w;\n\n return intersect;\n}\n\nfloat calculateAlpha(\n float iterationCount, float reflectivity,\n vec2 hitPixel, vec3 hitPoint, float dist, vec3 rayDir\n)\n{\n float alpha = clamp(reflectivity, 0.0, 1.0);\n alpha *= 1.0 - (iterationCount / float(MAX_ITERATION));\n vec2 hitPixelNDC = hitPixel * 2.0 - 1.0;\n float maxDimension = min(1.0, max(abs(hitPixelNDC.x), abs(hitPixelNDC.y)));\n alpha *= 1.0 - max(0.0, maxDimension - screenEdgeFadeStart) / (1.0 - screenEdgeFadeStart);\n\n float _eyeFadeStart = eyeFadeStart;\n float _eyeFadeEnd = eyeFadeEnd;\n if (_eyeFadeStart > _eyeFadeEnd) {\n float tmp = _eyeFadeEnd;\n _eyeFadeEnd = _eyeFadeStart;\n _eyeFadeStart = tmp;\n }\n\n float eyeDir = clamp(rayDir.z, _eyeFadeStart, _eyeFadeEnd);\n alpha *= 1.0 - (eyeDir - _eyeFadeStart) / (_eyeFadeEnd - _eyeFadeStart);\n\n alpha *= 1.0 - clamp(dist / maxRayDistance, 0.0, 1.0);\n\n return alpha;\n}\n\n@import clay.util.rand\n\n@import clay.util.rgbm\n\nvoid main()\n{\n vec4 normalAndGloss = texture2D(gBufferTexture1, v_Texcoord);\n\n if (dot(normalAndGloss.rgb, vec3(1.0)) == 0.0) {\n discard;\n }\n\n float g = normalAndGloss.a;\n#if !defined(PHYSICALLY_CORRECT)\n if (g <= minGlossiness) {\n discard;\n }\n#endif\n\n float reflectivity = (g - minGlossiness) / (1.0 - minGlossiness);\n\n vec3 N = normalize(normalAndGloss.rgb * 2.0 - 1.0);\n N = normalize((toViewSpace * vec4(N, 0.0)).xyz);\n\n vec4 projectedPos = vec4(v_Texcoord * 2.0 - 1.0, fetchDepth(gBufferTexture2, v_Texcoord), 1.0);\n vec4 pos = projectionInv * projectedPos;\n vec3 rayOrigin = pos.xyz / pos.w;\n vec3 V = -normalize(rayOrigin);\n\n float ndv = clamp(dot(N, V), 0.0, 1.0);\n float iterationCount;\n float jitter = rand(fract(v_Texcoord + jitterOffset));\n\n#ifdef PHYSICALLY_CORRECT\n vec4 color = vec4(vec3(0.0), 1.0);\n vec4 albedoMetalness = texture2D(gBufferTexture3, v_Texcoord);\n vec3 albedo = albedoMetalness.rgb;\n float m = albedoMetalness.a;\n vec3 diffuseColor = albedo * (1.0 - m);\n vec3 spec = mix(vec3(0.04), albedo, m);\n\n float jitter2 = rand(fract(v_Texcoord)) * float(TOTAL_SAMPLES);\n\n for (int i = 0; i < SAMPLE_PER_FRAME; i++) {\n vec3 H = importanceSampleNormalGGX(float(i) + jitter2, 1.0 - g, N);\n vec3 rayDir = normalize(reflect(-V, H));\n#else\n vec3 rayDir = normalize(reflect(-V, N));\n#endif\n vec2 hitPixel;\n vec3 hitPoint;\n\n bool intersect = traceScreenSpaceRay(rayOrigin, rayDir, jitter, hitPixel, hitPoint, iterationCount);\n\n float dist = distance(rayOrigin, hitPoint);\n\n vec3 hitNormal = texture2D(gBufferTexture1, hitPixel).rgb * 2.0 - 1.0;\n hitNormal = normalize((toViewSpace * vec4(hitNormal, 0.0)).xyz);\n#ifdef PHYSICALLY_CORRECT\n float ndl = clamp(dot(N, rayDir), 0.0, 1.0);\n float vdh = clamp(dot(V, H), 0.0, 1.0);\n float ndh = clamp(dot(N, H), 0.0, 1.0);\n vec3 litTexel = vec3(0.0);\n if (dot(hitNormal, rayDir) < 0.0 && intersect) {\n litTexel = texture2D(sourceTexture, hitPixel).rgb;\n litTexel *= pow(clamp(1.0 - dist / 200.0, 0.0, 1.0), 3.0);\n\n }\n else {\n #ifdef SPECULARCUBEMAP_ENABLED\n vec3 rayDirW = normalize(toWorldSpace * vec4(rayDir, 0.0)).rgb;\n litTexel = RGBMDecode(textureCubeLodEXT(specularCubemap, rayDirW, 0.0), 8.12).rgb * specularIntensity;\n#endif\n }\n color.rgb += ndl * litTexel * (\n F_Schlick(ndl, spec) * G_Smith(g, ndv, ndl) * vdh / (ndh * ndv + 0.001)\n );\n }\n color.rgb /= float(SAMPLE_PER_FRAME);\n#else\n #if !defined(SPECULARCUBEMAP_ENABLED)\n if (dot(hitNormal, rayDir) >= 0.0) {\n discard;\n }\n if (!intersect) {\n discard;\n }\n#endif\n float alpha = clamp(calculateAlpha(iterationCount, reflectivity, hitPixel, hitPoint, dist, rayDir), 0.0, 1.0);\n vec4 color = texture2D(sourceTexture, hitPixel);\n color.rgb *= alpha;\n\n#ifdef SPECULARCUBEMAP_ENABLED\n vec3 rayDirW = normalize(toWorldSpace * vec4(rayDir, 0.0)).rgb;\n alpha = alpha * (intersect ? 1.0 : 0.0);\n float bias = (1.0 -g) * 5.0;\n color.rgb += (1.0 - alpha)\n * RGBMDecode(textureCubeLodEXT(specularCubemap, rayDirW, bias), 8.12).rgb\n * specularIntensity;\n#endif\n\n#endif\n\n gl_FragColor = encodeHDR(color);\n}\n@end\n\n@export ecgl.ssr.blur\n\nuniform sampler2D texture;\nuniform sampler2D gBufferTexture1;\nuniform sampler2D gBufferTexture2;\nuniform mat4 projection;\nuniform float depthRange : 0.05;\n\nvarying vec2 v_Texcoord;\n\nuniform vec2 textureSize;\nuniform float blurSize : 1.0;\n\n#ifdef BLEND\n #ifdef SSAOTEX_ENABLED\nuniform sampler2D ssaoTex;\n #endif\nuniform sampler2D sourceTexture;\n#endif\n\nfloat getLinearDepth(vec2 coord)\n{\n float depth = texture2D(gBufferTexture2, coord).r * 2.0 - 1.0;\n return projection[3][2] / (depth * projection[2][3] - projection[2][2]);\n}\n\n@import clay.util.rgbm\n\n\nvoid main()\n{\n @import clay.compositor.kernel.gaussian_9\n\n vec4 centerNTexel = texture2D(gBufferTexture1, v_Texcoord);\n float g = centerNTexel.a;\n float maxBlurSize = clamp(1.0 - g, 0.0, 1.0) * blurSize;\n#ifdef VERTICAL\n vec2 off = vec2(0.0, maxBlurSize / textureSize.y);\n#else\n vec2 off = vec2(maxBlurSize / textureSize.x, 0.0);\n#endif\n\n vec2 coord = v_Texcoord;\n\n vec4 sum = vec4(0.0);\n float weightAll = 0.0;\n\n vec3 cN = centerNTexel.rgb * 2.0 - 1.0;\n float cD = getLinearDepth(v_Texcoord);\n for (int i = 0; i < 9; i++) {\n vec2 coord = clamp((float(i) - 4.0) * off + v_Texcoord, vec2(0.0), vec2(1.0));\n float w = gaussianKernel[i]\n * clamp(dot(cN, texture2D(gBufferTexture1, coord).rgb * 2.0 - 1.0), 0.0, 1.0);\n float d = getLinearDepth(coord);\n w *= (1.0 - smoothstep(abs(cD - d) / depthRange, 0.0, 1.0));\n\n weightAll += w;\n sum += decodeHDR(texture2D(texture, coord)) * w;\n }\n\n#ifdef BLEND\n float aoFactor = 1.0;\n #ifdef SSAOTEX_ENABLED\n aoFactor = texture2D(ssaoTex, v_Texcoord).r;\n #endif\n gl_FragColor = encodeHDR(\n sum / weightAll * aoFactor + decodeHDR(texture2D(sourceTexture, v_Texcoord))\n );\n#else\n gl_FragColor = encodeHDR(sum / weightAll);\n#endif\n}\n\n@end"),Oi.prototype.setAmbientCubemap=function(e,t){this._ssrPass.material.set("specularCubemap",e),this._ssrPass.material.set("specularIntensity",t);var i=e&&t;this._ssrPass.material[i?"enableTexture":"disableTexture"]("specularCubemap")},Oi.prototype.update=function(e,t,i,n){var r=e.getWidth(),o=e.getHeight(),a=this._ssrTexture,s=this._texture2,l=this._texture3;a.width=this._prevTexture.width=this._currentTexture.width=r/this._downScale,a.height=this._prevTexture.height=this._currentTexture.height=o/this._downScale,s.width=l.width=r,s.height=l.height=o;var h=this._frameBuffer,d=this._ssrPass,c=this._blurPass1,u=this._blurPass2,f=this._blendPass,p=new de,m=new de;de.transpose(p,t.worldTransform),de.transpose(m,t.viewMatrix),d.setUniform("sourceTexture",i),d.setUniform("projection",t.projectionMatrix.array),d.setUniform("projectionInv",t.invProjectionMatrix.array),d.setUniform("toViewSpace",p.array),d.setUniform("toWorldSpace",m.array),d.setUniform("nearZ",t.near);var g=n/this._totalSamples*this._samplePerFrame;if(d.setUniform("jitterOffset",g),d.setUniform("sampleOffset",n*this._samplePerFrame),c.setUniform("textureSize",[a.width,a.height]),u.setUniform("textureSize",[r,o]),u.setUniform("sourceTexture",i),c.setUniform("projection",t.projectionMatrix.array),u.setUniform("projection",t.projectionMatrix.array),h.attach(a),h.bind(e),d.render(e),this._physicallyCorrect&&(h.attach(this._currentTexture),f.setUniform("texture1",this._prevTexture),f.setUniform("texture2",a),f.material.set({weight1:n>=1?.95:0,weight2:n>=1?.05:1}),f.render(e)),h.attach(s),c.setUniform("texture",this._physicallyCorrect?this._currentTexture:a),c.render(e),h.attach(l),u.setUniform("texture",s),u.render(e),h.unbind(e),this._physicallyCorrect){var _=this._prevTexture;this._prevTexture=this._currentTexture,this._currentTexture=_}},Oi.prototype.getTargetTexture=function(){return this._texture3},Oi.prototype.setParameter=function(e,t){"maxIteration"===e?this._ssrPass.material.define("fragment","MAX_ITERATION",t):this._ssrPass.setUniform(e,t)},Oi.prototype.setPhysicallyCorrect=function(e){e?(this._normalDistribution||(this._normalDistribution=De.generateNormalDistribution(64,this._totalSamples)),this._ssrPass.material.define("fragment","PHYSICALLY_CORRECT"),this._ssrPass.material.set("normalDistribution",this._normalDistribution),this._ssrPass.material.set("normalDistributionSize",[64,this._totalSamples])):this._ssrPass.material.undefine("fragment","PHYSICALLY_CORRECT"),this._physicallyCorrect=e},Oi.prototype.setSSAOTexture=function(e){var t=this._blurPass2;e?(t.material.enableTexture("ssaoTex"),t.material.set("ssaoTex",e)):t.material.disableTexture("ssaoTex")},Oi.prototype.isFinished=function(e){return!this._physicallyCorrect||e>this._totalSamples/this._samplePerFrame},Oi.prototype.dispose=function(e){this._ssrTexture.dispose(e),this._texture2.dispose(e),this._texture3.dispose(e),this._prevTexture.dispose(e),this._currentTexture.dispose(e),this._frameBuffer.dispose(e)};const Ii=[0,0,-.321585265978,-.154972575841,.458126042375,.188473391593,.842080129861,.527766490688,.147304551086,-.659453822776,-.331943915203,-.940619700594,.0479226680259,.54812163202,.701581552186,-.709825561388,-.295436780218,.940589268233,-.901489676764,.237713156085,.973570876096,-.109899459384,-.866792314779,-.451805525005,.330975007087,.800048655954,-.344275183665,.381779221166,-.386139432542,-.437418421534,-.576478634965,-.0148463392551,.385798197415,-.262426961053,-.666302061145,.682427250835,-.628010632582,-.732836215494,.10163141741,-.987658134403,.711995289051,-.320024291314,.0296005138058,.950296523438,.0130612307608,-.351024443122,-.879596633704,-.10478487883,.435712737232,.504254490347,.779203817497,.206477676721,.388264289969,-.896736162545,-.153106280781,-.629203242522,-.245517550697,.657969239148,.126830499058,.26862328493,-.634888119007,-.302301223431,.617074219636,.779817204925];function Ri(e,t,i,n,r){var o=e.gl;t.setUniform(o,"1i",i,r),o.activeTexture(o.TEXTURE0+r),n.isRenderable()?n.bind(e):n.unbind(e)}function zi(e,t,i,n,r){var o,a,s,l,h=e.gl;return function(r,d,c){if(!l||l.material!==r.material){var u=r.material,f=r.__program,p=u.get("roughness");null==p&&(p=1);var m=u.get("normalMap")||t,g=u.get("roughnessMap"),_=u.get("bumpMap"),v=u.get("uvRepeat"),x=u.get("uvOffset"),y=u.get("detailUvRepeat"),b=u.get("detailUvOffset"),w=!!_&&u.isTextureEnabled("bumpMap"),T=!!g&&u.isTextureEnabled("roughnessMap"),S=u.isDefined("fragment","DOUBLE_SIDED");_=_||i,g=g||n,c!==d?(d.set("normalMap",m),d.set("bumpMap",_),d.set("roughnessMap",g),d.set("useBumpMap",w),d.set("useRoughnessMap",T),d.set("doubleSide",S),null!=v&&d.set("uvRepeat",v),null!=x&&d.set("uvOffset",x),null!=y&&d.set("detailUvRepeat",y),null!=b&&d.set("detailUvOffset",b),d.set("roughness",p)):(f.setUniform(h,"1f","roughness",p),o!==m&&Ri(e,f,"normalMap",m,0),a!==_&&_&&Ri(e,f,"bumpMap",_,1),s!==g&&g&&Ri(e,f,"roughnessMap",g,2),null!=v&&f.setUniform(h,"2f","uvRepeat",v),null!=x&&f.setUniform(h,"2f","uvOffset",x),null!=y&&f.setUniform(h,"2f","detailUvRepeat",y),null!=b&&f.setUniform(h,"2f","detailUvOffset",b),f.setUniform(h,"1i","useBumpMap",+w),f.setUniform(h,"1i","useRoughnessMap",+T),f.setUniform(h,"1i","doubleSide",+S)),o=m,a=_,s=g,l=r}}}function Fi(e){this._depthTex=new F({format:Y.DEPTH_COMPONENT,type:Y.UNSIGNED_INT}),this._normalTex=new F({type:Y.HALF_FLOAT}),this._framebuffer=new Me,this._framebuffer.attach(this._normalTex),this._framebuffer.attach(this._depthTex,Me.DEPTH_ATTACHMENT),this._normalMaterial=new j({shader:new U(U.source("ecgl.normal.vertex"),U.source("ecgl.normal.fragment"))}),this._normalMaterial.enableTexture(["normalMap","bumpMap","roughnessMap"]),this._defaultNormalMap=me.createBlank("#000"),this._defaultBumpMap=me.createBlank("#000"),this._defaultRoughessMap=me.createBlank("#000"),this._debugPass=new Se({fragment:U.source("clay.compositor.output")}),this._debugPass.setUniform("texture",this._normalTex),this._debugPass.material.undefine("fragment","OUTPUT_ALPHA")}function Bi(e){e=e||{},this._edgePass=new Se({fragment:U.source("ecgl.edge")}),this._edgePass.setUniform("normalTexture",e.normalTexture),this._edgePass.setUniform("depthTexture",e.depthTexture),this._targetTexture=new F({type:Y.HALF_FLOAT}),this._frameBuffer=new Me,this._frameBuffer.attach(this._targetTexture)}U.import("@export ecgl.normal.vertex\n\n@import ecgl.common.transformUniforms\n\n@import ecgl.common.uv.header\n\n@import ecgl.common.attributes\n\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\n\n@import ecgl.common.normalMap.vertexHeader\n\n@import ecgl.common.vertexAnimation.header\n\nvoid main()\n{\n\n @import ecgl.common.vertexAnimation.main\n\n @import ecgl.common.uv.main\n\n v_Normal = normalize((worldInverseTranspose * vec4(normal, 0.0)).xyz);\n v_WorldPosition = (world * vec4(pos, 1.0)).xyz;\n\n @import ecgl.common.normalMap.vertexMain\n\n gl_Position = worldViewProjection * vec4(pos, 1.0);\n\n}\n\n\n@end\n\n\n@export ecgl.normal.fragment\n\n#define ROUGHNESS_CHANEL 0\n\nuniform bool useBumpMap;\nuniform bool useRoughnessMap;\nuniform bool doubleSide;\nuniform float roughness;\n\n@import ecgl.common.uv.fragmentHeader\n\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\n\nuniform mat4 viewInverse : VIEWINVERSE;\n\n@import ecgl.common.normalMap.fragmentHeader\n@import ecgl.common.bumpMap.header\n\nuniform sampler2D roughnessMap;\n\nvoid main()\n{\n vec3 N = v_Normal;\n \n bool flipNormal = false;\n if (doubleSide) {\n vec3 eyePos = viewInverse[3].xyz;\n vec3 V = normalize(eyePos - v_WorldPosition);\n\n if (dot(N, V) < 0.0) {\n flipNormal = true;\n }\n }\n\n @import ecgl.common.normalMap.fragmentMain\n\n if (useBumpMap) {\n N = bumpNormal(v_WorldPosition, v_Normal, N);\n }\n\n float g = 1.0 - roughness;\n\n if (useRoughnessMap) {\n float g2 = 1.0 - texture2D(roughnessMap, v_DetailTexcoord)[ROUGHNESS_CHANEL];\n g = clamp(g2 + (g - 0.5) * 2.0, 0.0, 1.0);\n }\n\n if (flipNormal) {\n N = -N;\n }\n\n gl_FragColor.rgb = (N.xyz + 1.0) * 0.5;\n gl_FragColor.a = g;\n}\n@end"),Fi.prototype.getDepthTexture=function(){return this._depthTex},Fi.prototype.getNormalTexture=function(){return this._normalTex},Fi.prototype.update=function(e,t,i){var n=e.getWidth(),r=e.getHeight(),o=this._depthTex,a=this._normalTex,s=this._normalMaterial;o.width=n,o.height=r,a.width=n,a.height=r;var l=t.getRenderList(i).opaque;this._framebuffer.bind(e),e.gl.clearColor(0,0,0,0),e.gl.clear(e.gl.COLOR_BUFFER_BIT|e.gl.DEPTH_BUFFER_BIT),e.gl.disable(e.gl.BLEND),e.renderPass(l,i,{getMaterial:function(){return s},ifRender:function(e){return e.renderNormal},beforeRender:zi(e,this._defaultNormalMap,this._defaultBumpMap,this._defaultRoughessMap,this._normalMaterial),sort:e.opaqueSortCompare}),this._framebuffer.unbind(e)},Fi.prototype.renderDebug=function(e){this._debugPass.render(e)},Fi.prototype.dispose=function(e){this._depthTex.dispose(e),this._normalTex.dispose(e)},Bi.prototype.update=function(e,t,i,n){var r=e.getWidth(),o=e.getHeight(),a=this._targetTexture;a.width=r,a.height=o;var s=this._frameBuffer;s.bind(e),this._edgePass.setUniform("projectionInv",t.invProjectionMatrix.array),this._edgePass.setUniform("textureSize",[r,o]),this._edgePass.setUniform("texture",i),this._edgePass.render(e),s.unbind(e)},Bi.prototype.getTargetTexture=function(){return this._targetTexture},Bi.prototype.setParameter=function(e,t){this._edgePass.setUniform(e,t)},Bi.prototype.dispose=function(e){this._targetTexture.dispose(e),this._frameBuffer.dispose(e)};const Gi={nodes:[{name:"source",type:"texture",outputs:{color:{}}},{name:"source_half",shader:"#source(clay.compositor.downsample)",inputs:{texture:"source"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 2)",height:"expr(height * 1.0 / 2)",type:"HALF_FLOAT"}}},parameters:{textureSize:"expr( [width * 1.0, height * 1.0] )"}},{name:"bright",shader:"#source(clay.compositor.bright)",inputs:{texture:"source_half"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 2)",height:"expr(height * 1.0 / 2)",type:"HALF_FLOAT"}}},parameters:{threshold:2,scale:4,textureSize:"expr([width * 1.0 / 2, height / 2])"}},{name:"bright_downsample_4",shader:"#source(clay.compositor.downsample)",inputs:{texture:"bright"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 4)",height:"expr(height * 1.0 / 4)",type:"HALF_FLOAT"}}},parameters:{textureSize:"expr( [width * 1.0 / 2, height / 2] )"}},{name:"bright_downsample_8",shader:"#source(clay.compositor.downsample)",inputs:{texture:"bright_downsample_4"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 8)",height:"expr(height * 1.0 / 8)",type:"HALF_FLOAT"}}},parameters:{textureSize:"expr( [width * 1.0 / 4, height / 4] )"}},{name:"bright_downsample_16",shader:"#source(clay.compositor.downsample)",inputs:{texture:"bright_downsample_8"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 16)",height:"expr(height * 1.0 / 16)",type:"HALF_FLOAT"}}},parameters:{textureSize:"expr( [width * 1.0 / 8, height / 8] )"}},{name:"bright_downsample_32",shader:"#source(clay.compositor.downsample)",inputs:{texture:"bright_downsample_16"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 32)",height:"expr(height * 1.0 / 32)",type:"HALF_FLOAT"}}},parameters:{textureSize:"expr( [width * 1.0 / 16, height / 16] )"}},{name:"bright_upsample_16_blur_h",shader:"#source(clay.compositor.gaussian_blur)",inputs:{texture:"bright_downsample_32"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 16)",height:"expr(height * 1.0 / 16)",type:"HALF_FLOAT"}}},parameters:{blurSize:1,blurDir:0,textureSize:"expr( [width * 1.0 / 32, height / 32] )"}},{name:"bright_upsample_16_blur_v",shader:"#source(clay.compositor.gaussian_blur)",inputs:{texture:"bright_upsample_16_blur_h"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 16)",height:"expr(height * 1.0 / 16)",type:"HALF_FLOAT"}}},parameters:{blurSize:1,blurDir:1,textureSize:"expr( [width * 1.0 / 16, height * 1.0 / 16] )"}},{name:"bright_upsample_8_blur_h",shader:"#source(clay.compositor.gaussian_blur)",inputs:{texture:"bright_downsample_16"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 8)",height:"expr(height * 1.0 / 8)",type:"HALF_FLOAT"}}},parameters:{blurSize:1,blurDir:0,textureSize:"expr( [width * 1.0 / 16, height * 1.0 / 16] )"}},{name:"bright_upsample_8_blur_v",shader:"#source(clay.compositor.gaussian_blur)",inputs:{texture:"bright_upsample_8_blur_h"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 8)",height:"expr(height * 1.0 / 8)",type:"HALF_FLOAT"}}},parameters:{blurSize:1,blurDir:1,textureSize:"expr( [width * 1.0 / 8, height * 1.0 / 8] )"}},{name:"bright_upsample_8_blend",shader:"#source(clay.compositor.blend)",inputs:{texture1:"bright_upsample_8_blur_v",texture2:"bright_upsample_16_blur_v"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 8)",height:"expr(height * 1.0 / 8)",type:"HALF_FLOAT"}}},parameters:{weight1:.3,weight2:.7}},{name:"bright_upsample_4_blur_h",shader:"#source(clay.compositor.gaussian_blur)",inputs:{texture:"bright_downsample_8"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 4)",height:"expr(height * 1.0 / 4)",type:"HALF_FLOAT"}}},parameters:{blurSize:1,blurDir:0,textureSize:"expr( [width * 1.0 / 8, height * 1.0 / 8] )"}},{name:"bright_upsample_4_blur_v",shader:"#source(clay.compositor.gaussian_blur)",inputs:{texture:"bright_upsample_4_blur_h"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 4)",height:"expr(height * 1.0 / 4)",type:"HALF_FLOAT"}}},parameters:{blurSize:1,blurDir:1,textureSize:"expr( [width * 1.0 / 4, height * 1.0 / 4] )"}},{name:"bright_upsample_4_blend",shader:"#source(clay.compositor.blend)",inputs:{texture1:"bright_upsample_4_blur_v",texture2:"bright_upsample_8_blend"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 4)",height:"expr(height * 1.0 / 4)",type:"HALF_FLOAT"}}},parameters:{weight1:.3,weight2:.7}},{name:"bright_upsample_2_blur_h",shader:"#source(clay.compositor.gaussian_blur)",inputs:{texture:"bright_downsample_4"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 2)",height:"expr(height * 1.0 / 2)",type:"HALF_FLOAT"}}},parameters:{blurSize:1,blurDir:0,textureSize:"expr( [width * 1.0 / 4, height * 1.0 / 4] )"}},{name:"bright_upsample_2_blur_v",shader:"#source(clay.compositor.gaussian_blur)",inputs:{texture:"bright_upsample_2_blur_h"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 2)",height:"expr(height * 1.0 / 2)",type:"HALF_FLOAT"}}},parameters:{blurSize:1,blurDir:1,textureSize:"expr( [width * 1.0 / 2, height * 1.0 / 2] )"}},{name:"bright_upsample_2_blend",shader:"#source(clay.compositor.blend)",inputs:{texture1:"bright_upsample_2_blur_v",texture2:"bright_upsample_4_blend"},outputs:{color:{parameters:{width:"expr(width * 1.0 / 2)",height:"expr(height * 1.0 / 2)",type:"HALF_FLOAT"}}},parameters:{weight1:.3,weight2:.7}},{name:"bright_upsample_full_blur_h",shader:"#source(clay.compositor.gaussian_blur)",inputs:{texture:"bright"},outputs:{color:{parameters:{width:"expr(width * 1.0)",height:"expr(height * 1.0)",type:"HALF_FLOAT"}}},parameters:{blurSize:1,blurDir:0,textureSize:"expr( [width * 1.0 / 2, height * 1.0 / 2] )"}},{name:"bright_upsample_full_blur_v",shader:"#source(clay.compositor.gaussian_blur)",inputs:{texture:"bright_upsample_full_blur_h"},outputs:{color:{parameters:{width:"expr(width * 1.0)",height:"expr(height * 1.0)",type:"HALF_FLOAT"}}},parameters:{blurSize:1,blurDir:1,textureSize:"expr( [width * 1.0, height * 1.0] )"}},{name:"bloom_composite",shader:"#source(clay.compositor.blend)",inputs:{texture1:"bright_upsample_full_blur_v",texture2:"bright_upsample_2_blend"},outputs:{color:{parameters:{width:"expr(width * 1.0)",height:"expr(height * 1.0)",type:"HALF_FLOAT"}}},parameters:{weight1:.3,weight2:.7}},{name:"coc",shader:"#source(ecgl.dof.coc)",outputs:{color:{parameters:{minFilter:"NEAREST",magFilter:"NEAREST",width:"expr(width * 1.0)",height:"expr(height * 1.0)"}}},parameters:{focalDist:50,focalRange:30}},{name:"dof_far_blur",shader:"#source(ecgl.dof.diskBlur)",inputs:{texture:"source",coc:"coc"},outputs:{color:{parameters:{width:"expr(width * 1.0)",height:"expr(height * 1.0)",type:"HALF_FLOAT"}}},parameters:{textureSize:"expr( [width * 1.0, height * 1.0] )"}},{name:"dof_near_blur",shader:"#source(ecgl.dof.diskBlur)",inputs:{texture:"source",coc:"coc"},outputs:{color:{parameters:{width:"expr(width * 1.0)",height:"expr(height * 1.0)",type:"HALF_FLOAT"}}},parameters:{textureSize:"expr( [width * 1.0, height * 1.0] )"},defines:{BLUR_NEARFIELD:null}},{name:"dof_coc_blur",shader:"#source(ecgl.dof.diskBlur)",inputs:{texture:"coc"},outputs:{color:{parameters:{minFilter:"NEAREST",magFilter:"NEAREST",width:"expr(width * 1.0)",height:"expr(height * 1.0)"}}},parameters:{textureSize:"expr( [width * 1.0, height * 1.0] )"},defines:{BLUR_COC:null}},{name:"dof_composite",shader:"#source(ecgl.dof.composite)",inputs:{original:"source",blurred:"dof_far_blur",nearfield:"dof_near_blur",coc:"coc",nearcoc:"dof_coc_blur"},outputs:{color:{parameters:{width:"expr(width * 1.0)",height:"expr(height * 1.0)",type:"HALF_FLOAT"}}}},{name:"composite",shader:"#source(clay.compositor.hdr.composite)",inputs:{texture:"source",bloom:"bloom_composite"},outputs:{color:{parameters:{width:"expr(width * 1.0)",height:"expr(height * 1.0)"}}},defines:{}},{name:"FXAA",shader:"#source(clay.compositor.fxaa)",inputs:{texture:"composite"}}]};function Hi(e,t){return{color:{parameters:{width:e,height:t}}}}U.import(Ce),U.import(Ae),U.import(Pe),U.import(Ee),U.import(Ne),U.import(Oe),U.import(Ie),U.import(Re),U.import(ze),U.import("@export ecgl.dof.coc\n\nuniform sampler2D depth;\n\nuniform float zNear: 0.1;\nuniform float zFar: 2000;\n\nuniform float focalDistance: 3;\nuniform float focalRange: 1;\nuniform float focalLength: 30;\nuniform float fstop: 2.8;\n\nvarying vec2 v_Texcoord;\n\n@import clay.util.encode_float\n\nvoid main()\n{\n float z = texture2D(depth, v_Texcoord).r * 2.0 - 1.0;\n\n float dist = 2.0 * zNear * zFar / (zFar + zNear - z * (zFar - zNear));\n\n float aperture = focalLength / fstop;\n\n float coc;\n\n float uppper = focalDistance + focalRange;\n float lower = focalDistance - focalRange;\n if (dist <= uppper && dist >= lower) {\n coc = 0.5;\n }\n else {\n float focalAdjusted = dist > uppper ? uppper : lower;\n\n coc = abs(aperture * (focalLength * (dist - focalAdjusted)) / (dist * (focalAdjusted - focalLength)));\n coc = clamp(coc, 0.0, 2.0) / 2.00001;\n\n if (dist < lower) {\n coc = -coc;\n }\n coc = coc * 0.5 + 0.5;\n }\n\n gl_FragColor = encodeFloat(coc);\n}\n@end\n\n\n@export ecgl.dof.composite\n\n#define DEBUG 0\n\nuniform sampler2D original;\nuniform sampler2D blurred;\nuniform sampler2D nearfield;\nuniform sampler2D coc;\nuniform sampler2D nearcoc;\nvarying vec2 v_Texcoord;\n\n@import clay.util.rgbm\n@import clay.util.float\n\nvoid main()\n{\n vec4 blurredColor = texture2D(blurred, v_Texcoord);\n vec4 originalColor = texture2D(original, v_Texcoord);\n\n float fCoc = decodeFloat(texture2D(coc, v_Texcoord));\n\n fCoc = abs(fCoc * 2.0 - 1.0);\n\n float weight = smoothstep(0.0, 1.0, fCoc);\n \n#ifdef NEARFIELD_ENABLED\n vec4 nearfieldColor = texture2D(nearfield, v_Texcoord);\n float fNearCoc = decodeFloat(texture2D(nearcoc, v_Texcoord));\n fNearCoc = abs(fNearCoc * 2.0 - 1.0);\n\n gl_FragColor = encodeHDR(\n mix(\n nearfieldColor, mix(originalColor, blurredColor, weight),\n pow(1.0 - fNearCoc, 4.0)\n )\n );\n#else\n gl_FragColor = encodeHDR(mix(originalColor, blurredColor, weight));\n#endif\n\n}\n\n@end\n\n\n\n@export ecgl.dof.diskBlur\n\n#define POISSON_KERNEL_SIZE 16;\n\nuniform sampler2D texture;\nuniform sampler2D coc;\nvarying vec2 v_Texcoord;\n\nuniform float blurRadius : 10.0;\nuniform vec2 textureSize : [512.0, 512.0];\n\nuniform vec2 poissonKernel[POISSON_KERNEL_SIZE];\n\nuniform float percent;\n\nfloat nrand(const in vec2 n) {\n return fract(sin(dot(n.xy ,vec2(12.9898,78.233))) * 43758.5453);\n}\n\n@import clay.util.rgbm\n@import clay.util.float\n\n\nvoid main()\n{\n vec2 offset = blurRadius / textureSize;\n\n float rnd = 6.28318 * nrand(v_Texcoord + 0.07 * percent );\n float cosa = cos(rnd);\n float sina = sin(rnd);\n vec4 basis = vec4(cosa, -sina, sina, cosa);\n\n#if !defined(BLUR_NEARFIELD) && !defined(BLUR_COC)\n offset *= abs(decodeFloat(texture2D(coc, v_Texcoord)) * 2.0 - 1.0);\n#endif\n\n#ifdef BLUR_COC\n float cocSum = 0.0;\n#else\n vec4 color = vec4(0.0);\n#endif\n\n\n float weightSum = 0.0;\n\n for (int i = 0; i < POISSON_KERNEL_SIZE; i++) {\n vec2 ofs = poissonKernel[i];\n\n ofs = vec2(dot(ofs, basis.xy), dot(ofs, basis.zw));\n\n vec2 uv = v_Texcoord + ofs * offset;\n vec4 texel = texture2D(texture, uv);\n\n float w = 1.0;\n#ifdef BLUR_COC\n float fCoc = decodeFloat(texel) * 2.0 - 1.0;\n cocSum += clamp(fCoc, -1.0, 0.0) * w;\n#else\n texel = texel;\n #if !defined(BLUR_NEARFIELD)\n float fCoc = decodeFloat(texture2D(coc, uv)) * 2.0 - 1.0;\n w *= abs(fCoc);\n #endif\n texel.rgb *= texel.a;\n color += texel * w;\n#endif\n\n weightSum += w;\n }\n\n#ifdef BLUR_COC\n gl_FragColor = encodeFloat(clamp(cocSum / weightSum, -1.0, 0.0) * 0.5 + 0.5);\n#else\n color /= weightSum;\n color.rgb /= (color.a + 0.0001);\n gl_FragColor = color;\n#endif\n}\n\n@end"),U.import("@export ecgl.edge\n\nuniform sampler2D texture;\n\nuniform sampler2D normalTexture;\nuniform sampler2D depthTexture;\n\nuniform mat4 projectionInv;\n\nuniform vec2 textureSize;\n\nuniform vec4 edgeColor: [0,0,0,0.8];\n\nvarying vec2 v_Texcoord;\n\nvec3 packColor(vec2 coord) {\n float z = texture2D(depthTexture, coord).r * 2.0 - 1.0;\n vec4 p = vec4(v_Texcoord * 2.0 - 1.0, z, 1.0);\n vec4 p4 = projectionInv * p;\n\n return vec3(\n texture2D(normalTexture, coord).rg,\n -p4.z / p4.w / 5.0\n );\n}\n\nvoid main() {\n vec2 cc = v_Texcoord;\n vec3 center = packColor(cc);\n\n float size = clamp(1.0 - (center.z - 10.0) / 100.0, 0.0, 1.0) * 0.5;\n float dx = size / textureSize.x;\n float dy = size / textureSize.y;\n\n vec2 coord;\n vec3 topLeft = packColor(cc+vec2(-dx, -dy));\n vec3 top = packColor(cc+vec2(0.0, -dy));\n vec3 topRight = packColor(cc+vec2(dx, -dy));\n vec3 left = packColor(cc+vec2(-dx, 0.0));\n vec3 right = packColor(cc+vec2(dx, 0.0));\n vec3 bottomLeft = packColor(cc+vec2(-dx, dy));\n vec3 bottom = packColor(cc+vec2(0.0, dy));\n vec3 bottomRight = packColor(cc+vec2(dx, dy));\n\n vec3 v = -topLeft-2.0*top-topRight+bottomLeft+2.0*bottom+bottomRight;\n vec3 h = -bottomLeft-2.0*left-topLeft+bottomRight+2.0*right+topRight;\n\n float edge = sqrt(dot(h, h) + dot(v, v));\n\n edge = smoothstep(0.8, 1.0, edge);\n\n gl_FragColor = mix(texture2D(texture, v_Texcoord), vec4(edgeColor.rgb, 1.0), edgeColor.a * edge);\n}\n@end");var Ui=["composite","FXAA"];function Vi(){this._width,this._height,this._dpr,this._sourceTexture=new F({type:Y.HALF_FLOAT}),this._depthTexture=new F({format:Y.DEPTH_COMPONENT,type:Y.UNSIGNED_INT}),this._framebuffer=new Me,this._framebuffer.attach(this._sourceTexture),this._framebuffer.attach(this._depthTexture,Me.DEPTH_ATTACHMENT),this._normalPass=new Fi,this._compositor=Le(Gi);var e=this._compositor.getNodeByName("source");e.texture=this._sourceTexture;var t=this._compositor.getNodeByName("coc");this._sourceNode=e,this._cocNode=t,this._compositeNode=this._compositor.getNodeByName("composite"),this._fxaaNode=this._compositor.getNodeByName("FXAA"),this._dofBlurNodes=["dof_far_blur","dof_near_blur","dof_coc_blur"].map((function(e){return this._compositor.getNodeByName(e)}),this),this._dofBlurKernel=0,this._dofBlurKernelSize=new Float32Array(0),this._finalNodesChain=Ui.map((function(e){return this._compositor.getNodeByName(e)}),this);var i={normalTexture:this._normalPass.getNormalTexture(),depthTexture:this._normalPass.getDepthTexture()};this._ssaoPass=new Ni(i),this._ssrPass=new Oi(i),this._edgePass=new Bi(i)}function ki(e){for(var t=[],i=0;i<30;i++)t.push([Ci(i,2),Ci(i,3)]);this._haltonSequence=t,this._frame=0,this._sourceTex=new F,this._sourceFb=new Me,this._sourceFb.attach(this._sourceTex),this._prevFrameTex=new F,this._outputTex=new F;var n=this._blendPass=new Se({fragment:U.source("clay.compositor.blend")});n.material.disableTexturesAll(),n.material.enableTexture(["texture1","texture2"]),this._blendFb=new Me({depthBuffer:!1}),this._outputPass=new Se({fragment:U.source("clay.compositor.output"),blendWithPrevious:!0}),this._outputPass.material.define("fragment","OUTPUT_ALPHA"),this._outputPass.material.blend=function(e){e.blendEquationSeparate(e.FUNC_ADD,e.FUNC_ADD),e.blendFuncSeparate(e.ONE,e.ONE_MINUS_SRC_ALPHA,e.ONE,e.ONE_MINUS_SRC_ALPHA)}}function Wi(e){e=e||"perspective",this.layer=null,this.scene=new W,this.rootNode=this.scene,this.viewport={x:0,y:0,width:0,height:0},this.setProjection(e),this._compositor=new Vi,this._temporalSS=new ki,this._shadowMapPass=new Fe;for(var t=[],i=0,n=0;n<30;n++){for(var r=[],o=0;o<6;o++)r.push(4*Ci(i,2)-2),r.push(4*Ci(i,3)-2),i++;t.push(r)}this._pcfKernels=t,this.scene.on("beforerender",(function(e,t,i){this.needsTemporalSS()&&this._temporalSS.jitterProjection(e,i)}),this)}Vi.prototype.resize=function(e,t,i){e*=i=i||1,t*=i;var n=this._sourceTexture,r=this._depthTexture;n.width=e,n.height=t,r.width=e,r.height=t;var o={getWidth:function(){return e},getHeight:function(){return t},getDevicePixelRatio:function(){return i}};function a(e,t){if("function"==typeof e[t]){var i=e[t].__original||e[t];e[t]=function(e){return i.call(this,o)},e[t].__original=i}}this._compositor.nodes.forEach((function(e){for(var t in e.outputs){var i=e.outputs[t].parameters;i&&(a(i,"width"),a(i,"height"))}for(var n in e.parameters)a(e.parameters,n)})),this._width=e,this._height=t,this._dpr=i},Vi.prototype.getWidth=function(){return this._width},Vi.prototype.getHeight=function(){return this._height},Vi.prototype._ifRenderNormalPass=function(){return this._enableSSAO||this._enableEdge||this._enableSSR},Vi.prototype._getPrevNode=function(e){for(var t=Ui.indexOf(e.name)-1,i=this._finalNodesChain[t];i&&!this._compositor.getNodeByName(i.name);)t-=1,i=this._finalNodesChain[t];return i},Vi.prototype._getNextNode=function(e){for(var t=Ui.indexOf(e.name)+1,i=this._finalNodesChain[t];i&&!this._compositor.getNodeByName(i.name);)t+=1,i=this._finalNodesChain[t];return i},Vi.prototype._addChainNode=function(e){var t=this._getPrevNode(e),i=this._getNextNode(e);t&&(e.inputs.texture=t.name,i?(e.outputs=Hi(this.getWidth.bind(this),this.getHeight.bind(this)),i.inputs.texture=e.name):e.outputs=null,this._compositor.addNode(e))},Vi.prototype._removeChainNode=function(e){var t=this._getPrevNode(e),i=this._getNextNode(e);t&&(i?(t.outputs=Hi(this.getWidth.bind(this),this.getHeight.bind(this)),i.inputs.texture=t.name):t.outputs=null,this._compositor.removeNode(e))},Vi.prototype.updateNormal=function(e,t,i,n){this._ifRenderNormalPass()&&this._normalPass.update(e,t,i)},Vi.prototype.updateSSAO=function(e,t,i,n){this._ssaoPass.update(e,i,n)},Vi.prototype.enableSSAO=function(){this._enableSSAO=!0},Vi.prototype.disableSSAO=function(){this._enableSSAO=!1},Vi.prototype.enableSSR=function(){this._enableSSR=!0},Vi.prototype.disableSSR=function(){this._enableSSR=!1},Vi.prototype.getSSAOTexture=function(){return this._ssaoPass.getTargetTexture()},Vi.prototype.getSourceFrameBuffer=function(){return this._framebuffer},Vi.prototype.getSourceTexture=function(){return this._sourceTexture},Vi.prototype.disableFXAA=function(){this._removeChainNode(this._fxaaNode)},Vi.prototype.enableFXAA=function(){this._addChainNode(this._fxaaNode)},Vi.prototype.enableBloom=function(){this._compositeNode.inputs.bloom="bloom_composite",this._compositor.dirty()},Vi.prototype.disableBloom=function(){this._compositeNode.inputs.bloom=null,this._compositor.dirty()},Vi.prototype.enableDOF=function(){this._compositeNode.inputs.texture="dof_composite",this._compositor.dirty()},Vi.prototype.disableDOF=function(){this._compositeNode.inputs.texture="source",this._compositor.dirty()},Vi.prototype.enableColorCorrection=function(){this._compositeNode.define("COLOR_CORRECTION"),this._enableColorCorrection=!0},Vi.prototype.disableColorCorrection=function(){this._compositeNode.undefine("COLOR_CORRECTION"),this._enableColorCorrection=!1},Vi.prototype.enableEdge=function(){this._enableEdge=!0},Vi.prototype.disableEdge=function(){this._enableEdge=!1},Vi.prototype.setBloomIntensity=function(e){this._compositeNode.setParameter("bloomIntensity",e)},Vi.prototype.setSSAOParameter=function(e,t){switch(e){case"quality":var i={low:6,medium:12,high:32,ultra:62}[t]||12;this._ssaoPass.setParameter("kernelSize",i);break;case"radius":this._ssaoPass.setParameter(e,t),this._ssaoPass.setParameter("bias",t/200);break;case"intensity":this._ssaoPass.setParameter(e,t)}},Vi.prototype.setDOFParameter=function(e,t){switch(e){case"focalDistance":case"focalRange":case"fstop":this._cocNode.setParameter(e,t);break;case"blurRadius":for(var i=0;i<this._dofBlurNodes.length;i++)this._dofBlurNodes[i].setParameter("blurRadius",t);break;case"quality":var n={low:4,medium:8,high:16,ultra:32}[t]||8;this._dofBlurKernelSize=n;for(i=0;i<this._dofBlurNodes.length;i++)this._dofBlurNodes[i].pass.material.define("POISSON_KERNEL_SIZE",n);this._dofBlurKernel=new Float32Array(2*n)}},Vi.prototype.setSSRParameter=function(e,t){if(null!=t)switch(e){case"quality":var i={low:10,medium:15,high:30,ultra:80}[t]||20,n={low:32,medium:16,high:8,ultra:4}[t]||16;this._ssrPass.setParameter("maxIteration",i),this._ssrPass.setParameter("pixelStride",n);break;case"maxRoughness":this._ssrPass.setParameter("minGlossiness",Math.max(Math.min(1-t,1),0));break;case"physical":this.setPhysicallyCorrectSSR(t)}},Vi.prototype.setPhysicallyCorrectSSR=function(e){this._ssrPass.setPhysicallyCorrect(e)},Vi.prototype.setEdgeColor=function(e){var t=Pt.parseColor(e);this._edgePass.setParameter("edgeColor",t)},Vi.prototype.setExposure=function(e){this._compositeNode.setParameter("exposure",Math.pow(2,e))},Vi.prototype.setColorLookupTexture=function(e,t){this._compositeNode.pass.material.setTextureImage("lut",this._enableColorCorrection?e:"none",t,{minFilter:Pt.Texture.NEAREST,magFilter:Pt.Texture.NEAREST,flipY:!1})},Vi.prototype.setColorCorrection=function(e,t){this._compositeNode.setParameter(e,t)},Vi.prototype.isSSREnabled=function(){return this._enableSSR},Vi.prototype.composite=function(e,t,i,n,r){var o=this._sourceTexture,a=o;this._enableEdge&&(this._edgePass.update(e,i,o,r),o=a=this._edgePass.getTargetTexture()),this._enableSSR&&(this._ssrPass.update(e,i,o,r),a=this._ssrPass.getTargetTexture(),this._ssrPass.setSSAOTexture(this._enableSSAO?this._ssaoPass.getTargetTexture():null)),this._sourceNode.texture=a,this._cocNode.setParameter("depth",this._depthTexture);for(var s=this._dofBlurKernel,l=this._dofBlurKernelSize,h=r%Math.floor(Ii.length/2/l),d=0;d<2*l;d++)s[d]=Ii[d+h*l*2];for(d=0;d<this._dofBlurNodes.length;d++)this._dofBlurNodes[d].setParameter("percent",r/30),this._dofBlurNodes[d].setParameter("poissonKernel",s);this._cocNode.setParameter("zNear",i.near),this._cocNode.setParameter("zFar",i.far),this._compositor.render(e,n)},Vi.prototype.dispose=function(e){this._sourceTexture.dispose(e),this._depthTexture.dispose(e),this._framebuffer.dispose(e),this._compositor.dispose(e),this._normalPass.dispose(e),this._ssaoPass.dispose(e)},ki.prototype={constructor:ki,jitterProjection:function(e,t){var i=e.viewport,n=i.devicePixelRatio||e.getDevicePixelRatio(),r=i.width*n,o=i.height*n,a=this._haltonSequence[this._frame%this._haltonSequence.length],s=new de;s.array[12]=(2*a[0]-1)/r,s.array[13]=(2*a[1]-1)/o,de.mul(t.projectionMatrix,s,t.projectionMatrix),de.invert(t.invProjectionMatrix,t.projectionMatrix)},resetFrame:function(){this._frame=0},getFrame:function(){return this._frame},getSourceFrameBuffer:function(){return this._sourceFb},getOutputTexture:function(){return this._outputTex},resize:function(e,t){this._prevFrameTex.width=e,this._prevFrameTex.height=t,this._outputTex.width=e,this._outputTex.height=t,this._sourceTex.width=e,this._sourceTex.height=t,this._prevFrameTex.dirty(),this._outputTex.dirty(),this._sourceTex.dirty()},isFinished:function(){return this._frame>=this._haltonSequence.length},render:function(e,t,i){var n=this._blendPass;0===this._frame?(n.setUniform("weight1",0),n.setUniform("weight2",1)):(n.setUniform("weight1",.9),n.setUniform("weight2",.1)),n.setUniform("texture1",this._prevFrameTex),n.setUniform("texture2",t||this._sourceTex),this._blendFb.attach(this._outputTex),this._blendFb.bind(e),n.render(e),this._blendFb.unbind(e),i||(this._outputPass.setUniform("texture",this._outputTex),this._outputPass.render(e));var r=this._prevFrameTex;this._prevFrameTex=this._outputTex,this._outputTex=r,this._frame++},dispose:function(e){this._sourceFb.dispose(e),this._blendFb.dispose(e),this._prevFrameTex.dispose(e),this._outputTex.dispose(e),this._sourceTex.dispose(e),this._outputPass.dispose(e),this._blendPass.dispose(e)}},Wi.prototype.setProjection=function(e){var t=this.camera;t&&t.update(),"perspective"===e?this.camera instanceof ne||(this.camera=new ne,t&&this.camera.setLocalTransform(t.localTransform)):this.camera instanceof re||(this.camera=new re,t&&this.camera.setLocalTransform(t.localTransform)),this.camera.near=.1,this.camera.far=2e3},Wi.prototype.setViewport=function(e,t,i,n,r){this.camera instanceof ne&&(this.camera.aspect=i/n),r=r||1,this.viewport.x=e,this.viewport.y=t,this.viewport.width=i,this.viewport.height=n,this.viewport.devicePixelRatio=r,this._compositor.resize(i*r,n*r),this._temporalSS.resize(i*r,n*r)},Wi.prototype.containPoint=function(e,t){var i=this.viewport;return t=this.layer.renderer.getHeight()-t,e>=i.x&&t>=i.y&&e<=i.x+i.width&&t<=i.y+i.height};var ji=new G;function Zi(e,t){var i=e.getBoxLayoutParams(),n=c(i,{width:t.getWidth(),height:t.getHeight()});n.y=t.getHeight()-n.y-n.height,this.viewGL.setViewport(n.x,n.y,n.width,n.height,t.getDevicePixelRatio());var r=e.get("boxWidth"),o=e.get("boxHeight"),a=e.get("boxDepth");this.getAxis("x").setExtent(-r/2,r/2),this.getAxis("y").setExtent(a/2,-a/2),this.getAxis("z").setExtent(-o/2,o/2),this.size=[r,o,a]}function Xi(e,t){var i={};e.eachSeries((function(e){if(e.coordinateSystem===this){var t=e.getData();["x","y","z"].forEach((function(e){t.mapDimensionsAll(e,!0).forEach((function(n){var r,o;r=e,o=t.getDataExtent(n,!0),i[r]=i[r]||[Infinity,-Infinity],i[r][0]=Math.min(o[0],i[r][0]),i[r][1]=Math.max(o[1],i[r][1])}))}))}}),this),["xAxis3D","yAxis3D","zAxis3D"].forEach((function(t){e.eachComponent(t,(function(e){var n=t.charAt(0),r=e.getReferringComponents("grid3D").models[0],o=r.coordinateSystem;if(o===this){var a=o.getAxis(n);if(!a){(a=new Li(n,u(i[n]||[Infinity,-Infinity],e))).type=e.get("type");var s="category"===a.type;a.onBand=s&&e.get("boundaryGap"),a.inverse=e.get("inverse"),e.axis=a,a.model=e,a.getLabelModel=function(){return e.getModel("axisLabel",r.getModel("axisLabel"))},a.getTickModel=function(){return e.getModel("axisTick",r.getModel("axisTick"))},o.addAxis(a)}}}),this)}),this),this.resize(this.model,t)}Wi.prototype.castRay=function(e,t,i){var n=this.layer.renderer,r=n.viewport;return n.viewport=this.viewport,n.screenToNDC(e,t,ji),this.camera.castRay(ji,i),n.viewport=r,i},Wi.prototype.prepareRender=function(){this.scene.update(),this.camera.update(),this.scene.updateLights();var e=this.scene.updateRenderList(this.camera);this._needsSortProgressively=!1;for(var t=0;t<e.transparent.length;t++){var i=e.transparent[t].geometry;i.needsSortVerticesProgressively&&i.needsSortVerticesProgressively()&&(this._needsSortProgressively=!0),i.needsSortTrianglesProgressively&&i.needsSortTrianglesProgressively()&&(this._needsSortProgressively=!0)}this._frame=0,this._temporalSS.resetFrame()},Wi.prototype.render=function(e,t){this._doRender(e,t,this._frame),this._frame++},Wi.prototype.needsAccumulate=function(){return this.needsTemporalSS()||this._needsSortProgressively},Wi.prototype.needsTemporalSS=function(){var e=this._enableTemporalSS;return"auto"===e&&(e=this._enablePostEffect),e},Wi.prototype.hasDOF=function(){return this._enableDOF},Wi.prototype.isAccumulateFinished=function(){return this.needsTemporalSS()?this._temporalSS.isFinished():this._frame>30},Wi.prototype._doRender=function(e,t,i){var n=this.scene,r=this.camera;i=i||0,this._updateTransparent(e,n,r,i),t||(this._shadowMapPass.kernelPCF=this._pcfKernels[0],this._shadowMapPass.render(e,n,r,!0)),this._updateShadowPCFKernel(i);var o,a=e.clearColor;(e.gl.clearColor(a[0],a[1],a[2],a[3]),this._enablePostEffect&&(this.needsTemporalSS()&&this._temporalSS.jitterProjection(e,r),this._compositor.updateNormal(e,n,r,this._temporalSS.getFrame())),this._updateSSAO(e,n,r,this._temporalSS.getFrame()),this._enablePostEffect)?((o=this._compositor.getSourceFrameBuffer()).bind(e),e.gl.clear(e.gl.DEPTH_BUFFER_BIT|e.gl.COLOR_BUFFER_BIT),e.render(n,r,!0,!0),o.unbind(e),this.needsTemporalSS()&&t?(this._compositor.composite(e,n,r,this._temporalSS.getSourceFrameBuffer(),this._temporalSS.getFrame()),e.setViewport(this.viewport),this._temporalSS.render(e)):(e.setViewport(this.viewport),this._compositor.composite(e,n,r,null,0))):this.needsTemporalSS()&&t?((o=this._temporalSS.getSourceFrameBuffer()).bind(e),e.saveClear(),e.clearBit=e.gl.DEPTH_BUFFER_BIT|e.gl.COLOR_BUFFER_BIT,e.render(n,r,!0,!0),e.restoreClear(),o.unbind(e),e.setViewport(this.viewport),this._temporalSS.render(e)):(e.setViewport(this.viewport),e.render(n,r,!0,!0))},Wi.prototype._updateTransparent=function(e,t,i,n){for(var r=new B,o=new de,a=i.getWorldPosition(),s=t.getRenderList(i).transparent,l=0;l<s.length;l++){var h=s[l],d=h.geometry;de.invert(o,h.worldTransform),B.transformMat4(r,a,o),d.needsSortTriangles&&d.needsSortTriangles()&&d.doSortTriangles(r,n),d.needsSortVertices&&d.needsSortVertices()&&d.doSortVertices(r,n)}},Wi.prototype._updateSSAO=function(e,t,i){var n=this._enableSSAO&&this._enablePostEffect;n&&this._compositor.updateSSAO(e,t,i,this._temporalSS.getFrame());for(var r=t.getRenderList(i),o=0;o<r.opaque.length;o++){var a=r.opaque[o];a.renderNormal&&a.material[n?"enableTexture":"disableTexture"]("ssaoMap"),n&&a.material.set("ssaoMap",this._compositor.getSSAOTexture())}},Wi.prototype._updateShadowPCFKernel=function(e){for(var t=this._pcfKernels[e%this._pcfKernels.length],i=this.scene.getRenderList(this.camera).opaque,n=0;n<i.length;n++)i[n].receiveShadow&&(i[n].material.set("pcfKernel",t),i[n].material.define("fragment","PCF_KERNEL_SIZE",t.length/2))},Wi.prototype.dispose=function(e){this._compositor.dispose(e.gl),this._temporalSS.dispose(e.gl),this._shadowMapPass.dispose(e)},Wi.prototype.setPostEffect=function(e,t){var i=this._compositor;this._enablePostEffect=e.get("enable");var n=e.getModel("bloom"),r=e.getModel("edge"),o=e.getModel("DOF",e.getModel("depthOfField")),a=e.getModel("SSAO",e.getModel("screenSpaceAmbientOcclusion")),s=e.getModel("SSR",e.getModel("screenSpaceReflection")),l=e.getModel("FXAA"),h=e.getModel("colorCorrection");n.get("enable")?i.enableBloom():i.disableBloom(),o.get("enable")?i.enableDOF():i.disableDOF(),s.get("enable")?i.enableSSR():i.disableSSR(),h.get("enable")?i.enableColorCorrection():i.disableColorCorrection(),r.get("enable")?i.enableEdge():i.disableEdge(),l.get("enable")?i.enableFXAA():i.disableFXAA(),this._enableDOF=o.get("enable"),this._enableSSAO=a.get("enable"),this._enableSSAO?i.enableSSAO():i.disableSSAO(),i.setBloomIntensity(n.get("intensity")),i.setEdgeColor(r.get("color")),i.setColorLookupTexture(h.get("lookupTexture"),t),i.setExposure(h.get("exposure")),["radius","quality","intensity"].forEach((function(e){i.setSSAOParameter(e,a.get(e))})),["quality","maxRoughness","physical"].forEach((function(e){i.setSSRParameter(e,s.get(e))})),["quality","focalDistance","focalRange","blurRadius","fstop"].forEach((function(e){i.setDOFParameter(e,o.get(e))})),["brightness","contrast","saturation"].forEach((function(e){i.setColorCorrection(e,h.get(e))}))},Wi.prototype.setDOFFocusOnPoint=function(e){if(this._enablePostEffect){if(e>this.camera.far||e<this.camera.near)return;return this._compositor.setDOFParameter("focalDistance",e),!0}},Wi.prototype.setTemporalSuperSampling=function(e){this._enableTemporalSS=e.get("enable")},Wi.prototype.isLinearSpace=function(){return this._enablePostEffect},Wi.prototype.setRootNode=function(e){if(this.rootNode!==e){for(var t=this.rootNode.children(),i=0;i<t.length;i++)e.add(t[i]);e!==this.scene&&this.scene.add(e),this.rootNode=e}},Wi.prototype.add=function(e){this.rootNode.add(e)},Wi.prototype.remove=function(e){this.rootNode.remove(e)},Wi.prototype.removeAll=function(e){this.rootNode.removeAll(e)},Object.assign(Wi.prototype,ye);var Yi={dimensions:Di.prototype.dimensions,create:function(e,t){var i=[];e.eachComponent("grid3D",(function(e){e.__viewGL=e.__viewGL||new Wi;var t=new Di;t.model=e,t.viewGL=e.__viewGL,e.coordinateSystem=t,i.push(t),t.resize=Zi,t.update=Xi}));var n=["xAxis3D","yAxis3D","zAxis3D"];return e.eachSeries((function(t){if("cartesian3D"===t.get("coordinateSystem")){if(null==(r=t.getReferringComponents("grid3D").models[0])){var i=function(e,t){return n.map((function(i){var n=e.getReferringComponents(i).models[0];return null==n&&(n=t.getComponent(i)),n}))}(t,e),r=i[0].getCoordSysModel();i.forEach((function(e){e.getCoordSysModel()}))}var o=r.coordinateSystem;t.coordinateSystem=o}})),i}},qi=r.extend({type:"cartesian3DAxis",axis:null,getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid3D",index:this.option.gridIndex,id:this.option.gridId})[0]}});f(qi);var Qi={show:!0,grid3DIndex:0,inverse:!1,name:"",nameLocation:"middle",nameTextStyle:{fontSize:16},nameGap:20,axisPointer:{},axisLine:{},axisTick:{},axisLabel:{},splitArea:{}},Ki=Ye({boundaryGap:!0,axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"},axisPointer:{label:{show:!1}}},Qi),Ji=Ye({boundaryGap:[0,0],splitNumber:5,axisPointer:{label:{}}},Qi),$i=qe({scale:!0,min:"dataMin",max:"dataMax"},Ji),en=qe({logBase:10},Ji);en.scale=!0;const tn={categoryAxis3D:Ki,valueAxis3D:Ji,timeAxis3D:$i,logAxis3D:en};var nn=["value","category","time","log"];function rn(e,t){return t.type||(t.data?"category":"value")}m((function(e){e.registerComponentModel(Yt),e.registerComponentView(Mi),e.registerCoordinateSystem("grid3D",Yi),["x","y","z"].forEach((function(t){!function(e,t,i,n,r){nn.forEach((function(o){var a=i.extend({type:t+"Axis3D."+o,__ordinalMeta:null,mergeDefaultAndTheme:function(e,i){var r=i.getTheme();Ye(e,r.get(o+"Axis3D")),Ye(e,this.getDefaultOption()),e.type=n(t,e)},optionUpdated:function(){"category"===this.option.type&&(this.__ordinalMeta=p.createByAxisModel(this))},getCategories:function(){if("category"===this.option.type)return this.__ordinalMeta.categories},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:Ye(et(tn[o+"Axis3D"]),r||{},!0)});e.registerComponentModel(a)})),e.registerSubTypeDefaulter(t+"Axis3D",tt(n,t))}(e,t,qi,rn,{name:t.toUpperCase()});const i=e.ComponentView.extend({type:t+"Axis3D"});e.registerComponentView(i)})),e.registerAction({type:"grid3DChangeCamera",event:"grid3dcamerachanged",update:"series:updateCamera"},(function(e,t){t.eachComponent({mainType:"grid3D",query:e},(function(t){t.setView(e)}))})),e.registerAction({type:"grid3DShowAxisPointer",event:"grid3dshowaxispointer",update:"grid3D:showAxisPointer"},(function(e,t){})),e.registerAction({type:"grid3DHideAxisPointer",event:"grid3dhideaxispointer",update:"grid3D:hideAxisPointer"},(function(e,t){}))}));const on={defaultOption:{shading:null,realisticMaterial:{textureTiling:1,textureOffset:0,detailTexture:null},lambertMaterial:{textureTiling:1,textureOffset:0,detailTexture:null},colorMaterial:{textureTiling:1,textureOffset:0,detailTexture:null},hatchingMaterial:{textureTiling:1,textureOffset:0,paperColor:"#fff"}}},an={getFilledRegions:function(e,t){var i,n=(e||[]).slice();if("string"==typeof t?i=(t=g(t))&&t.geoJson:t&&t.features&&(i=t),!i)return[];for(var r={},o=i.features,a=0;a<n.length;a++)r[n[a].name]=n[a];for(a=0;a<o.length;a++){var s=o[a].properties.name;r[s]||n.push({name:s})}return n},defaultOption:{show:!0,zlevel:-10,map:"",left:0,top:0,width:"100%",height:"100%",boxWidth:100,boxHeight:10,boxDepth:"auto",regionHeight:3,environment:"auto",groundPlane:{show:!1,color:"#aaa"},shading:"lambert",light:{main:{alpha:40,beta:30}},viewControl:{alpha:40,beta:0,distance:100,orthographicSize:60,minAlpha:5,minBeta:-80,maxBeta:80},label:{show:!1,distance:2,textStyle:{fontSize:20,color:"#000",backgroundColor:"rgba(255,255,255,0.7)",padding:3,borderRadius:4}},itemStyle:{color:"#fff",borderWidth:0,borderColor:"#333"},emphasis:{itemStyle:{color:"#639fc0"},label:{show:!0}}}};var sn=r.extend({type:"geo3D",layoutMode:"box",coordinateSystem:null,optionUpdated:function(){var e=this.option;e.regions=this.getFilledRegions(e.regions,e.map);var t=_(e.data||[],{coordDimensions:["value"],encodeDefine:this.get("encode"),dimensionsDefine:this.get("dimensions")}),i=new v(t,this);i.initData(e.regions);var n={};i.each((function(e){var t=i.getName(e),r=i.getItemModel(e);n[t]=r})),this._regionModelMap=n,this._data=i},getData:function(){return this._data},getRegionModel:function(e){var t=this.getData().getName(e);return this._regionModelMap[t]||new o(null,this)},getRegionPolygonCoords:function(e){var t=this.getData().getName(e),i=this.coordinateSystem.getRegion(t);return i?i.geometries:[]},getFormattedLabel:function(e,t){var i=this._data.getName(e),n=this.getRegionModel(e),r=n.get("normal"===t?["label","formatter"]:["emphasis","label","formatter"]);null==r&&(r=n.get(["label","formatter"]));var o={name:i};if("function"==typeof r)return o.status=t,r(o);if("string"==typeof r){var a=o.seriesName;return r.replace("{a}",null!=a?a:"")}return i},defaultOption:{regions:[]}});function ln(e,t,i){i=i||2;var n,r,o,a,s,l,h,d=t&&t.length,c=d?t[0]*i:e.length,u=hn(e,0,c,i,!0),f=[];if(!u)return f;if(d&&(u=function(e,t,i,n){var r,o,a,s=[];for(r=0,o=t.length;r<o;r++)(a=hn(e,t[r]*n,r<o-1?t[r+1]*n:e.length,n,!1))===a.next&&(a.steiner=!0),s.push(xn(a));for(s.sort(gn),r=0;r<s.length;r++)_n(s[r],i),i=dn(i,i.next);return i}(e,t,u,i)),e.length>80*i){n=o=e[0],r=a=e[1];for(var p=i;p<c;p+=i)(s=e[p])<n&&(n=s),(l=e[p+1])<r&&(r=l),s>o&&(o=s),l>a&&(a=l);h=Math.max(o-n,a-r)}return cn(u,f,i,n,r,h),f}function hn(e,t,i,n,r){var o,a;if(r===Pn(e,t,i,n)>0)for(o=t;o<i;o+=n)a=Ln(o,e[o],e[o+1],a);else for(o=i-n;o>=t;o-=n)a=Ln(o,e[o],e[o+1],a);return a&&Tn(a,a.next)&&(Cn(a),a=a.next),a}function dn(e,t){if(!e)return e;t||(t=e);var i,n=e;do{if(i=!1,n.steiner||!Tn(n,n.next)&&0!==wn(n.prev,n,n.next))n=n.next;else{if(Cn(n),(n=t=n.prev)===n.next)return null;i=!0}}while(i||n!==t);return t}function cn(e,t,i,n,r,o,a){if(e){!a&&o&&function(e,t,i,n){var r=e;do{null===r.z&&(r.z=vn(r.x,r.y,t,i,n)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next}while(r!==e);r.prevZ.nextZ=null,r.prevZ=null,function(e){var t,i,n,r,o,a,s,l,h=1;do{for(i=e,e=null,o=null,a=0;i;){for(a++,n=i,s=0,t=0;t<h&&(s++,n=n.nextZ);t++);for(l=h;s>0||l>0&&n;)0!==s&&(0===l||!n||i.z<=n.z)?(r=i,i=i.nextZ,s--):(r=n,n=n.nextZ,l--),o?o.nextZ=r:e=r,r.prevZ=o,o=r;i=n}o.nextZ=null,h*=2}while(a>1)}(r)}(e,n,r,o);for(var s,l,h=e;e.prev!==e.next;)if(s=e.prev,l=e.next,o?fn(e,n,r,o):un(e))t.push(s.i/i),t.push(e.i/i),t.push(l.i/i),Cn(e),e=l.next,h=l.next;else if((e=l)===h){a?1===a?cn(e=pn(e,t,i),t,i,n,r,o,2):2===a&&mn(e,t,i,n,r,o):cn(dn(e),t,i,n,r,o,1);break}}}function un(e){var t=e.prev,i=e,n=e.next;if(wn(t,i,n)>=0)return!1;for(var r=e.next.next;r!==e.prev;){if(yn(t.x,t.y,i.x,i.y,n.x,n.y,r.x,r.y)&&wn(r.prev,r,r.next)>=0)return!1;r=r.next}return!0}function fn(e,t,i,n){var r=e.prev,o=e,a=e.next;if(wn(r,o,a)>=0)return!1;for(var s=r.x<o.x?r.x<a.x?r.x:a.x:o.x<a.x?o.x:a.x,l=r.y<o.y?r.y<a.y?r.y:a.y:o.y<a.y?o.y:a.y,h=r.x>o.x?r.x>a.x?r.x:a.x:o.x>a.x?o.x:a.x,d=r.y>o.y?r.y>a.y?r.y:a.y:o.y>a.y?o.y:a.y,c=vn(s,l,t,i,n),u=vn(h,d,t,i,n),f=e.nextZ;f&&f.z<=u;){if(f!==e.prev&&f!==e.next&&yn(r.x,r.y,o.x,o.y,a.x,a.y,f.x,f.y)&&wn(f.prev,f,f.next)>=0)return!1;f=f.nextZ}for(f=e.prevZ;f&&f.z>=c;){if(f!==e.prev&&f!==e.next&&yn(r.x,r.y,o.x,o.y,a.x,a.y,f.x,f.y)&&wn(f.prev,f,f.next)>=0)return!1;f=f.prevZ}return!0}function pn(e,t,i){var n=e;do{var r=n.prev,o=n.next.next;!Tn(r,o)&&Sn(r,n,n.next,o)&&Mn(r,o)&&Mn(o,r)&&(t.push(r.i/i),t.push(n.i/i),t.push(o.i/i),Cn(n),Cn(n.next),n=e=o),n=n.next}while(n!==e);return n}function mn(e,t,i,n,r,o){var a=e;do{for(var s=a.next.next;s!==a.prev;){if(a.i!==s.i&&bn(a,s)){var l=Dn(a,s);return a=dn(a,a.next),l=dn(l,l.next),cn(a,t,i,n,r,o),void cn(l,t,i,n,r,o)}s=s.next}a=a.next}while(a!==e)}function gn(e,t){return e.x-t.x}function _n(e,t){if(t=function(e,t){var i,n=t,r=e.x,o=e.y,a=-Infinity;do{if(o<=n.y&&o>=n.next.y&&n.next.y!==n.y){var s=n.x+(o-n.y)*(n.next.x-n.x)/(n.next.y-n.y);if(s<=r&&s>a){if(a=s,s===r){if(o===n.y)return n;if(o===n.next.y)return n.next}i=n.x<n.next.x?n:n.next}}n=n.next}while(n!==t);if(!i)return null;if(r===a)return i.prev;var l,h=i,d=i.x,c=i.y,u=Infinity;n=i.next;for(;n!==h;)r>=n.x&&n.x>=d&&r!==n.x&&yn(o<c?r:a,o,d,c,o<c?a:r,o,n.x,n.y)&&((l=Math.abs(o-n.y)/(r-n.x))<u||l===u&&n.x>i.x)&&Mn(n,e)&&(i=n,u=l),n=n.next;return i}(e,t),t){var i=Dn(t,e);dn(i,i.next)}}function vn(e,t,i,n,r){return(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=32767*(e-i)/r)|e<<8))|e<<4))|e<<2))|e<<1))|(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=32767*(t-n)/r)|t<<8))|t<<4))|t<<2))|t<<1))<<1}function xn(e){var t=e,i=e;do{t.x<i.x&&(i=t),t=t.next}while(t!==e);return i}function yn(e,t,i,n,r,o,a,s){return(r-a)*(t-s)-(e-a)*(o-s)>=0&&(e-a)*(n-s)-(i-a)*(t-s)>=0&&(i-a)*(o-s)-(r-a)*(n-s)>=0}function bn(e,t){return e.next.i!==t.i&&e.prev.i!==t.i&&!function(e,t){var i=e;do{if(i.i!==e.i&&i.next.i!==e.i&&i.i!==t.i&&i.next.i!==t.i&&Sn(i,i.next,e,t))return!0;i=i.next}while(i!==e);return!1}(e,t)&&Mn(e,t)&&Mn(t,e)&&function(e,t){var i=e,n=!1,r=(e.x+t.x)/2,o=(e.y+t.y)/2;do{i.y>o!=i.next.y>o&&i.next.y!==i.y&&r<(i.next.x-i.x)*(o-i.y)/(i.next.y-i.y)+i.x&&(n=!n),i=i.next}while(i!==e);return n}(e,t)}function wn(e,t,i){return(t.y-e.y)*(i.x-t.x)-(t.x-e.x)*(i.y-t.y)}function Tn(e,t){return e.x===t.x&&e.y===t.y}function Sn(e,t,i,n){return!!(Tn(e,t)&&Tn(i,n)||Tn(e,n)&&Tn(i,t))||wn(e,t,i)>0!=wn(e,t,n)>0&&wn(i,n,e)>0!=wn(i,n,t)>0}function Mn(e,t){return wn(e.prev,e,e.next)<0?wn(e,t,e.next)>=0&&wn(e,e.prev,t)>=0:wn(e,t,e.prev)<0||wn(e,e.next,t)<0}function Dn(e,t){var i=new An(e.i,e.x,e.y),n=new An(t.i,t.x,t.y),r=e.next,o=t.prev;return e.next=t,t.prev=e,i.next=r,r.prev=i,n.next=i,i.prev=n,o.next=n,n.prev=o,n}function Ln(e,t,i,n){var r=new An(e,t,i);return n?(r.next=n.next,r.prev=n,n.next.prev=r,n.next=r):(r.prev=r,r.next=r),r}function Cn(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function An(e,t,i){this.i=e,this.x=t,this.y=i,this.prev=null,this.next=null,this.z=null,this.prevZ=null,this.nextZ=null,this.steiner=!1}function Pn(e,t,i,n){for(var r=0,o=t,a=i-n;o<i;o+=n)r+=(e[a]-e[o])*(e[o+1]+e[a+1]),a=o;return r}function En(e,t,i){var n=e[t];e[t]=e[i],e[i]=n}function Nn(e,t,i,n,r){var o=i,a=e[t];En(e,t,n);for(var s=i;s<n;s++)r(e[s],a)<0&&(En(e,s,o),o++);return En(e,n,o),o}function On(e,t,i,n){if(i<n){var r=Nn(e,Math.floor((i+n)/2),i,n,t);On(e,t,i,r-1),On(e,t,r+1,n)}}function In(){this._parts=[]}Ye(sn.prototype,an),Ye(sn.prototype,jt),Ye(sn.prototype,Zt),Ye(sn.prototype,Xt),Ye(sn.prototype,on),ln.deviation=function(e,t,i,n){var r=t&&t.length,o=r?t[0]*i:e.length,a=Math.abs(Pn(e,0,o,i));if(r)for(var s=0,l=t.length;s<l;s++){var h=t[s]*i,d=s<l-1?t[s+1]*i:e.length;a-=Math.abs(Pn(e,h,d,i))}var c=0;for(s=0;s<n.length;s+=3){var u=n[s]*i,f=n[s+1]*i,p=n[s+2]*i;c+=Math.abs((e[u]-e[p])*(e[f+1]-e[u+1])-(e[u]-e[f])*(e[p+1]-e[u+1]))}return 0===a&&0===c?0:Math.abs((c-a)/a)},In.prototype.step=function(e,t,i){var n=e.length;if(0===i){this._parts=[],this._sorted=!1;var r=Math.floor(n/2);this._parts.push({pivot:r,left:0,right:n-1}),this._currentSortPartIdx=0}if(!this._sorted){var o=this._parts;if(0===o.length)return this._sorted=!0,!0;if(o.length<512){for(var a=0;a<o.length;a++)o[a].pivot=Nn(e,o[a].pivot,o[a].left,o[a].right,t);var s=[];for(a=0;a<o.length;a++){var l=o[a].left;(h=o[a].pivot-1)>l&&s.push({pivot:Math.floor((h+l)/2),left:l,right:h});var h;l=o[a].pivot+1;(h=o[a].right)>l&&s.push({pivot:Math.floor((h+l)/2),left:l,right:h})}o=this._parts=s}else for(a=0;a<Math.floor(o.length/10);a++){var d=o.length-1-this._currentSortPartIdx;if(On(e,t,o[d].left,o[d].right),this._currentSortPartIdx++,this._currentSortPartIdx===o.length)return this._sorted=!0,!0}return!1}},In.sort=On;var Rn=we.vec3,zn=Rn.create(),Fn=Rn.create(),Bn=Rn.create();const Gn={needsSortTriangles:function(){return this.indices&&this.sortTriangles},needsSortTrianglesProgressively:function(){return this.needsSortTriangles()&&this.triangleCount>=2e4},doSortTriangles:function(e,t){var i=this.indices;if(0===t){var n=this.attributes.position;e=e.array;this._triangleZList&&this._triangleZList.length===this.triangleCount||(this._triangleZList=new Float32Array(this.triangleCount),this._sortedTriangleIndices=new Uint32Array(this.triangleCount),this._indicesTmp=new i.constructor(i.length),this._triangleZListTmp=new Float32Array(this.triangleCount));for(var r,o=0,a=0;a<i.length;){n.get(i[a++],zn),n.get(i[a++],Fn),n.get(i[a++],Bn);var s=Rn.sqrDist(zn,e),l=Rn.sqrDist(Fn,e),h=Rn.sqrDist(Bn,e),d=Math.min(s,l);d=Math.min(d,h),3===a?(r=d,d=0):d-=r,this._triangleZList[o++]=d}}var c=this._sortedTriangleIndices;for(a=0;a<c.length;a++)c[a]=a;if(this.triangleCount<2e4)0===t&&this._simpleSort(!0);else for(a=0;a<3;a++)this._progressiveQuickSort(3*t+a);var u=this._indicesTmp,f=this._triangleZListTmp,p=this._triangleZList;for(a=0;a<this.triangleCount;a++){var m=3*c[a],g=3*a;u[g++]=i[m++],u[g++]=i[m++],u[g]=i[m],f[a]=p[c[a]]}var _=this._indicesTmp;this._indicesTmp=this.indices,this.indices=_;_=this._triangleZListTmp;this._triangleZListTmp=this._triangleZList,this._triangleZList=_,this.dirtyIndices()},_simpleSort:function(e){var t=this._triangleZList,i=this._sortedTriangleIndices;function n(e,i){return t[i]-t[e]}e?Array.prototype.sort.call(i,n):In.sort(i,n,0,i.length-1)},_progressiveQuickSort:function(e){var t=this._triangleZList,i=this._sortedTriangleIndices;this._quickSort=this._quickSort||new In,this._quickSort.step(i,(function(e,i){return t[i]-t[e]}),e)}};function Hn(e,t){const i=e.getItemVisual(t,"style");if(i){return i[e.getVisual("drawType")]}}function Un(e,t){const i=e.getItemVisual(t,"style");return i&&i.opacity}function Vn(e,t,i){this._labelsMesh=new _i,this._labelTextureSurface=new ri({width:512,height:512,devicePixelRatio:i.getDevicePixelRatio(),onupdate:function(){i.getZr().refresh()}}),this._api=i,this._labelsMesh.material.set("textureAtlas",this._labelTextureSurface.getTexture())}Vn.prototype.getLabelPosition=function(e,t,i){return[0,0,0]},Vn.prototype.getLabelDistance=function(e,t,i){return 0},Vn.prototype.getMesh=function(){return this._labelsMesh},Vn.prototype.updateData=function(e,t,i){null==t&&(t=0),null==i&&(i=e.count()),this._labelsVisibilitiesBits&&this._labelsVisibilitiesBits.length===i-t||(this._labelsVisibilitiesBits=new Uint8Array(i-t));for(var n=["label","show"],r=["emphasis","label","show"],o=t;o<i;o++){var a=e.getItemModel(o),s=a.get(n),l=a.get(r);null==l&&(l=s);var h=(s?1:0)|(l?2:0);this._labelsVisibilitiesBits[o-t]=h}this._start=t,this._end=i,this._data=e},Vn.prototype.updateLabels=function(e){if(this._data){for(var t=(e=e||[]).length>0,i={},n=0;n<e.length;n++)i[e[n]]=!0;this._labelsMesh.geometry.convertToDynamicArray(!0),this._labelTextureSurface.clear();for(var r=["label"],o=["emphasis","label"],s=this._data.hostModel,l=this._data,h=s.getModel(r),d=s.getModel(o,h),c={left:"right",right:"left",top:"center",bottom:"center"},u={left:"middle",right:"middle",top:"bottom",bottom:"top"},f=this._start;f<this._end;f++){var p=!1;if(t&&i[f]&&(p=!0),this._labelsVisibilitiesBits[f-this._start]&(p?2:1)){var m=l.getItemModel(f).getModel(p?o:r,p?d:h),g=m.get("distance")||0,_=m.get("position"),v=this._api.getDevicePixelRatio(),x=s.getFormattedLabel(f,p?"emphasis":"normal");if(null==x||""===x)return;var y=new Je({style:a(m,{text:x,fill:m.get("color")||Hn(l,f)||"#000",align:"left",verticalAlign:"top",opacity:St(m.get("opacity"),Un(l,f),1)})}),b=y.getBoundingRect();b.height*=1.2;var w=this._labelTextureSurface.add(y),T=c[_]||"center",S=u[_]||"bottom";this._labelsMesh.geometry.addSprite(this.getLabelPosition(f,_,g),[b.width*v,b.height*v],w,T,S,this.getLabelDistance(f,_,g)*v)}}this._labelsMesh.material.set("uvScale",this._labelTextureSurface.getCoordsScale()),this._labelTextureSurface.getZr().refreshImmediately(),this._labelsMesh.geometry.convertToTypedArray(),this._labelsMesh.geometry.dirty()}},Vn.prototype.dispose=function(){this._labelTextureSurface.dispose()};var kn=we.vec3;function Wn(e){this.rootNode=new Pt.Node,this._triangulationResults={},this._shadersMap=Pt.COMMON_SHADERS.filter((function(e){return"shadow"!==e})).reduce((function(e,t){return e[t]=Pt.createShader("ecgl."+t),e}),{}),this._linesShader=Pt.createShader("ecgl.meshLines3D");var t={};Pt.COMMON_SHADERS.forEach((function(e){t[e]=new Pt.Material({shader:Pt.createShader("ecgl."+e)})})),this._groundMaterials=t,this._groundMesh=new Pt.Mesh({geometry:new Pt.PlaneGeometry({dynamic:!0}),castShadow:!1,renderNormal:!0,$ignorePicking:!0}),this._groundMesh.rotation.rotateX(-Math.PI/2),this._labelsBuilder=new Vn(512,512,e),this._labelsBuilder.getMesh().renderOrder=100,this._labelsBuilder.getMesh().material.depthTest=!1,this.rootNode.add(this._labelsBuilder.getMesh()),this._initMeshes(),this._api=e}Pt.Shader.import(wi),Wn.prototype={constructor:Wn,extrudeY:!0,update:function(e,t,i,n,r){var o=e.getData();null==n&&(n=0),null==r&&(r=o.count()),this._startIndex=n,this._endIndex=r-1,this._triangulation(e,n,r);var a=this._getShader(e.get("shading"));this._prepareMesh(e,a,i,n,r),this.rootNode.updateWorldTransform(),this._updateRegionMesh(e,i,n,r);var s=e.coordinateSystem;"geo3D"===s.type&&this._updateGroundPlane(e,s,i);var l=this;this._labelsBuilder.updateData(o,n,r),this._labelsBuilder.getLabelPosition=function(e,t,i){var n=o.getName(e),r=i;if("geo3D"===s.type){var a=s.getRegion(n);return a?(d=a.getCenter(),s.dataToPoint([d[0],d[1],r])):[NaN,NaN,NaN]}var h=l._triangulationResults[e-l._startIndex],d=l.extrudeY?[(h.max[0]+h.min[0])/2,h.max[1]+r,(h.max[2]+h.min[2])/2]:[(h.max[0]+h.min[0])/2,(h.max[1]+h.min[1])/2,h.max[2]+r]},this._data=o,this._labelsBuilder.updateLabels(),this._updateDebugWireframe(e),this._lastHoverDataIndex=0},_initMeshes:function(){var e=this;var t,i=(t=new Pt.Mesh({name:"Polygon",material:new Pt.Material({shader:e._shadersMap.lambert}),geometry:new Pt.Geometry({sortTriangles:!0,dynamic:!0}),culling:!1,ignorePicking:!0,renderNormal:!0}),Object.assign(t.geometry,Gn),t),n=new Pt.Mesh({material:new Pt.Material({shader:this._linesShader}),castShadow:!1,ignorePicking:!0,$ignorePicking:!0,geometry:new ii({useNativeLine:!1})});this.rootNode.add(i),this.rootNode.add(n),i.material.define("both","VERTEX_COLOR"),i.material.define("fragment","DOUBLE_SIDED"),this._polygonMesh=i,this._linesMesh=n,this.rootNode.add(this._groundMesh)},_getShader:function(e){var t=this._shadersMap[e];return t||(t=this._shadersMap.lambert),t.__shading=e,t},_prepareMesh:function(e,t,i,n,r){for(var o=0,a=0,s=0,l=0,h=n;h<r;h++){var d=this._getRegionPolygonInfo(h),c=this._getRegionLinesInfo(h,e,this._linesMesh.geometry);o+=d.vertexCount,a+=d.triangleCount,s+=c.vertexCount,l+=c.triangleCount}var u=this._polygonMesh,f=u.geometry;["position","normal","texcoord0","color"].forEach((function(e){f.attributes[e].init(o)})),f.indices=o>65535?new Uint32Array(3*a):new Uint16Array(3*a),u.material.shader!==t&&u.material.attachShader(t,!0),Pt.setMaterialFromModel(t.__shading,u.material,e,i),s>0&&(this._linesMesh.geometry.resetOffset(),this._linesMesh.geometry.setVertexCount(s),this._linesMesh.geometry.setTriangleCount(l)),this._dataIndexOfVertex=new Uint32Array(o),this._vertexRangeOfDataIndex=new Uint32Array(2*(r-n))},_updateRegionMesh:function(e,t,i,n){for(var r=e.getData(),o=0,a=0,s=!1,l=this._polygonMesh,h=this._linesMesh,d=i;d<n;d++){var c=e.getRegionModel(d),u=c.getModel("itemStyle"),f=St(Hn(r,d),u.get("color"),"#fff"),p=St(Un(r,d),u.get("opacity"),1),m=Pt.parseColor(f),g=Pt.parseColor(u.get("borderColor"));m[3]*=p,g[3]*=p;var _=m[3]<.99;l.material.set("color",[1,1,1,1]),s=s||_;for(var v=St(c.get("height",!0),e.get("regionHeight")),x=this._updatePolygonGeometry(e,l.geometry,d,v,o,a,m),y=o;y<x.vertexOffset;y++)this._dataIndexOfVertex[y]=d;this._vertexRangeOfDataIndex[2*(d-i)]=o,this._vertexRangeOfDataIndex[2*(d-i)+1]=x.vertexOffset,o=x.vertexOffset,a=x.triangleOffset;var b=u.get("borderWidth"),w=b>0;w&&(b*=t.getDevicePixelRatio(),this._updateLinesGeometry(h.geometry,e,d,v,b,e.coordinateSystem.transform)),h.invisible=!w,h.material.set({color:g})}(l=this._polygonMesh).material.transparent=s,l.material.depthMask=!s,l.geometry.updateBoundingBox(),l.frontFace=this.extrudeY?Pt.Mesh.CCW:Pt.Mesh.CW,l.material.get("normalMap")&&l.geometry.generateTangents(),l.seriesIndex=e.seriesIndex,l.on("mousemove",this._onmousemove,this),l.on("mouseout",this._onmouseout,this)},_updateDebugWireframe:function(e){var t=e.getModel("debug.wireframe");if(t.get("show")){var i=Pt.parseColor(t.get("lineStyle.color")||"rgba(0,0,0,0.5)"),n=St(t.get("lineStyle.width"),1),r=this._polygonMesh;r.geometry.generateBarycentric(),r.material.define("both","WIREFRAME_TRIANGLE"),r.material.set("wireframeLineColor",i),r.material.set("wireframeLineWidth",n)}},_onmousemove:function(e){var t=this._dataIndexOfVertex[e.triangle[0]];null==t&&(t=-1),t!==this._lastHoverDataIndex&&(this.downplay(this._lastHoverDataIndex),this.highlight(t),this._labelsBuilder.updateLabels([t])),this._lastHoverDataIndex=t,this._polygonMesh.dataIndex=t},_onmouseout:function(e){e.target&&(this.downplay(this._lastHoverDataIndex),this._lastHoverDataIndex=-1,this._polygonMesh.dataIndex=-1),this._labelsBuilder.updateLabels([])},_updateGroundPlane:function(e,t,i){var n=e.getModel("groundPlane",e);if(this._groundMesh.invisible=!n.get("show",!0),!this._groundMesh.invisible){var r=e.get("shading"),o=this._groundMaterials[r];o||(o=this._groundMaterials.lambert),Pt.setMaterialFromModel(r,o,n,i),o.get("normalMap")&&this._groundMesh.geometry.generateTangents(),this._groundMesh.material=o,this._groundMesh.material.set("color",Pt.parseColor(n.get("color"))),this._groundMesh.scale.set(t.size[0],t.size[2],1)}},_triangulation:function(e,t,i){this._triangulationResults=[];for(var n=[Infinity,Infinity,Infinity],r=[-Infinity,-Infinity,-Infinity],o=e.coordinateSystem,a=t;a<i;a++){for(var s=[],l=e.getRegionPolygonCoords(a),h=0;h<l.length;h++){var d=l[h].exterior,c=l[h].interiors,u=[],f=[];if(!(d.length<3)){for(var p=0,m=0;m<d.length;m++){var g=d[m];u[p++]=g[0],u[p++]=g[1]}for(m=0;m<c.length;m++)if(!(c[m].length<3)){for(var _=u.length/2,v=0;v<c[m].length;v++){g=c[m][v];u.push(g[0]),u.push(g[1])}f.push(_)}var x=ln(u,f),y=new Float64Array(u.length/2*3),b=[],w=[Infinity,Infinity,Infinity],T=[-Infinity,-Infinity,-Infinity],S=0;for(m=0;m<u.length;)kn.set(b,u[m++],0,u[m++]),o&&o.transform&&kn.transformMat4(b,b,o.transform),kn.min(w,w,b),kn.max(T,T,b),y[S++]=b[0],y[S++]=b[1],y[S++]=b[2];kn.min(n,n,w),kn.max(r,r,T),s.push({points:y,indices:x,min:w,max:T})}}this._triangulationResults.push(s)}this._geoBoundingBox=[n,r]},_getRegionPolygonInfo:function(e){for(var t=this._triangulationResults[e-this._startIndex],i=0,n=0,r=0;r<t.length;r++)i+=t[r].points.length/3,n+=t[r].indices.length/3;return{vertexCount:2*i+4*i,triangleCount:2*n+2*i}},_updatePolygonGeometry:function(e,t,i,n,r,o,a){var s=e.get("projectUVOnGround"),l=t.attributes.position,h=t.attributes.normal,d=t.attributes.texcoord0,c=t.attributes.color,u=this._triangulationResults[i-this._startIndex],f=c.value&&a,p=t.indices,m=this.extrudeY?1:2,g=this.extrudeY?2:1,_=[this.rootNode.worldTransform.x.len(),this.rootNode.worldTransform.y.len(),this.rootNode.worldTransform.z.len()],v=kn.mul([],this._geoBoundingBox[0],_),x=kn.mul([],this._geoBoundingBox[1],_),y=Math.max(x[0]-v[0],x[2]-v[2]);function b(e,t,i){var n=r;!function(e,t){for(var i=e.points,n=i.length,o=[],s=[],h=0;h<n;h+=3)o[0]=i[h],o[m]=t,o[g]=i[h+2],s[0]=(i[h]*_[0]-v[0])/y,s[1]=(i[h+2]*_[g]-v[2])/y,l.set(r,o),f&&c.set(r,a),d.set(r++,s)}(e,t);for(var s=e.indices.length,h=0;h<s;h++)p[3*o+h]=e.indices[h]+n;o+=e.indices.length/3}for(var w=this.extrudeY?[0,1,0]:[0,0,1],T=kn.negate([],w),S=0;S<u.length;S++){var M=r,D=u[S];b(D,0),b(D,n);for(var L=D.points.length/3,C=0;C<L;C++)h.set(M+C,T),h.set(M+C+L,w);var A=[0,3,1,1,3,2],P=[[],[],[],[]],E=[],N=[],O=[],I=[],R=0;for(C=0;C<L;C++){for(var z=(C+1)%L,F=(D.points[3*z]-D.points[3*C])*_[0],B=(D.points[3*z+2]-D.points[3*C+2])*_[g],G=Math.sqrt(F*F+B*B),H=0;H<4;H++){var U=0===H||3===H,V=3*(U?C:z);P[H][0]=D.points[V],P[H][m]=H>1?n:0,P[H][g]=D.points[V+2],l.set(r+H,P[H]),s?(I[0]=(D.points[V]*_[0]-v[0])/y,I[1]=(D.points[V+2]*_[g]-v[g])/y):(I[0]=(U?R:R+G)/y,I[1]=(P[H][m]*_[m]-v[m])/y),d.set(r+H,I)}kn.sub(E,P[1],P[0]),kn.sub(N,P[3],P[0]),kn.cross(O,E,N),kn.normalize(O,O);for(H=0;H<4;H++)h.set(r+H,O),f&&c.set(r+H,a);for(H=0;H<6;H++)p[3*o+H]=A[H]+r;r+=4,o+=2,R+=G}}return t.dirty(),{vertexOffset:r,triangleOffset:o}},_getRegionLinesInfo:function(e,t,i){var n=0,r=0;t.getRegionModel(e).getModel("itemStyle").get("borderWidth")>0&&t.getRegionPolygonCoords(e).forEach((function(e){var t=e.exterior,o=e.interiors;n+=i.getPolylineVertexCount(t),r+=i.getPolylineTriangleCount(t);for(var a=0;a<o.length;a++)n+=i.getPolylineVertexCount(o[a]),r+=i.getPolylineTriangleCount(o[a])}),this);return{vertexCount:n,triangleCount:r}},_updateLinesGeometry:function(e,t,i,n,r,o){function a(e){for(var t=new Float64Array(3*e.length),i=0,r=[],a=0;a<e.length;a++)r[0]=e[a][0],r[1]=n+.1,r[2]=e[a][1],o&&kn.transformMat4(r,r,o),t[i++]=r[0],t[i++]=r[1],t[i++]=r[2];return t}var s=[1,1,1,1];t.getRegionPolygonCoords(i).forEach((function(t){var i=t.exterior,n=t.interiors;e.addPolyline(a(i),s,r);for(var o=0;o<n.length;o++)e.addPolyline(a(n[o]),s,r)}))},highlight:function(e){var t=this._data;if(t){var i=t.getItemModel(e).getModel(["emphasis","itemStyle"]),n=i.get("color"),r=St(i.get("opacity"),Un(t,e),1);if(null==n){var o=Hn(t,e);n=it(o,-.4)}null==r&&(r=Un(t,e));var a=Pt.parseColor(n);a[3]*=r,this._setColorOfDataIndex(t,e,a)}},downplay:function(e){var t=this._data;if(t){var i=t.getItemModel(e),n=St(Hn(t,e),i.get(["itemStyle","color"]),"#fff"),r=St(Un(t,e),i.get(["itemStyle","opacity"]),1),o=Pt.parseColor(n);o[3]*=r,this._setColorOfDataIndex(t,e,o)}},dispose:function(){this._labelsBuilder.dispose()},_setColorOfDataIndex:function(e,t,i){if(!(t<this._startIndex&&t>this._endIndex)){t-=this._startIndex;for(var n=this._vertexRangeOfDataIndex[2*t];n<this._vertexRangeOfDataIndex[2*t+1];n++)this._polygonMesh.geometry.attributes.color.set(n,i);this._polygonMesh.geometry.dirty(),this._api.getZr().refresh()}}};const jn=s.extend({type:"geo3D",__ecgl__:!0,init:function(e,t){this._geo3DBuilder=new Wn(t),this.groupGL=new Pt.Node,this._lightRoot=new Pt.Node,this._sceneHelper=new oi(this._lightRoot),this._sceneHelper.initLight(this._lightRoot),this._control=new Jt({zr:t.getZr()}),this._control.init()},render:function(e,t,i){this.groupGL.add(this._geo3DBuilder.rootNode);var n=e.coordinateSystem;if(n&&n.viewGL){n.viewGL.add(this._lightRoot),e.get("show")?n.viewGL.add(this.groupGL):n.viewGL.remove(this.groupGL);var r=this._control;r.setViewGL(n.viewGL);var o=e.getModel("viewControl");r.setFromViewControlModel(o,0),this._sceneHelper.setScene(n.viewGL.scene),this._sceneHelper.updateLight(e),n.viewGL.setPostEffect(e.getModel("postEffect"),i),n.viewGL.setTemporalSuperSampling(e.getModel("temporalSuperSampling")),this._geo3DBuilder.update(e,t,i,0,e.getData().count());var a=n.viewGL.isLinearSpace()?"define":"undefine";this._geo3DBuilder.rootNode.traverse((function(e){e.material&&e.material[a]("fragment","SRGB_DECODE")})),r.off("update"),r.on("update",(function(){i.dispatchAction({type:"geo3DChangeCamera",alpha:r.getAlpha(),beta:r.getBeta(),distance:r.getDistance(),center:r.getCenter(),from:this.uid,geo3DId:e.id})})),r.update()}},afterRender:function(e,t,i,n){var r=n.renderer;this._sceneHelper.updateAmbientCubemap(r,e,i),this._sceneHelper.updateSkybox(r,e,i)},dispose:function(){this._control.dispose(),this._geo3DBuilder.dispose()}});var Zn=we.vec3,Xn=we.mat4,Yn=[y,b];function qn(e,t,i,n,r){this.name=e,this.map=t,this.regionHeight=0,this.regions=[],this._nameCoordMap={},this.loadGeoJson(i,n,r),this.transform=Xn.identity(new Float64Array(16)),this.invTransform=Xn.identity(new Float64Array(16)),this.extrudeY=!0,this.altitudeAxis}function Qn(e,t){var i=e.getBoxLayoutParams(),n=c(i,{width:t.getWidth(),height:t.getHeight()});n.y=t.getHeight()-n.y-n.height,this.viewGL.setViewport(n.x,n.y,n.width,n.height,t.getDevicePixelRatio());var r=this.getGeoBoundingRect(),o=r.width/r.height*(e.get("aspectScale")||.75),a=e.get("boxWidth"),s=e.get("boxDepth"),l=e.get("boxHeight");null==l&&(l=5),isNaN(a)&&isNaN(s)&&(a=100),isNaN(s)?s=a/o:isNaN(a)&&(a=s/o),this.setSize(a,l,s),this.regionHeight=e.get("regionHeight"),this.altitudeAxis&&this.altitudeAxis.setExtent(0,Math.max(l-this.regionHeight,0))}function Kn(e,t){var i=[Infinity,-Infinity];if(e.eachSeries((function(e){if(e.coordinateSystem===this&&"series.map3D"!==e.type){var t=e.getData(),n=e.coordDimToDataDim("alt"),r=n&&n[0];if(r){var o=t.getDataExtent(r,!0);i[0]=Math.min(i[0],o[0]),i[1]=Math.max(i[1],o[1])}}}),this),i&&isFinite(i[1]-i[0])){var n=u(i,{type:"value",min:"dataMin",max:"dataMax"});this.altitudeAxis=new d("altitude",n),this.resize(this.model,t)}}qn.prototype={constructor:qn,type:"geo3D",dimensions:["lng","lat","alt"],containPoint:function(){},loadGeoJson:function(e,t,i){var n=x||x;try{this.regions=e?n(e):[]}catch(h){throw"Invalid geoJson format\n"+h}t=t||{},i=i||{};for(var r=this.regions,o={},a=0;a<r.length;a++){var s=r[a].name;s=i[s]||s,r[a].name=s,o[s]=r[a],this.addGeoCoord(s,r[a].getCenter());var l=t[s];l&&r[a].transformTo(l.left,l.top,l.width,l.height)}this._regionsMap=o,this._geoRect=null,Yn.forEach((function(e){e(this)}),this)},getGeoBoundingRect:function(){if(this._geoRect)return this._geoRect;for(var e,t=this.regions,i=0;i<t.length;i++){var n=t[i].getBoundingRect();(e=e||n.clone()).union(n)}return this._geoRect=e||new nt(0,0,0,0)},addGeoCoord:function(e,t){this._nameCoordMap[e]=t},getRegion:function(e){return this._regionsMap[e]},getRegionByCoord:function(e){for(var t=this.regions,i=0;i<t.length;i++)if(t[i].contain(e))return t[i]},setSize:function(e,t,i){this.size=[e,t,i];var n=this.getGeoBoundingRect(),r=e/n.width,o=-i/n.height,a=-e/2-n.x*r,s=i/2-n.y*o,l=this.extrudeY?[a,0,s]:[a,s,0],h=this.extrudeY?[r,1,o]:[r,o,1],d=this.transform;Xn.identity(d),Xn.translate(d,d,l),Xn.scale(d,d,h),Xn.invert(this.invTransform,d)},dataToPoint:function(e,t){t=t||[];var i=this.extrudeY?1:2,n=this.extrudeY?2:1,r=e[2];return isNaN(r)&&(r=0),t[0]=e[0],t[n]=e[1],this.altitudeAxis?t[i]=this.altitudeAxis.dataToCoord(r):t[i]=0,t[i]+=this.regionHeight,Zn.transformMat4(t,t,this.transform),t},pointToData:function(e,t){}};var Jn=0,$n={dimensions:qn.prototype.dimensions,create:function(e,t){var i=[];if(!g)throw new Error("geo3D component depends on geo component");function n(e,n){var r=$n.createGeo3D(e);e.__viewGL=e.__viewGL||new Wi,r.viewGL=e.__viewGL,e.coordinateSystem=r,r.model=e,i.push(r),r.resize=Qn,r.resize(e,t),r.update=Kn}return e.eachComponent("geo3D",(function(e,t){n(e)})),e.eachSeriesByType("map3D",(function(e,t){var i=e.get("coordinateSystem");null==i&&(i="geo3D"),"geo3D"===i&&n(e)})),e.eachSeries((function(t){if("geo3D"===t.get("coordinateSystem")){if("series.map3D"===t.type)return;var i=t.getReferringComponents("geo3D").models[0];if(i||(i=e.getComponent("geo3D")),!i)throw new Error('geo "'+St(t.get("geo3DIndex"),t.get("geo3DId"),0)+'" not found');t.coordinateSystem=i.coordinateSystem}})),i},createGeo3D:function(e){var t,i=e.get("map");return"string"==typeof i?(t=i,i=g(i)):i&&i.features&&(i={geoJson:i}),null==t&&(t="GEO_ANONYMOUS_"+Jn++),new qn(t+Jn++,t,i&&i.geoJson,i&&i.specialAreas,e.get("nameMap"))}};function er(e){e.registerComponentModel(sn),e.registerComponentView(jn),e.registerAction({type:"geo3DChangeCamera",event:"geo3dcamerachanged",update:"series:updateCamera"},(function(e,t){t.eachComponent({mainType:"geo3D",query:e},(function(t){t.setView(e)}))})),e.registerCoordinateSystem("geo3D",$n)}function tr(e,t){e.id=e.id||e.name||t+""}m(er);var ir=r.extend({type:"globe",layoutMode:"box",coordinateSystem:null,init:function(){ir.superApply(this,"init",arguments),je(this.option.layers,(function(e,t){Ye(e,this.defaultLayerOption),tr(e,t)}),this)},mergeOption:function(e){var t=this.option.layers;function i(e){return rt(e,(function(e,t,i){return tr(t,i),e[t.id]=t,e}),{})}if(this.option.layers=null,ir.superApply(this,"mergeOption",arguments),t&&t.length){var n=i(e.layers),r=i(t);for(var o in n)r[o]?Ye(r[o],n[o],!0):t.push(e.layers[o]);this.option.layers=t}je(this.option.layers,(function(e){Ye(e,this.defaultLayerOption)}),this)},optionUpdated:function(){this.updateDisplacementHash()},defaultLayerOption:{show:!0,type:"overlay"},defaultOption:{show:!0,zlevel:-10,left:0,top:0,width:"100%",height:"100%",environment:"auto",baseColor:"#fff",baseTexture:"",heightTexture:"",displacementTexture:"",displacementScale:0,displacementQuality:"medium",globeRadius:100,globeOuterRadius:150,shading:"lambert",light:{main:{time:""}},atmosphere:{show:!1,offset:5,color:"#ffffff",glowPower:6,innerGlowPower:2},viewControl:{autoRotate:!0,panSensitivity:0,targetCoord:null},layers:[]},setDisplacementData:function(e,t,i){this.displacementData=e,this.displacementWidth=t,this.displacementHeight=i},getDisplacementTexture:function(){return this.get("displacementTexture")||this.get("heightTexture")},getDisplacemenScale:function(){var e=this.getDisplacementTexture(),t=this.get("displacementScale");return e&&"none"!==e||(t=0),t},hasDisplacement:function(){return this.getDisplacemenScale()>0},_displacementChanged:!0,_displacementScale:0,updateDisplacementHash:function(){var e=this.getDisplacementTexture(),t=this.getDisplacemenScale();this._displacementChanged=this._displacementTexture!==e||this._displacementScale!==t,this._displacementTexture=e,this._displacementScale=t},isDisplacementChanged:function(){return this._displacementChanged}});Ye(ir.prototype,jt),Ye(ir.prototype,Zt),Ye(ir.prototype,Xt),Ye(ir.prototype,on);var nr=Math.PI,rr=Math.sin,or=Math.cos,ar=Math.tan,sr=Math.asin,lr=Math.atan2,hr=nr/180;var dr=23.4397*hr;function cr(e,t){return lr(rr(e)*or(dr)-ar(t)*rr(dr),or(e))}function ur(e,t){return sr(rr(t)*or(dr)+or(t)*rr(dr)*rr(e))}function fr(e,t,i){return lr(rr(e),or(e)*rr(t)-ar(i)*or(t))}function pr(e,t,i){return sr(rr(t)*rr(i)+or(t)*or(i)*or(e))}function mr(e){var t=function(e){return hr*(357.5291+.98560028*e)}(e),i=function(e){return e+hr*(1.9148*rr(e)+.02*rr(2*e)+3e-4*rr(3*e))+102.9372*hr+nr}(t);return{dec:ur(i,0),ra:cr(i,0)}}var gr={};gr.getPosition=function(e,t,i){var n=hr*-i,r=hr*t,o=function(e){return function(e){return e.valueOf()/864e5-.5+2440588}(e)-2451545}(e),a=mr(o),s=function(e,t){return hr*(280.16+360.9856235*e)-t}(o,n)-a.ra;return{azimuth:fr(s,r,a.dec),altitude:pr(s,r,a.dec)}};Pt.Shader.import(V),Pt.Shader.import("@export ecgl.atmosphere.vertex\nattribute vec3 position: POSITION;\nattribute vec3 normal : NORMAL;\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform mat4 normalMatrix : WORLDINVERSETRANSPOSE;\n\nvarying vec3 v_Normal;\n\nvoid main() {\n v_Normal = normalize((normalMatrix * vec4(normal, 0.0)).xyz);\n gl_Position = worldViewProjection * vec4(position, 1.0);\n}\n@end\n\n\n@export ecgl.atmosphere.fragment\nuniform mat4 viewTranspose: VIEWTRANSPOSE;\nuniform float glowPower;\nuniform vec3 glowColor;\n\nvarying vec3 v_Normal;\n\nvoid main() {\n float intensity = pow(1.0 - dot(v_Normal, (viewTranspose * vec4(0.0, 0.0, 1.0, 0.0)).xyz), glowPower);\n gl_FragColor = vec4(glowColor, intensity * intensity);\n}\n@end");const _r=s.extend({type:"globe",__ecgl__:!0,_displacementScale:0,init:function(e,t){this.groupGL=new Pt.Node,this._sphereGeometry=new Pt.SphereGeometry({widthSegments:200,heightSegments:100,dynamic:!0}),this._overlayGeometry=new Pt.SphereGeometry({widthSegments:80,heightSegments:40}),this._planeGeometry=new Pt.PlaneGeometry,this._earthMesh=new Pt.Mesh({renderNormal:!0}),this._atmosphereMesh=new Pt.Mesh,this._atmosphereGeometry=new Pt.SphereGeometry({widthSegments:80,heightSegments:40}),this._atmosphereMaterial=new Pt.Material({shader:new Pt.Shader(Pt.Shader.source("ecgl.atmosphere.vertex"),Pt.Shader.source("ecgl.atmosphere.fragment")),transparent:!0}),this._atmosphereMesh.geometry=this._atmosphereGeometry,this._atmosphereMesh.material=this._atmosphereMaterial,this._atmosphereMesh.frontFace=Pt.Mesh.CW,this._lightRoot=new Pt.Node,this._sceneHelper=new oi,this._sceneHelper.initLight(this._lightRoot),this.groupGL.add(this._atmosphereMesh),this.groupGL.add(this._earthMesh),this._control=new Jt({zr:t.getZr()}),this._control.init(),this._layerMeshes={}},render:function(e,t,i){var n=e.coordinateSystem,r=e.get("shading");n.viewGL.add(this._lightRoot),e.get("show")?n.viewGL.add(this.groupGL):n.viewGL.remove(this.groupGL),this._sceneHelper.setScene(n.viewGL.scene),n.viewGL.setPostEffect(e.getModel("postEffect"),i),n.viewGL.setTemporalSuperSampling(e.getModel("temporalSuperSampling"));var o=this._earthMesh;o.geometry=this._sphereGeometry;var a="ecgl."+r;o.material&&o.material.shader.name===a||(o.material=Pt.createMaterial(a)),Pt.setMaterialFromModel(r,o.material,e,i),["roughnessMap","metalnessMap","detailMap","normalMap"].forEach((function(e){var t=o.material.get(e);t&&(t.flipY=!1)})),o.material.set("color",Pt.parseColor(e.get("baseColor")));var s=.99*n.radius;if(o.scale.set(s,s,s),e.get("atmosphere.show")){o.material.define("both","ATMOSPHERE_ENABLED"),this._atmosphereMesh.invisible=!1,this._atmosphereMaterial.setUniforms({glowPower:e.get("atmosphere.glowPower")||6,glowColor:e.get("atmosphere.color")||"#ffffff"}),o.material.setUniforms({glowPower:e.get("atmosphere.innerGlowPower")||2,glowColor:e.get("atmosphere.color")||"#ffffff"});var l=e.get("atmosphere.offset")||5;this._atmosphereMesh.scale.set(s+l,s+l,s+l)}else o.material.undefine("both","ATMOSPHERE_ENABLED"),this._atmosphereMesh.invisible=!0;var h=o.material.setTextureImage("diffuseMap",e.get("baseTexture"),i,{flipY:!1,anisotropic:8});h&&h.surface&&h.surface.attachToMesh(o);var d=o.material.setTextureImage("bumpMap",e.get("heightTexture"),i,{flipY:!1,anisotropic:8});d&&d.surface&&d.surface.attachToMesh(o),o.material[e.get("postEffect.enable")?"define":"undefine"]("fragment","SRGB_DECODE"),this._updateLight(e,i),this._displaceVertices(e,i),this._updateViewControl(e,i),this._updateLayers(e,i)},afterRender:function(e,t,i,n){var r=n.renderer;this._sceneHelper.updateAmbientCubemap(r,e,i),this._sceneHelper.updateSkybox(r,e,i)},_updateLayers:function(e,t){var i=e.coordinateSystem,n=e.get("layers"),r=i.radius,a=[],s=[],l=[],h=[];je(n,(function(e){var n=new o(e),d=n.get("type"),c=Pt.loadTexture(n.get("texture"),t,{flipY:!1,anisotropic:8});if(c.surface&&c.surface.attachToMesh(this._earthMesh),"blend"===d){var u=n.get("blendTo"),f=St(n.get("intensity"),1);"emission"===u?(l.push(c),h.push(f)):(a.push(c),s.push(f))}else{var p=n.get("id"),m=this._layerMeshes[p];m||(m=this._layerMeshes[p]=new Pt.Mesh({geometry:this._overlayGeometry,castShadow:!1,ignorePicking:!0})),"lambert"===n.get("shading")?(m.material=m.__lambertMaterial||new Pt.Material({autoUpdateTextureStatus:!1,shader:Pt.createShader("ecgl.lambert"),transparent:!0,depthMask:!1}),m.__lambertMaterial=m.material):(m.material=m.__colorMaterial||new Pt.Material({autoUpdateTextureStatus:!1,shader:Pt.createShader("ecgl.color"),transparent:!0,depthMask:!1}),m.__colorMaterial=m.material),m.material.enableTexture("diffuseMap");var g=n.get("distance"),_=r+(null==g?i.radius/100:g);m.scale.set(_,_,_),r=_;var v=this._blankTexture||(this._blankTexture=Pt.createBlankTexture("rgba(255, 255, 255, 0)"));m.material.set("diffuseMap",v),Pt.loadTexture(n.get("texture"),t,{flipY:!1,anisotropic:8},(function(e){e.surface&&e.surface.attachToMesh(m),m.material.set("diffuseMap",e),t.getZr().refresh()})),n.get("show")?this.groupGL.add(m):this.groupGL.remove(m)}}),this);var d=this._earthMesh.material;d.define("fragment","LAYER_DIFFUSEMAP_COUNT",a.length),d.define("fragment","LAYER_EMISSIVEMAP_COUNT",l.length),d.set("layerDiffuseMap",a),d.set("layerDiffuseIntensity",s),d.set("layerEmissiveMap",l),d.set("layerEmissionIntensity",h);var c=e.getModel("debug.wireframe");if(c.get("show")){d.define("both","WIREFRAME_TRIANGLE");var u=Pt.parseColor(c.get("lineStyle.color")||"rgba(0,0,0,0.5)"),f=St(c.get("lineStyle.width"),1);d.set("wireframeLineWidth",f),d.set("wireframeLineColor",u)}else d.undefine("both","WIREFRAME_TRIANGLE")},_updateViewControl:function(e,t){var i=e.coordinateSystem,n=e.getModel("viewControl");i.viewGL.camera;var r=this;var o=this._control;o.setViewGL(i.viewGL);var a,s,l=n.get("targetCoord");null!=l&&(s=l[0]+90,a=l[1]),o.setFromViewControlModel(n,{baseDistance:i.radius,alpha:a,beta:s}),o.off("update"),o.on("update",(function(){t.dispatchAction({type:"globeChangeCamera",alpha:o.getAlpha(),beta:o.getBeta(),distance:o.getDistance()-i.radius,center:o.getCenter(),from:r.uid,globeId:e.id})}))},_displaceVertices:function(e,t){var i=e.get("displacementQuality"),n=e.get("debug.wireframe.show"),r=e.coordinateSystem;if(e.isDisplacementChanged()||i!==this._displacementQuality||n!==this._showDebugWireframe){this._displacementQuality=i,this._showDebugWireframe=n;var o=this._sphereGeometry,a={low:100,medium:200,high:400,ultra:800}[i]||200,s=a/2;(o.widthSegments!==a||n)&&(o.widthSegments=a,o.heightSegments=s,o.build()),this._doDisplaceVertices(o,r),n&&o.generateBarycentric()}},_doDisplaceVertices:function(e,t){var i=e.attributes.position.value,n=e.attributes.texcoord0.value,r=e.__originalPosition;r&&r.length===i.length||((r=new Float32Array(i.length)).set(i),e.__originalPosition=r);for(var o=t.displacementWidth,a=t.displacementHeight,s=t.displacementData,l=0;l<e.vertexCount;l++){var h=3*l,d=2*l,c=r[h+1],u=r[h+2],f=r[h+3],p=n[d++],m=n[d++],g=Math.round(p*(o-1)),_=Math.round(m*(a-1)),v=s?s[_*o+g]:0;i[h+1]=c+c*v,i[h+2]=u+u*v,i[h+3]=f+f*v}e.generateVertexNormals(),e.dirty(),e.updateBoundingBox()},_updateLight:function(e,t){var i=this._earthMesh;this._sceneHelper.updateLight(e);var n=this._sceneHelper.mainLight,r=e.get("light.main.time")||new Date,o=gr.getPosition(w(r),0,0),a=Math.cos(o.altitude);n.position.y=-a*Math.cos(o.azimuth),n.position.x=Math.sin(o.altitude),n.position.z=a*Math.sin(o.azimuth),n.lookAt(i.getWorldPosition())},dispose:function(e,t){this.groupGL.removeAll(),this._control.dispose()}});var vr=we.vec3;function xr(e){this.radius=e,this.viewGL=null,this.altitudeAxis,this.displacementData=null,this.displacementWidth,this.displacementHeight}function yr(e,t){var i=e.getBoxLayoutParams(),n=c(i,{width:t.getWidth(),height:t.getHeight()});n.y=t.getHeight()-n.y-n.height,this.viewGL.setViewport(n.x,n.y,n.width,n.height,t.getDevicePixelRatio()),this.radius=e.get("globeRadius");var r=e.get("globeOuterRadius");this.altitudeAxis&&this.altitudeAxis.setExtent(0,r-this.radius)}function br(e,t){var i=[Infinity,-Infinity];if(e.eachSeries((function(e){if(e.coordinateSystem===this){var t=e.getData(),n=e.coordDimToDataDim("alt"),r=n&&n[0];if(r){var o=t.getDataExtent(r,!0);i[0]=Math.min(i[0],o[0]),i[1]=Math.max(i[1],o[1])}}}),this),i&&isFinite(i[1]-i[0])){var n=u(i,{type:"value",min:"dataMin",max:"dataMax"});this.altitudeAxis=new d("altitude",n),this.resize(this.model,t)}}xr.prototype={constructor:xr,dimensions:["lng","lat","alt"],type:"globe",containPoint:function(){},setDisplacementData:function(e,t,i){this.displacementData=e,this.displacementWidth=t,this.displacementHeight=i},_getDisplacementScale:function(e,t){var i=(e+180)/360*(this.displacementWidth-1),n=(90-t)/180*(this.displacementHeight-1),r=Math.round(i)+Math.round(n)*this.displacementWidth;return this.displacementData[r]},dataToPoint:function(e,t){var i=e[0],n=e[1],r=e[2]||0,o=this.radius;this.displacementData&&(o*=1+this._getDisplacementScale(i,n)),this.altitudeAxis&&(o+=this.altitudeAxis.dataToCoord(r)),i=i*Math.PI/180,n=n*Math.PI/180;var a=Math.cos(n)*o;return(t=t||[])[0]=-a*Math.cos(i+Math.PI),t[1]=Math.sin(n)*o,t[2]=a*Math.sin(i+Math.PI),t},pointToData:function(e,t){var i=e[0],n=e[1],r=e[2],o=vr.len(e);i/=o,n/=o,r/=o;var a=Math.asin(n),s=Math.atan2(r,-i);s<0&&(s=2*Math.PI+s);var l=180*a/Math.PI,h=180*s/Math.PI-180;return(t=t||[])[0]=h,t[1]=l,t[2]=o-this.radius,this.altitudeAxis&&(t[2]=this.altitudeAxis.coordToData(t[2])),t}};var wr={dimensions:xr.prototype.dimensions,create:function(e,t){var i=[];return e.eachComponent("globe",(function(e){e.__viewGL=e.__viewGL||new Wi;var n=new xr;n.viewGL=e.__viewGL,e.coordinateSystem=n,n.model=e,i.push(n),n.resize=yr,n.resize(e,t),n.update=br})),e.eachSeries((function(t){if("globe"===t.get("coordinateSystem")){var i=t.getReferringComponents("globe").models[0];if(i||(i=e.getComponent("globe")),!i)throw new Error('globe "'+St(t.get("globe3DIndex"),t.get("globe3DId"),0)+'" not found');var n=i.coordinateSystem;t.coordinateSystem=n}})),e.eachComponent("globe",(function(e,i){var n=e.coordinateSystem,r=e.getDisplacementTexture(),o=e.getDisplacemenScale();if(e.isDisplacementChanged()){if(e.hasDisplacement()){var a=!0;Pt.loadTexture(r,t,(function(i){var n=function(e,t){var i=document.createElement("canvas"),n=i.getContext("2d"),r=e.width,o=e.height;i.width=r,i.height=o,n.drawImage(e,0,0,r,o);for(var a=n.getImageData(0,0,r,o).data,s=new Float32Array(a.length/4),l=0;l<a.length/4;l++){var h=a[4*l];s[l]=h/255*t}return{data:s,width:r,height:o}}(i.image,o);e.setDisplacementData(n.data,n.width,n.height),a||t.dispatchAction({type:"globeUpdateDisplacment"})})),a=!1}else n.setDisplacementData(null,0,0);n.setDisplacementData(e.displacementData,e.displacementWidth,e.displacementHeight)}})),i}};m((function(e){e.registerComponentModel(ir),e.registerComponentView(_r),e.registerCoordinateSystem("globe",wr),e.registerAction({type:"globeChangeCamera",event:"globecamerachanged",update:"series:updateCamera"},(function(e,t){t.eachComponent({mainType:"globe",query:e},(function(t){t.setView(e)}))})),e.registerAction({type:"globeUpdateDisplacment",event:"globedisplacementupdated",update:"update"},(function(e,t){}))}));var Tr=["zoom","center","pitch","bearing"],Sr=r.extend({type:"mapbox3D",layoutMode:"box",coordinateSystem:null,defaultOption:{zlevel:-10,style:"mapbox://styles/mapbox/light-v9",center:[0,0],zoom:0,pitch:0,bearing:0,light:{main:{alpha:20,beta:30}},altitudeScale:1,boxHeight:"auto"},getMapboxCameraOption:function(){var e=this;return Tr.reduce((function(t,i){return t[i]=e.get(i),t}),{})},setMapboxCameraOption:function(e){null!=e&&Tr.forEach((function(t){null!=e[t]&&(this.option[t]=e[t])}),this)},getMapbox:function(){return this._mapbox},setMapbox:function(e){this._mapbox=e}});function Mr(e,t){if(this.id=e,this.zr=t,this.dom=document.createElement("div"),this.dom.style.cssText="position:absolute;left:0;right:0;top:0;bottom:0;",!mapboxgl)throw new Error("Mapbox GL library must be included. See https://www.mapbox.com/mapbox-gl-js/api/");this._mapbox=new mapboxgl.Map({container:this.dom}),this._initEvents()}Ye(Sr.prototype,Zt),Ye(Sr.prototype,Xt),Mr.prototype.setUnpainted=function(){},Mr.prototype.resize=function(){this._mapbox.resize()},Mr.prototype.getMapbox=function(){return this._mapbox},Mr.prototype.clear=function(){},Mr.prototype.refresh=function(){this._mapbox.resize()};var Dr=["mousedown","mouseup","click","dblclick","mousemove","mousewheel","wheel","touchstart","touchend","touchmove","touchcancel"];Mr.prototype._initEvents=function(){var e=this._mapbox.getCanvasContainer();this._handlers=this._handlers||{contextmenu:function(e){return e.preventDefault(),!1}},Dr.forEach((function(t){this._handlers[t]=function(t){var i={};for(var n in t)i[n]=t[n];i.bubbles=!1;var r=new t.constructor(t.type,i);e.dispatchEvent(r)},this.zr.dom.addEventListener(t,this._handlers[t])}),this),this.zr.dom.addEventListener("contextmenu",this._handlers.contextmenu)},Mr.prototype.dispose=function(){Dr.forEach((function(e){this.zr.dom.removeEventListener(e,this._handlers[e])}),this)};const Lr="\n@export ecgl.displayShadow.vertex\n\n@import ecgl.common.transformUniforms\n\n@import ecgl.common.uv.header\n\n@import ecgl.common.attributes\n\nvarying vec3 v_WorldPosition;\n\nvarying vec3 v_Normal;\n\nvoid main()\n{\n @import ecgl.common.uv.main\n v_Normal = normalize((worldInverseTranspose * vec4(normal, 0.0)).xyz);\n\n v_WorldPosition = (world * vec4(position, 1.0)).xyz;\n gl_Position = worldViewProjection * vec4(position, 1.0);\n}\n\n@end\n\n\n@export ecgl.displayShadow.fragment\n\n@import ecgl.common.uv.fragmentHeader\n\nvarying vec3 v_Normal;\nvarying vec3 v_WorldPosition;\n\nuniform float roughness: 0.2;\n\n#ifdef DIRECTIONAL_LIGHT_COUNT\n@import clay.header.directional_light\n#endif\n\n@import ecgl.common.ssaoMap.header\n\n@import clay.plugin.compute_shadow_map\n\nvoid main()\n{\n float shadow = 1.0;\n\n @import ecgl.common.ssaoMap.main\n\n#if defined(DIRECTIONAL_LIGHT_COUNT) && defined(DIRECTIONAL_LIGHT_SHADOWMAP_COUNT)\n float shadowContribsDir[DIRECTIONAL_LIGHT_COUNT];\n if(shadowEnabled)\n {\n computeShadowOfDirectionalLights(v_WorldPosition, shadowContribsDir);\n }\n for (int i = 0; i < DIRECTIONAL_LIGHT_COUNT; i++) {\n shadow = min(shadow, shadowContribsDir[i] * 0.5 + 0.5);\n }\n#endif\n\n shadow *= 0.5 + ao * 0.5;\n shadow = clamp(shadow, 0.0, 1.0);\n\n gl_FragColor = vec4(vec3(0.0), 1.0 - shadow);\n}\n\n@end";Pt.Shader.import(Lr);const Cr=s.extend({type:"mapbox3D",__ecgl__:!0,init:function(e,t){var i=t.getZr();this._zrLayer=new Mr("mapbox3D",i),i.painter.insertLayer(-1e3,this._zrLayer),this._lightRoot=new Pt.Node,this._sceneHelper=new oi(this._lightRoot),this._sceneHelper.initLight(this._lightRoot);var n=this._zrLayer.getMapbox(),r=this._dispatchInteractAction.bind(this,t,n);["zoom","rotate","drag","pitch","rotate","move"].forEach((function(e){n.on(e,r)})),this._groundMesh=new Pt.Mesh({geometry:new Pt.PlaneGeometry,material:new Pt.Material({shader:new Pt.Shader({vertex:Pt.Shader.source("ecgl.displayShadow.vertex"),fragment:Pt.Shader.source("ecgl.displayShadow.fragment")}),depthMask:!1}),renderOrder:-100,culling:!1,castShadow:!1,$ignorePicking:!0,renderNormal:!0})},render:function(e,t,i){var n=this._zrLayer.getMapbox(),r=e.get("style"),o=JSON.stringify(r);o!==this._oldStyleStr&&r&&n.setStyle(r),this._oldStyleStr=o,n.setCenter(e.get("center")),n.setZoom(e.get("zoom")),n.setPitch(e.get("pitch")),n.setBearing(e.get("bearing")),e.setMapbox(n);var a=e.coordinateSystem;a.viewGL.scene.add(this._lightRoot),a.viewGL.add(this._groundMesh),this._updateGroundMesh(),this._sceneHelper.setScene(a.viewGL.scene),this._sceneHelper.updateLight(e),a.viewGL.setPostEffect(e.getModel("postEffect"),i),a.viewGL.setTemporalSuperSampling(e.getModel("temporalSuperSampling")),this._mapbox3DModel=e},afterRender:function(e,t,i,n){var r=n.renderer;this._sceneHelper.updateAmbientCubemap(r,e,i),this._sceneHelper.updateSkybox(r,e,i),e.coordinateSystem.viewGL.scene.traverse((function(e){e.material&&(e.material.define("fragment","NORMAL_UP_AXIS",2),e.material.define("fragment","NORMAL_FRONT_AXIS",1))}))},updateCamera:function(e,t,i,n){e.coordinateSystem.setCameraOption(n),this._updateGroundMesh(),i.getZr().refresh()},_dispatchInteractAction:function(e,t,i){e.dispatchAction({type:"mapbox3DChangeCamera",pitch:t.getPitch(),zoom:t.getZoom(),center:t.getCenter().toArray(),bearing:t.getBearing(),mapbox3DId:this._mapbox3DModel&&this._mapbox3DModel.id})},_updateGroundMesh:function(){if(this._mapbox3DModel){var e=this._mapbox3DModel.coordinateSystem,t=e.dataToPoint(e.center);this._groundMesh.position.set(t[0],t[1],-.001);var i=new Pt.Plane(new Pt.Vector3(0,0,1),0),n=e.viewGL.camera.castRay(new Pt.Vector2(-1,-1)),r=e.viewGL.camera.castRay(new Pt.Vector2(1,1)),o=n.intersectPlane(i),a=r.intersectPlane(i),s=o.dist(a)/e.viewGL.rootNode.scale.x;this._groundMesh.scale.set(s,s,1)}},dispose:function(e,t){this._zrLayer&&this._zrLayer.dispose(),t.getZr().painter.delLayer(-1e3)}});var Ar=we.mat4,Pr=512,Er=.6435011087932844,Nr=Math.PI,Or=.1;function Ir(){this.width=0,this.height=0,this.altitudeScale=1,this.boxHeight="auto",this.altitudeExtent,this.bearing=0,this.pitch=0,this.center=[0,0],this._origin,this.zoom=0,this._initialZoom,this.maxPitch=60,this.zoomOffset=0}function Rr(){Ir.apply(this,arguments)}function zr(e,t,i){function n(e,t){var i=t.getWidth(),n=t.getHeight(),r=t.getDevicePixelRatio();this.viewGL.setViewport(0,0,i,n,r),this.width=i,this.height=n,this.altitudeScale=e.get("altitudeScale"),this.boxHeight=e.get("boxHeight")}function r(e,t){if("auto"!==this.model.get("boxHeight")){var i=[Infinity,-Infinity];e.eachSeries((function(e){if(e.coordinateSystem===this){var t=e.getData(),n=e.coordDimToDataDim("alt")[0];if(n){var r=t.getDataExtent(n,!0);i[0]=Math.min(i[0],r[0]),i[1]=Math.max(i[1],r[1])}}}),this),i&&isFinite(i[1]-i[0])&&(this.altitudeExtent=i)}}return{dimensions:t.prototype.dimensions,create:function(o,a){var s=[];return o.eachComponent(e,(function(e){var i=e.__viewGL;i||(i=e.__viewGL=new Wi).setRootNode(new Pt.Node);var o=new t;o.viewGL=e.__viewGL,o.resize=n,o.resize(e,a),s.push(o),e.coordinateSystem=o,o.model=e,o.update=r})),o.eachSeries((function(t){if(t.get("coordinateSystem")===e){var i=t.getReferringComponents(e).models[0];if(i||(i=o.getComponent(e)),!i)throw new Error(e+' "'+St(t.get(e+"Index"),t.get(e+"Id"),0)+'" not found');t.coordinateSystem=i.coordinateSystem}})),i&&i(s,o,a),s}}}Ir.prototype={constructor:Ir,dimensions:["lng","lat","alt"],containPoint:function(){},setCameraOption:function(e){this.bearing=e.bearing,this.pitch=e.pitch,this.center=e.center,this.zoom=e.zoom,this._origin||(this._origin=this.projectOnTileWithScale(this.center,Pr)),null==this._initialZoom&&(this._initialZoom=this.zoom),this.updateTransform()},updateTransform:function(){if(this.height){var e=.5/Math.tan(Er/2)*this.height*Or,t=Math.max(Math.min(this.pitch,this.maxPitch),0)/180*Math.PI,i=Er/2,n=Math.PI/2+t,r=Math.sin(i)*e/Math.sin(Math.PI-n-i),o=1.1*(Math.cos(Math.PI/2-t)*r+e);this.pitch>50&&(o=1e3);var a=[];Ar.perspective(a,Er,this.width/this.height,1,o),this.viewGL.camera.projectionMatrix.setArray(a),this.viewGL.camera.decomposeProjectionMatrix();a=Ar.identity([]);var s=this.dataToPoint(this.center);Ar.scale(a,a,[1,-1,1]),Ar.translate(a,a,[0,0,-e]),Ar.rotateX(a,a,t),Ar.rotateZ(a,a,-this.bearing/180*Math.PI),Ar.translate(a,a,[-s[0]*this.getScale()*Or,-s[1]*this.getScale()*Or,0]),this.viewGL.camera.viewMatrix.array=a;var l=[];Ar.invert(l,a),this.viewGL.camera.worldTransform.array=l,this.viewGL.camera.decomposeWorldTransform();var h,d=Pr*this.getScale();if(this.altitudeExtent&&!isNaN(this.boxHeight)){var c=this.altitudeExtent[1]-this.altitudeExtent[0];h=this.boxHeight/c*this.getScale()/Math.pow(2,this._initialZoom-this.zoomOffset)}else h=d/(2*Math.PI*6378e3*Math.abs(Math.cos(this.center[1]*(Math.PI/180))))*this.altitudeScale*Or;this.viewGL.rootNode.scale.set(this.getScale()*Or,this.getScale()*Or,h)}},getScale:function(){return Math.pow(2,this.zoom-this.zoomOffset)},projectOnTile:function(e,t){return this.projectOnTileWithScale(e,this.getScale()*Pr,t)},projectOnTileWithScale:function(e,t,i){var n=e[0],r=e[1]*Nr/180,o=t*(n*Nr/180+Nr)/(2*Nr),a=t*(Nr-Math.log(Math.tan(Nr/4+.5*r)))/(2*Nr);return(i=i||[])[0]=o,i[1]=a,i},unprojectFromTile:function(e,t){return this.unprojectOnTileWithScale(e,this.getScale()*Pr,t)},unprojectOnTileWithScale:function(e,t,i){var n=e[0],r=e[1],o=n/t*(2*Nr)-Nr,a=2*(Math.atan(Math.exp(Nr-r/t*(2*Nr)))-Nr/4);return(i=i||[])[0]=180*o/Nr,i[1]=180*a/Nr,i},dataToPoint:function(e,t){return(t=this.projectOnTileWithScale(e,Pr,t))[0]-=this._origin[0],t[1]-=this._origin[1],t[2]=isNaN(e[2])?0:e[2],isNaN(e[2])||(t[2]=e[2],this.altitudeExtent&&(t[2]-=this.altitudeExtent[0])),t}},Rr.prototype=new Ir,Rr.prototype.constructor=Rr,Rr.prototype.type="mapbox3D";var Fr=zr("mapbox3D",Rr,(function(e){e.forEach((function(e){e.setCameraOption(e.model.getMapboxCameraOption())}))}));m((function(e){e.registerComponentModel(Sr),e.registerComponentView(Cr),e.registerCoordinateSystem("mapbox3D",Fr),e.registerAction({type:"mapbox3DChangeCamera",event:"mapbox3dcamerachanged",update:"mapbox3D:updateCamera"},(function(e,t){t.eachComponent({mainType:"mapbox3D",query:e},(function(t){t.setMapboxCameraOption(e)}))}))}));var Br=["zoom","center","pitch","bearing"],Gr=r.extend({type:"maptalks3D",layoutMode:"box",coordinateSystem:null,defaultOption:{zlevel:-10,urlTemplate:"http://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png",attribution:'&copy; <a href="http://osm.org">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/">CARTO</a>',center:[0,0],zoom:0,pitch:0,bearing:0,light:{main:{alpha:20,beta:30}},altitudeScale:1,boxHeight:"auto"},getMaptalksCameraOption:function(){var e=this;return Br.reduce((function(t,i){return t[i]=e.get(i),t}),{})},setMaptalksCameraOption:function(e){null!=e&&Br.forEach((function(t){null!=e[t]&&(this.option[t]=e[t])}),this)},getMaptalks:function(){return this._maptalks},setMaptalks:function(e){this._maptalks=e}});function Hr(e,t,i,n){if(this.id=e,this.zr=t,this.dom=document.createElement("div"),this.dom.style.cssText="position:absolute;left:0;right:0;top:0;bottom:0;",!maptalks)throw new Error("Maptalks library must be included. See https://maptalks.org");this._maptalks=new maptalks.Map(this.dom,{center:i,zoom:n,doubleClickZoom:!1,fog:!1}),this._initEvents()}Ye(Gr.prototype,Zt),Ye(Gr.prototype,Xt),Hr.prototype.setUnpainted=function(){},Hr.prototype.resize=function(){this._maptalks.checkSize()},Hr.prototype.getMaptalks=function(){return this._maptalks},Hr.prototype.clear=function(){},Hr.prototype.refresh=function(){this._maptalks.checkSize()};var Ur=["mousedown","mouseup","click","dblclick","mousemove","mousewheel","DOMMouseScroll","touchstart","touchend","touchmove","touchcancel"];Hr.prototype._initEvents=function(){var e=this.dom;this._handlers=this._handlers||{contextmenu:function(e){return e.preventDefault(),!1}},Ur.forEach((function(t){this._handlers[t]=function(i){var n={};for(var r in i)n[r]=i[r];n.bubbles=!1;var o=new i.constructor(i.type,n);"mousewheel"===t||"DOMMouseScroll"===t?e.dispatchEvent(o):e.firstElementChild.dispatchEvent(o)},this.zr.dom.addEventListener(t,this._handlers[t])}),this),this.zr.dom.addEventListener("contextmenu",this._handlers.contextmenu)},Hr.prototype.dispose=function(){Ur.forEach((function(e){this.zr.dom.removeEventListener(e,this._handlers[e])}),this),this._maptalks.remove()},Pt.Shader.import(Lr);const Vr=s.extend({type:"maptalks3D",__ecgl__:!0,init:function(e,t){this._groundMesh=new Pt.Mesh({geometry:new Pt.PlaneGeometry,material:new Pt.Material({shader:new Pt.Shader({vertex:Pt.Shader.source("ecgl.displayShadow.vertex"),fragment:Pt.Shader.source("ecgl.displayShadow.fragment")}),depthMask:!1}),renderOrder:-100,culling:!1,castShadow:!1,$ignorePicking:!0,renderNormal:!0})},_initMaptalksLayer:function(e,t){var i=t.getZr();this._zrLayer=new Hr("maptalks3D",i,e.get("center"),e.get("zoom")),i.painter.insertLayer(-1e3,this._zrLayer),this._lightRoot=new Pt.Node,this._sceneHelper=new oi(this._lightRoot),this._sceneHelper.initLight(this._lightRoot);var n=this._zrLayer.getMaptalks(),r=this._dispatchInteractAction.bind(this,t,n);["zoomend","zooming","zoomstart","dragrotating","pitch","pitchend","movestart","moving","moveend","resize","touchstart","touchmove","touchend","animating"].forEach((function(e){n.on(e,r)}))},render:function(e,t,i){this._zrLayer||this._initMaptalksLayer(e,i);var n=this._zrLayer.getMaptalks(),r=e.get("urlTemplate"),o=n.getBaseLayer();r!==this._oldUrlTemplate&&(o?o.setOptions({urlTemplate:r,attribution:e.get("attribution")}):(o=new maptalks.TileLayer("maptalks-echarts-gl-baselayer",{urlTemplate:r,subdomains:["a","b","c"],attribution:e.get("attribution")}),n.setBaseLayer(o))),this._oldUrlTemplate=r,n.setCenter(e.get("center")),n.setZoom(e.get("zoom"),{animation:!1}),n.setPitch(e.get("pitch")),n.setBearing(e.get("bearing")),e.setMaptalks(n);var a=e.coordinateSystem;a.viewGL.scene.add(this._lightRoot),a.viewGL.add(this._groundMesh),this._updateGroundMesh(),this._sceneHelper.setScene(a.viewGL.scene),this._sceneHelper.updateLight(e),a.viewGL.setPostEffect(e.getModel("postEffect"),i),a.viewGL.setTemporalSuperSampling(e.getModel("temporalSuperSampling")),this._maptalks3DModel=e},afterRender:function(e,t,i,n){var r=n.renderer;this._sceneHelper.updateAmbientCubemap(r,e,i),this._sceneHelper.updateSkybox(r,e,i),e.coordinateSystem.viewGL.scene.traverse((function(e){e.material&&(e.material.define("fragment","NORMAL_UP_AXIS",2),e.material.define("fragment","NORMAL_FRONT_AXIS",1))}))},updateCamera:function(e,t,i,n){e.coordinateSystem.setCameraOption(n),this._updateGroundMesh(),i.getZr().refresh()},_dispatchInteractAction:function(e,t,i){var n;e.dispatchAction({type:"maptalks3DChangeCamera",pitch:t.getPitch(),zoom:(n=t.getResolution(),19-Math.log(n/kr)/Math.LN2+1),center:t.getCenter().toArray(),bearing:t.getBearing(),maptalks3DId:this._maptalks3DModel&&this._maptalks3DModel.id})},_updateGroundMesh:function(){if(this._maptalks3DModel){var e=this._maptalks3DModel.coordinateSystem,t=e.dataToPoint(e.center);this._groundMesh.position.set(t[0],t[1],-.001);var i=new Pt.Plane(new Pt.Vector3(0,0,1),0),n=e.viewGL.camera.castRay(new Pt.Vector2(-1,-1)),r=e.viewGL.camera.castRay(new Pt.Vector2(1,1)),o=n.intersectPlane(i),a=r.intersectPlane(i),s=o.dist(a)/e.viewGL.rootNode.scale.x;this._groundMesh.scale.set(s,s,1)}},dispose:function(e,t){this._zrLayer&&this._zrLayer.dispose(),t.getZr().painter.delLayer(-1e3)}}),kr=12756274*Math.PI/(256*Math.pow(2,20));function Wr(){Ir.apply(this,arguments),this.maxPitch=85,this.zoomOffset=1}Wr.prototype=new Ir,Wr.prototype.constructor=Wr,Wr.prototype.type="maptalks3D";var jr=zr("maptalks3D",Wr,(function(e){e.forEach((function(e){e.setCameraOption(e.model.getMaptalksCameraOption())}))}));m((function(e){e.registerComponentModel(Gr),e.registerComponentView(Vr),e.registerCoordinateSystem("maptalks3D",jr),e.registerAction({type:"maptalks3DChangeCamera",event:"maptalks3dcamerachanged",update:"maptalks3D:updateCamera"},(function(e,t){t.eachComponent({mainType:"maptalks3D",query:e},(function(t){t.setMaptalksCameraOption(e)}))}))}));var Zr=we.vec3,Xr=T.isDimensionStacked;function Yr(e,t,i){for(var n=e.getDataExtent(t),r=e.getDataExtent(i),o=n[1]-n[0]||n[0],a=r[1]-r[0]||r[0],s=new Uint8Array(2500),l=0;l<e.count();l++){var h=e.get(t,l),d=e.get(i,l),c=Math.floor((h-n[0])/o*49),u=50*Math.floor((d-r[0])/a*49)+c;s[u]=s[u]||1}var f=0;for(l=0;l<s.length;l++)s[l]&&f++;return f/s.length}var qr=we.vec3,Qr=T.isDimensionStacked;function Kr(e,t){var i=Qr(e,t[2]);return{dimension:i?e.getCalculationInfo("stackResultDimension"):t[2],isStacked:i}}function Jr(e){e.registerLayout((function(e,t){e.eachSeriesByType("bar3D",(function(e){var t=e.coordinateSystem,i=t&&t.type;"globe"===i?function(e,t){var i=e.getData(),n=e.get("minHeight")||0,r=e.get("barSize"),o=["lng","lat","alt"].map((function(t){return e.coordDimToDataDim(t)[0]}));if(null==r){var a=t.radius*Math.PI,s=Yr(i,o[0],o[1]);r=[a/Math.sqrt(i.count()/s),a/Math.sqrt(i.count()/s)]}else Ge(r)||(r=[r,r]);var l=Kr(i,o);i.each(o,(function(e,o,a,s){var h=i.get(l.dimension,s),d=l.isStacked?h-a:t.altitudeAxis.scale.getExtent()[0],c=Math.max(t.altitudeAxis.dataToCoord(a),n),u=t.dataToPoint([e,o,d]),f=t.dataToPoint([e,o,h]),p=qr.sub([],f,u);qr.normalize(p,p);var m=[r[0],c,r[1]];i.setItemLayout(s,[u,p,m])})),i.setLayout("orient",B.UP.array)}(e,t):"cartesian3D"===i?function(e,t){var i=e.getData(),n=e.get("barSize");if(null==n){var r,o,a=t.size,s=t.getAxis("x"),l=t.getAxis("y");r="category"===s.type?.7*s.getBandWidth():.6*Math.round(a[0]/Math.sqrt(i.count())),o="category"===l.type?.7*l.getBandWidth():.6*Math.round(a[1]/Math.sqrt(i.count())),n=[r,o]}else Ge(n)||(n=[n,n]);var h,d,c,u=t.getAxis("z").scale.getExtent(),f=(d=(h=u)[0],c=h[1],!(d>0&&c>0||d<0&&c<0)),p=["x","y","z"].map((function(t){return e.coordDimToDataDim(t)[0]})),m=Xr(i,p[2]),g=m?i.getCalculationInfo("stackResultDimension"):p[2];i.each(p,(function(e,r,o,a){var s=i.get(g,a),l=m?s-o:f?0:u[0],h=t.dataToPoint([e,r,l]),d=t.dataToPoint([e,r,s]),c=Zr.dist(h,d),p=[0,d[1]<h[1]?-1:1,0];0===Math.abs(c)&&(c=.1);var _=[n[0],c,n[1]];i.setItemLayout(a,[h,p,_])})),i.setLayout("orient",[1,0,0])}(e,t):"geo3D"===i?function(e,t){var i=e.getData(),n=e.get("barSize"),r=e.get("minHeight")||0,o=["lng","lat","alt"].map((function(t){return e.coordDimToDataDim(t)[0]}));if(null==n){var a=Math.min(t.size[0],t.size[2]),s=Yr(i,o[0],o[1]);n=[a/Math.sqrt(i.count()/s),a/Math.sqrt(i.count()/s)]}else Ge(n)||(n=[n,n]);var l=[0,1,0],h=Kr(i,o);i.each(o,(function(e,o,a,s){var d=i.get(h.dimension,s),c=h.isStacked?d-a:t.altitudeAxis.scale.getExtent()[0],u=Math.max(t.altitudeAxis.dataToCoord(a),r),f=t.dataToPoint([e,o,c]),p=[n[0],u,n[1]];i.setItemLayout(s,[f,l,p])})),i.setLayout("orient",[1,0,0])}(e,t):"mapbox3D"!==i&&"maptalks3D"!==i||function(e,t){var i=e.getData(),n=e.coordDimToDataDim("lng")[0],r=e.coordDimToDataDim("lat")[0],o=e.coordDimToDataDim("alt")[0],a=e.get("barSize"),s=e.get("minHeight")||0;if(null==a){var l=i.getDataExtent(n),h=i.getDataExtent(r),d=t.dataToPoint([l[0],h[0]]),c=t.dataToPoint([l[1],h[1]]),u=Math.min(Math.abs(d[0]-c[0]),Math.abs(d[1]-c[1]))||1,f=Yr(i,n,r);a=[u/Math.sqrt(i.count()/f),u/Math.sqrt(i.count()/f)]}else Ge(a)||(a=[a,a]),a[0]/=t.getScale()/16,a[1]/=t.getScale()/16;var p=[0,0,1],m=[n,r,o],g=Kr(i,m);i.each(m,(function(e,n,r,o){var l=i.get(g.dimension,o),h=g.isStacked?l-r:0,d=t.dataToPoint([e,n,h]),c=t.dataToPoint([e,n,l]),u=Math.max(c[2]-d[2],s),f=[a[0],u,a[1]];i.setItemLayout(o,[d,p,f])})),i.setLayout("orient",[1,0,0])}(e,t)}))}))}var $r={};function eo(e,t,i){var n=e.getData(),r=e.getRawValue(t),o=Ge(r)?function(e){var i=[],r=function(e,t){var i=[];return je(e.dimensions,(function(n){var r=e.getDimensionInfo(n),o=r.otherDims[t];null!=o&&!1!==o&&(i[o]=r.name)})),i}(n,"tooltip");function o(e,t){var r=n.getDimensionInfo(t);if(r&&!1!==r.otherDims.tooltip){var o=r.type,a="- "+(r.tooltipName||r.name)+": "+("ordinal"===o?e+"":"time"===o?L("yyyy/MM/dd hh:mm:ss",e):M(e));a&&i.push(ot(a))}}return r.length?je(r,(function(e){o(n.get(e,t),e)})):je(e,o),"<br/>"+i.join("<br/>")}(r):ot(M(r)),a=n.getName(t),s=Hn(n,t);at(s)&&s.colorStops&&(s=(s.colorStops[0]||{}).color);var l=D(s=s||"transparent"),h=e.name;return"\0-"===h&&(h=""),(h=h?ot(h)+"<br/>":"")+l+(a?ot(a)+": "+o:o)}function to(e,t,i){i=i||e.getSource();var n=t||C(e.get("coordinateSystem"))||["x","y","z"],r=_(i,{dimensionsDefine:i.dimensionsDefine||e.get("dimensions"),encodeDefine:i.encodeDefine||e.get("encode"),coordDimensions:n.map((function(t){var i=e.getReferringComponents(t+"Axis3D").models[0];return{type:i&&"category"===i.get("type")?"ordinal":"float",name:t}}))});"cartesian3D"===e.get("coordinateSystem")&&r.forEach((function(t){if(n.indexOf(t.coordDim)>=0){var i=e.getReferringComponents(t.coordDim+"Axis3D").models[0];i&&"category"===i.get("type")&&(t.ordinalMeta=i.getOrdinalMeta())}}));var o=T.enableDataStack(e,r,{byIndex:!0,stackedCoordDimension:"z"}),a=new v(r,e);return a.setCalculationInfo(o),a.initData(i),a}$r.getFormattedLabel=function(e,t,i,n,r){i=i||"normal";var o=e.getData(n).getItemModel(t),a=e.getDataParams(t,n);null!=r&&a.value instanceof Array&&(a.value=a.value[r]);var s,l=o.get("normal"===i?["label","formatter"]:["emphasis","label","formatter"]);return null==l&&(l=o.get(["label","formatter"])),"function"==typeof l?(a.status=i,s=l(a)):"string"==typeof l&&(s=S(l,a)),s},$r.normalizeToArray=function(e){return e instanceof Array?e:null==e?[]:[e]};var io=A.extend({type:"series.bar3D",dependencies:["globe"],visualStyleAccessPathvisu:"itemStyle",getInitialData:function(e,t){return to(this)},getFormattedLabel:function(e,t,i,n){var r=$r.getFormattedLabel(this,e,t,i,n);return null==r&&(r=this.getData().get("z",e)),r},formatTooltip:function(e){return eo(this,e)},defaultOption:{coordinateSystem:"cartesian3D",globeIndex:0,grid3DIndex:0,zlevel:-10,bevelSize:0,bevelSmoothness:2,onGridPlane:"xy",shading:"color",minHeight:0,itemStyle:{opacity:1},label:{show:!1,distance:2,textStyle:{fontSize:14,color:"#000",backgroundColor:"rgba(255,255,255,0.7)",padding:3,borderRadius:3}},emphasis:{label:{show:!0}},animationDurationUpdate:500}});Ye(io.prototype,on);var no,ro,oo,ao,so,lo,ho,co,uo=we.vec3,fo=we.mat3,po=q.extend((function(){return{attributes:{position:new q.Attribute("position","float",3,"POSITION"),normal:new q.Attribute("normal","float",3,"NORMAL"),color:new q.Attribute("color","float",4,"COLOR"),prevPosition:new q.Attribute("prevPosition","float",3),prevNormal:new q.Attribute("prevNormal","float",3)},dynamic:!0,enableNormal:!1,bevelSize:1,bevelSegments:0,_dataIndices:null,_vertexOffset:0,_triangleOffset:0}}),{resetOffset:function(){this._vertexOffset=0,this._triangleOffset=0},setBarCount:function(e){var t=this.enableNormal,i=this.getBarVertexCount()*e,n=this.getBarTriangleCount()*e;this.vertexCount!==i&&(this.attributes.position.init(i),t?this.attributes.normal.init(i):this.attributes.normal.value=null,this.attributes.color.init(i)),this.triangleCount!==n&&(this.indices=i>65535?new Uint32Array(3*n):new Uint16Array(3*n),this._dataIndices=new Uint32Array(i))},getBarVertexCount:function(){var e=this.bevelSize>0?this.bevelSegments:0;return e>0?this._getBevelBarVertexCount(e):this.enableNormal?24:8},getBarTriangleCount:function(){var e=this.bevelSize>0?this.bevelSegments:0;return e>0?this._getBevelBarTriangleCount(e):12},_getBevelBarVertexCount:function(e){return 4*(e+1)*(e+1)*2},_getBevelBarTriangleCount:function(e){return(4*e+3+1)*(2*e+1)*2+4},setColor:function(e,t){for(var i=this.getBarVertexCount(),n=i*(e+1),r=i*e;r<n;r++)this.attributes.color.set(r,t);this.dirtyAttribute("color")},getDataIndexOfVertex:function(e){return this._dataIndices?this._dataIndices[e]:null},addBar:function(){for(var e=uo.create,t=uo.scaleAndAdd,i=e(),n=e(),r=e(),o=e(),a=e(),s=e(),l=e(),h=[],d=[],c=0;c<8;c++)h[c]=e();var u=[[0,1,5,4],[2,3,7,6],[4,5,6,7],[3,2,1,0],[0,4,7,3],[1,2,6,5]],f=[0,1,2,0,2,3],p=[];for(c=0;c<u.length;c++)for(var m=u[c],g=0;g<2;g++){for(var _=[],v=0;v<3;v++)_.push(m[f[3*g+v]]);p.push(_)}return function(e,c,m,g,_,v){var x=this._vertexOffset;if(this.bevelSize>0&&this.bevelSegments>0)this._addBevelBar(e,c,m,g,this.bevelSize,this.bevelSegments,_);else{uo.copy(r,c),uo.normalize(r,r),uo.cross(o,m,r),uo.normalize(o,o),uo.cross(n,r,o),uo.normalize(o,o),uo.negate(a,n),uo.negate(s,r),uo.negate(l,o),t(h[0],e,n,g[0]/2),t(h[0],h[0],o,g[2]/2),t(h[1],e,n,g[0]/2),t(h[1],h[1],l,g[2]/2),t(h[2],e,a,g[0]/2),t(h[2],h[2],l,g[2]/2),t(h[3],e,a,g[0]/2),t(h[3],h[3],o,g[2]/2),t(i,e,r,g[1]),t(h[4],i,n,g[0]/2),t(h[4],h[4],o,g[2]/2),t(h[5],i,n,g[0]/2),t(h[5],h[5],l,g[2]/2),t(h[6],i,a,g[0]/2),t(h[6],h[6],l,g[2]/2),t(h[7],i,a,g[0]/2),t(h[7],h[7],o,g[2]/2);var y=this.attributes;if(this.enableNormal){d[0]=n,d[1]=a,d[2]=r,d[3]=s,d[4]=o,d[5]=l;for(var b=this._vertexOffset,w=0;w<u.length;w++){for(var T=3*this._triangleOffset,S=0;S<6;S++)this.indices[T++]=b+f[S];b+=4,this._triangleOffset+=2}for(w=0;w<u.length;w++){var M=d[w];for(S=0;S<4;S++){var D=u[w][S];y.position.set(this._vertexOffset,h[D]),y.normal.set(this._vertexOffset,M),y.color.set(this._vertexOffset++,_)}}}else{for(w=0;w<p.length;w++){for(T=3*this._triangleOffset,S=0;S<3;S++)this.indices[T+S]=p[w][S]+this._vertexOffset;this._triangleOffset++}for(w=0;w<h.length;w++)y.position.set(this._vertexOffset,h[w]),y.color.set(this._vertexOffset++,_)}}var L=this._vertexOffset;for(w=x;w<L;w++)this._dataIndices[w]=v}}(),_addBevelBar:(no=uo.create(),ro=uo.create(),oo=uo.create(),ao=fo.create(),so=[],lo=[1,-1,-1,1],ho=[1,1,-1,-1],co=[2,0],function(e,t,i,n,r,o,a){uo.copy(ro,t),uo.normalize(ro,ro),uo.cross(oo,i,ro),uo.normalize(oo,oo),uo.cross(no,ro,oo),uo.normalize(oo,oo),ao[0]=no[0],ao[1]=no[1],ao[2]=no[2],ao[3]=ro[0],ao[4]=ro[1],ao[5]=ro[2],ao[6]=oo[0],ao[7]=oo[1],ao[8]=oo[2],r=Math.min(n[0],n[2])/2*r;for(var s=0;s<3;s++)so[s]=Math.max(n[s]-2*r,0);var l=(n[0]-so[0])/2,h=(n[1]-so[1])/2,d=(n[2]-so[2])/2,c=[],u=[],f=this._vertexOffset,p=[];for(s=0;s<2;s++){p[s]=p[s]=[];for(var m=0;m<=o;m++)for(var g=0;g<4;g++){(0===m&&0===s||1===s&&m===o)&&p[s].push(f);for(var _=0;_<=o;_++){var v=_/o*Math.PI/2+Math.PI/2*g,x=m/o*Math.PI/2+Math.PI/2*s;u[0]=l*Math.cos(v)*Math.sin(x),u[1]=h*Math.cos(x),u[2]=d*Math.sin(v)*Math.sin(x),c[0]=u[0]+lo[g]*so[0]/2,c[1]=u[1]+h+co[s]*so[1]/2,c[2]=u[2]+ho[g]*so[2]/2,Math.abs(l-h)<1e-6&&Math.abs(h-d)<1e-6||(u[0]/=l*l,u[1]/=h*h,u[2]/=d*d),uo.normalize(u,u),uo.transformMat3(c,c,ao),uo.transformMat3(u,u,ao),uo.add(c,c,e),this.attributes.position.set(f,c),this.enableNormal&&this.attributes.normal.set(f,u),this.attributes.color.set(f,a),f++}}}var y=4*o+3,b=2*o+1,w=y+1;for(g=0;g<b;g++)for(s=0;s<=y;s++){var T=g*w+s+this._vertexOffset,S=g*w+(s+1)%w+this._vertexOffset,M=(g+1)*w+(s+1)%w+this._vertexOffset,D=(g+1)*w+s+this._vertexOffset;this.setTriangleIndices(this._triangleOffset++,[M,T,S]),this.setTriangleIndices(this._triangleOffset++,[M,D,T])}this.setTriangleIndices(this._triangleOffset++,[p[0][0],p[0][2],p[0][1]]),this.setTriangleIndices(this._triangleOffset++,[p[0][0],p[0][3],p[0][2]]),this.setTriangleIndices(this._triangleOffset++,[p[1][0],p[1][1],p[1][2]]),this.setTriangleIndices(this._triangleOffset++,[p[1][0],p[1][2],p[1][3]]),this._vertexOffset=f})});qe(po.prototype,$t),qe(po.prototype,Gn);var mo=we.vec3;const go=P.extend({type:"bar3D",__ecgl__:!0,init:function(e,t){this.groupGL=new Pt.Node,this._api=t,this._labelsBuilder=new Vn(256,256,t);var i=this;this._labelsBuilder.getLabelPosition=function(e,t,n){if(i._data){var r=i._data.getItemLayout(e),o=r[0],a=r[1],s=r[2][1];return mo.scaleAndAdd([],o,a,n+s)}return[0,0]},this._labelsBuilder.getMesh().renderOrder=100},render:function(e,t,i){var n=this._prevBarMesh;this._prevBarMesh=this._barMesh,this._barMesh=n,this._barMesh||(this._barMesh=new Pt.Mesh({geometry:new po,shadowDepthMaterial:new Pt.Material({shader:new Pt.Shader(Pt.Shader.source("ecgl.sm.depth.vertex"),Pt.Shader.source("ecgl.sm.depth.fragment"))}),culling:"cartesian3D"===e.coordinateSystem.type,renderOrder:10,renderNormal:!0})),this.groupGL.remove(this._prevBarMesh),this.groupGL.add(this._barMesh),this.groupGL.add(this._labelsBuilder.getMesh());var r=e.coordinateSystem;if(this._doRender(e,i),r&&r.viewGL){r.viewGL.add(this.groupGL);var o=r.viewGL.isLinearSpace()?"define":"undefine";this._barMesh.material[o]("fragment","SRGB_DECODE")}this._data=e.getData(),this._labelsBuilder.updateData(this._data),this._labelsBuilder.updateLabels(),this._updateAnimation(e)},_updateAnimation:function(e){Pt.updateVertexAnimation([["prevPosition","position"],["prevNormal","normal"]],this._prevBarMesh,this._barMesh,e)},_doRender:function(e,t){var i=e.getData(),n=e.get("shading"),r="color"!==n,o=this,a=this._barMesh,s="ecgl."+n;a.material&&a.material.shader.name===s||(a.material=Pt.createMaterial(s,["VERTEX_COLOR"])),Pt.setMaterialFromModel(n,a.material,e,t),a.geometry.enableNormal=r,a.geometry.resetOffset();var l=e.get("bevelSize"),h=e.get("bevelSmoothness");a.geometry.bevelSegments=h,a.geometry.bevelSize=l;var d=[],c=new Float32Array(4*i.count()),u=0,f=0,p=!1;i.each((function(e){if(i.hasValue(e)){var t=Hn(i,e),n=Un(i,e);null==n&&(n=1),Pt.parseColor(t,d),d[3]*=n,c[u++]=d[0],c[u++]=d[1],c[u++]=d[2],c[u++]=d[3],d[3]>0&&(f++,d[3]<.99&&(p=!0))}})),a.geometry.setBarCount(f);var m=i.getLayout("orient"),g=this._barIndexOfData=new Int32Array(i.count());f=0;i.each((function(e){if(i.hasValue(e)){var t=i.getItemLayout(e),n=t[0],r=t[1],a=t[2],s=4*e;d[0]=c[s++],d[1]=c[s++],d[2]=c[s++],d[3]=c[s++],d[3]>0&&(o._barMesh.geometry.addBar(n,r,m,a,d,e),g[e]=f++)}else g[e]=-1})),a.geometry.dirty(),a.geometry.updateBoundingBox();var _=a.material;_.transparent=p,_.depthMask=!p,a.geometry.sortTriangles=p,this._initHandler(e,t)},_initHandler:function(e,t){var i=e.getData(),n=this._barMesh,r="cartesian3D"===e.coordinateSystem.type;n.seriesIndex=e.seriesIndex;var o=-1;n.off("mousemove"),n.off("mouseout"),n.on("mousemove",(function(e){var a=n.geometry.getDataIndexOfVertex(e.triangle[0]);a!==o&&(this._downplay(o),this._highlight(a),this._labelsBuilder.updateLabels([a]),r&&t.dispatchAction({type:"grid3DShowAxisPointer",value:[i.get("x",a),i.get("y",a),i.get("z",a,!0)]})),o=a,n.dataIndex=a}),this),n.on("mouseout",(function(e){this._downplay(o),this._labelsBuilder.updateLabels(),o=-1,n.dataIndex=-1,r&&t.dispatchAction({type:"grid3DHideAxisPointer"})}),this)},_highlight:function(e){var t=this._data;if(t){var i=this._barIndexOfData[e];if(!(i<0)){var n=t.getItemModel(e).getModel("emphasis.itemStyle"),r=n.get("color"),o=n.get("opacity");if(null==r){var a=Hn(t,e);r=it(a,-.4)}null==o&&(o=Un(t,e));var s=Pt.parseColor(r);s[3]*=o,this._barMesh.geometry.setColor(i,s),this._api.getZr().refresh()}}},_downplay:function(e){var t=this._data;if(t){var i=this._barIndexOfData[e];if(!(i<0)){var n=Hn(t,e),r=Un(t,e),o=Pt.parseColor(n);o[3]*=r,this._barMesh.geometry.setColor(i,o),this._api.getZr().refresh()}}},highlight:function(e,t,i,n){this._toggleStatus("highlight",e,t,i,n)},downplay:function(e,t,i,n){this._toggleStatus("downplay",e,t,i,n)},_toggleStatus:function(e,t,i,n,r){var o=t.getData(),a=Mt(o,r),s=this;null!=a?je($r.normalizeToArray(a),(function(t){"highlight"===e?this._highlight(t):this._downplay(t)}),this):o.each((function(t){"highlight"===e?s._highlight(t):s._downplay(t)}))},remove:function(){this.groupGL.removeAll()},dispose:function(){this._labelsBuilder.dispose(),this.groupGL.removeAll()}});m((function(e){e.registerChartView(go),e.registerSeriesModel(io),Jr(e),e.registerProcessor((function(e,t){e.eachSeriesByType("bar3d",(function(e){var t=e.getData();t.filterSelf((function(e){return t.hasValue(e)}))}))}))}));var _o=A.extend({type:"series.line3D",dependencies:["grid3D"],visualStyleAccessPath:"lineStyle",visualDrawType:"stroke",getInitialData:function(e,t){return to(this)},formatTooltip:function(e){return eo(this,e)},defaultOption:{coordinateSystem:"cartesian3D",zlevel:-10,grid3DIndex:0,lineStyle:{width:2},animationDurationUpdate:500}}),vo=we.vec3;Pt.Shader.import(wi);const xo=P.extend({type:"line3D",__ecgl__:!0,init:function(e,t){this.groupGL=new Pt.Node,this._api=t},render:function(e,t,i){var n=this._prevLine3DMesh;this._prevLine3DMesh=this._line3DMesh,this._line3DMesh=n,this._line3DMesh||(this._line3DMesh=new Pt.Mesh({geometry:new ii({useNativeLine:!1,sortTriangles:!0}),material:new Pt.Material({shader:Pt.createShader("ecgl.meshLines3D")}),renderOrder:10}),this._line3DMesh.geometry.pick=this._pick.bind(this)),this.groupGL.remove(this._prevLine3DMesh),this.groupGL.add(this._line3DMesh);var r=e.coordinateSystem;if(r&&r.viewGL){r.viewGL.add(this.groupGL);var o=r.viewGL.isLinearSpace()?"define":"undefine";this._line3DMesh.material[o]("fragment","SRGB_DECODE")}this._doRender(e,i),this._data=e.getData(),this._camera=r.viewGL.camera,this.updateCamera(),this._updateAnimation(e)},updateCamera:function(){this._updateNDCPosition()},_doRender:function(e,t){var i=e.getData(),n=this._line3DMesh;n.geometry.resetOffset();var r=i.getLayout("points"),o=[],a=new Float32Array(r.length/3*4),s=0,l=!1;i.each((function(e){var t=Hn(i,e),n=Un(i,e);null==n&&(n=1),Pt.parseColor(t,o),o[3]*=n,a[s++]=o[0],a[s++]=o[1],a[s++]=o[2],a[s++]=o[3],o[3]<.99&&(l=!0)})),n.geometry.setVertexCount(n.geometry.getPolylineVertexCount(r)),n.geometry.setTriangleCount(n.geometry.getPolylineTriangleCount(r)),n.geometry.addPolyline(r,a,St(e.get("lineStyle.width"),1)),n.geometry.dirty(),n.geometry.updateBoundingBox();var h=n.material;h.transparent=l,h.depthMask=!l;var d=e.getModel("debug.wireframe");d.get("show")?(n.geometry.createAttribute("barycentric","float",3),n.geometry.generateBarycentric(),n.material.set("both","WIREFRAME_TRIANGLE"),n.material.set("wireframeLineColor",Pt.parseColor(d.get("lineStyle.color")||"rgba(0,0,0,0.5)")),n.material.set("wireframeLineWidth",St(d.get("lineStyle.width"),1))):n.material.set("both","WIREFRAME_TRIANGLE"),this._points=r,this._initHandler(e,t)},_updateAnimation:function(e){Pt.updateVertexAnimation([["prevPosition","position"],["prevPositionPrev","positionPrev"],["prevPositionNext","positionNext"]],this._prevLine3DMesh,this._line3DMesh,e)},_initHandler:function(e,t){var i=e.getData(),n=e.coordinateSystem,r=this._line3DMesh,o=-1;r.seriesIndex=e.seriesIndex,r.off("mousemove"),r.off("mouseout"),r.on("mousemove",(function(e){var a=n.pointToData(e.point.array),s=i.indicesOfNearest("x",a[0])[0];s!==o&&(t.dispatchAction({type:"grid3DShowAxisPointer",value:[i.get("x",s),i.get("y",s),i.get("z",s)]}),r.dataIndex=s),o=s}),this),r.on("mouseout",(function(e){o=-1,r.dataIndex=-1,t.dispatchAction({type:"grid3DHideAxisPointer"})}),this)},_updateNDCPosition:function(){var e=new de,t=this._camera;de.multiply(e,t.projectionMatrix,t.viewMatrix);var i=this._positionNDC,n=this._points,r=n.length/3;i&&i.length/2===r||(i=this._positionNDC=new Float32Array(2*r));for(var o=[],a=0;a<r;a++){var s=3*a,l=2*a;o[0]=n[s],o[1]=n[s+1],o[2]=n[s+2],o[3]=1,vo.transformMat4(o,o,e.array),i[l]=o[0]/o[3],i[l+1]=o[1]/o[3]}},_pick:function(e,t,i,n,r,o){var a=this._positionNDC,s=this._data.hostModel.get("lineStyle.width"),l=-1,h=.5*i.viewport.width,d=.5*i.viewport.height;e=(e+1)*h,t=(t+1)*d;for(var c=1;c<a.length/2;c++){var u=(a[2*(c-1)]+1)*h,f=(a[2*(c-1)+1]+1)*d,p=(a[2*c]+1)*h,m=(a[2*c+1]+1)*d;if(st(u,f,p,m,s,e,t))l=(u-e)*(u-e)+(f-t)*(f-t)<(p-e)*(p-e)+(m-t)*(m-t)?c-1:c}if(l>=0){var g=3*l,_=new B(this._points[g],this._points[g+1],this._points[g+2]);o.push({dataIndex:l,point:_,pointWorld:_.clone(),target:this._line3DMesh,distance:this._camera.getWorldPosition().dist(_)})}},remove:function(){this.groupGL.removeAll()},dispose:function(){this.groupGL.removeAll()}});m((function(e){e.registerChartView(xo),e.registerSeriesModel(_o),e.registerLayout((function(e,t){e.eachSeriesByType("line3D",(function(e){var t=e.getData(),i=e.coordinateSystem;if(i){if("cartesian3D"!==i.type)return;var n=new Float32Array(3*t.count()),r=[],o=[],a=i.dimensions.map((function(t){return e.coordDimToDataDim(t)[0]}));i&&t.each(a,(function(e,t,a,s){r[0]=e,r[1]=t,r[2]=a,i.dataToPoint(r,o),n[3*s]=o[0],n[3*s+1]=o[1],n[3*s+2]=o[2]})),t.setLayout("points",n)}}))}))}));const yo=A.extend({type:"series.scatter3D",dependencies:["globe","grid3D","geo3D"],visualStyleAccessPath:"itemStyle",hasSymbolVisual:!0,getInitialData:function(e,t){return to(this)},getFormattedLabel:function(e,t,i,n){var r=$r.getFormattedLabel(this,e,t,i,n);if(null==r){var o=this.getData(),a=o.dimensions[o.dimensions.length-1];r=o.get(a,e)}return r},formatTooltip:function(e){return eo(this,e)},defaultOption:{coordinateSystem:"cartesian3D",zlevel:-10,progressive:1e5,progressiveThreshold:1e5,grid3DIndex:0,globeIndex:0,symbol:"circle",symbolSize:10,blendMode:"source-over",label:{show:!1,position:"right",distance:5,textStyle:{fontSize:14,color:"#000",backgroundColor:"rgba(255,255,255,0.7)",padding:3,borderRadius:3}},itemStyle:{opacity:.8},emphasis:{label:{show:!0}},animationDurationUpdate:500}});function bo(e,t,i){(t=t||document.createElement("canvas")).width=e,t.height=e;var n=t.getContext("2d");return i&&i(n),t}var wo={getMarginByStyle:function(e){var t=e.minMargin||0,i=0;e.stroke&&"none"!==e.stroke&&(i=null==e.lineWidth?1:e.lineWidth);var n=e.shadowBlur||0,r=e.shadowOffsetX||0,o=e.shadowOffsetY||0,a={};return a.left=Math.max(i/2,-r+n,t),a.right=Math.max(i/2,r+n,t),a.top=Math.max(i/2,-o+n,t),a.bottom=Math.max(i/2,o+n,t),a},createSymbolSprite:function(e,t,i,n){var r=function(e,t,i,n){Ge(t)||(t=[t,t]);var r=wo.getMarginByStyle(i,n),o=t[0]+r.left+r.right,a=t[1]+r.top+r.bottom,s=E(e,0,0,t[0],t[1]),l=Math.max(o,a);s.x=r.left,s.y=r.top,o>a?s.y+=(l-a)/2:s.x+=(l-o)/2;var h=s.getBoundingRect();return s.x-=h.x,s.y-=h.y,s.setStyle(i),s.update(),s.__size=l,s}(e,t,i),o=wo.getMarginByStyle(i);return{image:bo(r.__size,n,(function(e){Ke(e,r)})),margin:o}},createSDFFromCanvas:function(e,t,i,n){return bo(t,n,(function(t){var n=e.getContext("2d").getImageData(0,0,e.width,e.height);t.putImageData(function(e,t,i){var n=t.width,r=t.height,o=e.canvas.width,a=e.canvas.height,s=n/o,l=r/a;function h(e){return e<128?1:-1}function d(e,o){var a=Infinity;e=Math.floor(e*s);for(var d=(o=Math.floor(o*l))*n+e,c=h(t.data[4*d]),u=Math.max(o-i,0);u<Math.min(o+i,r);u++)for(var f=Math.max(e-i,0);f<Math.min(e+i,n);f++){d=u*n+f;var p=f-e,m=u-o;if(c!==h(t.data[4*d])){var g=p*p+m*m;g<a&&(a=g)}}return c*Math.sqrt(a)}for(var c=e.createImageData(o,a),u=0;u<a;u++)for(var f=0;f<o;f++){var p=d(f,u)/i*.5+.5,m=4*(u*o+f);c.data[m++]=255*(1-p),c.data[m++]=255*(1-p),c.data[m++]=255*(1-p),c.data[m++]=255}return c}(t,n,i),0,0)}))},createSimpleSprite:function(e,t){return bo(e,t,(function(t){var i=e/2;t.beginPath(),t.arc(i,i,60,0,2*Math.PI,!1),t.closePath();var n=t.createRadialGradient(i,i,0,i,i,i);n.addColorStop(0,"rgba(255, 255, 255, 1)"),n.addColorStop(.5,"rgba(255, 255, 255, 0.5)"),n.addColorStop(1,"rgba(255, 255, 255, 0)"),t.fillStyle=n,t.fill()}))}},To=we.vec3;const So={needsSortVertices:function(){return this.sortVertices},needsSortVerticesProgressively:function(){return this.needsSortVertices()&&this.vertexCount>=2e4},doSortVertices:function(e,t){var i=this.indices,n=To.create();if(!i){i=this.indices=this.vertexCount>65535?new Uint32Array(this.vertexCount):new Uint16Array(this.vertexCount);for(var r=0;r<i.length;r++)i[r]=r}if(0===t){var o,a=this.attributes.position,s=(e=e.array,0);this._zList&&this._zList.length===this.vertexCount||(this._zList=new Float32Array(this.vertexCount));for(r=0;r<this.vertexCount;r++){a.get(r,n);var l=To.sqrDist(n,e);isNaN(l)&&(l=1e7,s++),0===r?(o=l,l=0):l-=o,this._zList[r]=l}this._noneCount=s}if(this.vertexCount<2e4)0===t&&this._simpleSort(this._noneCount/this.vertexCount>.05);else for(r=0;r<3;r++)this._progressiveQuickSort(3*t+r);this.dirtyIndices()},_simpleSort:function(e){var t=this._zList,i=this.indices;function n(e,i){return t[i]-t[e]}e?Array.prototype.sort.call(i,n):In.sort(i,n,0,i.length-1)},_progressiveQuickSort:function(e){var t=this._zList,i=this.indices;this._quickSort=this._quickSort||new In,this._quickSort.step(i,(function(e,i){return t[i]-t[e]}),e)}};var Mo=we.vec4;Pt.Shader.import("@export ecgl.sdfSprite.vertex\n\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform float elapsedTime : 0;\n\nattribute vec3 position : POSITION;\n\n#ifdef VERTEX_SIZE\nattribute float size;\n#else\nuniform float u_Size;\n#endif\n\n#ifdef VERTEX_COLOR\nattribute vec4 a_FillColor: COLOR;\nvarying vec4 v_Color;\n#endif\n\n#ifdef VERTEX_ANIMATION\nattribute vec3 prevPosition;\nattribute float prevSize;\nuniform float percent : 1.0;\n#endif\n\n\n#ifdef POSITIONTEXTURE_ENABLED\nuniform sampler2D positionTexture;\n#endif\n\nvarying float v_Size;\n\nvoid main()\n{\n\n#ifdef POSITIONTEXTURE_ENABLED\n gl_Position = worldViewProjection * vec4(texture2D(positionTexture, position.xy).xy, -10.0, 1.0);\n#else\n\n #ifdef VERTEX_ANIMATION\n vec3 pos = mix(prevPosition, position, percent);\n #else\n vec3 pos = position;\n #endif\n gl_Position = worldViewProjection * vec4(pos, 1.0);\n#endif\n\n#ifdef VERTEX_SIZE\n#ifdef VERTEX_ANIMATION\n v_Size = mix(prevSize, size, percent);\n#else\n v_Size = size;\n#endif\n#else\n v_Size = u_Size;\n#endif\n\n#ifdef VERTEX_COLOR\n v_Color = a_FillColor;\n #endif\n\n gl_PointSize = v_Size;\n}\n\n@end\n\n@export ecgl.sdfSprite.fragment\n\nuniform vec4 color: [1, 1, 1, 1];\nuniform vec4 strokeColor: [1, 1, 1, 1];\nuniform float smoothing: 0.07;\n\nuniform float lineWidth: 0.0;\n\n#ifdef VERTEX_COLOR\nvarying vec4 v_Color;\n#endif\n\nvarying float v_Size;\n\nuniform sampler2D sprite;\n\n@import clay.util.srgb\n\nvoid main()\n{\n gl_FragColor = color;\n\n vec4 _strokeColor = strokeColor;\n\n#ifdef VERTEX_COLOR\n gl_FragColor *= v_Color;\n #endif\n\n#ifdef SPRITE_ENABLED\n float d = texture2D(sprite, gl_PointCoord).r;\n gl_FragColor.a *= smoothstep(0.5 - smoothing, 0.5 + smoothing, d);\n\n if (lineWidth > 0.0) {\n float sLineWidth = lineWidth / 2.0;\n\n float outlineMaxValue0 = 0.5 + sLineWidth;\n float outlineMaxValue1 = 0.5 + sLineWidth + smoothing;\n float outlineMinValue0 = 0.5 - sLineWidth - smoothing;\n float outlineMinValue1 = 0.5 - sLineWidth;\n\n if (d <= outlineMaxValue1 && d >= outlineMinValue0) {\n float a = _strokeColor.a;\n if (d <= outlineMinValue1) {\n a = a * smoothstep(outlineMinValue0, outlineMinValue1, d);\n }\n else {\n a = a * smoothstep(outlineMaxValue1, outlineMaxValue0, d);\n }\n gl_FragColor.rgb = mix(gl_FragColor.rgb * gl_FragColor.a, _strokeColor.rgb, a);\n gl_FragColor.a = gl_FragColor.a * (1.0 - a) + a;\n }\n }\n#endif\n\n#ifdef SRGB_DECODE\n gl_FragColor = sRGBToLinear(gl_FragColor);\n#endif\n}\n@end");var Do=Pt.Mesh.extend((function(){var e=new Pt.Geometry({dynamic:!0,attributes:{color:new Pt.Geometry.Attribute("color","float",4,"COLOR"),position:new Pt.Geometry.Attribute("position","float",3,"POSITION"),size:new Pt.Geometry.Attribute("size","float",1),prevPosition:new Pt.Geometry.Attribute("prevPosition","float",3),prevSize:new Pt.Geometry.Attribute("prevSize","float",1)}});Object.assign(e,So);var t=new Pt.Material({shader:Pt.createShader("ecgl.sdfSprite"),transparent:!0,depthMask:!1});t.enableTexture("sprite"),t.define("both","VERTEX_COLOR"),t.define("both","VERTEX_SIZE");var i=new Pt.Texture2D({image:document.createElement("canvas"),flipY:!1});return t.set("sprite",i),e.pick=this._pick.bind(this),{geometry:e,material:t,mode:Pt.Mesh.POINTS,sizeScale:1}}),{_pick:function(e,t,i,n,r,o){var a=this._positionNDC;if(a)for(var s=i.viewport,l=2/s.width,h=2/s.height,d=this.geometry.vertexCount-1;d>=0;d--){var c,u=a[2*(c=this.geometry.indices?this.geometry.indices[d]:d)],f=a[2*c+1],p=this.geometry.attributes.size.get(c)/this.sizeScale/2;if(e>u-p*l&&e<u+p*l&&t>f-p*h&&t<f+p*h){var m=new Pt.Vector3,g=new Pt.Vector3;this.geometry.attributes.position.get(c,m.array),Pt.Vector3.transformMat4(g,m,this.worldTransform),o.push({vertexIndex:c,point:m,pointWorld:g,target:this,distance:g.distance(n.getWorldPosition())})}}},updateNDCPosition:function(e,t,i){var n=this._positionNDC,r=this.geometry;n&&n.length/2===r.vertexCount||(n=this._positionNDC=new Float32Array(2*r.vertexCount));for(var o=Mo.create(),a=0;a<r.vertexCount;a++)r.attributes.position.get(a,o),o[3]=1,Mo.transformMat4(o,o,e.array),Mo.scale(o,o,1/o[3]),n[2*a]=o[0],n[2*a+1]=o[1]}});function Lo(e,t){this.rootNode=new Pt.Node,this.is2D=e,this._labelsBuilder=new Vn(256,256,t),this._labelsBuilder.getMesh().renderOrder=100,this.rootNode.add(this._labelsBuilder.getMesh()),this._api=t,this._spriteImageCanvas=document.createElement("canvas"),this._startDataIndex=0,this._endDataIndex=0,this._sizeScale=1}Lo.prototype={constructor:Lo,highlightOnMouseover:!0,update:function(e,t,i,n,r){var o=this._prevMesh;this._prevMesh=this._mesh,this._mesh=o;var a=e.getData();if(null==n&&(n=0),null==r&&(r=a.count()),this._startDataIndex=n,this._endDataIndex=r-1,!this._mesh){var s=this._prevMesh&&this._prevMesh.material;this._mesh=new Do({renderOrder:10,frustumCulling:!1}),s&&(this._mesh.material=s)}s=this._mesh.material;var l=this._mesh.geometry,h=l.attributes;this.rootNode.remove(this._prevMesh),this.rootNode.add(this._mesh),this._setPositionTextureToMesh(this._mesh,this._positionTexture);var d=this._getSymbolInfo(e,n,r),c=i.getDevicePixelRatio(),u=e.getModel("itemStyle").getItemStyle(),f=e.get("large"),p=1;d.maxSize>2?(p=this._updateSymbolSprite(e,u,d,c),s.enableTexture("sprite")):s.disableTexture("sprite"),h.position.init(r-n);var m=[];if(f){s.undefine("VERTEX_SIZE"),s.undefine("VERTEX_COLOR");var g=function(e){const t=e.getVisual("style");if(t)return t[e.getVisual("drawType")]}(a),_=function(e){return e.getVisual("style").opacity}(a);Pt.parseColor(g,m),m[3]*=_,s.set({color:m,u_Size:d.maxSize*this._sizeScale})}else s.set({color:[1,1,1,1]}),s.define("VERTEX_SIZE"),s.define("VERTEX_COLOR"),h.size.init(r-n),h.color.init(r-n),this._originalOpacity=new Float32Array(r-n);for(var v=a.getLayout("points"),x=h.position.value,y=0;y<r-n;y++){var b=3*y,w=2*y;if(this.is2D?(x[b]=v[w],x[b+1]=v[w+1],x[b+2]=-10):(x[b]=v[b],x[b+1]=v[b+1],x[b+2]=v[b+2]),!f){g=Hn(a,y),_=Un(a,y);Pt.parseColor(g,m),m[3]*=_,h.color.set(y,m),m[3];var T=a.getItemVisual(y,"symbolSize");T=T instanceof Array?Math.max(T[0],T[1]):T,isNaN(T)&&(T=0),h.size.value[y]=T*p*this._sizeScale,this._originalOpacity[y]=m[3]}}this._mesh.sizeScale=p,l.updateBoundingBox(),l.dirty(),this._updateMaterial(e,u);var S=e.coordinateSystem;S&&S.viewGL&&s[S.viewGL.isLinearSpace()?"define":"undefine"]("fragment","SRGB_DECODE");f||this._updateLabelBuilder(e,n,r),this._updateHandler(e,t,i),this._updateAnimation(e),this._api=i},getPointsMesh:function(){return this._mesh},updateLabels:function(e){this._labelsBuilder.updateLabels(e)},hideLabels:function(){this.rootNode.remove(this._labelsBuilder.getMesh())},showLabels:function(){this.rootNode.add(this._labelsBuilder.getMesh())},dispose:function(){this._labelsBuilder.dispose()},_updateSymbolSprite:function(e,t,i,n){i.maxSize=Math.min(2*i.maxSize,200);var r=[];return i.aspect>1?(r[0]=i.maxSize,r[1]=i.maxSize/i.aspect):(r[1]=i.maxSize,r[0]=i.maxSize*i.aspect),r[0]=r[0]||1,r[1]=r[1]||1,this._symbolType===i.type&&function(e,t){return e&&t&&e[0]===t[0]&&e[1]===t[1]}(this._symbolSize,r)&&this._lineWidth===t.lineWidth||(wo.createSymbolSprite(i.type,r,{fill:"#fff",lineWidth:t.lineWidth,stroke:"transparent",shadowColor:"transparent",minMargin:Math.min(r[0]/2,10)},this._spriteImageCanvas),wo.createSDFFromCanvas(this._spriteImageCanvas,Math.min(this._spriteImageCanvas.width,32),20,this._mesh.material.get("sprite").image),this._symbolType=i.type,this._symbolSize=r,this._lineWidth=t.lineWidth),this._spriteImageCanvas.width/i.maxSize*n},_updateMaterial:function(e,t){var i="lighter"===e.get("blendMode")?Pt.additiveBlend:null,n=this._mesh.material;n.blend=i,n.set("lineWidth",t.lineWidth/20);var r=Pt.parseColor(t.stroke);n.set("strokeColor",r),n.transparent=!0,n.depthMask=!1,n.depthTest=!this.is2D,n.sortVertices=!this.is2D},_updateLabelBuilder:function(e,t,i){var n=e.getData(),r=this._mesh.geometry,o=r.attributes.position.value,a=(t=this._startDataIndex,this._mesh.sizeScale);this._labelsBuilder.updateData(n,t,i),this._labelsBuilder.getLabelPosition=function(e,i,n){var r=3*(e-t);return[o[r],o[r+1],o[r+2]]},this._labelsBuilder.getLabelDistance=function(e,i,n){return r.attributes.size.get(e-t)/a/2+n},this._labelsBuilder.updateLabels()},_updateAnimation:function(e){Pt.updateVertexAnimation([["prevPosition","position"],["prevSize","size"]],this._prevMesh,this._mesh,e)},_updateHandler:function(e,t,i){var n,r=e.getData(),o=this._mesh,a=this,s=-1,l=e.coordinateSystem&&"cartesian3D"===e.coordinateSystem.type;l&&(n=e.coordinateSystem.model),o.seriesIndex=e.seriesIndex,o.off("mousemove"),o.off("mouseout"),o.on("mousemove",(function(t){var h=t.vertexIndex+a._startDataIndex;h!==s&&(this.highlightOnMouseover&&(this.downplay(r,s),this.highlight(r,h),this._labelsBuilder.updateLabels([h])),l&&i.dispatchAction({type:"grid3DShowAxisPointer",value:[r.get(e.coordDimToDataDim("x")[0],h),r.get(e.coordDimToDataDim("y")[0],h),r.get(e.coordDimToDataDim("z")[0],h)],grid3DIndex:n.componentIndex})),o.dataIndex=h,s=h}),this),o.on("mouseout",(function(e){var t=e.vertexIndex+a._startDataIndex;this.highlightOnMouseover&&(this.downplay(r,t),this._labelsBuilder.updateLabels()),s=-1,o.dataIndex=-1,l&&i.dispatchAction({type:"grid3DHideAxisPointer",grid3DIndex:n.componentIndex})}),this)},updateLayout:function(e,t,i){var n=e.getData();if(this._mesh){var r=this._mesh.geometry.attributes.position.value,o=n.getLayout("points");if(this.is2D)for(var a=0;a<o.length/2;a++){var s=3*a,l=2*a;r[s]=o[l],r[s+1]=o[l+1],r[s+2]=-10}else for(a=0;a<o.length;a++)r[a]=o[a];this._mesh.geometry.dirty(),i.getZr().refresh()}},updateView:function(e){if(this._mesh){var t=new de;de.mul(t,e.viewMatrix,this._mesh.worldTransform),de.mul(t,e.projectionMatrix,t),this._mesh.updateNDCPosition(t,this.is2D,this._api)}},highlight:function(e,t){if(!(t>this._endDataIndex||t<this._startDataIndex)){var i=e.getItemModel(t).getModel("emphasis.itemStyle"),n=i.get("color"),r=i.get("opacity");if(null==n){var o=Hn(e,t);n=it(o,-.4)}null==r&&(r=Un(e,t));var a=Pt.parseColor(n);a[3]*=r,this._mesh.geometry.attributes.color.set(t-this._startDataIndex,a),this._mesh.geometry.dirtyAttribute("color"),this._api.getZr().refresh()}},downplay:function(e,t){if(!(t>this._endDataIndex||t<this._startDataIndex)){var i=Hn(e,t),n=Un(e,t),r=Pt.parseColor(i);r[3]*=n,this._mesh.geometry.attributes.color.set(t-this._startDataIndex,r),this._mesh.geometry.dirtyAttribute("color"),this._api.getZr().refresh()}},fadeOutAll:function(e){if(this._originalOpacity){for(var t=this._mesh.geometry,i=0;i<t.vertexCount;i++){var n=this._originalOpacity[i]*e;t.attributes.color.value[4*i+3]=n}t.dirtyAttribute("color"),this._api.getZr().refresh()}},fadeInAll:function(){this.fadeOutAll(1)},setPositionTexture:function(e){this._mesh&&this._setPositionTextureToMesh(this._mesh,e),this._positionTexture=e},removePositionTexture:function(){this._positionTexture=null,this._mesh&&this._setPositionTextureToMesh(this._mesh,null)},setSizeScale:function(e){if(e!==this._sizeScale){if(this._mesh){var t=this._mesh.material.get("u_Size");this._mesh.material.set("u_Size",t/this._sizeScale*e);var i=this._mesh.geometry.attributes;if(i.size.value)for(var n=0;n<i.size.value.length;n++)i.size.value[n]=i.size.value[n]/this._sizeScale*e}this._sizeScale=e}},_setPositionTextureToMesh:function(e,t){t&&e.material.set("positionTexture",t),e.material[t?"enableTexture":"disableTexture"]("positionTexture")},_getSymbolInfo:function(e,t,i){if(e.get("large"))return(h=St(e.get("symbolSize"),1))instanceof Array?(a=Math.max(h[0],h[1]),n=h[0]/h[1]):(a=h,n=1),{maxSize:h,type:e.get("symbol"),aspect:n};for(var n,r=e.getData(),o=r.getItemVisual(0,"symbol")||"circle",a=0,s=t;s<i;s++){var l,h=r.getItemVisual(s,"symbolSize"),d=r.getItemVisual(s,"symbol");if(h instanceof Array)l=h[0]/h[1],a=Math.max(Math.max(h[0],h[1]),a);else{if(isNaN(h))continue;l=1,a=Math.max(h,a)}o=d,n=l}return{maxSize:a,type:o,aspect:n}}};const Co=P.extend({type:"scatter3D",hasSymbolVisual:!0,__ecgl__:!0,init:function(e,t){this.groupGL=new Pt.Node,this._pointsBuilderList=[],this._currentStep=0},render:function(e,t,i){if(this.groupGL.removeAll(),e.getData().count()){var n=e.coordinateSystem;if(n&&n.viewGL){n.viewGL.add(this.groupGL),this._camera=n.viewGL.camera;var r=this._pointsBuilderList[0];r||(r=this._pointsBuilderList[0]=new Lo(!1,i)),this._pointsBuilderList.length=1,this.groupGL.add(r.rootNode),r.update(e,t,i),r.updateView(n.viewGL.camera)}}},incrementalPrepareRender:function(e,t,i){var n=e.coordinateSystem;n&&n.viewGL&&(n.viewGL.add(this.groupGL),this._camera=n.viewGL.camera),this.groupGL.removeAll(),this._currentStep=0},incrementalRender:function(e,t,i,n){if(!(e.end<=e.start)){var r=this._pointsBuilderList[this._currentStep];r||(r=new Lo(!1,n),this._pointsBuilderList[this._currentStep]=r),this.groupGL.add(r.rootNode),r.update(t,i,n,e.start,e.end),r.updateView(t.coordinateSystem.viewGL.camera),this._currentStep++}},updateCamera:function(){this._pointsBuilderList.forEach((function(e){e.updateView(this._camera)}),this)},highlight:function(e,t,i,n){this._toggleStatus("highlight",e,t,i,n)},downplay:function(e,t,i,n){this._toggleStatus("downplay",e,t,i,n)},_toggleStatus:function(e,t,i,n,r){var o=t.getData(),a=Mt(o,r),s="highlight"===e;null!=a?je($r.normalizeToArray(a),(function(e){for(var t=0;t<this._pointsBuilderList.length;t++){var i=this._pointsBuilderList[t];s?i.highlight(o,e):i.downplay(o,e)}}),this):o.each((function(e){for(var t=0;t<this._pointsBuilderList.length;t++){var i=this._pointsBuilderList[t];s?i.highlight(o,e):i.downplay(o,e)}}))},dispose:function(){this._pointsBuilderList.forEach((function(e){e.dispose()})),this.groupGL.removeAll()},remove:function(){this.groupGL.removeAll()}});m((function(e){e.registerChartView(Co),e.registerSeriesModel(yo),e.registerLayout({seriesType:"scatter3D",reset:function(e){var t=e.coordinateSystem;if(t){var i=t.dimensions;if(i.length<3)return;var n=i.map((function(t){return e.coordDimToDataDim(t)[0]})),r=[],o=[];return{progress:function(e,i){for(var a=new Float32Array(3*(e.end-e.start)),s=e.start;s<e.end;s++){var l=3*(s-e.start);r[0]=i.get(n[0],s),r[1]=i.get(n[1],s),r[2]=i.get(n[2],s),t.dataToPoint(r,o),a[l]=o[0],a[l+1]=o[1],a[l+2]=o[2]}i.setLayout("points",a)}}}}})}));var Ao=we.vec3,Po=we.vec2,Eo=Ao.normalize,No=Ao.cross,Oo=Ao.sub,Io=Ao.add,Ro=Ao.create,zo=Ro(),Fo=Ro(),Bo=Ro(),Go=Ro(),Ho=[],Uo=[];function Vo(e,t){Po.copy(Ho,e[0]),Po.copy(Uo,e[1]);var i=[],n=i[0]=Ro(),r=i[1]=Ro(),o=i[2]=Ro(),a=i[3]=Ro();t.dataToPoint(Ho,n),t.dataToPoint(Uo,a),Eo(zo,n),Oo(Fo,a,n),Eo(Fo,Fo),No(Bo,Fo,zo),Eo(Bo,Bo),No(Fo,zo,Bo),Io(r,zo,Fo),Eo(r,r),Eo(zo,a),Oo(Fo,n,a),Eo(Fo,Fo),No(Bo,Fo,zo),Eo(Bo,Bo),No(Fo,zo,Bo),Io(o,zo,Fo),Eo(o,o),Io(Go,n,a),Eo(Go,Go);var s=Ao.dot(n,Go),l=Ao.dot(Go,r),h=(Math.max(Ao.len(n),Ao.len(a))-s)/l*2;return Ao.scaleAndAdd(r,n,r,h),Ao.scaleAndAdd(o,a,o,h),i}function ko(e,t){for(var i=new Float32Array(3*e.length),n=0,r=[],o=0;o<e.length;o++)t.dataToPoint(e[o],r),i[n++]=r[0],i[n++]=r[1],i[n++]=r[2];return i}function Wo(e){var t=[];return e.each((function(i){var n=e.getItemModel(i),r=n.option instanceof Array?n.option:n.getShallow("coords",!0);t.push(r)})),{coordsList:t}}function jo(e,t,i){var n=e.getData(),r=e.get("polyline"),o=Wo(n);n.setLayout("lineType",r?"polyline":"cubicBezier"),n.each((function(e){var a=o.coordsList[e],s=r?ko(a,t):function(e,t,i){var n=[],r=n[0]=Ao.create(),o=n[1]=Ao.create(),a=n[2]=Ao.create(),s=n[3]=Ao.create();t.dataToPoint(e[0],r),t.dataToPoint(e[1],s);var l=Ao.dist(r,s);return Ao.lerp(o,r,s,.3),Ao.lerp(a,r,s,.3),Ao.scaleAndAdd(o,o,i,Math.min(.1*l,10)),Ao.scaleAndAdd(a,a,i,Math.min(.1*l,10)),n}(a,t,i);n.setItemLayout(e,s)}))}function Zo(e,t){e.eachSeriesByType("lines3D",(function(e){var t=e.coordinateSystem;"globe"===t.type?function(e,t){var i=e.getData(),n=e.get("polyline");i.setLayout("lineType",n?"polyline":"cubicBezier");var r=Wo(i);i.each((function(e){var o=r.coordsList[e],a=n?ko:Vo;i.setItemLayout(e,a(o,t))}))}(e,t):"geo3D"===t.type?jo(e,t,[0,1,0]):"mapbox3D"!==t.type&&"maptalks3D"!==t.type||jo(e,t,[0,0,1])}))}const Xo=A.extend({type:"series.lines3D",dependencies:["globe"],visualStyleAccessPath:"lineStyle",visualDrawType:"stroke",getInitialData:function(e,t){var i=new v(["value"],this);return i.hasItemOption=!1,i.initData(e.data,[],(function(e,t,n,r){if(e instanceof Array)return NaN;i.hasItemOption=!0;var o=e.value;return null!=o?o instanceof Array?o[r]:o:void 0})),i},defaultOption:{coordinateSystem:"globe",globeIndex:0,geo3DIndex:0,zlevel:-10,polyline:!1,effect:{show:!1,period:4,trailWidth:4,trailLength:.2,spotIntensity:6},silent:!0,blendMode:"source-over",lineStyle:{width:1,opacity:.5}}});var Yo=we.vec3;function qo(e){return e>0?1:-1}Pt.Shader.import("@export ecgl.trail2.vertex\nattribute vec3 position: POSITION;\nattribute vec3 positionPrev;\nattribute vec3 positionNext;\nattribute float offset;\nattribute float dist;\nattribute float distAll;\nattribute float start;\n\nattribute vec4 a_Color : COLOR;\n\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform vec4 viewport : VIEWPORT;\nuniform float near : NEAR;\n\nuniform float speed : 0;\nuniform float trailLength: 0.3;\nuniform float time;\nuniform float period: 1000;\n\nuniform float spotSize: 1;\n\nvarying vec4 v_Color;\nvarying float v_Percent;\nvarying float v_SpotPercent;\n\n@import ecgl.common.wireframe.vertexHeader\n\n@import ecgl.lines3D.clipNear\n\nvoid main()\n{\n @import ecgl.lines3D.expandLine\n\n gl_Position = currProj;\n\n v_Color = a_Color;\n\n @import ecgl.common.wireframe.vertexMain\n\n#ifdef CONSTANT_SPEED\n float t = mod((speed * time + start) / distAll, 1. + trailLength) - trailLength;\n#else\n float t = mod((time + start) / period, 1. + trailLength) - trailLength;\n#endif\n\n float trailLen = distAll * trailLength;\n\n v_Percent = (dist - t * distAll) / trailLen;\n\n v_SpotPercent = spotSize / distAll;\n\n }\n@end\n\n\n@export ecgl.trail2.fragment\n\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\nuniform float spotIntensity: 5;\n\nvarying vec4 v_Color;\nvarying float v_Percent;\nvarying float v_SpotPercent;\n\n@import ecgl.common.wireframe.fragmentHeader\n\n@import clay.util.srgb\n\nvoid main()\n{\n if (v_Percent > 1.0 || v_Percent < 0.0) {\n discard;\n }\n\n float fade = v_Percent;\n\n#ifdef SRGB_DECODE\n gl_FragColor = sRGBToLinear(color * v_Color);\n#else\n gl_FragColor = color * v_Color;\n#endif\n\n @import ecgl.common.wireframe.fragmentMain\n\n if (v_Percent > (1.0 - v_SpotPercent)) {\n gl_FragColor.rgb *= spotIntensity;\n }\n\n gl_FragColor.a *= fade;\n}\n\n@end");const Qo=Pt.Mesh.extend((function(){var e=new Pt.Material({shader:new Pt.Shader(Pt.Shader.source("ecgl.trail2.vertex"),Pt.Shader.source("ecgl.trail2.fragment")),transparent:!0,depthMask:!1}),t=new ii({dynamic:!0});return t.createAttribute("dist","float",1),t.createAttribute("distAll","float",1),t.createAttribute("start","float",1),{geometry:t,material:e,culling:!1,$ignorePicking:!0}}),{updateData:function(e,t,i){var n=e.hostModel,r=this.geometry,o=n.getModel("effect"),a=o.get("trailWidth")*t.getDevicePixelRatio(),s=o.get("trailLength"),l=n.get("effect.constantSpeed"),h=1e3*n.get("effect.period"),d=null!=l;d?this.material.set("speed",l/1e3):this.material.set("period",h),this.material[d?"define":"undefine"]("vertex","CONSTANT_SPEED");var c=n.get("polyline");r.trailLength=s,this.material.set("trailLength",s),r.resetOffset(),["position","positionPrev","positionNext"].forEach((function(e){r.attributes[e].value=i.attributes[e].value}));["dist","distAll","start","offset","color"].forEach((function(e){r.attributes[e].init(r.vertexCount)})),r.indices=i.indices;var u=[],f=o.get("trailColor"),p=o.get("trailOpacity"),m=null!=f,g=null!=p;this.updateWorldTransform();var _=this.worldTransform.x.len(),v=this.worldTransform.y.len(),x=this.worldTransform.z.len(),y=0,b=0;e.each((function(t){var n=e.getItemLayout(t),o=g?p:Un(e,t),s=Hn(e,t);null==o&&(o=1),(u=Pt.parseColor(m?f:s,u))[3]*=o;for(var l=c?i.getPolylineVertexCount(n):i.getCubicCurveVertexCount(n[0],n[1],n[2],n[3]),w=0,T=[],S=[],M=y;M<y+l;M++)r.attributes.position.get(M,T),T[0]*=_,T[1]*=v,T[2]*=x,M>y&&(w+=Yo.dist(T,S)),r.attributes.dist.set(M,w),Yo.copy(S,T);b=Math.max(b,w);var D=Math.random()*(d?w:h);for(M=y;M<y+l;M++)r.attributes.distAll.set(M,w),r.attributes.start.set(M,D),r.attributes.offset.set(M,qo(i.attributes.offset.get(M))*a/2),r.attributes.color.set(M,u);y+=l})),this.material.set("spotSize",.1*b*s),this.material.set("spotIntensity",o.get("spotIntensity")),r.dirty()},setAnimationTime:function(e){this.material.set("time",e)}});Pt.Shader.import(wi);const Ko=P.extend({type:"lines3D",__ecgl__:!0,init:function(e,t){this.groupGL=new Pt.Node,this._meshLinesMaterial=new Pt.Material({shader:Pt.createShader("ecgl.meshLines3D"),transparent:!0,depthMask:!1}),this._linesMesh=new Pt.Mesh({geometry:new ii,material:this._meshLinesMaterial,$ignorePicking:!0}),this._trailMesh=new Qo},render:function(e,t,i){this.groupGL.add(this._linesMesh);var n=e.coordinateSystem,r=e.getData();if(n&&n.viewGL){n.viewGL.add(this.groupGL),this._updateLines(e,t,i);var o=n.viewGL.isLinearSpace()?"define":"undefine";this._linesMesh.material[o]("fragment","SRGB_DECODE"),this._trailMesh.material[o]("fragment","SRGB_DECODE")}var a=this._trailMesh;if(a.stopAnimation(),e.get("effect.show")){this.groupGL.add(a),a.updateData(r,i,this._linesMesh.geometry),a.__time=a.__time||0;var s=36e5;this._curveEffectsAnimator=a.animate("",{loop:!0}).when(s,{__time:s}).during((function(){a.setAnimationTime(a.__time)})).start()}else this.groupGL.remove(a),this._curveEffectsAnimator=null;this._linesMesh.material.blend=this._trailMesh.material.blend="lighter"===e.get("blendMode")?Pt.additiveBlend:null},pauseEffect:function(){this._curveEffectsAnimator&&this._curveEffectsAnimator.pause()},resumeEffect:function(){this._curveEffectsAnimator&&this._curveEffectsAnimator.resume()},toggleEffect:function(){var e=this._curveEffectsAnimator;e&&(e.isPaused()?e.resume():e.pause())},_updateLines:function(e,t,i){var n=e.getData(),r=e.coordinateSystem,o=this._linesMesh.geometry,a=e.get("polyline");o.expandLine=!0;var s=function(e){return null!=e.radius?e.radius:null!=e.size?Math.max(e.size[0],e.size[1],e.size[2]):100}(r);o.segmentScale=s/20;var l="lineStyle.width".split("."),h=i.getDevicePixelRatio();n.each((function(e){var t=n.getItemModel(e).get(l);null==t&&(t=1),n.setItemVisual(e,"lineWidth",t)})),o.useNativeLine=!1;var d=0,c=0;n.each((function(e){var t=n.getItemLayout(e);a?(d+=o.getPolylineVertexCount(t),c+=o.getPolylineTriangleCount(t)):(d+=o.getCubicCurveVertexCount(t[0],t[1],t[2],t[3]),c+=o.getCubicCurveTriangleCount(t[0],t[1],t[2],t[3]))})),o.setVertexCount(d),o.setTriangleCount(c),o.resetOffset();var u=[];n.each((function(e){var t=n.getItemLayout(e),i=Hn(n,e),r=Un(n,e),s=n.getItemVisual(e,"lineWidth")*h;null==r&&(r=1),(u=Pt.parseColor(i,u))[3]*=r,a?o.addPolyline(t,u,s):o.addCubicCurve(t[0],t[1],t[2],t[3],u,s)})),o.dirty()},remove:function(){this.groupGL.removeAll()},dispose:function(){this.groupGL.removeAll()}});function Jo(e,t){for(var i=[],n=0;n<t.length;n++)i.push(e.dataToPoint(t[n]));return i}m((function(e){e.registerChartView(Ko),e.registerSeriesModel(Xo),e.registerLayout(Zo),e.registerAction({type:"lines3DPauseEffect",event:"lines3deffectpaused",update:"series.lines3D:pauseEffect"},(function(){})),e.registerAction({type:"lines3DResumeEffect",event:"lines3deffectresumed",update:"series.lines3D:resumeEffect"},(function(){})),e.registerAction({type:"lines3DToggleEffect",event:"lines3deffectchanged",update:"series.lines3D:toggleEffect"},(function(){}))}));var $o=A.extend({type:"series.polygons3D",getRegionModel:function(e){return this.getData().getItemModel(e)},getRegionPolygonCoords:function(e){var t=this.coordinateSystem,i=this.getData().getItemModel(e),n=i.option instanceof Array?i.option:i.getShallow("coords");i.get("multiPolygon")||(n=[n]);for(var r=[],o=0;o<n.length;o++){for(var a=[],s=1;s<n[o].length;s++)a.push(Jo(t,n[o][s]));r.push({exterior:Jo(t,n[o][0]),interiors:a})}return r},getInitialData:function(e){var t=new v(["value"],this);return t.hasItemOption=!1,t.initData(e.data,[],(function(e,i,n,r){if(e instanceof Array)return NaN;t.hasItemOption=!0;var o=e.value;return null!=o?o instanceof Array?o[r]:o:void 0})),t},defaultOption:{show:!0,data:null,multiPolygon:!1,progressiveThreshold:1e3,progressive:1e3,zlevel:-10,label:{show:!1,distance:2,textStyle:{fontSize:20,color:"#000",backgroundColor:"rgba(255,255,255,0.7)",padding:3,borderRadius:4}},itemStyle:{color:"#fff",borderWidth:0,borderColor:"#333"},emphasis:{itemStyle:{color:"#639fc0"},label:{show:!0}}}});Ye($o.prototype,on);const ea=P.extend({type:"polygons3D",__ecgl__:!0,init:function(e,t){this.groupGL=new Pt.Node,this._geo3DBuilderList=[],this._currentStep=0},render:function(e,t,i){this.groupGL.removeAll();var n=e.coordinateSystem;n&&n.viewGL&&n.viewGL.add(this.groupGL);var r=this._geo3DBuilderList[0];r||((r=new Wn(i)).extrudeY="mapbox3D"!==n.type&&"maptalks3D"!==n.type,this._geo3DBuilderList[0]=r),this._updateShaderDefines(n,r),r.update(e,t,i),this._geo3DBuilderList.length=1,this.groupGL.add(r.rootNode)},incrementalPrepareRender:function(e,t,i){this.groupGL.removeAll();var n=e.coordinateSystem;n&&n.viewGL&&n.viewGL.add(this.groupGL),this._currentStep=0},incrementalRender:function(e,t,i,n){var r=this._geo3DBuilderList[this._currentStep],o=t.coordinateSystem;r||((r=new Wn(n)).extrudeY="mapbox3D"!==o.type&&"maptalks3D"!==o.type,this._geo3DBuilderList[this._currentStep]=r),r.update(t,i,n,e.start,e.end),this.groupGL.add(r.rootNode),this._updateShaderDefines(o,r),this._currentStep++},_updateShaderDefines:function(e,t){var i=e.viewGL.isLinearSpace()?"define":"undefine";t.rootNode.traverse((function(t){t.material&&(t.material[i]("fragment","SRGB_DECODE"),"mapbox3D"!==e.type&&"maptalks3D"!==e.type||(t.material.define("fragment","NORMAL_UP_AXIS",2),t.material.define("fragment","NORMAL_FRONT_AXIS",1)))}))},remove:function(){this.groupGL.removeAll()},dispose:function(){this.groupGL.removeAll(),this._geo3DBuilderList.forEach((function(e){e.dispose()}))}});m((function(e){e.registerChartView(ea),e.registerSeriesModel($o)}));var ta=A.extend({type:"series.surface",dependencies:["globe","grid3D","geo3D"],visualStyleAccessPath:"itemStyle",formatTooltip:function(e){return eo(this,e)},getInitialData:function(e,t){var i=e.data;function n(e){return!(isNaN(e.min)||isNaN(e.max)||isNaN(e.step))}function r(e){var t=l;return Math.max(t(e.min),t(e.max),t(e.step))+1}if(!i)if(e.parametric){var o=e.parametricEquation||{},a=o.u||{},s=o.v||{};["u","v"].forEach((function(e){n(o[e])})),["x","y","z"].forEach((function(e){o[e]}));var h=Math.floor((a.max+a.step-a.min)/a.step),d=Math.floor((s.max+s.step-s.min)/s.step);i=new Float32Array(h*d*5);var c=r(a),u=r(s);for(S=0,M=0;M<d;M++)for(D=0;D<h;D++){var f=D*a.step+a.min,p=M*s.step+s.min,m=N(Math.min(f,a.max),c),g=N(Math.min(p,s.max),u);L=o.x(m,g),C=o.y(m,g),E=o.z(m,g);i[S++]=L,i[S++]=C,i[S++]=E,i[S++]=m,i[S++]=g}}else{var _=e.equation||{},v=_.x||{},x=_.y||{};if(["x","y"].forEach((function(e){n(_[e])})),"function"!=typeof _.z)return;var y=Math.floor((v.max+v.step-v.min)/v.step),b=Math.floor((x.max+x.step-x.min)/x.step);i=new Float32Array(y*b*3);for(var w=r(v),T=r(x),S=0,M=0;M<b;M++)for(var D=0;D<y;D++){var L=D*v.step+v.min,C=M*x.step+x.min,A=N(Math.min(L,v.max),w),P=N(Math.min(C,x.max),T),E=_.z(A,P);i[S++]=A,i[S++]=P,i[S++]=E}}var O=["x","y","z"];return e.parametric&&O.push("u","v"),to(this,O,i)},defaultOption:{coordinateSystem:"cartesian3D",zlevel:-10,grid3DIndex:0,shading:"lambert",parametric:!1,wireframe:{show:!0,lineStyle:{color:"rgba(0,0,0,0.5)",width:1}},equation:{x:{min:-1,max:1,step:.1},y:{min:-1,max:1,step:.1},z:null},parametricEquation:{u:{min:-1,max:1,step:.1},v:{min:-1,max:1,step:.1},x:null,y:null,z:null},dataShape:null,itemStyle:{},animationDurationUpdate:500}});Ye(ta.prototype,on);var ia=we.vec3;const na=P.extend({type:"surface",__ecgl__:!0,init:function(e,t){this.groupGL=new Pt.Node},render:function(e,t,i){var n=this._prevSurfaceMesh;this._prevSurfaceMesh=this._surfaceMesh,this._surfaceMesh=n,this._surfaceMesh||(this._surfaceMesh=this._createSurfaceMesh()),this.groupGL.remove(this._prevSurfaceMesh),this.groupGL.add(this._surfaceMesh);var r=e.coordinateSystem,o=e.get("shading"),a=e.getData(),s="ecgl."+o;if(this._surfaceMesh.material&&this._surfaceMesh.material.shader.name===s||(this._surfaceMesh.material=Pt.createMaterial(s,["VERTEX_COLOR","DOUBLE_SIDED"])),Pt.setMaterialFromModel(o,this._surfaceMesh.material,e,i),r&&r.viewGL){r.viewGL.add(this.groupGL);var l=r.viewGL.isLinearSpace()?"define":"undefine";this._surfaceMesh.material[l]("fragment","SRGB_DECODE")}var h=e.get("parametric"),d=e.get("dataShape");d||(d=this._getDataShape(a,h));var c=e.getModel("wireframe"),u=c.get("lineStyle.width"),f=c.get("show")&&u>0;this._updateSurfaceMesh(this._surfaceMesh,e,d,f);var p=this._surfaceMesh.material;f?(p.define("WIREFRAME_QUAD"),p.set("wireframeLineWidth",u),p.set("wireframeLineColor",Pt.parseColor(c.get("lineStyle.color")))):p.undefine("WIREFRAME_QUAD"),this._initHandler(e,i),this._updateAnimation(e)},_updateAnimation:function(e){Pt.updateVertexAnimation([["prevPosition","position"],["prevNormal","normal"]],this._prevSurfaceMesh,this._surfaceMesh,e)},_createSurfaceMesh:function(){var e=new Pt.Mesh({geometry:new Pt.Geometry({dynamic:!0,sortTriangles:!0}),shadowDepthMaterial:new Pt.Material({shader:new Pt.Shader(Pt.Shader.source("ecgl.sm.depth.vertex"),Pt.Shader.source("ecgl.sm.depth.fragment"))}),culling:!1,renderOrder:10,renderNormal:!0});return e.geometry.createAttribute("barycentric","float",4),e.geometry.createAttribute("prevPosition","float",3),e.geometry.createAttribute("prevNormal","float",3),Object.assign(e.geometry,Gn),e},_initHandler:function(e,t){var i=e.getData(),n=this._surfaceMesh,r=e.coordinateSystem;n.seriesIndex=e.seriesIndex;var o=-1;n.off("mousemove"),n.off("mouseout"),n.on("mousemove",(function(e){var a=function(e,t){for(var i=Infinity,r=-1,o=[],a=0;a<e.length;a++){n.geometry.attributes.position.get(e[a],o);var s=ia.dist(t.array,o);s<i&&(i=s,r=e[a])}return r}(e.triangle,e.point);if(a>=0){var s=[];n.geometry.attributes.position.get(a,s);for(var l=r.pointToData(s),h=Infinity,d=-1,c=[],u=0;u<i.count();u++){c[0]=i.get("x",u),c[1]=i.get("y",u),c[2]=i.get("z",u);var f=ia.squaredDistance(c,l);f<h&&(d=u,h=f)}d!==o&&t.dispatchAction({type:"grid3DShowAxisPointer",value:l}),o=d,n.dataIndex=d}else n.dataIndex=-1}),this),n.on("mouseout",(function(e){o=-1,n.dataIndex=-1,t.dispatchAction({type:"grid3DHideAxisPointer"})}),this)},_updateSurfaceMesh:function(e,t,i,n){var r=e.geometry,o=t.getData(),a=o.getLayout("points"),s=0;o.each((function(e){o.hasValue(e)||s++}));var l=s||n,h=r.attributes.position,d=r.attributes.normal,c=r.attributes.texcoord0,u=r.attributes.barycentric,f=r.attributes.color,p=i[0],m=i[1],g="color"!==t.get("shading");if(l){var _=(p-1)*(m-1)*4;h.init(_),n&&u.init(_)}else h.value=new Float32Array(a);f.init(r.vertexCount),c.init(r.vertexCount);var v,x=[0,3,1,1,3,2],y=[[1,1,0,0],[0,1,0,1],[1,0,0,1],[1,0,1,0]],b=r.indices=new(r.vertexCount>65535?Uint32Array:Uint16Array)((p-1)*(m-1)*6),w=function(e,t,i){i[1]=e*m+t,i[0]=e*m+t+1,i[3]=(e+1)*m+t+1,i[2]=(e+1)*m+t},T=!1;if(l){var S=[],M=[],D=0;g?d.init(r.vertexCount):d.value=null;for(var L=[[],[],[]],C=[],A=[],P=ia.create(),E=function(e,t,i){var n=3*t;return i[0]=e[n],i[1]=e[n+1],i[2]=e[n+2],i},N=new Float32Array(a.length),O=new Float32Array(a.length/3*4),I=0;I<o.count();I++)if(o.hasValue(I)){var R=Pt.parseColor(Hn(o,I));null!=(Z=Un(o,I))&&(R[3]*=Z),R[3]<.99&&(T=!0);for(var z=0;z<4;z++)O[4*I+z]=R[z]}var F=[1e7,1e7,1e7];for(I=0;I<p-1;I++)for(var B=0;B<m-1;B++){var G=4*(I*(m-1)+B);w(I,B,S);var H=!1;for(z=0;z<4;z++)E(a,S[z],M),v=M,(isNaN(v[0])||isNaN(v[1])||isNaN(v[2]))&&(H=!0);for(z=0;z<4;z++)H?h.set(G+z,F):(E(a,S[z],M),h.set(G+z,M)),n&&u.set(G+z,y[z]);for(z=0;z<6;z++)b[D++]=x[z]+G;if(g&&!H)for(z=0;z<2;z++){for(var U=3*z,V=0;V<3;V++){E(a,j=S[x[U]+V],L[V])}ia.sub(C,L[0],L[1]),ia.sub(A,L[1],L[2]),ia.cross(P,C,A);for(V=0;V<3;V++){var k=3*S[x[U]+V];N[k]=N[k]+P[0],N[k+1]=N[k+1]+P[1],N[k+2]=N[k+2]+P[2]}}}if(g)for(I=0;I<N.length/3;I++)E(N,I,P),ia.normalize(P,P),N[3*I]=P[0],N[3*I+1]=P[1],N[3*I+2]=P[2];R=[];var W=[];for(I=0;I<p-1;I++)for(B=0;B<m-1;B++){G=4*(I*(m-1)+B);w(I,B,S);for(z=0;z<4;z++){for(V=0;V<4;V++)R[V]=O[4*S[z]+V];f.set(G+z,R),g&&(E(N,S[z],P),d.set(G+z,P));var j=S[z];W[0]=j%m/(m-1),W[1]=Math.floor(j/m)/(p-1),c.set(G+z,W)}0}}else{for(W=[],I=0;I<o.count();I++){W[0]=I%m/(m-1),W[1]=Math.floor(I/m)/(p-1);var Z;R=Pt.parseColor(Hn(o,I));null!=(Z=Un(o,I))&&(R[3]*=Z),R[3]<.99&&(T=!0),f.set(I,R),c.set(I,W)}S=[];var X=0;for(I=0;I<p-1;I++)for(B=0;B<m-1;B++){w(I,B,S);for(z=0;z<6;z++)b[X++]=S[x[z]]}g?r.generateVertexNormals():d.value=null}e.material.get("normalMap")&&r.generateTangents(),r.updateBoundingBox(),r.dirty(),e.material.transparent=T,e.material.depthMask=!T},_getDataShape:function(e,t){for(var i=-Infinity,n=0,r=0,o=!1,a=t?"u":"x",s=e.count(),l=0;l<s;l++){var h=e.get(a,l);h<i&&(r=0,n++),i=h,r++}if(n&&1!==r||(o=!0),!o)return[n+1,r];for(var d=Math.floor(Math.sqrt(s));d>0;){if(Math.floor(s/d)===s/d)return[d,s/d];d--}return[d=Math.floor(Math.sqrt(s)),d]},dispose:function(){this.groupGL.removeAll()},remove:function(){this.groupGL.removeAll()}});function ra(e,t){for(var i=[],n=0;n<t.length;n++)i.push(e.dataToPoint(t[n]));return i}m((function(e){e.registerChartView(na),e.registerSeriesModel(ta),e.registerLayout((function(e,t){e.eachSeriesByType("surface",(function(e){var t=e.coordinateSystem;!t||t.type;var i=e.getData(),n=new Float32Array(3*i.count()),r=[NaN,NaN,NaN];if(t&&"cartesian3D"===t.type){var o=t.dimensions.map((function(t){return e.coordDimToDataDim(t)[0]}));i.each(o,(function(e,o,a,s){var l;l=i.hasValue(s)?t.dataToPoint([e,o,a]):r,n[3*s]=l[0],n[3*s+1]=l[1],n[3*s+2]=l[2]}))}i.setLayout("points",n)}))}))}));var oa=A.extend({type:"series.map3D",layoutMode:"box",coordinateSystem:null,visualStyleAccessPath:"itemStyle",optionUpdated:function(e){var t=this.get("coordinateSystem");null!=t&&"geo3D"!==t&&(this.get("groundPlane.show")&&(this.option.groundPlane.show=!1),this._geo=null)},getInitialData:function(e){e.data=this.getFilledRegions(e.data,e.map);var t=_(e.data,{coordDimensions:["value"]}),i=new v(t,this);i.initData(e.data);var n={};return i.each((function(e){var t=i.getName(e),r=i.getItemModel(e);n[t]=r})),this._regionModelMap=n,i},formatTooltip:function(e){return eo(this,e)},getRegionModel:function(e){var t=this.getData().getName(e);return this._regionModelMap[t]||new o(null,this)},getRegionPolygonCoords:function(e){var t=this.coordinateSystem,i=this.getData().getName(e);if(t.transform)return(n=t.getRegion(i))?n.geometries:[];this._geo||(this._geo=$n.createGeo3D(this));for(var n=this._geo.getRegion(i),r=[],o=0;o<n.geometries.length;o++){var a=n.geometries[o],s=[],l=ra(t,a.exterior);if(s&&s.length)for(var h=0;h<a.interiors.length;h++)s.push(ra(t,s[h]));r.push({interiors:s,exterior:l})}return r},getFormattedLabel:function(e,t){var i=$r.getFormattedLabel(this,e,t);return null==i&&(i=this.getData().getName(e)),i},defaultOption:{coordinateSystem:"geo3D",data:null}});Ye(oa.prototype,an),Ye(oa.prototype,jt),Ye(oa.prototype,Zt),Ye(oa.prototype,Xt),Ye(oa.prototype,on);const aa=P.extend({type:"map3D",__ecgl__:!0,init:function(e,t){this._geo3DBuilder=new Wn(t),this.groupGL=new Pt.Node},render:function(e,t,i){var n=e.coordinateSystem;if(n&&n.viewGL){if(this.groupGL.add(this._geo3DBuilder.rootNode),n.viewGL.add(this.groupGL),"geo3D"===n.type){this._sceneHelper||(this._sceneHelper=new oi,this._sceneHelper.initLight(this.groupGL)),this._sceneHelper.setScene(n.viewGL.scene),this._sceneHelper.updateLight(e),n.viewGL.setPostEffect(e.getModel("postEffect"),i),n.viewGL.setTemporalSuperSampling(e.getModel("temporalSuperSampling"));var r=this._control;r||(r=this._control=new Jt({zr:i.getZr()}),this._control.init());var o=e.getModel("viewControl");r.setViewGL(n.viewGL),r.setFromViewControlModel(o,0),r.off("update"),r.on("update",(function(){i.dispatchAction({type:"map3DChangeCamera",alpha:r.getAlpha(),beta:r.getBeta(),distance:r.getDistance(),from:this.uid,map3DId:e.id})})),this._geo3DBuilder.extrudeY=!0}else this._control&&(this._control.dispose(),this._control=null),this._sceneHelper&&(this._sceneHelper.dispose(),this._sceneHelper=null),e.getData().getLayout("geo3D"),this._geo3DBuilder.extrudeY=!1;this._geo3DBuilder.update(e,t,i,0,e.getData().count());var a=n.viewGL.isLinearSpace()?"define":"undefine";this._geo3DBuilder.rootNode.traverse((function(e){e.material&&e.material[a]("fragment","SRGB_DECODE")}))}},afterRender:function(e,t,i,n){var r=n.renderer,o=e.coordinateSystem;o&&"geo3D"===o.type&&(this._sceneHelper.updateAmbientCubemap(r,e,i),this._sceneHelper.updateSkybox(r,e,i))},dispose:function(){this.groupGL.removeAll(),this._control.dispose(),this._geo3DBuilder.dispose()}});m((function(e){er(e),e.registerChartView(aa),e.registerSeriesModel(oa),e.registerAction({type:"map3DChangeCamera",event:"map3dcamerachanged",update:"series:updateCamera"},(function(e,t){t.eachComponent({mainType:"series",subType:"map3D",query:e},(function(t){t.setView(e)}))}))}));const sa=A.extend({type:"series.scatterGL",dependencies:["grid","polar","geo","singleAxis"],visualStyleAccessPath:"itemStyle",hasSymbolVisual:!0,getInitialData:function(){return O(this)},defaultOption:{coordinateSystem:"cartesian2d",zlevel:10,progressive:1e5,progressiveThreshold:1e5,large:!1,symbol:"circle",symbolSize:10,zoomScale:0,blendMode:"source-over",itemStyle:{opacity:.8},postEffect:{enable:!1,colorCorrection:{exposure:0,brightness:0,contrast:1,saturation:1,enable:!0}}}});function la(e){this.viewGL=e}la.prototype.reset=function(e,t){this._updateCamera(t.getWidth(),t.getHeight(),t.getDevicePixelRatio()),this._viewTransform=lt(),this.updateTransform(e,t)},la.prototype.updateTransform=function(e,t){var i=e.coordinateSystem;i.getRoamTransform&&(ht(this._viewTransform,i.getRoamTransform()),this._setCameraTransform(this._viewTransform),t.getZr().refresh())},la.prototype.dataToPoint=function(e,t,i){i=e.dataToPoint(t,null,i);var n=this._viewTransform;n&&dt(i,i,n)},la.prototype.removeTransformInPoint=function(e){return this._viewTransform&&dt(e,e,this._viewTransform),e},la.prototype.getZoom=function(){if(this._viewTransform){var e=this._viewTransform;return 1/Math.max(Math.sqrt(e[0]*e[0]+e[1]*e[1]),Math.sqrt(e[2]*e[2]+e[3]*e[3]))}return 1},la.prototype._setCameraTransform=function(e){var t=this.viewGL.camera;t.position.set(e[4],e[5],0),t.scale.set(Math.sqrt(e[0]*e[0]+e[1]*e[1]),Math.sqrt(e[2]*e[2]+e[3]*e[3]),1)},la.prototype._updateCamera=function(e,t,i){this.viewGL.setViewport(0,0,e,t,i);var n=this.viewGL.camera;n.left=n.top=0,n.bottom=t,n.right=e,n.near=0,n.far=100};const ha=P.extend({type:"scatterGL",__ecgl__:!0,init:function(e,t){this.groupGL=new Pt.Node,this.viewGL=new Wi("orthographic"),this.viewGL.add(this.groupGL),this._pointsBuilderList=[],this._currentStep=0,this._sizeScale=1,this._glViewHelper=new la(this.viewGL)},render:function(e,t,i){if(this.groupGL.removeAll(),this._glViewHelper.reset(e,i),e.getData().count()){var n=this._pointsBuilderList[0];n||(n=this._pointsBuilderList[0]=new Lo(!0,i)),this._pointsBuilderList.length=1,this.groupGL.add(n.rootNode),this._removeTransformInPoints(e.getData().getLayout("points")),n.update(e,t,i),this.viewGL.setPostEffect(e.getModel("postEffect"),i)}},incrementalPrepareRender:function(e,t,i){this.groupGL.removeAll(),this._glViewHelper.reset(e,i),this._currentStep=0,this.viewGL.setPostEffect(e.getModel("postEffect"),i)},incrementalRender:function(e,t,i,n){if(!(e.end<=e.start)){var r=this._pointsBuilderList[this._currentStep];r||(r=new Lo(!0,n),this._pointsBuilderList[this._currentStep]=r),this.groupGL.add(r.rootNode),this._removeTransformInPoints(t.getData().getLayout("points")),r.setSizeScale(this._sizeScale),r.update(t,i,n,e.start,e.end),n.getZr().refresh(),this._currentStep++}},updateTransform:function(e,t,i){if(e.coordinateSystem.getRoamTransform){this._glViewHelper.updateTransform(e,i);var n=this._glViewHelper.getZoom(),r=Math.max((e.get("zoomScale")||0)*(n-1)+1,0);this._sizeScale=r,this._pointsBuilderList.forEach((function(e){e.setSizeScale(r)}))}},_removeTransformInPoints:function(e){if(e)for(var t=[],i=0;i<e.length;i+=2)t[0]=e[i],t[1]=e[i+1],this._glViewHelper.removeTransformInPoint(t),e[i]=t[0],e[i+1]=t[1]},dispose:function(){this.groupGL.removeAll(),this._pointsBuilderList.forEach((function(e){e.dispose()}))},remove:function(){this.groupGL.removeAll()}});m((function(e){e.registerChartView(ha),e.registerSeriesModel(sa),e.registerLayout({seriesType:"scatterGL",reset:function(e){var t,i=e.coordinateSystem,n=e.getData();if(i){var r=i.dimensions.map((function(e){return n.mapDimension(e)})).slice(0,2),o=[];1===r.length?t=function(e){for(var t=new Float32Array(2*(e.end-e.start)),o=e.start;o<e.end;o++){var a=2*(o-e.start),s=n.get(r[0],o),l=i.dataToPoint(s);t[a]=l[0],t[a+1]=l[1]}n.setLayout("points",t)}:2===r.length&&(t=function(e){for(var t=new Float32Array(2*(e.end-e.start)),a=e.start;a<e.end;a++){var s=2*(a-e.start),l=n.get(r[0],a),h=n.get(r[1],a);o[0]=l,o[1]=h,o=i.dataToPoint(o),t[s]=o[0],t[s+1]=o[1]}n.setLayout("points",t)})}return{progress:t}}})}));var da=A.extend({type:"series.graphGL",visualStyleAccessPath:"itemStyle",hasSymbolVisual:!0,init:function(e){da.superApply(this,"init",arguments),this.legendDataProvider=function(){return this._categoriesData},this._updateCategoriesData()},mergeOption:function(e){da.superApply(this,"mergeOption",arguments),this._updateCategoriesData()},getFormattedLabel:function(e,t,i,n){var r=$r.getFormattedLabel(this,e,t,i,n);if(null==r){var o=this.getData(),a=o.dimensions[o.dimensions.length-1];r=o.get(a,e)}return r},getInitialData:function(e,t){var i=e.edges||e.links||[],n=e.data||e.nodes||[],r=this;if(n&&i)return function(e,t,i,n,r){for(var o=new I(n),a=0;a<e.length;a++)o.addNode(St(e[a].id,e[a].name,a),a);var s,l=[],h=[],d=0;for(a=0;a<t.length;a++){var c=t[a],u=c.source,f=c.target;o.addEdge(u,f,d)&&(h.push(c),l.push(St(c.id,u+" > "+f)),d++)}var p=_(e,{coordDimensions:["value"]});(s=new v(p,i)).initData(e);var m=new v(["value"],i);return m.initData(h,l),r&&r(s,m),R({mainData:s,struct:o,structAttr:"graph",datas:{node:s,edge:m},datasAttr:{node:"data",edge:"edgeData"}}),o.update(),o}(n,i,this,!0,(function(e,i){e.wrapMethod("getItemModel",(function(e){const t=r._categoriesModels[e.getShallow("category")];return t&&(t.parentModel=e.parentModel,e.parentModel=t),e}));const n=t.getModel([]).getModel;function o(e,t){const i=n.call(this,e,t);return i.resolveParentPath=a,i}function a(e){if(e&&("label"===e[0]||"label"===e[1])){const t=e.slice();return"label"===e[0]?t[0]="edgeLabel":"label"===e[1]&&(t[1]="edgeLabel"),t}return e}i.wrapMethod("getItemModel",(function(e){return e.resolveParentPath=a,e.getModel=o,e}))})).data},getGraph:function(){return this.getData().graph},getEdgeData:function(){return this.getGraph().edgeData},getCategoriesData:function(){return this._categoriesData},formatTooltip:function(e,t,i){if("edge"===i){var n=this.getData(),r=this.getDataParams(e,i),o=n.graph.getEdgeByIndex(e),a=n.getName(o.node1.dataIndex),s=n.getName(o.node2.dataIndex),l=[];return null!=a&&l.push(a),null!=s&&l.push(s),l=ot(l.join(" > ")),r.value&&(l+=" : "+ot(r.value)),l}return da.superApply(this,"formatTooltip",arguments)},_updateCategoriesData:function(){var e=(this.option.categories||[]).map((function(e){return null!=e.value?e:Object.assign({value:0},e)})),t=new v(["value"],this);t.initData(e),this._categoriesData=t,this._categoriesModels=t.mapArray((function(e){return t.getItemModel(e,!0)}))},setView:function(e){null!=e.zoom&&(this.option.zoom=e.zoom),null!=e.offset&&(this.option.offset=e.offset)},setNodePosition:function(e){for(var t=0;t<e.length/2;t++){var i=e[2*t],n=e[2*t+1],r=this.getData().getRawDataItem(t);r.x=i,r.y=n}},isAnimationEnabled:function(){return da.superCall(this,"isAnimationEnabled")&&!("force"===this.get("layout")&&this.get("force.layoutAnimation"))},defaultOption:{zlevel:10,z:2,legendHoverLink:!0,layout:"forceAtlas2",forceAtlas2:{initLayout:null,GPU:!0,steps:1,maxSteps:1e3,repulsionByDegree:!0,linLogMode:!1,strongGravityMode:!1,gravity:1,edgeWeightInfluence:1,edgeWeight:[1,4],nodeWeight:[1,4],preventOverlap:!1,gravityCenter:null},focusNodeAdjacency:!0,focusNodeAdjacencyOn:"mouseover",left:"center",top:"center",symbol:"circle",symbolSize:5,roam:!1,center:null,zoom:1,label:{show:!1,formatter:"{b}",position:"right",distance:5,textStyle:{fontSize:14}},itemStyle:{},lineStyle:{color:"#aaa",width:1,opacity:.5},emphasis:{label:{show:!0}},animation:!1}}),ca=we.vec2,ua=[[0,0],[1,1]],fa=q.extend((function(){return{segmentScale:4,dynamic:!0,useNativeLine:!0,attributes:{position:new q.Attribute("position","float",2,"POSITION"),normal:new q.Attribute("normal","float",2),offset:new q.Attribute("offset","float",1),color:new q.Attribute("color","float",4,"COLOR")}}}),{resetOffset:function(){this._vertexOffset=0,this._faceOffset=0,this._itemVertexOffsets=[]},setVertexCount:function(e){var t=this.attributes;this.vertexCount!==e&&(t.position.init(e),t.color.init(e),this.useNativeLine||(t.offset.init(e),t.normal.init(e)),e>65535?this.indices instanceof Uint16Array&&(this.indices=new Uint32Array(this.indices)):this.indices instanceof Uint32Array&&(this.indices=new Uint16Array(this.indices)))},setTriangleCount:function(e){this.triangleCount!==e&&(this.indices=0===e?null:this.vertexCount>65535?new Uint32Array(3*e):new Uint16Array(3*e))},_getCubicCurveApproxStep:function(e,t,i,n){return 1/(ca.dist(e,t)+ca.dist(i,t)+ca.dist(n,i)+1)*this.segmentScale},getCubicCurveVertexCount:function(e,t,i,n){var r=this._getCubicCurveApproxStep(e,t,i,n),o=Math.ceil(1/r);return this.useNativeLine?2*o:2*o+2},getCubicCurveTriangleCount:function(e,t,i,n){var r=this._getCubicCurveApproxStep(e,t,i,n),o=Math.ceil(1/r);return this.useNativeLine?0:2*o},getLineVertexCount:function(){return this.getPolylineVertexCount(ua)},getLineTriangleCount:function(){return this.getPolylineTriangleCount(ua)},getPolylineVertexCount:function(e){var t;"number"==typeof e?t=e:t="number"!=typeof e[0]?e.length:e.length/2;return this.useNativeLine?2*(t-1):2*(t-1)+2},getPolylineTriangleCount:function(e){var t;"number"==typeof e?t=e:t="number"!=typeof e[0]?e.length:e.length/2;return this.useNativeLine?0:2*(t-1)},addCubicCurve:function(e,t,i,n,r,o){null==o&&(o=1);var a=e[0],s=e[1],l=t[0],h=t[1],d=i[0],c=i[1],u=n[0],f=n[1],p=this._getCubicCurveApproxStep(e,t,i,n),m=p*p,g=m*p,_=3*p,v=3*m,x=6*m,y=6*g,b=a-2*l+d,w=s-2*h+c,T=3*(l-d)-a+u,S=3*(h-c)-s+f,M=a,D=s,L=(l-a)*_+b*v+T*g,C=(h-s)*_+w*v+S*g,A=b*x+T*y,P=w*x+S*y,E=T*y,N=S*y,O=0,I=0,R=Math.ceil(1/p),z=new Float32Array(3*(R+1)),F=(z=[],0);for(I=0;I<R+1;I++)z[F++]=M,z[F++]=D,M+=L,D+=C,L+=A,C+=P,A+=E,P+=N,(O+=p)>1&&(M=L>0?Math.min(M,u):Math.max(M,u),D=C>0?Math.min(D,f):Math.max(D,f));this.addPolyline(z,r,o)},addLine:function(e,t,i,n){this.addPolyline([e,t],i,n)},addPolyline:function(){var e=ca.create(),t=ca.create(),i=ca.create(),n=ca.create(),r=[],o=[],a=[];return function(s,l,h,d,c){if(s.length){var u="number"!=typeof s[0];if(null==c&&(c=u?s.length:s.length/2),!(c<2)){null==d&&(d=0),null==h&&(h=1),this._itemVertexOffsets.push(this._vertexOffset);for(var f,p=u?"number"!=typeof l[0]:l.length/4===c,m=this.attributes.position,g=this.attributes.color,_=this.attributes.offset,v=this.attributes.normal,x=this.indices,y=this._vertexOffset,b=0;b<c;b++){if(u)r=s[b+d],f=p?l[b+d]:l;else{var w=2*b+d;if((r=r||[])[0]=s[w],r[1]=s[w+1],p){var T=4*b+d;(f=f||[])[0]=l[T],f[1]=l[T+1],f[2]=l[T+2],f[3]=l[T+3]}else f=l}if(this.useNativeLine)b>1&&(m.copy(y,y-1),g.copy(y,y-1),y++);else{var S;if(b<c-1){if(u)ca.copy(o,s[b+1]);else{w=2*(b+1)+d;(o=o||[])[0]=s[w],o[1]=s[w+1]}if(b>0){ca.sub(e,r,a),ca.sub(t,o,r),ca.normalize(e,e),ca.normalize(t,t),ca.add(n,e,t),ca.normalize(n,n);var M=h/2*Math.min(1/ca.dot(e,n),2);i[0]=-n[1],i[1]=n[0],S=M}else ca.sub(e,o,r),ca.normalize(e,e),i[0]=-e[1],i[1]=e[0],S=h/2}else ca.sub(e,r,a),ca.normalize(e,e),i[0]=-e[1],i[1]=e[0],S=h/2;v.set(y,i),v.set(y+1,i),_.set(y,S),_.set(y+1,-S),ca.copy(a,r),m.set(y,r),m.set(y+1,r),g.set(y,f),g.set(y+1,f),y+=2}if(this.useNativeLine)g.set(y,f),m.set(y,r),y++;else if(b>0){var D=3*this._faceOffset;(x=this.indices)[D]=y-4,x[D+1]=y-3,x[D+2]=y-2,x[D+3]=y-3,x[D+4]=y-1,x[D+5]=y-2,this._faceOffset+=2}}this._vertexOffset=y}}}}(),setItemColor:function(e,t){for(var i=this._itemVertexOffsets[e],n=e<this._itemVertexOffsets.length-1?this._itemVertexOffsets[e+1]:this._vertexOffset,r=i;r<n;r++)this.attributes.color.set(r,t);this.dirty("color")}});qe(fa.prototype,$t);Pt.Shader.import("@export ecgl.forceAtlas2.updateNodeRepulsion\n\n#define NODE_COUNT 0\n\nuniform sampler2D positionTex;\n\nuniform vec2 textureSize;\nuniform float gravity;\nuniform float scaling;\nuniform vec2 gravityCenter;\n\nuniform bool strongGravityMode;\nuniform bool preventOverlap;\n\nvarying vec2 v_Texcoord;\n\nvoid main() {\n\n vec4 n0 = texture2D(positionTex, v_Texcoord);\n\n vec2 force = vec2(0.0);\n for (int i = 0; i < NODE_COUNT; i++) {\n vec2 uv = vec2(\n mod(float(i), textureSize.x) / (textureSize.x - 1.0),\n floor(float(i) / textureSize.x) / (textureSize.y - 1.0)\n );\n vec4 n1 = texture2D(positionTex, uv);\n\n vec2 dir = n0.xy - n1.xy;\n float d2 = dot(dir, dir);\n\n if (d2 > 0.0) {\n float factor = 0.0;\n if (preventOverlap) {\n float d = sqrt(d2);\n d = d - n0.w - n1.w;\n if (d > 0.0) {\n factor = scaling * n0.z * n1.z / (d * d);\n }\n else if (d < 0.0) {\n factor = scaling * 100.0 * n0.z * n1.z;\n }\n }\n else {\n factor = scaling * n0.z * n1.z / d2;\n }\n force += dir * factor;\n }\n }\n\n vec2 dir = gravityCenter - n0.xy;\n float d = 1.0;\n if (!strongGravityMode) {\n d = length(dir);\n }\n\n force += dir * n0.z * gravity / (d + 1.0);\n\n gl_FragColor = vec4(force, 0.0, 1.0);\n}\n@end\n\n@export ecgl.forceAtlas2.updateEdgeAttraction.vertex\n\nattribute vec2 node1;\nattribute vec2 node2;\nattribute float weight;\n\nuniform sampler2D positionTex;\nuniform float edgeWeightInfluence;\nuniform bool preventOverlap;\nuniform bool linLogMode;\n\nuniform vec2 windowSize: WINDOW_SIZE;\n\nvarying vec2 v_Force;\n\nvoid main() {\n\n vec4 n0 = texture2D(positionTex, node1);\n vec4 n1 = texture2D(positionTex, node2);\n\n vec2 dir = n1.xy - n0.xy;\n float d = length(dir);\n float w;\n if (edgeWeightInfluence == 0.0) {\n w = 1.0;\n }\n else if (edgeWeightInfluence == 1.0) {\n w = weight;\n }\n else {\n w = pow(weight, edgeWeightInfluence);\n }\n vec2 offset = vec2(1.0 / windowSize.x, 1.0 / windowSize.y);\n vec2 scale = vec2((windowSize.x - 1.0) / windowSize.x, (windowSize.y - 1.0) / windowSize.y);\n vec2 pos = node1 * scale * 2.0 - 1.0;\n gl_Position = vec4(pos + offset, 0.0, 1.0);\n gl_PointSize = 1.0;\n\n float factor;\n if (preventOverlap) {\n d = d - n1.w - n0.w;\n }\n if (d <= 0.0) {\n v_Force = vec2(0.0);\n return;\n }\n\n if (linLogMode) {\n factor = w * log(d) / d;\n }\n else {\n factor = w;\n }\n v_Force = dir * factor;\n}\n@end\n\n@export ecgl.forceAtlas2.updateEdgeAttraction.fragment\n\nvarying vec2 v_Force;\n\nvoid main() {\n gl_FragColor = vec4(v_Force, 0.0, 0.0);\n}\n@end\n\n@export ecgl.forceAtlas2.calcWeightedSum.vertex\n\nattribute vec2 node;\n\nvarying vec2 v_NodeUv;\n\nvoid main() {\n\n v_NodeUv = node;\n gl_Position = vec4(0.0, 0.0, 0.0, 1.0);\n gl_PointSize = 1.0;\n}\n@end\n\n@export ecgl.forceAtlas2.calcWeightedSum.fragment\n\nvarying vec2 v_NodeUv;\n\nuniform sampler2D positionTex;\nuniform sampler2D forceTex;\nuniform sampler2D forcePrevTex;\n\nvoid main() {\n vec2 force = texture2D(forceTex, v_NodeUv).rg;\n vec2 forcePrev = texture2D(forcePrevTex, v_NodeUv).rg;\n\n float mass = texture2D(positionTex, v_NodeUv).z;\n float swing = length(force - forcePrev) * mass;\n float traction = length(force + forcePrev) * 0.5 * mass;\n\n gl_FragColor = vec4(swing, traction, 0.0, 0.0);\n}\n@end\n\n@export ecgl.forceAtlas2.calcGlobalSpeed\n\nuniform sampler2D globalSpeedPrevTex;\nuniform sampler2D weightedSumTex;\nuniform float jitterTolerence;\n\nvoid main() {\n vec2 weightedSum = texture2D(weightedSumTex, vec2(0.5)).xy;\n float prevGlobalSpeed = texture2D(globalSpeedPrevTex, vec2(0.5)).x;\n float globalSpeed = jitterTolerence * jitterTolerence\n * weightedSum.y / weightedSum.x;\n if (prevGlobalSpeed > 0.0) {\n globalSpeed = min(globalSpeed / prevGlobalSpeed, 1.5) * prevGlobalSpeed;\n }\n gl_FragColor = vec4(globalSpeed, 0.0, 0.0, 1.0);\n}\n@end\n\n@export ecgl.forceAtlas2.updatePosition\n\nuniform sampler2D forceTex;\nuniform sampler2D forcePrevTex;\nuniform sampler2D positionTex;\nuniform sampler2D globalSpeedTex;\n\nvarying vec2 v_Texcoord;\n\nvoid main() {\n vec2 force = texture2D(forceTex, v_Texcoord).xy;\n vec2 forcePrev = texture2D(forcePrevTex, v_Texcoord).xy;\n vec4 node = texture2D(positionTex, v_Texcoord);\n\n float globalSpeed = texture2D(globalSpeedTex, vec2(0.5)).r;\n float swing = length(force - forcePrev);\n float speed = 0.1 * globalSpeed / (0.1 + globalSpeed * sqrt(swing));\n\n float df = length(force);\n if (df > 0.0) {\n speed = min(df * speed, 10.0) / df;\n\n gl_FragColor = vec4(node.xy + speed * force, node.zw);\n }\n else {\n gl_FragColor = node;\n }\n}\n@end\n\n@export ecgl.forceAtlas2.edges.vertex\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\n\nattribute vec2 node;\nattribute vec4 a_Color : COLOR;\nvarying vec4 v_Color;\n\nuniform sampler2D positionTex;\n\nvoid main()\n{\n gl_Position = worldViewProjection * vec4(\n texture2D(positionTex, node).xy, -10.0, 1.0\n );\n v_Color = a_Color;\n}\n@end\n\n@export ecgl.forceAtlas2.edges.fragment\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\nvarying vec4 v_Color;\nvoid main() {\n gl_FragColor = color * v_Color;\n}\n@end");var pa={repulsionByDegree:!0,linLogMode:!1,strongGravityMode:!1,gravity:1,scaling:1,edgeWeightInfluence:1,jitterTolerence:.1,preventOverlap:!1,dissuadeHubs:!1,gravityCenter:null};function ma(e){var t={type:Pt.Texture.FLOAT,minFilter:Pt.Texture.NEAREST,magFilter:Pt.Texture.NEAREST};this._positionSourceTex=new Pt.Texture2D(t),this._positionSourceTex.flipY=!1,this._positionTex=new Pt.Texture2D(t),this._positionPrevTex=new Pt.Texture2D(t),this._forceTex=new Pt.Texture2D(t),this._forcePrevTex=new Pt.Texture2D(t),this._weightedSumTex=new Pt.Texture2D(t),this._weightedSumTex.width=this._weightedSumTex.height=1,this._globalSpeedTex=new Pt.Texture2D(t),this._globalSpeedPrevTex=new Pt.Texture2D(t),this._globalSpeedTex.width=this._globalSpeedTex.height=1,this._globalSpeedPrevTex.width=this._globalSpeedPrevTex.height=1,this._nodeRepulsionPass=new Se({fragment:Pt.Shader.source("ecgl.forceAtlas2.updateNodeRepulsion")}),this._positionPass=new Se({fragment:Pt.Shader.source("ecgl.forceAtlas2.updatePosition")}),this._globalSpeedPass=new Se({fragment:Pt.Shader.source("ecgl.forceAtlas2.calcGlobalSpeed")}),this._copyPass=new Se({fragment:Pt.Shader.source("clay.compositor.output")});var i=function(e){e.blendEquation(e.FUNC_ADD),e.blendFunc(e.ONE,e.ONE)};this._edgeForceMesh=new Pt.Mesh({geometry:new Pt.Geometry({attributes:{node1:new Pt.Geometry.Attribute("node1","float",2),node2:new Pt.Geometry.Attribute("node2","float",2),weight:new Pt.Geometry.Attribute("weight","float",1)},dynamic:!0,mainAttribute:"node1"}),material:new Pt.Material({transparent:!0,shader:Pt.createShader("ecgl.forceAtlas2.updateEdgeAttraction"),blend:i,depthMask:!1,depthText:!1}),mode:Pt.Mesh.POINTS}),this._weightedSumMesh=new Pt.Mesh({geometry:new Pt.Geometry({attributes:{node:new Pt.Geometry.Attribute("node","float",2)},dynamic:!0,mainAttribute:"node"}),material:new Pt.Material({transparent:!0,shader:Pt.createShader("ecgl.forceAtlas2.calcWeightedSum"),blend:i,depthMask:!1,depthText:!1}),mode:Pt.Mesh.POINTS}),this._framebuffer=new Me({depthBuffer:!1}),this._dummyCamera=new Pt.OrthographicCamera({left:-1,right:1,top:1,bottom:-1,near:0,far:100}),this._globalSpeed=0}ma.prototype.updateOption=function(e){for(var t in pa)this[t]=pa[t];var i=this._nodes.length;if(this.jitterTolerence=i>5e4?10:i>5e3?1:.1,this.scaling=i>100?2:10,e)for(var t in pa)null!=e[t]&&(this[t]=e[t]);if(this.repulsionByDegree)for(var n=this._positionSourceTex.pixels,r=0;r<this._nodes.length;r++)n[4*r+2]=(this._nodes[r].degree||0)+1},ma.prototype._updateGravityCenter=function(e){var t=this._nodes,i=this._edges;if(this.gravityCenter)this._gravityCenter=this.gravityCenter;else{for(var n=[Infinity,Infinity],r=[-Infinity,-Infinity],o=0;o<t.length;o++)n[0]=Math.min(t[o].x,n[0]),n[1]=Math.min(t[o].y,n[1]),r[0]=Math.max(t[o].x,r[0]),r[1]=Math.max(t[o].y,r[1]);this._gravityCenter=[.5*(n[0]+r[0]),.5*(n[1]+r[1])]}for(o=0;o<i.length;o++){var a=i[o].node1,s=i[o].node2;t[a].degree=(t[a].degree||0)+1,t[s].degree=(t[s].degree||0)+1}},ma.prototype.initData=function(e,t){this._nodes=e,this._edges=t,this._updateGravityCenter();var i=Math.ceil(Math.sqrt(e.length)),n=i,r=new Float32Array(i*n*4);this._resize(i,n);for(var o=0,a=0;a<e.length;a++){var s=e[a];r[o++]=s.x||0,r[o++]=s.y||0,r[o++]=s.mass||1,r[o++]=s.size||1}this._positionSourceTex.pixels=r;var l=this._edgeForceMesh.geometry,h=t.length;l.attributes.node1.init(2*h),l.attributes.node2.init(2*h),l.attributes.weight.init(2*h);var d=[];for(a=0;a<t.length;a++){var c=l.attributes,u=t[a].weight;null==u&&(u=1),c.node1.set(a,this.getNodeUV(t[a].node1,d)),c.node2.set(a,this.getNodeUV(t[a].node2,d)),c.weight.set(a,u),c.node1.set(a+h,this.getNodeUV(t[a].node2,d)),c.node2.set(a+h,this.getNodeUV(t[a].node1,d)),c.weight.set(a+h,u)}var f=this._weightedSumMesh.geometry;f.attributes.node.init(e.length);for(a=0;a<e.length;a++)f.attributes.node.set(a,this.getNodeUV(a,d));l.dirty(),f.dirty(),this._nodeRepulsionPass.material.define("fragment","NODE_COUNT",e.length),this._nodeRepulsionPass.material.setUniform("textureSize",[i,n]),this._inited=!1,this._frame=0},ma.prototype.getNodes=function(){return this._nodes},ma.prototype.getEdges=function(){return this._edges},ma.prototype.step=function(e){this._inited||(this._initFromSource(e),this._inited=!0),this._frame++,this._framebuffer.attach(this._forceTex),this._framebuffer.bind(e);var t=this._nodeRepulsionPass;t.setUniform("strongGravityMode",this.strongGravityMode),t.setUniform("gravity",this.gravity),t.setUniform("gravityCenter",this._gravityCenter),t.setUniform("scaling",this.scaling),t.setUniform("preventOverlap",this.preventOverlap),t.setUniform("positionTex",this._positionPrevTex),t.render(e);var i=this._edgeForceMesh;i.material.set("linLogMode",this.linLogMode),i.material.set("edgeWeightInfluence",this.edgeWeightInfluence),i.material.set("preventOverlap",this.preventOverlap),i.material.set("positionTex",this._positionPrevTex),e.gl.enable(e.gl.BLEND),e.renderPass([i],this._dummyCamera),this._framebuffer.attach(this._weightedSumTex),e.gl.clearColor(0,0,0,0),e.gl.clear(e.gl.COLOR_BUFFER_BIT),e.gl.enable(e.gl.BLEND);var n=this._weightedSumMesh;n.material.set("positionTex",this._positionPrevTex),n.material.set("forceTex",this._forceTex),n.material.set("forcePrevTex",this._forcePrevTex),e.renderPass([n],this._dummyCamera),this._framebuffer.attach(this._globalSpeedTex);var r=this._globalSpeedPass;r.setUniform("globalSpeedPrevTex",this._globalSpeedPrevTex),r.setUniform("weightedSumTex",this._weightedSumTex),r.setUniform("jitterTolerence",this.jitterTolerence),e.gl.disable(e.gl.BLEND),r.render(e);var o=this._positionPass;this._framebuffer.attach(this._positionTex),o.setUniform("globalSpeedTex",this._globalSpeedTex),o.setUniform("positionTex",this._positionPrevTex),o.setUniform("forceTex",this._forceTex),o.setUniform("forcePrevTex",this._forcePrevTex),o.render(e),this._framebuffer.unbind(e),this._swapTexture()},ma.prototype.update=function(e,t,i){null==t&&(t=1),t=Math.max(t,1);for(var n=0;n<t;n++)this.step(e);i&&i()},ma.prototype.getNodePositionTexture=function(){return this._inited?this._positionPrevTex:this._positionSourceTex},ma.prototype.getNodeUV=function(e,t){t=t||[];var i=this._positionTex.width,n=this._positionTex.height;return t[0]=e%i/(i-1),t[1]=Math.floor(e/i)/(n-1)||0,t},ma.prototype.getNodePosition=function(e,t){var i=this._positionArr,n=this._positionTex.width,r=this._positionTex.height,o=n*r;i&&i.length===4*o||(i=this._positionArr=new Float32Array(4*o)),this._framebuffer.bind(e),this._framebuffer.attach(this._positionPrevTex),e.gl.readPixels(0,0,n,r,e.gl.RGBA,e.gl.FLOAT,i),this._framebuffer.unbind(e),t||(t=new Float32Array(2*this._nodes.length));for(var a=0;a<this._nodes.length;a++)t[2*a]=i[4*a],t[2*a+1]=i[4*a+1];return t},ma.prototype.getTextureData=function(e,t){var i=this["_"+t+"Tex"],n=i.width,r=i.height;this._framebuffer.bind(e),this._framebuffer.attach(i);var o=new Float32Array(n*r*4);return e.gl.readPixels(0,0,n,r,e.gl.RGBA,e.gl.FLOAT,o),this._framebuffer.unbind(e),o},ma.prototype.getTextureSize=function(){return{width:this._positionTex.width,height:this._positionTex.height}},ma.prototype.isFinished=function(e){return this._frame>e},ma.prototype._swapTexture=function(){var e=this._positionPrevTex;this._positionPrevTex=this._positionTex,this._positionTex=e;e=this._forcePrevTex;this._forcePrevTex=this._forceTex,this._forceTex=e;e=this._globalSpeedPrevTex;this._globalSpeedPrevTex=this._globalSpeedTex,this._globalSpeedTex=e},ma.prototype._initFromSource=function(e){this._framebuffer.attach(this._positionPrevTex),this._framebuffer.bind(e),this._copyPass.setUniform("texture",this._positionSourceTex),this._copyPass.render(e),e.gl.clearColor(0,0,0,0),this._framebuffer.attach(this._forcePrevTex),e.gl.clear(e.gl.COLOR_BUFFER_BIT),this._framebuffer.attach(this._globalSpeedPrevTex),e.gl.clear(e.gl.COLOR_BUFFER_BIT),this._framebuffer.unbind(e)},ma.prototype._resize=function(e,t){["_positionSourceTex","_positionTex","_positionPrevTex","_forceTex","_forcePrevTex"].forEach((function(i){this[i].width=e,this[i].height=t,this[i].dirty()}),this)},ma.prototype.dispose=function(e){this._framebuffer.dispose(e),this._copyPass.dispose(e),this._nodeRepulsionPass.dispose(e),this._positionPass.dispose(e),this._globalSpeedPass.dispose(e),this._edgeForceMesh.geometry.dispose(e),this._weightedSumMesh.geometry.dispose(e),this._positionSourceTex.dispose(e),this._positionTex.dispose(e),this._positionPrevTex.dispose(e),this._forceTex.dispose(e),this._forcePrevTex.dispose(e),this._weightedSumTex.dispose(e),this._globalSpeedTex.dispose(e),this._globalSpeedPrevTex.dispose(e)};var ga=function(){var e=function(){return new Float32Array(2)},t=function(e,t){var i=t[0]-e[0],n=t[1]-e[1];return Math.sqrt(i*i+n*n)},i=function(e){var t=e[0],i=e[1];return Math.sqrt(t*t+i*i)},n=function(e,t,i,n){return e[0]=t[0]+i[0]*n,e[1]=t[1]+i[1]*n,e},r=function(e,t,i){return e[0]=t[0]+i[0],e[1]=t[1]+i[1],e},o=function(e,t,i){return e[0]=t[0]-i[0],e[1]=t[1]-i[1],e},a=function(e,t){return e[0]=t[0],e[1]=t[1],e},s=function(e,t,i){return e[0]=t,e[1]=i,e};function l(){this.subRegions=[],this.nSubRegions=0,this.node=null,this.mass=0,this.centerOfMass=null,this.bbox=new Float32Array(4),this.size=0}var h=l.prototype;function d(){this.position=new Float32Array(2),this.force=e(),this.forcePrev=e(),this.mass=1,this.inDegree=0,this.outDegree=0}function c(e,t){this.source=e,this.target=t,this.weight=1}function u(){this.autoSettings=!0,this.barnesHutOptimize=!0,this.barnesHutTheta=1.5,this.repulsionByDegree=!0,this.linLogMode=!1,this.strongGravityMode=!1,this.gravity=1,this.scaling=1,this.edgeWeightInfluence=1,this.jitterTolerence=.1,this.preventOverlap=!1,this.dissuadeHubs=!1,this.rootRegion=new l,this.rootRegion.centerOfMass=e(),this.nodes=[],this.edges=[],this.bbox=new Float32Array(4),this.gravityCenter=null,this._massArr=null,this._swingingArr=null,this._sizeArr=null,this._globalSpeed=0}h.beforeUpdate=function(){for(var e=0;e<this.nSubRegions;e++)this.subRegions[e].beforeUpdate();this.mass=0,this.centerOfMass&&(this.centerOfMass[0]=0,this.centerOfMass[1]=0),this.nSubRegions=0,this.node=null},h.afterUpdate=function(){this.subRegions.length=this.nSubRegions;for(var e=0;e<this.nSubRegions;e++)this.subRegions[e].afterUpdate()},h.addNode=function(e){if(0===this.nSubRegions){if(null==this.node)return void(this.node=e);this._addNodeToSubRegion(this.node),this.node=null}this._addNodeToSubRegion(e),this._updateCenterOfMass(e)},h.findSubRegion=function(e,t){for(var i=0;i<this.nSubRegions;i++){var n=this.subRegions[i];if(n.contain(e,t))return n}},h.contain=function(e,t){return this.bbox[0]<=e&&this.bbox[2]>=e&&this.bbox[1]<=t&&this.bbox[3]>=t},h.setBBox=function(e,t,i,n){this.bbox[0]=e,this.bbox[1]=t,this.bbox[2]=i,this.bbox[3]=n,this.size=(i-e+n-t)/2},h._newSubRegion=function(){var e=this.subRegions[this.nSubRegions];return e||(e=new l,this.subRegions[this.nSubRegions]=e),this.nSubRegions++,e},h._addNodeToSubRegion=function(e){var t=this.findSubRegion(e.position[0],e.position[1]),i=this.bbox;if(!t){var n=(i[0]+i[2])/2,r=(i[1]+i[3])/2,o=(i[2]-i[0])/2,a=(i[3]-i[1])/2,s=e.position[0]>=n?1:0,l=e.position[1]>=r?1:0;(t=this._newSubRegion()).setBBox(s*o+i[0],l*a+i[1],(s+1)*o+i[0],(l+1)*a+i[1])}t.addNode(e)},h._updateCenterOfMass=function(e){null==this.centerOfMass&&(this.centerOfMass=new Float32Array(2));var t=this.centerOfMass[0]*this.mass,i=this.centerOfMass[1]*this.mass;t+=e.position[0]*e.mass,i+=e.position[1]*e.mass,this.mass+=e.mass,this.centerOfMass[0]=t/this.mass,this.centerOfMass[1]=i/this.mass};var f,p=u.prototype;p.initNodes=function(e,t,i){var n=t.length;this.nodes.length=0;for(var r=void 0!==i,o=0;o<n;o++){var a=new d;a.position[0]=e[2*o],a.position[1]=e[2*o+1],a.mass=t[o],r&&(a.size=i[o]),this.nodes.push(a)}this._massArr=t,this._swingingArr=new Float32Array(n),r&&(this._sizeArr=i)},p.initEdges=function(e,t){var i=e.length/2;this.edges.length=0;for(var n=0;n<i;n++){var r=e[2*n],o=e[2*n+1],a=this.nodes[r],s=this.nodes[o];if(!a||!s)return;a.outDegree++,s.inDegree++;var l=new c(a,s);t&&(l.weight=t[n]),this.edges.push(l)}},p.updateSettings=function(){if(this.repulsionByDegree)for(var e=0;e<this.nodes.length;e++){(t=this.nodes[e]).mass=t.inDegree+t.outDegree+1}else for(e=0;e<this.nodes.length;e++){var t;(t=this.nodes[e]).mass=this._massArr[e]}},p.update=function(){var o=this.nodes.length;if(this.updateSettings(),this.updateBBox(),this.barnesHutOptimize){this.rootRegion.setBBox(this.bbox[0],this.bbox[1],this.bbox[2],this.bbox[3]),this.rootRegion.beforeUpdate();for(var l=0;l<o;l++)this.rootRegion.addNode(this.nodes[l]);this.rootRegion.afterUpdate()}for(l=0;l<o;l++){var h=this.nodes[l];a(h.forcePrev,h.force),s(h.force,0,0)}for(l=0;l<o;l++){var d=this.nodes[l];if(this.barnesHutOptimize)this.applyRegionToNodeRepulsion(this.rootRegion,d);else for(var c=l+1;c<o;c++){var u=this.nodes[c];this.applyNodeToNodeRepulsion(d,u,!1)}this.gravity>0&&(this.strongGravityMode?this.applyNodeStrongGravity(d):this.applyNodeGravity(d))}for(l=0;l<this.edges.length;l++)this.applyEdgeAttraction(this.edges[l]);var f=0,p=0,m=e();for(l=0;l<o;l++){h=this.nodes[l];f+=(_=t(h.force,h.forcePrev))*h.mass,r(m,h.force,h.forcePrev),p+=.5*i(m)*h.mass,this._swingingArr[l]=_}var g=this.jitterTolerence*this.jitterTolerence*p/f;this._globalSpeed>0&&(g=Math.min(g/this._globalSpeed,1.5)*this._globalSpeed),this._globalSpeed=g;for(l=0;l<o;l++){h=this.nodes[l];var _=this._swingingArr[l],v=.1*g/(1+g*Math.sqrt(_)),x=i(h.force);x>0&&(v=Math.min(x*v,10)/x,n(h.position,h.position,h.force,v))}},p.applyRegionToNodeRepulsion=(f=e(),function(e,t){if(e.node)this.applyNodeToNodeRepulsion(e.node,t,!0);else{o(f,t.position,e.centerOfMass);var i=f[0]*f[0]+f[1]*f[1];if(i>this.barnesHutTheta*e.size*e.size){var r=this.scaling*t.mass*e.mass/i;n(t.force,t.force,f,r)}else for(var a=0;a<e.nSubRegions;a++)this.applyRegionToNodeRepulsion(e.subRegions[a],t)}}),p.applyNodeToNodeRepulsion=function(){var t=e();return function(e,i,r){if(e!=i){o(t,e.position,i.position);var a=t[0]*t[0]+t[1]*t[1];if(0!==a){var s;if(this.preventOverlap){var l=Math.sqrt(a);if((l=l-e.size-i.size)>0)s=this.scaling*e.mass*i.mass/(l*l);else{if(!(l<0))return;s=100*this.scaling*e.mass*i.mass}}else s=this.scaling*e.mass*i.mass/a;n(e.force,e.force,t,s),n(i.force,i.force,t,-s)}}}}(),p.applyEdgeAttraction=function(){var t=e();return function(e){var r=e.source,a=e.target;o(t,r.position,a.position);var s,l,h=i(t);s=0===this.edgeWeightInfluence?1:1===this.edgeWeightInfluence?e.weight:Math.pow(e.weight,this.edgeWeightInfluence),this.preventOverlap&&(h=h-r.size-a.size)<=0||(l=this.linLogMode?-s*Math.log(h+1)/(h+1):-s,n(r.force,r.force,t,l),n(a.force,a.force,t,-l))}}(),p.applyNodeGravity=function(){var t=e();return function(e){o(t,this.gravityCenter,e.position);var r=i(t);n(e.force,e.force,t,this.gravity*e.mass/(r+1))}}(),p.applyNodeStrongGravity=function(){var t=e();return function(e){o(t,this.gravityCenter,e.position),n(e.force,e.force,t,this.gravity*e.mass)}}(),p.updateBBox=function(){for(var e=Infinity,t=Infinity,i=-Infinity,n=-Infinity,r=0;r<this.nodes.length;r++){var o=this.nodes[r].position;e=Math.min(e,o[0]),t=Math.min(t,o[1]),i=Math.max(i,o[0]),n=Math.max(n,o[1])}this.bbox[0]=e,this.bbox[1]=t,this.bbox[2]=i,this.bbox[3]=n},p.getGlobalSpeed=function(){return this._globalSpeed};var m=null;self.onmessage=function(e){switch(e.data.cmd){case"init":(m=new u).initNodes(e.data.nodesPosition,e.data.nodesMass,e.data.nodesSize),m.initEdges(e.data.edges,e.data.edgesWeight);break;case"updateConfig":if(m)for(var t in e.data.config)m[t]=e.data.config[t];break;case"update":var i=e.data.steps;if(m){for(var n=0;n<i;n++)m.update();var r=m.nodes.length,o=new Float32Array(2*r);for(n=0;n<r;n++){var a=m.nodes[n];o[2*n]=a.position[0],o[2*n+1]=a.position[1]}self.postMessage({buffer:o.buffer,globalSpeed:m.getGlobalSpeed()},[o.buffer])}else{var s=new Float32Array;self.postMessage({buffer:s.buffer,globalSpeed:m.getGlobalSpeed()},[s.buffer])}}}}.toString();ga=ga.slice(ga.indexOf("{")+1,ga.lastIndexOf("}"));var _a={barnesHutOptimize:!0,barnesHutTheta:1.5,repulsionByDegree:!0,linLogMode:!1,strongGravityMode:!1,gravity:1,scaling:1,edgeWeightInfluence:1,jitterTolerence:.1,preventOverlap:!1,dissuadeHubs:!1,gravityCenter:null},va=function(e){for(var t in _a)this[t]=_a[t];if(e)for(var t in e)this[t]=e[t];this._nodes=[],this._edges=[],this._disposed=!1,this._positionTex=new F({type:Y.FLOAT,flipY:!1,minFilter:Y.NEAREST,magFilter:Y.NEAREST})};va.prototype.initData=function(e,t){var i=new Blob([ga]),n=window.URL.createObjectURL(i);this._worker=new Worker(n),this._worker.onmessage=this._$onupdate.bind(this),this._nodes=e,this._edges=t,this._frame=0;for(var r=e.length,o=t.length,a=new Float32Array(2*r),s=new Float32Array(r),l=new Float32Array(r),h=new Float32Array(2*o),d=new Float32Array(o),c=0;c<e.length;c++){var u=e[c];a[2*c]=u.x,a[2*c+1]=u.y,s[c]=null==u.mass?1:u.mass,l[c]=null==u.size?1:u.size}for(c=0;c<t.length;c++){var f=t[c],p=f.node1,m=f.node2;h[2*c]=p,h[2*c+1]=m,d[c]=null==f.weight?1:f.weight}var g=Math.ceil(Math.sqrt(e.length)),_=g,v=new Float32Array(g*_*4),x=this._positionTex;x.width=g,x.height=_,x.pixels=v,this._worker.postMessage({cmd:"init",nodesPosition:a,nodesMass:s,nodesSize:l,edges:h,edgesWeight:d}),this._globalSpeed=Infinity},va.prototype.updateOption=function(e){var t={};for(var i in _a)t[i]=_a[i];var n=this._nodes,r=this._edges,o=n.length;if(t.jitterTolerence=o>5e4?10:o>5e3?1:.1,t.scaling=o>100?2:10,t.barnesHutOptimize=o>1e3,e)for(var i in _a)null!=e[i]&&(t[i]=e[i]);if(!t.gravityCenter){for(var a=[Infinity,Infinity],s=[-Infinity,-Infinity],l=0;l<n.length;l++)a[0]=Math.min(n[l].x,a[0]),a[1]=Math.min(n[l].y,a[1]),s[0]=Math.max(n[l].x,s[0]),s[1]=Math.max(n[l].y,s[1]);t.gravityCenter=[.5*(a[0]+s[0]),.5*(a[1]+s[1])]}for(l=0;l<r.length;l++){var h=r[l].node1,d=r[l].node2;n[h].degree=(n[h].degree||0)+1,n[d].degree=(n[d].degree||0)+1}this._worker&&this._worker.postMessage({cmd:"updateConfig",config:t})},va.prototype.update=function(e,t,i){null==t&&(t=1),t=Math.max(t,1),this._frame+=t,this._onupdate=i,this._worker&&this._worker.postMessage({cmd:"update",steps:Math.round(t)})},va.prototype._$onupdate=function(e){if(!this._disposed){var t=new Float32Array(e.data.buffer);this._globalSpeed=e.data.globalSpeed,this._positionArr=t,this._updateTexture(t),this._onupdate&&this._onupdate()}},va.prototype.getNodePositionTexture=function(){return this._positionTex},va.prototype.getNodeUV=function(e,t){t=t||[];var i=this._positionTex.width,n=this._positionTex.height;return t[0]=e%i/(i-1),t[1]=Math.floor(e/i)/(n-1),t},va.prototype.getNodes=function(){return this._nodes},va.prototype.getEdges=function(){return this._edges},va.prototype.isFinished=function(e){return this._frame>e},va.prototype.getNodePosition=function(e,t){if(t||(t=new Float32Array(2*this._nodes.length)),this._positionArr)for(var i=0;i<this._positionArr.length;i++)t[i]=this._positionArr[i];return t},va.prototype._updateTexture=function(e){for(var t=this._positionTex.pixels,i=0,n=0;n<e.length;)t[i++]=e[n++],t[i++]=e[n++],t[i++]=1,t[i++]=1;this._positionTex.dirty()},va.prototype.dispose=function(e){this._disposed=!0,this._worker=null};var xa=be.extend((function(){return{zr:null,viewGL:null,minZoom:.2,maxZoom:5,_needsUpdate:!1,_dx:0,_dy:0,_zoom:1}}),(function(){this._mouseDownHandler=this._mouseDownHandler.bind(this),this._mouseWheelHandler=this._mouseWheelHandler.bind(this),this._mouseMoveHandler=this._mouseMoveHandler.bind(this),this._mouseUpHandler=this._mouseUpHandler.bind(this),this._update=this._update.bind(this)}),{init:function(){var e=this.zr;e.on("mousedown",this._mouseDownHandler),e.on("mousewheel",this._mouseWheelHandler),e.on("globalout",this._mouseUpHandler),e.animation.on("frame",this._update)},setTarget:function(e){this._target=e},setZoom:function(e){this._zoom=Math.max(Math.min(e,this.maxZoom),this.minZoom),this._needsUpdate=!0},setOffset:function(e){this._dx=e[0],this._dy=e[1],this._needsUpdate=!0},getZoom:function(){return this._zoom},getOffset:function(){return[this._dx,this._dy]},_update:function(){if(this._target&&this._needsUpdate){var e=this._target,t=this._zoom;e.position.x=this._dx,e.position.y=this._dy,e.scale.set(t,t,t),this.zr.refresh(),this._needsUpdate=!1,this.trigger("update")}},_mouseDownHandler:function(e){if(!e.target){var t=e.offsetX,i=e.offsetY;if(!this.viewGL||this.viewGL.containPoint(t,i)){this.zr.on("mousemove",this._mouseMoveHandler),this.zr.on("mouseup",this._mouseUpHandler);var n=this._convertPos(t,i);this._x=n.x,this._y=n.y}}},_convertPos:function(e,t){var i=this.viewGL.camera,n=this.viewGL.viewport;return{x:(e-n.x)/n.width*(i.right-i.left)+i.left,y:(t-n.y)/n.height*(i.bottom-i.top)+i.top}},_mouseMoveHandler:function(e){var t=this._convertPos(e.offsetX,e.offsetY);this._dx+=t.x-this._x,this._dy+=t.y-this._y,this._x=t.x,this._y=t.y,this._needsUpdate=!0},_mouseUpHandler:function(e){this.zr.off("mousemove",this._mouseMoveHandler),this.zr.off("mouseup",this._mouseUpHandler)},_mouseWheelHandler:function(e){var t=(e=e.event).wheelDelta||-e.detail;if(0!==t){var i=e.offsetX,n=e.offsetY;if(!this.viewGL||this.viewGL.containPoint(i,n)){var r=t>0?1.1:.9,o=Math.max(Math.min(this._zoom*r,this.maxZoom),this.minZoom);r=o/this._zoom;var a=this._convertPos(i,n),s=(a.x-this._dx)*(r-1),l=(a.y-this._dy)*(r-1);this._dx-=s,this._dy-=l,this._zoom=o,this._needsUpdate=!0}}},dispose:function(){var e=this.zr;e.off("mousedown",this._mouseDownHandler),e.off("mousemove",this._mouseMoveHandler),e.off("mouseup",this._mouseUpHandler),e.off("mousewheel",this._mouseWheelHandler),e.off("globalout",this._mouseUpHandler),e.animation.off("frame",this._update)}});var ya=we.vec2;Pt.Shader.import("@export ecgl.lines2D.vertex\n\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\n\nattribute vec2 position: POSITION;\nattribute vec4 a_Color : COLOR;\nvarying vec4 v_Color;\n\n#ifdef POSITIONTEXTURE_ENABLED\nuniform sampler2D positionTexture;\n#endif\n\nvoid main()\n{\n gl_Position = worldViewProjection * vec4(position, -10.0, 1.0);\n\n v_Color = a_Color;\n}\n\n@end\n\n@export ecgl.lines2D.fragment\n\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\n\nvarying vec4 v_Color;\n\nvoid main()\n{\n gl_FragColor = color * v_Color;\n}\n@end\n\n\n@export ecgl.meshLines2D.vertex\n\nattribute vec2 position: POSITION;\nattribute vec2 normal;\nattribute float offset;\nattribute vec4 a_Color : COLOR;\n\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform vec4 viewport : VIEWPORT;\n\nvarying vec4 v_Color;\nvarying float v_Miter;\n\nvoid main()\n{\n vec4 p2 = worldViewProjection * vec4(position + normal, -10.0, 1.0);\n gl_Position = worldViewProjection * vec4(position, -10.0, 1.0);\n\n p2.xy /= p2.w;\n gl_Position.xy /= gl_Position.w;\n\n vec2 N = normalize(p2.xy - gl_Position.xy);\n gl_Position.xy += N * offset / viewport.zw * 2.0;\n\n gl_Position.xy *= gl_Position.w;\n\n v_Color = a_Color;\n}\n@end\n\n\n@export ecgl.meshLines2D.fragment\n\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\n\nvarying vec4 v_Color;\nvarying float v_Miter;\n\nvoid main()\n{\n gl_FragColor = color * v_Color;\n}\n\n@end");var ba=1;const wa=P.extend({type:"graphGL",__ecgl__:!0,init:function(e,t){this.groupGL=new Pt.Node,this.viewGL=new Wi("orthographic"),this.viewGL.camera.left=this.viewGL.camera.right=0,this.viewGL.add(this.groupGL),this._pointsBuilder=new Lo(!0,t),this._forceEdgesMesh=new Pt.Mesh({material:new Pt.Material({shader:Pt.createShader("ecgl.forceAtlas2.edges"),transparent:!0,depthMask:!1,depthTest:!1}),$ignorePicking:!0,geometry:new Pt.Geometry({attributes:{node:new Pt.Geometry.Attribute("node","float",2),color:new Pt.Geometry.Attribute("color","float",4,"COLOR")},dynamic:!0,mainAttribute:"node"}),renderOrder:-1,mode:Pt.Mesh.LINES}),this._edgesMesh=new Pt.Mesh({material:new Pt.Material({shader:Pt.createShader("ecgl.meshLines2D"),transparent:!0,depthMask:!1,depthTest:!1}),$ignorePicking:!0,geometry:new fa({useNativeLine:!1,dynamic:!0}),renderOrder:-1,culling:!1}),this._layoutId=0,this._control=new xa({zr:t.getZr(),viewGL:this.viewGL}),this._control.setTarget(this.groupGL),this._control.init(),this._clickHandler=this._clickHandler.bind(this)},render:function(e,t,i){this.groupGL.add(this._pointsBuilder.rootNode),this._model=e,this._api=i,this._initLayout(e,t,i),this._pointsBuilder.update(e,t,i),this._forceLayoutInstance instanceof ma||this.groupGL.remove(this._forceEdgesMesh),this._updateCamera(e,i),this._control.off("update"),this._control.on("update",(function(){i.dispatchAction({type:"graphGLRoam",seriesId:e.id,zoom:this._control.getZoom(),offset:this._control.getOffset()}),this._pointsBuilder.updateView(this.viewGL.camera)}),this),this._control.setZoom(St(e.get("zoom"),1)),this._control.setOffset(e.get("offset")||[0,0]);var n=this._pointsBuilder.getPointsMesh();if(n.off("mousemove",this._mousemoveHandler),n.off("mouseout",this._mouseOutHandler,this),i.getZr().off("click",this._clickHandler),this._pointsBuilder.highlightOnMouseover=!0,e.get("focusNodeAdjacency")){var r=e.get("focusNodeAdjacencyOn");"click"===r?i.getZr().on("click",this._clickHandler):"mouseover"===r&&(n.on("mousemove",this._mousemoveHandler,this),n.on("mouseout",this._mouseOutHandler,this),this._pointsBuilder.highlightOnMouseover=!1)}this._lastMouseOverDataIndex=-1},_clickHandler:function(e){if(!this._layouting){var t=this._pointsBuilder.getPointsMesh().dataIndex;t>=0?this._api.dispatchAction({type:"graphGLFocusNodeAdjacency",seriesId:this._model.id,dataIndex:t}):this._api.dispatchAction({type:"graphGLUnfocusNodeAdjacency",seriesId:this._model.id})}},_mousemoveHandler:function(e){if(!this._layouting){var t=this._pointsBuilder.getPointsMesh().dataIndex;t>=0?t!==this._lastMouseOverDataIndex&&this._api.dispatchAction({type:"graphGLFocusNodeAdjacency",seriesId:this._model.id,dataIndex:t}):this._mouseOutHandler(e),this._lastMouseOverDataIndex=t}},_mouseOutHandler:function(e){this._layouting||(this._api.dispatchAction({type:"graphGLUnfocusNodeAdjacency",seriesId:this._model.id}),this._lastMouseOverDataIndex=-1)},_updateForceEdgesGeometry:function(e,t){var i=this._forceEdgesMesh.geometry,n=t.getEdgeData(),r=0,o=this._forceLayoutInstance,a=2*n.count();i.attributes.node.init(a),i.attributes.color.init(a),n.each((function(t){var a=e[t];i.attributes.node.set(r,o.getNodeUV(a.node1)),i.attributes.node.set(r+1,o.getNodeUV(a.node2));var s=Hn(n,a.dataIndex),l=Pt.parseColor(s);l[3]*=St(Un(n,a.dataIndex),1),i.attributes.color.set(r,l),i.attributes.color.set(r+1,l),r+=2})),i.dirty()},_updateMeshLinesGeometry:function(){var e=this._model.getEdgeData(),t=this._edgesMesh.geometry,i=(e=this._model.getEdgeData(),this._model.getData().getLayout("points"));t.resetOffset(),t.setVertexCount(e.count()*t.getLineVertexCount()),t.setTriangleCount(e.count()*t.getLineTriangleCount());var n=[],r=[],o=["lineStyle","width"];this._originalEdgeColors=new Float32Array(4*e.count()),this._edgeIndicesMap=new Float32Array(e.count()),e.each((function(a){var s=e.graph.getEdgeByIndex(a),l=2*s.node1.dataIndex,h=2*s.node2.dataIndex;n[0]=i[l],n[1]=i[l+1],r[0]=i[h],r[1]=i[h+1];var d=Hn(e,s.dataIndex),c=Pt.parseColor(d);c[3]*=St(Un(e,s.dataIndex),1);var u=e.getItemModel(s.dataIndex),f=St(u.get(o),1)*this._api.getDevicePixelRatio();t.addLine(n,r,c,f);for(var p=0;p<4;p++)this._originalEdgeColors[4*s.dataIndex+p]=c[p];this._edgeIndicesMap[s.dataIndex]=a}),this),t.dirty()},_updateForceNodesGeometry:function(e){for(var t=this._pointsBuilder.getPointsMesh(),i=[],n=0;n<e.count();n++)this._forceLayoutInstance.getNodeUV(n,i),t.geometry.attributes.position.set(n,i);t.geometry.dirty("position")},_initLayout:function(e,t,i){var n=e.get("layout"),r=e.getGraph(),o=e.getBoxLayoutParams(),a=c(o,{width:i.getWidth(),height:i.getHeight()});"force"===n&&(n="forceAtlas2"),this.stopLayout(e,t,i,{beforeLayout:!0});var s=e.getData(),l=e.getData();if("forceAtlas2"===n){var h=e.getModel("forceAtlas2"),d=this._forceLayoutInstance,u=[],f=[],p=s.getDataExtent("value"),m=l.getDataExtent("value"),g=St(h.get("edgeWeight"),1),_=St(h.get("nodeWeight"),1);"number"==typeof g&&(g=[g,g]),"number"==typeof _&&(_=[_,_]);var v=0,x={},y=new Float32Array(2*s.count());if(r.eachNode((function(e){var t,i,n=e.dataIndex,r=s.get("value",n);if(s.hasItemOption){var o=s.getItemModel(n);t=o.get("x"),i=o.get("y")}null==t&&(t=a.x+Math.random()*a.width,i=a.y+Math.random()*a.height),y[2*v]=t,y[2*v+1]=i,x[e.id]=v++;var l=z(r,p,_);isNaN(l)&&(l=isNaN(_[0])?1:_[0]),u.push({x:t,y:i,mass:l,size:s.getItemVisual(n,"symbolSize")})})),s.setLayout("points",y),r.eachEdge((function(e){var t=e.dataIndex,i=s.get("value",t),n=z(i,m,g);isNaN(n)&&(n=isNaN(g[0])?1:g[0]),f.push({node1:x[e.node1.id],node2:x[e.node2.id],weight:n,dataIndex:t})})),!d){var b=h.get("GPU");this._forceLayoutInstance&&((!b||this._forceLayoutInstance instanceof ma)&&(b||this._forceLayoutInstance instanceof va)||(this._forceLayoutInstanceToDispose=this._forceLayoutInstance)),d=this._forceLayoutInstance=b?new ma:new va}d.initData(u,f),d.updateOption(h.option),this._updateForceEdgesGeometry(d.getEdges(),e),this._updatePositionTexture(),i.dispatchAction({type:"graphGLStartLayout",from:this.uid})}else{y=new Float32Array(2*s.count()),v=0;r.eachNode((function(e){var t,i,n=e.dataIndex;if(s.hasItemOption){var r=s.getItemModel(n);t=r.get("x"),i=r.get("y")}y[v++]=t,y[v++]=i})),s.setLayout("points",y),this._updateAfterLayout(e,t,i)}},_updatePositionTexture:function(){var e=this._forceLayoutInstance.getNodePositionTexture();this._pointsBuilder.setPositionTexture(e),this._forceEdgesMesh.material.set("positionTex",e)},startLayout:function(e,t,i,n){if(!n||null==n.from||n.from===this.uid){var r=this.viewGL,o=(i=this._api,this._forceLayoutInstance),a=this._model.getData(),s=this._model.getModel("forceAtlas2");if(o&&(this.groupGL.remove(this._edgesMesh),this.groupGL.add(this._forceEdgesMesh),this._forceLayoutInstance)){this._updateForceNodesGeometry(e.getData()),this._pointsBuilder.hideLabels();var l=this,h=this._layoutId=ba++,d=s.getShallow("maxSteps"),c=s.getShallow("steps"),u=0,f=Math.max(2*c,20),p=function(t){if(t===l._layoutId)return o.isFinished(d)?(i.dispatchAction({type:"graphGLStopLayout",from:l.uid}),void i.dispatchAction({type:"graphGLFinishLayout",points:a.getLayout("points"),from:l.uid})):void o.update(r.layer.renderer,c,(function(){l._updatePositionTexture(),(u+=c)>=f&&(l._syncNodePosition(e),u=0),i.getZr().refresh(),We((function(){p(t)}))}))};We((function(){l._forceLayoutInstanceToDispose&&(l._forceLayoutInstanceToDispose.dispose(r.layer.renderer),l._forceLayoutInstanceToDispose=null),p(h)})),this._layouting=!0}}},stopLayout:function(e,t,i,n){n&&null!=n.from&&n.from!==this.uid||(this._layoutId=0,this.groupGL.remove(this._forceEdgesMesh),this.groupGL.add(this._edgesMesh),this._forceLayoutInstance&&this.viewGL.layer&&(n&&n.beforeLayout||(this._syncNodePosition(e),this._updateAfterLayout(e,t,i)),this._api.getZr().refresh(),this._layouting=!1))},_syncNodePosition:function(e){var t=this._forceLayoutInstance.getNodePosition(this.viewGL.layer.renderer);e.getData().setLayout("points",t),e.setNodePosition(t)},_updateAfterLayout:function(e,t,i){this._updateMeshLinesGeometry(),this._pointsBuilder.removePositionTexture(),this._pointsBuilder.updateLayout(e,t,i),this._pointsBuilder.updateView(this.viewGL.camera),this._pointsBuilder.updateLabels(),this._pointsBuilder.showLabels()},focusNodeAdjacency:function(e,t,i,n){var r=this._model.getData();this._downplayAll();var o=n.dataIndex,a=r.graph,s=[],l=a.getNodeByIndex(o);s.push(l),l.edges.forEach((function(e){e.dataIndex<0||(e.node1!==l&&s.push(e.node1),e.node2!==l&&s.push(e.node2))}),this),this._pointsBuilder.fadeOutAll(.05),this._fadeOutEdgesAll(.05),s.forEach((function(e){this._pointsBuilder.highlight(r,e.dataIndex)}),this),this._pointsBuilder.updateLabels(s.map((function(e){return e.dataIndex})));var h=[];l.edges.forEach((function(e){e.dataIndex>=0&&(this._highlightEdge(e.dataIndex),h.push(e))}),this),this._focusNodes=s,this._focusEdges=h},unfocusNodeAdjacency:function(e,t,i,n){this._downplayAll(),this._pointsBuilder.fadeInAll(),this._fadeInEdgesAll(),this._pointsBuilder.updateLabels()},_highlightEdge:function(e){var t=this._model.getEdgeData().getItemModel(e),i=Pt.parseColor(t.get("emphasis.lineStyle.color")||t.get("lineStyle.color")),n=St(t.get("emphasis.lineStyle.opacity"),t.get("lineStyle.opacity"),1);i[3]*=n,this._edgesMesh.geometry.setItemColor(this._edgeIndicesMap[e],i)},_downplayAll:function(){this._focusNodes&&this._focusNodes.forEach((function(e){this._pointsBuilder.downplay(this._model.getData(),e.dataIndex)}),this),this._focusEdges&&this._focusEdges.forEach((function(e){this._downplayEdge(e.dataIndex)}),this)},_downplayEdge:function(e){var t=this._getColor(e,[]);this._edgesMesh.geometry.setItemColor(this._edgeIndicesMap[e],t)},_setEdgeFade:function(){var e=[];return function(t,i){this._getColor(t,e),e[3]*=i,this._edgesMesh.geometry.setItemColor(this._edgeIndicesMap[t],e)}}(),_getColor:function(e,t){for(var i=0;i<4;i++)t[i]=this._originalEdgeColors[4*e+i];return t},_fadeOutEdgesAll:function(e){this._model.getData().graph.eachEdge((function(t){this._setEdgeFade(t.dataIndex,e)}),this)},_fadeInEdgesAll:function(){this._fadeOutEdgesAll(1)},_updateCamera:function(e,t){this.viewGL.setViewport(0,0,t.getWidth(),t.getHeight(),t.getDevicePixelRatio());for(var i=this.viewGL.camera,n=e.getData().getLayout("points"),r=ya.create(Infinity,Infinity),o=ya.create(-Infinity,-Infinity),a=[],s=0;s<n.length;)a[0]=n[s++],a[1]=n[s++],ya.min(r,r,a),ya.max(o,o,a);var l=(o[1]+r[1])/2,h=(o[0]+r[0])/2;if(!(h>i.left&&h<i.right&&l<i.bottom&&l>i.top)){var d=Math.max(o[0]-r[0],10),c=d/t.getWidth()*t.getHeight();d*=1.4,c*=1.4,r[0]-=.2*d,i.left=r[0],i.top=l-c/2,i.bottom=l+c/2,i.right=d+r[0],i.near=0,i.far=100}},dispose:function(){var e=this.viewGL.layer.renderer;this._forceLayoutInstance&&this._forceLayoutInstance.dispose(e),this.groupGL.removeAll(),this._layoutId=-1,this._pointsBuilder.dispose()},remove:function(){this.groupGL.removeAll(),this._control.dispose()}});function Ta(e){return e instanceof Array||(e=[e,e]),e}m((function(e){function t(){}e.registerChartView(wa),e.registerSeriesModel(da),e.registerVisual((function(e){const t={};e.eachSeriesByType("graphGL",(function(e){var i=e.getCategoriesData(),n=e.getData(),r={};i.each((function(n){var o=i.getName(n);r["ec-"+o]=n;var a=i.getItemModel(n),s=a.getModel("itemStyle").getItemStyle();s.fill||(s.fill=e.getColorFromPalette(o,t)),i.setItemVisual(n,"style",s);var l=["symbol","symbolSize","symbolKeepAspect"];for(let e=0;e<l.length;e++){var h=a.getShallow(l[e],!0);null!=h&&i.setItemVisual(n,l[e],h)}})),i.count()&&n.each((function(e){let t=n.getItemModel(e).getShallow("category");if(null!=t){"string"==typeof t&&(t=r["ec-"+t]);var o=i.getItemVisual(t,"style"),a=n.ensureUniqueItemVisual(e,"style");ct(a,o);var s=["symbol","symbolSize","symbolKeepAspect"];for(let r=0;r<s.length;r++)n.setItemVisual(e,s[r],i.getItemVisual(t,s[r]))}}))}))})),e.registerVisual((function(e){e.eachSeriesByType("graphGL",(function(e){var t=e.getGraph(),i=e.getEdgeData(),n=Ta(e.get("edgeSymbol")),r=Ta(e.get("edgeSymbolSize"));i.setVisual("drawType","stroke"),i.setVisual("fromSymbol",n&&n[0]),i.setVisual("toSymbol",n&&n[1]),i.setVisual("fromSymbolSize",r&&r[0]),i.setVisual("toSymbolSize",r&&r[1]),i.setVisual("style",e.getModel("lineStyle").getLineStyle()),i.each((function(e){var n=i.getItemModel(e),r=t.getEdgeByIndex(e),o=Ta(n.getShallow("symbol",!0)),a=Ta(n.getShallow("symbolSize",!0)),s=n.getModel("lineStyle").getLineStyle(),l=i.ensureUniqueItemVisual(e,"style");switch(ct(l,s),l.stroke){case"source":var h=r.node1.getVisual("style");l.stroke=h&&h.fill;break;case"target":h=r.node2.getVisual("style");l.stroke=h&&h.fill}o[0]&&r.setVisual("fromSymbol",o[0]),o[1]&&r.setVisual("toSymbol",o[1]),a[0]&&r.setVisual("fromSymbolSize",a[0]),a[1]&&r.setVisual("toSymbolSize",a[1])}))}))})),e.registerAction({type:"graphGLRoam",event:"graphglroam",update:"series.graphGL:roam"},(function(e,t){t.eachComponent({mainType:"series",query:e},(function(t){t.setView(e)}))})),e.registerAction({type:"graphGLStartLayout",event:"graphgllayoutstarted",update:"series.graphGL:startLayout"},t),e.registerAction({type:"graphGLStopLayout",event:"graphgllayoutstopped",update:"series.graphGL:stopLayout"},t),e.registerAction({type:"graphGLFocusNodeAdjacency",event:"graphGLFocusNodeAdjacency",update:"series.graphGL:focusNodeAdjacency"},t),e.registerAction({type:"graphGLUnfocusNodeAdjacency",event:"graphGLUnfocusNodeAdjacency",update:"series.graphGL:unfocusNodeAdjacency"},t)}));const Sa=A.extend({type:"series.flowGL",dependencies:["geo","grid","bmap"],visualStyleAccessPath:"itemStyle",getInitialData:function(e,t){var i=this.get("coordinateSystem"),n="geo"===i?["lng","lat"]:C(i)||["x","y"];n.push("vx","vy");var r=_(this.getSource(),{coordDimensions:n,encodeDefine:this.get("encode"),dimensionsDefine:this.get("dimensions")}),o=new v(r,this);return o.initData(this.getSource()),o},defaultOption:{coordinateSystem:"cartesian2d",zlevel:10,supersampling:1,particleType:"point",particleDensity:128,particleSize:1,particleSpeed:1,particleTrail:2,colorTexture:null,gridWidth:"auto",gridHeight:"auto",itemStyle:{color:"#fff",opacity:.8}}});var Ma=q.extend((function(){return{dynamic:!0,attributes:{position:new q.Attribute("position","float",3,"POSITION")}}}),{resetOffset:function(){this._vertexOffset=0,this._faceOffset=0},setLineCount:function(e){var t=this.attributes,i=4*e,n=2*e;this.vertexCount!==i&&t.position.init(i),this.triangleCount!==n&&(this.indices=0===n?null:this.vertexCount>65535?new Uint32Array(3*n):new Uint16Array(3*n))},addLine:function(e){var t=this._vertexOffset;this.attributes.position.set(t,[e[0],e[1],1]),this.attributes.position.set(t+1,[e[0],e[1],-1]),this.attributes.position.set(t+2,[e[0],e[1],2]),this.attributes.position.set(t+3,[e[0],e[1],-2]),this.setTriangleIndices(this._faceOffset++,[t,t+1,t+2]),this.setTriangleIndices(this._faceOffset++,[t+1,t+2,t+3]),this._vertexOffset+=4}});U.import("@export ecgl.vfParticle.particle.fragment\n\nuniform sampler2D particleTexture;\nuniform sampler2D spawnTexture;\nuniform sampler2D velocityTexture;\n\nuniform float deltaTime;\nuniform float elapsedTime;\n\nuniform float speedScaling : 1.0;\n\nuniform vec2 textureSize;\nuniform vec4 region : [0, 0, 1, 1];\nuniform float firstFrameTime;\n\nvarying vec2 v_Texcoord;\n\n\nvoid main()\n{\n vec4 p = texture2D(particleTexture, v_Texcoord);\n bool spawn = false;\n if (p.w <= 0.0) {\n p = texture2D(spawnTexture, fract(v_Texcoord + elapsedTime / 10.0));\n p.w -= firstFrameTime;\n spawn = true;\n }\n vec2 v = texture2D(velocityTexture, fract(p.xy * region.zw + region.xy)).xy;\n v = (v - 0.5) * 2.0;\n p.z = length(v);\n p.xy += v * deltaTime / 10.0 * speedScaling;\n p.w -= deltaTime;\n\n if (spawn || p.xy != fract(p.xy)) {\n p.z = 0.0;\n }\n p.xy = fract(p.xy);\n\n gl_FragColor = p;\n}\n@end\n\n@export ecgl.vfParticle.renderPoints.vertex\n\n#define PI 3.1415926\n\nattribute vec2 texcoord : TEXCOORD_0;\n\nuniform sampler2D particleTexture;\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\n\nuniform float size : 1.0;\n\nvarying float v_Mag;\nvarying vec2 v_Uv;\n\nvoid main()\n{\n vec4 p = texture2D(particleTexture, texcoord);\n\n if (p.w > 0.0 && p.z > 1e-5) {\n gl_Position = worldViewProjection * vec4(p.xy * 2.0 - 1.0, 0.0, 1.0);\n }\n else {\n gl_Position = vec4(100000.0, 100000.0, 100000.0, 1.0);\n }\n\n v_Mag = p.z;\n v_Uv = p.xy;\n\n gl_PointSize = size;\n}\n\n@end\n\n@export ecgl.vfParticle.renderPoints.fragment\n\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\nuniform sampler2D gradientTexture;\nuniform sampler2D colorTexture;\nuniform sampler2D spriteTexture;\n\nvarying float v_Mag;\nvarying vec2 v_Uv;\n\nvoid main()\n{\n gl_FragColor = color;\n#ifdef SPRITETEXTURE_ENABLED\n gl_FragColor *= texture2D(spriteTexture, gl_PointCoord);\n if (color.a == 0.0) {\n discard;\n }\n#endif\n#ifdef GRADIENTTEXTURE_ENABLED\n gl_FragColor *= texture2D(gradientTexture, vec2(v_Mag, 0.5));\n#endif\n#ifdef COLORTEXTURE_ENABLED\n gl_FragColor *= texture2D(colorTexture, v_Uv);\n#endif\n}\n\n@end\n\n@export ecgl.vfParticle.renderLines.vertex\n\n#define PI 3.1415926\n\nattribute vec3 position : POSITION;\n\nuniform sampler2D particleTexture;\nuniform sampler2D prevParticleTexture;\n\nuniform float size : 1.0;\nuniform vec4 vp: VIEWPORT;\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\n\nvarying float v_Mag;\nvarying vec2 v_Uv;\n\n@import clay.util.rand\n\nvoid main()\n{\n vec4 p = texture2D(particleTexture, position.xy);\n vec4 p2 = texture2D(prevParticleTexture, position.xy);\n\n p.xy = p.xy * 2.0 - 1.0;\n p2.xy = p2.xy * 2.0 - 1.0;\n\n if (p.w > 0.0 && p.z > 1e-5) {\n vec2 dir = normalize(p.xy - p2.xy);\n vec2 norm = vec2(dir.y / vp.z, -dir.x / vp.w) * sign(position.z) * size;\n if (abs(position.z) == 2.0) {\n gl_Position = vec4(p.xy + norm, 0.0, 1.0);\n v_Uv = p.xy;\n v_Mag = p.z;\n }\n else {\n gl_Position = vec4(p2.xy + norm, 0.0, 1.0);\n v_Mag = p2.z;\n v_Uv = p2.xy;\n }\n gl_Position = worldViewProjection * gl_Position;\n }\n else {\n gl_Position = vec4(100000.0, 100000.0, 100000.0, 1.0);\n }\n}\n\n@end\n\n@export ecgl.vfParticle.renderLines.fragment\n\nuniform vec4 color : [1.0, 1.0, 1.0, 1.0];\nuniform sampler2D gradientTexture;\nuniform sampler2D colorTexture;\n\nvarying float v_Mag;\nvarying vec2 v_Uv;\n\nvoid main()\n{\n gl_FragColor = color;\n #ifdef GRADIENTTEXTURE_ENABLED\n gl_FragColor *= texture2D(gradientTexture, vec2(v_Mag, 0.5));\n#endif\n#ifdef COLORTEXTURE_ENABLED\n gl_FragColor *= texture2D(colorTexture, v_Uv);\n#endif\n}\n\n@end\n");var Da=function(){this.motionBlurFactor=.99,this.vectorFieldTexture=new F({type:Y.FLOAT,flipY:!1}),this.particleLife=[5,20],this._particleType="point",this._particleSize=1,this.particleColor=[1,1,1,1],this.particleSpeedScaling=1,this._thisFrameTexture=null,this._particlePass=null,this._spawnTexture=null,this._particleTexture0=null,this._particleTexture1=null,this._particlePointsMesh=null,this._surfaceFrameBuffer=null,this._elapsedTime=0,this._scene=null,this._camera=null,this._lastFrameTexture=null,this._supersampling=1,this._downsampleTextures=[],this._width=512,this._height=512,this.init()};Da.prototype={constructor:Da,init:function(){var e={type:Y.FLOAT,minFilter:Y.NEAREST,magFilter:Y.NEAREST,useMipmap:!1};this._spawnTexture=new F(e),this._particleTexture0=new F(e),this._particleTexture1=new F(e),this._frameBuffer=new Me({depthBuffer:!1}),this._particlePass=new Se({fragment:U.source("ecgl.vfParticle.particle.fragment")}),this._particlePass.setUniform("velocityTexture",this.vectorFieldTexture),this._particlePass.setUniform("spawnTexture",this._spawnTexture),this._downsamplePass=new Se({fragment:U.source("clay.compositor.downsample")});var t=new X({renderOrder:10,material:new j({shader:new U(U.source("ecgl.vfParticle.renderPoints.vertex"),U.source("ecgl.vfParticle.renderPoints.fragment"))}),mode:X.POINTS,geometry:new q({dynamic:!0,mainAttribute:"texcoord0"})}),i=new X({renderOrder:10,material:new j({shader:new U(U.source("ecgl.vfParticle.renderLines.vertex"),U.source("ecgl.vfParticle.renderLines.fragment"))}),geometry:new Ma,culling:!1}),n=new X({material:new j({shader:new U(U.source("ecgl.color.vertex"),U.source("ecgl.color.fragment"))}),geometry:new K});n.material.enableTexture("diffuseMap"),this._particlePointsMesh=t,this._particleLinesMesh=i,this._lastFrameFullQuadMesh=n,this._camera=new re,this._thisFrameTexture=new F,this._lastFrameTexture=new F},setParticleDensity:function(e,t){for(var i=new Float32Array(4*(e*t)),n=0,r=this.particleLife,o=0;o<e;o++)for(var a=0;a<t;a++,n++){i[4*n]=Math.random(),i[4*n+1]=Math.random(),i[4*n+2]=Math.random();var s=(r[1]-r[0])*Math.random()+r[0];i[4*n+3]=s}"line"===this._particleType?this._setLineGeometry(e,t):this._setPointsGeometry(e,t),this._spawnTexture.width=e,this._spawnTexture.height=t,this._spawnTexture.pixels=i,this._particleTexture0.width=this._particleTexture1.width=e,this._particleTexture0.height=this._particleTexture1.height=t,this._particlePass.setUniform("textureSize",[e,t])},_setPointsGeometry:function(e,t){var i=e*t,n=this._particlePointsMesh.geometry,r=n.attributes;r.texcoord0.init(i);for(var o=0,a=0;a<e;a++)for(var s=0;s<t;s++,o++)r.texcoord0.value[2*o]=a/e,r.texcoord0.value[2*o+1]=s/t;n.dirty()},_setLineGeometry:function(e,t){var i=e*t,n=this._getParticleMesh().geometry;n.setLineCount(i),n.resetOffset();for(var r=0;r<e;r++)for(var o=0;o<t;o++)n.addLine([r/e,o/t]);n.dirty()},_getParticleMesh:function(){return"line"===this._particleType?this._particleLinesMesh:this._particlePointsMesh},update:function(e,t,i,n){var r=this._getParticleMesh(),o=this._frameBuffer,a=this._particlePass;n&&this._updateDownsampleTextures(e,t),r.material.set("size",this._particleSize*this._supersampling),r.material.set("color",this.particleColor),a.setUniform("speedScaling",this.particleSpeedScaling),o.attach(this._particleTexture1),a.setUniform("firstFrameTime",n?(this.particleLife[1]+this.particleLife[0])/2:0),a.setUniform("particleTexture",this._particleTexture0),a.setUniform("deltaTime",i),a.setUniform("elapsedTime",this._elapsedTime),a.render(e,o),r.material.set("particleTexture",this._particleTexture1),r.material.set("prevParticleTexture",this._particleTexture0),o.attach(this._thisFrameTexture),o.bind(e),e.gl.clear(e.gl.DEPTH_BUFFER_BIT|e.gl.COLOR_BUFFER_BIT);var s=this._lastFrameFullQuadMesh;s.material.set("diffuseMap",this._lastFrameTexture),s.material.set("color",[1,1,1,this.motionBlurFactor]),this._camera.update(!0),e.renderPass([s,r],this._camera),o.unbind(e),this._downsample(e),this._swapTexture(),this._elapsedTime+=i},_downsample:function(e){var t=this._downsampleTextures;if(0!==t.length)for(var i=0,n=this._thisFrameTexture,r=t[i];r;)this._frameBuffer.attach(r),this._downsamplePass.setUniform("texture",n),this._downsamplePass.setUniform("textureSize",[n.width,n.height]),this._downsamplePass.render(e,this._frameBuffer),n=r,r=t[++i]},getSurfaceTexture:function(){var e=this._downsampleTextures;return e.length>0?e[e.length-1]:this._lastFrameTexture},setRegion:function(e){this._particlePass.setUniform("region",e)},resize:function(e,t){this._lastFrameTexture.width=e*this._supersampling,this._lastFrameTexture.height=t*this._supersampling,this._thisFrameTexture.width=e*this._supersampling,this._thisFrameTexture.height=t*this._supersampling,this._width=e,this._height=t},setParticleSize:function(e){var t=this._getParticleMesh();if(e<=2)return t.material.disableTexture("spriteTexture"),void(t.material.transparent=!1);this._spriteTexture||(this._spriteTexture=new F),this._spriteTexture.image&&this._spriteTexture.image.width===e||(this._spriteTexture.image=function(e){var t=document.createElement("canvas");t.width=t.height=e;var i=t.getContext("2d");return i.fillStyle="#fff",i.arc(e/2,e/2,e/2,0,2*Math.PI),i.fill(),t}(e),this._spriteTexture.dirty()),t.material.transparent=!0,t.material.enableTexture("spriteTexture"),t.material.set("spriteTexture",this._spriteTexture),this._particleSize=e},setGradientTexture:function(e){var t=this._getParticleMesh().material;t[e?"enableTexture":"disableTexture"]("gradientTexture"),t.setUniform("gradientTexture",e)},setColorTextureImage:function(e,t){this._getParticleMesh().material.setTextureImage("colorTexture",e,t,{flipY:!0})},setParticleType:function(e){this._particleType=e},clearFrame:function(e){var t=this._frameBuffer;t.attach(this._lastFrameTexture),t.bind(e),e.gl.clear(e.gl.DEPTH_BUFFER_BIT|e.gl.COLOR_BUFFER_BIT),t.unbind(e)},setSupersampling:function(e){this._supersampling=e,this.resize(this._width,this._height)},_updateDownsampleTextures:function(e,t){for(var i=this._downsampleTextures,n=Math.max(Math.floor(Math.log(this._supersampling/t.getDevicePixelRatio())/Math.log(2)),0),r=2,o=this._width*this._supersampling,a=this._height*this._supersampling,s=0;s<n;s++)i[s]=i[s]||new F,i[s].width=o/r,i[s].height=a/r,r*=2;for(;s<i.length;s++)i[s].dispose(e);i.length=n},_swapTexture:function(){var e=this._particleTexture0;this._particleTexture0=this._particleTexture1,this._particleTexture1=e;e=this._thisFrameTexture;this._thisFrameTexture=this._lastFrameTexture,this._lastFrameTexture=e},dispose:function(e){e.disposeFrameBuffer(this._frameBuffer),e.disposeTexture(this.vectorFieldTexture),e.disposeTexture(this._spawnTexture),e.disposeTexture(this._particleTexture0),e.disposeTexture(this._particleTexture1),e.disposeTexture(this._thisFrameTexture),e.disposeTexture(this._lastFrameTexture),e.disposeGeometry(this._particleLinesMesh.geometry),e.disposeGeometry(this._particlePointsMesh.geometry),e.disposeGeometry(this._lastFrameFullQuadMesh.geometry),this._spriteTexture&&e.disposeTexture(this._spriteTexture),this._particlePass.dispose(e),this._downsamplePass.dispose(e),this._downsampleTextures.forEach((function(t){t.dispose(e)}))}};const La=P.extend({type:"flowGL",__ecgl__:!0,init:function(e,t){this.viewGL=new Wi("orthographic"),this.groupGL=new Pt.Node,this.viewGL.add(this.groupGL),this._particleSurface=new Da;var i=new Pt.Mesh({geometry:new Pt.PlaneGeometry,material:new Pt.Material({shader:new Pt.Shader({vertex:Pt.Shader.source("ecgl.color.vertex"),fragment:Pt.Shader.source("ecgl.color.fragment")}),transparent:!0})});i.material.enableTexture("diffuseMap"),this.groupGL.add(i),this._planeMesh=i},render:function(e,t,i){var n=this._particleSurface;n.setParticleType(e.get("particleType")),n.setSupersampling(e.get("supersampling")),this._updateData(e,i),this._updateCamera(i.getWidth(),i.getHeight(),i.getDevicePixelRatio());var r=St(e.get("particleDensity"),128);n.setParticleDensity(r,r);var o=this._planeMesh,a=+new Date,s=this,l=!0;o.__percent=0,o.stopAnimation(),o.animate("",{loop:!0}).when(1e5,{__percent:1}).during((function(){var e=+new Date,t=Math.min(e-a,20);a+=t,s._renderer&&(n.update(s._renderer,i,t/1e3,l),o.material.set("diffuseMap",n.getSurfaceTexture())),l=!1})).start();var h=e.getModel("itemStyle"),d=Pt.parseColor(h.get("color"));d[3]*=St(h.get("opacity"),1),o.material.set("color",d),n.setColorTextureImage(e.get("colorTexture"),i),n.setParticleSize(e.get("particleSize")),n.particleSpeedScaling=e.get("particleSpeed"),n.motionBlurFactor=1-Math.pow(.1,e.get("particleTrail"))},updateTransform:function(e,t,i){this._updateData(e,i)},afterRender:function(e,t,i,n){var r=n.renderer;this._renderer=r},_updateData:function(e,t){var i=e.coordinateSystem,n=i.dimensions.map((function(t){return e.coordDimToDataDim(t)[0]})),r=e.getData(),o=r.getDataExtent(n[0]),a=r.getDataExtent(n[1]),s=e.get("gridWidth"),l=e.get("gridHeight");if(null==s||"auto"===s){var h=(o[1]-o[0])/(a[1]-a[0]);s=Math.round(Math.sqrt(h*r.count()))}null!=l&&"auto"!==l||(l=Math.ceil(r.count()/s));var d=this._particleSurface.vectorFieldTexture,c=d.pixels;if(c&&c.length===l*s*4)for(var u=0;u<c.length;u++)c[u]=0;else c=d.pixels=new Float32Array(s*l*4);var f=0,p=Infinity,m=new Float32Array(2*r.count()),g=0,_=[[Infinity,Infinity],[-Infinity,-Infinity]];r.each([n[0],n[1],"vx","vy"],(function(e,t,n,r){var o=i.dataToPoint([e,t]);m[g++]=o[0],m[g++]=o[1],_[0][0]=Math.min(o[0],_[0][0]),_[0][1]=Math.min(o[1],_[0][1]),_[1][0]=Math.max(o[0],_[1][0]),_[1][1]=Math.max(o[1],_[1][1]);var a=Math.sqrt(n*n+r*r);f=Math.max(f,a),p=Math.min(p,a)})),r.each(["vx","vy"],(function(e,t,i){var n=Math.round((m[2*i]-_[0][0])/(_[1][0]-_[0][0])*(s-1)),r=4*((l-1-Math.round((m[2*i+1]-_[0][1])/(_[1][1]-_[0][1])*(l-1)))*s+n);c[r]=e/f*.5+.5,c[r+1]=t/f*.5+.5,c[r+3]=1})),d.width=s,d.height=l,"bmap"===e.get("coordinateSystem")&&this._fillEmptyPixels(d),d.dirty(),this._updatePlanePosition(_[0],_[1],e,t),this._updateGradientTexture(r.getVisual("visualMeta"),[p,f])},_fillEmptyPixels:function(e){var t=e.pixels,i=e.width,n=e.height;function r(e,r,o){e=Math.max(Math.min(e,i-1),0);var a=4*((r=Math.max(Math.min(r,n-1),0))*(i-1)+e);return 0!==t[a+3]&&(o[0]=t[a],o[1]=t[a+1],!0)}function o(e,t,i){i[0]=e[0]+t[0],i[1]=e[1]+t[1]}for(var a=[],s=[],l=[],h=[],d=[],c=0,u=0;u<n;u++)for(var f=0;f<i;f++){var p=4*(u*(i-1)+f);0===t[p+3]&&(c=a[0]=a[1]=0,r(f-1,u,s)&&(c++,o(s,a,a)),r(f+1,u,l)&&(c++,o(l,a,a)),r(f,u-1,h)&&(c++,o(h,a,a)),r(f,u+1,d)&&(c++,o(d,a,a)),a[0]/=c,a[1]/=c,t[p]=a[0],t[p+1]=a[1]),t[p+3]=1}},_updateGradientTexture:function(e,t){if(e&&e.length){this._gradientTexture=this._gradientTexture||new Pt.Texture2D({image:document.createElement("canvas")});var i=this._gradientTexture,n=i.image;n.width=200,n.height=1;var r=n.getContext("2d"),o=r.createLinearGradient(0,.5,n.width,.5);e[0].stops.forEach((function(e){var i;t[1]===t[0]?i=0:(i=e.value/t[1],i=Math.min(Math.max(i,0),1)),o.addColorStop(i,e.color)})),r.fillStyle=o,r.fillRect(0,0,n.width,n.height),i.dirty(),this._particleSurface.setGradientTexture(this._gradientTexture)}else this._particleSurface.setGradientTexture(null)},_updatePlanePosition:function(e,t,i,n){var r=this._limitInViewportAndFullFill(e,t,i,n);e=r.leftTop,t=r.rightBottom,this._particleSurface.setRegion(r.region),this._planeMesh.position.set((e[0]+t[0])/2,n.getHeight()-(e[1]+t[1])/2,0);var o=t[0]-e[0],a=t[1]-e[1];this._planeMesh.scale.set(o/2,a/2,1),this._particleSurface.resize(Math.max(Math.min(o,2048),1),Math.max(Math.min(a,2048),1)),this._renderer&&this._particleSurface.clearFrame(this._renderer)},_limitInViewportAndFullFill:function(e,t,i,n){var r=[Math.max(e[0],0),Math.max(e[1],0)],o=[Math.min(t[0],n.getWidth()),Math.min(t[1],n.getHeight())];if("bmap"===i.get("coordinateSystem")){var a=i.getData().getDataExtent(i.coordDimToDataDim("lng")[0]);Math.floor(a[1]-a[0])>=359&&(r[0]>0&&(r[0]=0),o[0]<n.getWidth()&&(o[0]=n.getWidth()))}var s=t[0]-e[0],l=t[1]-e[1],h=o[0]-r[0],d=o[1]-r[1];return{leftTop:r,rightBottom:o,region:[(r[0]-e[0])/s,1-d/l-(r[1]-e[1])/l,h/s,d/l]}},_updateCamera:function(e,t,i){this.viewGL.setViewport(0,0,e,t,i);var n=this.viewGL.camera;n.left=n.bottom=0,n.top=t,n.right=e,n.near=0,n.far=100,n.position.z=10},remove:function(){this._planeMesh.stopAnimation(),this.groupGL.removeAll()},dispose:function(){this._renderer&&this._particleSurface.dispose(this._renderer),this.groupGL.removeAll()}});m((function(e){e.registerChartView(La),e.registerSeriesModel(Sa)}));var Ca=A.extend({type:"series.linesGL",dependencies:["grid","geo"],visualStyleAccessPath:"lineStyle",visualDrawType:"stroke",streamEnabled:!0,init:function(e){var t=this._processFlatCoordsArray(e.data);this._flatCoords=t.flatCoords,this._flatCoordsOffset=t.flatCoordsOffset,t.flatCoords&&(e.data=new Float32Array(t.count)),Ca.superApply(this,"init",arguments)},mergeOption:function(e){var t=this._processFlatCoordsArray(e.data);this._flatCoords=t.flatCoords,this._flatCoordsOffset=t.flatCoordsOffset,t.flatCoords&&(e.data=new Float32Array(t.count)),Ca.superApply(this,"mergeOption",arguments)},appendData:function(e){var t=this._processFlatCoordsArray(e.data);t.flatCoords&&(this._flatCoords?(this._flatCoords=ut(this._flatCoords,t.flatCoords),this._flatCoordsOffset=ut(this._flatCoordsOffset,t.flatCoordsOffset)):(this._flatCoords=t.flatCoords,this._flatCoordsOffset=t.flatCoordsOffset),e.data=new Float32Array(t.count)),this.getRawData().appendData(e.data)},_getCoordsFromItemModel:function(e){var t=this.getData().getItemModel(e);return t.option instanceof Array?t.option:t.getShallow("coords")},getLineCoordsCount:function(e){return this._flatCoordsOffset?this._flatCoordsOffset[2*e+1]:this._getCoordsFromItemModel(e).length},getLineCoords:function(e,t){if(this._flatCoordsOffset){for(var i=this._flatCoordsOffset[2*e],n=this._flatCoordsOffset[2*e+1],r=0;r<n;r++)t[r]=t[r]||[],t[r][0]=this._flatCoords[i+2*r],t[r][1]=this._flatCoords[i+2*r+1];return n}var o=this._getCoordsFromItemModel(e);for(r=0;r<o.length;r++)t[r]=t[r]||[],t[r][0]=o[r][0],t[r][1]=o[r][1];return o.length},_processFlatCoordsArray:function(e){var t=0;if(this._flatCoords&&(t=this._flatCoords.length),"number"==typeof e[0]){for(var i=e.length,n=new Uint32Array(i),r=new Float64Array(i),o=0,a=0,s=0,l=0;l<i;){s++;var h=e[l++];n[a++]=o+t,n[a++]=h;for(var d=0;d<h;d++){var c=e[l++],u=e[l++];r[o++]=c,r[o++]=u}}return{flatCoordsOffset:new Uint32Array(n.buffer,0,a),flatCoords:r,count:s}}return{flatCoordsOffset:null,flatCoords:null,count:e.length}},getInitialData:function(e,t){var i=new v(["value"],this);return i.hasItemOption=!1,i.initData(e.data,[],(function(e,t,n,r){if(e instanceof Array)return NaN;i.hasItemOption=!0;var o=e.value;return null!=o?o instanceof Array?o[r]:o:void 0})),i},defaultOption:{coordinateSystem:"geo",zlevel:10,progressive:1e4,progressiveThreshold:5e4,blendMode:"source-over",lineStyle:{opacity:.8},postEffect:{enable:!1,colorCorrection:{exposure:0,brightness:0,contrast:1,saturation:1,enable:!0}}}});const Aa=P.extend({type:"linesGL",__ecgl__:!0,init:function(e,t){this.groupGL=new Pt.Node,this.viewGL=new Wi("orthographic"),this.viewGL.add(this.groupGL),this._glViewHelper=new la(this.viewGL),this._nativeLinesShader=Pt.createShader("ecgl.lines3D"),this._meshLinesShader=Pt.createShader("ecgl.meshLines3D"),this._linesMeshes=[],this._currentStep=0},render:function(e,t,i){this.groupGL.removeAll(),this._glViewHelper.reset(e,i);var n=this._linesMeshes[0];n||(n=this._linesMeshes[0]=this._createLinesMesh(e)),this._linesMeshes.length=1,this.groupGL.add(n),this._updateLinesMesh(e,n,0,e.getData().count()),this.viewGL.setPostEffect(e.getModel("postEffect"),i)},incrementalPrepareRender:function(e,t,i){this.groupGL.removeAll(),this._glViewHelper.reset(e,i),this._currentStep=0,this.viewGL.setPostEffect(e.getModel("postEffect"),i)},incrementalRender:function(e,t,i,n){var r=this._linesMeshes[this._currentStep];r||(r=this._createLinesMesh(t),this._linesMeshes[this._currentStep]=r),this._updateLinesMesh(t,r,e.start,e.end),this.groupGL.add(r),n.getZr().refresh(),this._currentStep++},updateTransform:function(e,t,i){e.coordinateSystem.getRoamTransform&&this._glViewHelper.updateTransform(e,i)},_createLinesMesh:function(e){return new Pt.Mesh({$ignorePicking:!0,material:new Pt.Material({shader:Pt.createShader("ecgl.lines3D"),transparent:!0,depthMask:!1,depthTest:!1}),geometry:new fa({segmentScale:10,useNativeLine:!0,dynamic:!1}),mode:Pt.Mesh.LINES,culling:!1})},_updateLinesMesh:function(e,t,i,n){var r=e.getData();t.material.blend="lighter"===e.get("blendMode")?Pt.additiveBlend:null;var o=e.get("lineStyle.curveness")||0,a=e.get("polyline"),s=t.geometry,l=e.coordinateSystem,h=St(e.get("lineStyle.width"),1);h>1?(t.material.shader!==this._meshLinesShader&&t.material.attachShader(this._meshLinesShader),t.mode=Pt.Mesh.TRIANGLES):(t.material.shader!==this._nativeLinesShader&&t.material.attachShader(this._nativeLinesShader),t.mode=Pt.Mesh.LINES),i=i||0,n=n||r.count(),s.resetOffset();var d=0,c=0,u=[],f=[],p=[],m=[],g=[],_=.3,v=.7;function x(){f[0]=u[0]*v+m[0]*_-(u[1]-m[1])*o,f[1]=u[1]*v+m[1]*_-(m[0]-u[0])*o,p[0]=u[0]*_+m[0]*v-(u[1]-m[1])*o,p[1]=u[1]*_+m[1]*v-(m[0]-u[0])*o}if(a||0!==o)for(var y=i;y<n;y++)if(a){var b=e.getLineCoordsCount(y);d+=s.getPolylineVertexCount(b),c+=s.getPolylineTriangleCount(b)}else e.getLineCoords(y,g),this._glViewHelper.dataToPoint(l,g[0],u),this._glViewHelper.dataToPoint(l,g[1],m),x(),d+=s.getCubicCurveVertexCount(u,f,p,m),c+=s.getCubicCurveTriangleCount(u,f,p,m);else{var w=n-i;d+=w*s.getLineVertexCount(),c+=w*s.getLineVertexCount()}s.setVertexCount(d),s.setTriangleCount(c);var T=i,S=[];for(y=i;y<n;y++){Pt.parseColor(Hn(r,T),S);var M=St(Un(r,T),1);S[3]*=M;b=e.getLineCoords(y,g);for(var D=0;D<b;D++)this._glViewHelper.dataToPoint(l,g[D],g[D]);a?s.addPolyline(g,S,h,0,b):0!==o?(u=g[0],m=g[1],x(),s.addCubicCurve(u,f,p,m,S,h)):s.addPolyline(g,S,h,0,2),T++}},dispose:function(){this.groupGL.removeAll()},remove:function(){this.groupGL.removeAll()}});m((function(e){e.registerChartView(Aa),e.registerSeriesModel(Ca)}));
