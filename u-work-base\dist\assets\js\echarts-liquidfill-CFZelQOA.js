import{I as e,k as t,S as a,J as i,K as r,L as n,N as l,P as s,Q as o,R as h,T as d,B as u}from"./echarts-PHL8p6cd.js";import{Q as p,J as g,_ as v,O as c,N as m,F as y}from"./zrender-DJ-1imfX.js";e({type:"series.liquidFill",optionUpdated:function(){var e=this.option;e.gridSize=Math.max(Math.floor(e.gridSize),4)},getInitialData:function(e,i){var r=t(e.data,{coordDimensions:["value"]}),n=new a(r,this);return n.initData(e.data),n},defaultOption:{color:["#294D99","#156ACF","#1598ED","#45BDFF"],center:["50%","50%"],radius:"50%",amplitude:"8%",waveLength:"80%",phase:"auto",period:"auto",direction:"right",shape:"circle",waveAnimation:!0,animationEasing:"linear",animationEasingUpdate:"linear",animationDuration:2e3,animationDurationUpdate:1e3,outline:{show:!0,borderDistance:8,itemStyle:{color:"none",borderColor:"#294D99",borderWidth:8,shadowBlur:20,shadowColor:"rgba(0, 0, 0, 0.25)"}},backgroundStyle:{color:"#E3F7FF"},itemStyle:{opacity:.95,shadowBlur:50,shadowColor:"rgba(0, 0, 0, 0.4)"},label:{show:!0,color:"#294D99",insideColor:"#fff",fontSize:50,fontWeight:"bold",align:"center",baseline:"middle",position:"inside"},emphasis:{itemStyle:{opacity:.8}}}});const w=i({type:"ec-liquid-fill",shape:{waveLength:0,radius:0,radiusY:0,cx:0,cy:0,waterLevel:0,amplitude:0,phase:0,inverse:!1},buildPath:function(e,t){null==t.radiusY&&(t.radiusY=t.radius);for(var a=Math.max(2*Math.ceil(2*t.radius/t.waveLength*4),8);t.phase<2*-Math.PI;)t.phase+=2*Math.PI;for(;t.phase>0;)t.phase-=2*Math.PI;var i=t.phase/Math.PI/2*t.waveLength,r=t.cx-t.radius+i-2*t.radius;e.moveTo(r,t.waterLevel);for(var n=0,l=0;l<a;++l){var s=l%4,o=f(l*t.waveLength/4,s,t.waveLength,t.amplitude);e.bezierCurveTo(o[0][0]+r,-o[0][1]+t.waterLevel,o[1][0]+r,-o[1][1]+t.waterLevel,o[2][0]+r,-o[2][1]+t.waterLevel),l===a-1&&(n=o[2][0])}t.inverse?(e.lineTo(n+r,t.cy-t.radiusY),e.lineTo(r,t.cy-t.radiusY),e.lineTo(r,t.waterLevel)):(e.lineTo(n+r,t.cy+t.radiusY),e.lineTo(r,t.cy+t.radiusY),e.lineTo(r,t.waterLevel)),e.closePath()}});function f(e,t,a,i){return 0===t?[[e+.5*a/Math.PI/2,i/2],[e+.5*a/Math.PI,i],[e+a/4,i]]:1===t?[[e+.5*a/Math.PI/2*(Math.PI-2),i],[e+.5*a/Math.PI/2*(Math.PI-1),i/2],[e+a/4,0]]:2===t?[[e+.5*a/Math.PI/2,-i/2],[e+.5*a/Math.PI,-i],[e+a/4,-i]]:[[e+.5*a/Math.PI/2*(Math.PI-2),-i],[e+.5*a/Math.PI/2*(Math.PI-1),-i/2],[e+a/4,0]]}var M=n;function I(e){return e&&0===e.indexOf("path://")}r({type:"liquidFill",render:function(e,t,a){var i=this,r=this.group;r.removeAll();var n=e.getData(),f=n.getItemModel(0),P=f.get("center"),b=f.get("radius"),L=a.getWidth(),x=a.getHeight(),S=Math.min(L,x),C=0,D=0,T=e.get("outline.show");T&&(C=e.get("outline.borderDistance"),D=M(e.get("outline.itemStyle.borderWidth"),S));var F,E,Y,z=M(P[0],L),A=M(P[1],x),B=!1,O=e.get("shape");("container"===O?(B=!0,E=[(F=[L/2,x/2])[0]-D/2,F[1]-D/2],Y=[M(C,L),M(C,x)],b=[Math.max(E[0]-Y[0],0),Math.max(E[1]-Y[1],0)]):(E=(F=M(b,S)/2)-D/2,Y=M(C,S),b=Math.max(E-Y,0)),T)&&(j().style.lineWidth=D,r.add(j()));var W=B?0:z-b,_=B?0:A-b,k=null;r.add(function(){var t=G(b);t.setStyle(e.getModel("backgroundStyle").getItemStyle()),t.style.fill=null,t.z2=5;var a=G(b);a.setStyle(e.getModel("backgroundStyle").getItemStyle()),a.style.stroke=null;var i=new p;return i.add(t),i.add(a),i}());var N=this._data,U=[];function G(e,t){if(O){if(I(O)){var a=d(O.slice(7),{}),i=a.getBoundingRect(),r=i.width,n=i.height;r>n?(n*=2*e/r,r=2*e):(r*=2*e/n,n=2*e);var l=t?0:z-r/2,s=t?0:A-n/2;return a=d(O.slice(7),{},new m(l,s,r,n)),t&&(a.x=-r/2,a.y=-n/2),a}if(B){var o=t?-e[0]:z-e[0],h=t?-e[1]:A-e[1];return u("rect",o,h,2*e[0],2*e[1])}h=t?-e:A-e;return"pin"===O?h+=e:"arrow"===O&&(h-=e),u(O,o=t?-e:z-e,h,2*e,2*e)}return new y({shape:{cx:t?0:z,cy:t?0:A,r:e}})}function j(){var t=G(F);return t.style.fill=null,t.setStyle(e.getModel("outline.itemStyle").getItemStyle()),t}function q(t,a,i){var r=B?b[0]:b,l=B?x/2:b,s=n.getItemModel(t),h=s.getModel("itemStyle"),d=s.get("phase"),u=M(s.get("amplitude"),2*l),p=M(s.get("waveLength"),2*r),g=l-n.get("value",t)*l*2;d=i?i.shape.phase:"auto"===d?t*Math.PI/4:d;var v=h.getItemStyle();if(!v.fill){var c=e.get("color"),m=t%c.length;v.fill=c[m]}var y=new w({shape:{waveLength:p,radius:r,radiusY:l,cx:2*r,cy:0,waterLevel:g,amplitude:u,phase:d,inverse:a},style:v,x:z,y:A});y.shape._waterLevel=g;var f=s.getModel("emphasis.itemStyle").getItemStyle();f.lineWidth=0,y.ensureState("emphasis").style=f,o(y);var I=G(b,!0);return I.setStyle({fill:"white"}),y.setClipPath(I),y}function J(e,t,a){var i=n.getItemModel(e),r=i.get("period"),l=i.get("direction"),s=n.get("value",e),o=i.get("phase");o=a?a.shape.phase:"auto"===o?e*Math.PI/4:o;var h,d,u=0;"auto"===r?(h=5e3,u=0===(d=n.count())?h:h*(.2+(d-e)/d*.8)):u="function"==typeof r?r(s,e):r;var p=0;"right"===l||null==l?p=Math.PI:"left"===l?p=-Math.PI:"none"===l&&(p=0),"none"!==l&&i.get("waveAnimation")&&t.animate("shape",!0).when(0,{phase:o}).when(u/2,{phase:p+o}).when(u,{phase:2*p+o}).during((function(){k&&k.dirty(!0)})).start()}n.diff(N).add((function(t){var a=q(t,!1),i=a.shape.waterLevel;a.shape.waterLevel=B?x/2:b,l(a,{shape:{waterLevel:i}},e),a.z2=2,J(t,a,null),r.add(a),n.setItemGraphicEl(t,a),U.push(a)})).update((function(t,a){for(var l=N.getItemGraphicEl(a),o=q(t,!1,l),h={},d=["amplitude","cx","cy","phase","radius","radiusY","waterLevel","waveLength"],u=0;u<d.length;++u){var p=d[u];o.shape.hasOwnProperty(p)&&(h[p]=o.shape[p])}var g={},v=["fill","opacity","shadowBlur","shadowColor"];for(u=0;u<v.length;++u){p=v[u];o.style.hasOwnProperty(p)&&(g[p]=o.style[p])}B&&(h.radiusY=x/2),s(l,{shape:h,x:o.x,y:o.y},e),e.isUniversalTransitionEnabled&&e.isUniversalTransitionEnabled()?s(l,{style:g},e):l.useStyle(g);var c=l.getClipPath(),m=o.getClipPath();l.setClipPath(o.getClipPath()),l.shape.inverse=o.inverse,c&&m&&i._shape===O&&!I(O)&&s(m,{shape:c.shape},e,{isFrom:!0}),J(t,l,l),r.add(l),n.setItemGraphicEl(t,l),U.push(l)})).remove((function(e){var t=N.getItemGraphicEl(e);r.remove(t)})).execute(),f.get("label.show")&&r.add(function(t){var a=f.getModel("label");function i(){var t=e.getFormattedLabel(0,"normal"),a=100*n.get("value",0),i=n.getName(0)||e.name;return isNaN(a)||(i=a.toFixed(0)+"%"),null==t?i:t}var r={z2:10,shape:{x:W,y:_,width:2*(B?b[0]:b),height:2*(B?b[1]:b)},style:{fill:"transparent"},textConfig:{position:a.get("position")||"inside"},silent:!0},l={style:{text:i(),textAlign:a.get("align"),textVerticalAlign:a.get("baseline")}};Object.assign(l.style,h(a));var s=new g(r),o=new g(r);o.disableLabelAnimation=!0,s.disableLabelAnimation=!0;var d=new v(l),u=new v(l);s.setTextContent(d),o.setTextContent(u);var m=a.get("insideColor");u.style.fill=m;var y=new p;y.add(s),y.add(o);var w=G(b,!0);return(k=new c({shape:{paths:t},x:z,y:A})).setClipPath(w),o.setClipPath(k),y}(U)),this._shape=O,this._data=n},dispose:function(){}});
