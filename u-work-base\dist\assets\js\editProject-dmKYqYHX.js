import{M as e,N as a,g as l,b as t}from"./main-Djn9RDyT.js";import{v as s,i as u,e as o,l as r,g as d}from"./projectGallery-xT8wgNPG.js";import{a as i}from"./axios-7z2hFSF6.js";import{S as n,F as c,b as p,c as v,I as m,B as f,y as h,z as g,T as b,O as j,_,i as y,P as k,Q as w,d as C,M as D}from"./ant-design-vue-DYY9BtJq.js";import{d as S,r as x,am as M,a9 as U,o as I,aa as N,c as Y,e as P,ab as q,u as L,J as V,ad as $,b as O,F as z,ag as E,n as A}from"./@vue-HScy-mz9.js";import{_ as F}from"./vue-qr-CB2aNKv5.js";import"./element-plus-DGm4IBRH.js";import"./lodash-es-CsK89k5Z.js";import"./@element-plus-BuZNC1gX.js";import"./@popperjs-DxP-MrnL.js";import"./@ctrl-B2IeE8ye.js";import"./dayjs-CA7qlNSr.js";import"./@babel-B4rXMRun.js";import"./async-validator-BTKOuuO-.js";import"./memoize-one-Ds0C_khL.js";import"./normalize-wheel-es-BhHBPXsK.js";import"./@floating-ui-anUbB2i9.js";import"./@ant-design-CA72ad83.js";import"./wujie-vue3--W-3MQbm.js";import"./wujie-BrX_NCCF.js";import"./pinia-CheWBXuN.js";import"./monaco-editor-K5t0WTxP.js";import"./vue-router-BEwRlUkF.js";import"./nprogress-DfxabVLP.js";import"./vue3-cookies-D4wQmYyh.js";import"./pinyin-pro-DCC_6C_3.js";import"./jsencrypt-BWvXBO1z.js";import"./validator-DM5yI3AY.js";import"./crypto-js-Duvj5un5.js";/* empty css                    *//* empty css                      */import"./resize-observer-polyfill-CzGuHLZU.js";import"./@emotion-BqFXxR3o.js";import"./stylis-BXpgKhw9.js";import"./scroll-into-view-if-needed-NubnV8l_.js";import"./compute-scroll-into-view-DCNUDwgY.js";import"./vue-types-DUrkvkDc.js";import"./dom-align-EUo7BKhy.js";import"./throttle-debounce-w9OM8Bxz.js";import"./js-binary-schema-parser-G48GG52R.js";const R={class:"i-box"},T={style:{display:"flex","align-items":"center"}},J={class:"i-box"},B={style:{display:"flex","justify-content":"space-between"}},H={class:"upload-wrap"},G=["accept"],K={class:"file-list"},Q=["onClick"],W={class:"form-item-notice keep-px"},X={class:"upload-wrap"},Z=["accept"],ee={class:"file-list"},ae=["onClick"],le={class:"form-item-notice keep-px"},te=F(S({__name:"editProject",emits:["ok"],setup(S,{expose:F,emit:te}){const se=te,ue=x(!1),oe=x(),re=x(!1),de=x(!1),ie=x(),ne=x([]),ce=async()=>{const e=ve.value.contractCode;if(e)try{const t=await a({tenantCode:e}),{tenant:s,token:u}=t.data;i.get(`${window.config.appApi}/systemx/project-pack/page`,{params:{pageNo:1,pageSize:99},headers:{Tenant:e,Authorization:`Bearer ${u}`,"X-Security-FreshToken":l()}}).then((e=>{var a,l;const t=(null==(l=null==(a=e.data)?void 0:a.data)?void 0:l.rows)||[];t.length&&(ne.value=t.map((e=>({label:e.name,value:pe(e.url)}))))})).catch((()=>{ne.value=[]}))}catch(t){ne.value=[]}},pe=e=>`${window.location.origin}/osr/resource/${e}index.html`,ve=x({projectName:"",contractCode:"",productName:"",productVersion:"",techPlatform:"",saleDepartment:"",projectManager:"",salesManager:"",saleSupporter:"",industry:"",ultimateCustomer:"",contractedCustomer:"",projectStartDate:"",projectEndDate:"",projectStatus:"",screenResolution:"",projectAccessLinker:"",projectSimpleDescription:"",overallDemand:"",projectScope:"",functionalDecompositio:"",projectPracticalSummary:"",deliverablesImages:[],deliverablesVideo:[],tagIds:[],id:""}),me={contractCode:[{required:!0,message:"请输入合同编号"}],projectName:[{required:!0,message:"请输入项目名称"}],techPlatform:[{required:!0,message:"请输入采纳的技术平台"}],saleDepartment:[{required:!0,message:"请输入所属部门"}],projectManager:[{required:!0,message:"请输入项目经理"}],salesManager:[{required:!0,message:"请输入销售经理"}],projectStatus:[{required:!0,message:"请输入项目状态"}],projectAccessLinker:[{required:!1,validator:e}],deliverablesImages:[{required:!0,message:"请上传封面图",trigger:"change"}],tagIds:[{required:!0,message:"请选择标签"},{required:!0,validator:(e,a,l)=>{let t=0;return a.forEach((e=>{if(1===e.length)if(ge.value.includes(e[0])){const a=fe.value.find((a=>a.value===e[0]));a&&a.children&&(t+=a.children.length)}else t++;else t=t+e.length-1})),t>10?Promise.reject(new Error("最多选择10个标签")):Promise.resolve()}}]},fe=x([]),he=x(new Map),ge=x([]),be=x(new Map),je=x(new Map),_e=(e,a)=>a.some((a=>a.label.toLowerCase().indexOf(e.toLowerCase())>-1)),ye=x(["mp4","MP4"]),ke=async e=>{const a=e.target;if(ve.value.deliverablesVideo.length>=10)return t("warning","最多支持上传10个文件"),a.value="",!1;const l=a.files[0],{size:u,name:o}=l,r=o.lastIndexOf("."),d=o.slice(r+1).toLocaleLowerCase();if(!ye.value.includes(d))return t("warning",`文件后缀必须是${ye.value.join("/")}`),a.value="",!1;if(u>1073741824)return t("warning","文件大小不能超过1024M"),a.value="",!1;a.value="",de.value=!0;const i=new FormData;i.append("file",l),i.append("bucket","edtp-source"),i.append("replaceName","false");const n=await s(i);200===n.code?(de.value=!1,ve.value.deliverablesVideo.push(n.data),t("success","视频上传成功")):(de.value=!1,t("error",n.message))},we=x(!1),Ce=x(),De=(e,a)=>{we.value=e,"string"==typeof a&&(Ce.value=Se(a))},Se=e=>`${e}`,xe=x(["png","jpg","jpeg","gif"]),Me=async e=>{const a=e.target;if(ve.value.deliverablesImages.length>=10)return t("warning","最多支持上传10个文件"),a.value="",!1;const l=a.files[0],{size:s,name:o}=l,r=o.lastIndexOf("."),d=o.slice(r+1).toLocaleLowerCase();if(!xe.value.includes(d))return t("warning",`文件后缀必须是${xe.value.join("/")}`),a.value="",!1;if(s>5242880)return t("warning","文件大小不能超过5M"),a.value="",!1;a.value="",de.value=!0;const i=new FormData;i.append("file",l),i.append("bucket","edtp-source"),i.append("replaceName","false");const n=await u(i);200===n.code?(ve.value.deliverablesImages.push(n.data),ie.value.validateFields(["deliverablesImages"]),t("success","图片上传成功"),de.value=!1):(t("error",n.message),de.value=!1)},Ue=x(new Map),Ie=x(!1),Ne=((e,a)=>{let l;return function(...t){l||(e.apply(this,t),l=setTimeout((()=>{l=null}),a))}})((()=>{d(ve.value.contractCode).then((e=>{200===e.code?(Object.keys(e.data).forEach((a=>{if(e.data[a]&&(ve.value[a]=e.data[a]),("productName"===a||"industry"===a)&&e.data[a]&&Ue.value.size){const l=Ue.value.get(e.data[a]);l&&ve.value.tagIds.push(l.split("-"))}})),e.data.contractCode?Ie.value=!0:t("error","合同编号不存在")):t("error",e.message)}))}),1e3),Ye=x(["",""]),Pe=()=>{de.value||(ie.value.resetFields(),ne.value=[],ue.value=!1,de.value=!1,de.value=!1,Ie.value=!1)},qe=()=>{let e=[];return ve.value.tagIds.forEach((a=>{1===a.length?e=e.concat(be.value.get(a[0])):e.push(a[1])})),e};return F({init:e=>{ue.value=!0,Ie.value=!0,ne.value=[],new Promise((e=>{r().then((a=>{const l=a.data.map((e=>{var a;return ge.value.push(e.id),be.value.set(e.id,[]),{value:e.id,label:`${e.groupName}`,children:null==(a=e.tags)?void 0:a.map((a=>(be.value.get(e.id).push(a.id),he.value.set(`${a.tagName}`,a.color),je.value.set(a.id,e.id),{value:a.id,label:`${a.tagName}`})))}}));fe.value=l.filter((e=>e.children)),e(!0)}))})).then((()=>{A((()=>{ie.value.resetFields(),Object.keys(e).forEach((a=>{ve.value[a]=e[a]})),ve.value.tagIds=(e=>{const a=[];return e.forEach((e=>{a.push([je.value.get(e.id),e.id])})),a})(e.exTags);try{Ye.value=Array.isArray(JSON.parse(e.projectPracticalSummary))?JSON.parse(e.projectPracticalSummary):["",""]}catch(a){Ye.value=["",""]}ce()}))}))}}),(e,a)=>{const l=m,s=f,u=v,r=p,d=h,i=b,S=g,x=j,A=y,F=k,te=w,pe=M("DeleteOutlined"),ge=M("question-circle-outlined"),be=C,je=_,Ue=c,Le=n,Ve=D;return I(),U(Ve,{width:1200,title:"编辑","body-style":{maxHeight:"600px",overflow:"auto"},"wrap-class-name":"cus-modal",open:ue.value,"confirm-loading":de.value,"mask-closable":!1,"ok-text":"确认","cancel-text":"取消",onOk:a[24]||(a[24]=e=>{de.value||(de.value=!0,ie.value.validate().then((()=>{const e={...ve.value,projectPracticalSummary:JSON.stringify(Ye.value),tagIds:qe()};o(e).then((e=>{de.value=!1,200===e.code?(t("success","项目案例编辑成功"),se("ok"),Pe()):t("error",e.message)}))})).catch((e=>{de.value=!1})))}),onCancel:Pe},{default:N((()=>[Y(Le,{spinning:de.value,style:{position:"fixed",top:"250px"}},{default:N((()=>[Y(Ue,{ref_key:"formRef",ref:ie,model:ve.value,rules:me,"label-align":"left"},{default:N((()=>[P("header",null,[P("div",R,[Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{class:"project-code-item keep-px",name:"contractCode",label:"合同编号","has-feedback":""},{default:N((()=>[P("div",T,[Y(l,{value:ve.value.contractCode,"onUpdate:value":a[0]||(a[0]=e=>ve.value.contractCode=e),onBlur:ce,maxlength:20,disabled:Ie.value,style:{width:"100%"},placeholder:"请输入合同编号"},null,8,["value","disabled"]),re.value&&1===oe.value?(I(),U(s,{key:0,disabled:!ve.value.contractCode,style:{"min-width":"80px",padding:"0"},type:"primary",onClick:L(Ne)},{default:N((()=>a[25]||(a[25]=[V("同步")]))),_:1},8,["disabled","onClick"])):q("",!0)])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"productName",label:"产品信息(合同)","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.productName,"onUpdate:value":a[1]||(a[1]=e=>ve.value.productName=e),maxlength:30,placeholder:"请输入产品信息(合同)"},null,8,["value"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"techPlatform",label:"采纳的技术平台","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.techPlatform,"onUpdate:value":a[2]||(a[2]=e=>ve.value.techPlatform=e),maxlength:30,placeholder:"请输入采纳的技术平台"},null,8,["value"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"projectManager",label:"项目经理","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.projectManager,"onUpdate:value":a[3]||(a[3]=e=>ve.value.projectManager=e),disabled:1===oe.value,placeholder:"请输入项目经理"},null,8,["value","disabled"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"saleSupporter",label:"支持售前","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.saleSupporter,"onUpdate:value":a[4]||(a[4]=e=>ve.value.saleSupporter=e),maxlength:30,placeholder:"请输入支持售前"},null,8,["value"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"ultimateCustomer",label:"最终客户名称","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.ultimateCustomer,"onUpdate:value":a[5]||(a[5]=e=>ve.value.ultimateCustomer=e),maxlength:30,placeholder:"请输入最终客户名称"},null,8,["value"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"projectStartDate",label:"项目启动时间","has-feedback":""},{default:N((()=>[Y(d,{value:ve.value.projectStartDate,"onUpdate:value":a[6]||(a[6]=e=>ve.value.projectStartDate=e),disabled:1===oe.value,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",placeholder:"请选择项目启动时间"},null,8,["value","disabled"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"projectStatus",label:"项目状态","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.projectStatus,"onUpdate:value":a[7]||(a[7]=e=>ve.value.projectStatus=e),disabled:1===oe.value,placeholder:"请输入项目状态"},null,8,["value","disabled"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"tagIds",label:"资源标签","has-feedback":""},{default:N((()=>[Y(S,{value:ve.value.tagIds,"onUpdate:value":a[8]||(a[8]=e=>ve.value.tagIds=e),defaultValue:ve.value.tagIds,"show-checked-strategy":L(g).SHOW_CHILD,style:{width:"100%"},multiple:"","show-search":{filter:_e},"max-tag-count":"responsive",options:fe.value,placeholder:"请选择标签",checkStrictly:!0},{tagRender:N((e=>{return[(I(),U(i,{key:e.value,color:(a=e.label,he.value.get(a)||"blue")},{default:N((()=>[V($(e.label),1)])),_:2},1032,["color"]))];var a})),_:1},8,["value","defaultValue","show-checked-strategy","show-search","options"])])),_:1})])),_:1})]),P("div",J,[Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"projectName",label:"项目名称","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.projectName,"onUpdate:value":a[9]||(a[9]=e=>ve.value.projectName=e),disabled:1===oe.value,title:ve.value.projectName,placeholder:"请输入项目名称"},null,8,["value","disabled","title"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"productVersion",label:"产品版本","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.productVersion,"onUpdate:value":a[10]||(a[10]=e=>ve.value.productVersion=e),maxlength:30,placeholder:"请输入产品版本"},null,8,["value"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"saleDepartment",label:"所属部门(销售)","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.saleDepartment,"onUpdate:value":a[11]||(a[11]=e=>ve.value.saleDepartment=e),disabled:1===oe.value,placeholder:"请输入所属部门(销售)"},null,8,["value","disabled"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"salesManager",label:"销售经理","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.salesManager,"onUpdate:value":a[12]||(a[12]=e=>ve.value.salesManager=e),disabled:1===oe.value,placeholder:"请输入销售经理"},null,8,["value","disabled"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"industry",label:"行业","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.industry,"onUpdate:value":a[13]||(a[13]=e=>ve.value.industry=e),disabled:1===oe.value,placeholder:"请输入行业"},null,8,["value","disabled"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"contractedCustomer",label:"签约客户名称","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.contractedCustomer,"onUpdate:value":a[14]||(a[14]=e=>ve.value.contractedCustomer=e),disabled:1===oe.value,placeholder:"请输入签约客户名称"},null,8,["value","disabled"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"projectEndDate",label:"项目完成时间","has-feedback":""},{default:N((()=>[Y(d,{value:ve.value.projectEndDate,"onUpdate:value":a[15]||(a[15]=e=>ve.value.projectEndDate=e),disabled:1===oe.value,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",placeholder:"请选择项目完成时间"},null,8,["value","disabled"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"screenResolution",label:"屏幕分辨率","has-feedback":""},{default:N((()=>[Y(l,{value:ve.value.screenResolution,"onUpdate:value":a[16]||(a[16]=e=>ve.value.screenResolution=e),maxlength:30,placeholder:"请输入屏幕分辨率"},null,8,["value"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"projectAccessLinker",label:"项目地址","has-feedback":""},{default:N((()=>[Y(x,{value:ve.value.projectAccessLinker,"onUpdate:value":a[17]||(a[17]=e=>ve.value.projectAccessLinker=e),options:ne.value,placeholder:"请输入项目地址"},null,8,["value","options"])])),_:1})])),_:1})])]),Y(je,{gutter:24},{default:N((()=>[Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"projectSimpleDescription",label:"一句话简介"},{default:N((()=>[Y(A,{value:ve.value.projectSimpleDescription,"onUpdate:value":a[18]||(a[18]=e=>ve.value.projectSimpleDescription=e),rows:4,placeholder:"(在什么样的背景下)利用了什么产品，整合了什么资源，形成了什么类型的平台，为用户带来了什么样的价值。"},null,8,["value"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"proSituation",label:"项目整体需求情况"},{default:N((()=>[Y(A,{value:ve.value.overallDemand,"onUpdate:value":a[19]||(a[19]=e=>ve.value.overallDemand=e),rows:4},null,8,["value"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"projectScope",label:"项目范围"},{default:N((()=>[Y(A,{value:ve.value.projectScope,"onUpdate:value":a[20]||(a[20]=e=>ve.value.projectScope=e),rows:4,placeholder:"请输入项目的交付范围（精炼版sow）"},null,8,["value"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"functionalDecompositio",label:"功能介绍分析"},{default:N((()=>[Y(A,{value:ve.value.functionalDecompositio,"onUpdate:value":a[21]||(a[21]=e=>ve.value.functionalDecompositio=e),rows:4,placeholder:"业务场景分解描述，成果需确认无敏感内容"},null,8,["value"])])),_:1})])),_:1}),Y(r,{md:24,sm:24},{default:N((()=>[Y(u,{name:"successM",label:"项目实战经验总结"},{default:N((()=>[Y(F,null,{default:N((()=>[P("div",B,[Y(A,{value:Ye.value[0],"onUpdate:value":a[22]||(a[22]=e=>Ye.value[0]=e),style:{width:"49.5%","margin-right":"1%"},rows:4,placeholder:"项目最成功的1-5个因素"},null,8,["value"]),Y(A,{value:Ye.value[1],"onUpdate:value":a[23]||(a[23]=e=>Ye.value[1]=e),style:{width:"49.5%"},rows:4,placeholder:"项目最痛苦的1-5个因素"},null,8,["value"])])])),_:1})])),_:1})])),_:1}),Y(r,{md:24,sm:24,class:"form-item"},{default:N((()=>[Y(u,{name:"deliverablesImages",label:"上传图片","has-feedback":""},{default:N((()=>[P("div",H,[Y(s,{class:"upload-btn",type:"primary"},{default:N((()=>[a[26]||(a[26]=V(" 点击上传 ")),P("input",{type:"file",class:"input-file",accept:xe.value.map((e=>`.${e}`)).join(","),onChange:Me},null,40,G)])),_:1})]),P("div",K,[(I(!0),O(z,null,E(ve.value.deliverablesImages,((e,a)=>(I(),O("div",{key:e,class:"file-item"},[P("div",{class:"file-name",onClick:a=>De(!0,e)},[Y(te,{width:48,height:48,src:Se(e)},null,8,["src"]),V(" "+$(e),1)],8,Q),Y(pe,{class:"delete-icon",onClick:e=>(e=>{ve.value.deliverablesImages.splice(e,1)})(a)},null,8,["onClick"])])))),128)),Y(te,{width:200,style:{display:"none"},preview:{visible:we.value,onVisibleChange:De},src:Ce.value},null,8,["preview","src"])])])),_:1}),P("span",W,[Y(be,{title:"支持文件格式: png、jpeg、jpg、gif, 大小限制: 5M",placement:"right"},{default:N((()=>[Y(ge,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1}),Y(r,{md:24,sm:24,class:"form-item"},{default:N((()=>[Y(u,{name:"deliverablesVideo",label:"上传视频","has-feedback":""},{default:N((()=>[P("div",X,[Y(s,{class:"upload-btn",type:"primary"},{default:N((()=>[a[27]||(a[27]=V(" 点击上传 ")),P("input",{type:"file",class:"input-file",accept:ye.value.map((e=>`.${e}`)).join(","),onChange:ke},null,40,Z)])),_:1})]),P("div",ee,[(I(!0),O(z,null,E(ve.value.deliverablesVideo,((e,a)=>(I(),O("div",{key:e,class:"file-item"},[P("div",{class:"file-name",onClick:a=>(e=>{window.open(Se(e),"_blank")})(e)},$(e),9,ae),Y(pe,{class:"delete-icon",onClick:e=>(e=>{ve.value.deliverablesVideo.splice(e,1)})(a)},null,8,["onClick"])])))),128))])])),_:1}),P("span",le,[Y(be,{title:"支持文件格式: mp4, 大小限制:1024M",placement:"right"},{default:N((()=>[Y(ge,{style:{color:"#ef7b1a"}})])),_:1})])])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-99f2e7fc"]]);export{te as default};
