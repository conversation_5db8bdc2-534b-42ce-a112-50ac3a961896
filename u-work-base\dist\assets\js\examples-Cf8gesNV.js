import{s as t}from"./main-Djn9RDyT.js";const a="/edtap/sys-sample/page",s="/edtap/sys-sample/add",e="/edtap/sys-sample/batch-delete",o="/edtap/sys-sample/edit",r="/edtap/sys-sample/change-status";function d(s){const{data:e}=s;return t({url:a,method:"post",data:e})}function n(a,e){return t({url:s,method:"post",data:a,onUploadProgress(t){e(t)}})}function p(a,s=null){return t({url:o,method:"post",data:a,onUploadProgress(t){s&&s(t)}})}function u(a){return t({url:r,method:"post",data:a})}function l(a){return t({url:e,method:"post",data:a})}function m(){return t({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"5"}})}export{m as a,u as b,l as d,p as e,d as g,n as s};
