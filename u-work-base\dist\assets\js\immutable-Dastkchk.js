var t="delete",r=32,e=31,n={};function i(t){t&&(t.value=!0)}function o(){}function u(t){return void 0===t.size&&(t.size=t.__iterate(a)),t.size}function s(t,r){if("number"!=typeof r){var e=r>>>0;if(""+e!==r||4294967295===e)return NaN;r=e}return r<0?u(t)+r:r}function a(){return!0}function c(t,r,e){return(0===t&&!_(t)||void 0!==e&&t<=-e)&&(void 0===r||void 0!==e&&r>=e)}function f(t,r){return p(t,r,0)}function h(t,r){return p(t,r,r)}function p(t,r,e){return void 0===t?e:_(t)?Infinity===r?r:0|Math.max(0,r+t):void 0===r||r===t?t:0|Math.min(r,t)}function _(t){return t<0||0===t&&1/t==-Infinity}var l="@@__IMMUTABLE_ITERABLE__@@";function v(t){return Boolean(t&&t[l])}var y="@@__IMMUTABLE_KEYED__@@";function d(t){return Boolean(t&&t[y])}var g="@@__IMMUTABLE_INDEXED__@@";function w(t){return Boolean(t&&t[g])}function m(t){return d(t)||w(t)}var S=function(t){return v(t)?t:H(t)},z=function(t){function r(t){return d(t)?t:J(t)}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r}(S),b=function(t){function r(t){return w(t)?t:V(t)}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r}(S),I=function(t){function r(t){return v(t)&&!m(t)?t:Y(t)}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r}(S);S.Keyed=z,S.Indexed=b,S.Set=I;var O="@@__IMMUTABLE_SEQ__@@";function E(t){return Boolean(t&&t[O])}var j="@@__IMMUTABLE_RECORD__@@";function q(t){return Boolean(t&&t[j])}function M(t){return v(t)||q(t)}var D="@@__IMMUTABLE_ORDERED__@@";function A(t){return Boolean(t&&t[D])}var x="function"==typeof Symbol&&Symbol.iterator,k="@@iterator",R=x||k,U=function(t){this.next=t};function T(t,r,e,n){var i=0===t?r:1===t?e:[r,e];return n?n.value=i:n={value:i,done:!1},n}function B(){return{value:void 0,done:!0}}function K(t){return!!Array.isArray(t)||!!P(t)}function L(t){return t&&"function"==typeof t.next}function C(t){var r=P(t);return r&&r.call(t)}function P(t){var r=t&&(x&&t[x]||t[k]);if("function"==typeof r)return r}U.prototype.toString=function(){return"[Iterator]"},U.KEYS=0,U.VALUES=1,U.ENTRIES=2,U.prototype.inspect=U.prototype.toSource=function(){return this.toString()},U.prototype[R]=function(){return this};var W=Object.prototype.hasOwnProperty;function N(t){return!(!Array.isArray(t)&&"string"!=typeof t)||t&&"object"==typeof t&&Number.isInteger(t.length)&&t.length>=0&&(0===t.length?1===Object.keys(t).length:t.hasOwnProperty(t.length-1))}var H=function(t){function r(t){return null==t?Z():M(t)?t.toSeq():function(t){var r=rt(t);if(r)return(n=P(e=t))&&n===e.entries?r.fromEntrySeq():function(t){var r=P(t);return r&&r===t.keys}(t)?r.toSetSeq():r;var e,n;if("object"==typeof t)return new X(t);throw new TypeError("Expected Array or collection object of values, or keyed object: "+t)}(t)}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.toSeq=function(){return this},r.prototype.toString=function(){return this.__toString("Seq {","}")},r.prototype.cacheResult=function(){return!this._cache&&this.__iterateUncached&&(this._cache=this.entrySeq().toArray(),this.size=this._cache.length),this},r.prototype.__iterate=function(t,r){var e=this._cache;if(e){for(var n=e.length,i=0;i!==n;){var o=e[r?n-++i:i++];if(!1===t(o[1],o[0],this))break}return i}return this.__iterateUncached(t,r)},r.prototype.__iterator=function(t,r){var e=this._cache;if(e){var n=e.length,i=0;return new U((function(){if(i===n)return{value:void 0,done:!0};var o=e[r?n-++i:i++];return T(t,o[0],o[1])}))}return this.__iteratorUncached(t,r)},r}(S),J=function(t){function r(t){return null==t?Z().toKeyedSeq():v(t)?d(t)?t.toSeq():t.fromEntrySeq():q(t)?t.toSeq():$(t)}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.toKeyedSeq=function(){return this},r}(H),V=function(t){function r(t){return null==t?Z():v(t)?d(t)?t.entrySeq():t.toIndexedSeq():q(t)?t.toSeq().entrySeq():tt(t)}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.of=function(){return r(arguments)},r.prototype.toIndexedSeq=function(){return this},r.prototype.toString=function(){return this.__toString("Seq [","]")},r}(H),Y=function(t){function r(t){return(v(t)&&!m(t)?t:V(t)).toSetSeq()}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.of=function(){return r(arguments)},r.prototype.toSetSeq=function(){return this},r}(H);H.isSeq=E,H.Keyed=J,H.Set=Y,H.Indexed=V,H.prototype[O]=!0;var Q=function(t){function r(t){this._array=t,this.size=t.length}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.get=function(t,r){return this.has(t)?this._array[s(this,t)]:r},r.prototype.__iterate=function(t,r){for(var e=this._array,n=e.length,i=0;i!==n;){var o=r?n-++i:i++;if(!1===t(e[o],o,this))break}return i},r.prototype.__iterator=function(t,r){var e=this._array,n=e.length,i=0;return new U((function(){if(i===n)return{value:void 0,done:!0};var o=r?n-++i:i++;return T(t,o,e[o])}))},r}(V),X=function(t){function r(t){var r=Object.keys(t).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t):[]);this._object=t,this._keys=r,this.size=r.length}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.get=function(t,r){return void 0===r||this.has(t)?this._object[t]:r},r.prototype.has=function(t){return W.call(this._object,t)},r.prototype.__iterate=function(t,r){for(var e=this._object,n=this._keys,i=n.length,o=0;o!==i;){var u=n[r?i-++o:o++];if(!1===t(e[u],u,this))break}return o},r.prototype.__iterator=function(t,r){var e=this._object,n=this._keys,i=n.length,o=0;return new U((function(){if(o===i)return{value:void 0,done:!0};var u=n[r?i-++o:o++];return T(t,u,e[u])}))},r}(J);X.prototype[D]=!0;var F,G=function(t){function r(t){this._collection=t,this.size=t.length||t.size}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.__iterateUncached=function(t,r){if(r)return this.cacheResult().__iterate(t,r);var e=C(this._collection),n=0;if(L(e))for(var i;!(i=e.next()).done&&!1!==t(i.value,n++,this););return n},r.prototype.__iteratorUncached=function(t,r){if(r)return this.cacheResult().__iterator(t,r);var e=C(this._collection);if(!L(e))return new U(B);var n=0;return new U((function(){var r=e.next();return r.done?r:T(t,n++,r.value)}))},r}(V);function Z(){return F||(F=new Q([]))}function $(t){var r=rt(t);if(r)return r.fromEntrySeq();if("object"==typeof t)return new X(t);throw new TypeError("Expected Array or collection object of [k, v] entries, or keyed object: "+t)}function tt(t){var r=rt(t);if(r)return r;throw new TypeError("Expected Array or collection object of values: "+t)}function rt(t){return N(t)?new Q(t):K(t)?new G(t):void 0}var et="@@__IMMUTABLE_MAP__@@";function nt(t){return Boolean(t&&t[et])}function it(t){return nt(t)&&A(t)}function ot(t){return Boolean(t&&"function"==typeof t.equals&&"function"==typeof t.hashCode)}function ut(t,r){if(t===r||t!=t&&r!=r)return!0;if(!t||!r)return!1;if("function"==typeof t.valueOf&&"function"==typeof r.valueOf){if((t=t.valueOf())===(r=r.valueOf())||t!=t&&r!=r)return!0;if(!t||!r)return!1}return!!(ot(t)&&ot(r)&&t.equals(r))}var st="function"==typeof Math.imul&&-2===Math.imul(4294967295,2)?Math.imul:function(t,r){var e=65535&(t|=0),n=65535&(r|=0);return e*n+((t>>>16)*n+e*(r>>>16)<<16>>>0)|0};function at(t){return t>>>1&1073741824|3221225471&t}var ct=Object.prototype.valueOf;function ft(t){if(null==t)return ht(t);if("function"==typeof t.hashCode)return at(t.hashCode(t));var r,e=(r=t).valueOf!==ct&&"function"==typeof r.valueOf?r.valueOf(r):r;if(null==e)return ht(e);switch(typeof e){case"boolean":return e?1108378657:1108378656;case"number":return function(t){if(t!=t||Infinity===t)return 0;var r=0|t;r!==t&&(r^=4294967295*t);for(;t>4294967295;)r^=t/=4294967295;return at(r)}(e);case"string":return e.length>St?function(t){var r=It[t];void 0===r&&(r=pt(t),bt===zt&&(bt=0,It={}),bt++,It[t]=r);return r}(e):pt(e);case"object":case"function":return function(t){var r;if(dt&&void 0!==(r=yt.get(t)))return r;if(r=t[mt],void 0!==r)return r;if(!lt){if(void 0!==(r=t.propertyIsEnumerable&&t.propertyIsEnumerable[mt]))return r;if(void 0!==(r=function(t){if(t&&t.nodeType>0)switch(t.nodeType){case 1:return t.uniqueID;case 9:return t.documentElement&&t.documentElement.uniqueID}}(t)))return r}if(r=vt(),dt)yt.set(t,r);else{if(void 0!==_t&&!1===_t(t))throw new Error("Non-extensible objects are not allowed as keys.");if(lt)Object.defineProperty(t,mt,{enumerable:!1,configurable:!1,writable:!1,value:r});else if(void 0!==t.propertyIsEnumerable&&t.propertyIsEnumerable===t.constructor.prototype.propertyIsEnumerable)t.propertyIsEnumerable=function(){return this.constructor.prototype.propertyIsEnumerable.apply(this,arguments)},t.propertyIsEnumerable[mt]=r;else{if(void 0===t.nodeType)throw new Error("Unable to set a non-enumerable property on object.");t[mt]=r}}return r}(e);case"symbol":return function(t){var r=gt[t];if(void 0!==r)return r;return r=vt(),gt[t]=r,r}(e);default:if("function"==typeof e.toString)return pt(e.toString());throw new Error("Value type "+typeof e+" cannot be hashed.")}}function ht(t){return null===t?1108378658:1108378659}function pt(t){for(var r=0,e=0;e<t.length;e++)r=31*r+t.charCodeAt(e)|0;return at(r)}var _t=Object.isExtensible,lt=function(){try{return Object.defineProperty({},"@",{}),!0}catch(t){return!1}}();function vt(){var t=++wt;return 1073741824&wt&&(wt=0),t}var yt,dt="function"==typeof WeakMap;dt&&(yt=new WeakMap);var gt=Object.create(null),wt=0,mt="__immutablehash__";"function"==typeof Symbol&&(mt=Symbol(mt));var St=16,zt=255,bt=0,It={},Ot=function(t){function r(t,r){this._iter=t,this._useKeys=r,this.size=t.size}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.get=function(t,r){return this._iter.get(t,r)},r.prototype.has=function(t){return this._iter.has(t)},r.prototype.valueSeq=function(){return this._iter.valueSeq()},r.prototype.reverse=function(){var t=this,r=At(this,!0);return this._useKeys||(r.valueSeq=function(){return t._iter.toSeq().reverse()}),r},r.prototype.map=function(t,r){var e=this,n=Dt(this,t,r);return this._useKeys||(n.valueSeq=function(){return e._iter.toSeq().map(t,r)}),n},r.prototype.__iterate=function(t,r){var e=this;return this._iter.__iterate((function(r,n){return t(r,n,e)}),r)},r.prototype.__iterator=function(t,r){return this._iter.__iterator(t,r)},r}(J);Ot.prototype[D]=!0;var Et=function(t){function r(t){this._iter=t,this.size=t.size}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.includes=function(t){return this._iter.includes(t)},r.prototype.__iterate=function(t,r){var e=this,n=0;return r&&u(this),this._iter.__iterate((function(i){return t(i,r?e.size-++n:n++,e)}),r)},r.prototype.__iterator=function(t,r){var e=this,n=this._iter.__iterator(1,r),i=0;return r&&u(this),new U((function(){var o=n.next();return o.done?o:T(t,r?e.size-++i:i++,o.value,o)}))},r}(V),jt=function(t){function r(t){this._iter=t,this.size=t.size}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.has=function(t){return this._iter.includes(t)},r.prototype.__iterate=function(t,r){var e=this;return this._iter.__iterate((function(r){return t(r,r,e)}),r)},r.prototype.__iterator=function(t,r){var e=this._iter.__iterator(1,r);return new U((function(){var r=e.next();return r.done?r:T(t,r.value,r.value,r)}))},r}(Y),qt=function(t){function r(t){this._iter=t,this.size=t.size}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.entrySeq=function(){return this._iter.toSeq()},r.prototype.__iterate=function(t,r){var e=this;return this._iter.__iterate((function(r){if(r){Pt(r);var n=v(r);return t(n?r.get(1):r[1],n?r.get(0):r[0],e)}}),r)},r.prototype.__iterator=function(t,r){var e=this._iter.__iterator(1,r);return new U((function(){for(;;){var r=e.next();if(r.done)return r;var n=r.value;if(n){Pt(n);var i=v(n);return T(t,i?n.get(0):n[0],i?n.get(1):n[1],r)}}}))},r}(J);function Mt(t){var r=Nt(t);return r._iter=t,r.size=t.size,r.flip=function(){return t},r.reverse=function(){var r=t.reverse.apply(this);return r.flip=function(){return t.reverse()},r},r.has=function(r){return t.includes(r)},r.includes=function(r){return t.has(r)},r.cacheResult=Ht,r.__iterateUncached=function(r,e){var n=this;return t.__iterate((function(t,e){return!1!==r(e,t,n)}),e)},r.__iteratorUncached=function(r,e){if(2===r){var n=t.__iterator(r,e);return new U((function(){var t=n.next();if(!t.done){var r=t.value[0];t.value[0]=t.value[1],t.value[1]=r}return t}))}return t.__iterator(1===r?0:1,e)},r}function Dt(t,r,e){var i=Nt(t);return i.size=t.size,i.has=function(r){return t.has(r)},i.get=function(i,o){var u=t.get(i,n);return u===n?o:r.call(e,u,i,t)},i.__iterateUncached=function(n,i){var o=this;return t.__iterate((function(t,i,u){return!1!==n(r.call(e,t,i,u),i,o)}),i)},i.__iteratorUncached=function(n,i){var o=t.__iterator(2,i);return new U((function(){var i=o.next();if(i.done)return i;var u=i.value,s=u[0];return T(n,s,r.call(e,u[1],s,t),i)}))},i}function At(t,r){var e=this,n=Nt(t);return n._iter=t,n.size=t.size,n.reverse=function(){return t},t.flip&&(n.flip=function(){var r=Mt(t);return r.reverse=function(){return t.flip()},r}),n.get=function(e,n){return t.get(r?e:-1-e,n)},n.has=function(e){return t.has(r?e:-1-e)},n.includes=function(r){return t.includes(r)},n.cacheResult=Ht,n.__iterate=function(e,n){var i=this,o=0;return n&&u(t),t.__iterate((function(t,u){return e(t,r?u:n?i.size-++o:o++,i)}),!n)},n.__iterator=function(n,i){var o=0;i&&u(t);var s=t.__iterator(2,!i);return new U((function(){var t=s.next();if(t.done)return t;var u=t.value;return T(n,r?u[0]:i?e.size-++o:o++,u[1],t)}))},n}function xt(t,r,e,i){var o=Nt(t);return i&&(o.has=function(i){var o=t.get(i,n);return o!==n&&!!r.call(e,o,i,t)},o.get=function(i,o){var u=t.get(i,n);return u!==n&&r.call(e,u,i,t)?u:o}),o.__iterateUncached=function(n,o){var u=this,s=0;return t.__iterate((function(t,o,a){if(r.call(e,t,o,a))return s++,n(t,i?o:s-1,u)}),o),s},o.__iteratorUncached=function(n,o){var u=t.__iterator(2,o),s=0;return new U((function(){for(;;){var o=u.next();if(o.done)return o;var a=o.value,c=a[0],f=a[1];if(r.call(e,f,c,t))return T(n,i?c:s++,f,o)}}))},o}function kt(t,r,e,n){var i=t.size;if(c(r,e,i))return t;if(void 0===i&&(r<0||e<0))return kt(t.toSeq().cacheResult(),r,e,n);var o,u=f(r,i),a=h(e,i)-u;a==a&&(o=a<0?0:a);var p=Nt(t);return p.size=0===o?o:t.size&&o||void 0,!n&&E(t)&&o>=0&&(p.get=function(r,e){return(r=s(this,r))>=0&&r<o?t.get(r+u,e):e}),p.__iterateUncached=function(r,e){var i=this;if(0===o)return 0;if(e)return this.cacheResult().__iterate(r,e);var s=0,a=!0,c=0;return t.__iterate((function(t,e){if(!a||!(a=s++<u))return c++,!1!==r(t,n?e:c-1,i)&&c!==o})),c},p.__iteratorUncached=function(r,e){if(0!==o&&e)return this.cacheResult().__iterator(r,e);if(0===o)return new U(B);var i=t.__iterator(r,e),s=0,a=0;return new U((function(){for(;s++<u;)i.next();if(++a>o)return{value:void 0,done:!0};var t=i.next();return n||1===r||t.done?t:T(r,a-1,0===r?void 0:t.value[1],t)}))},p}function Rt(t,r,e,n){var i=Nt(t);return i.__iterateUncached=function(i,o){var u=this;if(o)return this.cacheResult().__iterate(i,o);var s=!0,a=0;return t.__iterate((function(t,o,c){if(!s||!(s=r.call(e,t,o,c)))return a++,i(t,n?o:a-1,u)})),a},i.__iteratorUncached=function(i,o){var u=this;if(o)return this.cacheResult().__iterator(i,o);var s=t.__iterator(2,o),a=!0,c=0;return new U((function(){var t,o,f;do{if((t=s.next()).done)return n||1===i?t:T(i,c++,0===i?void 0:t.value[1],t);var h=t.value;o=h[0],f=h[1],a&&(a=r.call(e,f,o,u))}while(a);return 2===i?t:T(i,o,f,t)}))},i}function Ut(t,r,e){var n=Nt(t);return n.__iterateUncached=function(i,o){if(o)return this.cacheResult().__iterate(i,o);var u=0,s=!1;return function t(a,c){a.__iterate((function(o,a){return(!r||c<r)&&v(o)?t(o,c+1):(u++,!1===i(o,e?a:u-1,n)&&(s=!0)),!s}),o)}(t,0),u},n.__iteratorUncached=function(n,i){if(i)return this.cacheResult().__iterator(n,i);var o=t.__iterator(n,i),u=[],s=0;return new U((function(){for(;o;){var t=o.next();if(!1===t.done){var a=t.value;if(2===n&&(a=a[1]),r&&!(u.length<r)||!v(a))return e?t:T(n,s++,a,t);u.push(o),o=a.__iterator(n,i)}else o=u.pop()}return{value:void 0,done:!0}}))},n}function Tt(t,r,e){r||(r=Jt);var n=d(t),i=0,o=t.toSeq().map((function(r,n){return[n,r,i++,e?e(r,n,t):r]})).valueSeq().toArray();return o.sort((function(t,e){return r(t[3],e[3])||t[2]-e[2]})).forEach(n?function(t,r){o[r].length=2}:function(t,r){o[r]=t[1]}),n?J(o):w(t)?V(o):Y(o)}function Bt(t,r,e){if(r||(r=Jt),e){var n=t.toSeq().map((function(r,n){return[r,e(r,n,t)]})).reduce((function(t,e){return Kt(r,t[1],e[1])?e:t}));return n&&n[0]}return t.reduce((function(t,e){return Kt(r,t,e)?e:t}))}function Kt(t,r,e){var n=t(e,r);return 0===n&&e!==r&&(null==e||e!=e)||n>0}function Lt(t,r,e,n){var i=Nt(t),o=new Q(e).map((function(t){return t.size}));return i.size=n?o.max():o.min(),i.__iterate=function(t,r){for(var e,n=this.__iterator(1,r),i=0;!(e=n.next()).done&&!1!==t(e.value,i++,this););return i},i.__iteratorUncached=function(t,i){var o=e.map((function(t){return t=S(t),C(i?t.reverse():t)})),u=0,s=!1;return new U((function(){var e;return s||(e=o.map((function(t){return t.next()})),s=n?e.every((function(t){return t.done})):e.some((function(t){return t.done}))),s?{value:void 0,done:!0}:T(t,u++,r.apply(null,e.map((function(t){return t.value}))))}))},i}function Ct(t,r){return t===r?t:E(t)?r:t.constructor(r)}function Pt(t){if(t!==Object(t))throw new TypeError("Expected [K, V] tuple: "+t)}function Wt(t){return d(t)?z:w(t)?b:I}function Nt(t){return Object.create((d(t)?J:w(t)?V:Y).prototype)}function Ht(){return this._iter.cacheResult?(this._iter.cacheResult(),this.size=this._iter.size,this):H.prototype.cacheResult.call(this)}function Jt(t,r){return void 0===t&&void 0===r?0:void 0===t?1:void 0===r?-1:t>r?1:t<r?-1:0}function Vt(t,r){r=r||0;for(var e=Math.max(0,t.length-r),n=new Array(e),i=0;i<e;i++)n[i]=t[i+r];return n}function Yt(t,r){if(!t)throw new Error(r)}function Qt(t){Yt(Infinity!==t,"Cannot perform this action with an infinite size.")}function Xt(t){if(N(t)&&"string"!=typeof t)return t;if(A(t))return t.toArray();throw new TypeError("Invalid keyPath: expected Ordered Collection or Array: "+t)}Et.prototype.cacheResult=Ot.prototype.cacheResult=jt.prototype.cacheResult=qt.prototype.cacheResult=Ht;var Ft=Object.prototype.toString;function Gt(t){if(!t||"object"!=typeof t||"[object Object]"!==Ft.call(t))return!1;var r=Object.getPrototypeOf(t);if(null===r)return!0;for(var e=r,n=Object.getPrototypeOf(r);null!==n;)e=n,n=Object.getPrototypeOf(e);return e===r}function Zt(t){return"object"==typeof t&&(M(t)||Array.isArray(t)||Gt(t))}function $t(t){try{return"string"==typeof t?JSON.stringify(t):String(t)}catch(r){return JSON.stringify(t)}}function tr(t,r){return M(t)?t.has(r):Zt(t)&&W.call(t,r)}function rr(t,r,e){return M(t)?t.get(r,e):tr(t,r)?"function"==typeof t.get?t.get(r):t[r]:e}function er(t){if(Array.isArray(t))return Vt(t);var r={};for(var e in t)W.call(t,e)&&(r[e]=t[e]);return r}function nr(t,r){if(!Zt(t))throw new TypeError("Cannot update non-data-structure value: "+t);if(M(t)){if(!t.remove)throw new TypeError("Cannot update immutable value without .remove() method: "+t);return t.remove(r)}if(!W.call(t,r))return t;var e=er(t);return Array.isArray(e)?e.splice(r,1):delete e[r],e}function ir(t,r,e){if(!Zt(t))throw new TypeError("Cannot update non-data-structure value: "+t);if(M(t)){if(!t.set)throw new TypeError("Cannot update immutable value without .set() method: "+t);return t.set(r,e)}if(W.call(t,r)&&e===t[r])return t;var n=er(t);return n[r]=e,n}function or(t,r,e,i){i||(i=e,e=void 0);var o=ur(M(t),t,Xt(r),0,e,i);return o===n?e:o}function ur(t,r,e,i,o,u){var s=r===n;if(i===e.length){var a=s?o:r,c=u(a);return c===a?r:c}if(!s&&!Zt(r))throw new TypeError("Cannot update within non-data-structure value in path ["+e.slice(0,i).map($t)+"]: "+r);var f=e[i],h=s?n:rr(r,f,n),p=ur(h===n?t:M(h),h,e,i+1,o,u);return p===h?r:p===n?nr(r,f):ir(s?t?Lr():{}:r,f,p)}function sr(t,r,e){return or(t,r,n,(function(){return e}))}function ar(t,r){return sr(this,t,r)}function cr(t,r){return or(t,r,(function(){return n}))}function fr(t){return cr(this,t)}function hr(t,r,e,n){return or(t,[r],e,n)}function pr(t,r,e){return 1===arguments.length?t(this):hr(this,t,r,e)}function _r(t,r,e){return or(this,t,r,e)}function lr(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return yr(this,t)}function vr(t){for(var r=[],e=arguments.length-1;e-- >0;)r[e]=arguments[e+1];if("function"!=typeof t)throw new TypeError("Invalid merger function: "+t);return yr(this,r,t)}function yr(t,r,e){for(var i=[],o=0;o<r.length;o++){var u=z(r[o]);0!==u.size&&i.push(u)}return 0===i.length?t:0!==t.toSeq().size||t.__ownerID||1!==i.length?t.withMutations((function(t){for(var r=e?function(r,i){hr(t,i,n,(function(t){return t===n?r:e(t,r,i)}))}:function(r,e){t.set(e,r)},o=0;o<i.length;o++)i[o].forEach(r)})):t.constructor(i[0])}function dr(t,r,e){return gr(t,r,function(t){function r(e,n,i){return Zt(e)&&Zt(n)&&(o=n,u=H(e),s=H(o),w(u)===w(s)&&d(u)===d(s))?gr(e,[n],r):t?t(e,n,i):n;var o,u,s}return r}(e))}function gr(t,r,e){if(!Zt(t))throw new TypeError("Cannot merge into non-data-structure value: "+t);if(M(t))return"function"==typeof e&&t.mergeWith?t.mergeWith.apply(t,[e].concat(r)):t.merge?t.merge.apply(t,r):t.concat.apply(t,r);for(var n=Array.isArray(t),i=t,o=n?b:z,u=n?function(r){i===t&&(i=er(i)),i.push(r)}:function(r,n){var o=W.call(i,n),u=o&&e?e(i[n],r,n):r;o&&u===i[n]||(i===t&&(i=er(i)),i[n]=u)},s=0;s<r.length;s++)o(r[s]).forEach(u);return i}function wr(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return dr(this,t)}function mr(t){for(var r=[],e=arguments.length-1;e-- >0;)r[e]=arguments[e+1];return dr(this,r,t)}function Sr(t){for(var r=[],e=arguments.length-1;e-- >0;)r[e]=arguments[e+1];return or(this,t,Lr(),(function(t){return gr(t,r)}))}function zr(t){for(var r=[],e=arguments.length-1;e-- >0;)r[e]=arguments[e+1];return or(this,t,Lr(),(function(t){return dr(t,r)}))}function br(t){var r=this.asMutable();return t(r),r.wasAltered()?r.__ensureOwner(this.__ownerID):this}function Ir(){return this.__ownerID?this:this.__ensureOwner(new o)}function Or(){return this.__ensureOwner()}function Er(){return this.__altered}var jr=function(t){function r(r){return null==r?Lr():nt(r)&&!A(r)?r:Lr().withMutations((function(e){var n=t(r);Qt(n.size),n.forEach((function(t,r){return e.set(r,t)}))}))}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return this.__toString("Map {","}")},r.prototype.get=function(t,r){return this._root?this._root.get(0,void 0,t,r):r},r.prototype.set=function(t,r){return Cr(this,t,r)},r.prototype.remove=function(t){return Cr(this,t,n)},r.prototype.deleteAll=function(t){var r=S(t);return 0===r.size?this:this.withMutations((function(t){r.forEach((function(r){return t.remove(r)}))}))},r.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._root=null,this.__hash=void 0,this.__altered=!0,this):Lr()},r.prototype.sort=function(t){return fe(Tt(this,t))},r.prototype.sortBy=function(t,r){return fe(Tt(this,r,t))},r.prototype.map=function(t,r){var e=this;return this.withMutations((function(n){n.forEach((function(i,o){n.set(o,t.call(r,i,o,e))}))}))},r.prototype.__iterator=function(t,r){return new Ur(this,t,r)},r.prototype.__iterate=function(t,r){var e=this,n=0;return this._root&&this._root.iterate((function(r){return n++,t(r[1],r[0],e)}),r),n},r.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Kr(this.size,this._root,t,this.__hash):0===this.size?Lr():(this.__ownerID=t,this.__altered=!1,this)},r}(z);jr.isMap=nt;var qr=jr.prototype;qr[et]=!0,qr[t]=qr.remove,qr.removeAll=qr.deleteAll,qr.setIn=ar,qr.removeIn=qr.deleteIn=fr,qr.update=pr,qr.updateIn=_r,qr.merge=qr.concat=lr,qr.mergeWith=vr,qr.mergeDeep=wr,qr.mergeDeepWith=mr,qr.mergeIn=Sr,qr.mergeDeepIn=zr,qr.withMutations=br,qr.wasAltered=Er,qr.asImmutable=Or,qr["@@transducer/init"]=qr.asMutable=Ir,qr["@@transducer/step"]=function(t,r){return t.set(r[0],r[1])},qr["@@transducer/result"]=function(t){return t.asImmutable()};var Mr=function(t,r){this.ownerID=t,this.entries=r};Mr.prototype.get=function(t,r,e,n){for(var i=this.entries,o=0,u=i.length;o<u;o++)if(ut(e,i[o][0]))return i[o][1];return n},Mr.prototype.update=function(t,r,e,u,s,a,c){for(var f=s===n,h=this.entries,p=0,_=h.length;p<_&&!ut(u,h[p][0]);p++);var l=p<_;if(l?h[p][1]===s:f)return this;if(i(c),(f||!l)&&i(a),!f||1!==h.length){if(!l&&!f&&h.length>=Vr)return function(t,r,e,n){t||(t=new o);for(var i=new kr(t,ft(e),[e,n]),u=0;u<r.length;u++){var s=r[u];i=i.update(t,0,void 0,s[0],s[1])}return i}(t,h,u,s);var v=t&&t===this.ownerID,y=v?h:Vt(h);return l?f?p===_-1?y.pop():y[p]=y.pop():y[p]=[u,s]:y.push([u,s]),v?(this.entries=y,this):new Mr(t,y)}};var Dr=function(t,r,e){this.ownerID=t,this.bitmap=r,this.nodes=e};Dr.prototype.get=function(t,r,n,i){void 0===r&&(r=ft(n));var o=1<<((0===t?r:r>>>t)&e),u=this.bitmap;return u&o?this.nodes[Hr(u&o-1)].get(t+5,r,n,i):i},Dr.prototype.update=function(t,i,o,u,s,a,c){void 0===o&&(o=ft(u));var f=(0===i?o:o>>>i)&e,h=1<<f,p=this.bitmap,_=!!(p&h);if(!_&&s===n)return this;var l=Hr(p&h-1),v=this.nodes,y=_?v[l]:void 0,d=Pr(y,t,i+5,o,u,s,a,c);if(d===y)return this;if(!_&&d&&v.length>=Yr)return function(t,e,n,i,o){for(var u=0,s=new Array(r),a=0;0!==n;a++,n>>>=1)s[a]=1&n?e[u++]:void 0;return s[i]=o,new Ar(t,u+1,s)}(t,v,p,f,d);if(_&&!d&&2===v.length&&Wr(v[1^l]))return v[1^l];if(_&&d&&1===v.length&&Wr(d))return d;var g=t&&t===this.ownerID,w=_?d?p:p^h:p|h,m=_?d?Jr(v,l,d,g):function(t,r,e){var n=t.length-1;if(e&&r===n)return t.pop(),t;for(var i=new Array(n),o=0,u=0;u<n;u++)u===r&&(o=1),i[u]=t[u+o];return i}(v,l,g):function(t,r,e,n){var i=t.length+1;if(n&&r+1===i)return t[r]=e,t;for(var o=new Array(i),u=0,s=0;s<i;s++)s===r?(o[s]=e,u=-1):o[s]=t[s+u];return o}(v,l,d,g);return g?(this.bitmap=w,this.nodes=m,this):new Dr(t,w,m)};var Ar=function(t,r,e){this.ownerID=t,this.count=r,this.nodes=e};Ar.prototype.get=function(t,r,n,i){void 0===r&&(r=ft(n));var o=(0===t?r:r>>>t)&e,u=this.nodes[o];return u?u.get(t+5,r,n,i):i},Ar.prototype.update=function(t,r,i,o,u,s,a){void 0===i&&(i=ft(o));var c=(0===r?i:i>>>r)&e,f=u===n,h=this.nodes,p=h[c];if(f&&!p)return this;var _=Pr(p,t,r+5,i,o,u,s,a);if(_===p)return this;var l=this.count;if(p){if(!_&&--l<Qr)return function(t,r,e,n){for(var i=0,o=0,u=new Array(e),s=0,a=1,c=r.length;s<c;s++,a<<=1){var f=r[s];void 0!==f&&s!==n&&(i|=a,u[o++]=f)}return new Dr(t,i,u)}(t,h,l,c)}else l++;var v=t&&t===this.ownerID,y=Jr(h,c,_,v);return v?(this.count=l,this.nodes=y,this):new Ar(t,l,y)};var xr=function(t,r,e){this.ownerID=t,this.keyHash=r,this.entries=e};xr.prototype.get=function(t,r,e,n){for(var i=this.entries,o=0,u=i.length;o<u;o++)if(ut(e,i[o][0]))return i[o][1];return n},xr.prototype.update=function(t,r,e,o,u,s,a){void 0===e&&(e=ft(o));var c=u===n;if(e!==this.keyHash)return c?this:(i(a),i(s),Nr(this,t,r,e,[o,u]));for(var f=this.entries,h=0,p=f.length;h<p&&!ut(o,f[h][0]);h++);var _=h<p;if(_?f[h][1]===u:c)return this;if(i(a),(c||!_)&&i(s),c&&2===p)return new kr(t,this.keyHash,f[1^h]);var l=t&&t===this.ownerID,v=l?f:Vt(f);return _?c?h===p-1?v.pop():v[h]=v.pop():v[h]=[o,u]:v.push([o,u]),l?(this.entries=v,this):new xr(t,this.keyHash,v)};var kr=function(t,r,e){this.ownerID=t,this.keyHash=r,this.entry=e};kr.prototype.get=function(t,r,e,n){return ut(e,this.entry[0])?this.entry[1]:n},kr.prototype.update=function(t,r,e,o,u,s,a){var c=u===n,f=ut(o,this.entry[0]);return(f?u===this.entry[1]:c)?this:(i(a),c?void i(s):f?t&&t===this.ownerID?(this.entry[1]=u,this):new kr(t,this.keyHash,[o,u]):(i(s),Nr(this,t,r,ft(o),[o,u])))},Mr.prototype.iterate=xr.prototype.iterate=function(t,r){for(var e=this.entries,n=0,i=e.length-1;n<=i;n++)if(!1===t(e[r?i-n:n]))return!1},Dr.prototype.iterate=Ar.prototype.iterate=function(t,r){for(var e=this.nodes,n=0,i=e.length-1;n<=i;n++){var o=e[r?i-n:n];if(o&&!1===o.iterate(t,r))return!1}},kr.prototype.iterate=function(t,r){return t(this.entry)};var Rr,Ur=function(t){function r(t,r,e){this._type=r,this._reverse=e,this._stack=t._root&&Br(t._root)}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.next=function(){for(var t=this._type,r=this._stack;r;){var e=r.node,n=r.index++,i=void 0;if(e.entry){if(0===n)return Tr(t,e.entry)}else if(e.entries){if(n<=(i=e.entries.length-1))return Tr(t,e.entries[this._reverse?i-n:n])}else if(n<=(i=e.nodes.length-1)){var o=e.nodes[this._reverse?i-n:n];if(o){if(o.entry)return Tr(t,o.entry);r=this._stack=Br(o,r)}continue}r=this._stack=this._stack.__prev}return{value:void 0,done:!0}},r}(U);function Tr(t,r){return T(t,r[0],r[1])}function Br(t,r){return{node:t,index:0,__prev:r}}function Kr(t,r,e,n){var i=Object.create(qr);return i.size=t,i._root=r,i.__ownerID=e,i.__hash=n,i.__altered=!1,i}function Lr(){return Rr||(Rr=Kr(0))}function Cr(t,r,e){var i,o;if(t._root){var u={value:!1},s={value:!1};if(i=Pr(t._root,t.__ownerID,0,void 0,r,e,u,s),!s.value)return t;o=t.size+(u.value?e===n?-1:1:0)}else{if(e===n)return t;o=1,i=new Mr(t.__ownerID,[[r,e]])}return t.__ownerID?(t.size=o,t._root=i,t.__hash=void 0,t.__altered=!0,t):i?Kr(o,i):Lr()}function Pr(t,r,e,o,u,s,a,c){return t?t.update(r,e,o,u,s,a,c):s===n?t:(i(c),i(a),new kr(r,o,[u,s]))}function Wr(t){return t.constructor===kr||t.constructor===xr}function Nr(t,r,n,i,o){if(t.keyHash===i)return new xr(r,i,[t.entry,o]);var u,s=(0===n?t.keyHash:t.keyHash>>>n)&e,a=(0===n?i:i>>>n)&e,c=s===a?[Nr(t,r,n+5,i,o)]:(u=new kr(r,i,o),s<a?[t,u]:[u,t]);return new Dr(r,1<<s|1<<a,c)}function Hr(t){return t=(t=(858993459&(t-=t>>1&1431655765))+(t>>2&858993459))+(t>>4)&252645135,t+=t>>8,127&(t+=t>>16)}function Jr(t,r,e,n){var i=n?t:Vt(t);return i[r]=e,i}var Vr=8,Yr=16,Qr=8,Xr="@@__IMMUTABLE_LIST__@@";function Fr(t){return Boolean(t&&t[Xr])}var Gr=function(t){function n(e){var n=ne();if(null==e)return n;if(Fr(e))return e;var i=t(e),o=i.size;return 0===o?n:(Qt(o),o>0&&o<r?ee(0,o,5,null,new $r(i.toArray())):n.withMutations((function(t){t.setSize(o),i.forEach((function(r,e){return t.set(e,r)}))})))}return t&&(n.__proto__=t),n.prototype=Object.create(t&&t.prototype),n.prototype.constructor=n,n.of=function(){return this(arguments)},n.prototype.toString=function(){return this.__toString("List [","]")},n.prototype.get=function(t,r){if((t=s(this,t))>=0&&t<this.size){var n=ue(this,t+=this._origin);return n&&n.array[t&e]}return r},n.prototype.set=function(t,r){return function(t,r,e){if(r=s(t,r),r!=r)return t;if(r>=t.size||r<0)return t.withMutations((function(t){r<0?se(t,r).set(0,e):se(t,0,r+1).set(r,e)}));r+=t._origin;var n=t._tail,i=t._root,o={value:!1};r>=ae(t._capacity)?n=ie(n,t.__ownerID,0,r,e,o):i=ie(i,t.__ownerID,t._level,r,e,o);if(!o.value)return t;if(t.__ownerID)return t._root=i,t._tail=n,t.__hash=void 0,t.__altered=!0,t;return ee(t._origin,t._capacity,t._level,i,n)}(this,t,r)},n.prototype.remove=function(t){return this.has(t)?0===t?this.shift():t===this.size-1?this.pop():this.splice(t,1):this},n.prototype.insert=function(t,r){return this.splice(t,0,r)},n.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=this._origin=this._capacity=0,this._level=5,this._root=this._tail=this.__hash=void 0,this.__altered=!0,this):ne()},n.prototype.push=function(){var t=arguments,r=this.size;return this.withMutations((function(e){se(e,0,r+t.length);for(var n=0;n<t.length;n++)e.set(r+n,t[n])}))},n.prototype.pop=function(){return se(this,0,-1)},n.prototype.unshift=function(){var t=arguments;return this.withMutations((function(r){se(r,-t.length);for(var e=0;e<t.length;e++)r.set(e,t[e])}))},n.prototype.shift=function(){return se(this,1)},n.prototype.concat=function(){for(var r=arguments,e=[],n=0;n<arguments.length;n++){var i=r[n],o=t("string"!=typeof i&&K(i)?i:[i]);0!==o.size&&e.push(o)}return 0===e.length?this:0!==this.size||this.__ownerID||1!==e.length?this.withMutations((function(t){e.forEach((function(r){return r.forEach((function(r){return t.push(r)}))}))})):this.constructor(e[0])},n.prototype.setSize=function(t){return se(this,0,t)},n.prototype.map=function(t,r){var e=this;return this.withMutations((function(n){for(var i=0;i<e.size;i++)n.set(i,t.call(r,n.get(i),i,e))}))},n.prototype.slice=function(t,r){var e=this.size;return c(t,r,e)?this:se(this,f(t,e),h(r,e))},n.prototype.__iterator=function(t,r){var e=r?this.size:0,n=re(this,r);return new U((function(){var i=n();return i===te?{value:void 0,done:!0}:T(t,r?--e:e++,i)}))},n.prototype.__iterate=function(t,r){for(var e,n=r?this.size:0,i=re(this,r);(e=i())!==te&&!1!==t(e,r?--n:n++,this););return n},n.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?ee(this._origin,this._capacity,this._level,this._root,this._tail,t,this.__hash):0===this.size?ne():(this.__ownerID=t,this.__altered=!1,this)},n}(b);Gr.isList=Fr;var Zr=Gr.prototype;Zr[Xr]=!0,Zr[t]=Zr.remove,Zr.merge=Zr.concat,Zr.setIn=ar,Zr.deleteIn=Zr.removeIn=fr,Zr.update=pr,Zr.updateIn=_r,Zr.mergeIn=Sr,Zr.mergeDeepIn=zr,Zr.withMutations=br,Zr.wasAltered=Er,Zr.asImmutable=Or,Zr["@@transducer/init"]=Zr.asMutable=Ir,Zr["@@transducer/step"]=function(t,r){return t.push(r)},Zr["@@transducer/result"]=function(t){return t.asImmutable()};var $r=function(t,r){this.array=t,this.ownerID=r};$r.prototype.removeBefore=function(t,r,n){if(!(n&(1<<r+5)-1)||0===this.array.length)return this;var i=n>>>r&e;if(i>=this.array.length)return new $r([],t);var o,u=0===i;if(r>0){var s=this.array[i];if((o=s&&s.removeBefore(t,r-5,n))===s&&u)return this}if(u&&!o)return this;var a=oe(this,t);if(!u)for(var c=0;c<i;c++)a.array[c]=void 0;return o&&(a.array[i]=o),a},$r.prototype.removeAfter=function(t,n,i){if(i===(n?1<<n+5:r)||0===this.array.length)return this;var o,u=i-1>>>n&e;if(u>=this.array.length)return this;if(n>0){var s=this.array[u];if((o=s&&s.removeAfter(t,n-5,i))===s&&u===this.array.length-1)return this}var a=oe(this,t);return a.array.splice(u+1),o&&(a.array[u]=o),a};var te={};function re(t,e){var n=t._origin,i=t._capacity,o=ae(i),u=t._tail;return s(t._root,t._level,0);function s(t,a,c){return 0===a?function(t,s){var a=s===o?u&&u.array:t&&t.array,c=s>n?0:n-s,f=i-s;f>r&&(f=r);return function(){if(c===f)return te;var t=e?--f:c++;return a&&a[t]}}(t,c):function(t,o,u){var a,c=t&&t.array,f=u>n?0:n-u>>o,h=1+(i-u>>o);h>r&&(h=r);return function(){for(;;){if(a){var t=a();if(t!==te)return t;a=null}if(f===h)return te;var r=e?--h:f++;a=s(c&&c[r],o-5,u+(r<<o))}}}(t,a,c)}}function ee(t,r,e,n,i,o,u){var s=Object.create(Zr);return s.size=r-t,s._origin=t,s._capacity=r,s._level=e,s._root=n,s._tail=i,s.__ownerID=o,s.__hash=u,s.__altered=!1,s}function ne(){return ee(0,0,5)}function ie(t,r,n,o,u,s){var a,c=o>>>n&e,f=t&&c<t.array.length;if(!f&&void 0===u)return t;if(n>0){var h=t&&t.array[c],p=ie(h,r,n-5,o,u,s);return p===h?t:((a=oe(t,r)).array[c]=p,a)}return f&&t.array[c]===u?t:(s&&i(s),a=oe(t,r),void 0===u&&c===a.array.length-1?a.array.pop():a.array[c]=u,a)}function oe(t,r){return r&&t&&r===t.ownerID?t:new $r(t?t.array.slice():[],r)}function ue(t,r){if(r>=ae(t._capacity))return t._tail;if(r<1<<t._level+5){for(var n=t._root,i=t._level;n&&i>0;)n=n.array[r>>>i&e],i-=5;return n}}function se(t,r,n){void 0!==r&&(r|=0),void 0!==n&&(n|=0);var i=t.__ownerID||new o,u=t._origin,s=t._capacity,a=u+r,c=void 0===n?s:n<0?s+n:u+n;if(a===u&&c===s)return t;if(a>=c)return t.clear();for(var f=t._level,h=t._root,p=0;a+p<0;)h=new $r(h&&h.array.length?[void 0,h]:[],i),p+=1<<(f+=5);p&&(a+=p,u+=p,c+=p,s+=p);for(var _=ae(s),l=ae(c);l>=1<<f+5;)h=new $r(h&&h.array.length?[h]:[],i),f+=5;var v=t._tail,y=l<_?ue(t,c-1):l>_?new $r([],i):v;if(v&&l>_&&a<s&&v.array.length){for(var d=h=oe(h,i),g=f;g>5;g-=5){var w=_>>>g&e;d=d.array[w]=oe(d.array[w],i)}d.array[_>>>5&e]=v}if(c<s&&(y=y&&y.removeAfter(i,0,c)),a>=l)a-=l,c-=l,f=5,h=null,y=y&&y.removeBefore(i,0,a);else if(a>u||l<_){for(p=0;h;){var m=a>>>f&e;if(m!==l>>>f&e)break;m&&(p+=(1<<f)*m),f-=5,h=h.array[m]}h&&a>u&&(h=h.removeBefore(i,f,a-p)),h&&l<_&&(h=h.removeAfter(i,f,l-p)),p&&(a-=p,c-=p)}return t.__ownerID?(t.size=c-a,t._origin=a,t._capacity=c,t._level=f,t._root=h,t._tail=y,t.__hash=void 0,t.__altered=!0,t):ee(a,c,f,h,y)}function ae(t){return t<r?0:t-1>>>5<<5}var ce,fe=function(t){function r(t){return null==t?pe():it(t)?t:pe().withMutations((function(r){var e=z(t);Qt(e.size),e.forEach((function(t,e){return r.set(e,t)}))}))}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.of=function(){return this(arguments)},r.prototype.toString=function(){return this.__toString("OrderedMap {","}")},r.prototype.get=function(t,r){var e=this._map.get(t);return void 0!==e?this._list.get(e)[1]:r},r.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._map.clear(),this._list.clear(),this.__altered=!0,this):pe()},r.prototype.set=function(t,r){return _e(this,t,r)},r.prototype.remove=function(t){return _e(this,t,n)},r.prototype.__iterate=function(t,r){var e=this;return this._list.__iterate((function(r){return r&&t(r[1],r[0],e)}),r)},r.prototype.__iterator=function(t,r){return this._list.fromEntrySeq().__iterator(t,r)},r.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var r=this._map.__ensureOwner(t),e=this._list.__ensureOwner(t);return t?he(r,e,t,this.__hash):0===this.size?pe():(this.__ownerID=t,this.__altered=!1,this._map=r,this._list=e,this)},r}(jr);function he(t,r,e,n){var i=Object.create(fe.prototype);return i.size=t?t.size:0,i._map=t,i._list=r,i.__ownerID=e,i.__hash=n,i.__altered=!1,i}function pe(){return ce||(ce=he(Lr(),ne()))}function _e(t,e,i){var o,u,s=t._map,a=t._list,c=s.get(e),f=void 0!==c;if(i===n){if(!f)return t;a.size>=r&&a.size>=2*s.size?(o=(u=a.filter((function(t,r){return void 0!==t&&c!==r}))).toKeyedSeq().map((function(t){return t[0]})).flip().toMap(),t.__ownerID&&(o.__ownerID=u.__ownerID=t.__ownerID)):(o=s.remove(e),u=c===a.size-1?a.pop():a.set(c,void 0))}else if(f){if(i===a.get(c)[1])return t;o=s,u=a.set(c,[e,i])}else o=s.set(e,a.size),u=a.set(a.size,[e,i]);return t.__ownerID?(t.size=o.size,t._map=o,t._list=u,t.__hash=void 0,t.__altered=!0,t):he(o,u)}fe.isOrderedMap=it,fe.prototype[D]=!0,fe.prototype[t]=fe.prototype.remove;var le="@@__IMMUTABLE_STACK__@@";function ve(t){return Boolean(t&&t[le])}var ye=function(t){function r(t){return null==t?me():ve(t)?t:me().pushAll(t)}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.of=function(){return this(arguments)},r.prototype.toString=function(){return this.__toString("Stack [","]")},r.prototype.get=function(t,r){var e=this._head;for(t=s(this,t);e&&t--;)e=e.next;return e?e.value:r},r.prototype.peek=function(){return this._head&&this._head.value},r.prototype.push=function(){var t=arguments;if(0===arguments.length)return this;for(var r=this.size+arguments.length,e=this._head,n=arguments.length-1;n>=0;n--)e={value:t[n],next:e};return this.__ownerID?(this.size=r,this._head=e,this.__hash=void 0,this.__altered=!0,this):we(r,e)},r.prototype.pushAll=function(r){if(0===(r=t(r)).size)return this;if(0===this.size&&ve(r))return r;Qt(r.size);var e=this.size,n=this._head;return r.__iterate((function(t){e++,n={value:t,next:n}}),!0),this.__ownerID?(this.size=e,this._head=n,this.__hash=void 0,this.__altered=!0,this):we(e,n)},r.prototype.pop=function(){return this.slice(1)},r.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._head=void 0,this.__hash=void 0,this.__altered=!0,this):me()},r.prototype.slice=function(r,e){if(c(r,e,this.size))return this;var n=f(r,this.size);if(h(e,this.size)!==this.size)return t.prototype.slice.call(this,r,e);for(var i=this.size-n,o=this._head;n--;)o=o.next;return this.__ownerID?(this.size=i,this._head=o,this.__hash=void 0,this.__altered=!0,this):we(i,o)},r.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?we(this.size,this._head,t,this.__hash):0===this.size?me():(this.__ownerID=t,this.__altered=!1,this)},r.prototype.__iterate=function(t,r){var e=this;if(r)return new Q(this.toArray()).__iterate((function(r,n){return t(r,n,e)}),r);for(var n=0,i=this._head;i&&!1!==t(i.value,n++,this);)i=i.next;return n},r.prototype.__iterator=function(t,r){if(r)return new Q(this.toArray()).__iterator(t,r);var e=0,n=this._head;return new U((function(){if(n){var r=n.value;return n=n.next,T(t,e++,r)}return{value:void 0,done:!0}}))},r}(b);ye.isStack=ve;var de,ge=ye.prototype;function we(t,r,e,n){var i=Object.create(ge);return i.size=t,i._head=r,i.__ownerID=e,i.__hash=n,i.__altered=!1,i}function me(){return de||(de=we(0))}ge[le]=!0,ge.shift=ge.pop,ge.unshift=ge.push,ge.unshiftAll=ge.pushAll,ge.withMutations=br,ge.wasAltered=Er,ge.asImmutable=Or,ge["@@transducer/init"]=ge.asMutable=Ir,ge["@@transducer/step"]=function(t,r){return t.unshift(r)},ge["@@transducer/result"]=function(t){return t.asImmutable()};var Se="@@__IMMUTABLE_SET__@@";function ze(t){return Boolean(t&&t[Se])}function be(t){return ze(t)&&A(t)}function Ie(t,r){if(t===r)return!0;if(!v(r)||void 0!==t.size&&void 0!==r.size&&t.size!==r.size||void 0!==t.__hash&&void 0!==r.__hash&&t.__hash!==r.__hash||d(t)!==d(r)||w(t)!==w(r)||A(t)!==A(r))return!1;if(0===t.size&&0===r.size)return!0;var e=!m(t);if(A(t)){var i=t.entries();return r.every((function(t,r){var n=i.next().value;return n&&ut(n[1],t)&&(e||ut(n[0],r))}))&&i.next().done}var o=!1;if(void 0===t.size)if(void 0===r.size)"function"==typeof t.cacheResult&&t.cacheResult();else{o=!0;var u=t;t=r,r=u}var s=!0,a=r.__iterate((function(r,i){if(e?!t.has(r):o?!ut(r,t.get(i,n)):!ut(t.get(i,n),r))return s=!1,!1}));return s&&t.size===a}function Oe(t,r){var e=function(e){t.prototype[e]=r[e]};return Object.keys(r).forEach(e),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(r).forEach(e),t}function Ee(t){if(!t||"object"!=typeof t)return t;if(!v(t)){if(!Zt(t))return t;t=H(t)}if(d(t)){var r={};return t.__iterate((function(t,e){r[e]=Ee(t)})),r}var e=[];return t.__iterate((function(t){e.push(Ee(t))})),e}var je=function(t){function r(r){return null==r?xe():ze(r)&&!A(r)?r:xe().withMutations((function(e){var n=t(r);Qt(n.size),n.forEach((function(t){return e.add(t)}))}))}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.of=function(){return this(arguments)},r.fromKeys=function(t){return this(z(t).keySeq())},r.intersect=function(t){return(t=S(t).toArray()).length?Me.intersect.apply(r(t.pop()),t):xe()},r.union=function(t){return(t=S(t).toArray()).length?Me.union.apply(r(t.pop()),t):xe()},r.prototype.toString=function(){return this.__toString("Set {","}")},r.prototype.has=function(t){return this._map.has(t)},r.prototype.add=function(t){return De(this,this._map.set(t,t))},r.prototype.remove=function(t){return De(this,this._map.remove(t))},r.prototype.clear=function(){return De(this,this._map.clear())},r.prototype.map=function(t,r){var e=this,n=!1,i=De(this,this._map.mapEntries((function(i){var o=i[1],u=t.call(r,o,o,e);return u!==o&&(n=!0),[u,u]}),r));return n?i:this},r.prototype.union=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return 0===(r=r.filter((function(t){return 0!==t.size}))).length?this:0!==this.size||this.__ownerID||1!==r.length?this.withMutations((function(e){for(var n=0;n<r.length;n++)"string"==typeof r[n]?e.add(r[n]):t(r[n]).forEach((function(t){return e.add(t)}))})):this.constructor(r[0])},r.prototype.intersect=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if(0===r.length)return this;r=r.map((function(r){return t(r)}));var n=[];return this.forEach((function(t){r.every((function(r){return r.includes(t)}))||n.push(t)})),this.withMutations((function(t){n.forEach((function(r){t.remove(r)}))}))},r.prototype.subtract=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if(0===r.length)return this;r=r.map((function(r){return t(r)}));var n=[];return this.forEach((function(t){r.some((function(r){return r.includes(t)}))&&n.push(t)})),this.withMutations((function(t){n.forEach((function(r){t.remove(r)}))}))},r.prototype.sort=function(t){return Ge(Tt(this,t))},r.prototype.sortBy=function(t,r){return Ge(Tt(this,r,t))},r.prototype.wasAltered=function(){return this._map.wasAltered()},r.prototype.__iterate=function(t,r){var e=this;return this._map.__iterate((function(r){return t(r,r,e)}),r)},r.prototype.__iterator=function(t,r){return this._map.__iterator(t,r)},r.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var r=this._map.__ensureOwner(t);return t?this.__make(r,t):0===this.size?this.__empty():(this.__ownerID=t,this._map=r,this)},r}(I);je.isSet=ze;var qe,Me=je.prototype;function De(t,r){return t.__ownerID?(t.size=r.size,t._map=r,t):r===t._map?t:0===r.size?t.__empty():t.__make(r)}function Ae(t,r){var e=Object.create(Me);return e.size=t?t.size:0,e._map=t,e.__ownerID=r,e}function xe(){return qe||(qe=Ae(Lr()))}Me[Se]=!0,Me[t]=Me.remove,Me.merge=Me.concat=Me.union,Me.withMutations=br,Me.asImmutable=Or,Me["@@transducer/init"]=Me.asMutable=Ir,Me["@@transducer/step"]=function(t,r){return t.add(r)},Me["@@transducer/result"]=function(t){return t.asImmutable()},Me.__empty=xe,Me.__make=Ae;var ke,Re=function(t){function r(t,e,n){if(void 0===n&&(n=1),!(this instanceof r))return new r(t,e,n);if(Yt(0!==n,"Cannot step a Range by 0"),Yt(void 0!==t,"You must define a start value when using Range"),Yt(void 0!==e,"You must define an end value when using Range"),n=Math.abs(n),e<t&&(n=-n),this._start=t,this._end=e,this._step=n,this.size=Math.max(0,Math.ceil((e-t)/n-1)+1),0===this.size){if(ke)return ke;ke=this}}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return 0===this.size?"Range []":"Range [ "+this._start+"..."+this._end+(1!==this._step?" by "+this._step:"")+" ]"},r.prototype.get=function(t,r){return this.has(t)?this._start+s(this,t)*this._step:r},r.prototype.includes=function(t){var r=(t-this._start)/this._step;return r>=0&&r<this.size&&r===Math.floor(r)},r.prototype.slice=function(t,e){return c(t,e,this.size)?this:(t=f(t,this.size),(e=h(e,this.size))<=t?new r(0,0):new r(this.get(t,this._end),this.get(e,this._end),this._step))},r.prototype.indexOf=function(t){var r=t-this._start;if(r%this._step==0){var e=r/this._step;if(e>=0&&e<this.size)return e}return-1},r.prototype.lastIndexOf=function(t){return this.indexOf(t)},r.prototype.__iterate=function(t,r){for(var e=this.size,n=this._step,i=r?this._start+(e-1)*n:this._start,o=0;o!==e&&!1!==t(i,r?e-++o:o++,this);)i+=r?-n:n;return o},r.prototype.__iterator=function(t,r){var e=this.size,n=this._step,i=r?this._start+(e-1)*n:this._start,o=0;return new U((function(){if(o===e)return{value:void 0,done:!0};var u=i;return i+=r?-n:n,T(t,r?e-++o:o++,u)}))},r.prototype.equals=function(t){return t instanceof r?this._start===t._start&&this._end===t._end&&this._step===t._step:Ie(this,t)},r}(V);function Ue(t,r,e){for(var i=Xt(r),o=0;o!==i.length;)if((t=rr(t,i[o++],n))===n)return e;return t}function Te(t,r){return Ue(this,t,r)}function Be(t,r){return Ue(t,r,n)!==n}function Ke(){Qt(this.size);var t={};return this.__iterate((function(r,e){t[e]=r})),t}S.Iterator=U,Oe(S,{toArray:function(){Qt(this.size);var t=new Array(this.size||0),r=d(this),e=0;return this.__iterate((function(n,i){t[e++]=r?[i,n]:n})),t},toIndexedSeq:function(){return new Et(this)},toJS:function(){return Ee(this)},toKeyedSeq:function(){return new Ot(this,!0)},toMap:function(){return jr(this.toKeyedSeq())},toObject:Ke,toOrderedMap:function(){return fe(this.toKeyedSeq())},toOrderedSet:function(){return Ge(d(this)?this.valueSeq():this)},toSet:function(){return je(d(this)?this.valueSeq():this)},toSetSeq:function(){return new jt(this)},toSeq:function(){return w(this)?this.toIndexedSeq():d(this)?this.toKeyedSeq():this.toSetSeq()},toStack:function(){return ye(d(this)?this.valueSeq():this)},toList:function(){return Gr(d(this)?this.valueSeq():this)},toString:function(){return"[Collection]"},__toString:function(t,r){return 0===this.size?t+r:t+" "+this.toSeq().map(this.__toStringMapper).join(", ")+" "+r},concat:function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return Ct(this,function(t,r){var e=d(t),n=[t].concat(r).map((function(t){return v(t)?e&&(t=z(t)):t=e?$(t):tt(Array.isArray(t)?t:[t]),t})).filter((function(t){return 0!==t.size}));if(0===n.length)return t;if(1===n.length){var i=n[0];if(i===t||e&&d(i)||w(t)&&w(i))return i}var o=new Q(n);return e?o=o.toKeyedSeq():w(t)||(o=o.toSetSeq()),(o=o.flatten(!0)).size=n.reduce((function(t,r){if(void 0!==t){var e=r.size;if(void 0!==e)return t+e}}),0),o}(this,t))},includes:function(t){return this.some((function(r){return ut(r,t)}))},entries:function(){return this.__iterator(2)},every:function(t,r){Qt(this.size);var e=!0;return this.__iterate((function(n,i,o){if(!t.call(r,n,i,o))return e=!1,!1})),e},filter:function(t,r){return Ct(this,xt(this,t,r,!0))},partition:function(t,r){return function(t,r,e){var n=d(t),i=[[],[]];t.__iterate((function(o,u){i[r.call(e,o,u,t)?1:0].push(n?[u,o]:o)}));var o=Wt(t);return i.map((function(r){return Ct(t,o(r))}))}(this,t,r)},find:function(t,r,e){var n=this.findEntry(t,r);return n?n[1]:e},forEach:function(t,r){return Qt(this.size),this.__iterate(r?t.bind(r):t)},join:function(t){Qt(this.size),t=void 0!==t?""+t:",";var r="",e=!0;return this.__iterate((function(n){e?e=!1:r+=t,r+=null!=n?n.toString():""})),r},keys:function(){return this.__iterator(0)},map:function(t,r){return Ct(this,Dt(this,t,r))},reduce:function(t,r,e){return Ne(this,t,r,e,arguments.length<2,!1)},reduceRight:function(t,r,e){return Ne(this,t,r,e,arguments.length<2,!0)},reverse:function(){return Ct(this,At(this,!0))},slice:function(t,r){return Ct(this,kt(this,t,r,!0))},some:function(t,r){Qt(this.size);var e=!1;return this.__iterate((function(n,i,o){if(t.call(r,n,i,o))return e=!0,!1})),e},sort:function(t){return Ct(this,Tt(this,t))},values:function(){return this.__iterator(1)},butLast:function(){return this.slice(0,-1)},isEmpty:function(){return void 0!==this.size?0===this.size:!this.some((function(){return!0}))},count:function(t,r){return u(t?this.toSeq().filter(t,r):this)},countBy:function(t,r){return function(t,r,e){var n=jr().asMutable();return t.__iterate((function(i,o){n.update(r.call(e,i,o,t),0,(function(t){return t+1}))})),n.asImmutable()}(this,t,r)},equals:function(t){return Ie(this,t)},entrySeq:function(){var t=this;if(t._cache)return new Q(t._cache);var r=t.toSeq().map(Je).toIndexedSeq();return r.fromEntrySeq=function(){return t.toSeq()},r},filterNot:function(t,r){return this.filter(Ve(t),r)},findEntry:function(t,r,e){var n=e;return this.__iterate((function(e,i,o){if(t.call(r,e,i,o))return n=[i,e],!1})),n},findKey:function(t,r){var e=this.findEntry(t,r);return e&&e[0]},findLast:function(t,r,e){return this.toKeyedSeq().reverse().find(t,r,e)},findLastEntry:function(t,r,e){return this.toKeyedSeq().reverse().findEntry(t,r,e)},findLastKey:function(t,r){return this.toKeyedSeq().reverse().findKey(t,r)},first:function(t){return this.find(a,null,t)},flatMap:function(t,r){return Ct(this,function(t,r,e){var n=Wt(t);return t.toSeq().map((function(i,o){return n(r.call(e,i,o,t))})).flatten(!0)}(this,t,r))},flatten:function(t){return Ct(this,Ut(this,t,!0))},fromEntrySeq:function(){return new qt(this)},get:function(t,r){return this.find((function(r,e){return ut(e,t)}),void 0,r)},getIn:Te,groupBy:function(t,r){return function(t,r,e){var n=d(t),i=(A(t)?fe():jr()).asMutable();t.__iterate((function(o,u){i.update(r.call(e,o,u,t),(function(t){return(t=t||[]).push(n?[u,o]:o),t}))}));var o=Wt(t);return i.map((function(r){return Ct(t,o(r))})).asImmutable()}(this,t,r)},has:function(t){return this.get(t,n)!==n},hasIn:function(t){return Be(this,t)},isSubset:function(t){return t="function"==typeof t.includes?t:S(t),this.every((function(r){return t.includes(r)}))},isSuperset:function(t){return(t="function"==typeof t.isSubset?t:S(t)).isSubset(this)},keyOf:function(t){return this.findKey((function(r){return ut(r,t)}))},keySeq:function(){return this.toSeq().map(He).toIndexedSeq()},last:function(t){return this.toSeq().reverse().first(t)},lastKeyOf:function(t){return this.toKeyedSeq().reverse().keyOf(t)},max:function(t){return Bt(this,t)},maxBy:function(t,r){return Bt(this,r,t)},min:function(t){return Bt(this,t?Ye(t):Xe)},minBy:function(t,r){return Bt(this,r?Ye(r):Xe,t)},rest:function(){return this.slice(1)},skip:function(t){return 0===t?this:this.slice(Math.max(0,t))},skipLast:function(t){return 0===t?this:this.slice(0,-Math.max(0,t))},skipWhile:function(t,r){return Ct(this,Rt(this,t,r,!0))},skipUntil:function(t,r){return this.skipWhile(Ve(t),r)},sortBy:function(t,r){return Ct(this,Tt(this,r,t))},take:function(t){return this.slice(0,Math.max(0,t))},takeLast:function(t){return this.slice(-Math.max(0,t))},takeWhile:function(t,r){return Ct(this,function(t,r,e){var n=Nt(t);return n.__iterateUncached=function(n,i){var o=this;if(i)return this.cacheResult().__iterate(n,i);var u=0;return t.__iterate((function(t,i,s){return r.call(e,t,i,s)&&++u&&n(t,i,o)})),u},n.__iteratorUncached=function(n,i){var o=this;if(i)return this.cacheResult().__iterator(n,i);var u=t.__iterator(2,i),s=!0;return new U((function(){if(!s)return{value:void 0,done:!0};var t=u.next();if(t.done)return t;var i=t.value,a=i[0],c=i[1];return r.call(e,c,a,o)?2===n?t:T(n,a,c,t):(s=!1,{value:void 0,done:!0})}))},n}(this,t,r))},takeUntil:function(t,r){return this.takeWhile(Ve(t),r)},update:function(t){return t(this)},valueSeq:function(){return this.toIndexedSeq()},hashCode:function(){return this.__hash||(this.__hash=function(t){if(Infinity===t.size)return 0;var r=A(t),e=d(t),n=r?1:0;return t.__iterate(e?r?function(t,r){n=31*n+Fe(ft(t),ft(r))|0}:function(t,r){n=n+Fe(ft(t),ft(r))|0}:r?function(t){n=31*n+ft(t)|0}:function(t){n=n+ft(t)|0}),function(t,r){return r=st(r,3432918353),r=st(r<<15|r>>>-15,461845907),r=st(r<<13|r>>>-13,5),r=st((r=r+3864292196^t)^r>>>16,2246822507),r=at((r=st(r^r>>>13,3266489909))^r>>>16)}(t.size,n)}(this))}});var Le=S.prototype;Le[l]=!0,Le[R]=Le.values,Le.toJSON=Le.toArray,Le.__toStringMapper=$t,Le.inspect=Le.toSource=function(){return this.toString()},Le.chain=Le.flatMap,Le.contains=Le.includes,Oe(z,{flip:function(){return Ct(this,Mt(this))},mapEntries:function(t,r){var e=this,n=0;return Ct(this,this.toSeq().map((function(i,o){return t.call(r,[o,i],n++,e)})).fromEntrySeq())},mapKeys:function(t,r){var e=this;return Ct(this,this.toSeq().flip().map((function(n,i){return t.call(r,n,i,e)})).flip())}});var Ce=z.prototype;Ce[y]=!0,Ce[R]=Le.entries,Ce.toJSON=Ke,Ce.__toStringMapper=function(t,r){return $t(r)+": "+$t(t)},Oe(b,{toKeyedSeq:function(){return new Ot(this,!1)},filter:function(t,r){return Ct(this,xt(this,t,r,!1))},findIndex:function(t,r){var e=this.findEntry(t,r);return e?e[0]:-1},indexOf:function(t){var r=this.keyOf(t);return void 0===r?-1:r},lastIndexOf:function(t){var r=this.lastKeyOf(t);return void 0===r?-1:r},reverse:function(){return Ct(this,At(this,!1))},slice:function(t,r){return Ct(this,kt(this,t,r,!1))},splice:function(t,r){var e=arguments.length;if(r=Math.max(r||0,0),0===e||2===e&&!r)return this;t=f(t,t<0?this.count():this.size);var n=this.slice(0,t);return Ct(this,1===e?n:n.concat(Vt(arguments,2),this.slice(t+r)))},findLastIndex:function(t,r){var e=this.findLastEntry(t,r);return e?e[0]:-1},first:function(t){return this.get(0,t)},flatten:function(t){return Ct(this,Ut(this,t,!1))},get:function(t,r){return(t=s(this,t))<0||Infinity===this.size||void 0!==this.size&&t>this.size?r:this.find((function(r,e){return e===t}),void 0,r)},has:function(t){return(t=s(this,t))>=0&&(void 0!==this.size?Infinity===this.size||t<this.size:-1!==this.indexOf(t))},interpose:function(t){return Ct(this,function(t,r){var e=Nt(t);return e.size=t.size&&2*t.size-1,e.__iterateUncached=function(e,n){var i=this,o=0;return t.__iterate((function(t){return(!o||!1!==e(r,o++,i))&&!1!==e(t,o++,i)}),n),o},e.__iteratorUncached=function(e,n){var i,o=t.__iterator(1,n),u=0;return new U((function(){return(!i||u%2)&&(i=o.next()).done?i:u%2?T(e,u++,r):T(e,u++,i.value,i)}))},e}(this,t))},interleave:function(){var t=[this].concat(Vt(arguments)),r=Lt(this.toSeq(),V.of,t),e=r.flatten(!0);return r.size&&(e.size=r.size*t.length),Ct(this,e)},keySeq:function(){return Re(0,this.size)},last:function(t){return this.get(-1,t)},skipWhile:function(t,r){return Ct(this,Rt(this,t,r,!1))},zip:function(){return Ct(this,Lt(this,Qe,[this].concat(Vt(arguments))))},zipAll:function(){return Ct(this,Lt(this,Qe,[this].concat(Vt(arguments)),!0))},zipWith:function(t){var r=Vt(arguments);return r[0]=this,Ct(this,Lt(this,t,r))}});var Pe=b.prototype;Pe[g]=!0,Pe[D]=!0,Oe(I,{get:function(t,r){return this.has(t)?t:r},includes:function(t){return this.has(t)},keySeq:function(){return this.valueSeq()}});var We=I.prototype;function Ne(t,r,e,n,i,o){return Qt(t.size),t.__iterate((function(t,o,u){i?(i=!1,e=t):e=r.call(n,e,t,o,u)}),o),e}function He(t,r){return r}function Je(t,r){return[r,t]}function Ve(t){return function(){return!t.apply(this,arguments)}}function Ye(t){return function(){return-t.apply(this,arguments)}}function Qe(){return Vt(arguments)}function Xe(t,r){return t<r?1:t>r?-1:0}function Fe(t,r){return t^r+2654435769+(t<<6)+(t>>2)}We.has=Le.includes,We.contains=We.includes,We.keys=We.values,Oe(J,Ce),Oe(V,Pe),Oe(Y,We);var Ge=function(t){function r(t){return null==t?rn():be(t)?t:rn().withMutations((function(r){var e=I(t);Qt(e.size),e.forEach((function(t){return r.add(t)}))}))}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.of=function(){return this(arguments)},r.fromKeys=function(t){return this(z(t).keySeq())},r.prototype.toString=function(){return this.__toString("OrderedSet {","}")},r}(je);Ge.isOrderedSet=be;var Ze,$e=Ge.prototype;function tn(t,r){var e=Object.create($e);return e.size=t?t.size:0,e._map=t,e.__ownerID=r,e}function rn(){return Ze||(Ze=tn(pe()))}$e[D]=!0,$e.zip=Pe.zip,$e.zipWith=Pe.zipWith,$e.zipAll=Pe.zipAll,$e.__empty=rn,$e.__make=tn;var en=function(t,r){var e;!function(t){if(q(t))throw new Error("Can not call `Record` with an immutable Record as default values. Use a plain javascript object instead.");if(M(t))throw new Error("Can not call `Record` with an immutable Collection as default values. Use a plain javascript object instead.");if(null===t||"object"!=typeof t)throw new Error("Can not call `Record` with a non-object as default values. Use a plain javascript object instead.")}(t);var n=function(o){var u=this;if(o instanceof n)return o;if(!(this instanceof n))return new n(o);if(!e){e=!0;var s=Object.keys(t),a=i._indices={};i._name=r,i._keys=s,i._defaultValues=t;for(var c=0;c<s.length;c++){var f=s[c];a[f]=c,i[f]?"object"==typeof console&&console.warn:an(i,f)}}return this.__ownerID=void 0,this._values=Gr().withMutations((function(t){t.setSize(u._keys.length),z(o).forEach((function(r,e){t.set(u._indices[e],r===u._defaultValues[e]?void 0:r)}))})),this},i=n.prototype=Object.create(nn);return i.constructor=n,r&&(n.displayName=r),n};en.prototype.toString=function(){for(var t,r=un(this)+" { ",e=this._keys,n=0,i=e.length;n!==i;n++)r+=(n?", ":"")+(t=e[n])+": "+$t(this.get(t));return r+" }"},en.prototype.equals=function(t){return this===t||q(t)&&sn(this).equals(sn(t))},en.prototype.hashCode=function(){return sn(this).hashCode()},en.prototype.has=function(t){return this._indices.hasOwnProperty(t)},en.prototype.get=function(t,r){if(!this.has(t))return r;var e=this._indices[t],n=this._values.get(e);return void 0===n?this._defaultValues[t]:n},en.prototype.set=function(t,r){if(this.has(t)){var e=this._values.set(this._indices[t],r===this._defaultValues[t]?void 0:r);if(e!==this._values&&!this.__ownerID)return on(this,e)}return this},en.prototype.remove=function(t){return this.set(t)},en.prototype.clear=function(){var t=this._values.clear().setSize(this._keys.length);return this.__ownerID?this:on(this,t)},en.prototype.wasAltered=function(){return this._values.wasAltered()},en.prototype.toSeq=function(){return sn(this)},en.prototype.toJS=function(){return Ee(this)},en.prototype.entries=function(){return this.__iterator(2)},en.prototype.__iterator=function(t,r){return sn(this).__iterator(t,r)},en.prototype.__iterate=function(t,r){return sn(this).__iterate(t,r)},en.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var r=this._values.__ensureOwner(t);return t?on(this,r,t):(this.__ownerID=t,this._values=r,this)},en.isRecord=q,en.getDescriptiveName=un;var nn=en.prototype;function on(t,r,e){var n=Object.create(Object.getPrototypeOf(t));return n._values=r,n.__ownerID=e,n}function un(t){return t.constructor.displayName||t.constructor.name||"Record"}function sn(t){return $(t._keys.map((function(r){return[r,t.get(r)]})))}function an(t,r){try{Object.defineProperty(t,r,{get:function(){return this.get(r)},set:function(t){Yt(this.__ownerID,"Cannot set on an immutable record."),this.set(r,t)}})}catch(e){}}nn[j]=!0,nn[t]=nn.remove,nn.deleteIn=nn.removeIn=fr,nn.getIn=Te,nn.hasIn=Le.hasIn,nn.merge=lr,nn.mergeWith=vr,nn.mergeIn=Sr,nn.mergeDeep=wr,nn.mergeDeepWith=mr,nn.mergeDeepIn=zr,nn.setIn=ar,nn.update=pr,nn.updateIn=_r,nn.withMutations=br,nn.asMutable=Ir,nn.asImmutable=Or,nn[R]=nn.entries,nn.toJSON=nn.toObject=Le.toObject,nn.inspect=nn.toSource=function(){return this.toString()};var cn,fn=function(t){function r(t,e){if(!(this instanceof r))return new r(t,e);if(this._value=t,this.size=void 0===e?Infinity:Math.max(0,e),0===this.size){if(cn)return cn;cn=this}}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return 0===this.size?"Repeat []":"Repeat [ "+this._value+" "+this.size+" times ]"},r.prototype.get=function(t,r){return this.has(t)?this._value:r},r.prototype.includes=function(t){return ut(this._value,t)},r.prototype.slice=function(t,e){var n=this.size;return c(t,e,n)?this:new r(this._value,h(e,n)-f(t,n))},r.prototype.reverse=function(){return this},r.prototype.indexOf=function(t){return ut(this._value,t)?0:-1},r.prototype.lastIndexOf=function(t){return ut(this._value,t)?this.size:-1},r.prototype.__iterate=function(t,r){for(var e=this.size,n=0;n!==e&&!1!==t(this._value,r?e-++n:n++,this););return n},r.prototype.__iterator=function(t,r){var e=this,n=this.size,i=0;return new U((function(){return i===n?{value:void 0,done:!0}:T(t,r?n-++i:i++,e._value)}))},r.prototype.equals=function(t){return t instanceof r?ut(this._value,t._value):Ie(this,t)},r}(V);function hn(t,r,e,n,i,o){if("string"!=typeof e&&!M(e)&&(N(e)||K(e)||Gt(e))){if(~t.indexOf(e))throw new TypeError("Cannot convert circular structure to Immutable");t.push(e),i&&""!==n&&i.push(n);var u=r.call(o,n,H(e).map((function(n,o){return hn(t,r,n,o,i,e)})),i&&i.slice());return t.pop(),i&&i.pop(),u}return e}function pn(t,r){return w(r)?r.toList():d(r)?r.toMap():r.toSet()}var _n=S;const ln=Object.freeze(Object.defineProperty({__proto__:null,Collection:S,Iterable:_n,List:Gr,Map:jr,OrderedMap:fe,OrderedSet:Ge,PairSorting:{LeftThenRight:-1,RightThenLeft:1},Range:Re,Record:en,Repeat:fn,Seq:H,Set:je,Stack:ye,fromJS:function(t,r){return hn([],r||pn,t,"",r&&r.length>2?[]:void 0,{"":t})},get:rr,getIn:Ue,has:tr,hasIn:Be,hash:ft,is:ut,isAssociative:m,isCollection:v,isImmutable:M,isIndexed:w,isKeyed:d,isList:Fr,isMap:nt,isOrdered:A,isOrderedMap:it,isOrderedSet:be,isPlainObject:Gt,isRecord:q,isSeq:E,isSet:ze,isStack:ve,isValueObject:ot,merge:function(t){for(var r=[],e=arguments.length-1;e-- >0;)r[e]=arguments[e+1];return gr(t,r)},mergeDeep:function(t){for(var r=[],e=arguments.length-1;e-- >0;)r[e]=arguments[e+1];return dr(t,r)},mergeDeepWith:function(t,r){for(var e=[],n=arguments.length-2;n-- >0;)e[n]=arguments[n+2];return dr(r,e,t)},mergeWith:function(t,r){for(var e=[],n=arguments.length-2;n-- >0;)e[n]=arguments[n+2];return gr(r,e,t)},remove:nr,removeIn:cr,set:ir,setIn:sr,update:hr,updateIn:or,version:"5.0.3"},Symbol.toStringTag,{value:"Module"}));export{ln as i};
