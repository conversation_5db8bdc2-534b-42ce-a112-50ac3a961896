import{s as a}from"./main-Djn9RDyT.js";function e(e){return a({url:"/edtap/effect-package/editEffectSnap",method:"post",data:e})}function t(e){return a({url:"/edtap/effect-package/sync-model",method:"post",data:e})}function d(){return a({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"8"}})}function o(){return a({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"7"}})}const p=e=>a({url:"/edtap/effect-package/page",method:"get",params:e}),r=e=>a({url:"/edtap/effect-package/review",method:"post",data:e}),s=(e,t=null)=>a({url:"/edtap/effect-package/upload",method:"post",data:e,onUploadProgress(a){t&&t(a)}}),c=(e,t=null)=>a({url:"/edtap/effect-package/edit",method:"post",data:e,onUploadProgress(a){t&&t(a)}}),f=e=>a({url:"/edtap/effect-package/download",method:"get",params:e}),n=e=>a({url:"/edtap/effect-package/delete",method:"get",params:e});export{s as a,n as b,o as c,f as d,c as e,t as f,p as g,d as m,r,e as s};
