const e=(t,r,a={},s=a)=>{if(Array.isArray(r))r.forEach((r=>e(t,r,a,s)));else if("function"==typeof r)r(t,a,s,e);else{const o=Object.keys(r)[0];Array.isArray(r[o])?(s[o]={},e(t,r[o],a,s[o])):s[o]=r[o](t,a,s,e)}return a},t=(e,t)=>(r,a,s,o)=>{t(r,a,s)&&o(r,e,a,s)},r=e=>({data:e,pos:0}),a=(e=0)=>t=>t.data[t.pos+e],s=e=>t=>t.data.subarray(t.pos,t.pos+=e),o=e=>t=>t.data.subarray(t.pos,t.pos+e),n=e=>t=>Array.from(s(e)(t)).map((e=>String.fromCharCode(e))).join(""),i=e=>t=>{const r=s(2)(t);return e?(r[1]<<8)+r[0]:(r[0]<<8)+r[1]},d=(e,t)=>(r,a,o)=>{const n="function"==typeof t?t(r,a,o):t,i=s(e),d=new Array(n);for(var p=0;p<n;p++)d[p]=i(r);return d},p=e=>t=>{const r=(e=>e.data[e.pos++])(t),a=new Array(8);for(var s=0;s<8;s++)a[7-s]=!!(r&1<<s);return Object.keys(e).reduce(((t,r)=>{const s=e[r];return s.length?t[r]=((e,t,r)=>{for(var a=0,s=0;s<r;s++)a+=e[t+s]&&2**(r-s-1);return a})(a,s.index,s.length):t[r]=a[s.index],t}),{})};var c={blocks:e=>{const t=[],r=e.data.length;for(var a=0,o=(e=>e.data[e.pos++])(e);0!==o&&o;o=(e=>e.data[e.pos++])(e)){if(e.pos+o>=r){const o=r-e.pos;t.push(s(o)(e)),a+=o;break}t.push(s(o)(e)),a+=o}const n=new Uint8Array(a);for(var i=0,d=0;d<t.length;d++)n.set(t[d],i),i+=t[d].length;return n}};const l=t({gce:[{codes:s(2)},{byteSize:e=>e.data[e.pos++]},{extras:p({future:{index:0,length:3},disposal:{index:3,length:3},userInput:{index:6},transparentColorGiven:{index:7}})},{delay:i(!0)},{transparentColorIndex:e=>e.data[e.pos++]},{terminator:e=>e.data[e.pos++]}]},(e=>{var t=o(2)(e);return 33===t[0]&&249===t[1]})),u=t({image:[{code:e=>e.data[e.pos++]},{descriptor:[{left:i(!0)},{top:i(!0)},{width:i(!0)},{height:i(!0)},{lct:p({exists:{index:0},interlaced:{index:1},sort:{index:2},future:{index:3,length:2},size:{index:5,length:3}})}]},t({lct:d(3,((e,t,r)=>Math.pow(2,r.descriptor.lct.size+1)))},((e,t,r)=>r.descriptor.lct.exists)),{data:[{minCodeSize:e=>e.data[e.pos++]},c]}]},(e=>44===a()(e))),x=t({text:[{codes:s(2)},{blockSize:e=>e.data[e.pos++]},{preData:(e,t,r)=>s(r.text.blockSize)(e)},c]},(e=>{var t=o(2)(e);return 33===t[0]&&1===t[1]})),h=t({application:[{codes:s(2)},{blockSize:e=>e.data[e.pos++]},{id:(e,t,r)=>n(r.blockSize)(e)},c]},(e=>{var t=o(2)(e);return 33===t[0]&&255===t[1]})),g=t({comment:[{codes:s(2)},c]},(e=>{var t=o(2)(e);return 33===t[0]&&254===t[1]})),f=[{header:[{signature:n(3)},{version:n(3)}]},{lsd:[{width:i(!0)},{height:i(!0)},{gct:p({exists:{index:0},resolution:{index:1,length:3},sort:{index:4},size:{index:5,length:3}})},{backgroundColorIndex:e=>e.data[e.pos++]},{pixelAspectRatio:e=>e.data[e.pos++]}]},t({gct:d(3,((e,t)=>Math.pow(2,t.lsd.gct.size+1)))},((e,t)=>t.lsd.gct.exists)),{frames:(y=[l,h,g,u,x],b=e=>{var t=a()(e);return 33===t||44===t},(e,t,r,a)=>{const s=[];let o=e.pos;for(;b(e,t,r);){const r={};if(a(e,y,t,r),e.pos===o)break;o=e.pos,s.push(r)}return s})}];var y,b;export{r as b,e as p,f as s};
