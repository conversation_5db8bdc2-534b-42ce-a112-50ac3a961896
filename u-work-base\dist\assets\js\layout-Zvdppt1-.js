import{s as t}from"./main-Djn9RDyT.js";function a(){return t({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"10"}})}const e=a=>t({url:"/edtap/sys-layoutTemple/page",method:"post",data:a}),o=(a,e)=>t({url:"/edtap/sys-layoutTemple/add",method:"post",data:a,onUploadProgress(t){e&&e(t)}});function s(a){return t({url:"/edtap/sys-layoutTemple/batch-delete",method:"post",data:a})}const d=(a,e)=>t({url:"/edtap/sys-layoutTemple/edit",method:"post",data:a,onUploadProgress(t){e&&e(t)}}),l=a=>t({url:"/edtap/sys-layoutTemple/download-layoutTemple",method:"post",data:a}),p=a=>t({url:"/edtap/sys-layoutTemple/myLayoutTemplePage",method:"post",data:a});function u(a){return t({url:"/edtap/sys-layoutTemple/change-status",method:"post",data:a})}export{o as a,u as b,s as c,l as d,d as e,p as f,e as g,a as l};
