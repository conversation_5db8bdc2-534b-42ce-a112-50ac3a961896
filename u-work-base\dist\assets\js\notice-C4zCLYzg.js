import{s as e}from"./main-Djn9RDyT.js";const a="/edtap/realTime/message",t="/edtap/realTime/allRead",r="/edtap/realTime/read",m="edtap/realTime/deleteByIds";function s(t){return e({url:a,method:"get",params:t})}function d(a){return e({url:t,method:"get",params:a})}function n(a){return e({url:r,method:"get",params:a})}function o(a){return e({url:m,method:"post",data:a})}export{d as a,o as d,s as g,n as r};
