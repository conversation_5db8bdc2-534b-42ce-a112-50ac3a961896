import{s as t}from"./main-Djn9RDyT.js";const a="/edtap/sys-install-manage/add",e="/edtap/sys-install-manage/edit",s="/edtap/sys-install-manage/page",n="/edtap/sys-install-manage/batch-delete",o="/edtap/web-assets/list-all-type",r="/edtap/web-assets/add",d="/edtap/sys-file-info/upload",u="/edtap/web-assets/update-status",p="/edtap/web-assets/delete",l="/edtap/sys-file-info/download",i="/edtap/sys-project/download-project",m="/edtap/provider/redirect";function f(e){return t({url:a,method:"post",data:e})}function c(a){return t({url:e,method:"post",data:a})}function h(a){return t({url:s,method:"post",data:a})}function g(a){return t({url:n,method:"post",data:a})}function y(a){return t({url:m,method:"get",params:a})}function b(a){return t({url:o,method:"get",params:a})}function w(a,e){return t({url:d,method:"post",data:a,headers:{"Content-Type":"multipart/form-data"},onUploadProgress(t){e(t)}})}function j(a){return t({url:l,method:"get",params:a,responseType:"blob"})}function T(a){return t({url:r,method:"post",data:a})}function k(a){return t({url:u,method:"post",data:a})}function v(a){return t({url:p,method:"post",data:a})}function x(a){return t({url:i,method:"post",data:a})}export{h as a,v as b,j as c,x as d,b as e,k as f,y as g,T as h,f as i,c as j,g as k,w as u};
