import{r as t,a as n,as as e,K as s,ax as o,M as c,w as a,n as r,p as i,D as u,ar as f,P as p,Q as l,i as h,L as d,ay as y}from"./@vue-HScy-mz9.js";let b;const _=t=>b=t,v=Symbol();function j(t){return t&&"object"==typeof t&&"[object Object]"===Object.prototype.toString.call(t)&&"function"!=typeof t.toJSON}var $,g;function O(){const n=e(!0),s=n.run((()=>t({})));let o=[],c=[];const a=f({install(t){_(a),a._a=t,t.provide(v,a),t.config.globalProperties.$pinia=a,c.forEach((t=>o.push(t))),c=[]},use(t){return this._a?o.push(t):c.push(t),this},_p:o,_a:null,_e:n,_s:new Map,state:s});return a}(g=$||($={})).direct="direct",g.patchObject="patch object",g.patchFunction="patch function";const m=()=>{};function S(t,n,e,s=m){t.push(n);const o=()=>{const e=t.indexOf(n);e>-1&&(t.splice(e,1),s())};return!e&&p()&&l(o),o}function P(t,...n){t.slice().forEach((t=>{t(...n)}))}const E=t=>t(),w=Symbol(),x=Symbol();function M(t,n){t instanceof Map&&n instanceof Map?n.forEach(((n,e)=>t.set(e,n))):t instanceof Set&&n instanceof Set&&n.forEach(t.add,t);for(const e in n){if(!n.hasOwnProperty(e))continue;const c=n[e],a=t[e];j(a)&&j(c)&&t.hasOwnProperty(e)&&!s(c)&&!o(c)?t[e]=M(a,c):t[e]=c}return t}const{assign:I}=Object;function A(t){return!(!s(t)||!t.effect)}function F(u,p,l,h){const{state:y,actions:b,getters:v}=p,j=l.state.value[u];let g;return g=function(i,u,f={},p,l,h){let d;const y=I({actions:{}},f),b={deep:!0};let v,j,g,O=[],m=[];const F=p.state.value[i];let k;function C(t){let n;v=j=!1,"function"==typeof t?(t(p.state.value[i]),n={type:$.patchFunction,storeId:i,events:g}):(M(p.state.value[i],t),n={type:$.patchObject,payload:t,storeId:i,events:g});const e=k=Symbol();r().then((()=>{k===e&&(v=!0)})),j=!0,P(O,n,p.state.value[i])}t({});const D=function(){const{state:t}=f,n=t?t():{};this.$patch((t=>{I(t,n)}))};function J(){d.stop(),O=[],m=[],p._s.delete(i)}const K=(t,n="")=>{if(w in t)return t[x]=n,t;const e=function(){_(p);const n=Array.from(arguments),s=[],o=[];function c(t){s.push(t)}function a(t){o.push(t)}let r;P(m,{args:n,name:e[x],store:N,after:c,onError:a});try{r=t.apply(this&&this.$id===i?this:N,n)}catch(u){throw P(o,u),u}return r instanceof Promise?r.then((t=>(P(s,t),t))).catch((t=>(P(o,t),Promise.reject(t)))):(P(s,r),r)};return e[w]=!0,e[x]=n,e},L={_p:p,$id:i,$onAction:S.bind(null,m),$patch:C,$reset:D,$subscribe(t,n={}){const e=S(O,t,n.detached,(()=>s())),s=d.run((()=>a((()=>p.state.value[i]),(e=>{("sync"===n.flush?j:v)&&t({storeId:i,type:$.direct,events:g},e)}),I({},b,n))));return e},$dispose:J},N=n(L);p._s.set(i,N);const Q=p._a&&p._a.runWithContext||E,W=Q((()=>p._e.run((()=>(d=e()).run((()=>u({action:K})))))));for(const t in W){const n=W[t];if(s(n)&&!A(n)||o(n));else if("function"==typeof n){const e=K(n,t);W[t]=e,y.actions[t]=n}}I(N,W),I(c(N),W),Object.defineProperty(N,"$state",{get:()=>p.state.value[i],set:t=>{C((n=>{I(n,t)}))}}),p._p.forEach((t=>{I(N,d.run((()=>t({store:N,app:p._a,pinia:p,options:y}))))})),F&&h&&f.hydrate&&f.hydrate(N.$state,F);return v=!0,j=!0,N}(u,(function(){j||(l.state.value[u]=y?y():{});const t=d(l.state.value[u]);return I(t,b,Object.keys(v||{}).reduce(((t,n)=>(t[n]=f(i((()=>{_(l);const t=l._s.get(u);return v[n].call(t,t)}))),t)),{}))}),p,l,0,!0),g}function k(t,n,e){let s,o;function c(t,n){const e=y();(t=t||(e?h(v,null):null))&&_(t),(t=b)._s.has(s)||F(s,o,t);return t._s.get(s)}return"string"==typeof t?(s=t,o=n):(o=t,s=t.id),c.$id=s,c}function C(t){{const n=c(t),e={};for(const c in n){const a=n[c];a.effect?e[c]=i({get:()=>t[c],set(n){t[c]=n}}):(s(a)||o(a))&&(e[c]=u(t,c))}return e}}export{O as c,k as d,C as s};
