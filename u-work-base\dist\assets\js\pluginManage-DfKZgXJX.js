import{s as t}from"./main-Djn9RDyT.js";const a="/edtap/edtapMicro-api/page",e="/edtap/edtapMicro-api/add",r="/edtap/edtapMicro-api/batch-delete",o="/edtap/edtapMicro-api/edit",d="/edtap/edtapMicro-api/change-status",p="/edtap/edtapMicro-api/detail";function n(e){return t({url:a,method:"get",params:e})}function i(a,r){return t({url:e,method:"post",data:a,onUploadProgress(t){r(t)}})}function s(a){return t({url:r,method:"post",data:a})}function u(a,e){return t({url:o,method:"post",data:a,onUploadProgress(t){e(t)}})}function c(a){return t({url:d,method:"post",data:a})}function m(a){return t({url:p,method:"get",params:a})}export{i as a,n as b,c,s as d,u as e,m as g};
