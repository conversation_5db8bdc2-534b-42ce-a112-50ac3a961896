import{s as t}from"./main-Djn9RDyT.js";const a="/edtap/projectExampleNew/page",e="/edtap/projectExampleNew/add",o="/edtap/projectExampleNew/modify",n="/edtap/annotate-component/page-base",p="/edtap/annotate-component/delete-base",r="/edtap/projectExampleNew/delete",d="/edtap/projectExample/getInfoByContractNumber",u="/edtap/file/uploadFileByPath",s="/edtap/file/uploadFileByPath",m="/edtap/file/uploadFileByPath",l="/edtap/annotate-component/scene-bind",c="/edtap/annotate-component/component-bind",i="/edtap/annotate-component/upload-base",f="/edtap/annotate-component/upload-component",h="/edtap/projectExampleNew/approve",g="/edtap/annotate-component/delete-component";function N(a){return t({url:h,method:"post",data:a})}function j(a,e){return t({url:f,method:"post",data:a,onUploadProgress(t){e(t)}})}function x(a,e){return t({url:i,method:"post",data:a,onUploadProgress(t){e(t)}})}function b(a){return t({url:c,method:"post",data:a})}function y(a){return t({url:l,method:"post",data:a})}function E(a){return t({url:`${o}?projExampleId=${a.id}`,method:"post",data:a})}function w(a){return t({url:e,method:"post",data:a})}function P(){return t({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"4"}})}function B(){return t({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"2"}})}function G(){return t({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"3"}})}function $(){return t({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"4"}})}function F(a){return t({url:m,method:"post",data:a})}function v(a){return t({url:u,method:"post",data:a})}function I(a){return t({url:s,method:"post",data:a})}function U(a){return t({url:`${d}/${a}`,method:"get"})}function k(e){return t({url:a,method:"post",data:e})}function q(a){return t({url:p,method:"post",data:a})}function C(a){return t({url:g,method:"post",data:a})}function z(a){return t({url:r,method:"post",data:a})}function A(a){return t({url:n,method:"post",data:a})}const D=a=>t({url:"/edtap/annotate-component/page-component",method:"post",data:a}),H=a=>t({url:"/edtap/annotate-component/modify-component",method:"post",data:a}),J=a=>t({url:"/edtap/annotate-component/modify",method:"post",data:a});export{w as a,N as b,G as c,D as d,E as e,F as f,U as g,$ as h,v as i,k as j,z as k,P as l,J as m,y as n,B as o,H as p,A as q,b as r,C as s,q as t,j as u,I as v,x as w};
