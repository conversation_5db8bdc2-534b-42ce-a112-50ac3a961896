import{c as t,g as e}from"./@babel-B4rXMRun.js";import{l as n}from"./lodash.clonedeep-BYjN7_az.js";import{l as r}from"./lodash.isequal-CYhSZ-LT.js";var i=-1;function s(t,e,n,r){if(t===e)return t?[[0,t]]:[];if(null!=n){var a=function(t,e,n){var r="number"==typeof n?{index:n,length:0}:n.oldRange,i="number"==typeof n?null:n.newRange,s=t.length,u=e.length;if(0===r.length&&(null===i||0===i.length)){var o=r.index,f=t.slice(0,o),l=t.slice(o),a=i?i.index:null,h=o+u-s;if((null===a||a===h)&&!(h<0||h>u)){var p=e.slice(0,h);if((b=e.slice(h))===l){var c=Math.min(o,h);if((y=f.slice(0,c))===(x=p.slice(0,c)))return g(y,f.slice(c),p.slice(c),l)}}if(null===a||a===o){var d=o,b=(p=e.slice(0,d),e.slice(d));if(p===f){var v=Math.min(s-d,u-d);if((m=l.slice(l.length-v))===(k=b.slice(b.length-v)))return g(f,l.slice(0,l.length-v),b.slice(0,b.length-v),m)}}}if(r.length>0&&i&&0===i.length){var y=t.slice(0,r.index),m=t.slice(r.index+r.length);if(!(u<(c=y.length)+(v=m.length))){var x=e.slice(0,c),k=e.slice(u-v);if(y===x&&m===k)return g(y,t.slice(c,s-v),e.slice(c,u-v),m)}}return null}(t,e,n);if(a)return a}var h=o(t,e),p=t.substring(0,h);h=f(t=t.substring(h),e=e.substring(h));var c=t.substring(t.length-h),d=function(t,e){var n;if(!t)return[[1,e]];if(!e)return[[i,t]];var r=t.length>e.length?t:e,l=t.length>e.length?e:t,a=r.indexOf(l);if(-1!==a)return n=[[1,r.substring(0,a)],[0,l],[1,r.substring(a+l.length)]],t.length>e.length&&(n[0][0]=n[2][0]=i),n;if(1===l.length)return[[i,t],[1,e]];var h=function(t,e){var n=t.length>e.length?t:e,r=t.length>e.length?e:t;if(n.length<4||2*r.length<n.length)return null;function i(t,e,n){for(var r,i,s,u,l=t.substring(n,n+Math.floor(t.length/4)),a=-1,h="";-1!==(a=e.indexOf(l,a+1));){var p=o(t.substring(n),e.substring(a)),c=f(t.substring(0,n),e.substring(0,a));h.length<c+p&&(h=e.substring(a-c,a)+e.substring(a,a+p),r=t.substring(0,n-c),i=t.substring(n+p),s=e.substring(0,a-c),u=e.substring(a+p))}return 2*h.length>=t.length?[r,i,s,u,h]:null}var s,u,l,a,h,p=i(n,r,Math.ceil(n.length/4)),c=i(n,r,Math.ceil(n.length/2));if(!p&&!c)return null;s=c?p&&p[4].length>c[4].length?p:c:p;t.length>e.length?(u=s[0],l=s[1],a=s[2],h=s[3]):(a=s[0],h=s[1],u=s[2],l=s[3]);var g=s[4];return[u,l,a,h,g]}(t,e);if(h){var p=h[0],c=h[1],g=h[2],d=h[3],b=h[4],v=s(p,g),y=s(c,d);return v.concat([[0,b]],y)}return function(t,e){for(var n=t.length,r=e.length,s=Math.ceil((n+r)/2),o=s,f=2*s,l=new Array(f),a=new Array(f),h=0;h<f;h++)l[h]=-1,a[h]=-1;l[o+1]=0,a[o+1]=0;for(var p=n-r,c=p%2!=0,g=0,d=0,b=0,v=0,y=0;y<s;y++){for(var m=-y+g;m<=y-d;m+=2){for(var x=o+m,k=(L=m===-y||m!==y&&l[x-1]<l[x+1]?l[x+1]:l[x-1]+1)-m;L<n&&k<r&&t.charAt(L)===e.charAt(k);)L++,k++;if(l[x]=L,L>n)d+=2;else if(k>r)g+=2;else if(c){if((_=o+p-m)>=0&&_<f&&-1!==a[_])if(L>=(M=n-a[_]))return u(t,e,L,k)}}for(var j=-y+b;j<=y-v;j+=2){for(var M,_=o+j,A=(M=j===-y||j!==y&&a[_-1]<a[_+1]?a[_+1]:a[_-1]+1)-j;M<n&&A<r&&t.charAt(n-M-1)===e.charAt(r-A-1);)M++,A++;if(a[_]=M,M>n)v+=2;else if(A>r)b+=2;else if(!c){if((x=o+p-j)>=0&&x<f&&-1!==l[x]){var L;k=o+(L=l[x])-x;if(L>=(M=n-M))return u(t,e,L,k)}}}}return[[i,t],[1,e]]}(t,e)}(t=t.substring(0,t.length-h),e=e.substring(0,e.length-h));return p&&d.unshift([0,p]),c&&d.push([0,c]),l(d,r),d}function u(t,e,n,r){var i=t.substring(0,n),u=e.substring(0,r),o=t.substring(n),f=e.substring(r),l=s(i,u),a=s(o,f);return l.concat(a)}function o(t,e){if(!t||!e||t.charAt(0)!==e.charAt(0))return 0;for(var n=0,r=Math.min(t.length,e.length),i=r,s=0;n<i;)t.substring(s,i)==e.substring(s,i)?s=n=i:r=i,i=Math.floor((r-n)/2+n);return a(t.charCodeAt(i-1))&&i--,i}function f(t,e){if(!t||!e||t.slice(-1)!==e.slice(-1))return 0;for(var n=0,r=Math.min(t.length,e.length),i=r,s=0;n<i;)t.substring(t.length-i,t.length-s)==e.substring(e.length-i,e.length-s)?s=n=i:r=i,i=Math.floor((r-n)/2+n);return h(t.charCodeAt(t.length-i))&&i--,i}function l(t,e){t.push([0,""]);for(var n,r=0,s=0,u=0,a="",h="";r<t.length;)if(r<t.length-1&&!t[r][1])t.splice(r,1);else switch(t[r][0]){case 1:u++,h+=t[r][1],r++;break;case i:s++,a+=t[r][1],r++;break;case 0:var g=r-u-s-1;if(e){if(g>=0&&c(t[g][1])){var d=t[g][1].slice(-1);if(t[g][1]=t[g][1].slice(0,-1),a=d+a,h=d+h,!t[g][1]){t.splice(g,1),r--;var b=g-1;t[b]&&1===t[b][0]&&(u++,h=t[b][1]+h,b--),t[b]&&t[b][0]===i&&(s++,a=t[b][1]+a,b--),g=b}}if(p(t[r][1])){d=t[r][1].charAt(0);t[r][1]=t[r][1].slice(1),a+=d,h+=d}}if(r<t.length-1&&!t[r][1]){t.splice(r,1);break}if(a.length>0||h.length>0){a.length>0&&h.length>0&&(0!==(n=o(h,a))&&(g>=0?t[g][1]+=h.substring(0,n):(t.splice(0,0,[0,h.substring(0,n)]),r++),h=h.substring(n),a=a.substring(n)),0!==(n=f(h,a))&&(t[r][1]=h.substring(h.length-n)+t[r][1],h=h.substring(0,h.length-n),a=a.substring(0,a.length-n)));var v=u+s;0===a.length&&0===h.length?(t.splice(r-v,v),r-=v):0===a.length?(t.splice(r-v,v,[1,h]),r=r-v+1):0===h.length?(t.splice(r-v,v,[i,a]),r=r-v+1):(t.splice(r-v,v,[i,a],[1,h]),r=r-v+2)}0!==r&&0===t[r-1][0]?(t[r-1][1]+=t[r][1],t.splice(r,1)):r++,u=0,s=0,a="",h=""}""===t[t.length-1][1]&&t.pop();var y=!1;for(r=1;r<t.length-1;)0===t[r-1][0]&&0===t[r+1][0]&&(t[r][1].substring(t[r][1].length-t[r-1][1].length)===t[r-1][1]?(t[r][1]=t[r-1][1]+t[r][1].substring(0,t[r][1].length-t[r-1][1].length),t[r+1][1]=t[r-1][1]+t[r+1][1],t.splice(r-1,1),y=!0):t[r][1].substring(0,t[r+1][1].length)==t[r+1][1]&&(t[r-1][1]+=t[r+1][1],t[r][1]=t[r][1].substring(t[r+1][1].length)+t[r+1][1],t.splice(r+1,1),y=!0)),r++;y&&l(t,e)}function a(t){return t>=55296&&t<=56319}function h(t){return t>=56320&&t<=57343}function p(t){return h(t.charCodeAt(0))}function c(t){return a(t.charCodeAt(t.length-1))}function g(t,e,n,r){return c(t)||p(r)?null:function(t){for(var e=[],n=0;n<t.length;n++)t[n][1].length>0&&e.push(t[n]);return e}([[0,t],[i,e],[1,n],[0,r]])}function d(t,e,n){return s(t,e,n,!0)}d.INSERT=1,d.DELETE=i,d.EQUAL=0;var b=d,v={},y=t&&t.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(v,"__esModule",{value:!0});var m,x,k=y(n),j=y(r);(x=m||(m={})).compose=function(t,e,n){void 0===t&&(t={}),void 0===e&&(e={}),"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});var r=k.default(e);for(var i in n||(r=Object.keys(r).reduce((function(t,e){return null!=r[e]&&(t[e]=r[e]),t}),{})),t)void 0!==t[i]&&void 0===e[i]&&(r[i]=t[i]);return Object.keys(r).length>0?r:void 0},x.diff=function(t,e){void 0===t&&(t={}),void 0===e&&(e={}),"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});var n=Object.keys(t).concat(Object.keys(e)).reduce((function(n,r){return j.default(t[r],e[r])||(n[r]=void 0===e[r]?null:e[r]),n}),{});return Object.keys(n).length>0?n:void 0},x.invert=function(t,e){void 0===t&&(t={}),void 0===e&&(e={}),t=t||{};var n=Object.keys(e).reduce((function(n,r){return e[r]!==t[r]&&void 0!==t[r]&&(n[r]=e[r]),n}),{});return Object.keys(t).reduce((function(n,r){return t[r]!==e[r]&&void 0===e[r]&&(n[r]=null),n}),n)},x.transform=function(t,e,n){if(void 0===n&&(n=!1),"object"!=typeof t)return e;if("object"==typeof e){if(!n)return e;var r=Object.keys(e).reduce((function(n,r){return void 0===t[r]&&(n[r]=e[r]),n}),{});return Object.keys(r).length>0?r:void 0}},v.default=m;var M,_,A={},L={};function w(){if(_)return A;_=1;var e=t&&t.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(A,"__esModule",{value:!0});var n,r,i=e(function(){if(M)return L;M=1;var e=t&&t.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(L,"__esModule",{value:!0});var n=e(w()),r=function(){function t(t){this.ops=t,this.index=0,this.offset=0}return t.prototype.hasNext=function(){return this.peekLength()<Infinity},t.prototype.next=function(t){t||(t=Infinity);var e=this.ops[this.index];if(e){var r=this.offset,i=n.default.length(e);if(t>=i-r?(t=i-r,this.index+=1,this.offset=0):this.offset+=t,"number"==typeof e.delete)return{delete:t};var s={};return e.attributes&&(s.attributes=e.attributes),"number"==typeof e.retain?s.retain=t:"string"==typeof e.insert?s.insert=e.insert.substr(r,t):s.insert=e.insert,s}return{retain:Infinity}},t.prototype.peek=function(){return this.ops[this.index]},t.prototype.peekLength=function(){return this.ops[this.index]?n.default.length(this.ops[this.index])-this.offset:Infinity},t.prototype.peekType=function(){return this.ops[this.index]?"number"==typeof this.ops[this.index].delete?"delete":"number"==typeof this.ops[this.index].retain?"retain":"insert":"retain"},t.prototype.rest=function(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);var t=this.offset,e=this.index,n=this.next(),r=this.ops.slice(this.index);return this.offset=t,this.index=e,[n].concat(r)}return[]},t}();return L.default=r,L}());return(r=n||(n={})).iterator=function(t){return new i.default(t)},r.length=function(t){return"number"==typeof t.delete?t.delete:"number"==typeof t.retain?t.retain:"string"==typeof t.insert?t.insert.length:1},A.default=n,A}var O=t&&t.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},E=O(b),T=O(n),N=O(r),I=O(v),D=O(w()),C=String.fromCharCode(0);const P=e(function(){function t(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]}return t.prototype.insert=function(t,e){var n={};return"string"==typeof t&&0===t.length?this:(n.insert=t,null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n))},t.prototype.delete=function(t){return t<=0?this:this.push({delete:t})},t.prototype.retain=function(t,e){if(t<=0)return this;var n={retain:t};return null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n)},t.prototype.push=function(t){var e=this.ops.length,n=this.ops[e-1];if(t=T.default(t),"object"==typeof n){if("number"==typeof t.delete&&"number"==typeof n.delete)return this.ops[e-1]={delete:n.delete+t.delete},this;if("number"==typeof n.delete&&null!=t.insert&&(e-=1,"object"!=typeof(n=this.ops[e-1])))return this.ops.unshift(t),this;if(N.default(t.attributes,n.attributes)){if("string"==typeof t.insert&&"string"==typeof n.insert)return this.ops[e-1]={insert:n.insert+t.insert},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if("number"==typeof t.retain&&"number"==typeof n.retain)return this.ops[e-1]={retain:n.retain+t.retain},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this},t.prototype.chop=function(){var t=this.ops[this.ops.length-1];return t&&t.retain&&!t.attributes&&this.ops.pop(),this},t.prototype.filter=function(t){return this.ops.filter(t)},t.prototype.forEach=function(t){this.ops.forEach(t)},t.prototype.map=function(t){return this.ops.map(t)},t.prototype.partition=function(t){var e=[],n=[];return this.forEach((function(r){(t(r)?e:n).push(r)})),[e,n]},t.prototype.reduce=function(t,e){return this.ops.reduce(t,e)},t.prototype.changeLength=function(){return this.reduce((function(t,e){return e.insert?t+D.default.length(e):e.delete?t-e.delete:t}),0)},t.prototype.length=function(){return this.reduce((function(t,e){return t+D.default.length(e)}),0)},t.prototype.slice=function(e,n){void 0===e&&(e=0),void 0===n&&(n=Infinity);for(var r=[],i=D.default.iterator(this.ops),s=0;s<n&&i.hasNext();){var u=void 0;s<e?u=i.next(e-s):(u=i.next(n-s),r.push(u)),s+=D.default.length(u)}return new t(r)},t.prototype.compose=function(e){var n=D.default.iterator(this.ops),r=D.default.iterator(e.ops),i=[],s=r.peek();if(null!=s&&"number"==typeof s.retain&&null==s.attributes){for(var u=s.retain;"insert"===n.peekType()&&n.peekLength()<=u;)u-=n.peekLength(),i.push(n.next());s.retain-u>0&&r.next(s.retain-u)}for(var o=new t(i);n.hasNext()||r.hasNext();)if("insert"===r.peekType())o.push(r.next());else if("delete"===n.peekType())o.push(n.next());else{var f=Math.min(n.peekLength(),r.peekLength()),l=n.next(f),a=r.next(f);if("number"==typeof a.retain){var h={};"number"==typeof l.retain?h.retain=f:h.insert=l.insert;var p=I.default.compose(l.attributes,a.attributes,"number"==typeof l.retain);if(p&&(h.attributes=p),o.push(h),!r.hasNext()&&N.default(o.ops[o.ops.length-1],h)){var c=new t(n.rest());return o.concat(c).chop()}}else"number"==typeof a.delete&&"number"==typeof l.retain&&o.push(a)}return o.chop()},t.prototype.concat=function(e){var n=new t(this.ops.slice());return e.ops.length>0&&(n.push(e.ops[0]),n.ops=n.ops.concat(e.ops.slice(1))),n},t.prototype.diff=function(e,n){if(this.ops===e.ops)return new t;var r=[this,e].map((function(t){return t.map((function(n){if(null!=n.insert)return"string"==typeof n.insert?n.insert:C;throw new Error("diff() called "+(t===e?"on":"with")+" non-document")})).join("")})),i=new t,s=E.default(r[0],r[1],n),u=D.default.iterator(this.ops),o=D.default.iterator(e.ops);return s.forEach((function(t){for(var e=t[1].length;e>0;){var n=0;switch(t[0]){case E.default.INSERT:n=Math.min(o.peekLength(),e),i.push(o.next(n));break;case E.default.DELETE:n=Math.min(e,u.peekLength()),u.next(n),i.delete(n);break;case E.default.EQUAL:n=Math.min(u.peekLength(),o.peekLength(),e);var r=u.next(n),s=o.next(n);N.default(r.insert,s.insert)?i.retain(n,I.default.diff(r.attributes,s.attributes)):i.push(s).delete(n)}e-=n}})),i.chop()},t.prototype.eachLine=function(e,n){void 0===n&&(n="\n");for(var r=D.default.iterator(this.ops),i=new t,s=0;r.hasNext();){if("insert"!==r.peekType())return;var u=r.peek(),o=D.default.length(u)-r.peekLength(),f="string"==typeof u.insert?u.insert.indexOf(n,o)-o:-1;if(f<0)i.push(r.next());else if(f>0)i.push(r.next(f));else{if(!1===e(i,r.next(1).attributes||{},s))return;s+=1,i=new t}}i.length()>0&&e(i,{},s)},t.prototype.invert=function(e){var n=new t;return this.reduce((function(t,r){if(r.insert)n.delete(D.default.length(r));else{if(r.retain&&null==r.attributes)return n.retain(r.retain),t+r.retain;if(r.delete||r.retain&&r.attributes){var i=r.delete||r.retain;return e.slice(t,t+i).forEach((function(t){r.delete?n.push(t):r.retain&&r.attributes&&n.retain(D.default.length(t),I.default.invert(r.attributes,t.attributes))})),t+i}}return t}),0),n.chop()},t.prototype.transform=function(e,n){if(void 0===n&&(n=!1),n=!!n,"number"==typeof e)return this.transformPosition(e,n);for(var r=e,i=D.default.iterator(this.ops),s=D.default.iterator(r.ops),u=new t;i.hasNext()||s.hasNext();)if("insert"!==i.peekType()||!n&&"insert"===s.peekType())if("insert"===s.peekType())u.push(s.next());else{var o=Math.min(i.peekLength(),s.peekLength()),f=i.next(o),l=s.next(o);if(f.delete)continue;l.delete?u.push(l):u.retain(o,I.default.transform(f.attributes,l.attributes,n))}else u.retain(D.default.length(i.next()));return u.chop()},t.prototype.transformPosition=function(t,e){void 0===e&&(e=!1),e=!!e;for(var n=D.default.iterator(this.ops),r=0;n.hasNext()&&r<=t;){var i=n.peekLength(),s=n.peekType();n.next(),"delete"!==s?("insert"===s&&(r<t||!e)&&(t+=i),r+=i):t-=Math.min(i,t-r)}return t},t.Op=D.default,t.AttributeMap=I.default,t}());export{P as D};
