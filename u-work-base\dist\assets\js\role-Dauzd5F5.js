import{s as t}from"./main-Djn9RDyT.js";const e="/edtap/sys-role/team-allow-grant",a="/edtap/sys-role/add",r="/edtap/sys-role/edit",s="/edtap/sys-role/delete",o="/edtap/sys-role/delete-batch",n="/edtap/sys-role/own-menu",d="/edtap/sys-role/grant-menu",u="/edtap/sys-menu/temp-tree-for-grant";function l(e){return t({url:u,method:"get",params:e})}function m(a){return t({url:e,method:"get",params:a})}function p(e){return t({url:a,method:"post",data:e})}function c(e){return t({url:r,method:"post",data:e})}function f(e){return t({url:s,method:"post",data:e})}function i(e){return t({url:o,method:"post",data:e})}function h(e){return t({url:n,method:"get",params:e})}function y(e){return t({url:d,method:"post",data:e})}export{i as a,p as b,c,l as d,h as e,y as f,m as g,f as s};
