import{s as t}from"./main-Djn9RDyT.js";const e="/edtap/sys-role/allow-grant",r="/edtap/sys-role/team-allow-grant",a="/edtap/sys-role/add",s="/edtap/sys-role/edit",o="/edtap/sys-role/delete",n="/edtap/sys-role/delete-batch",u="/edtap/sys-role/own-menu",d="/edtap/sys-role/grant-menu",l="/edtap/sys-menu/tree-for-grant",p="/edtap/sys-user/page-by-role",m="/edtap/sys-role/delete-role-user",c="/edtap/sys-role/change-acquiesce",i="/edtap/sys-menu/temp-tree-for-grant";function f(e){return t({url:l,method:"get",params:e})}function h(e){return t({url:i,method:"get",params:e})}function g(r){return t({url:e,method:"get",params:r})}function y(e){return t({url:r,method:"get",params:e})}function b(e){return t({url:c,method:"post",data:e})}function w(e){return t({url:a,method:"post",data:e})}function j(e){return t({url:s,method:"post",data:e})}function k(e){return t({url:o,method:"post",data:e})}function q(e){return t({url:n,method:"post",data:e})}function x(e){return t({url:u,method:"get",params:e})}function v(e){return t({url:d,method:"post",data:e})}function z(e){return t({url:p,method:"get",params:e})}function A(e){return t({url:m,method:"post",data:e})}export{j as a,x as b,v as c,b as d,y as e,k as f,h as g,q as h,f as i,g as j,z as k,A as l,w as s};
