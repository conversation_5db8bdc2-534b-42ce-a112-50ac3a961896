import{s as a}from"./main-Djn9RDyT.js";function s(){return a({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"5"}})}const t=s=>a({url:"/edtap/sys-sample/page",method:"post",data:s}),e=s=>a({url:"/edtap/sys-sample/detail",method:"get",params:s}),p=s=>a({url:"/edtap/sys-sample/download-sample",method:"post",data:s}),d=s=>a({url:"/edtap/sys-sample/mySamplePage",method:"post",data:s});export{d as a,e as b,p as d,t as g,s};
