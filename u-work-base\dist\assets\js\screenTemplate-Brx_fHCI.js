import{s as t}from"./main-Djn9RDyT.js";const a=a=>t({url:"/edtap/projectGroup/moveProjectGroup",method:"post",data:a}),e=a=>t({url:`/edtap/ex/sceneTemplate/addCount/${a}`,method:"post"}),o=a=>t({url:"/edtap/scene/copy",method:"post",data:a}),p=a=>t({url:"/edtap/annotate-component/page-base",method:"post",data:a});function s(){return t({url:"/edtap/tagGroup/list",method:"post",data:{tagName:"{}",range:"2"}})}function d(a){return t({url:"/edtap/sys-project/user-project-list",method:"post",data:a})}const n=a=>t({url:"/edtap/annotate-component/modify",method:"post",data:a});function r(a){return t({url:"/edtap/annotate-component/delete-base",method:"post",data:a})}export{p as a,r as b,o as c,e as d,d as g,n as p,a as r,s};
