import{r as e,l as t,w as s,n as a,f as i,j as r,h as l,O as n,p as o,i as d,q as c}from"./@vue-HScy-mz9.js";function p(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function u(e,t){void 0===e&&(e={}),void 0===t&&(t={});const s=["__proto__","constructor","prototype"];Object.keys(t).filter((e=>s.indexOf(e)<0)).forEach((s=>{void 0===e[s]?e[s]=t[s]:p(t[s])&&p(e[s])&&Object.keys(t[s]).length>0&&u(e[s],t[s])}))}const m={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function f(){const e="undefined"!=typeof document?document:{};return u(e,m),e}const h={document:m,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function v(){const e="undefined"!=typeof window?window:{};return u(e,h),e}function g(e){return void 0===e&&(e=""),e.trim().split(" ").filter((e=>!!e.trim()))}function w(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function b(){return Date.now()}function y(e,t){void 0===t&&(t="x");const s=v();let a,i,r;const l=function(e){const t=v();let s;return t.getComputedStyle&&(s=t.getComputedStyle(e,null)),!s&&e.currentStyle&&(s=e.currentStyle),s||(s=e.style),s}(e);return s.WebKitCSSMatrix?(i=l.transform||l.webkitTransform,i.split(",").length>6&&(i=i.split(", ").map((e=>e.replace(",","."))).join(", ")),r=new s.WebKitCSSMatrix("none"===i?"":i)):(r=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),a=r.toString().split(",")),"x"===t&&(i=s.WebKitCSSMatrix?r.m41:16===a.length?parseFloat(a[12]):parseFloat(a[4])),"y"===t&&(i=s.WebKitCSSMatrix?r.m42:16===a.length?parseFloat(a[13]):parseFloat(a[5])),i||0}function E(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function S(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let a=1;a<arguments.length;a+=1){const i=a<0||arguments.length<=a?void 0:arguments[a];if(null!=i&&(s=i,!("undefined"!=typeof window&&void 0!==window.HTMLElement?s instanceof HTMLElement:s&&(1===s.nodeType||11===s.nodeType)))){const s=Object.keys(Object(i)).filter((e=>t.indexOf(e)<0));for(let t=0,a=s.length;t<a;t+=1){const a=s[t],r=Object.getOwnPropertyDescriptor(i,a);void 0!==r&&r.enumerable&&(E(e[a])&&E(i[a])?i[a].__swiper__?e[a]=i[a]:S(e[a],i[a]):!E(e[a])&&E(i[a])?(e[a]={},i[a].__swiper__?e[a]=i[a]:S(e[a],i[a])):e[a]=i[a])}}}var s;return e}function x(e,t,s){e.style.setProperty(t,s)}function T(e){let{swiper:t,targetPosition:s,side:a}=e;const i=v(),r=-t.translate;let l,n=null;const o=t.params.speed;t.wrapperEl.style.scrollSnapType="none",i.cancelAnimationFrame(t.cssModeFrameID);const d=s>r?"next":"prev",c=(e,t)=>"next"===d&&e>=t||"prev"===d&&e<=t,p=()=>{l=(new Date).getTime(),null===n&&(n=l);const e=Math.max(Math.min((l-n)/o,1),0),d=.5-Math.cos(e*Math.PI)/2;let u=r+d*(s-r);if(c(u,s)&&(u=s),t.wrapperEl.scrollTo({[a]:u}),c(u,s))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout((()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[a]:u})})),void i.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=i.requestAnimationFrame(p)};p()}function M(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function C(e,t){void 0===t&&(t="");const s=v(),a=[...e.children];return s.HTMLSlotElement&&e instanceof HTMLSlotElement&&a.push(...e.assignedElements()),t?a.filter((e=>e.matches(t))):a}function P(e){try{return}catch(t){}}function L(e,t){void 0===t&&(t=[]);const s=document.createElement(e);return s.classList.add(...Array.isArray(t)?t:g(t)),s}function O(e){const t=v(),s=f(),a=e.getBoundingClientRect(),i=s.body,r=e.clientTop||i.clientTop||0,l=e.clientLeft||i.clientLeft||0,n=e===t?t.scrollY:e.scrollTop,o=e===t?t.scrollX:e.scrollLeft;return{top:a.top+n-r,left:a.left+o-l}}function z(e,t){return v().getComputedStyle(e,null).getPropertyValue(t)}function I(e){let t,s=e;if(s){for(t=0;null!==(s=s.previousSibling);)1===s.nodeType&&(t+=1);return t}}function k(e,t){const s=[];let a=e.parentElement;for(;a;)t?a.matches(t)&&s.push(a):s.push(a),a=a.parentElement;return s}function A(e,t){t&&e.addEventListener("transitionend",(function s(a){a.target===e&&(t.call(e,a),e.removeEventListener("transitionend",s))}))}function $(e,t,s){const a=v();return e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(a.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(a.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom"))}function D(e){return(Array.isArray(e)?e:[e]).filter((e=>!!e))}function B(e){return t=>Math.abs(t)>0&&e.browser&&e.browser.need3dFix&&Math.abs(t)%90==0?t+.001:t}let _,N,G;function H(){return _||(_=function(){const e=v(),t=f();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),_}function R(e){return void 0===e&&(e={}),N||(N=function(e){let{userAgent:t}=void 0===e?{}:e;const s=H(),a=v(),i=a.navigator.platform,r=t||a.navigator.userAgent,l={ios:!1,android:!1},n=a.screen.width,o=a.screen.height,d=r.match(/(Android);?[\s\/]+([\d.]+)?/);let c=r.match(/(iPad).*OS\s([\d_]+)/);const p=r.match(/(iPod)(.*OS\s([\d_]+))?/),u=!c&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m="Win32"===i;let f="MacIntel"===i;return!c&&f&&s.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${n}x${o}`)>=0&&(c=r.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),f=!1),d&&!m&&(l.os="android",l.android=!0),(c||u||p)&&(l.os="ios",l.ios=!0),l}(e)),N}function j(){return G||(G=function(){const e=v(),t=R();let s=!1;function a(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}if(a()){const t=String(e.navigator.userAgent);if(t.includes("Version/")){const[e,a]=t.split("Version/")[1].split(" ")[0].split(".").map((e=>Number(e)));s=e<16||16===e&&a<2}}const i=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),r=a();return{isSafari:s||r,needPerspectiveFix:s,need3dFix:r||i&&t.ios,isWebView:i}}()),G}const X=(e,t,s)=>{t&&!e.classList.contains(s)?e.classList.add(s):!t&&e.classList.contains(s)&&e.classList.remove(s)};const Y=(e,t,s)=>{t&&!e.classList.contains(s)?e.classList.add(s):!t&&e.classList.contains(s)&&e.classList.remove(s)};const F=(e,t)=>{if(!e||e.destroyed||!e.params)return;const s=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(s){let t=s.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(s.shadowRoot?t=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame((()=>{s.shadowRoot&&(t=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),t&&t.remove())}))),t&&t.remove()}},q=(e,t)=>{if(!e.slides[t])return;const s=e.slides[t].querySelector('[loading="lazy"]');s&&s.removeAttribute("loading")},V=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const s=e.slides.length;if(!s||!t||t<0)return;t=Math.min(t,s);const a="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),i=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const s=i,r=[s-t];return r.push(...Array.from({length:t}).map(((e,t)=>s+a+t))),void e.slides.forEach(((t,s)=>{r.includes(t.column)&&q(e,s)}))}const r=i+a-1;if(e.params.rewind||e.params.loop)for(let l=i-t;l<=r+t;l+=1){const t=(l%s+s)%s;(t<i||t>r)&&q(e,t)}else for(let l=Math.max(i-t,0);l<=Math.min(r+t,s-1);l+=1)l!==i&&(l>r||l<i)&&q(e,l)};function W(e){let{swiper:t,runCallbacks:s,direction:a,step:i}=e;const{activeIndex:r,previousIndex:l}=t;let n=a;if(n||(n=r>l?"next":r<l?"prev":"reset"),t.emit(`transition${i}`),s&&r!==l){if("reset"===n)return void t.emit(`slideResetTransition${i}`);t.emit(`slideChangeTransition${i}`),"next"===n?t.emit(`slideNextTransition${i}`):t.emit(`slidePrevTransition${i}`)}}function U(e,t,s){const a=v(),{params:i}=e,r=i.edgeSwipeDetection,l=i.edgeSwipeThreshold;return!r||!(s<=l||s>=a.innerWidth-l)||"prevent"===r&&(t.preventDefault(),!0)}function K(e){const t=this,s=f();let a=e;a.originalEvent&&(a=a.originalEvent);const i=t.touchEventsData;if("pointerdown"===a.type){if(null!==i.pointerId&&i.pointerId!==a.pointerId)return;i.pointerId=a.pointerId}else"touchstart"===a.type&&1===a.targetTouches.length&&(i.touchId=a.targetTouches[0].identifier);if("touchstart"===a.type)return void U(t,a,a.targetTouches[0].pageX);const{params:r,touches:l,enabled:n}=t;if(!n)return;if(!r.simulateTouch&&"mouse"===a.pointerType)return;if(t.animating&&r.preventInteractionOnTransition)return;!t.animating&&r.cssMode&&r.loop&&t.loopFix();let o=a.target;if("wrapper"===r.touchEventsTarget&&!function(e,t){const s=v();let a=t.contains(e);!a&&s.HTMLSlotElement&&t instanceof HTMLSlotElement&&(a=[...t.assignedElements()].includes(e),a||(a=function(e,t){const s=[t];for(;s.length>0;){const t=s.shift();if(e===t)return!0;s.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t)));return a}(o,t.wrapperEl))return;if("which"in a&&3===a.which)return;if("button"in a&&a.button>0)return;if(i.isTouched&&i.isMoved)return;const d=!!r.noSwipingClass&&""!==r.noSwipingClass,c=a.composedPath?a.composedPath():a.path;d&&a.target&&a.target.shadowRoot&&c&&(o=c[0]);const p=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,u=!(!a.target||!a.target.shadowRoot);if(r.noSwiping&&(u?function(e,t){return void 0===t&&(t=this),function t(s){if(!s||s===f()||s===v())return null;s.assignedSlot&&(s=s.assignedSlot);const a=s.closest(e);return a||s.getRootNode?a||t(s.getRootNode().host):null}(t)}(p,o):o.closest(p)))return void(t.allowClick=!0);if(r.swipeHandler&&!o.closest(r.swipeHandler))return;l.currentX=a.pageX,l.currentY=a.pageY;const m=l.currentX,h=l.currentY;if(!U(t,a,m))return;Object.assign(i,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),l.startX=m,l.startY=h,i.touchStartTime=b(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,r.threshold>0&&(i.allowThresholdMove=!1);let g=!0;o.matches(i.focusableElements)&&(g=!1,"SELECT"===o.nodeName&&(i.isTouched=!1)),s.activeElement&&s.activeElement.matches(i.focusableElements)&&s.activeElement!==o&&("mouse"===a.pointerType||"mouse"!==a.pointerType&&!o.matches(i.focusableElements))&&s.activeElement.blur();const w=g&&t.allowTouchMove&&r.touchStartPreventDefault;!r.touchStartForcePreventDefault&&!w||o.isContentEditable||a.preventDefault(),r.freeMode&&r.freeMode.enabled&&t.freeMode&&t.animating&&!r.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",a)}function Z(e){const t=f(),s=this,a=s.touchEventsData,{params:i,touches:r,rtlTranslate:l,enabled:n}=s;if(!n)return;if(!i.simulateTouch&&"mouse"===e.pointerType)return;let o,d=e;if(d.originalEvent&&(d=d.originalEvent),"pointermove"===d.type){if(null!==a.touchId)return;if(d.pointerId!==a.pointerId)return}if("touchmove"===d.type){if(o=[...d.changedTouches].find((e=>e.identifier===a.touchId)),!o||o.identifier!==a.touchId)return}else o=d;if(!a.isTouched)return void(a.startMoving&&a.isScrolling&&s.emit("touchMoveOpposite",d));const c=o.pageX,p=o.pageY;if(d.preventedByNestedSwiper)return r.startX=c,void(r.startY=p);if(!s.allowTouchMove)return d.target.matches(a.focusableElements)||(s.allowClick=!1),void(a.isTouched&&(Object.assign(r,{startX:c,startY:p,currentX:c,currentY:p}),a.touchStartTime=b()));if(i.touchReleaseOnEdges&&!i.loop)if(s.isVertical()){if(p<r.startY&&s.translate<=s.maxTranslate()||p>r.startY&&s.translate>=s.minTranslate())return a.isTouched=!1,void(a.isMoved=!1)}else if(c<r.startX&&s.translate<=s.maxTranslate()||c>r.startX&&s.translate>=s.minTranslate())return;if(t.activeElement&&t.activeElement.matches(a.focusableElements)&&t.activeElement!==d.target&&"mouse"!==d.pointerType&&t.activeElement.blur(),t.activeElement&&d.target===t.activeElement&&d.target.matches(a.focusableElements))return a.isMoved=!0,void(s.allowClick=!1);a.allowTouchCallbacks&&s.emit("touchMove",d),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=c,r.currentY=p;const u=r.currentX-r.startX,m=r.currentY-r.startY;if(s.params.threshold&&Math.sqrt(u**2+m**2)<s.params.threshold)return;if(void 0===a.isScrolling){let e;s.isHorizontal()&&r.currentY===r.startY||s.isVertical()&&r.currentX===r.startX?a.isScrolling=!1:u*u+m*m>=25&&(e=180*Math.atan2(Math.abs(m),Math.abs(u))/Math.PI,a.isScrolling=s.isHorizontal()?e>i.touchAngle:90-e>i.touchAngle)}if(a.isScrolling&&s.emit("touchMoveOpposite",d),void 0===a.startMoving&&(r.currentX===r.startX&&r.currentY===r.startY||(a.startMoving=!0)),a.isScrolling||"touchmove"===d.type&&a.preventTouchMoveFromPointerMove)return void(a.isTouched=!1);if(!a.startMoving)return;s.allowClick=!1,!i.cssMode&&d.cancelable&&d.preventDefault(),i.touchMoveStopPropagation&&!i.nested&&d.stopPropagation();let h=s.isHorizontal()?u:m,v=s.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;i.oneWayMovement&&(h=Math.abs(h)*(l?1:-1),v=Math.abs(v)*(l?1:-1)),r.diff=h,h*=i.touchRatio,l&&(h=-h,v=-v);const g=s.touchesDirection;s.swipeDirection=h>0?"prev":"next",s.touchesDirection=v>0?"prev":"next";const w=s.params.loop&&!i.cssMode,y="next"===s.touchesDirection&&s.allowSlideNext||"prev"===s.touchesDirection&&s.allowSlidePrev;if(!a.isMoved){if(w&&y&&s.loopFix({direction:s.swipeDirection}),a.startTranslate=s.getTranslate(),s.setTransition(0),s.animating){const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});s.wrapperEl.dispatchEvent(e)}a.allowMomentumBounce=!1,!i.grabCursor||!0!==s.allowSlideNext&&!0!==s.allowSlidePrev||s.setGrabCursor(!0),s.emit("sliderFirstMove",d)}if((new Date).getTime(),!1!==i._loopSwapReset&&a.isMoved&&a.allowThresholdMove&&g!==s.touchesDirection&&w&&y&&Math.abs(h)>=1)return Object.assign(r,{startX:c,startY:p,currentX:c,currentY:p,startTranslate:a.currentTranslate}),a.loopSwapReset=!0,void(a.startTranslate=a.currentTranslate);s.emit("sliderMove",d),a.isMoved=!0,a.currentTranslate=h+a.startTranslate;let E=!0,S=i.resistanceRatio;if(i.touchReleaseOnEdges&&(S=0),h>0?(w&&y&&a.allowThresholdMove&&a.currentTranslate>(i.centeredSlides?s.minTranslate()-s.slidesSizesGrid[s.activeIndex+1]-("auto"!==i.slidesPerView&&s.slides.length-i.slidesPerView>=2?s.slidesSizesGrid[s.activeIndex+1]+s.params.spaceBetween:0)-s.params.spaceBetween:s.minTranslate())&&s.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),a.currentTranslate>s.minTranslate()&&(E=!1,i.resistance&&(a.currentTranslate=s.minTranslate()-1+(-s.minTranslate()+a.startTranslate+h)**S))):h<0&&(w&&y&&a.allowThresholdMove&&a.currentTranslate<(i.centeredSlides?s.maxTranslate()+s.slidesSizesGrid[s.slidesSizesGrid.length-1]+s.params.spaceBetween+("auto"!==i.slidesPerView&&s.slides.length-i.slidesPerView>=2?s.slidesSizesGrid[s.slidesSizesGrid.length-1]+s.params.spaceBetween:0):s.maxTranslate())&&s.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:s.slides.length-("auto"===i.slidesPerView?s.slidesPerViewDynamic():Math.ceil(parseFloat(i.slidesPerView,10)))}),a.currentTranslate<s.maxTranslate()&&(E=!1,i.resistance&&(a.currentTranslate=s.maxTranslate()+1-(s.maxTranslate()-a.startTranslate-h)**S))),E&&(d.preventedByNestedSwiper=!0),!s.allowSlideNext&&"next"===s.swipeDirection&&a.currentTranslate<a.startTranslate&&(a.currentTranslate=a.startTranslate),!s.allowSlidePrev&&"prev"===s.swipeDirection&&a.currentTranslate>a.startTranslate&&(a.currentTranslate=a.startTranslate),s.allowSlidePrev||s.allowSlideNext||(a.currentTranslate=a.startTranslate),i.threshold>0){if(!(Math.abs(h)>i.threshold||a.allowThresholdMove))return void(a.currentTranslate=a.startTranslate);if(!a.allowThresholdMove)return a.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,a.currentTranslate=a.startTranslate,void(r.diff=s.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY)}i.followFinger&&!i.cssMode&&((i.freeMode&&i.freeMode.enabled&&s.freeMode||i.watchSlidesProgress)&&(s.updateActiveIndex(),s.updateSlidesClasses()),i.freeMode&&i.freeMode.enabled&&s.freeMode&&s.freeMode.onTouchMove(),s.updateProgress(a.currentTranslate),s.setTranslate(a.currentTranslate))}function Q(e){const t=this,s=t.touchEventsData;let a,i=e;i.originalEvent&&(i=i.originalEvent);if("touchend"===i.type||"touchcancel"===i.type){if(a=[...i.changedTouches].find((e=>e.identifier===s.touchId)),!a||a.identifier!==s.touchId)return}else{if(null!==s.touchId)return;if(i.pointerId!==s.pointerId)return;a=i}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(i.type)){if(!(["pointercancel","contextmenu"].includes(i.type)&&(t.browser.isSafari||t.browser.isWebView)))return}s.pointerId=null,s.touchId=null;const{params:r,touches:l,rtlTranslate:n,slidesGrid:o,enabled:d}=t;if(!d)return;if(!r.simulateTouch&&"mouse"===i.pointerType)return;if(s.allowTouchCallbacks&&t.emit("touchEnd",i),s.allowTouchCallbacks=!1,!s.isTouched)return s.isMoved&&r.grabCursor&&t.setGrabCursor(!1),s.isMoved=!1,void(s.startMoving=!1);r.grabCursor&&s.isMoved&&s.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const c=b(),p=c-s.touchStartTime;if(t.allowClick){const e=i.path||i.composedPath&&i.composedPath();t.updateClickedSlide(e&&e[0]||i.target,e),t.emit("tap click",i),p<300&&c-s.lastClickTime<300&&t.emit("doubleTap doubleClick",i)}if(s.lastClickTime=b(),w((()=>{t.destroyed||(t.allowClick=!0)})),!s.isTouched||!s.isMoved||!t.swipeDirection||0===l.diff&&!s.loopSwapReset||s.currentTranslate===s.startTranslate&&!s.loopSwapReset)return s.isTouched=!1,s.isMoved=!1,void(s.startMoving=!1);let u;if(s.isTouched=!1,s.isMoved=!1,s.startMoving=!1,u=r.followFinger?n?t.translate:-t.translate:-s.currentTranslate,r.cssMode)return;if(r.freeMode&&r.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:u});const m=u>=-t.maxTranslate()&&!t.params.loop;let f=0,h=t.slidesSizesGrid[0];for(let w=0;w<o.length;w+=w<r.slidesPerGroupSkip?1:r.slidesPerGroup){const e=w<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;void 0!==o[w+e]?(m||u>=o[w]&&u<o[w+e])&&(f=w,h=o[w+e]-o[w]):(m||u>=o[w])&&(f=w,h=o[o.length-1]-o[o.length-2])}let v=null,g=null;r.rewind&&(t.isBeginning?g=r.virtual&&r.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(v=0));const y=(u-o[f])/h,E=f<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(p>r.longSwipesMs){if(!r.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(y>=r.longSwipesRatio?t.slideTo(r.rewind&&t.isEnd?v:f+E):t.slideTo(f)),"prev"===t.swipeDirection&&(y>1-r.longSwipesRatio?t.slideTo(f+E):null!==g&&y<0&&Math.abs(y)>r.longSwipesRatio?t.slideTo(g):t.slideTo(f))}else{if(!r.shortSwipes)return void t.slideTo(t.activeIndex);t.navigation&&(i.target===t.navigation.nextEl||i.target===t.navigation.prevEl)?i.target===t.navigation.nextEl?t.slideTo(f+E):t.slideTo(f):("next"===t.swipeDirection&&t.slideTo(null!==v?v:f+E),"prev"===t.swipeDirection&&t.slideTo(null!==g?g:f))}}function J(){const e=this,{params:t,el:s}=e;if(s&&0===s.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:a,allowSlidePrev:i,snapGrid:r}=e,l=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const n=l&&t.loop;!("auto"===t.slidesPerView||t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||n?e.params.loop&&!l?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout((()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()}),500)),e.allowSlidePrev=i,e.allowSlideNext=a,e.params.watchOverflow&&r!==e.snapGrid&&e.checkOverflow()}function ee(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function te(){const e=this,{wrapperEl:t,rtlTranslate:s,enabled:a}=e;if(!a)return;let i;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const r=e.maxTranslate()-e.minTranslate();i=0===r?0:(e.translate-e.minTranslate())/r,i!==e.progress&&e.updateProgress(s?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function se(e){const t=this;F(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}function ae(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const ie=(e,t)=>{const s=f(),{params:a,el:i,wrapperEl:r,device:l}=e,n=!!a.nested,o="on"===t?"addEventListener":"removeEventListener",d=t;i&&"string"!=typeof i&&(s[o]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:n}),i[o]("touchstart",e.onTouchStart,{passive:!1}),i[o]("pointerdown",e.onTouchStart,{passive:!1}),s[o]("touchmove",e.onTouchMove,{passive:!1,capture:n}),s[o]("pointermove",e.onTouchMove,{passive:!1,capture:n}),s[o]("touchend",e.onTouchEnd,{passive:!0}),s[o]("pointerup",e.onTouchEnd,{passive:!0}),s[o]("pointercancel",e.onTouchEnd,{passive:!0}),s[o]("touchcancel",e.onTouchEnd,{passive:!0}),s[o]("pointerout",e.onTouchEnd,{passive:!0}),s[o]("pointerleave",e.onTouchEnd,{passive:!0}),s[o]("contextmenu",e.onTouchEnd,{passive:!0}),(a.preventClicks||a.preventClicksPropagation)&&i[o]("click",e.onClick,!0),a.cssMode&&r[o]("scroll",e.onScroll),a.updateOnWindowResize?e[d](l.ios||l.android?"resize orientationchange observerUpdate":"resize observerUpdate",J,!0):e[d]("observerUpdate",J,!0),i[o]("load",e.onLoad,{capture:!0}))};const re=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var le={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function ne(e,t){return function(s){void 0===s&&(s={});const a=Object.keys(s)[0],i=s[a];"object"==typeof i&&null!==i?(!0===e[a]&&(e[a]={enabled:!0}),"navigation"===a&&e[a]&&e[a].enabled&&!e[a].prevEl&&!e[a].nextEl&&(e[a].auto=!0),["pagination","scrollbar"].indexOf(a)>=0&&e[a]&&e[a].enabled&&!e[a].el&&(e[a].auto=!0),a in e&&"enabled"in i?("object"!=typeof e[a]||"enabled"in e[a]||(e[a].enabled=!0),e[a]||(e[a]={enabled:!1}),S(t,s)):S(t,s)):S(t,s)}}const oe={eventsEmitter:{on(e,t,s){const a=this;if(!a.eventsListeners||a.destroyed)return a;if("function"!=typeof t)return a;const i=s?"unshift":"push";return e.split(" ").forEach((e=>{a.eventsListeners[e]||(a.eventsListeners[e]=[]),a.eventsListeners[e][i](t)})),a},once(e,t,s){const a=this;if(!a.eventsListeners||a.destroyed)return a;if("function"!=typeof t)return a;function i(){a.off(e,i),i.__emitterProxy&&delete i.__emitterProxy;for(var s=arguments.length,r=new Array(s),l=0;l<s;l++)r[l]=arguments[l];t.apply(a,r)}return i.__emitterProxy=t,a.on(e,i,s)},onAny(e,t){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof e)return s;const a=t?"unshift":"push";return s.eventsAnyListeners.indexOf(e)<0&&s.eventsAnyListeners[a](e),s},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const s=t.eventsAnyListeners.indexOf(e);return s>=0&&t.eventsAnyListeners.splice(s,1),t},off(e,t){const s=this;return!s.eventsListeners||s.destroyed?s:s.eventsListeners?(e.split(" ").forEach((e=>{void 0===t?s.eventsListeners[e]=[]:s.eventsListeners[e]&&s.eventsListeners[e].forEach(((a,i)=>{(a===t||a.__emitterProxy&&a.__emitterProxy===t)&&s.eventsListeners[e].splice(i,1)}))})),s):s},emit(){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsListeners)return e;let t,s,a;for(var i=arguments.length,r=new Array(i),l=0;l<i;l++)r[l]=arguments[l];"string"==typeof r[0]||Array.isArray(r[0])?(t=r[0],s=r.slice(1,r.length),a=e):(t=r[0].events,s=r[0].data,a=r[0].context||e),s.unshift(a);return(Array.isArray(t)?t:t.split(" ")).forEach((t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach((e=>{e.apply(a,[t,...s])})),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach((e=>{e.apply(a,s)}))})),e}},update:{updateSize:function(){const e=this;let t,s;const a=e.el;t=void 0!==e.params.width&&null!==e.params.width?e.params.width:a.clientWidth,s=void 0!==e.params.height&&null!==e.params.height?e.params.height:a.clientHeight,0===t&&e.isHorizontal()||0===s&&e.isVertical()||(t=t-parseInt(z(a,"padding-left")||0,10)-parseInt(z(a,"padding-right")||0,10),s=s-parseInt(z(a,"padding-top")||0,10)-parseInt(z(a,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(s)&&(s=0),Object.assign(e,{width:t,height:s,size:e.isHorizontal()?t:s}))},updateSlides:function(){const e=this;function t(t,s){return parseFloat(t.getPropertyValue(e.getDirectionLabel(s))||0)}const s=e.params,{wrapperEl:a,slidesEl:i,size:r,rtlTranslate:l,wrongRTL:n}=e,o=e.virtual&&s.virtual.enabled,d=o?e.virtual.slides.length:e.slides.length,c=C(i,`.${e.params.slideClass}, swiper-slide`),p=o?e.virtual.slides.length:c.length;let u=[];const m=[],f=[];let h=s.slidesOffsetBefore;"function"==typeof h&&(h=s.slidesOffsetBefore.call(e));let v=s.slidesOffsetAfter;"function"==typeof v&&(v=s.slidesOffsetAfter.call(e));const g=e.snapGrid.length,w=e.slidesGrid.length;let b=s.spaceBetween,y=-h,E=0,S=0;if(void 0===r)return;"string"==typeof b&&b.indexOf("%")>=0?b=parseFloat(b.replace("%",""))/100*r:"string"==typeof b&&(b=parseFloat(b)),e.virtualSize=-b,c.forEach((e=>{l?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""})),s.centeredSlides&&s.cssMode&&(x(a,"--swiper-centered-offset-before",""),x(a,"--swiper-centered-offset-after",""));const T=s.grid&&s.grid.rows>1&&e.grid;let M;T?e.grid.initSlides(c):e.grid&&e.grid.unsetSlides();const P="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter((e=>void 0!==s.breakpoints[e].slidesPerView)).length>0;for(let x=0;x<p;x+=1){let a;if(M=0,c[x]&&(a=c[x]),T&&e.grid.updateSlide(x,a,c),!c[x]||"none"!==z(a,"display")){if("auto"===s.slidesPerView){P&&(c[x].style[e.getDirectionLabel("width")]="");const i=getComputedStyle(a),r=a.style.transform,l=a.style.webkitTransform;if(r&&(a.style.transform="none"),l&&(a.style.webkitTransform="none"),s.roundLengths)M=e.isHorizontal()?$(a,"width"):$(a,"height");else{const e=t(i,"width"),s=t(i,"padding-left"),r=t(i,"padding-right"),l=t(i,"margin-left"),n=t(i,"margin-right"),o=i.getPropertyValue("box-sizing");if(o&&"border-box"===o)M=e+l+n;else{const{clientWidth:t,offsetWidth:i}=a;M=e+s+r+l+n+(i-t)}}r&&(a.style.transform=r),l&&(a.style.webkitTransform=l),s.roundLengths&&(M=Math.floor(M))}else M=(r-(s.slidesPerView-1)*b)/s.slidesPerView,s.roundLengths&&(M=Math.floor(M)),c[x]&&(c[x].style[e.getDirectionLabel("width")]=`${M}px`);c[x]&&(c[x].swiperSlideSize=M),f.push(M),s.centeredSlides?(y=y+M/2+E/2+b,0===E&&0!==x&&(y=y-r/2-b),0===x&&(y=y-r/2-b),Math.abs(y)<.001&&(y=0),s.roundLengths&&(y=Math.floor(y)),S%s.slidesPerGroup==0&&u.push(y),m.push(y)):(s.roundLengths&&(y=Math.floor(y)),(S-Math.min(e.params.slidesPerGroupSkip,S))%e.params.slidesPerGroup==0&&u.push(y),m.push(y),y=y+M+b),e.virtualSize+=M+b,E=M,S+=1}}if(e.virtualSize=Math.max(e.virtualSize,r)+v,l&&n&&("slide"===s.effect||"coverflow"===s.effect)&&(a.style.width=`${e.virtualSize+b}px`),s.setWrapperSize&&(a.style[e.getDirectionLabel("width")]=`${e.virtualSize+b}px`),T&&e.grid.updateWrapperSize(M,u),!s.centeredSlides){const t=[];for(let a=0;a<u.length;a+=1){let i=u[a];s.roundLengths&&(i=Math.floor(i)),u[a]<=e.virtualSize-r&&t.push(i)}u=t,Math.floor(e.virtualSize-r)-Math.floor(u[u.length-1])>1&&u.push(e.virtualSize-r)}if(o&&s.loop){const t=f[0]+b;if(s.slidesPerGroup>1){const a=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/s.slidesPerGroup),i=t*s.slidesPerGroup;for(let e=0;e<a;e+=1)u.push(u[u.length-1]+i)}for(let a=0;a<e.virtual.slidesBefore+e.virtual.slidesAfter;a+=1)1===s.slidesPerGroup&&u.push(u[u.length-1]+t),m.push(m[m.length-1]+t),e.virtualSize+=t}if(0===u.length&&(u=[0]),0!==b){const t=e.isHorizontal()&&l?"marginLeft":e.getDirectionLabel("marginRight");c.filter(((e,t)=>!(s.cssMode&&!s.loop)||t!==c.length-1)).forEach((e=>{e.style[t]=`${b}px`}))}if(s.centeredSlides&&s.centeredSlidesBounds){let e=0;f.forEach((t=>{e+=t+(b||0)})),e-=b;const t=e>r?e-r:0;u=u.map((e=>e<=0?-h:e>t?t+v:e))}if(s.centerInsufficientSlides){let e=0;f.forEach((t=>{e+=t+(b||0)})),e-=b;const t=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(e+t<r){const s=(r-e-t)/2;u.forEach(((e,t)=>{u[t]=e-s})),m.forEach(((e,t)=>{m[t]=e+s}))}}if(Object.assign(e,{slides:c,snapGrid:u,slidesGrid:m,slidesSizesGrid:f}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){x(a,"--swiper-centered-offset-before",-u[0]+"px"),x(a,"--swiper-centered-offset-after",e.size/2-f[f.length-1]/2+"px");const t=-e.snapGrid[0],s=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((e=>e+t)),e.slidesGrid=e.slidesGrid.map((e=>e+s))}if(p!==d&&e.emit("slidesLengthChange"),u.length!==g&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),m.length!==w&&e.emit("slidesGridLengthChange"),s.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!(o||s.cssMode||"slide"!==s.effect&&"fade"!==s.effect)){const t=`${s.containerModifierClass}backface-hidden`,a=e.el.classList.contains(t);p<=s.maxBackfaceHiddenSlides?a||e.el.classList.add(t):a&&e.el.classList.remove(t)}},updateAutoHeight:function(e){const t=this,s=[],a=t.virtual&&t.params.virtual.enabled;let i,r=0;"number"==typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const l=e=>a?t.slides[t.getSlideIndexByData(e)]:t.slides[e];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach((e=>{s.push(e)}));else for(i=0;i<Math.ceil(t.params.slidesPerView);i+=1){const e=t.activeIndex+i;if(e>t.slides.length&&!a)break;s.push(l(e))}else s.push(l(t.activeIndex));for(i=0;i<s.length;i+=1)if(void 0!==s[i]){const e=s[i].offsetHeight;r=e>r?e:r}(r||0===r)&&(t.wrapperEl.style.height=`${r}px`)},updateSlidesOffset:function(){const e=this,t=e.slides,s=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let a=0;a<t.length;a+=1)t[a].swiperSlideOffset=(e.isHorizontal()?t[a].offsetLeft:t[a].offsetTop)-s-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);const t=this,s=t.params,{slides:a,rtlTranslate:i,snapGrid:r}=t;if(0===a.length)return;void 0===a[0].swiperSlideOffset&&t.updateSlidesOffset();let l=-e;i&&(l=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let n=s.spaceBetween;"string"==typeof n&&n.indexOf("%")>=0?n=parseFloat(n.replace("%",""))/100*t.size:"string"==typeof n&&(n=parseFloat(n));for(let o=0;o<a.length;o+=1){const e=a[o];let d=e.swiperSlideOffset;s.cssMode&&s.centeredSlides&&(d-=a[0].swiperSlideOffset);const c=(l+(s.centeredSlides?t.minTranslate():0)-d)/(e.swiperSlideSize+n),p=(l-r[0]+(s.centeredSlides?t.minTranslate():0)-d)/(e.swiperSlideSize+n),u=-(l-d),m=u+t.slidesSizesGrid[o],f=u>=0&&u<=t.size-t.slidesSizesGrid[o],h=u>=0&&u<t.size-1||m>1&&m<=t.size||u<=0&&m>=t.size;h&&(t.visibleSlides.push(e),t.visibleSlidesIndexes.push(o)),X(e,h,s.slideVisibleClass),X(e,f,s.slideFullyVisibleClass),e.progress=i?-c:c,e.originalProgress=i?-p:p}},updateProgress:function(e){const t=this;if(void 0===e){const s=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*s||0}const s=t.params,a=t.maxTranslate()-t.minTranslate();let{progress:i,isBeginning:r,isEnd:l,progressLoop:n}=t;const o=r,d=l;if(0===a)i=0,r=!0,l=!0;else{i=(e-t.minTranslate())/a;const s=Math.abs(e-t.minTranslate())<1,n=Math.abs(e-t.maxTranslate())<1;r=s||i<=0,l=n||i>=1,s&&(i=0),n&&(i=1)}if(s.loop){const s=t.getSlideIndexByData(0),a=t.getSlideIndexByData(t.slides.length-1),i=t.slidesGrid[s],r=t.slidesGrid[a],l=t.slidesGrid[t.slidesGrid.length-1],o=Math.abs(e);n=o>=i?(o-i)/l:(o+l-r)/l,n>1&&(n-=1)}Object.assign(t,{progress:i,progressLoop:n,isBeginning:r,isEnd:l}),(s.watchSlidesProgress||s.centeredSlides&&s.autoHeight)&&t.updateSlidesProgress(e),r&&!o&&t.emit("reachBeginning toEdge"),l&&!d&&t.emit("reachEnd toEdge"),(o&&!r||d&&!l)&&t.emit("fromEdge"),t.emit("progress",i)},updateSlidesClasses:function(){const e=this,{slides:t,params:s,slidesEl:a,activeIndex:i}=e,r=e.virtual&&s.virtual.enabled,l=e.grid&&s.grid&&s.grid.rows>1,n=e=>C(a,`.${s.slideClass}${e}, swiper-slide${e}`)[0];let o,d,c;if(r)if(s.loop){let t=i-e.virtual.slidesBefore;t<0&&(t=e.virtual.slides.length+t),t>=e.virtual.slides.length&&(t-=e.virtual.slides.length),o=n(`[data-swiper-slide-index="${t}"]`)}else o=n(`[data-swiper-slide-index="${i}"]`);else l?(o=t.find((e=>e.column===i)),c=t.find((e=>e.column===i+1)),d=t.find((e=>e.column===i-1))):o=t[i];o&&(l||(c=function(e,t){const s=[];for(;e.nextElementSibling;){const a=e.nextElementSibling;t?a.matches(t)&&s.push(a):s.push(a),e=a}return s}(o,`.${s.slideClass}, swiper-slide`)[0],s.loop&&!c&&(c=t[0]),d=function(e,t){const s=[];for(;e.previousElementSibling;){const a=e.previousElementSibling;t?a.matches(t)&&s.push(a):s.push(a),e=a}return s}(o,`.${s.slideClass}, swiper-slide`)[0],s.loop&&0===!d&&(d=t[t.length-1]))),t.forEach((e=>{Y(e,e===o,s.slideActiveClass),Y(e,e===c,s.slideNextClass),Y(e,e===d,s.slidePrevClass)})),e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,s=t.rtlTranslate?t.translate:-t.translate,{snapGrid:a,params:i,activeIndex:r,realIndex:l,snapIndex:n}=t;let o,d=e;const c=e=>{let s=e-t.virtual.slidesBefore;return s<0&&(s=t.virtual.slides.length+s),s>=t.virtual.slides.length&&(s-=t.virtual.slides.length),s};if(void 0===d&&(d=function(e){const{slidesGrid:t,params:s}=e,a=e.rtlTranslate?e.translate:-e.translate;let i;for(let r=0;r<t.length;r+=1)void 0!==t[r+1]?a>=t[r]&&a<t[r+1]-(t[r+1]-t[r])/2?i=r:a>=t[r]&&a<t[r+1]&&(i=r+1):a>=t[r]&&(i=r);return s.normalizeSlideIndex&&(i<0||void 0===i)&&(i=0),i}(t)),a.indexOf(s)>=0)o=a.indexOf(s);else{const e=Math.min(i.slidesPerGroupSkip,d);o=e+Math.floor((d-e)/i.slidesPerGroup)}if(o>=a.length&&(o=a.length-1),d===r&&!t.params.loop)return void(o!==n&&(t.snapIndex=o,t.emit("snapIndexChange")));if(d===r&&t.params.loop&&t.virtual&&t.params.virtual.enabled)return void(t.realIndex=c(d));const p=t.grid&&i.grid&&i.grid.rows>1;let u;if(t.virtual&&i.virtual.enabled&&i.loop)u=c(d);else if(p){const e=t.slides.find((e=>e.column===d));let s=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(s)&&(s=Math.max(t.slides.indexOf(e),0)),u=Math.floor(s/i.grid.rows)}else if(t.slides[d]){const e=t.slides[d].getAttribute("data-swiper-slide-index");u=e?parseInt(e,10):d}else u=d;Object.assign(t,{previousSnapIndex:n,snapIndex:o,previousRealIndex:l,realIndex:u,previousIndex:r,activeIndex:d}),t.initialized&&V(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(l!==u&&t.emit("realIndexChange"),t.emit("slideChange"))},updateClickedSlide:function(e,t){const s=this,a=s.params;let i=e.closest(`.${a.slideClass}, swiper-slide`);!i&&s.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach((e=>{!i&&e.matches&&e.matches(`.${a.slideClass}, swiper-slide`)&&(i=e)}));let r,l=!1;if(i)for(let n=0;n<s.slides.length;n+=1)if(s.slides[n]===i){l=!0,r=n;break}if(!i||!l)return s.clickedSlide=void 0,void(s.clickedIndex=void 0);s.clickedSlide=i,s.virtual&&s.params.virtual.enabled?s.clickedIndex=parseInt(i.getAttribute("data-swiper-slide-index"),10):s.clickedIndex=r,a.slideToClickedSlide&&void 0!==s.clickedIndex&&s.clickedIndex!==s.activeIndex&&s.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");const{params:t,rtlTranslate:s,translate:a,wrapperEl:i}=this;if(t.virtualTranslate)return s?-a:a;if(t.cssMode)return a;let r=y(i,e);return r+=this.cssOverflowAdjustment(),s&&(r=-r),r||0},setTranslate:function(e,t){const s=this,{rtlTranslate:a,params:i,wrapperEl:r,progress:l}=s;let n,o=0,d=0;s.isHorizontal()?o=a?-e:e:d=e,i.roundLengths&&(o=Math.floor(o),d=Math.floor(d)),s.previousTranslate=s.translate,s.translate=s.isHorizontal()?o:d,i.cssMode?r[s.isHorizontal()?"scrollLeft":"scrollTop"]=s.isHorizontal()?-o:-d:i.virtualTranslate||(s.isHorizontal()?o-=s.cssOverflowAdjustment():d-=s.cssOverflowAdjustment(),r.style.transform=`translate3d(${o}px, ${d}px, 0px)`);const c=s.maxTranslate()-s.minTranslate();n=0===c?0:(e-s.minTranslate())/c,n!==l&&s.updateProgress(e),s.emit("setTranslate",s.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,s,a,i){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===s&&(s=!0),void 0===a&&(a=!0);const r=this,{params:l,wrapperEl:n}=r;if(r.animating&&l.preventInteractionOnTransition)return!1;const o=r.minTranslate(),d=r.maxTranslate();let c;if(c=a&&e>o?o:a&&e<d?d:e,r.updateProgress(c),l.cssMode){const e=r.isHorizontal();if(0===t)n[e?"scrollLeft":"scrollTop"]=-c;else{if(!r.support.smoothScroll)return T({swiper:r,targetPosition:-c,side:e?"left":"top"}),!0;n.scrollTo({[e?"left":"top"]:-c,behavior:"smooth"})}return!0}return 0===t?(r.setTransition(0),r.setTranslate(c),s&&(r.emit("beforeTransitionStart",t,i),r.emit("transitionEnd"))):(r.setTransition(t),r.setTranslate(c),s&&(r.emit("beforeTransitionStart",t,i),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,s&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){const s=this;s.params.cssMode||(s.wrapperEl.style.transitionDuration=`${e}ms`,s.wrapperEl.style.transitionDelay=0===e?"0ms":""),s.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);const s=this,{params:a}=s;a.cssMode||(a.autoHeight&&s.updateAutoHeight(),W({swiper:s,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);const s=this,{params:a}=s;s.animating=!1,a.cssMode||(s.setTransition(0),W({swiper:s,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,s,a,i){void 0===e&&(e=0),void 0===s&&(s=!0),"string"==typeof e&&(e=parseInt(e,10));const r=this;let l=e;l<0&&(l=0);const{params:n,snapGrid:o,slidesGrid:d,previousIndex:c,activeIndex:p,rtlTranslate:u,wrapperEl:m,enabled:f}=r;if(!f&&!a&&!i||r.destroyed||r.animating&&n.preventInteractionOnTransition)return!1;void 0===t&&(t=r.params.speed);const h=Math.min(r.params.slidesPerGroupSkip,l);let v=h+Math.floor((l-h)/r.params.slidesPerGroup);v>=o.length&&(v=o.length-1);const g=-o[v];if(n.normalizeSlideIndex)for(let E=0;E<d.length;E+=1){const e=-Math.floor(100*g),t=Math.floor(100*d[E]),s=Math.floor(100*d[E+1]);void 0!==d[E+1]?e>=t&&e<s-(s-t)/2?l=E:e>=t&&e<s&&(l=E+1):e>=t&&(l=E)}if(r.initialized&&l!==p){if(!r.allowSlideNext&&(u?g>r.translate&&g>r.minTranslate():g<r.translate&&g<r.minTranslate()))return!1;if(!r.allowSlidePrev&&g>r.translate&&g>r.maxTranslate()&&(p||0)!==l)return!1}let w;l!==(c||0)&&s&&r.emit("beforeSlideChangeStart"),r.updateProgress(g),w=l>p?"next":l<p?"prev":"reset";const b=r.virtual&&r.params.virtual.enabled;if(!(b&&i)&&(u&&-g===r.translate||!u&&g===r.translate))return r.updateActiveIndex(l),n.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==n.effect&&r.setTranslate(g),"reset"!==w&&(r.transitionStart(s,w),r.transitionEnd(s,w)),!1;if(n.cssMode){const e=r.isHorizontal(),s=u?g:-g;if(0===t)b&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),b&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame((()=>{m[e?"scrollLeft":"scrollTop"]=s}))):m[e?"scrollLeft":"scrollTop"]=s,b&&requestAnimationFrame((()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1}));else{if(!r.support.smoothScroll)return T({swiper:r,targetPosition:s,side:e?"left":"top"}),!0;m.scrollTo({[e?"left":"top"]:s,behavior:"smooth"})}return!0}const y=j().isSafari;return b&&!i&&y&&r.isElement&&r.virtual.update(!1,!1,l),r.setTransition(t),r.setTranslate(g),r.updateActiveIndex(l),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,a),r.transitionStart(s,w),0===t?r.transitionEnd(s,w):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(s,w))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,s,a){if(void 0===e&&(e=0),void 0===s&&(s=!0),"string"==typeof e){e=parseInt(e,10)}const i=this;if(i.destroyed)return;void 0===t&&(t=i.params.speed);const r=i.grid&&i.params.grid&&i.params.grid.rows>1;let l=e;if(i.params.loop)if(i.virtual&&i.params.virtual.enabled)l+=i.virtual.slidesBefore;else{let e;if(r){const t=l*i.params.grid.rows;e=i.slides.find((e=>1*e.getAttribute("data-swiper-slide-index")===t)).column}else e=i.getSlideIndexByData(l);const t=r?Math.ceil(i.slides.length/i.params.grid.rows):i.slides.length,{centeredSlides:s}=i.params;let n=i.params.slidesPerView;"auto"===n?n=i.slidesPerViewDynamic():(n=Math.ceil(parseFloat(i.params.slidesPerView,10)),s&&n%2==0&&(n+=1));let o=t-e<n;if(s&&(o=o||e<Math.ceil(n/2)),a&&s&&"auto"!==i.params.slidesPerView&&!r&&(o=!1),o){const a=s?e<i.activeIndex?"prev":"next":e-i.activeIndex-1<i.params.slidesPerView?"next":"prev";i.loopFix({direction:a,slideTo:!0,activeSlideIndex:"next"===a?e+1:e-t+1,slideRealIndex:"next"===a?i.realIndex:void 0})}if(r){const e=l*i.params.grid.rows;l=i.slides.find((t=>1*t.getAttribute("data-swiper-slide-index")===e)).column}else l=i.getSlideIndexByData(l)}return requestAnimationFrame((()=>{i.slideTo(l,t,s,a)})),i},slideNext:function(e,t,s){void 0===t&&(t=!0);const a=this,{enabled:i,params:r,animating:l}=a;if(!i||a.destroyed)return a;void 0===e&&(e=a.params.speed);let n=r.slidesPerGroup;"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(n=Math.max(a.slidesPerViewDynamic("current",!0),1));const o=a.activeIndex<r.slidesPerGroupSkip?1:n,d=a.virtual&&r.virtual.enabled;if(r.loop){if(l&&!d&&r.loopPreventsSliding)return!1;if(a.loopFix({direction:"next"}),a._clientLeft=a.wrapperEl.clientLeft,a.activeIndex===a.slides.length-1&&r.cssMode)return requestAnimationFrame((()=>{a.slideTo(a.activeIndex+o,e,t,s)})),!0}return r.rewind&&a.isEnd?a.slideTo(0,e,t,s):a.slideTo(a.activeIndex+o,e,t,s)},slidePrev:function(e,t,s){void 0===t&&(t=!0);const a=this,{params:i,snapGrid:r,slidesGrid:l,rtlTranslate:n,enabled:o,animating:d}=a;if(!o||a.destroyed)return a;void 0===e&&(e=a.params.speed);const c=a.virtual&&i.virtual.enabled;if(i.loop){if(d&&!c&&i.loopPreventsSliding)return!1;a.loopFix({direction:"prev"}),a._clientLeft=a.wrapperEl.clientLeft}function p(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const u=p(n?a.translate:-a.translate),m=r.map((e=>p(e))),f=i.freeMode&&i.freeMode.enabled;let h=r[m.indexOf(u)-1];if(void 0===h&&(i.cssMode||f)){let e;r.forEach(((t,s)=>{u>=t&&(e=s)})),void 0!==e&&(h=f?r[e]:r[e>0?e-1:e])}let v=0;if(void 0!==h&&(v=l.indexOf(h),v<0&&(v=a.activeIndex-1),"auto"===i.slidesPerView&&1===i.slidesPerGroup&&i.slidesPerGroupAuto&&(v=v-a.slidesPerViewDynamic("previous",!0)+1,v=Math.max(v,0))),i.rewind&&a.isBeginning){const i=a.params.virtual&&a.params.virtual.enabled&&a.virtual?a.virtual.slides.length-1:a.slides.length-1;return a.slideTo(i,e,t,s)}return i.loop&&0===a.activeIndex&&i.cssMode?(requestAnimationFrame((()=>{a.slideTo(v,e,t,s)})),!0):a.slideTo(v,e,t,s)},slideReset:function(e,t,s){void 0===t&&(t=!0);const a=this;if(!a.destroyed)return void 0===e&&(e=a.params.speed),a.slideTo(a.activeIndex,e,t,s)},slideToClosest:function(e,t,s,a){void 0===t&&(t=!0),void 0===a&&(a=.5);const i=this;if(i.destroyed)return;void 0===e&&(e=i.params.speed);let r=i.activeIndex;const l=Math.min(i.params.slidesPerGroupSkip,r),n=l+Math.floor((r-l)/i.params.slidesPerGroup),o=i.rtlTranslate?i.translate:-i.translate;if(o>=i.snapGrid[n]){const e=i.snapGrid[n];o-e>(i.snapGrid[n+1]-e)*a&&(r+=i.params.slidesPerGroup)}else{const e=i.snapGrid[n-1];o-e<=(i.snapGrid[n]-e)*a&&(r-=i.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,i.slidesGrid.length-1),i.slideTo(r,e,t,s)},slideToClickedSlide:function(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:s}=e,a="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let i,r=e.clickedIndex;const l=e.isElement?"swiper-slide":`.${t.slideClass}`;if(t.loop){if(e.animating)return;i=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?r<e.loopedSlides-a/2||r>e.slides.length-e.loopedSlides+a/2?(e.loopFix(),r=e.getSlideIndex(C(s,`${l}[data-swiper-slide-index="${i}"]`)[0]),w((()=>{e.slideTo(r)}))):e.slideTo(r):r>e.slides.length-a?(e.loopFix(),r=e.getSlideIndex(C(s,`${l}[data-swiper-slide-index="${i}"]`)[0]),w((()=>{e.slideTo(r)}))):e.slideTo(r)}else e.slideTo(r)}},loop:{loopCreate:function(e){const t=this,{params:s,slidesEl:a}=t;if(!s.loop||t.virtual&&t.params.virtual.enabled)return;const i=()=>{C(a,`.${s.slideClass}, swiper-slide`).forEach(((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}))},r=t.grid&&s.grid&&s.grid.rows>1,l=s.slidesPerGroup*(r?s.grid.rows:1),n=t.slides.length%l!=0,o=r&&t.slides.length%s.grid.rows!=0,d=e=>{for(let a=0;a<e;a+=1){const e=t.isElement?L("swiper-slide",[s.slideBlankClass]):L("div",[s.slideClass,s.slideBlankClass]);t.slidesEl.append(e)}};if(n){if(s.loopAddBlankSlides){d(l-t.slides.length%l),t.recalcSlides(),t.updateSlides()}else P();i()}else if(o){if(s.loopAddBlankSlides){d(s.grid.rows-t.slides.length%s.grid.rows),t.recalcSlides(),t.updateSlides()}else P();i()}else i();t.loopFix({slideRealIndex:e,direction:s.centeredSlides?void 0:"next"})},loopFix:function(e){let{slideRealIndex:t,slideTo:s=!0,direction:a,setTranslate:i,activeSlideIndex:r,byController:l,byMousewheel:n}=void 0===e?{}:e;const o=this;if(!o.params.loop)return;o.emit("beforeLoopFix");const{slides:d,allowSlidePrev:c,allowSlideNext:p,slidesEl:u,params:m}=o,{centeredSlides:f}=m;if(o.allowSlidePrev=!0,o.allowSlideNext=!0,o.virtual&&m.virtual.enabled)return s&&(m.centeredSlides||0!==o.snapIndex?m.centeredSlides&&o.snapIndex<m.slidesPerView?o.slideTo(o.virtual.slides.length+o.snapIndex,0,!1,!0):o.snapIndex===o.snapGrid.length-1&&o.slideTo(o.virtual.slidesBefore,0,!1,!0):o.slideTo(o.virtual.slides.length,0,!1,!0)),o.allowSlidePrev=c,o.allowSlideNext=p,void o.emit("loopFix");let h=m.slidesPerView;"auto"===h?h=o.slidesPerViewDynamic():(h=Math.ceil(parseFloat(m.slidesPerView,10)),f&&h%2==0&&(h+=1));const v=m.slidesPerGroupAuto?h:m.slidesPerGroup;let g=v;g%v!=0&&(g+=v-g%v),g+=m.loopAdditionalSlides,o.loopedSlides=g;const w=o.grid&&m.grid&&m.grid.rows>1;(d.length<h+g||w&&"row"===m.grid.fill)&&P();const b=[],y=[];let E=o.activeIndex;void 0===r?r=o.getSlideIndex(d.find((e=>e.classList.contains(m.slideActiveClass)))):E=r;const S="next"===a||!a,x="prev"===a||!a;let T=0,M=0;const C=w?Math.ceil(d.length/m.grid.rows):d.length,L=(w?d[r].column:r)+(f&&void 0===i?-h/2+.5:0);if(L<g){T=Math.max(g-L,v);for(let e=0;e<g-L;e+=1){const t=e-Math.floor(e/C)*C;if(w){const e=C-t-1;for(let t=d.length-1;t>=0;t-=1)d[t].column===e&&b.push(t)}else b.push(C-t-1)}}else if(L+h>C-g){M=Math.max(L-(C-2*g),v);for(let e=0;e<M;e+=1){const t=e-Math.floor(e/C)*C;w?d.forEach(((e,s)=>{e.column===t&&y.push(s)})):y.push(t)}}if(o.__preventObserver__=!0,requestAnimationFrame((()=>{o.__preventObserver__=!1})),x&&b.forEach((e=>{d[e].swiperLoopMoveDOM=!0,u.prepend(d[e]),d[e].swiperLoopMoveDOM=!1})),S&&y.forEach((e=>{d[e].swiperLoopMoveDOM=!0,u.append(d[e]),d[e].swiperLoopMoveDOM=!1})),o.recalcSlides(),"auto"===m.slidesPerView?o.updateSlides():w&&(b.length>0&&x||y.length>0&&S)&&o.slides.forEach(((e,t)=>{o.grid.updateSlide(t,e,o.slides)})),m.watchSlidesProgress&&o.updateSlidesOffset(),s)if(b.length>0&&x){if(void 0===t){const e=o.slidesGrid[E],t=o.slidesGrid[E+T]-e;n?o.setTranslate(o.translate-t):(o.slideTo(E+Math.ceil(T),0,!1,!0),i&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-t,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-t))}else if(i){const e=w?b.length/m.grid.rows:b.length;o.slideTo(o.activeIndex+e,0,!1,!0),o.touchEventsData.currentTranslate=o.translate}}else if(y.length>0&&S)if(void 0===t){const e=o.slidesGrid[E],t=o.slidesGrid[E-M]-e;n?o.setTranslate(o.translate-t):(o.slideTo(E-M,0,!1,!0),i&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-t,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-t))}else{const e=w?y.length/m.grid.rows:y.length;o.slideTo(o.activeIndex-e,0,!1,!0)}if(o.allowSlidePrev=c,o.allowSlideNext=p,o.controller&&o.controller.control&&!l){const e={slideRealIndex:t,direction:a,setTranslate:i,activeSlideIndex:r,byController:!0};Array.isArray(o.controller.control)?o.controller.control.forEach((t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===m.slidesPerView&&s})})):o.controller.control instanceof o.constructor&&o.controller.control.params.loop&&o.controller.control.loopFix({...e,slideTo:o.controller.control.params.slidesPerView===m.slidesPerView&&s})}o.emit("loopFix")},loopDestroy:function(){const e=this,{params:t,slidesEl:s}=e;if(!t.loop||!s||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const a=[];e.slides.forEach((e=>{const t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;a[t]=e})),e.slides.forEach((e=>{e.removeAttribute("data-swiper-slide-index")})),a.forEach((e=>{s.append(e)})),e.recalcSlides(),e.slideTo(e.realIndex,0)}},grabCursor:{setGrabCursor:function(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const s="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),s.style.cursor="move",s.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame((()=>{t.__preventObserver__=!1}))},unsetGrabCursor:function(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame((()=>{e.__preventObserver__=!1})))}},events:{attachEvents:function(){const e=this,{params:t}=e;e.onTouchStart=K.bind(e),e.onTouchMove=Z.bind(e),e.onTouchEnd=Q.bind(e),e.onDocumentTouchStart=ae.bind(e),t.cssMode&&(e.onScroll=te.bind(e)),e.onClick=ee.bind(e),e.onLoad=se.bind(e),ie(e,"on")},detachEvents:function(){ie(this,"off")}},breakpoints:{setBreakpoint:function(){const e=this,{realIndex:t,initialized:s,params:a,el:i}=e,r=a.breakpoints;if(!r||r&&0===Object.keys(r).length)return;const l=f(),n="window"!==a.breakpointsBase&&a.breakpointsBase?"container":a.breakpointsBase,o=["window","container"].includes(a.breakpointsBase)||!a.breakpointsBase?e.el:l.querySelector(a.breakpointsBase),d=e.getBreakpoint(r,n,o);if(!d||e.currentBreakpoint===d)return;const c=(d in r?r[d]:void 0)||e.originalParams,p=re(e,a),u=re(e,c),m=e.params.grabCursor,h=c.grabCursor,v=a.enabled;p&&!u?(i.classList.remove(`${a.containerModifierClass}grid`,`${a.containerModifierClass}grid-column`),e.emitContainerClasses()):!p&&u&&(i.classList.add(`${a.containerModifierClass}grid`),(c.grid.fill&&"column"===c.grid.fill||!c.grid.fill&&"column"===a.grid.fill)&&i.classList.add(`${a.containerModifierClass}grid-column`),e.emitContainerClasses()),m&&!h?e.unsetGrabCursor():!m&&h&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach((t=>{if(void 0===c[t])return;const s=a[t]&&a[t].enabled,i=c[t]&&c[t].enabled;s&&!i&&e[t].disable(),!s&&i&&e[t].enable()}));const g=c.direction&&c.direction!==a.direction,w=a.loop&&(c.slidesPerView!==a.slidesPerView||g),b=a.loop;g&&s&&e.changeDirection(),S(e.params,c);const y=e.params.enabled,E=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),v&&!y?e.disable():!v&&y&&e.enable(),e.currentBreakpoint=d,e.emit("_beforeBreakpoint",c),s&&(w?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!b&&E?(e.loopCreate(t),e.updateSlides()):b&&!E&&e.loopDestroy()),e.emit("breakpoint",c)},getBreakpoint:function(e,t,s){if(void 0===t&&(t="window"),!e||"container"===t&&!s)return;let a=!1;const i=v(),r="window"===t?i.innerHeight:s.clientHeight,l=Object.keys(e).map((e=>{if("string"==typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:r*t,point:e}}return{value:e,point:e}}));l.sort(((e,t)=>parseInt(e.value,10)-parseInt(t.value,10)));for(let n=0;n<l.length;n+=1){const{point:e,value:r}=l[n];"window"===t?i.matchMedia(`(min-width: ${r}px)`).matches&&(a=e):r<=s.clientWidth&&(a=e)}return a||"max"}},checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:s}=e,{slidesOffsetBefore:a}=s;if(a){const t=e.slides.length-1,s=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*a;e.isLocked=e.size>s}else e.isLocked=1===e.snapGrid.length;!0===s.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===s.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){const e=this,{classNames:t,params:s,rtl:a,el:i,device:r}=e,l=function(e,t){const s=[];return e.forEach((e=>{"object"==typeof e?Object.keys(e).forEach((a=>{e[a]&&s.push(t+a)})):"string"==typeof e&&s.push(t+e)})),s}(["initialized",s.direction,{"free-mode":e.params.freeMode&&s.freeMode.enabled},{autoheight:s.autoHeight},{rtl:a},{grid:s.grid&&s.grid.rows>1},{"grid-column":s.grid&&s.grid.rows>1&&"column"===s.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":s.cssMode},{centered:s.cssMode&&s.centeredSlides},{"watch-progress":s.watchSlidesProgress}],s.containerModifierClass);t.push(...l),i.classList.add(...t),e.emitContainerClasses()},removeClasses:function(){const{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},de={};let ce=class e{constructor(){let t,s;for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];1===i.length&&i[0].constructor&&"Object"===Object.prototype.toString.call(i[0]).slice(8,-1)?s=i[0]:[t,s]=i,s||(s={}),s=S({},s),t&&!s.el&&(s.el=t);const l=f();if(s.el&&"string"==typeof s.el&&l.querySelectorAll(s.el).length>1){const t=[];return l.querySelectorAll(s.el).forEach((a=>{const i=S({},s,{el:a});t.push(new e(i))})),t}const n=this;n.__swiper__=!0,n.support=H(),n.device=R({userAgent:s.userAgent}),n.browser=j(),n.eventsListeners={},n.eventsAnyListeners=[],n.modules=[...n.__modules__],s.modules&&Array.isArray(s.modules)&&n.modules.push(...s.modules);const o={};n.modules.forEach((e=>{e({params:s,swiper:n,extendParams:ne(s,o),on:n.on.bind(n),once:n.once.bind(n),off:n.off.bind(n),emit:n.emit.bind(n)})}));const d=S({},le,o);return n.params=S({},d,de,s),n.originalParams=S({},n.params),n.passedParams=S({},s),n.params&&n.params.on&&Object.keys(n.params.on).forEach((e=>{n.on(e,n.params.on[e])})),n.params&&n.params.onAny&&n.onAny(n.params.onAny),Object.assign(n,{enabled:n.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===n.params.direction,isVertical:()=>"vertical"===n.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:n.params.allowSlideNext,allowSlidePrev:n.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:n.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:n.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),n.emit("_swiper"),n.params.init&&n.init(),n}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:s}=this,a=I(C(t,`.${s.slideClass}, swiper-slide`)[0]);return I(e)-a}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find((t=>1*t.getAttribute("data-swiper-slide-index")===e)))}recalcSlides(){const{slidesEl:e,params:t}=this;this.slides=C(e,`.${t.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const s=this;e=Math.min(Math.max(e,0),1);const a=s.minTranslate(),i=(s.maxTranslate()-a)*e+a;s.translateTo(i,void 0===t?0:t),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter((t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass)));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter((e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass))).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach((s=>{const a=e.getSlideClasses(s);t.push({slideEl:s,classNames:a}),e.emit("_slideClass",s,a)})),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);const{params:s,slides:a,slidesGrid:i,slidesSizesGrid:r,size:l,activeIndex:n}=this;let o=1;if("number"==typeof s.slidesPerView)return s.slidesPerView;if(s.centeredSlides){let e,t=a[n]?Math.ceil(a[n].swiperSlideSize):0;for(let s=n+1;s<a.length;s+=1)a[s]&&!e&&(t+=Math.ceil(a[s].swiperSlideSize),o+=1,t>l&&(e=!0));for(let s=n-1;s>=0;s-=1)a[s]&&!e&&(t+=a[s].swiperSlideSize,o+=1,t>l&&(e=!0))}else if("current"===e)for(let d=n+1;d<a.length;d+=1){(t?i[d]+r[d]-i[n]<l:i[d]-i[n]<l)&&(o+=1)}else for(let d=n-1;d>=0;d-=1){i[n]-i[d]<l&&(o+=1)}return o}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:s}=e;function a(){const t=e.rtlTranslate?-1*e.translate:e.translate,s=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(s),e.updateActiveIndex(),e.updateSlidesClasses()}let i;if(s.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach((t=>{t.complete&&F(e,t)})),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),s.freeMode&&s.freeMode.enabled&&!s.cssMode)a(),s.autoHeight&&e.updateAutoHeight();else{if(("auto"===s.slidesPerView||s.slidesPerView>1)&&e.isEnd&&!s.centeredSlides){const t=e.virtual&&s.virtual.enabled?e.virtual.slides:e.slides;i=e.slideTo(t.length-1,0,!1,!0)}else i=e.slideTo(e.activeIndex,0,!1,!0);i||a()}s.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);const s=this,a=s.params.direction;return e||(e="horizontal"===a?"vertical":"horizontal"),e===a||"horizontal"!==e&&"vertical"!==e||(s.el.classList.remove(`${s.params.containerModifierClass}${a}`),s.el.classList.add(`${s.params.containerModifierClass}${e}`),s.emitContainerClasses(),s.params.direction=e,s.slides.forEach((t=>{"vertical"===e?t.style.width="":t.style.height=""})),s.emit("changeDirection"),t&&s.update()),s}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let s=e||t.params.el;if("string"==typeof s&&(s=document.querySelector(s)),!s)return!1;s.swiper=t,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const a=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let i=(()=>{if(s&&s.shadowRoot&&s.shadowRoot.querySelector){return s.shadowRoot.querySelector(a())}return C(s,a())[0]})();return!i&&t.params.createElements&&(i=L("div",t.params.wrapperClass),s.append(i),C(s,`.${t.params.slideClass}`).forEach((e=>{i.append(e)}))),Object.assign(t,{el:s,wrapperEl:i,slidesEl:t.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:i,hostEl:t.isElement?s.parentNode.host:s,mounted:!0,rtl:"rtl"===s.dir.toLowerCase()||"rtl"===z(s,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===s.dir.toLowerCase()||"rtl"===z(s,"direction")),wrongRTL:"-webkit-box"===z(i,"display")}),!0}init(e){const t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();const s=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&s.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),s.forEach((e=>{e.complete?F(t,e):e.addEventListener("load",(e=>{F(t,e.target)}))})),V(t),t.initialized=!0,V(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const s=this,{params:a,el:i,wrapperEl:r,slides:l}=s;return void 0===s.params||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),a.loop&&s.loopDestroy(),t&&(s.removeClasses(),i&&"string"!=typeof i&&i.removeAttribute("style"),r&&r.removeAttribute("style"),l&&l.length&&l.forEach((e=>{e.classList.remove(a.slideVisibleClass,a.slideFullyVisibleClass,a.slideActiveClass,a.slideNextClass,a.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")}))),s.emit("destroy"),Object.keys(s.eventsListeners).forEach((e=>{s.off(e)})),!1!==e&&(s.el&&"string"!=typeof s.el&&(s.el.swiper=null),function(e){const t=e;Object.keys(t).forEach((e=>{try{t[e]=null}catch(s){}try{delete t[e]}catch(s){}}))}(s)),s.destroyed=!0),null}static extendDefaults(e){S(de,e)}static get extendedDefaults(){return de}static get defaults(){return le}static installModule(t){e.prototype.__modules__||(e.prototype.__modules__=[]);const s=e.prototype.__modules__;"function"==typeof t&&s.indexOf(t)<0&&s.push(t)}static use(t){return Array.isArray(t)?(t.forEach((t=>e.installModule(t))),e):(e.installModule(t),e)}};Object.keys(oe).forEach((e=>{Object.keys(oe[e]).forEach((t=>{ce.prototype[t]=oe[e][t]}))})),ce.use([function(e){let{swiper:t,on:s,emit:a}=e;const i=v();let r=null,l=null;const n=()=>{t&&!t.destroyed&&t.initialized&&(a("beforeResize"),a("resize"))},o=()=>{t&&!t.destroyed&&t.initialized&&a("orientationchange")};s("init",(()=>{t.params.resizeObserver&&void 0!==i.ResizeObserver?t&&!t.destroyed&&t.initialized&&(r=new ResizeObserver((e=>{l=i.requestAnimationFrame((()=>{const{width:s,height:a}=t;let i=s,r=a;e.forEach((e=>{let{contentBoxSize:s,contentRect:a,target:l}=e;l&&l!==t.el||(i=a?a.width:(s[0]||s).inlineSize,r=a?a.height:(s[0]||s).blockSize)})),i===s&&r===a||n()}))})),r.observe(t.el)):(i.addEventListener("resize",n),i.addEventListener("orientationchange",o))})),s("destroy",(()=>{l&&i.cancelAnimationFrame(l),r&&r.unobserve&&t.el&&(r.unobserve(t.el),r=null),i.removeEventListener("resize",n),i.removeEventListener("orientationchange",o)}))},function(e){let{swiper:t,extendParams:s,on:a,emit:i}=e;const r=[],l=v(),n=function(e,s){void 0===s&&(s={});const a=new(l.MutationObserver||l.WebkitMutationObserver)((e=>{if(t.__preventObserver__)return;if(1===e.length)return void i("observerUpdate",e[0]);const s=function(){i("observerUpdate",e[0])};l.requestAnimationFrame?l.requestAnimationFrame(s):l.setTimeout(s,0)}));a.observe(e,{attributes:void 0===s.attributes||s.attributes,childList:t.isElement||(void 0===s.childList||s).childList,characterData:void 0===s.characterData||s.characterData}),r.push(a)};s({observer:!1,observeParents:!1,observeSlideChildren:!1}),a("init",(()=>{if(t.params.observer){if(t.params.observeParents){const e=k(t.hostEl);for(let t=0;t<e.length;t+=1)n(e[t])}n(t.hostEl,{childList:t.params.observeSlideChildren}),n(t.wrapperEl,{attributes:!1})}})),a("destroy",(()=>{r.forEach((e=>{e.disconnect()})),r.splice(0,r.length)}))}]);const pe=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function ue(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function me(e,t){const s=["__proto__","constructor","prototype"];Object.keys(t).filter((e=>s.indexOf(e)<0)).forEach((s=>{void 0===e[s]?e[s]=t[s]:ue(t[s])&&ue(e[s])&&Object.keys(t[s]).length>0?t[s].__swiper__?e[s]=t[s]:me(e[s],t[s]):e[s]=t[s]}))}function fe(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function he(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function ve(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function ge(e){void 0===e&&(e="");const t=e.split(" ").map((e=>e.trim())).filter((e=>!!e)),s=[];return t.forEach((e=>{s.indexOf(e)<0&&s.push(e)})),s.join(" ")}function we(e,t){void 0===e&&(e={});const s={on:{}},a={};me(s,le),s._emitClasses=!0,s.init=!1;const i={},r=pe.map((e=>e.replace(/_/,""))),l=Object.assign({},e);return Object.keys(l).forEach((t=>{void 0!==e[t]&&(r.indexOf(t)>=0?ue(e[t])?(s[t]={},a[t]={},me(s[t],e[t]),me(a[t],e[t])):(s[t]=e[t],a[t]=e[t]):0===t.search(/on[A-Z]/)&&"function"==typeof e[t]?s.on[`${t[2].toLowerCase()}${t.substr(3)}`]=e[t]:i[t]=e[t])})),["navigation","pagination","scrollbar"].forEach((e=>{!0===s[e]&&(s[e]={}),!1===s[e]&&delete s[e]})),{params:s,passedParams:a,rest:i,events:{}}}function be(e,t,s){void 0===e&&(e={});const a=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]},r=(e,t)=>{Array.isArray(e)&&e.forEach((e=>{const s="symbol"==typeof e.type;"default"===t&&(t="container-end"),s&&e.children?r(e.children,t):e.type&&("SwiperSlide"===e.type.name||"AsyncComponentWrapper"===e.type.name)||e.componentOptions&&"SwiperSlide"===e.componentOptions.tag?a.push(e):i[t]&&i[t].push(e)}))};return Object.keys(e).forEach((t=>{if("function"!=typeof e[t])return;const s=e[t]();r(s,t)})),s.value=t.value,t.value=a,{slides:a,slots:i}}const ye={name:"Swiper",props:{tag:{type:String,default:"div"},wrapperTag:{type:String,default:"div"},modules:{type:Array,default:void 0},init:{type:Boolean,default:void 0},direction:{type:String,default:void 0},oneWayMovement:{type:Boolean,default:void 0},swiperElementNodeName:{type:String,default:"SWIPER-CONTAINER"},touchEventsTarget:{type:String,default:void 0},initialSlide:{type:Number,default:void 0},speed:{type:Number,default:void 0},cssMode:{type:Boolean,default:void 0},updateOnWindowResize:{type:Boolean,default:void 0},resizeObserver:{type:Boolean,default:void 0},nested:{type:Boolean,default:void 0},focusableElements:{type:String,default:void 0},width:{type:Number,default:void 0},height:{type:Number,default:void 0},preventInteractionOnTransition:{type:Boolean,default:void 0},userAgent:{type:String,default:void 0},url:{type:String,default:void 0},edgeSwipeDetection:{type:[Boolean,String],default:void 0},edgeSwipeThreshold:{type:Number,default:void 0},autoHeight:{type:Boolean,default:void 0},setWrapperSize:{type:Boolean,default:void 0},virtualTranslate:{type:Boolean,default:void 0},effect:{type:String,default:void 0},breakpoints:{type:Object,default:void 0},breakpointsBase:{type:String,default:void 0},spaceBetween:{type:[Number,String],default:void 0},slidesPerView:{type:[Number,String],default:void 0},maxBackfaceHiddenSlides:{type:Number,default:void 0},slidesPerGroup:{type:Number,default:void 0},slidesPerGroupSkip:{type:Number,default:void 0},slidesPerGroupAuto:{type:Boolean,default:void 0},centeredSlides:{type:Boolean,default:void 0},centeredSlidesBounds:{type:Boolean,default:void 0},slidesOffsetBefore:{type:Number,default:void 0},slidesOffsetAfter:{type:Number,default:void 0},normalizeSlideIndex:{type:Boolean,default:void 0},centerInsufficientSlides:{type:Boolean,default:void 0},watchOverflow:{type:Boolean,default:void 0},roundLengths:{type:Boolean,default:void 0},touchRatio:{type:Number,default:void 0},touchAngle:{type:Number,default:void 0},simulateTouch:{type:Boolean,default:void 0},shortSwipes:{type:Boolean,default:void 0},longSwipes:{type:Boolean,default:void 0},longSwipesRatio:{type:Number,default:void 0},longSwipesMs:{type:Number,default:void 0},followFinger:{type:Boolean,default:void 0},allowTouchMove:{type:Boolean,default:void 0},threshold:{type:Number,default:void 0},touchMoveStopPropagation:{type:Boolean,default:void 0},touchStartPreventDefault:{type:Boolean,default:void 0},touchStartForcePreventDefault:{type:Boolean,default:void 0},touchReleaseOnEdges:{type:Boolean,default:void 0},uniqueNavElements:{type:Boolean,default:void 0},resistance:{type:Boolean,default:void 0},resistanceRatio:{type:Number,default:void 0},watchSlidesProgress:{type:Boolean,default:void 0},grabCursor:{type:Boolean,default:void 0},preventClicks:{type:Boolean,default:void 0},preventClicksPropagation:{type:Boolean,default:void 0},slideToClickedSlide:{type:Boolean,default:void 0},loop:{type:Boolean,default:void 0},loopedSlides:{type:Number,default:void 0},loopPreventsSliding:{type:Boolean,default:void 0},rewind:{type:Boolean,default:void 0},allowSlidePrev:{type:Boolean,default:void 0},allowSlideNext:{type:Boolean,default:void 0},swipeHandler:{type:Boolean,default:void 0},noSwiping:{type:Boolean,default:void 0},noSwipingClass:{type:String,default:void 0},noSwipingSelector:{type:String,default:void 0},passiveListeners:{type:Boolean,default:void 0},containerModifierClass:{type:String,default:void 0},slideClass:{type:String,default:void 0},slideActiveClass:{type:String,default:void 0},slideVisibleClass:{type:String,default:void 0},slideFullyVisibleClass:{type:String,default:void 0},slideBlankClass:{type:String,default:void 0},slideNextClass:{type:String,default:void 0},slidePrevClass:{type:String,default:void 0},wrapperClass:{type:String,default:void 0},lazyPreloaderClass:{type:String,default:void 0},lazyPreloadPrevNext:{type:Number,default:void 0},runCallbacksOnInit:{type:Boolean,default:void 0},observer:{type:Boolean,default:void 0},observeParents:{type:Boolean,default:void 0},observeSlideChildren:{type:Boolean,default:void 0},a11y:{type:[Boolean,Object],default:void 0},autoplay:{type:[Boolean,Object],default:void 0},controller:{type:Object,default:void 0},coverflowEffect:{type:Object,default:void 0},cubeEffect:{type:Object,default:void 0},fadeEffect:{type:Object,default:void 0},flipEffect:{type:Object,default:void 0},creativeEffect:{type:Object,default:void 0},cardsEffect:{type:Object,default:void 0},hashNavigation:{type:[Boolean,Object],default:void 0},history:{type:[Boolean,Object],default:void 0},keyboard:{type:[Boolean,Object],default:void 0},mousewheel:{type:[Boolean,Object],default:void 0},navigation:{type:[Boolean,Object],default:void 0},pagination:{type:[Boolean,Object],default:void 0},parallax:{type:[Boolean,Object],default:void 0},scrollbar:{type:[Boolean,Object],default:void 0},thumbs:{type:Object,default:void 0},virtual:{type:[Boolean,Object],default:void 0},zoom:{type:[Boolean,Object],default:void 0},grid:{type:[Object],default:void 0},freeMode:{type:[Boolean,Object],default:void 0},enabled:{type:Boolean,default:void 0}},emits:["_beforeBreakpoint","_containerClasses","_slideClass","_slideClasses","_swiper","_freeModeNoMomentumRelease","activeIndexChange","afterInit","autoplay","autoplayStart","autoplayStop","autoplayPause","autoplayResume","autoplayTimeLeft","beforeDestroy","beforeInit","beforeLoopFix","beforeResize","beforeSlideChangeStart","beforeTransitionStart","breakpoint","changeDirection","click","disable","doubleTap","doubleClick","destroy","enable","fromEdge","hashChange","hashSet","init","keyPress","lock","loopFix","momentumBounce","navigationHide","navigationShow","navigationPrev","navigationNext","observerUpdate","orientationchange","paginationHide","paginationRender","paginationShow","paginationUpdate","progress","reachBeginning","reachEnd","realIndexChange","resize","scroll","scrollbarDragEnd","scrollbarDragMove","scrollbarDragStart","setTransition","setTranslate","slidesUpdated","slideChange","slideChangeTransitionEnd","slideChangeTransitionStart","slideNextTransitionEnd","slideNextTransitionStart","slidePrevTransitionEnd","slidePrevTransitionStart","slideResetTransitionStart","slideResetTransitionEnd","sliderMove","sliderFirstMove","slidesLengthChange","slidesGridLengthChange","snapGridLengthChange","snapIndexChange","swiper","tap","toEdge","touchEnd","touchMove","touchMoveOpposite","touchStart","transitionEnd","transitionStart","unlock","update","virtualUpdate","zoomChange"],setup(n,o){let{slots:d,emit:p}=o;const{tag:u,wrapperTag:m}=n,f=e("swiper"),h=e(null),v=e(!1),g=e(!1),w=e(null),b=e(null),y=e(null),E={value:[]},S={value:[]},x=e(null),T=e(null),M=e(null),C=e(null),{params:P,passedParams:L}=we(n);be(d,E,S),y.value=L,S.value=E.value;P.onAny=function(e){for(var t=arguments.length,s=new Array(t>1?t-1:0),a=1;a<t;a++)s[a-1]=arguments[a];p(e,...s)},Object.assign(P.on,{_beforeBreakpoint:()=>{be(d,E,S),v.value=!0},_containerClasses(e,t){f.value=t}});const O={...P};if(delete O.wrapperClass,b.value=new ce(O),b.value.virtual&&b.value.params.virtual.enabled){b.value.virtual.slides=E.value;const e={cache:!1,slides:E.value,renderExternal:e=>{h.value=e},renderExternalUpdate:!1};me(b.value.params.virtual,e),me(b.value.originalParams.virtual,e)}function z(e){return P.virtual?function(e,t,s){if(!s)return null;const a=e=>{let s=e;return e<0?s=t.length+e:s>=t.length&&(s-=t.length),s},i=e.value.isHorizontal()?{[e.value.rtlTranslate?"right":"left"]:`${s.offset}px`}:{top:`${s.offset}px`},{from:r,to:n}=s,o=e.value.params.loop?-t.length:0,d=e.value.params.loop?2*t.length:t.length,c=[];for(let l=o;l<d;l+=1)l>=r&&l<=n&&c.length<t.length&&c.push(t[a(l)]);return c.map((t=>(t.props||(t.props={}),t.props.style||(t.props.style={}),t.props.swiperRef=e,t.props.style=i,t.type?l(t.type,{...t.props},t.children):t.componentOptions?l(t.componentOptions.Ctor,{...t.props},t.componentOptions.children):void 0)))}(b,e,h.value):(e.forEach(((e,t)=>{e.props||(e.props={}),e.props.swiperRef=b,e.props.swiperSlideIndex=t})),e)}return t((()=>{!g.value&&b.value&&(b.value.emitSlidesClasses(),g.value=!0);const{passedParams:e}=we(n),t=function(e,t,s,a,i){const r=[];if(!t)return r;const l=e=>{r.indexOf(e)<0&&r.push(e)};if(s&&a){const e=a.map(i),t=s.map(i);e.join("")!==t.join("")&&l("children"),a.length!==s.length&&l("children")}return pe.filter((e=>"_"===e[0])).map((e=>e.replace(/_/,""))).forEach((s=>{if(s in e&&s in t)if(ue(e[s])&&ue(t[s])){const a=Object.keys(e[s]),i=Object.keys(t[s]);a.length!==i.length?l(s):(a.forEach((a=>{e[s][a]!==t[s][a]&&l(s)})),i.forEach((a=>{e[s][a]!==t[s][a]&&l(s)})))}else e[s]!==t[s]&&l(s)})),r}(e,y.value,E.value,S.value,(e=>e.props&&e.props.key));y.value=e,(t.length||v.value)&&b.value&&!b.value.destroyed&&function(e){let{swiper:t,slides:s,passedParams:a,changedParams:i,nextEl:r,prevEl:l,scrollbarEl:n,paginationEl:o}=e;const d=i.filter((e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e)),{params:c,pagination:p,navigation:u,scrollbar:m,virtual:f,thumbs:h}=t;let v,g,w,b,y,E,S,x;i.includes("thumbs")&&a.thumbs&&a.thumbs.swiper&&!a.thumbs.swiper.destroyed&&c.thumbs&&(!c.thumbs.swiper||c.thumbs.swiper.destroyed)&&(v=!0),i.includes("controller")&&a.controller&&a.controller.control&&c.controller&&!c.controller.control&&(g=!0),i.includes("pagination")&&a.pagination&&(a.pagination.el||o)&&(c.pagination||!1===c.pagination)&&p&&!p.el&&(w=!0),i.includes("scrollbar")&&a.scrollbar&&(a.scrollbar.el||n)&&(c.scrollbar||!1===c.scrollbar)&&m&&!m.el&&(b=!0),i.includes("navigation")&&a.navigation&&(a.navigation.prevEl||l)&&(a.navigation.nextEl||r)&&(c.navigation||!1===c.navigation)&&u&&!u.prevEl&&!u.nextEl&&(y=!0);const T=e=>{t[e]&&(t[e].destroy(),"navigation"===e?(t.isElement&&(t[e].prevEl.remove(),t[e].nextEl.remove()),c[e].prevEl=void 0,c[e].nextEl=void 0,t[e].prevEl=void 0,t[e].nextEl=void 0):(t.isElement&&t[e].el.remove(),c[e].el=void 0,t[e].el=void 0))};i.includes("loop")&&t.isElement&&(c.loop&&!a.loop?E=!0:!c.loop&&a.loop?S=!0:x=!0),d.forEach((e=>{if(ue(c[e])&&ue(a[e]))Object.assign(c[e],a[e]),"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e||!("enabled"in a[e])||a[e].enabled||T(e);else{const t=a[e];!0!==t&&!1!==t||"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e?c[e]=a[e]:!1===t&&T(e)}})),d.includes("controller")&&!g&&t.controller&&t.controller.control&&c.controller&&c.controller.control&&(t.controller.control=c.controller.control),i.includes("children")&&s&&f&&c.virtual.enabled?(f.slides=s,f.update(!0)):i.includes("virtual")&&f&&c.virtual.enabled&&(s&&(f.slides=s),f.update(!0)),i.includes("children")&&s&&c.loop&&(x=!0),v&&h.init()&&h.update(!0);g&&(t.controller.control=c.controller.control),w&&(!t.isElement||o&&"string"!=typeof o||(o=document.createElement("div"),o.classList.add("swiper-pagination"),o.part.add("pagination"),t.el.appendChild(o)),o&&(c.pagination.el=o),p.init(),p.render(),p.update()),b&&(!t.isElement||n&&"string"!=typeof n||(n=document.createElement("div"),n.classList.add("swiper-scrollbar"),n.part.add("scrollbar"),t.el.appendChild(n)),n&&(c.scrollbar.el=n),m.init(),m.updateSize(),m.setTranslate()),y&&(t.isElement&&(r&&"string"!=typeof r||(r=document.createElement("div"),r.classList.add("swiper-button-next"),r.innerHTML=t.hostEl.constructor.nextButtonSvg,r.part.add("button-next"),t.el.appendChild(r)),l&&"string"!=typeof l||(l=document.createElement("div"),l.classList.add("swiper-button-prev"),l.innerHTML=t.hostEl.constructor.prevButtonSvg,l.part.add("button-prev"),t.el.appendChild(l))),r&&(c.navigation.nextEl=r),l&&(c.navigation.prevEl=l),u.init(),u.update()),i.includes("allowSlideNext")&&(t.allowSlideNext=a.allowSlideNext),i.includes("allowSlidePrev")&&(t.allowSlidePrev=a.allowSlidePrev),i.includes("direction")&&t.changeDirection(a.direction,!1),(E||x)&&t.loopDestroy(),(S||x)&&t.loopCreate(),t.update()}({swiper:b.value,slides:E.value,passedParams:e,changedParams:t,nextEl:x.value,prevEl:T.value,scrollbarEl:C.value,paginationEl:M.value}),v.value=!1})),c("swiper",b),s(h,(()=>{a((()=>{var e;!(e=b.value)||e.destroyed||!e.params.virtual||e.params.virtual&&!e.params.virtual.enabled||(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())}))})),i((()=>{w.value&&(!function(e,t){let{el:s,nextEl:a,prevEl:i,paginationEl:r,scrollbarEl:l,swiper:n}=e;fe(t)&&a&&i&&(n.params.navigation.nextEl=a,n.originalParams.navigation.nextEl=a,n.params.navigation.prevEl=i,n.originalParams.navigation.prevEl=i),he(t)&&r&&(n.params.pagination.el=r,n.originalParams.pagination.el=r),ve(t)&&l&&(n.params.scrollbar.el=l,n.originalParams.scrollbar.el=l),n.init(s)}({el:w.value,nextEl:x.value,prevEl:T.value,paginationEl:M.value,scrollbarEl:C.value,swiper:b.value},P),p("swiper",b.value))})),r((()=>{b.value&&!b.value.destroyed&&b.value.destroy(!0,!1)})),()=>{const{slides:e,slots:t}=be(d,E,S);return l(u,{ref:w,class:ge(f.value)},[t["container-start"],l(m,{class:(s=P.wrapperClass,void 0===s&&(s=""),s?s.includes("swiper-wrapper")?s:`swiper-wrapper ${s}`:"swiper-wrapper")},[t["wrapper-start"],z(e),t["wrapper-end"]]),fe(n)&&[l("div",{ref:T,class:"swiper-button-prev"}),l("div",{ref:x,class:"swiper-button-next"})],ve(n)&&l("div",{ref:C,class:"swiper-scrollbar"}),he(n)&&l("div",{ref:M,class:"swiper-pagination"}),t["container-end"]]);var s}}},Ee={name:"SwiperSlide",props:{tag:{type:String,default:"div"},swiperRef:{type:Object,required:!1},swiperSlideIndex:{type:Number,default:void 0,required:!1},zoom:{type:Boolean,default:void 0,required:!1},lazy:{type:Boolean,default:!1,required:!1},virtualIndex:{type:[String,Number],default:void 0}},setup(s,a){let{slots:d}=a,p=!1;const{swiperRef:u}=s,m=e(null),f=e("swiper-slide"),h=e(!1);function v(e,t,s){t===m.value&&(f.value=s)}i((()=>{u&&u.value&&(u.value.on("_slideClass",v),p=!0)})),n((()=>{!p&&u&&u.value&&(u.value.on("_slideClass",v),p=!0)})),t((()=>{m.value&&u&&u.value&&(void 0!==s.swiperSlideIndex&&(m.value.swiperSlideIndex=s.swiperSlideIndex),u.value.destroyed&&"swiper-slide"!==f.value&&(f.value="swiper-slide"))})),r((()=>{u&&u.value&&u.value.off("_slideClass",v)}));const g=o((()=>({isActive:f.value.indexOf("swiper-slide-active")>=0,isVisible:f.value.indexOf("swiper-slide-visible")>=0,isPrev:f.value.indexOf("swiper-slide-prev")>=0,isNext:f.value.indexOf("swiper-slide-next")>=0})));c("swiperSlide",g);const w=()=>{h.value=!0};return()=>l(s.tag,{class:ge(`${f.value}`),ref:m,"data-swiper-slide-index":void 0===s.virtualIndex&&u&&u.value&&u.value.params.loop?s.swiperSlideIndex:s.virtualIndex,onLoadCapture:w},s.zoom?l("div",{class:"swiper-zoom-container","data-swiper-zoom":"number"==typeof s.zoom?s.zoom:void 0},[d.default&&d.default(g.value),s.lazy&&!h.value&&l("div",{class:"swiper-lazy-preloader"})]):[d.default&&d.default(g.value),s.lazy&&!h.value&&l("div",{class:"swiper-lazy-preloader"})])}},Se=Object.freeze(Object.defineProperty({__proto__:null,Swiper:ye,SwiperSlide:Ee,useSwiper:()=>d("swiper"),useSwiperSlide:()=>d("swiperSlide")},Symbol.toStringTag,{value:"Module"}));function xe(e,t,s,a){return e.params.createElements&&Object.keys(a).forEach((i=>{if(!s[i]&&!0===s.auto){let r=C(e.el,`.${a[i]}`)[0];r||(r=L("div",a[i]),r.className=a[i],e.el.append(r)),s[i]=r,t[i]=r}})),s}function Te(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function Me(e){const t=this,{params:s,slidesEl:a}=t;s.loop&&t.loopDestroy();const i=e=>{if("string"==typeof e){const t=document.createElement("div");t.innerHTML=e,a.append(t.children[0]),t.innerHTML=""}else a.append(e)};if("object"==typeof e&&"length"in e)for(let r=0;r<e.length;r+=1)e[r]&&i(e[r]);else i(e);t.recalcSlides(),s.loop&&t.loopCreate(),s.observer&&!t.isElement||t.update()}function Ce(e){const t=this,{params:s,activeIndex:a,slidesEl:i}=t;s.loop&&t.loopDestroy();let r=a+1;const l=e=>{if("string"==typeof e){const t=document.createElement("div");t.innerHTML=e,i.prepend(t.children[0]),t.innerHTML=""}else i.prepend(e)};if("object"==typeof e&&"length"in e){for(let t=0;t<e.length;t+=1)e[t]&&l(e[t]);r=a+e.length}else l(e);t.recalcSlides(),s.loop&&t.loopCreate(),s.observer&&!t.isElement||t.update(),t.slideTo(r,0,!1)}function Pe(e,t){const s=this,{params:a,activeIndex:i,slidesEl:r}=s;let l=i;a.loop&&(l-=s.loopedSlides,s.loopDestroy(),s.recalcSlides());const n=s.slides.length;if(e<=0)return void s.prependSlide(t);if(e>=n)return void s.appendSlide(t);let o=l>e?l+1:l;const d=[];for(let c=n-1;c>=e;c-=1){const e=s.slides[c];e.remove(),d.unshift(e)}if("object"==typeof t&&"length"in t){for(let e=0;e<t.length;e+=1)t[e]&&r.append(t[e]);o=l>e?l+t.length:l}else r.append(t);for(let c=0;c<d.length;c+=1)r.append(d[c]);s.recalcSlides(),a.loop&&s.loopCreate(),a.observer&&!s.isElement||s.update(),a.loop?s.slideTo(o+s.loopedSlides,0,!1):s.slideTo(o,0,!1)}function Le(e){const t=this,{params:s,activeIndex:a}=t;let i=a;s.loop&&(i-=t.loopedSlides,t.loopDestroy());let r,l=i;if("object"==typeof e&&"length"in e){for(let s=0;s<e.length;s+=1)r=e[s],t.slides[r]&&t.slides[r].remove(),r<l&&(l-=1);l=Math.max(l,0)}else r=e,t.slides[r]&&t.slides[r].remove(),r<l&&(l-=1),l=Math.max(l,0);t.recalcSlides(),s.loop&&t.loopCreate(),s.observer&&!t.isElement||t.update(),s.loop?t.slideTo(l+t.loopedSlides,0,!1):t.slideTo(l,0,!1)}function Oe(){const e=this,t=[];for(let s=0;s<e.slides.length;s+=1)t.push(s);e.removeSlide(t)}function ze(e){const{effect:t,swiper:s,on:a,setTranslate:i,setTransition:r,overwriteParams:l,perspective:n,recreateShadows:o,getEffectParams:d}=e;let c;a("beforeInit",(()=>{if(s.params.effect!==t)return;s.classNames.push(`${s.params.containerModifierClass}${t}`),n&&n()&&s.classNames.push(`${s.params.containerModifierClass}3d`);const e=l?l():{};Object.assign(s.params,e),Object.assign(s.originalParams,e)})),a("setTranslate",(()=>{s.params.effect===t&&i()})),a("setTransition",((e,a)=>{s.params.effect===t&&r(a)})),a("transitionEnd",(()=>{if(s.params.effect===t&&o){if(!d||!d().slideShadows)return;s.slides.forEach((e=>{e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((e=>e.remove()))})),o()}})),a("virtualUpdate",(()=>{s.params.effect===t&&(s.slides.length||(c=!0),requestAnimationFrame((()=>{c&&s.slides&&s.slides.length&&(i(),c=!1)})))}))}function Ie(e,t){const s=M(t);return s!==t&&(s.style.backfaceVisibility="hidden",s.style["-webkit-backface-visibility"]="hidden"),s}function ke(e){let{swiper:t,duration:s,transformElements:a,allSlides:i}=e;const{activeIndex:r}=t;if(t.params.virtualTranslate&&0!==s){let e,s=!1;e=i?a:a.filter((e=>{const s=e.classList.contains("swiper-slide-transform")?(e=>{if(!e.parentElement)return t.slides.find((t=>t.shadowRoot&&t.shadowRoot===e.parentNode));return e.parentElement})(e):e;return t.getSlideIndex(s)===r})),e.forEach((e=>{A(e,(()=>{if(s)return;if(!t||t.destroyed)return;s=!0,t.animating=!1;const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});t.wrapperEl.dispatchEvent(e)}))}))}}function Ae(e,t,s){const a=`swiper-slide-shadow${s?`-${s}`:""}${e?` swiper-slide-shadow-${e}`:""}`,i=M(t);let r=i.querySelector(`.${a.split(" ").join(".")}`);return r||(r=L("div",a.split(" ")),i.append(r)),r}const $e=Object.freeze(Object.defineProperty({__proto__:null,A11y:function(e){let{swiper:t,extendParams:s,on:a}=e;s({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,containerRole:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null,scrollOnFocus:!0}}),t.a11y={clicked:!1};let i,r,l=null,n=(new Date).getTime();function o(e){const t=l;0!==t.length&&(t.innerHTML="",t.innerHTML=e)}function d(e){(e=D(e)).forEach((e=>{e.setAttribute("tabIndex","0")}))}function c(e){(e=D(e)).forEach((e=>{e.setAttribute("tabIndex","-1")}))}function p(e,t){(e=D(e)).forEach((e=>{e.setAttribute("role",t)}))}function u(e,t){(e=D(e)).forEach((e=>{e.setAttribute("aria-roledescription",t)}))}function m(e,t){(e=D(e)).forEach((e=>{e.setAttribute("aria-label",t)}))}function h(e){(e=D(e)).forEach((e=>{e.setAttribute("aria-disabled",!0)}))}function v(e){(e=D(e)).forEach((e=>{e.setAttribute("aria-disabled",!1)}))}function g(e){if(13!==e.keyCode&&32!==e.keyCode)return;const s=t.params.a11y,a=e.target;if(!t.pagination||!t.pagination.el||a!==t.pagination.el&&!t.pagination.el.contains(e.target)||e.target.matches(Te(t.params.pagination.bulletClass))){if(t.navigation&&t.navigation.prevEl&&t.navigation.nextEl){const e=D(t.navigation.prevEl);D(t.navigation.nextEl).includes(a)&&(t.isEnd&&!t.params.loop||t.slideNext(),t.isEnd?o(s.lastSlideMessage):o(s.nextSlideMessage)),e.includes(a)&&(t.isBeginning&&!t.params.loop||t.slidePrev(),t.isBeginning?o(s.firstSlideMessage):o(s.prevSlideMessage))}t.pagination&&a.matches(Te(t.params.pagination.bulletClass))&&a.click()}}function w(){return t.pagination&&t.pagination.bullets&&t.pagination.bullets.length}function b(){return w()&&t.params.pagination.clickable}const y=(e,t,s)=>{d(e),"BUTTON"!==e.tagName&&(p(e,"button"),e.addEventListener("keydown",g)),m(e,s),function(e,t){(e=D(e)).forEach((e=>{e.setAttribute("aria-controls",t)}))}(e,t)},E=e=>{r&&r!==e.target&&!r.contains(e.target)&&(i=!0),t.a11y.clicked=!0},S=()=>{i=!1,requestAnimationFrame((()=>{requestAnimationFrame((()=>{t.destroyed||(t.a11y.clicked=!1)}))}))},x=e=>{n=(new Date).getTime()},T=e=>{if(t.a11y.clicked||!t.params.a11y.scrollOnFocus)return;if((new Date).getTime()-n<100)return;const s=e.target.closest(`.${t.params.slideClass}, swiper-slide`);if(!s||!t.slides.includes(s))return;r=s;const a=t.slides.indexOf(s)===t.activeIndex,l=t.params.watchSlidesProgress&&t.visibleSlides&&t.visibleSlides.includes(s);a||l||e.sourceCapabilities&&e.sourceCapabilities.firesTouchEvents||(t.isHorizontal()?t.el.scrollLeft=0:t.el.scrollTop=0,requestAnimationFrame((()=>{i||(t.params.loop?t.slideToLoop(parseInt(s.getAttribute("data-swiper-slide-index")),0):t.slideTo(t.slides.indexOf(s),0),i=!1)})))},M=()=>{const e=t.params.a11y;e.itemRoleDescriptionMessage&&u(t.slides,e.itemRoleDescriptionMessage),e.slideRole&&p(t.slides,e.slideRole);const s=t.slides.length;e.slideLabelMessage&&t.slides.forEach(((a,i)=>{const r=t.params.loop?parseInt(a.getAttribute("data-swiper-slide-index"),10):i;m(a,e.slideLabelMessage.replace(/\{\{index\}\}/,r+1).replace(/\{\{slidesLength\}\}/,s))}))},C=()=>{const e=t.params.a11y;t.el.append(l);const s=t.el;e.containerRoleDescriptionMessage&&u(s,e.containerRoleDescriptionMessage),e.containerMessage&&m(s,e.containerMessage),e.containerRole&&p(s,e.containerRole);const a=t.wrapperEl,i=e.id||a.getAttribute("id")||`swiper-wrapper-${r=16,"x".repeat(r).replace(/x/g,(()=>Math.round(16*Math.random()).toString(16)))}`;var r;const n=t.params.autoplay&&t.params.autoplay.enabled?"off":"polite";var o;o=i,D(a).forEach((e=>{e.setAttribute("id",o)})),function(e,t){(e=D(e)).forEach((e=>{e.setAttribute("aria-live",t)}))}(a,n),M();let{nextEl:d,prevEl:c}=t.navigation?t.navigation:{};if(d=D(d),c=D(c),d&&d.forEach((t=>y(t,i,e.nextSlideMessage))),c&&c.forEach((t=>y(t,i,e.prevSlideMessage))),b()){D(t.pagination.el).forEach((e=>{e.addEventListener("keydown",g)}))}f().addEventListener("visibilitychange",x),t.el.addEventListener("focus",T,!0),t.el.addEventListener("focus",T,!0),t.el.addEventListener("pointerdown",E,!0),t.el.addEventListener("pointerup",S,!0)};a("beforeInit",(()=>{l=L("span",t.params.a11y.notificationClass),l.setAttribute("aria-live","assertive"),l.setAttribute("aria-atomic","true")})),a("afterInit",(()=>{t.params.a11y.enabled&&C()})),a("slidesLengthChange snapGridLengthChange slidesGridLengthChange",(()=>{t.params.a11y.enabled&&M()})),a("fromEdge toEdge afterInit lock unlock",(()=>{t.params.a11y.enabled&&function(){if(t.params.loop||t.params.rewind||!t.navigation)return;const{nextEl:e,prevEl:s}=t.navigation;s&&(t.isBeginning?(h(s),c(s)):(v(s),d(s))),e&&(t.isEnd?(h(e),c(e)):(v(e),d(e)))}()})),a("paginationUpdate",(()=>{t.params.a11y.enabled&&function(){const e=t.params.a11y;w()&&t.pagination.bullets.forEach((s=>{t.params.pagination.clickable&&(d(s),t.params.pagination.renderBullet||(p(s,"button"),m(s,e.paginationBulletMessage.replace(/\{\{index\}\}/,I(s)+1)))),s.matches(Te(t.params.pagination.bulletActiveClass))?s.setAttribute("aria-current","true"):s.removeAttribute("aria-current")}))}()})),a("destroy",(()=>{t.params.a11y.enabled&&function(){l&&l.remove();let{nextEl:e,prevEl:s}=t.navigation?t.navigation:{};e=D(e),s=D(s),e&&e.forEach((e=>e.removeEventListener("keydown",g))),s&&s.forEach((e=>e.removeEventListener("keydown",g))),b()&&D(t.pagination.el).forEach((e=>{e.removeEventListener("keydown",g)}));f().removeEventListener("visibilitychange",x),t.el&&"string"!=typeof t.el&&(t.el.removeEventListener("focus",T,!0),t.el.removeEventListener("pointerdown",E,!0),t.el.removeEventListener("pointerup",S,!0))}()}))},Autoplay:function(e){let t,s,{swiper:a,extendParams:i,on:r,emit:l,params:n}=e;a.autoplay={running:!1,paused:!1,timeLeft:0},i({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let o,d,c,p,u,m,h,v,g=n&&n.autoplay?n.autoplay.delay:3e3,w=n&&n.autoplay?n.autoplay.delay:3e3,b=(new Date).getTime();function y(e){a&&!a.destroyed&&a.wrapperEl&&e.target===a.wrapperEl&&(a.wrapperEl.removeEventListener("transitionend",y),v||e.detail&&e.detail.bySwiperTouchMove||C())}const E=()=>{if(a.destroyed||!a.autoplay.running)return;a.autoplay.paused?d=!0:d&&(w=o,d=!1);const e=a.autoplay.paused?o:b+w-(new Date).getTime();a.autoplay.timeLeft=e,l("autoplayTimeLeft",e,e/g),s=requestAnimationFrame((()=>{E()}))},S=e=>{if(a.destroyed||!a.autoplay.running)return;cancelAnimationFrame(s),E();let i=void 0===e?a.params.autoplay.delay:e;g=a.params.autoplay.delay,w=a.params.autoplay.delay;const r=(()=>{let e;if(e=a.virtual&&a.params.virtual.enabled?a.slides.find((e=>e.classList.contains("swiper-slide-active"))):a.slides[a.activeIndex],!e)return;return parseInt(e.getAttribute("data-swiper-autoplay"),10)})();!Number.isNaN(r)&&r>0&&void 0===e&&(i=r,g=r,w=r),o=i;const n=a.params.speed,d=()=>{a&&!a.destroyed&&(a.params.autoplay.reverseDirection?!a.isBeginning||a.params.loop||a.params.rewind?(a.slidePrev(n,!0,!0),l("autoplay")):a.params.autoplay.stopOnLastSlide||(a.slideTo(a.slides.length-1,n,!0,!0),l("autoplay")):!a.isEnd||a.params.loop||a.params.rewind?(a.slideNext(n,!0,!0),l("autoplay")):a.params.autoplay.stopOnLastSlide||(a.slideTo(0,n,!0,!0),l("autoplay")),a.params.cssMode&&(b=(new Date).getTime(),requestAnimationFrame((()=>{S()}))))};return i>0?(clearTimeout(t),t=setTimeout((()=>{d()}),i)):requestAnimationFrame((()=>{d()})),i},x=()=>{b=(new Date).getTime(),a.autoplay.running=!0,S(),l("autoplayStart")},T=()=>{a.autoplay.running=!1,clearTimeout(t),cancelAnimationFrame(s),l("autoplayStop")},M=(e,s)=>{if(a.destroyed||!a.autoplay.running)return;clearTimeout(t),e||(h=!0);const i=()=>{l("autoplayPause"),a.params.autoplay.waitForTransition?a.wrapperEl.addEventListener("transitionend",y):C()};if(a.autoplay.paused=!0,s)return m&&(o=a.params.autoplay.delay),m=!1,void i();const r=o||a.params.autoplay.delay;o=r-((new Date).getTime()-b),a.isEnd&&o<0&&!a.params.loop||(o<0&&(o=0),i())},C=()=>{a.isEnd&&o<0&&!a.params.loop||a.destroyed||!a.autoplay.running||(b=(new Date).getTime(),h?(h=!1,S(o)):S(),a.autoplay.paused=!1,l("autoplayResume"))},P=()=>{if(a.destroyed||!a.autoplay.running)return;const e=f();"hidden"===e.visibilityState&&(h=!0,M(!0)),"visible"===e.visibilityState&&C()},L=e=>{"mouse"===e.pointerType&&(h=!0,v=!0,a.animating||a.autoplay.paused||M(!0))},O=e=>{"mouse"===e.pointerType&&(v=!1,a.autoplay.paused&&C())};r("init",(()=>{a.params.autoplay.enabled&&(a.params.autoplay.pauseOnMouseEnter&&(a.el.addEventListener("pointerenter",L),a.el.addEventListener("pointerleave",O)),f().addEventListener("visibilitychange",P),x())})),r("destroy",(()=>{a.el&&"string"!=typeof a.el&&(a.el.removeEventListener("pointerenter",L),a.el.removeEventListener("pointerleave",O)),f().removeEventListener("visibilitychange",P),a.autoplay.running&&T()})),r("_freeModeStaticRelease",(()=>{(p||h)&&C()})),r("_freeModeNoMomentumRelease",(()=>{a.params.autoplay.disableOnInteraction?T():M(!0,!0)})),r("beforeTransitionStart",((e,t,s)=>{!a.destroyed&&a.autoplay.running&&(s||!a.params.autoplay.disableOnInteraction?M(!0,!0):T())})),r("sliderFirstMove",(()=>{!a.destroyed&&a.autoplay.running&&(a.params.autoplay.disableOnInteraction?T():(c=!0,p=!1,h=!1,u=setTimeout((()=>{h=!0,p=!0,M(!0)}),200)))})),r("touchEnd",(()=>{if(!a.destroyed&&a.autoplay.running&&c){if(clearTimeout(u),clearTimeout(t),a.params.autoplay.disableOnInteraction)return p=!1,void(c=!1);p&&a.params.cssMode&&C(),p=!1,c=!1}})),r("slideChange",(()=>{!a.destroyed&&a.autoplay.running&&(m=!0)})),Object.assign(a.autoplay,{start:x,stop:T,pause:M,resume:C})},Controller:function(e){let{swiper:t,extendParams:s,on:a}=e;function i(e,t){const s=function(){let e,t,s;return(a,i)=>{for(t=-1,e=a.length;e-t>1;)s=e+t>>1,a[s]<=i?t=s:e=s;return e}}();let a,i;return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(i=s(this.x,e),a=i-1,(e-this.x[a])*(this.y[i]-this.y[a])/(this.x[i]-this.x[a])+this.y[a]):0},this}function r(){t.controller.control&&t.controller.spline&&(t.controller.spline=void 0,delete t.controller.spline)}s({controller:{control:void 0,inverse:!1,by:"slide"}}),t.controller={control:void 0},a("beforeInit",(()=>{if("undefined"!=typeof window&&("string"==typeof t.params.controller.control||t.params.controller.control instanceof HTMLElement)){("string"==typeof t.params.controller.control?[...document.querySelectorAll(t.params.controller.control)]:[t.params.controller.control]).forEach((e=>{if(t.controller.control||(t.controller.control=[]),e&&e.swiper)t.controller.control.push(e.swiper);else if(e){const s=`${t.params.eventsPrefix}init`,a=i=>{t.controller.control.push(i.detail[0]),t.update(),e.removeEventListener(s,a)};e.addEventListener(s,a)}}))}else t.controller.control=t.params.controller.control})),a("update",(()=>{r()})),a("resize",(()=>{r()})),a("observerUpdate",(()=>{r()})),a("setTranslate",((e,s,a)=>{t.controller.control&&!t.controller.control.destroyed&&t.controller.setTranslate(s,a)})),a("setTransition",((e,s,a)=>{t.controller.control&&!t.controller.control.destroyed&&t.controller.setTransition(s,a)})),Object.assign(t.controller,{setTranslate:function(e,s){const a=t.controller.control;let r,l;const n=t.constructor;function o(e){if(e.destroyed)return;const s=t.rtlTranslate?-t.translate:t.translate;"slide"===t.params.controller.by&&(!function(e){t.controller.spline=t.params.loop?new i(t.slidesGrid,e.slidesGrid):new i(t.snapGrid,e.snapGrid)}(e),l=-t.controller.spline.interpolate(-s)),l&&"container"!==t.params.controller.by||(r=(e.maxTranslate()-e.minTranslate())/(t.maxTranslate()-t.minTranslate()),!Number.isNaN(r)&&Number.isFinite(r)||(r=1),l=(s-t.minTranslate())*r+e.minTranslate()),t.params.controller.inverse&&(l=e.maxTranslate()-l),e.updateProgress(l),e.setTranslate(l,t),e.updateActiveIndex(),e.updateSlidesClasses()}if(Array.isArray(a))for(let t=0;t<a.length;t+=1)a[t]!==s&&a[t]instanceof n&&o(a[t]);else a instanceof n&&s!==a&&o(a)},setTransition:function(e,s){const a=t.constructor,i=t.controller.control;let r;function l(s){s.destroyed||(s.setTransition(e,t),0!==e&&(s.transitionStart(),s.params.autoHeight&&w((()=>{s.updateAutoHeight()})),A(s.wrapperEl,(()=>{i&&s.transitionEnd()}))))}if(Array.isArray(i))for(r=0;r<i.length;r+=1)i[r]!==s&&i[r]instanceof a&&l(i[r]);else i instanceof a&&s!==i&&l(i)}})},EffectCards:function(e){let{swiper:t,extendParams:s,on:a}=e;s({cardsEffect:{slideShadows:!0,rotate:!0,perSlideRotate:2,perSlideOffset:8}}),ze({effect:"cards",swiper:t,on:a,setTranslate:()=>{const{slides:e,activeIndex:s,rtlTranslate:a}=t,i=t.params.cardsEffect,{startTranslate:r,isTouched:l}=t.touchEventsData,n=a?-t.translate:t.translate;for(let o=0;o<e.length;o+=1){const d=e[o],c=d.progress,p=Math.min(Math.max(c,-4),4);let u=d.swiperSlideOffset;t.params.centeredSlides&&!t.params.cssMode&&(t.wrapperEl.style.transform=`translateX(${t.minTranslate()}px)`),t.params.centeredSlides&&t.params.cssMode&&(u-=e[0].swiperSlideOffset);let m=t.params.cssMode?-u-t.translate:-u,f=0;const h=-100*Math.abs(p);let v=1,g=-i.perSlideRotate*p,w=i.perSlideOffset-.75*Math.abs(p);const b=t.virtual&&t.params.virtual.enabled?t.virtual.from+o:o,y=(b===s||b===s-1)&&p>0&&p<1&&(l||t.params.cssMode)&&n<r,E=(b===s||b===s+1)&&p<0&&p>-1&&(l||t.params.cssMode)&&n>r;if(y||E){const e=(1-Math.abs((Math.abs(p)-.5)/.5))**.5;g+=-28*p*e,v+=-.5*e,w+=96*e,f=-25*e*Math.abs(p)+"%"}if(m=p<0?`calc(${m}px ${a?"-":"+"} (${w*Math.abs(p)}%))`:p>0?`calc(${m}px ${a?"-":"+"} (-${w*Math.abs(p)}%))`:`${m}px`,!t.isHorizontal()){const e=f;f=m,m=e}const S=p<0?""+(1+(1-v)*p):""+(1-(1-v)*p),x=`\n        translate3d(${m}, ${f}, ${h}px)\n        rotateZ(${i.rotate?a?-g:g:0}deg)\n        scale(${S})\n      `;if(i.slideShadows){let e=d.querySelector(".swiper-slide-shadow");e||(e=Ae("cards",d)),e&&(e.style.opacity=Math.min(Math.max((Math.abs(p)-.5)/.5,0),1))}d.style.zIndex=-Math.abs(Math.round(c))+e.length;Ie(0,d).style.transform=x}},setTransition:e=>{const s=t.slides.map((e=>M(e)));s.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),ke({swiper:t,duration:e,transformElements:s})},perspective:()=>!0,overwriteParams:()=>({_loopSwapReset:!1,watchSlidesProgress:!0,loopAdditionalSlides:3,centeredSlides:!0,virtualTranslate:!t.params.cssMode})})},EffectCoverflow:function(e){let{swiper:t,extendParams:s,on:a}=e;s({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}}),ze({effect:"coverflow",swiper:t,on:a,setTranslate:()=>{const{width:e,height:s,slides:a,slidesSizesGrid:i}=t,r=t.params.coverflowEffect,l=t.isHorizontal(),n=t.translate,o=l?e/2-n:s/2-n,d=l?r.rotate:-r.rotate,c=r.depth,p=B(t);for(let t=0,u=a.length;t<u;t+=1){const e=a[t],s=i[t],n=(o-e.swiperSlideOffset-s/2)/s,u="function"==typeof r.modifier?r.modifier(n):n*r.modifier;let m=l?d*u:0,f=l?0:d*u,h=-c*Math.abs(u),v=r.stretch;"string"==typeof v&&-1!==v.indexOf("%")&&(v=parseFloat(r.stretch)/100*s);let g=l?0:v*u,w=l?v*u:0,b=1-(1-r.scale)*Math.abs(u);Math.abs(w)<.001&&(w=0),Math.abs(g)<.001&&(g=0),Math.abs(h)<.001&&(h=0),Math.abs(m)<.001&&(m=0),Math.abs(f)<.001&&(f=0),Math.abs(b)<.001&&(b=0);const y=`translate3d(${w}px,${g}px,${h}px)  rotateX(${p(f)}deg) rotateY(${p(m)}deg) scale(${b})`;if(Ie(0,e).style.transform=y,e.style.zIndex=1-Math.abs(Math.round(u)),r.slideShadows){let t=l?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),s=l?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom");t||(t=Ae("coverflow",e,l?"left":"top")),s||(s=Ae("coverflow",e,l?"right":"bottom")),t&&(t.style.opacity=u>0?u:0),s&&(s.style.opacity=-u>0?-u:0)}}},setTransition:e=>{t.slides.map((e=>M(e))).forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((t=>{t.style.transitionDuration=`${e}ms`}))}))},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})},EffectCreative:function(e){let{swiper:t,extendParams:s,on:a}=e;s({creativeEffect:{limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});const i=e=>"string"==typeof e?e:`${e}px`;ze({effect:"creative",swiper:t,on:a,setTranslate:()=>{const{slides:e,wrapperEl:s,slidesSizesGrid:a}=t,r=t.params.creativeEffect,{progressMultiplier:l}=r,n=t.params.centeredSlides,o=B(t);if(n){const e=a[0]/2-t.params.slidesOffsetBefore||0;s.style.transform=`translateX(calc(50% - ${e}px))`}for(let d=0;d<e.length;d+=1){const s=e[d],a=s.progress,c=Math.min(Math.max(s.progress,-r.limitProgress),r.limitProgress);let p=c;n||(p=Math.min(Math.max(s.originalProgress,-r.limitProgress),r.limitProgress));const u=s.swiperSlideOffset,m=[t.params.cssMode?-u-t.translate:-u,0,0],f=[0,0,0];let h=!1;t.isHorizontal()||(m[1]=m[0],m[0]=0);let v={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};c<0?(v=r.next,h=!0):c>0&&(v=r.prev,h=!0),m.forEach(((e,t)=>{m[t]=`calc(${e}px + (${i(v.translate[t])} * ${Math.abs(c*l)}))`})),f.forEach(((e,t)=>{let s=v.rotate[t]*Math.abs(c*l);f[t]=s})),s.style.zIndex=-Math.abs(Math.round(a))+e.length;const g=m.join(", "),w=`rotateX(${o(f[0])}deg) rotateY(${o(f[1])}deg) rotateZ(${o(f[2])}deg)`,b=p<0?`scale(${1+(1-v.scale)*p*l})`:`scale(${1-(1-v.scale)*p*l})`,y=p<0?1+(1-v.opacity)*p*l:1-(1-v.opacity)*p*l,E=`translate3d(${g}) ${w} ${b}`;if(h&&v.shadow||!h){let e=s.querySelector(".swiper-slide-shadow");if(!e&&v.shadow&&(e=Ae("creative",s)),e){const t=r.shadowPerProgress?c*(1/r.limitProgress):c;e.style.opacity=Math.min(Math.max(Math.abs(t),0),1)}}const S=Ie(0,s);S.style.transform=E,S.style.opacity=y,v.origin&&(S.style.transformOrigin=v.origin)}},setTransition:e=>{const s=t.slides.map((e=>M(e)));s.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),ke({swiper:t,duration:e,transformElements:s,allSlides:!0})},perspective:()=>t.params.creativeEffect.perspective,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!t.params.cssMode})})},EffectCube:function(e){let{swiper:t,extendParams:s,on:a}=e;s({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}});const i=(e,t,s)=>{let a=s?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),i=s?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom");a||(a=L("div",("swiper-slide-shadow-cube swiper-slide-shadow-"+(s?"left":"top")).split(" ")),e.append(a)),i||(i=L("div",("swiper-slide-shadow-cube swiper-slide-shadow-"+(s?"right":"bottom")).split(" ")),e.append(i)),a&&(a.style.opacity=Math.max(-t,0)),i&&(i.style.opacity=Math.max(t,0))};ze({effect:"cube",swiper:t,on:a,setTranslate:()=>{const{el:e,wrapperEl:s,slides:a,width:r,height:l,rtlTranslate:n,size:o,browser:d}=t,c=B(t),p=t.params.cubeEffect,u=t.isHorizontal(),m=t.virtual&&t.params.virtual.enabled;let f,h=0;p.shadow&&(u?(f=t.wrapperEl.querySelector(".swiper-cube-shadow"),f||(f=L("div","swiper-cube-shadow"),t.wrapperEl.append(f)),f.style.height=`${r}px`):(f=e.querySelector(".swiper-cube-shadow"),f||(f=L("div","swiper-cube-shadow"),e.append(f))));for(let t=0;t<a.length;t+=1){const e=a[t];let s=t;m&&(s=parseInt(e.getAttribute("data-swiper-slide-index"),10));let r=90*s,l=Math.floor(r/360);n&&(r=-r,l=Math.floor(-r/360));const d=Math.max(Math.min(e.progress,1),-1);let f=0,v=0,g=0;s%4==0?(f=4*-l*o,g=0):(s-1)%4==0?(f=0,g=4*-l*o):(s-2)%4==0?(f=o+4*l*o,g=o):(s-3)%4==0&&(f=-o,g=3*o+4*o*l),n&&(f=-f),u||(v=f,f=0);const w=`rotateX(${c(u?0:-r)}deg) rotateY(${c(u?r:0)}deg) translate3d(${f}px, ${v}px, ${g}px)`;d<=1&&d>-1&&(h=90*s+90*d,n&&(h=90*-s-90*d)),e.style.transform=w,p.slideShadows&&i(e,d,u)}if(s.style.transformOrigin=`50% 50% -${o/2}px`,s.style["-webkit-transform-origin"]=`50% 50% -${o/2}px`,p.shadow)if(u)f.style.transform=`translate3d(0px, ${r/2+p.shadowOffset}px, ${-r/2}px) rotateX(89.99deg) rotateZ(0deg) scale(${p.shadowScale})`;else{const e=Math.abs(h)-90*Math.floor(Math.abs(h)/90),t=1.5-(Math.sin(2*e*Math.PI/360)/2+Math.cos(2*e*Math.PI/360)/2),s=p.shadowScale,a=p.shadowScale/t,i=p.shadowOffset;f.style.transform=`scale3d(${s}, 1, ${a}) translate3d(0px, ${l/2+i}px, ${-l/2/a}px) rotateX(-89.99deg)`}const v=(d.isSafari||d.isWebView)&&d.needPerspectiveFix?-o/2:0;s.style.transform=`translate3d(0px,0,${v}px) rotateX(${c(t.isHorizontal()?0:h)}deg) rotateY(${c(t.isHorizontal()?-h:0)}deg)`,s.style.setProperty("--swiper-cube-translate-z",`${v}px`)},setTransition:e=>{const{el:s,slides:a}=t;if(a.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),t.params.cubeEffect.shadow&&!t.isHorizontal()){const t=s.querySelector(".swiper-cube-shadow");t&&(t.style.transitionDuration=`${e}ms`)}},recreateShadows:()=>{const e=t.isHorizontal();t.slides.forEach((t=>{const s=Math.max(Math.min(t.progress,1),-1);i(t,s,e)}))},getEffectParams:()=>t.params.cubeEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0})})},EffectFade:function(e){let{swiper:t,extendParams:s,on:a}=e;s({fadeEffect:{crossFade:!1}}),ze({effect:"fade",swiper:t,on:a,setTranslate:()=>{const{slides:e}=t;t.params.fadeEffect;for(let s=0;s<e.length;s+=1){const e=t.slides[s];let a=-e.swiperSlideOffset;t.params.virtualTranslate||(a-=t.translate);let i=0;t.isHorizontal()||(i=a,a=0);const r=t.params.fadeEffect.crossFade?Math.max(1-Math.abs(e.progress),0):1+Math.min(Math.max(e.progress,-1),0),l=Ie(0,e);l.style.opacity=r,l.style.transform=`translate3d(${a}px, ${i}px, 0px)`}},setTransition:e=>{const s=t.slides.map((e=>M(e)));s.forEach((t=>{t.style.transitionDuration=`${e}ms`})),ke({swiper:t,duration:e,transformElements:s,allSlides:!0})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!t.params.cssMode})})},EffectFlip:function(e){let{swiper:t,extendParams:s,on:a}=e;s({flipEffect:{slideShadows:!0,limitRotation:!0}});const i=(e,s)=>{let a=t.isHorizontal()?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),i=t.isHorizontal()?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom");a||(a=Ae("flip",e,t.isHorizontal()?"left":"top")),i||(i=Ae("flip",e,t.isHorizontal()?"right":"bottom")),a&&(a.style.opacity=Math.max(-s,0)),i&&(i.style.opacity=Math.max(s,0))};ze({effect:"flip",swiper:t,on:a,setTranslate:()=>{const{slides:e,rtlTranslate:s}=t,a=t.params.flipEffect,r=B(t);for(let l=0;l<e.length;l+=1){const n=e[l];let o=n.progress;t.params.flipEffect.limitRotation&&(o=Math.max(Math.min(n.progress,1),-1));const d=n.swiperSlideOffset;let c=-180*o,p=0,u=t.params.cssMode?-d-t.translate:-d,m=0;t.isHorizontal()?s&&(c=-c):(m=u,u=0,p=-c,c=0),n.style.zIndex=-Math.abs(Math.round(o))+e.length,a.slideShadows&&i(n,o);const f=`translate3d(${u}px, ${m}px, 0px) rotateX(${r(p)}deg) rotateY(${r(c)}deg)`;Ie(0,n).style.transform=f}},setTransition:e=>{const s=t.slides.map((e=>M(e)));s.forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((t=>{t.style.transitionDuration=`${e}ms`}))})),ke({swiper:t,duration:e,transformElements:s})},recreateShadows:()=>{t.params.flipEffect,t.slides.forEach((e=>{let s=e.progress;t.params.flipEffect.limitRotation&&(s=Math.max(Math.min(e.progress,1),-1)),i(e,s)}))},getEffectParams:()=>t.params.flipEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!t.params.cssMode})})},FreeMode:function(e){let{swiper:t,extendParams:s,emit:a,once:i}=e;s({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}}),Object.assign(t,{freeMode:{onTouchStart:function(){if(t.params.cssMode)return;const e=t.getTranslate();t.setTranslate(e),t.setTransition(0),t.touchEventsData.velocities.length=0,t.freeMode.onTouchEnd({currentPos:t.rtl?t.translate:-t.translate})},onTouchMove:function(){if(t.params.cssMode)return;const{touchEventsData:e,touches:s}=t;0===e.velocities.length&&e.velocities.push({position:s[t.isHorizontal()?"startX":"startY"],time:e.touchStartTime}),e.velocities.push({position:s[t.isHorizontal()?"currentX":"currentY"],time:b()})},onTouchEnd:function(e){let{currentPos:s}=e;if(t.params.cssMode)return;const{params:r,wrapperEl:l,rtlTranslate:n,snapGrid:o,touchEventsData:d}=t,c=b()-d.touchStartTime;if(s<-t.minTranslate())t.slideTo(t.activeIndex);else if(s>-t.maxTranslate())t.slides.length<o.length?t.slideTo(o.length-1):t.slideTo(t.slides.length-1);else{if(r.freeMode.momentum){if(d.velocities.length>1){const e=d.velocities.pop(),s=d.velocities.pop(),a=e.position-s.position,i=e.time-s.time;t.velocity=a/i,t.velocity/=2,Math.abs(t.velocity)<r.freeMode.minimumVelocity&&(t.velocity=0),(i>150||b()-e.time>300)&&(t.velocity=0)}else t.velocity=0;t.velocity*=r.freeMode.momentumVelocityRatio,d.velocities.length=0;let e=1e3*r.freeMode.momentumRatio;const s=t.velocity*e;let c=t.translate+s;n&&(c=-c);let p,u=!1;const m=20*Math.abs(t.velocity)*r.freeMode.momentumBounceRatio;let f;if(c<t.maxTranslate())r.freeMode.momentumBounce?(c+t.maxTranslate()<-m&&(c=t.maxTranslate()-m),p=t.maxTranslate(),u=!0,d.allowMomentumBounce=!0):c=t.maxTranslate(),r.loop&&r.centeredSlides&&(f=!0);else if(c>t.minTranslate())r.freeMode.momentumBounce?(c-t.minTranslate()>m&&(c=t.minTranslate()+m),p=t.minTranslate(),u=!0,d.allowMomentumBounce=!0):c=t.minTranslate(),r.loop&&r.centeredSlides&&(f=!0);else if(r.freeMode.sticky){let e;for(let t=0;t<o.length;t+=1)if(o[t]>-c){e=t;break}c=Math.abs(o[e]-c)<Math.abs(o[e-1]-c)||"next"===t.swipeDirection?o[e]:o[e-1],c=-c}if(f&&i("transitionEnd",(()=>{t.loopFix()})),0!==t.velocity){if(e=n?Math.abs((-c-t.translate)/t.velocity):Math.abs((c-t.translate)/t.velocity),r.freeMode.sticky){const s=Math.abs((n?-c:c)-t.translate),a=t.slidesSizesGrid[t.activeIndex];e=s<a?r.speed:s<2*a?1.5*r.speed:2.5*r.speed}}else if(r.freeMode.sticky)return void t.slideToClosest();r.freeMode.momentumBounce&&u?(t.updateProgress(p),t.setTransition(e),t.setTranslate(c),t.transitionStart(!0,t.swipeDirection),t.animating=!0,A(l,(()=>{t&&!t.destroyed&&d.allowMomentumBounce&&(a("momentumBounce"),t.setTransition(r.speed),setTimeout((()=>{t.setTranslate(p),A(l,(()=>{t&&!t.destroyed&&t.transitionEnd()}))}),0))}))):t.velocity?(a("_freeModeNoMomentumRelease"),t.updateProgress(c),t.setTransition(e),t.setTranslate(c),t.transitionStart(!0,t.swipeDirection),t.animating||(t.animating=!0,A(l,(()=>{t&&!t.destroyed&&t.transitionEnd()})))):t.updateProgress(c),t.updateActiveIndex(),t.updateSlidesClasses()}else{if(r.freeMode.sticky)return void t.slideToClosest();r.freeMode&&a("_freeModeNoMomentumRelease")}(!r.freeMode.momentum||c>=r.longSwipesMs)&&(a("_freeModeStaticRelease"),t.updateProgress(),t.updateActiveIndex(),t.updateSlidesClasses())}}}})},Grid:function(e){let t,s,a,i,{swiper:r,extendParams:l,on:n}=e;l({grid:{rows:1,fill:"column"}});const o=()=>{let e=r.params.spaceBetween;return"string"==typeof e&&e.indexOf("%")>=0?e=parseFloat(e.replace("%",""))/100*r.size:"string"==typeof e&&(e=parseFloat(e)),e};n("init",(()=>{i=r.params.grid&&r.params.grid.rows>1})),n("update",(()=>{const{params:e,el:t}=r,s=e.grid&&e.grid.rows>1;i&&!s?(t.classList.remove(`${e.containerModifierClass}grid`,`${e.containerModifierClass}grid-column`),a=1,r.emitContainerClasses()):!i&&s&&(t.classList.add(`${e.containerModifierClass}grid`),"column"===e.grid.fill&&t.classList.add(`${e.containerModifierClass}grid-column`),r.emitContainerClasses()),i=s})),r.grid={initSlides:e=>{const{slidesPerView:i}=r.params,{rows:l,fill:n}=r.params.grid,o=r.virtual&&r.params.virtual.enabled?r.virtual.slides.length:e.length;a=Math.floor(o/l),t=Math.floor(o/l)===o/l?o:Math.ceil(o/l)*l,"auto"!==i&&"row"===n&&(t=Math.max(t,i*l)),s=t/l},unsetSlides:()=>{r.slides&&r.slides.forEach((e=>{e.swiperSlideGridSet&&(e.style.height="",e.style[r.getDirectionLabel("margin-top")]="")}))},updateSlide:(e,i,l)=>{const{slidesPerGroup:n}=r.params,d=o(),{rows:c,fill:p}=r.params.grid,u=r.virtual&&r.params.virtual.enabled?r.virtual.slides.length:l.length;let m,f,h;if("row"===p&&n>1){const s=Math.floor(e/(n*c)),a=e-c*n*s,r=0===s?n:Math.min(Math.ceil((u-s*c*n)/c),n);h=Math.floor(a/r),f=a-h*r+s*n,m=f+h*t/c,i.style.order=m}else"column"===p?(f=Math.floor(e/c),h=e-f*c,(f>a||f===a&&h===c-1)&&(h+=1,h>=c&&(h=0,f+=1))):(h=Math.floor(e/s),f=e-h*s);i.row=h,i.column=f,i.style.height=`calc((100% - ${(c-1)*d}px) / ${c})`,i.style[r.getDirectionLabel("margin-top")]=0!==h?d&&`${d}px`:"",i.swiperSlideGridSet=!0},updateWrapperSize:(e,s)=>{const{centeredSlides:a,roundLengths:i}=r.params,l=o(),{rows:n}=r.params.grid;if(r.virtualSize=(e+l)*t,r.virtualSize=Math.ceil(r.virtualSize/n)-l,r.params.cssMode||(r.wrapperEl.style[r.getDirectionLabel("width")]=`${r.virtualSize+l}px`),a){const e=[];for(let t=0;t<s.length;t+=1){let a=s[t];i&&(a=Math.floor(a)),s[t]<r.virtualSize+s[0]&&e.push(a)}s.splice(0,s.length),s.push(...e)}}}},HashNavigation:function(e){let{swiper:t,extendParams:s,emit:a,on:i}=e,r=!1;const l=f(),n=v();s({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1,getSlideIndex(e,s){if(t.virtual&&t.params.virtual.enabled){const e=t.slides.find((e=>e.getAttribute("data-hash")===s));if(!e)return 0;return parseInt(e.getAttribute("data-swiper-slide-index"),10)}return t.getSlideIndex(C(t.slidesEl,`.${t.params.slideClass}[data-hash="${s}"], swiper-slide[data-hash="${s}"]`)[0])}}});const o=()=>{a("hashChange");const e=l.location.hash.replace("#",""),s=t.virtual&&t.params.virtual.enabled?t.slidesEl.querySelector(`[data-swiper-slide-index="${t.activeIndex}"]`):t.slides[t.activeIndex];if(e!==(s?s.getAttribute("data-hash"):"")){const s=t.params.hashNavigation.getSlideIndex(t,e);if(void 0===s||Number.isNaN(s))return;t.slideTo(s)}},d=()=>{if(!r||!t.params.hashNavigation.enabled)return;const e=t.virtual&&t.params.virtual.enabled?t.slidesEl.querySelector(`[data-swiper-slide-index="${t.activeIndex}"]`):t.slides[t.activeIndex],s=e?e.getAttribute("data-hash")||e.getAttribute("data-history"):"";t.params.hashNavigation.replaceState&&n.history&&n.history.replaceState?(n.history.replaceState(null,null,`#${s}`||""),a("hashSet")):(l.location.hash=s||"",a("hashSet"))};i("init",(()=>{t.params.hashNavigation.enabled&&(()=>{if(!t.params.hashNavigation.enabled||t.params.history&&t.params.history.enabled)return;r=!0;const e=l.location.hash.replace("#","");if(e){const s=0,a=t.params.hashNavigation.getSlideIndex(t,e);t.slideTo(a||0,s,t.params.runCallbacksOnInit,!0)}t.params.hashNavigation.watchState&&n.addEventListener("hashchange",o)})()})),i("destroy",(()=>{t.params.hashNavigation.enabled&&t.params.hashNavigation.watchState&&n.removeEventListener("hashchange",o)})),i("transitionEnd _freeModeNoMomentumRelease",(()=>{r&&d()})),i("slideChange",(()=>{r&&t.params.cssMode&&d()}))},History:function(e){let{swiper:t,extendParams:s,on:a}=e;s({history:{enabled:!1,root:"",replaceState:!1,key:"slides",keepQuery:!1}});let i=!1,r={};const l=e=>e.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,""),n=e=>{const t=v();let s;s=e?new URL(e):t.location;const a=s.pathname.slice(1).split("/").filter((e=>""!==e)),i=a.length;return{key:a[i-2],value:a[i-1]}},o=(e,s)=>{const a=v();if(!i||!t.params.history.enabled)return;let r;r=t.params.url?new URL(t.params.url):a.location;const n=t.virtual&&t.params.virtual.enabled?t.slidesEl.querySelector(`[data-swiper-slide-index="${s}"]`):t.slides[s];let o=l(n.getAttribute("data-history"));if(t.params.history.root.length>0){let s=t.params.history.root;"/"===s[s.length-1]&&(s=s.slice(0,s.length-1)),o=`${s}/${e?`${e}/`:""}${o}`}else r.pathname.includes(e)||(o=`${e?`${e}/`:""}${o}`);t.params.history.keepQuery&&(o+=r.search);const d=a.history.state;d&&d.value===o||(t.params.history.replaceState?a.history.replaceState({value:o},null,o):a.history.pushState({value:o},null,o))},d=(e,s,a)=>{if(s)for(let i=0,r=t.slides.length;i<r;i+=1){const r=t.slides[i];if(l(r.getAttribute("data-history"))===s){const s=t.getSlideIndex(r);t.slideTo(s,e,a)}}else t.slideTo(0,e,a)},c=()=>{r=n(t.params.url),d(t.params.speed,r.value,!1)};a("init",(()=>{t.params.history.enabled&&(()=>{const e=v();if(t.params.history){if(!e.history||!e.history.pushState)return t.params.history.enabled=!1,void(t.params.hashNavigation.enabled=!0);i=!0,r=n(t.params.url),r.key||r.value?(d(0,r.value,t.params.runCallbacksOnInit),t.params.history.replaceState||e.addEventListener("popstate",c)):t.params.history.replaceState||e.addEventListener("popstate",c)}})()})),a("destroy",(()=>{t.params.history.enabled&&(()=>{const e=v();t.params.history.replaceState||e.removeEventListener("popstate",c)})()})),a("transitionEnd _freeModeNoMomentumRelease",(()=>{i&&o(t.params.history.key,t.activeIndex)})),a("slideChange",(()=>{i&&t.params.cssMode&&o(t.params.history.key,t.activeIndex)}))},Keyboard:function(e){let{swiper:t,extendParams:s,on:a,emit:i}=e;const r=f(),l=v();function n(e){if(!t.enabled)return;const{rtlTranslate:s}=t;let a=e;a.originalEvent&&(a=a.originalEvent);const n=a.keyCode||a.charCode,o=t.params.keyboard.pageUpDown,d=o&&33===n,c=o&&34===n,p=37===n,u=39===n,m=38===n,f=40===n;if(!t.allowSlideNext&&(t.isHorizontal()&&u||t.isVertical()&&f||c))return!1;if(!t.allowSlidePrev&&(t.isHorizontal()&&p||t.isVertical()&&m||d))return!1;if(!(a.shiftKey||a.altKey||a.ctrlKey||a.metaKey||r.activeElement&&r.activeElement.nodeName&&("input"===r.activeElement.nodeName.toLowerCase()||"textarea"===r.activeElement.nodeName.toLowerCase()))){if(t.params.keyboard.onlyInViewport&&(d||c||p||u||m||f)){let e=!1;if(k(t.el,`.${t.params.slideClass}, swiper-slide`).length>0&&0===k(t.el,`.${t.params.slideActiveClass}`).length)return;const a=t.el,i=a.clientWidth,r=a.clientHeight,n=l.innerWidth,o=l.innerHeight,d=O(a);s&&(d.left-=a.scrollLeft);const c=[[d.left,d.top],[d.left+i,d.top],[d.left,d.top+r],[d.left+i,d.top+r]];for(let t=0;t<c.length;t+=1){const s=c[t];if(s[0]>=0&&s[0]<=n&&s[1]>=0&&s[1]<=o){if(0===s[0]&&0===s[1])continue;e=!0}}if(!e)return}t.isHorizontal()?((d||c||p||u)&&(a.preventDefault?a.preventDefault():a.returnValue=!1),((c||u)&&!s||(d||p)&&s)&&t.slideNext(),((d||p)&&!s||(c||u)&&s)&&t.slidePrev()):((d||c||m||f)&&(a.preventDefault?a.preventDefault():a.returnValue=!1),(c||f)&&t.slideNext(),(d||m)&&t.slidePrev()),i("keyPress",n)}}function o(){t.keyboard.enabled||(r.addEventListener("keydown",n),t.keyboard.enabled=!0)}function d(){t.keyboard.enabled&&(r.removeEventListener("keydown",n),t.keyboard.enabled=!1)}t.keyboard={enabled:!1},s({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}}),a("init",(()=>{t.params.keyboard.enabled&&o()})),a("destroy",(()=>{t.keyboard.enabled&&d()})),Object.assign(t.keyboard,{enable:o,disable:d})},Manipulation:function(e){let{swiper:t}=e;Object.assign(t,{appendSlide:Me.bind(t),prependSlide:Ce.bind(t),addSlide:Pe.bind(t),removeSlide:Le.bind(t),removeAllSlides:Oe.bind(t)})},Mousewheel:function(e){let{swiper:t,extendParams:s,on:a,emit:i}=e;const r=v();let l;s({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null,noMousewheelClass:"swiper-no-mousewheel"}}),t.mousewheel={enabled:!1};let n,o=b();const d=[];function c(){t.enabled&&(t.mouseEntered=!0)}function p(){t.enabled&&(t.mouseEntered=!1)}function u(e){return!(t.params.mousewheel.thresholdDelta&&e.delta<t.params.mousewheel.thresholdDelta)&&(!(t.params.mousewheel.thresholdTime&&b()-o<t.params.mousewheel.thresholdTime)&&(e.delta>=6&&b()-o<60||(e.direction<0?t.isEnd&&!t.params.loop||t.animating||(t.slideNext(),i("scroll",e.raw)):t.isBeginning&&!t.params.loop||t.animating||(t.slidePrev(),i("scroll",e.raw)),o=(new r.Date).getTime(),!1)))}function m(e){let s=e,a=!0;if(!t.enabled)return;if(e.target.closest(`.${t.params.mousewheel.noMousewheelClass}`))return;const r=t.params.mousewheel;t.params.cssMode&&s.preventDefault();let o=t.el;"container"!==t.params.mousewheel.eventsTarget&&(o=document.querySelector(t.params.mousewheel.eventsTarget));const c=o&&o.contains(s.target);if(!t.mouseEntered&&!c&&!r.releaseOnEdges)return!0;s.originalEvent&&(s=s.originalEvent);let p=0;const m=t.rtlTranslate?-1:1,f=function(e){let t=0,s=0,a=0,i=0;return"detail"in e&&(s=e.detail),"wheelDelta"in e&&(s=-e.wheelDelta/120),"wheelDeltaY"in e&&(s=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(t=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=s,s=0),a=10*t,i=10*s,"deltaY"in e&&(i=e.deltaY),"deltaX"in e&&(a=e.deltaX),e.shiftKey&&!a&&(a=i,i=0),(a||i)&&e.deltaMode&&(1===e.deltaMode?(a*=40,i*=40):(a*=800,i*=800)),a&&!t&&(t=a<1?-1:1),i&&!s&&(s=i<1?-1:1),{spinX:t,spinY:s,pixelX:a,pixelY:i}}(s);if(r.forceToAxis)if(t.isHorizontal()){if(!(Math.abs(f.pixelX)>Math.abs(f.pixelY)))return!0;p=-f.pixelX*m}else{if(!(Math.abs(f.pixelY)>Math.abs(f.pixelX)))return!0;p=-f.pixelY}else p=Math.abs(f.pixelX)>Math.abs(f.pixelY)?-f.pixelX*m:-f.pixelY;if(0===p)return!0;r.invert&&(p=-p);let h=t.getTranslate()+p*r.sensitivity;if(h>=t.minTranslate()&&(h=t.minTranslate()),h<=t.maxTranslate()&&(h=t.maxTranslate()),a=!!t.params.loop||!(h===t.minTranslate()||h===t.maxTranslate()),a&&t.params.nested&&s.stopPropagation(),t.params.freeMode&&t.params.freeMode.enabled){const e={time:b(),delta:Math.abs(p),direction:Math.sign(p)},a=n&&e.time<n.time+500&&e.delta<=n.delta&&e.direction===n.direction;if(!a){n=void 0;let o=t.getTranslate()+p*r.sensitivity;const c=t.isBeginning,u=t.isEnd;if(o>=t.minTranslate()&&(o=t.minTranslate()),o<=t.maxTranslate()&&(o=t.maxTranslate()),t.setTransition(0),t.setTranslate(o),t.updateProgress(),t.updateActiveIndex(),t.updateSlidesClasses(),(!c&&t.isBeginning||!u&&t.isEnd)&&t.updateSlidesClasses(),t.params.loop&&t.loopFix({direction:e.direction<0?"next":"prev",byMousewheel:!0}),t.params.freeMode.sticky){clearTimeout(l),l=void 0,d.length>=15&&d.shift();const s=d.length?d[d.length-1]:void 0,a=d[0];if(d.push(e),s&&(e.delta>s.delta||e.direction!==s.direction))d.splice(0);else if(d.length>=15&&e.time-a.time<500&&a.delta-e.delta>=1&&e.delta<=6){const s=p>0?.8:.2;n=e,d.splice(0),l=w((()=>{!t.destroyed&&t.params&&t.slideToClosest(t.params.speed,!0,void 0,s)}),0)}l||(l=w((()=>{if(t.destroyed||!t.params)return;n=e,d.splice(0),t.slideToClosest(t.params.speed,!0,void 0,.5)}),500))}if(a||i("scroll",s),t.params.autoplay&&t.params.autoplay.disableOnInteraction&&t.autoplay.stop(),r.releaseOnEdges&&(o===t.minTranslate()||o===t.maxTranslate()))return!0}}else{const s={time:b(),delta:Math.abs(p),direction:Math.sign(p),raw:e};d.length>=2&&d.shift();const a=d.length?d[d.length-1]:void 0;if(d.push(s),a?(s.direction!==a.direction||s.delta>a.delta||s.time>a.time+150)&&u(s):u(s),function(e){const s=t.params.mousewheel;if(e.direction<0){if(t.isEnd&&!t.params.loop&&s.releaseOnEdges)return!0}else if(t.isBeginning&&!t.params.loop&&s.releaseOnEdges)return!0;return!1}(s))return!0}return s.preventDefault?s.preventDefault():s.returnValue=!1,!1}function f(e){let s=t.el;"container"!==t.params.mousewheel.eventsTarget&&(s=document.querySelector(t.params.mousewheel.eventsTarget)),s[e]("mouseenter",c),s[e]("mouseleave",p),s[e]("wheel",m)}function h(){return t.params.cssMode?(t.wrapperEl.removeEventListener("wheel",m),!0):!t.mousewheel.enabled&&(f("addEventListener"),t.mousewheel.enabled=!0,!0)}function g(){return t.params.cssMode?(t.wrapperEl.addEventListener(event,m),!0):!!t.mousewheel.enabled&&(f("removeEventListener"),t.mousewheel.enabled=!1,!0)}a("init",(()=>{!t.params.mousewheel.enabled&&t.params.cssMode&&g(),t.params.mousewheel.enabled&&h()})),a("destroy",(()=>{t.params.cssMode&&h(),t.mousewheel.enabled&&g()})),Object.assign(t.mousewheel,{enable:h,disable:g})},Navigation:function(e){let{swiper:t,extendParams:s,on:a,emit:i}=e;function r(e){let s;return e&&"string"==typeof e&&t.isElement&&(s=t.el.querySelector(e)||t.hostEl.querySelector(e),s)?s:(e&&("string"==typeof e&&(s=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&s&&s.length>1&&1===t.el.querySelectorAll(e).length?s=t.el.querySelector(e):s&&1===s.length&&(s=s[0])),e&&!s?e:s)}function l(e,s){const a=t.params.navigation;(e=D(e)).forEach((e=>{e&&(e.classList[s?"add":"remove"](...a.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=s),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](a.lockClass))}))}function n(){const{nextEl:e,prevEl:s}=t.navigation;if(t.params.loop)return l(s,!1),void l(e,!1);l(s,t.isBeginning&&!t.params.rewind),l(e,t.isEnd&&!t.params.rewind)}function o(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),i("navigationPrev"))}function d(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),i("navigationNext"))}function c(){const e=t.params.navigation;if(t.params.navigation=xe(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!e.nextEl&&!e.prevEl)return;let s=r(e.nextEl),a=r(e.prevEl);Object.assign(t.navigation,{nextEl:s,prevEl:a}),s=D(s),a=D(a);const i=(s,a)=>{s&&s.addEventListener("click","next"===a?d:o),!t.enabled&&s&&s.classList.add(...e.lockClass.split(" "))};s.forEach((e=>i(e,"next"))),a.forEach((e=>i(e,"prev")))}function p(){let{nextEl:e,prevEl:s}=t.navigation;e=D(e),s=D(s);const a=(e,s)=>{e.removeEventListener("click","next"===s?d:o),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach((e=>a(e,"next"))),s.forEach((e=>a(e,"prev")))}s({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},a("init",(()=>{!1===t.params.navigation.enabled?u():(c(),n())})),a("toEdge fromEdge lock unlock",(()=>{n()})),a("destroy",(()=>{p()})),a("enable disable",(()=>{let{nextEl:e,prevEl:s}=t.navigation;e=D(e),s=D(s),t.enabled?n():[...e,...s].filter((e=>!!e)).forEach((e=>e.classList.add(t.params.navigation.lockClass)))})),a("click",((e,s)=>{let{nextEl:a,prevEl:r}=t.navigation;a=D(a),r=D(r);const l=s.target;let n=r.includes(l)||a.includes(l);if(t.isElement&&!n){const e=s.path||s.composedPath&&s.composedPath();e&&(n=e.find((e=>a.includes(e)||r.includes(e))))}if(t.params.navigation.hideOnClick&&!n){if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===l||t.pagination.el.contains(l)))return;let e;a.length?e=a[0].classList.contains(t.params.navigation.hiddenClass):r.length&&(e=r[0].classList.contains(t.params.navigation.hiddenClass)),i(!0===e?"navigationShow":"navigationHide"),[...a,...r].filter((e=>!!e)).forEach((e=>e.classList.toggle(t.params.navigation.hiddenClass)))}}));const u=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),p()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),c(),n()},disable:u,update:n,init:c,destroy:p})},Pagination:function(e){let{swiper:t,extendParams:s,on:a,emit:i}=e;const r="swiper-pagination";let l;s({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${r}-bullet`,bulletActiveClass:`${r}-bullet-active`,modifierClass:`${r}-`,currentClass:`${r}-current`,totalClass:`${r}-total`,hiddenClass:`${r}-hidden`,progressbarFillClass:`${r}-progressbar-fill`,progressbarOppositeClass:`${r}-progressbar-opposite`,clickableClass:`${r}-clickable`,lockClass:`${r}-lock`,horizontalClass:`${r}-horizontal`,verticalClass:`${r}-vertical`,paginationDisabledClass:`${r}-disabled`}}),t.pagination={el:null,bullets:[]};let n=0;function o(){return!t.params.pagination.el||!t.pagination.el||Array.isArray(t.pagination.el)&&0===t.pagination.el.length}function d(e,s){const{bulletActiveClass:a}=t.params.pagination;e&&(e=e[("prev"===s?"previous":"next")+"ElementSibling"])&&(e.classList.add(`${a}-${s}`),(e=e[("prev"===s?"previous":"next")+"ElementSibling"])&&e.classList.add(`${a}-${s}-${s}`))}function c(e){const s=e.target.closest(Te(t.params.pagination.bulletClass));if(!s)return;e.preventDefault();const a=I(s)*t.params.slidesPerGroup;if(t.params.loop){if(t.realIndex===a)return;const e=(i=t.realIndex,r=a,l=t.slides.length,(r%=l)==1+(i%=l)?"next":r===i-1?"previous":void 0);"next"===e?t.slideNext():"previous"===e?t.slidePrev():t.slideToLoop(a)}else t.slideTo(a);var i,r,l}function p(){const e=t.rtl,s=t.params.pagination;if(o())return;let a,r,c=t.pagination.el;c=D(c);const p=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,u=t.params.loop?Math.ceil(p/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(r=t.previousRealIndex||0,a=t.params.slidesPerGroup>1?Math.floor(t.realIndex/t.params.slidesPerGroup):t.realIndex):void 0!==t.snapIndex?(a=t.snapIndex,r=t.previousSnapIndex):(r=t.previousIndex||0,a=t.activeIndex||0),"bullets"===s.type&&t.pagination.bullets&&t.pagination.bullets.length>0){const i=t.pagination.bullets;let o,p,u;if(s.dynamicBullets&&(l=$(i[0],t.isHorizontal()?"width":"height"),c.forEach((e=>{e.style[t.isHorizontal()?"width":"height"]=l*(s.dynamicMainBullets+4)+"px"})),s.dynamicMainBullets>1&&void 0!==r&&(n+=a-(r||0),n>s.dynamicMainBullets-1?n=s.dynamicMainBullets-1:n<0&&(n=0)),o=Math.max(a-n,0),p=o+(Math.min(i.length,s.dynamicMainBullets)-1),u=(p+o)/2),i.forEach((e=>{const t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map((e=>`${s.bulletActiveClass}${e}`))].map((e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e)).flat();e.classList.remove(...t)})),c.length>1)i.forEach((e=>{const i=I(e);i===a?e.classList.add(...s.bulletActiveClass.split(" ")):t.isElement&&e.setAttribute("part","bullet"),s.dynamicBullets&&(i>=o&&i<=p&&e.classList.add(...`${s.bulletActiveClass}-main`.split(" ")),i===o&&d(e,"prev"),i===p&&d(e,"next"))}));else{const e=i[a];if(e&&e.classList.add(...s.bulletActiveClass.split(" ")),t.isElement&&i.forEach(((e,t)=>{e.setAttribute("part",t===a?"bullet-active":"bullet")})),s.dynamicBullets){const e=i[o],t=i[p];for(let a=o;a<=p;a+=1)i[a]&&i[a].classList.add(...`${s.bulletActiveClass}-main`.split(" "));d(e,"prev"),d(t,"next")}}if(s.dynamicBullets){const a=Math.min(i.length,s.dynamicMainBullets+4),r=(l*a-l)/2-u*l,n=e?"right":"left";i.forEach((e=>{e.style[t.isHorizontal()?n:"top"]=`${r}px`}))}}c.forEach(((e,r)=>{if("fraction"===s.type&&(e.querySelectorAll(Te(s.currentClass)).forEach((e=>{e.textContent=s.formatFractionCurrent(a+1)})),e.querySelectorAll(Te(s.totalClass)).forEach((e=>{e.textContent=s.formatFractionTotal(u)}))),"progressbar"===s.type){let i;i=s.progressbarOpposite?t.isHorizontal()?"vertical":"horizontal":t.isHorizontal()?"horizontal":"vertical";const r=(a+1)/u;let l=1,n=1;"horizontal"===i?l=r:n=r,e.querySelectorAll(Te(s.progressbarFillClass)).forEach((e=>{e.style.transform=`translate3d(0,0,0) scaleX(${l}) scaleY(${n})`,e.style.transitionDuration=`${t.params.speed}ms`}))}"custom"===s.type&&s.renderCustom?(e.innerHTML=s.renderCustom(t,a+1,u),0===r&&i("paginationRender",e)):(0===r&&i("paginationRender",e),i("paginationUpdate",e)),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](s.lockClass)}))}function u(){const e=t.params.pagination;if(o())return;const s=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.grid&&t.params.grid.rows>1?t.slides.length/Math.ceil(t.params.grid.rows):t.slides.length;let a=t.pagination.el;a=D(a);let r="";if("bullets"===e.type){let a=t.params.loop?Math.ceil(s/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&a>s&&(a=s);for(let s=0;s<a;s+=1)e.renderBullet?r+=e.renderBullet.call(t,s,e.bulletClass):r+=`<${e.bulletElement} ${t.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(r=e.renderFraction?e.renderFraction.call(t,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(r=e.renderProgressbar?e.renderProgressbar.call(t,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),t.pagination.bullets=[],a.forEach((s=>{"custom"!==e.type&&(s.innerHTML=r||""),"bullets"===e.type&&t.pagination.bullets.push(...s.querySelectorAll(Te(e.bulletClass)))})),"custom"!==e.type&&i("paginationRender",a[0])}function m(){t.params.pagination=xe(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const e=t.params.pagination;if(!e.el)return;let s;"string"==typeof e.el&&t.isElement&&(s=t.el.querySelector(e.el)),s||"string"!=typeof e.el||(s=[...document.querySelectorAll(e.el)]),s||(s=e.el),s&&0!==s.length&&(t.params.uniqueNavElements&&"string"==typeof e.el&&Array.isArray(s)&&s.length>1&&(s=[...t.el.querySelectorAll(e.el)],s.length>1&&(s=s.find((e=>k(e,".swiper")[0]===t.el)))),Array.isArray(s)&&1===s.length&&(s=s[0]),Object.assign(t.pagination,{el:s}),s=D(s),s.forEach((s=>{"bullets"===e.type&&e.clickable&&s.classList.add(...(e.clickableClass||"").split(" ")),s.classList.add(e.modifierClass+e.type),s.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass),"bullets"===e.type&&e.dynamicBullets&&(s.classList.add(`${e.modifierClass}${e.type}-dynamic`),n=0,e.dynamicMainBullets<1&&(e.dynamicMainBullets=1)),"progressbar"===e.type&&e.progressbarOpposite&&s.classList.add(e.progressbarOppositeClass),e.clickable&&s.addEventListener("click",c),t.enabled||s.classList.add(e.lockClass)})))}function f(){const e=t.params.pagination;if(o())return;let s=t.pagination.el;s&&(s=D(s),s.forEach((s=>{s.classList.remove(e.hiddenClass),s.classList.remove(e.modifierClass+e.type),s.classList.remove(t.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(s.classList.remove(...(e.clickableClass||"").split(" ")),s.removeEventListener("click",c))}))),t.pagination.bullets&&t.pagination.bullets.forEach((t=>t.classList.remove(...e.bulletActiveClass.split(" "))))}a("changeDirection",(()=>{if(!t.pagination||!t.pagination.el)return;const e=t.params.pagination;let{el:s}=t.pagination;s=D(s),s.forEach((s=>{s.classList.remove(e.horizontalClass,e.verticalClass),s.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass)}))})),a("init",(()=>{!1===t.params.pagination.enabled?h():(m(),u(),p())})),a("activeIndexChange",(()=>{void 0===t.snapIndex&&p()})),a("snapIndexChange",(()=>{p()})),a("snapGridLengthChange",(()=>{u(),p()})),a("destroy",(()=>{f()})),a("enable disable",(()=>{let{el:e}=t.pagination;e&&(e=D(e),e.forEach((e=>e.classList[t.enabled?"remove":"add"](t.params.pagination.lockClass))))})),a("lock unlock",(()=>{p()})),a("click",((e,s)=>{const a=s.target,r=D(t.pagination.el);if(t.params.pagination.el&&t.params.pagination.hideOnClick&&r&&r.length>0&&!a.classList.contains(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&a===t.navigation.nextEl||t.navigation.prevEl&&a===t.navigation.prevEl))return;const e=r[0].classList.contains(t.params.pagination.hiddenClass);i(!0===e?"paginationShow":"paginationHide"),r.forEach((e=>e.classList.toggle(t.params.pagination.hiddenClass)))}}));const h=()=>{t.el.classList.add(t.params.pagination.paginationDisabledClass);let{el:e}=t.pagination;e&&(e=D(e),e.forEach((e=>e.classList.add(t.params.pagination.paginationDisabledClass)))),f()};Object.assign(t.pagination,{enable:()=>{t.el.classList.remove(t.params.pagination.paginationDisabledClass);let{el:e}=t.pagination;e&&(e=D(e),e.forEach((e=>e.classList.remove(t.params.pagination.paginationDisabledClass)))),m(),u(),p()},disable:h,render:u,update:p,init:m,destroy:f})},Parallax:function(e){let{swiper:t,extendParams:s,on:a}=e;s({parallax:{enabled:!1}});const i="[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]",r=(e,s)=>{const{rtl:a}=t,i=a?-1:1,r=e.getAttribute("data-swiper-parallax")||"0";let l=e.getAttribute("data-swiper-parallax-x"),n=e.getAttribute("data-swiper-parallax-y");const o=e.getAttribute("data-swiper-parallax-scale"),d=e.getAttribute("data-swiper-parallax-opacity"),c=e.getAttribute("data-swiper-parallax-rotate");if(l||n?(l=l||"0",n=n||"0"):t.isHorizontal()?(l=r,n="0"):(n=r,l="0"),l=l.indexOf("%")>=0?parseInt(l,10)*s*i+"%":l*s*i+"px",n=n.indexOf("%")>=0?parseInt(n,10)*s+"%":n*s+"px",null!=d){const t=d-(d-1)*(1-Math.abs(s));e.style.opacity=t}let p=`translate3d(${l}, ${n}, 0px)`;if(null!=o){p+=` scale(${o-(o-1)*(1-Math.abs(s))})`}if(c&&null!=c){p+=` rotate(${c*s*-1}deg)`}e.style.transform=p},l=()=>{const{el:e,slides:s,progress:a,snapGrid:l,isElement:n}=t,o=C(e,i);t.isElement&&o.push(...C(t.hostEl,i)),o.forEach((e=>{r(e,a)})),s.forEach(((e,s)=>{let n=e.progress;t.params.slidesPerGroup>1&&"auto"!==t.params.slidesPerView&&(n+=Math.ceil(s/2)-a*(l.length-1)),n=Math.min(Math.max(n,-1),1),e.querySelectorAll(`${i}, [data-swiper-parallax-rotate]`).forEach((e=>{r(e,n)}))}))};a("beforeInit",(()=>{t.params.parallax.enabled&&(t.params.watchSlidesProgress=!0,t.originalParams.watchSlidesProgress=!0)})),a("init",(()=>{t.params.parallax.enabled&&l()})),a("setTranslate",(()=>{t.params.parallax.enabled&&l()})),a("setTransition",((e,s)=>{t.params.parallax.enabled&&function(e){void 0===e&&(e=t.params.speed);const{el:s,hostEl:a}=t,r=[...s.querySelectorAll(i)];t.isElement&&r.push(...a.querySelectorAll(i)),r.forEach((t=>{let s=parseInt(t.getAttribute("data-swiper-parallax-duration"),10)||e;0===e&&(s=0),t.style.transitionDuration=`${s}ms`}))}(s)}))},Scrollbar:function(e){let{swiper:t,extendParams:s,on:a,emit:i}=e;const r=f();let l,n,o,d,c=!1,p=null,u=null;function m(){if(!t.params.scrollbar.el||!t.scrollbar.el)return;const{scrollbar:e,rtlTranslate:s}=t,{dragEl:a,el:i}=e,r=t.params.scrollbar,l=t.params.loop?t.progressLoop:t.progress;let d=n,c=(o-n)*l;s?(c=-c,c>0?(d=n-c,c=0):-c+n>o&&(d=o+c)):c<0?(d=n+c,c=0):c+n>o&&(d=o-c),t.isHorizontal()?(a.style.transform=`translate3d(${c}px, 0, 0)`,a.style.width=`${d}px`):(a.style.transform=`translate3d(0px, ${c}px, 0)`,a.style.height=`${d}px`),r.hide&&(clearTimeout(p),i.style.opacity=1,p=setTimeout((()=>{i.style.opacity=0,i.style.transitionDuration="400ms"}),1e3))}function h(){if(!t.params.scrollbar.el||!t.scrollbar.el)return;const{scrollbar:e}=t,{dragEl:s,el:a}=e;s.style.width="",s.style.height="",o=t.isHorizontal()?a.offsetWidth:a.offsetHeight,d=t.size/(t.virtualSize+t.params.slidesOffsetBefore-(t.params.centeredSlides?t.snapGrid[0]:0)),n="auto"===t.params.scrollbar.dragSize?o*d:parseInt(t.params.scrollbar.dragSize,10),t.isHorizontal()?s.style.width=`${n}px`:s.style.height=`${n}px`,a.style.display=d>=1?"none":"",t.params.scrollbar.hide&&(a.style.opacity=0),t.params.watchOverflow&&t.enabled&&e.el.classList[t.isLocked?"add":"remove"](t.params.scrollbar.lockClass)}function v(e){return t.isHorizontal()?e.clientX:e.clientY}function b(e){const{scrollbar:s,rtlTranslate:a}=t,{el:i}=s;let r;r=(v(e)-O(i)[t.isHorizontal()?"left":"top"]-(null!==l?l:n/2))/(o-n),r=Math.max(Math.min(r,1),0),a&&(r=1-r);const d=t.minTranslate()+(t.maxTranslate()-t.minTranslate())*r;t.updateProgress(d),t.setTranslate(d),t.updateActiveIndex(),t.updateSlidesClasses()}function y(e){const s=t.params.scrollbar,{scrollbar:a,wrapperEl:r}=t,{el:n,dragEl:o}=a;c=!0,l=e.target===o?v(e)-e.target.getBoundingClientRect()[t.isHorizontal()?"left":"top"]:null,e.preventDefault(),e.stopPropagation(),r.style.transitionDuration="100ms",o.style.transitionDuration="100ms",b(e),clearTimeout(u),n.style.transitionDuration="0ms",s.hide&&(n.style.opacity=1),t.params.cssMode&&(t.wrapperEl.style["scroll-snap-type"]="none"),i("scrollbarDragStart",e)}function E(e){const{scrollbar:s,wrapperEl:a}=t,{el:r,dragEl:l}=s;c&&(e.preventDefault&&e.cancelable?e.preventDefault():e.returnValue=!1,b(e),a.style.transitionDuration="0ms",r.style.transitionDuration="0ms",l.style.transitionDuration="0ms",i("scrollbarDragMove",e))}function S(e){const s=t.params.scrollbar,{scrollbar:a,wrapperEl:r}=t,{el:l}=a;c&&(c=!1,t.params.cssMode&&(t.wrapperEl.style["scroll-snap-type"]="",r.style.transitionDuration=""),s.hide&&(clearTimeout(u),u=w((()=>{l.style.opacity=0,l.style.transitionDuration="400ms"}),1e3)),i("scrollbarDragEnd",e),s.snapOnRelease&&t.slideToClosest())}function x(e){const{scrollbar:s,params:a}=t,i=s.el;if(!i)return;const l=i,n=!!a.passiveListeners&&{passive:!1,capture:!1},o=!!a.passiveListeners&&{passive:!0,capture:!1};if(!l)return;const d="on"===e?"addEventListener":"removeEventListener";l[d]("pointerdown",y,n),r[d]("pointermove",E,n),r[d]("pointerup",S,o)}function T(){const{scrollbar:e,el:s}=t;t.params.scrollbar=xe(t,t.originalParams.scrollbar,t.params.scrollbar,{el:"swiper-scrollbar"});const a=t.params.scrollbar;if(!a.el)return;let i,l;if("string"==typeof a.el&&t.isElement&&(i=t.el.querySelector(a.el)),i||"string"!=typeof a.el)i||(i=a.el);else if(i=r.querySelectorAll(a.el),!i.length)return;t.params.uniqueNavElements&&"string"==typeof a.el&&i.length>1&&1===s.querySelectorAll(a.el).length&&(i=s.querySelector(a.el)),i.length>0&&(i=i[0]),i.classList.add(t.isHorizontal()?a.horizontalClass:a.verticalClass),i&&(l=i.querySelector(Te(t.params.scrollbar.dragClass)),l||(l=L("div",t.params.scrollbar.dragClass),i.append(l))),Object.assign(e,{el:i,dragEl:l}),a.draggable&&t.params.scrollbar.el&&t.scrollbar.el&&x("on"),i&&i.classList[t.enabled?"remove":"add"](...g(t.params.scrollbar.lockClass))}function M(){const e=t.params.scrollbar,s=t.scrollbar.el;s&&s.classList.remove(...g(t.isHorizontal()?e.horizontalClass:e.verticalClass)),t.params.scrollbar.el&&t.scrollbar.el&&x("off")}s({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),t.scrollbar={el:null,dragEl:null},a("changeDirection",(()=>{if(!t.scrollbar||!t.scrollbar.el)return;const e=t.params.scrollbar;let{el:s}=t.scrollbar;s=D(s),s.forEach((s=>{s.classList.remove(e.horizontalClass,e.verticalClass),s.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass)}))})),a("init",(()=>{!1===t.params.scrollbar.enabled?C():(T(),h(),m())})),a("update resize observerUpdate lock unlock changeDirection",(()=>{h()})),a("setTranslate",(()=>{m()})),a("setTransition",((e,s)=>{!function(e){t.params.scrollbar.el&&t.scrollbar.el&&(t.scrollbar.dragEl.style.transitionDuration=`${e}ms`)}(s)})),a("enable disable",(()=>{const{el:e}=t.scrollbar;e&&e.classList[t.enabled?"remove":"add"](...g(t.params.scrollbar.lockClass))})),a("destroy",(()=>{M()}));const C=()=>{t.el.classList.add(...g(t.params.scrollbar.scrollbarDisabledClass)),t.scrollbar.el&&t.scrollbar.el.classList.add(...g(t.params.scrollbar.scrollbarDisabledClass)),M()};Object.assign(t.scrollbar,{enable:()=>{t.el.classList.remove(...g(t.params.scrollbar.scrollbarDisabledClass)),t.scrollbar.el&&t.scrollbar.el.classList.remove(...g(t.params.scrollbar.scrollbarDisabledClass)),T(),h(),m()},disable:C,updateSize:h,setTranslate:m,init:T,destroy:M})},Thumbs:function(e){let{swiper:t,extendParams:s,on:a}=e;s({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let i=!1,r=!1;function l(){const e=t.thumbs.swiper;if(!e||e.destroyed)return;const s=e.clickedIndex,a=e.clickedSlide;if(a&&a.classList.contains(t.params.thumbs.slideThumbActiveClass))return;if(null==s)return;let i;i=e.params.loop?parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10):s,t.params.loop?t.slideToLoop(i):t.slideTo(i)}function n(){const{thumbs:e}=t.params;if(i)return!1;i=!0;const s=t.constructor;if(e.swiper instanceof s){if(e.swiper.destroyed)return i=!1,!1;t.thumbs.swiper=e.swiper,Object.assign(t.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(t.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),t.thumbs.swiper.update()}else if(E(e.swiper)){const a=Object.assign({},e.swiper);Object.assign(a,{watchSlidesProgress:!0,slideToClickedSlide:!1}),t.thumbs.swiper=new s(a),r=!0}return t.thumbs.swiper.el.classList.add(t.params.thumbs.thumbsContainerClass),t.thumbs.swiper.on("tap",l),!0}function o(e){const s=t.thumbs.swiper;if(!s||s.destroyed)return;const a="auto"===s.params.slidesPerView?s.slidesPerViewDynamic():s.params.slidesPerView;let i=1;const r=t.params.thumbs.slideThumbActiveClass;if(t.params.slidesPerView>1&&!t.params.centeredSlides&&(i=t.params.slidesPerView),t.params.thumbs.multipleActiveThumbs||(i=1),i=Math.floor(i),s.slides.forEach((e=>e.classList.remove(r))),s.params.loop||s.params.virtual&&s.params.virtual.enabled)for(let o=0;o<i;o+=1)C(s.slidesEl,`[data-swiper-slide-index="${t.realIndex+o}"]`).forEach((e=>{e.classList.add(r)}));else for(let o=0;o<i;o+=1)s.slides[t.realIndex+o]&&s.slides[t.realIndex+o].classList.add(r);const l=t.params.thumbs.autoScrollOffset,n=l&&!s.params.loop;if(t.realIndex!==s.realIndex||n){const i=s.activeIndex;let r,o;if(s.params.loop){const e=s.slides.find((e=>e.getAttribute("data-swiper-slide-index")===`${t.realIndex}`));r=s.slides.indexOf(e),o=t.activeIndex>t.previousIndex?"next":"prev"}else r=t.realIndex,o=r>t.previousIndex?"next":"prev";n&&(r+="next"===o?l:-1*l),s.visibleSlidesIndexes&&s.visibleSlidesIndexes.indexOf(r)<0&&(s.params.centeredSlides?r=r>i?r-Math.floor(a/2)+1:r+Math.floor(a/2)-1:r>i&&s.params.slidesPerGroup,s.slideTo(r,e?0:void 0))}}t.thumbs={swiper:null},a("beforeInit",(()=>{const{thumbs:e}=t.params;if(e&&e.swiper)if("string"==typeof e.swiper||e.swiper instanceof HTMLElement){const s=f(),a=()=>{const a="string"==typeof e.swiper?s.querySelector(e.swiper):e.swiper;if(a&&a.swiper)e.swiper=a.swiper,n(),o(!0);else if(a){const s=`${t.params.eventsPrefix}init`,i=r=>{e.swiper=r.detail[0],a.removeEventListener(s,i),n(),o(!0),e.swiper.update(),t.update()};a.addEventListener(s,i)}return a},i=()=>{if(t.destroyed)return;a()||requestAnimationFrame(i)};requestAnimationFrame(i)}else n(),o(!0)})),a("slideChange update resize observerUpdate",(()=>{o()})),a("setTransition",((e,s)=>{const a=t.thumbs.swiper;a&&!a.destroyed&&a.setTransition(s)})),a("beforeDestroy",(()=>{const e=t.thumbs.swiper;e&&!e.destroyed&&r&&e.destroy()})),Object.assign(t.thumbs,{init:n,update:o})},Virtual:function(e){let t,{swiper:s,extendParams:a,on:i,emit:r}=e;a({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}});const l=f();s.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]};const n=l.createElement("div");function o(e,t){const a=s.params.virtual;if(a.cache&&s.virtual.cache[t])return s.virtual.cache[t];let i;return a.renderSlide?(i=a.renderSlide.call(s,e,t),"string"==typeof i&&(n.innerHTML=i,i=n.children[0])):i=s.isElement?L("swiper-slide"):L("div",s.params.slideClass),i.setAttribute("data-swiper-slide-index",t),a.renderSlide||(i.innerHTML=e),a.cache&&(s.virtual.cache[t]=i),i}function d(e,t,a){const{slidesPerView:i,slidesPerGroup:l,centeredSlides:n,loop:d,initialSlide:c}=s.params;if(t&&!d&&c>0)return;const{addSlidesBefore:p,addSlidesAfter:u}=s.params.virtual,{from:m,to:f,slides:h,slidesGrid:v,offset:g}=s.virtual;s.params.cssMode||s.updateActiveIndex();const w=void 0===a?s.activeIndex||0:a;let b,y,E;b=s.rtlTranslate?"right":s.isHorizontal()?"left":"top",n?(y=Math.floor(i/2)+l+u,E=Math.floor(i/2)+l+p):(y=i+(l-1)+u,E=(d?i:l)+p);let S=w-E,x=w+y;d||(S=Math.max(S,0),x=Math.min(x,h.length-1));let T=(s.slidesGrid[S]||0)-(s.slidesGrid[0]||0);function M(){s.updateSlides(),s.updateProgress(),s.updateSlidesClasses(),r("virtualUpdate")}if(d&&w>=E?(S-=E,n||(T+=s.slidesGrid[0])):d&&w<E&&(S=-E,n&&(T+=s.slidesGrid[0])),Object.assign(s.virtual,{from:S,to:x,offset:T,slidesGrid:s.slidesGrid,slidesBefore:E,slidesAfter:y}),m===S&&f===x&&!e)return s.slidesGrid!==v&&T!==g&&s.slides.forEach((e=>{e.style[b]=T-Math.abs(s.cssOverflowAdjustment())+"px"})),s.updateProgress(),void r("virtualUpdate");if(s.params.virtual.renderExternal)return s.params.virtual.renderExternal.call(s,{offset:T,from:S,to:x,slides:function(){const e=[];for(let t=S;t<=x;t+=1)e.push(h[t]);return e}()}),void(s.params.virtual.renderExternalUpdate?M():r("virtualUpdate"));const P=[],L=[],O=e=>{let t=e;return e<0?t=h.length+e:t>=h.length&&(t-=h.length),t};if(e)s.slides.filter((e=>e.matches(`.${s.params.slideClass}, swiper-slide`))).forEach((e=>{e.remove()}));else for(let r=m;r<=f;r+=1)if(r<S||r>x){const e=O(r);s.slides.filter((t=>t.matches(`.${s.params.slideClass}[data-swiper-slide-index="${e}"], swiper-slide[data-swiper-slide-index="${e}"]`))).forEach((e=>{e.remove()}))}const z=d?-h.length:0,I=d?2*h.length:h.length;for(let s=z;s<I;s+=1)if(s>=S&&s<=x){const t=O(s);void 0===f||e?L.push(t):(s>f&&L.push(t),s<m&&P.push(t))}if(L.forEach((e=>{s.slidesEl.append(o(h[e],e))})),d)for(let r=P.length-1;r>=0;r-=1){const e=P[r];s.slidesEl.prepend(o(h[e],e))}else P.sort(((e,t)=>t-e)),P.forEach((e=>{s.slidesEl.prepend(o(h[e],e))}));C(s.slidesEl,".swiper-slide, swiper-slide").forEach((e=>{e.style[b]=T-Math.abs(s.cssOverflowAdjustment())+"px"})),M()}i("beforeInit",(()=>{if(!s.params.virtual.enabled)return;let e;if(void 0===s.passedParams.virtual.slides){const t=[...s.slidesEl.children].filter((e=>e.matches(`.${s.params.slideClass}, swiper-slide`)));t&&t.length&&(s.virtual.slides=[...t],e=!0,t.forEach(((e,t)=>{e.setAttribute("data-swiper-slide-index",t),s.virtual.cache[t]=e,e.remove()})))}e||(s.virtual.slides=s.params.virtual.slides),s.classNames.push(`${s.params.containerModifierClass}virtual`),s.params.watchSlidesProgress=!0,s.originalParams.watchSlidesProgress=!0,d(!1,!0)})),i("setTranslate",(()=>{s.params.virtual.enabled&&(s.params.cssMode&&!s._immediateVirtual?(clearTimeout(t),t=setTimeout((()=>{d()}),100)):d())})),i("init update resize",(()=>{s.params.virtual.enabled&&s.params.cssMode&&x(s.wrapperEl,"--swiper-virtual-size",`${s.virtualSize}px`)})),Object.assign(s.virtual,{appendSlide:function(e){if("object"==typeof e&&"length"in e)for(let t=0;t<e.length;t+=1)e[t]&&s.virtual.slides.push(e[t]);else s.virtual.slides.push(e);d(!0)},prependSlide:function(e){const t=s.activeIndex;let a=t+1,i=1;if(Array.isArray(e)){for(let t=0;t<e.length;t+=1)e[t]&&s.virtual.slides.unshift(e[t]);a=t+e.length,i=e.length}else s.virtual.slides.unshift(e);if(s.params.virtual.cache){const e=s.virtual.cache,t={};Object.keys(e).forEach((s=>{const a=e[s],r=a.getAttribute("data-swiper-slide-index");r&&a.setAttribute("data-swiper-slide-index",parseInt(r,10)+i),t[parseInt(s,10)+i]=a})),s.virtual.cache=t}d(!0),s.slideTo(a,0)},removeSlide:function(e){if(null==e)return;let t=s.activeIndex;if(Array.isArray(e))for(let a=e.length-1;a>=0;a-=1)s.params.virtual.cache&&(delete s.virtual.cache[e[a]],Object.keys(s.virtual.cache).forEach((t=>{t>e&&(s.virtual.cache[t-1]=s.virtual.cache[t],s.virtual.cache[t-1].setAttribute("data-swiper-slide-index",t-1),delete s.virtual.cache[t])}))),s.virtual.slides.splice(e[a],1),e[a]<t&&(t-=1),t=Math.max(t,0);else s.params.virtual.cache&&(delete s.virtual.cache[e],Object.keys(s.virtual.cache).forEach((t=>{t>e&&(s.virtual.cache[t-1]=s.virtual.cache[t],s.virtual.cache[t-1].setAttribute("data-swiper-slide-index",t-1),delete s.virtual.cache[t])}))),s.virtual.slides.splice(e,1),e<t&&(t-=1),t=Math.max(t,0);d(!0),s.slideTo(t,0)},removeAllSlides:function(){s.virtual.slides=[],s.params.virtual.cache&&(s.virtual.cache={}),d(!0),s.slideTo(0,0)},update:d})},Zoom:function(e){let{swiper:t,extendParams:s,on:a,emit:i}=e;const r=v();s({zoom:{enabled:!1,limitToOriginalSize:!1,maxRatio:3,minRatio:1,panOnMouseMove:!1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),t.zoom={enabled:!1};let l,n,o=1,d=!1,c=!1,p={x:0,y:0};const u=[],m={originX:0,originY:0,slideEl:void 0,slideWidth:void 0,slideHeight:void 0,imageEl:void 0,imageWrapEl:void 0,maxRatio:3},f={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},h={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0};let g,w=1;function b(){if(u.length<2)return 1;const e=u[0].pageX,t=u[0].pageY,s=u[1].pageX,a=u[1].pageY;return Math.sqrt((s-e)**2+(a-t)**2)}function E(){const e=t.params.zoom,s=m.imageWrapEl.getAttribute("data-swiper-zoom")||e.maxRatio;if(e.limitToOriginalSize&&m.imageEl&&m.imageEl.naturalWidth){const e=m.imageEl.naturalWidth/m.imageEl.offsetWidth;return Math.min(e,s)}return s}function S(e){const s=t.isElement?"swiper-slide":`.${t.params.slideClass}`;return!!e.target.matches(s)||t.slides.filter((t=>t.contains(e.target))).length>0}function x(e){const s=`.${t.params.zoom.containerClass}`;return!!e.target.matches(s)||[...t.hostEl.querySelectorAll(s)].filter((t=>t.contains(e.target))).length>0}function T(e){if("mouse"===e.pointerType&&u.splice(0,u.length),!S(e))return;const s=t.params.zoom;if(l=!1,n=!1,u.push(e),!(u.length<2)){if(l=!0,m.scaleStart=b(),!m.slideEl){m.slideEl=e.target.closest(`.${t.params.slideClass}, swiper-slide`),m.slideEl||(m.slideEl=t.slides[t.activeIndex]);let a=m.slideEl.querySelector(`.${s.containerClass}`);if(a&&(a=a.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),m.imageEl=a,m.imageWrapEl=a?k(m.imageEl,`.${s.containerClass}`)[0]:void 0,!m.imageWrapEl)return void(m.imageEl=void 0);m.maxRatio=E()}if(m.imageEl){const[e,t]=function(){if(u.length<2)return{x:null,y:null};const e=m.imageEl.getBoundingClientRect();return[(u[0].pageX+(u[1].pageX-u[0].pageX)/2-e.x-r.scrollX)/o,(u[0].pageY+(u[1].pageY-u[0].pageY)/2-e.y-r.scrollY)/o]}();m.originX=e,m.originY=t,m.imageEl.style.transitionDuration="0ms"}d=!0}}function M(e){if(!S(e))return;const s=t.params.zoom,a=t.zoom,i=u.findIndex((t=>t.pointerId===e.pointerId));i>=0&&(u[i]=e),u.length<2||(n=!0,m.scaleMove=b(),m.imageEl&&(a.scale=m.scaleMove/m.scaleStart*o,a.scale>m.maxRatio&&(a.scale=m.maxRatio-1+(a.scale-m.maxRatio+1)**.5),a.scale<s.minRatio&&(a.scale=s.minRatio+1-(s.minRatio-a.scale+1)**.5),m.imageEl.style.transform=`translate3d(0,0,0) scale(${a.scale})`))}function P(e){if(!S(e))return;if("mouse"===e.pointerType&&"pointerout"===e.type)return;const s=t.params.zoom,a=t.zoom,i=u.findIndex((t=>t.pointerId===e.pointerId));i>=0&&u.splice(i,1),l&&n&&(l=!1,n=!1,m.imageEl&&(a.scale=Math.max(Math.min(a.scale,m.maxRatio),s.minRatio),m.imageEl.style.transitionDuration=`${t.params.speed}ms`,m.imageEl.style.transform=`translate3d(0,0,0) scale(${a.scale})`,o=a.scale,d=!1,a.scale>1&&m.slideEl?m.slideEl.classList.add(`${s.zoomedSlideClass}`):a.scale<=1&&m.slideEl&&m.slideEl.classList.remove(`${s.zoomedSlideClass}`),1===a.scale&&(m.originX=0,m.originY=0,m.slideEl=void 0)))}function L(){t.touchEventsData.preventTouchMoveFromPointerMove=!1}function z(e){const s="mouse"===e.pointerType&&t.params.zoom.panOnMouseMove;if(!S(e)||!x(e))return;const a=t.zoom;if(!m.imageEl)return;if(!f.isTouched||!m.slideEl)return void(s&&A(e));if(s)return void A(e);f.isMoved||(f.width=m.imageEl.offsetWidth||m.imageEl.clientWidth,f.height=m.imageEl.offsetHeight||m.imageEl.clientHeight,f.startX=y(m.imageWrapEl,"x")||0,f.startY=y(m.imageWrapEl,"y")||0,m.slideWidth=m.slideEl.offsetWidth,m.slideHeight=m.slideEl.offsetHeight,m.imageWrapEl.style.transitionDuration="0ms");const i=f.width*a.scale,r=f.height*a.scale;f.minX=Math.min(m.slideWidth/2-i/2,0),f.maxX=-f.minX,f.minY=Math.min(m.slideHeight/2-r/2,0),f.maxY=-f.minY,f.touchesCurrent.x=u.length>0?u[0].pageX:e.pageX,f.touchesCurrent.y=u.length>0?u[0].pageY:e.pageY;if(Math.max(Math.abs(f.touchesCurrent.x-f.touchesStart.x),Math.abs(f.touchesCurrent.y-f.touchesStart.y))>5&&(t.allowClick=!1),!f.isMoved&&!d){if(t.isHorizontal()&&(Math.floor(f.minX)===Math.floor(f.startX)&&f.touchesCurrent.x<f.touchesStart.x||Math.floor(f.maxX)===Math.floor(f.startX)&&f.touchesCurrent.x>f.touchesStart.x))return f.isTouched=!1,void L();if(!t.isHorizontal()&&(Math.floor(f.minY)===Math.floor(f.startY)&&f.touchesCurrent.y<f.touchesStart.y||Math.floor(f.maxY)===Math.floor(f.startY)&&f.touchesCurrent.y>f.touchesStart.y))return f.isTouched=!1,void L()}e.cancelable&&e.preventDefault(),e.stopPropagation(),clearTimeout(g),t.touchEventsData.preventTouchMoveFromPointerMove=!0,g=setTimeout((()=>{t.destroyed||L()})),f.isMoved=!0;const l=(a.scale-o)/(m.maxRatio-t.params.zoom.minRatio),{originX:n,originY:c}=m;f.currentX=f.touchesCurrent.x-f.touchesStart.x+f.startX+l*(f.width-2*n),f.currentY=f.touchesCurrent.y-f.touchesStart.y+f.startY+l*(f.height-2*c),f.currentX<f.minX&&(f.currentX=f.minX+1-(f.minX-f.currentX+1)**.8),f.currentX>f.maxX&&(f.currentX=f.maxX-1+(f.currentX-f.maxX+1)**.8),f.currentY<f.minY&&(f.currentY=f.minY+1-(f.minY-f.currentY+1)**.8),f.currentY>f.maxY&&(f.currentY=f.maxY-1+(f.currentY-f.maxY+1)**.8),h.prevPositionX||(h.prevPositionX=f.touchesCurrent.x),h.prevPositionY||(h.prevPositionY=f.touchesCurrent.y),h.prevTime||(h.prevTime=Date.now()),h.x=(f.touchesCurrent.x-h.prevPositionX)/(Date.now()-h.prevTime)/2,h.y=(f.touchesCurrent.y-h.prevPositionY)/(Date.now()-h.prevTime)/2,Math.abs(f.touchesCurrent.x-h.prevPositionX)<2&&(h.x=0),Math.abs(f.touchesCurrent.y-h.prevPositionY)<2&&(h.y=0),h.prevPositionX=f.touchesCurrent.x,h.prevPositionY=f.touchesCurrent.y,h.prevTime=Date.now(),m.imageWrapEl.style.transform=`translate3d(${f.currentX}px, ${f.currentY}px,0)`}function I(){const e=t.zoom;m.slideEl&&t.activeIndex!==t.slides.indexOf(m.slideEl)&&(m.imageEl&&(m.imageEl.style.transform="translate3d(0,0,0) scale(1)"),m.imageWrapEl&&(m.imageWrapEl.style.transform="translate3d(0,0,0)"),m.slideEl.classList.remove(`${t.params.zoom.zoomedSlideClass}`),e.scale=1,o=1,m.slideEl=void 0,m.imageEl=void 0,m.imageWrapEl=void 0,m.originX=0,m.originY=0)}function A(e){if(o<=1||!m.imageWrapEl)return;if(!S(e)||!x(e))return;const t=r.getComputedStyle(m.imageWrapEl).transform,s=new r.DOMMatrix(t);if(!c)return c=!0,p.x=e.clientX,p.y=e.clientY,f.startX=s.e,f.startY=s.f,f.width=m.imageEl.offsetWidth||m.imageEl.clientWidth,f.height=m.imageEl.offsetHeight||m.imageEl.clientHeight,m.slideWidth=m.slideEl.offsetWidth,void(m.slideHeight=m.slideEl.offsetHeight);const a=-3*(e.clientX-p.x),i=-3*(e.clientY-p.y),l=f.width*o,n=f.height*o,d=m.slideWidth,u=m.slideHeight,h=Math.min(d/2-l/2,0),v=-h,g=Math.min(u/2-n/2,0),w=-g,b=Math.max(Math.min(f.startX+a,v),h),y=Math.max(Math.min(f.startY+i,w),g);m.imageWrapEl.style.transitionDuration="0ms",m.imageWrapEl.style.transform=`translate3d(${b}px, ${y}px, 0)`,p.x=e.clientX,p.y=e.clientY,f.startX=b,f.startY=y,f.currentX=b,f.currentY=y}function $(e){const s=t.zoom,a=t.params.zoom;if(!m.slideEl){e&&e.target&&(m.slideEl=e.target.closest(`.${t.params.slideClass}, swiper-slide`)),m.slideEl||(t.params.virtual&&t.params.virtual.enabled&&t.virtual?m.slideEl=C(t.slidesEl,`.${t.params.slideActiveClass}`)[0]:m.slideEl=t.slides[t.activeIndex]);let s=m.slideEl.querySelector(`.${a.containerClass}`);s&&(s=s.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),m.imageEl=s,m.imageWrapEl=s?k(m.imageEl,`.${a.containerClass}`)[0]:void 0}if(!m.imageEl||!m.imageWrapEl)return;let i,l,n,d,c,p,u,h,v,g,w,b,y,S,x,T,M,P;t.params.cssMode&&(t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.touchAction="none"),m.slideEl.classList.add(`${a.zoomedSlideClass}`),void 0===f.touchesStart.x&&e?(i=e.pageX,l=e.pageY):(i=f.touchesStart.x,l=f.touchesStart.y);const L=o,z="number"==typeof e?e:null;1===o&&z&&(i=void 0,l=void 0,f.touchesStart.x=void 0,f.touchesStart.y=void 0);const I=E();s.scale=z||I,o=z||I,!e||1===o&&z?(u=0,h=0):(M=m.slideEl.offsetWidth,P=m.slideEl.offsetHeight,n=O(m.slideEl).left+r.scrollX,d=O(m.slideEl).top+r.scrollY,c=n+M/2-i,p=d+P/2-l,v=m.imageEl.offsetWidth||m.imageEl.clientWidth,g=m.imageEl.offsetHeight||m.imageEl.clientHeight,w=v*s.scale,b=g*s.scale,y=Math.min(M/2-w/2,0),S=Math.min(P/2-b/2,0),x=-y,T=-S,L>0&&z&&"number"==typeof f.currentX&&"number"==typeof f.currentY?(u=f.currentX*s.scale/L,h=f.currentY*s.scale/L):(u=c*s.scale,h=p*s.scale),u<y&&(u=y),u>x&&(u=x),h<S&&(h=S),h>T&&(h=T)),z&&1===s.scale&&(m.originX=0,m.originY=0),f.currentX=u,f.currentY=h,m.imageWrapEl.style.transitionDuration="300ms",m.imageWrapEl.style.transform=`translate3d(${u}px, ${h}px,0)`,m.imageEl.style.transitionDuration="300ms",m.imageEl.style.transform=`translate3d(0,0,0) scale(${s.scale})`}function D(){const e=t.zoom,s=t.params.zoom;if(!m.slideEl){t.params.virtual&&t.params.virtual.enabled&&t.virtual?m.slideEl=C(t.slidesEl,`.${t.params.slideActiveClass}`)[0]:m.slideEl=t.slides[t.activeIndex];let e=m.slideEl.querySelector(`.${s.containerClass}`);e&&(e=e.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),m.imageEl=e,m.imageWrapEl=e?k(m.imageEl,`.${s.containerClass}`)[0]:void 0}m.imageEl&&m.imageWrapEl&&(t.params.cssMode&&(t.wrapperEl.style.overflow="",t.wrapperEl.style.touchAction=""),e.scale=1,o=1,f.currentX=void 0,f.currentY=void 0,f.touchesStart.x=void 0,f.touchesStart.y=void 0,m.imageWrapEl.style.transitionDuration="300ms",m.imageWrapEl.style.transform="translate3d(0,0,0)",m.imageEl.style.transitionDuration="300ms",m.imageEl.style.transform="translate3d(0,0,0) scale(1)",m.slideEl.classList.remove(`${s.zoomedSlideClass}`),m.slideEl=void 0,m.originX=0,m.originY=0,t.params.zoom.panOnMouseMove&&(p={x:0,y:0},c&&(c=!1,f.startX=0,f.startY=0)))}function B(e){const s=t.zoom;s.scale&&1!==s.scale?D():$(e)}function _(){return{passiveListener:!!t.params.passiveListeners&&{passive:!0,capture:!1},activeListenerWithCapture:!t.params.passiveListeners||{passive:!1,capture:!0}}}function N(){const e=t.zoom;if(e.enabled)return;e.enabled=!0;const{passiveListener:s,activeListenerWithCapture:a}=_();t.wrapperEl.addEventListener("pointerdown",T,s),t.wrapperEl.addEventListener("pointermove",M,a),["pointerup","pointercancel","pointerout"].forEach((e=>{t.wrapperEl.addEventListener(e,P,s)})),t.wrapperEl.addEventListener("pointermove",z,a)}function G(){const e=t.zoom;if(!e.enabled)return;e.enabled=!1;const{passiveListener:s,activeListenerWithCapture:a}=_();t.wrapperEl.removeEventListener("pointerdown",T,s),t.wrapperEl.removeEventListener("pointermove",M,a),["pointerup","pointercancel","pointerout"].forEach((e=>{t.wrapperEl.removeEventListener(e,P,s)})),t.wrapperEl.removeEventListener("pointermove",z,a)}Object.defineProperty(t.zoom,"scale",{get:()=>w,set(e){if(w!==e){const t=m.imageEl,s=m.slideEl;i("zoomChange",e,t,s)}w=e}}),a("init",(()=>{t.params.zoom.enabled&&N()})),a("destroy",(()=>{G()})),a("touchStart",((e,s)=>{t.zoom.enabled&&function(e){const s=t.device;if(!m.imageEl)return;if(f.isTouched)return;s.android&&e.cancelable&&e.preventDefault(),f.isTouched=!0;const a=u.length>0?u[0]:e;f.touchesStart.x=a.pageX,f.touchesStart.y=a.pageY}(s)})),a("touchEnd",((e,s)=>{t.zoom.enabled&&function(){const e=t.zoom;if(u.length=0,!m.imageEl)return;if(!f.isTouched||!f.isMoved)return f.isTouched=!1,void(f.isMoved=!1);f.isTouched=!1,f.isMoved=!1;let s=300,a=300;const i=h.x*s,r=f.currentX+i,l=h.y*a,n=f.currentY+l;0!==h.x&&(s=Math.abs((r-f.currentX)/h.x)),0!==h.y&&(a=Math.abs((n-f.currentY)/h.y));const o=Math.max(s,a);f.currentX=r,f.currentY=n;const d=f.width*e.scale,c=f.height*e.scale;f.minX=Math.min(m.slideWidth/2-d/2,0),f.maxX=-f.minX,f.minY=Math.min(m.slideHeight/2-c/2,0),f.maxY=-f.minY,f.currentX=Math.max(Math.min(f.currentX,f.maxX),f.minX),f.currentY=Math.max(Math.min(f.currentY,f.maxY),f.minY),m.imageWrapEl.style.transitionDuration=`${o}ms`,m.imageWrapEl.style.transform=`translate3d(${f.currentX}px, ${f.currentY}px,0)`}()})),a("doubleTap",((e,s)=>{!t.animating&&t.params.zoom.enabled&&t.zoom.enabled&&t.params.zoom.toggle&&B(s)})),a("transitionEnd",(()=>{t.zoom.enabled&&t.params.zoom.enabled&&I()})),a("slideChange",(()=>{t.zoom.enabled&&t.params.zoom.enabled&&t.params.cssMode&&I()})),Object.assign(t.zoom,{enable:N,disable:G,in:$,out:D,toggle:B})}},Symbol.toStringTag,{value:"Module"})),De=Object.freeze(Object.defineProperty({__proto__:null,Swiper:ce,default:ce},Symbol.toStringTag,{value:"Module"}));export{De as S,Se as a,$e as s};
