import{s as t}from"./main-Djn9RDyT.js";const a="/licensex/license-info/upload-license",e="/licensex/license-info/get-license-info",o="/systemx/data-migration/upload-database-info",n="/systemx/data-migration/upload-file",s="/systemx/data-migration/download-database-info";function r(e){return t({url:a,method:"post",data:e})}function i(){return t({url:e,method:"get"})}function d(a,e){return t({url:o,method:"post",data:a,headers:{"Content-Type":"multipart/form-data"},onUploadProgress(t){e(t)}})}function u(a,e){return t({url:n,method:"post",data:a,headers:{"Content-Type":"multipart/form-data"},onUploadProgress(t){e(t)}})}function l(a){return t({url:s,method:"get",params:a})}export{d as a,u as b,l as d,i as g,r as u};
