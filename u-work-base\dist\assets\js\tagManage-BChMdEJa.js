import{s as t,a}from"./main-Djn9RDyT.js";const r="/edtap/tagGroup/page";function o(a){return t({url:"/edtap/sys-dict-type/drop-down",method:"get",params:a})}function e(o){return t({url:r,method:"get",params:o,headers:{code:a().currentPlatformCode}})}function d(a){return t({url:"/edtap/tagGroup/add",method:"post",data:a})}function u(a){return t({url:"/edtap/tagGroup/edit",method:"post",data:a})}function p(a,r={tagName:"",color:""}){return t({url:"/edtap/tagGroup/addTag",method:"post",data:{exTag:r,groupId:a}})}function n(a,r={tagName:"",color:"",id:""}){return t({url:"/edtap/tagGroup/editTag",method:"post",data:{exTag:r,groupId:a}})}function g(a){return t({url:"/edtap/tagGroup/delGroup",method:"get",params:{groupId:a}})}function s(a,r){return t({url:"/edtap/tagGroup/del",method:"post",data:{groupId:a,tagId:r}})}function m(a,r){return t({url:"/edtap/tagGroup/modifyRange",method:"post",data:{range:r.toString(),groupId:a}})}export{p as a,u as b,n as c,o as d,m as e,g as f,s as g,d as r,e as t};
