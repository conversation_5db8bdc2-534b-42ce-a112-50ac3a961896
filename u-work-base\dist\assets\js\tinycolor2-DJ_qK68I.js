function t(r){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(r)}var r=/^\s+/,e=/\s+$/;function a(n,i){if(i=i||{},(n=n||"")instanceof a)return n;if(!(this instanceof a))return new a(n,i);var o=function(a){var n={r:0,g:0,b:0},i=1,o=null,h=null,s=null,f=!1,u=!1;"string"==typeof a&&(a=function(t){t=t.replace(r,"").replace(e,"").toLowerCase();var a,n=!1;if(M[t])t=M[t],n=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};if(a=E.rgb.exec(t))return{r:a[1],g:a[2],b:a[3]};if(a=E.rgba.exec(t))return{r:a[1],g:a[2],b:a[3],a:a[4]};if(a=E.hsl.exec(t))return{h:a[1],s:a[2],l:a[3]};if(a=E.hsla.exec(t))return{h:a[1],s:a[2],l:a[3],a:a[4]};if(a=E.hsv.exec(t))return{h:a[1],s:a[2],v:a[3]};if(a=E.hsva.exec(t))return{h:a[1],s:a[2],v:a[3],a:a[4]};if(a=E.hex8.exec(t))return{r:w(a[1]),g:w(a[2]),b:w(a[3]),a:F(a[4]),format:n?"name":"hex8"};if(a=E.hex6.exec(t))return{r:w(a[1]),g:w(a[2]),b:w(a[3]),format:n?"name":"hex"};if(a=E.hex4.exec(t))return{r:w(a[1]+""+a[1]),g:w(a[2]+""+a[2]),b:w(a[3]+""+a[3]),a:F(a[4]+""+a[4]),format:n?"name":"hex8"};if(a=E.hex3.exec(t))return{r:w(a[1]+""+a[1]),g:w(a[2]+""+a[2]),b:w(a[3]+""+a[3]),format:n?"name":"hex"};return!1}(a));"object"==t(a)&&(I(a.r)&&I(a.g)&&I(a.b)?(l=a.r,c=a.g,d=a.b,n={r:255*A(l,255),g:255*A(c,255),b:255*A(d,255)},f=!0,u="%"===String(a.r).substr(-1)?"prgb":"rgb"):I(a.h)&&I(a.s)&&I(a.v)?(o=H(a.s),h=H(a.v),n=function(t,r,e){t=6*A(t,360),r=A(r,100),e=A(e,100);var a=Math.floor(t),n=t-a,i=e*(1-r),o=e*(1-n*r),h=e*(1-(1-n)*r),s=a%6,f=[e,o,i,i,h,e][s],u=[h,e,e,o,i,i][s],l=[i,i,h,e,e,o][s];return{r:255*f,g:255*u,b:255*l}}(a.h,o,h),f=!0,u="hsv"):I(a.h)&&I(a.s)&&I(a.l)&&(o=H(a.s),s=H(a.l),n=function(t,r,e){var a,n,i;function o(t,r,e){return e<0&&(e+=1),e>1&&(e-=1),e<1/6?t+6*(r-t)*e:e<.5?r:e<2/3?t+(r-t)*(2/3-e)*6:t}if(t=A(t,360),r=A(r,100),e=A(e,100),0===r)a=n=i=e;else{var h=e<.5?e*(1+r):e+r-e*r,s=2*e-h;a=o(s,h,t+1/3),n=o(s,h,t),i=o(s,h,t-1/3)}return{r:255*a,g:255*n,b:255*i}}(a.h,o,s),f=!0,u="hsl"),a.hasOwnProperty("a")&&(i=a.a));var l,c,d;return i=x(i),{ok:f,format:a.format||u,r:Math.min(255,Math.max(n.r,0)),g:Math.min(255,Math.max(n.g,0)),b:Math.min(255,Math.max(n.b,0)),a:i}}(n);this._originalInput=n,this._r=o.r,this._g=o.g,this._b=o.b,this._a=o.a,this._roundA=Math.round(100*this._a)/100,this._format=i.format||o.format,this._gradientType=i.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=o.ok}function n(t,r,e){t=A(t,255),r=A(r,255),e=A(e,255);var a,n,i=Math.max(t,r,e),o=Math.min(t,r,e),h=(i+o)/2;if(i==o)a=n=0;else{var s=i-o;switch(n=h>.5?s/(2-i-o):s/(i+o),i){case t:a=(r-e)/s+(r<e?6:0);break;case r:a=(e-t)/s+2;break;case e:a=(t-r)/s+4}a/=6}return{h:a,s:n,l:h}}function i(t,r,e){t=A(t,255),r=A(r,255),e=A(e,255);var a,n,i=Math.max(t,r,e),o=Math.min(t,r,e),h=i,s=i-o;if(n=0===i?0:s/i,i==o)a=0;else{switch(i){case t:a=(r-e)/s+(r<e?6:0);break;case r:a=(e-t)/s+2;break;case e:a=(t-r)/s+4}a/=6}return{h:a,s:n,v:h}}function o(t,r,e,a){var n=[S(Math.round(t).toString(16)),S(Math.round(r).toString(16)),S(Math.round(e).toString(16))];return a&&n[0].charAt(0)==n[0].charAt(1)&&n[1].charAt(0)==n[1].charAt(1)&&n[2].charAt(0)==n[2].charAt(1)?n[0].charAt(0)+n[1].charAt(0)+n[2].charAt(0):n.join("")}function h(t,r,e,a){return[S(R(a)),S(Math.round(t).toString(16)),S(Math.round(r).toString(16)),S(Math.round(e).toString(16))].join("")}function s(t,r){r=0===r?0:r||10;var e=a(t).toHsl();return e.s-=r/100,e.s=k(e.s),a(e)}function f(t,r){r=0===r?0:r||10;var e=a(t).toHsl();return e.s+=r/100,e.s=k(e.s),a(e)}function u(t){return a(t).desaturate(100)}function l(t,r){r=0===r?0:r||10;var e=a(t).toHsl();return e.l+=r/100,e.l=k(e.l),a(e)}function c(t,r){r=0===r?0:r||10;var e=a(t).toRgb();return e.r=Math.max(0,Math.min(255,e.r-Math.round(-r/100*255))),e.g=Math.max(0,Math.min(255,e.g-Math.round(-r/100*255))),e.b=Math.max(0,Math.min(255,e.b-Math.round(-r/100*255))),a(e)}function d(t,r){r=0===r?0:r||10;var e=a(t).toHsl();return e.l-=r/100,e.l=k(e.l),a(e)}function g(t,r){var e=a(t).toHsl(),n=(e.h+r)%360;return e.h=n<0?360+n:n,a(e)}function b(t){var r=a(t).toHsl();return r.h=(r.h+180)%360,a(r)}function m(t,r){if(isNaN(r)||r<=0)throw new Error("Argument to polyad must be a positive number");for(var e=a(t).toHsl(),n=[a(t)],i=360/r,o=1;o<r;o++)n.push(a({h:(e.h+o*i)%360,s:e.s,l:e.l}));return n}function p(t){var r=a(t).toHsl(),e=r.h;return[a(t),a({h:(e+72)%360,s:r.s,l:r.l}),a({h:(e+216)%360,s:r.s,l:r.l})]}function _(t,r,e){r=r||6,e=e||30;var n=a(t).toHsl(),i=360/e,o=[a(t)];for(n.h=(n.h-(i*r>>1)+720)%360;--r;)n.h=(n.h+i)%360,o.push(a(n));return o}function v(t,r){r=r||6;for(var e=a(t).toHsv(),n=e.h,i=e.s,o=e.v,h=[],s=1/r;r--;)h.push(a({h:n,s:i,v:o})),o=(o+s)%1;return h}a.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,r,e,a=this.toRgb();return t=a.r/255,r=a.g/255,e=a.b/255,.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))+.0722*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))},setAlpha:function(t){return this._a=x(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=i(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=i(this._r,this._g,this._b),r=Math.round(360*t.h),e=Math.round(100*t.s),a=Math.round(100*t.v);return 1==this._a?"hsv("+r+", "+e+"%, "+a+"%)":"hsva("+r+", "+e+"%, "+a+"%, "+this._roundA+")"},toHsl:function(){var t=n(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=n(this._r,this._g,this._b),r=Math.round(360*t.h),e=Math.round(100*t.s),a=Math.round(100*t.l);return 1==this._a?"hsl("+r+", "+e+"%, "+a+"%)":"hsla("+r+", "+e+"%, "+a+"%, "+this._roundA+")"},toHex:function(t){return o(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return function(t,r,e,a,n){var i=[S(Math.round(t).toString(16)),S(Math.round(r).toString(16)),S(Math.round(e).toString(16)),S(R(a))];if(n&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)&&i[3].charAt(0)==i[3].charAt(1))return i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0)+i[3].charAt(0);return i.join("")}(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(100*A(this._r,255))+"%",g:Math.round(100*A(this._g,255))+"%",b:Math.round(100*A(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+Math.round(100*A(this._r,255))+"%, "+Math.round(100*A(this._g,255))+"%, "+Math.round(100*A(this._b,255))+"%)":"rgba("+Math.round(100*A(this._r,255))+"%, "+Math.round(100*A(this._g,255))+"%, "+Math.round(100*A(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(y[o(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var r="#"+h(this._r,this._g,this._b,this._a),e=r,n=this._gradientType?"GradientType = 1, ":"";if(t){var i=a(t);e="#"+h(i._r,i._g,i._b,i._a)}return"progid:DXImageTransform.Microsoft.gradient("+n+"startColorstr="+r+",endColorstr="+e+")"},toString:function(t){var r=!!t;t=t||this._format;var e=!1,a=this._a<1&&this._a>=0;return r||!a||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"hex4"!==t&&"hex8"!==t&&"name"!==t?("rgb"===t&&(e=this.toRgbString()),"prgb"===t&&(e=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(e=this.toHexString()),"hex3"===t&&(e=this.toHexString(!0)),"hex4"===t&&(e=this.toHex8String(!0)),"hex8"===t&&(e=this.toHex8String()),"name"===t&&(e=this.toName()),"hsl"===t&&(e=this.toHslString()),"hsv"===t&&(e=this.toHsvString()),e||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},clone:function(){return a(this.toString())},_applyModification:function(t,r){var e=t.apply(null,[this].concat([].slice.call(r)));return this._r=e._r,this._g=e._g,this._b=e._b,this.setAlpha(e._a),this},lighten:function(){return this._applyModification(l,arguments)},brighten:function(){return this._applyModification(c,arguments)},darken:function(){return this._applyModification(d,arguments)},desaturate:function(){return this._applyModification(s,arguments)},saturate:function(){return this._applyModification(f,arguments)},greyscale:function(){return this._applyModification(u,arguments)},spin:function(){return this._applyModification(g,arguments)},_applyCombination:function(t,r){return t.apply(null,[this].concat([].slice.call(r)))},analogous:function(){return this._applyCombination(_,arguments)},complement:function(){return this._applyCombination(b,arguments)},monochromatic:function(){return this._applyCombination(v,arguments)},splitcomplement:function(){return this._applyCombination(p,arguments)},triad:function(){return this._applyCombination(m,[3])},tetrad:function(){return this._applyCombination(m,[4])}},a.fromRatio=function(r,e){if("object"==t(r)){var n={};for(var i in r)r.hasOwnProperty(i)&&(n[i]="a"===i?r[i]:H(r[i]));r=n}return a(r,e)},a.equals=function(t,r){return!(!t||!r)&&a(t).toRgbString()==a(r).toRgbString()},a.random=function(){return a.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})},a.mix=function(t,r,e){e=0===e?0:e||50;var n=a(t).toRgb(),i=a(r).toRgb(),o=e/100;return a({r:(i.r-n.r)*o+n.r,g:(i.g-n.g)*o+n.g,b:(i.b-n.b)*o+n.b,a:(i.a-n.a)*o+n.a})},a.readability=function(t,r){var e=a(t),n=a(r);return(Math.max(e.getLuminance(),n.getLuminance())+.05)/(Math.min(e.getLuminance(),n.getLuminance())+.05)},a.isReadable=function(t,r,e){var n,i,o=a.readability(t,r);switch(i=!1,(n=function(t){var r,e;r=((t=t||{level:"AA",size:"small"}).level||"AA").toUpperCase(),e=(t.size||"small").toLowerCase(),"AA"!==r&&"AAA"!==r&&(r="AA");"small"!==e&&"large"!==e&&(e="small");return{level:r,size:e}}(e)).level+n.size){case"AAsmall":case"AAAlarge":i=o>=4.5;break;case"AAlarge":i=o>=3;break;case"AAAsmall":i=o>=7}return i},a.mostReadable=function(t,r,e){var n,i,o,h,s=null,f=0;i=(e=e||{}).includeFallbackColors,o=e.level,h=e.size;for(var u=0;u<r.length;u++)(n=a.readability(t,r[u]))>f&&(f=n,s=a(r[u]));return a.isReadable(t,s,{level:o,size:h})||!i?s:(e.includeFallbackColors=!1,a.mostReadable(t,["#fff","#000"],e))};var M=a.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},y=a.hexNames=function(t){var r={};for(var e in t)t.hasOwnProperty(e)&&(r[t[e]]=e);return r}(M);function x(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function A(t,r){(function(t){return"string"==typeof t&&-1!=t.indexOf(".")&&1===parseFloat(t)})(t)&&(t="100%");var e=function(t){return"string"==typeof t&&-1!=t.indexOf("%")}(t);return t=Math.min(r,Math.max(0,parseFloat(t))),e&&(t=parseInt(t*r,10)/100),Math.abs(t-r)<1e-6?1:t%r/parseFloat(r)}function k(t){return Math.min(1,Math.max(0,t))}function w(t){return parseInt(t,16)}function S(t){return 1==t.length?"0"+t:""+t}function H(t){return t<=1&&(t=100*t+"%"),t}function R(t){return Math.round(255*parseFloat(t)).toString(16)}function F(t){return w(t)/255}var C,q,N,E=(q="[\\s|\\(]+("+(C="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+C+")[,|\\s]+("+C+")\\s*\\)?",N="[\\s|\\(]+("+C+")[,|\\s]+("+C+")[,|\\s]+("+C+")[,|\\s]+("+C+")\\s*\\)?",{CSS_UNIT:new RegExp(C),rgb:new RegExp("rgb"+q),rgba:new RegExp("rgba"+N),hsl:new RegExp("hsl"+q),hsla:new RegExp("hsla"+N),hsv:new RegExp("hsv"+q),hsva:new RegExp("hsva"+N),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function I(t){return!!E.CSS_UNIT.exec(t)}export{a as t};
