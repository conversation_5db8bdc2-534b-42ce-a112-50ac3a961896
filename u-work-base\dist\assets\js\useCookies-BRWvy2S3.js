import{S as a,a1 as s,B as e,M as o}from"./ant-design-vue-DYY9BtJq.js";import{ad as l,ae as i}from"./@ant-design-CA72ad83.js";import{d as r,r as t,a9 as n,o as u,aa as c,c as d,e as v,b as m,F as f,ag as p,ab as g,u as w,J as _}from"./@vue-HScy-mz9.js";import{_ as b}from"./vue-qr-CB2aNKv5.js";import{u as k}from"./vue3-cookies-D4wQmYyh.js";const y={class:"content"},x={class:"custom-slick-arrow",style:{"z-index":1}},j={class:"custom-slick-arrow",style:{right:"10px"}},C={key:0,class:"video",justify:"center",controls:"",loop:"true"},h=["src"],A=["src"],I=b(r({__name:"ImgVideoPreview",setup(r,{expose:b}){const k=t(!1),I=t(!1),S=t("image"),z=window.config.baseUrl,E=t([]),O=()=>{k.value=!1,I.value=!1};return b({init:(a,s)=>{k.value=!0,S.value=a,E.value=s}}),(r,t)=>{const b=s,U=a,q=e,B=o;return u(),n(B,{width:1200,title:"预览","body-style":{maxHeight:"800px",overflow:"auto"},"wrap-class-name":"cus-modal",open:k.value,"confirm-loading":I.value,"mask-closable":!1,destroyOnClose:!0,footer:null,onCancel:O},{footer:c((()=>[d(q,{disabled:I.value,onClick:O},{default:c((()=>t[0]||(t[0]=[_("关闭")]))),_:1},8,["disabled"])])),default:c((()=>[d(U,{spinning:I.value},{default:c((()=>[v("div",y,[v("main",null,[d(b,{arrows:""},{prevArrow:c((()=>[v("div",x,[d(w(i))])])),nextArrow:c((()=>[v("div",j,[d(w(l))])])),default:c((()=>[(u(!0),m(f,null,p(E.value,((a,s)=>(u(),m("div",{key:s,class:"box-i"},["video"===S.value?(u(),m("video",C,[v("source",{src:w(z)+a},null,8,h)])):g("",!0),"image"===S.value?(u(),m("img",{key:1,src:w(z)+a},null,8,A)):g("",!0)])))),128))])),_:1})])])])),_:1},8,["spinning"])])),_:1},8,["open","confirm-loading"])}}}),[["__scopeId","data-v-ec31b3de"]]);function S(){const{cookies:a}=k();a.set("Authorization",a.get("ACCESS_TOKEN_U"))}export{I,S as u};
