import{s as e}from"./main-Djn9RDyT.js";const t={addUser:"/edtap/sys-user/add",editUser:"/edtap/sys-user/edit",userDetail:"/edtap/sys-user/detail",resetPassword:"/edtap/sys-user/reset-pwd",editTokenSwitch:"/edtap/sys-user/edit-token-switch",exportUserTemplate:"/edtap/sys-user/export-user-template",allPowerfulToken:"/edtap/validate-code/all-powerful-token",ownRole:"/edtap/sys-user/own-role",grantRole:"/edtap/sys-user/grant-role",importUserExcel:"/edtap/sys-user/import-user-excel",unbindUser:"/edtap/enterprise/unbind-user",getInviteCode:"/edtap/enterprise/invitationCode",joinTeam:"/edtap/enterprise/invitationTeam",exportEnterpriceUser:"/edtap/sys-user/enterprise/export"},r=t;function s(r){return e({url:t.addUser,method:"post",data:r,headers:{"Content-Type":"application/json"}})}function a(r){return e({url:t.editUser,method:"post",data:r,headers:{"Content-Type":"application/json"}})}function o(r){return e({url:t.userDetail,method:"get",params:r})}function n(r){return e({url:t.resetPassword,method:"post",data:r})}function d(r){return e({url:t.exportEnterpriceUser,method:"post",data:r,responseType:"blob"})}function u(r){return e({url:t.editTokenSwitch,method:"post",data:r})}function p(r){return e({url:t.exportUserTemplate,method:"get",params:r,responseType:"blob"})}function i(r){return e({url:t.allPowerfulToken,method:"post",data:r})}function l(r){return e({url:t.ownRole,method:"get",params:r})}function m(r){return e({url:t.grantRole,method:"post",data:r})}function c(r){return e({url:t.unbindUser,method:"post",data:r})}function h(r){return e({url:`${t.getInviteCode}`,method:"get",params:r})}function f(r){return e({url:`${t.joinTeam}`,method:"get",params:{code:r}})}export{s as a,h as b,u as c,i as d,a as e,r as f,o as g,p as h,l as i,m as j,f as k,n as r,d as s,c as u};
