import{s as e}from"./main-Djn9RDyT.js";const t={userList:"/edtap/sys-user/page",addUser:"/edtap/sys-user/add",editUser:"/edtap/sys-user/edit",userDetail:"/edtap/sys-user/detail",userStatus:"/edtap/sys-user/change-status",resetPassword:"/edtap/sys-user/reset-pwd",deleteUser:"/edtap/sys-user/delete",exportUser:"/edtap/sys-user/export",deleteBatch:"/edtap/sys-user/delete-batch",exportUserTemplate:"/edtap/sys-user/export-user-template",ownRole:"/edtap/sys-user/own-role",grantRole:"/edtap/sys-user/grant-role",importUserExcel:"/edtap/sys-user/import-user-excel",sysUserUpdatePwd:"/edtap/sys-user/update-pwd",addQuestion:"/edtap/sys-user/sys-question/add",editQuestion:"/edtap/sys-user/sys-question/edit",sysQuestionQuery:"/edtap/sys-user/sys-question/query",batchBindUser:"/edtap/enterprise/batch-bind-user",unbindUser:"/edtap/enterprise/unbind-user"},s=t;function r(s){return e({url:t.userList,method:"get",params:s})}function a(s){return e({url:t.addUser,method:"post",data:s,headers:{"Content-Type":"application/json"}})}function u(s){return e({url:t.editUser,method:"post",data:s,headers:{"Content-Type":"application/json"}})}function d(s){return e({url:t.batchBindUser,method:"post",data:s})}function n(s){return e({url:t.unbindUser,method:"post",data:s})}function o(s){return e({url:t.userDetail,method:"get",params:s})}function p(s){return e({url:t.userStatus,method:"post",data:s})}function i(s){return e({url:t.resetPassword,method:"post",data:s})}function l(s){return e({url:t.deleteUser,method:"post",data:s})}function c(s){return e({url:t.exportUser,method:"post",data:s,responseType:"blob"})}function m(s){return e({url:t.deleteBatch,method:"post",data:s})}function y(s){return e({url:t.exportUserTemplate,method:"get",params:s,responseType:"blob"})}function h(s){return e({url:t.ownRole,method:"get",params:s})}function f(s){return e({url:t.grantRole,method:"post",data:s})}function U(s){return e({url:t.sysUserUpdatePwd,method:"post",data:s})}function b(s){return e({url:t.addQuestion,method:"post",data:s})}function g(s){return e({url:t.editQuestion,method:"post",data:s})}function w(s){return e({url:t.sysQuestionQuery,method:"get",data:s})}export{o as a,d as b,a as c,c as d,u as e,l as f,r as g,p as h,m as i,s as j,y as k,h as l,f as m,w as n,b as o,g as p,i as r,U as s,n as u};
