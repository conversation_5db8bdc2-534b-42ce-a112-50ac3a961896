import{g as t}from"./@babel-B4rXMRun.js";var e={exports:{}},r={exports:{}};!function(t,e){function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(!("string"==typeof t||t instanceof String)){var e=r(t);throw null===t?e="null":"object"===e&&(e=t.constructor.name),new TypeError("Expected a string but received a ".concat(e))}},t.exports=e.default,t.exports.default=e.default}(r,r.exports);var n,o,l=r.exports,a={exports:{}},u={exports:{}};n=u,o=u.exports,Object.defineProperty(o,"__esModule",{value:!0}),o.default=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;for(var r in e)void 0===t[r]&&(t[r]=e[r]);return t},n.exports=o.default,n.exports.default=o.default;var i=u.exports;!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){(0,r.default)(t),(e=(0,n.default)(e,a)).allow_trailing_dot&&"."===t[t.length-1]&&(t=t.substring(0,t.length-1));!0===e.allow_wildcard&&0===t.indexOf("*.")&&(t=t.substring(2));var o=t.split("."),l=o[o.length-1];if(e.require_tld){if(o.length<2)return!1;if(!e.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(l))return!1;if(/\s/.test(l))return!1}if(!e.allow_numeric_tld&&/^\d+$/.test(l))return!1;return o.every((function(t){return!(t.length>63&&!e.ignore_max_length)&&(!!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(t)&&(!/[\uff01-\uff5e]/.test(t)&&(!/^-|-$/.test(t)&&!(!e.allow_underscores&&/_/.test(t)))))}))};var r=o(l),n=o(i);function o(t){return t&&t.__esModule?t:{default:t}}var a={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1};t.exports=e.default,t.exports.default=e.default}(a,a.exports);var c=a.exports,f={exports:{}};!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if((0,n.default)(e),!(r=String(r)))return t(e,4)||t(e,6);if("4"===r)return u.test(e);if("6"===r)return c.test(e);return!1};var r,n=(r=l)&&r.__esModule?r:{default:r};var o="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",a="(".concat(o,"[.]){3}").concat(o),u=new RegExp("^".concat(a,"$")),i="(?:[0-9a-fA-F]{1,4})",c=new RegExp("^("+"(?:".concat(i,":){7}(?:").concat(i,"|:)|")+"(?:".concat(i,":){6}(?:").concat(a,"|:").concat(i,"|:)|")+"(?:".concat(i,":){5}(?::").concat(a,"|(:").concat(i,"){1,2}|:)|")+"(?:".concat(i,":){4}(?:(:").concat(i,"){0,1}:").concat(a,"|(:").concat(i,"){1,3}|:)|")+"(?:".concat(i,":){3}(?:(:").concat(i,"){0,2}:").concat(a,"|(:").concat(i,"){1,4}|:)|")+"(?:".concat(i,":){2}(?:(:").concat(i,"){0,3}:").concat(a,"|(:").concat(i,"){1,5}|:)|")+"(?:".concat(i,":){1}(?:(:").concat(i,"){0,4}:").concat(a,"|(:").concat(i,"){1,6}|:)|")+"(?::((?::".concat(i,"){0,5}:").concat(a,"|(?::").concat(i,"){1,7}|:))")+")(%[0-9a-zA-Z-.:]{1,})?$");t.exports=e.default,t.exports.default=e.default}(f,f.exports);var s=f.exports;!function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){if((0,r.default)(t),!t||/[\s<>]/.test(t))return!1;if(0===t.indexOf("mailto:"))return!1;if((e=(0,a.default)(e,d)).validate_length&&t.length>=2083)return!1;if(!e.allow_fragments&&t.includes("#"))return!1;if(!e.allow_query_components&&(t.includes("?")||t.includes("&")))return!1;var l,u,i,c,s,h,v,g;if(v=t.split("#"),t=v.shift(),v=t.split("?"),t=v.shift(),(v=t.split("://")).length>1){if(l=v.shift().toLowerCase(),e.require_valid_protocol&&-1===e.protocols.indexOf(l))return!1}else{if(e.require_protocol)return!1;if("//"===t.slice(0,2)){if(!e.allow_protocol_relative_urls)return!1;v[0]=t.slice(2)}}if(""===(t=v.join("://")))return!1;if(v=t.split("/"),""===(t=v.shift())&&!e.require_host)return!0;if((v=t.split("@")).length>1){if(e.disallow_auth)return!1;if(""===v[0])return!1;if((u=v.shift()).indexOf(":")>=0&&u.split(":").length>2)return!1;var y=u.split(":"),x=(j=2,function(t){if(Array.isArray(t))return t}(w=y)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,l,a,u=[],i=!0,c=!1;try{if(l=(r=r.call(t)).next,0===e);else for(;!(i=(n=l.call(r)).done)&&(u.push(n.value),u.length!==e);i=!0);}catch(f){c=!0,o=f}finally{try{if(!i&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(w,j)||function(t,e){if(t){if("string"==typeof t)return f(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}(w,j)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),b=x[0],m=x[1];if(""===b&&""===m)return!1}var w,j;c=v.join("@"),h=null,g=null;var q=c.match(p);q?(i="",g=q[1],h=q[2]||null):(i=(v=c.split(":")).shift(),v.length&&(h=v.join(":")));if(null!==h&&h.length>0){if(s=parseInt(h,10),!/^[0-9]+$/.test(h)||s<=0||s>65535)return!1}else if(e.require_port)return!1;if(e.host_whitelist)return _(i,e.host_whitelist);if(""===i&&!e.require_host)return!0;if(!((0,o.default)(i)||(0,n.default)(i,e)||g&&(0,o.default)(g,6)))return!1;if(i=i||g,e.host_blacklist&&_(i,e.host_blacklist))return!1;return!0};var r=u(l),n=u(c),o=u(s),a=u(i);function u(t){return t&&t.__esModule?t:{default:t}}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var d={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_port:!1,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1,allow_fragments:!0,allow_query_components:!0,validate_length:!0},p=/^\[([^\]]+)\](?::([0-9]+))?$/;function _(t,e){for(var r=0;r<e.length;r++){var n=e[r];if(t===n||(o=n,"[object RegExp]"===Object.prototype.toString.call(o)&&n.test(t)))return!0}var o;return!1}t.exports=e.default,t.exports.default=e.default}(e,e.exports);const d=t(e.exports);export{d as i};
