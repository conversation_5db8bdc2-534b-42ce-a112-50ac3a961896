function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,o(n.key),n)}}function e(t,e,i){return(e=o(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function i(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function n(t){for(var n=1;n<arguments.length;n++){var o=null!=arguments[n]?arguments[n]:{};n%2?i(Object(o),!0).forEach((function(i){e(t,i,o[i])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):i(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}function o(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof e?e:e+""}function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var a={backdrop:!0,button:!0,navbar:!0,title:!0,toolbar:!0,className:"",container:"body",filter:null,fullscreen:!0,inheritedAttributes:["crossOrigin","decoding","isMap","loading","referrerPolicy","sizes","srcset","useMap"],initialCoverage:.9,initialViewIndex:0,inline:!1,interval:5e3,keyboard:!0,focus:!0,loading:!0,loop:!0,minWidth:200,minHeight:100,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,slideOnTouch:!0,toggleOnDblclick:!0,tooltip:!0,transition:!0,zIndex:2015,zIndexInline:0,zoomRatio:.1,minZoomRatio:.01,maxZoomRatio:100,url:"src",ready:null,show:null,shown:null,hide:null,hidden:null,view:null,viewed:null,move:null,moved:null,rotate:null,rotated:null,scale:null,scaled:null,zoom:null,zoomed:null,play:null,stop:null},r="undefined"!=typeof window&&void 0!==window.document,l=r?window:{},h=!(!r||!l.document.documentElement)&&"ontouchstart"in l.document.documentElement,c=!!r&&"PointerEvent"in l,u="viewer",d="move",m="switch",f="zoom",g="".concat(u,"-active"),v="".concat(u,"-close"),p="".concat(u,"-fade"),b="".concat(u,"-fixed"),w="".concat(u,"-fullscreen"),y="".concat(u,"-fullscreen-exit"),x="".concat(u,"-hide"),k="".concat(u,"-hide-md-down"),z="".concat(u,"-hide-sm-down"),T="".concat(u,"-hide-xs-down"),E="".concat(u,"-in"),D="".concat(u,"-invisible"),S="".concat(u,"-loading"),I="".concat(u,"-move"),A="".concat(u,"-open"),O="".concat(u,"-show"),C="".concat(u,"-transition"),L="click",R="dblclick",M="dragstart",F="focusin",N="keydown",Y="load",X="error",q=c?"pointerdown":h?"touchstart":"mousedown",P=c?"pointermove":h?"touchmove":"mousemove",W=c?"pointerup pointercancel":h?"touchend touchcancel":"mouseup",j="resize",H="transitionend",B="wheel",V="ready",U="show",K="shown",Z="hide",$="hidden",_="view",G="viewed",J="move",Q="moved",tt="rotate",et="rotated",it="scale",nt="scaled",ot="zoom",st="zoomed",at="play",rt="stop",lt="".concat(u,"Action"),ht=/\s\s*/,ct=["zoom-in","zoom-out","one-to-one","reset","prev","play","next","rotate-left","rotate-right","flip-horizontal","flip-vertical"];function ut(t){return"string"==typeof t}var dt=Number.isNaN||l.isNaN;function mt(t){return"number"==typeof t&&!dt(t)}function ft(t){return void 0===t}function gt(t){return"object"===s(t)&&null!==t}var vt=Object.prototype.hasOwnProperty;function pt(t){if(!gt(t))return!1;try{var e=t.constructor,i=e.prototype;return e&&i&&vt.call(i,"isPrototypeOf")}catch(n){return!1}}function bt(t){return"function"==typeof t}function wt(t,e){if(t&&bt(e))if(Array.isArray(t)||mt(t.length)){var i,n=t.length;for(i=0;i<n&&!1!==e.call(t,t[i],i,t);i+=1);}else gt(t)&&Object.keys(t).forEach((function(i){e.call(t,t[i],i,t)}));return t}var yt=Object.assign||function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];return gt(t)&&i.length>0&&i.forEach((function(e){gt(e)&&Object.keys(e).forEach((function(i){t[i]=e[i]}))})),t},xt=/^(?:width|height|left|top|marginLeft|marginTop)$/;function kt(t,e){var i=t.style;wt(e,(function(t,e){xt.test(e)&&mt(t)&&(t+="px"),i[e]=t}))}function zt(t,e){return!(!t||!e)&&(t.classList?t.classList.contains(e):t.className.indexOf(e)>-1)}function Tt(t,e){if(t&&e)if(mt(t.length))wt(t,(function(t){Tt(t,e)}));else if(t.classList)t.classList.add(e);else{var i=t.className.trim();i?i.indexOf(e)<0&&(t.className="".concat(i," ").concat(e)):t.className=e}}function Et(t,e){t&&e&&(mt(t.length)?wt(t,(function(t){Et(t,e)})):t.classList?t.classList.remove(e):t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,"")))}function Dt(t,e,i){e&&(mt(t.length)?wt(t,(function(t){Dt(t,e,i)})):i?Tt(t,e):Et(t,e))}var St=/([a-z\d])([A-Z])/g;function It(t){return t.replace(St,"$1-$2").toLowerCase()}function At(t,e){return gt(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(It(e)))}function Ot(t,e,i){gt(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute("data-".concat(It(e)),i)}var Ct=function(){var t=!1;if(r){var e=!1,i=function(){},n=Object.defineProperty({},"once",{get:function(){return t=!0,e},set:function(t){e=t}});l.addEventListener("test",i,n),l.removeEventListener("test",i,n)}return t}();function Lt(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=i;e.trim().split(ht).forEach((function(e){if(!Ct){var s=t.listeners;s&&s[e]&&s[e][i]&&(o=s[e][i],delete s[e][i],0===Object.keys(s[e]).length&&delete s[e],0===Object.keys(s).length&&delete t.listeners)}t.removeEventListener(e,o,n)}))}function Rt(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=i;e.trim().split(ht).forEach((function(e){if(n.once&&!Ct){var s=t.listeners,a=void 0===s?{}:s;o=function(){delete a[e][i],t.removeEventListener(e,o,n);for(var s=arguments.length,r=new Array(s),l=0;l<s;l++)r[l]=arguments[l];i.apply(t,r)},a[e]||(a[e]={}),a[e][i]&&t.removeEventListener(e,a[e][i],n),a[e][i]=o,t.listeners=a}t.addEventListener(e,o,n)}))}function Mt(t,e,i,o){var s;return bt(Event)&&bt(CustomEvent)?s=new CustomEvent(e,n({bubbles:!0,cancelable:!0,detail:i},o)):(s=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,i),t.dispatchEvent(s)}function Ft(t){var e=t.rotate,i=t.scaleX,n=t.scaleY,o=t.translateX,s=t.translateY,a=[];mt(o)&&0!==o&&a.push("translateX(".concat(o,"px)")),mt(s)&&0!==s&&a.push("translateY(".concat(s,"px)")),mt(e)&&0!==e&&a.push("rotate(".concat(e,"deg)")),mt(i)&&1!==i&&a.push("scaleX(".concat(i,")")),mt(n)&&1!==n&&a.push("scaleY(".concat(n,")"));var r=a.length?a.join(" "):"none";return{WebkitTransform:r,msTransform:r,transform:r}}var Nt=l.navigator&&/Version\/\d+(\.\d+)+?\s+Safari/i.test(l.navigator.userAgent);function Yt(t,e,i){var n=document.createElement("img");if(t.naturalWidth&&!Nt)return i(t.naturalWidth,t.naturalHeight),n;var o=document.body||document.documentElement;return n.onload=function(){i(n.width,n.height),Nt||o.removeChild(n)},wt(e.inheritedAttributes,(function(e){var i=t.getAttribute(e);null!==i&&n.setAttribute(e,i)})),n.src=t.src,Nt||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(n)),n}function Xt(t){switch(t){case 2:return T;case 3:return z;case 4:return k;default:return""}}function qt(t,e){var i=t.pageX,o=t.pageY,s={endX:i,endY:o};return e?s:n({timeStamp:Date.now(),startX:i,startY:o},s)}var Pt={render:function(){this.initContainer(),this.initViewer(),this.initList(),this.renderViewer()},initBody:function(){var t=this.element.ownerDocument,e=t.body||t.documentElement;this.body=e,this.scrollbarWidth=window.innerWidth-t.documentElement.clientWidth,this.initialBodyPaddingRight=e.style.paddingRight,this.initialBodyComputedPaddingRight=window.getComputedStyle(e).paddingRight},initContainer:function(){this.containerData={width:window.innerWidth,height:window.innerHeight}},initViewer:function(){var t,e=this.options,i=this.parent;e.inline&&(t={width:Math.max(i.offsetWidth,e.minWidth),height:Math.max(i.offsetHeight,e.minHeight)},this.parentData=t),!this.fulled&&t||(t=this.containerData),this.viewerData=yt({},t)},renderViewer:function(){this.options.inline&&!this.fulled&&kt(this.viewer,this.viewerData)},initList:function(){var t=this,e=this.element,i=this.options,n=this.list,o=[];n.innerHTML="",wt(this.images,(function(e,s){var a=e.src,r=e.alt||function(t){return ut(t)?decodeURIComponent(t.replace(/^.*\//,"").replace(/[?&#].*$/,"")):""}(a),l=t.getImageURL(e);if(a||l){var h=document.createElement("li"),c=document.createElement("img");wt(i.inheritedAttributes,(function(t){var i=e.getAttribute(t);null!==i&&c.setAttribute(t,i)})),i.navbar&&(c.src=a||l),c.alt=r,c.setAttribute("data-original-url",l||a),h.setAttribute("data-index",s),h.setAttribute("data-viewer-action","view"),h.setAttribute("role","button"),i.keyboard&&h.setAttribute("tabindex",0),h.appendChild(c),n.appendChild(h),o.push(h)}})),this.items=o,wt(o,(function(e){var n,o,s=e.firstElementChild;Ot(s,"filled",!0),i.loading&&Tt(e,S),Rt(s,Y,n=function(n){Lt(s,X,o),i.loading&&Et(e,S),t.loadImage(n)},{once:!0}),Rt(s,X,o=function(){Lt(s,Y,n),i.loading&&Et(e,S)},{once:!0})})),i.transition&&Rt(e,G,(function(){Tt(n,C)}),{once:!0})},renderList:function(){var t=this.index,e=this.items[t];if(e){var i=e.nextElementSibling,n=parseInt(window.getComputedStyle(i||e).marginLeft,10),o=e.offsetWidth,s=o+n;kt(this.list,yt({width:s*this.length-n},Ft({translateX:(this.viewerData.width-o)/2-s*t})))}},resetList:function(){var t=this.list;t.innerHTML="",Et(t,C),kt(t,Ft({translateX:0}))},initImage:function(t){var e,i=this,n=this.options,o=this.image,s=this.viewerData,a=this.footer.offsetHeight,r=s.width,l=Math.max(s.height-a,a),h=this.imageData||{};this.imageInitializing={abort:function(){e.onload=null}},e=Yt(o,n,(function(e,o){var s=e/o,a=Math.max(0,Math.min(1,n.initialCoverage)),c=r,u=l;i.imageInitializing=!1,l*s>r?u=r/s:c=l*s,a=mt(a)?a:.9,c=Math.min(c*a,e),u=Math.min(u*a,o);var d=(r-c)/2,m=(l-u)/2,f={left:d,top:m,x:d,y:m,width:c,height:u,oldRatio:1,ratio:c/e,aspectRatio:s,naturalWidth:e,naturalHeight:o},g=yt({},f);n.rotatable&&(f.rotate=h.rotate||0,g.rotate=0),n.scalable&&(f.scaleX=h.scaleX||1,f.scaleY=h.scaleY||1,g.scaleX=1,g.scaleY=1),i.imageData=f,i.initialImageData=g,t&&t()}))},renderImage:function(t){var e=this,i=this.image,n=this.imageData;if(kt(i,yt({width:n.width,height:n.height,marginLeft:n.x,marginTop:n.y},Ft(n))),t)if((this.viewing||this.moving||this.rotating||this.scaling||this.zooming)&&this.options.transition&&zt(i,C)){var o=function(){e.imageRendering=!1,t()};this.imageRendering={abort:function(){Lt(i,H,o)}},Rt(i,H,o,{once:!0})}else t()},resetImage:function(){var t=this.image;t&&(this.viewing&&this.viewing.abort(),t.parentNode.removeChild(t),this.image=null,this.title.innerHTML="")}},Wt={bind:function(){var t=this.options,e=this.viewer,i=this.canvas,n=this.element.ownerDocument;Rt(e,L,this.onClick=this.click.bind(this)),Rt(e,M,this.onDragStart=this.dragstart.bind(this)),Rt(i,q,this.onPointerDown=this.pointerdown.bind(this)),Rt(n,P,this.onPointerMove=this.pointermove.bind(this)),Rt(n,W,this.onPointerUp=this.pointerup.bind(this)),Rt(n,N,this.onKeyDown=this.keydown.bind(this)),Rt(window,j,this.onResize=this.resize.bind(this)),t.zoomable&&t.zoomOnWheel&&Rt(e,B,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),t.toggleOnDblclick&&Rt(i,R,this.onDblclick=this.dblclick.bind(this))},unbind:function(){var t=this.options,e=this.viewer,i=this.canvas,n=this.element.ownerDocument;Lt(e,L,this.onClick),Lt(e,M,this.onDragStart),Lt(i,q,this.onPointerDown),Lt(n,P,this.onPointerMove),Lt(n,W,this.onPointerUp),Lt(n,N,this.onKeyDown),Lt(window,j,this.onResize),t.zoomable&&t.zoomOnWheel&&Lt(e,B,this.onWheel,{passive:!1,capture:!0}),t.toggleOnDblclick&&Lt(i,R,this.onDblclick)}},jt={click:function(t){var e=this.options,i=this.imageData,n=t.target,o=At(n,lt);switch(o||"img"!==n.localName||"li"!==n.parentElement.localName||(o=At(n=n.parentElement,lt)),h&&t.isTrusted&&n===this.canvas&&clearTimeout(this.clickCanvasTimeout),o){case"mix":this.played?this.stop():e.inline?this.fulled?this.exit():this.full():this.hide();break;case"hide":this.pointerMoved||this.hide();break;case"view":this.view(At(n,"index"));break;case"zoom-in":this.zoom(.1,!0);break;case"zoom-out":this.zoom(-.1,!0);break;case"one-to-one":this.toggle();break;case"reset":this.reset();break;case"prev":this.prev(e.loop);break;case"play":this.play(e.fullscreen);break;case"next":this.next(e.loop);break;case"rotate-left":this.rotate(-90);break;case"rotate-right":this.rotate(90);break;case"flip-horizontal":this.scaleX(-i.scaleX||-1);break;case"flip-vertical":this.scaleY(-i.scaleY||-1);break;default:this.played&&this.stop()}},dblclick:function(t){t.preventDefault(),this.viewed&&t.target===this.image&&(h&&t.isTrusted&&clearTimeout(this.doubleClickImageTimeout),this.toggle(t.isTrusted?t:t.detail&&t.detail.originalEvent))},load:function(){var t=this;this.timeout&&(clearTimeout(this.timeout),this.timeout=!1);var e=this.element,i=this.options,n=this.image,o=this.index,s=this.viewerData;Et(n,D),i.loading&&Et(this.canvas,S),n.style.cssText="height:0;"+"margin-left:".concat(s.width/2,"px;")+"margin-top:".concat(s.height/2,"px;")+"max-width:none!important;position:relative;width:0;",this.initImage((function(){Dt(n,I,i.movable),Dt(n,C,i.transition),t.renderImage((function(){t.viewed=!0,t.viewing=!1,bt(i.viewed)&&Rt(e,G,i.viewed,{once:!0}),Mt(e,G,{originalImage:t.images[o],index:o,image:n},{cancelable:!1})}))}))},loadImage:function(t){var e=t.target,i=e.parentNode,n=i.offsetWidth||30,o=i.offsetHeight||50,s=!!At(e,"filled");Yt(e,this.options,(function(t,i){var a=t/i,r=n,l=o;o*a>n?s?r=o*a:l=n/a:s?l=n/a:r=o*a,kt(e,yt({width:r,height:l},Ft({translateX:(n-r)/2,translateY:(o-l)/2})))}))},keydown:function(t){var e=this.options;if(e.keyboard){var i=t.keyCode||t.which||t.charCode;if(13===i)this.viewer.contains(t.target)&&this.click(t);if(this.fulled)switch(i){case 27:this.played?this.stop():e.inline?this.fulled&&this.exit():this.hide();break;case 32:this.played&&this.stop();break;case 37:this.played&&this.playing?this.playing.prev():this.prev(e.loop);break;case 38:t.preventDefault(),this.zoom(e.zoomRatio,!0);break;case 39:this.played&&this.playing?this.playing.next():this.next(e.loop);break;case 40:t.preventDefault(),this.zoom(-e.zoomRatio,!0);break;case 48:case 49:t.ctrlKey&&(t.preventDefault(),this.toggle())}}},dragstart:function(t){"img"===t.target.localName&&t.preventDefault()},pointerdown:function(t){var e=this.options,i=this.pointers,n=t.buttons,o=t.button;if(this.pointerMoved=!1,!(!this.viewed||this.showing||this.viewing||this.hiding||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(mt(n)&&1!==n||mt(o)&&0!==o||t.ctrlKey))){t.preventDefault(),t.changedTouches?wt(t.changedTouches,(function(t){i[t.identifier]=qt(t)})):i[t.pointerId||0]=qt(t);var s=!!e.movable&&d;e.zoomOnTouch&&e.zoomable&&Object.keys(i).length>1?s=f:e.slideOnTouch&&("touch"===t.pointerType||"touchstart"===t.type)&&this.isSwitchable()&&(s=m),!e.transition||s!==d&&s!==f||Et(this.image,C),this.action=s}},pointermove:function(t){var e=this.pointers,i=this.action;this.viewed&&i&&(t.preventDefault(),t.changedTouches?wt(t.changedTouches,(function(t){yt(e[t.identifier]||{},qt(t,!0))})):yt(e[t.pointerId||0]||{},qt(t,!0)),this.change(t))},pointerup:function(t){var e,i=this,n=this.options,o=this.action,s=this.pointers;t.changedTouches?wt(t.changedTouches,(function(t){e=s[t.identifier],delete s[t.identifier]})):(e=s[t.pointerId||0],delete s[t.pointerId||0]),o&&(t.preventDefault(),!n.transition||o!==d&&o!==f||Tt(this.image,C),this.action=!1,h&&o!==f&&e&&Date.now()-e.timeStamp<500&&(clearTimeout(this.clickCanvasTimeout),clearTimeout(this.doubleClickImageTimeout),n.toggleOnDblclick&&this.viewed&&t.target===this.image?this.imageClicked?(this.imageClicked=!1,this.doubleClickImageTimeout=setTimeout((function(){Mt(i.image,R,{originalEvent:t})}),50)):(this.imageClicked=!0,this.doubleClickImageTimeout=setTimeout((function(){i.imageClicked=!1}),500)):(this.imageClicked=!1,n.backdrop&&"static"!==n.backdrop&&t.target===this.canvas&&(this.clickCanvasTimeout=setTimeout((function(){Mt(i.canvas,L,{originalEvent:t})}),50)))))},resize:function(){var t=this;if(this.isShown&&!this.hiding&&(this.fulled&&(this.close(),this.initBody(),this.open()),this.initContainer(),this.initViewer(),this.renderViewer(),this.renderList(),this.viewed&&this.initImage((function(){t.renderImage()})),this.played)){if(this.options.fullscreen&&this.fulled&&!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement))return void this.stop();wt(this.player.getElementsByTagName("img"),(function(e){Rt(e,Y,t.loadImage.bind(t),{once:!0}),Mt(e,Y)}))}},wheel:function(t){var e=this;if(this.viewed&&(t.preventDefault(),!this.wheeling)){this.wheeling=!0,setTimeout((function(){e.wheeling=!1}),50);var i=Number(this.options.zoomRatio)||.1,n=1;t.deltaY?n=t.deltaY>0?1:-1:t.wheelDelta?n=-t.wheelDelta/120:t.detail&&(n=t.detail>0?1:-1),this.zoom(-n*i,!0,null,t)}}},Ht={show:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.element,i=this.options;if(i.inline||this.showing||this.isShown||this.showing)return this;if(!this.ready)return this.build(),this.ready&&this.show(t),this;if(bt(i.show)&&Rt(e,U,i.show,{once:!0}),!1===Mt(e,U)||!this.ready)return this;this.hiding&&this.transitioning.abort(),this.showing=!0,this.open();var n=this.viewer;if(Et(n,x),n.setAttribute("role","dialog"),n.setAttribute("aria-labelledby",this.title.id),n.setAttribute("aria-modal",!0),n.removeAttribute("aria-hidden"),i.transition&&!t){var o=this.shown.bind(this);this.transitioning={abort:function(){Lt(n,H,o),Et(n,E)}},Tt(n,C),n.initialOffsetWidth=n.offsetWidth,Rt(n,H,o,{once:!0}),Tt(n,E)}else Tt(n,E),this.shown();return this},hide:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.element,n=this.options;if(n.inline||this.hiding||!this.isShown&&!this.showing)return this;if(bt(n.hide)&&Rt(i,Z,n.hide,{once:!0}),!1===Mt(i,Z))return this;this.showing&&this.transitioning.abort(),this.hiding=!0,this.played?this.stop():this.viewing&&this.viewing.abort();var o=this.viewer,s=this.image,a=function(){Et(o,E),t.hidden()};if(n.transition&&!e){var r=function(e){e&&e.target===o&&(Lt(o,H,r),t.hidden())},l=function(){zt(o,C)?(Rt(o,H,r),Et(o,E)):a()};this.transitioning={abort:function(){t.viewed&&zt(s,C)?Lt(s,H,l):zt(o,C)&&Lt(o,H,r)}},this.viewed&&zt(s,C)?(Rt(s,H,l,{once:!0}),this.zoomTo(0,!1,null,null,!0)):l()}else a();return this},view:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.initialViewIndex;if(e=Number(e)||0,this.hiding||this.played||e<0||e>=this.length||this.viewed&&e===this.index)return this;if(!this.isShown)return this.index=e,this.show();this.viewing&&this.viewing.abort();var i=this.element,n=this.options,o=this.title,s=this.canvas,a=this.items[e],r=a.querySelector("img"),l=At(r,"originalUrl"),h=r.getAttribute("alt"),c=document.createElement("img");if(wt(n.inheritedAttributes,(function(t){var e=r.getAttribute(t);null!==e&&c.setAttribute(t,e)})),c.src=l,c.alt=h,bt(n.view)&&Rt(i,_,n.view,{once:!0}),!1===Mt(i,_,{originalImage:this.images[e],index:e,image:c})||!this.isShown||this.hiding||this.played)return this;var u=this.items[this.index];u&&(Et(u,g),u.removeAttribute("aria-selected")),Tt(a,g),a.setAttribute("aria-selected",!0),n.focus&&a.focus(),this.image=c,this.viewed=!1,this.index=e,this.imageData={},Tt(c,D),n.loading&&Tt(s,S),s.innerHTML="",s.appendChild(c),this.renderList(),o.innerHTML="";var d,m,f=function(){var e,i=t.imageData,s=Array.isArray(n.title)?n.title[1]:n.title;o.innerHTML=ut(e=bt(s)?s.call(t,c,i):"".concat(h," (").concat(i.naturalWidth," × ").concat(i.naturalHeight,")"))?e.replace(/&(?!amp;|quot;|#39;|lt;|gt;)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):e};return Rt(i,G,f,{once:!0}),this.viewing={abort:function(){Lt(i,G,f),c.complete?t.imageRendering?t.imageRendering.abort():t.imageInitializing&&t.imageInitializing.abort():(c.src="",Lt(c,Y,d),t.timeout&&clearTimeout(t.timeout))}},c.complete?this.load():(Rt(c,Y,d=function(){Lt(c,X,m),t.load()},{once:!0}),Rt(c,X,m=function(){Lt(c,Y,d),t.timeout&&(clearTimeout(t.timeout),t.timeout=!1),Et(c,D),n.loading&&Et(t.canvas,S)},{once:!0}),this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout((function(){Et(c,D),t.timeout=!1}),1e3)),this},prev:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.index-1;return e<0&&(e=t?this.length-1:0),this.view(e),this},next:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.length-1,i=this.index+1;return i>e&&(i=t?0:e),this.view(i),this},move:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.imageData;return this.moveTo(ft(t)?t:i.x+Number(t),ft(e)?e:i.y+Number(e)),this},moveTo:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=this.element,s=this.options,a=this.imageData;if(t=Number(t),i=Number(i),this.viewed&&!this.played&&s.movable){var r=a.x,l=a.y,h=!1;if(mt(t)?h=!0:t=r,mt(i)?h=!0:i=l,h){if(bt(s.move)&&Rt(o,J,s.move,{once:!0}),!1===Mt(o,J,{x:t,y:i,oldX:r,oldY:l,originalEvent:n}))return this;a.x=t,a.y=i,a.left=t,a.top=i,this.moving=!0,this.renderImage((function(){e.moving=!1,bt(s.moved)&&Rt(o,Q,s.moved,{once:!0}),Mt(o,Q,{x:t,y:i,oldX:r,oldY:l,originalEvent:n},{cancelable:!1})}))}}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t)),this},rotateTo:function(t){var e=this,i=this.element,n=this.options,o=this.imageData;if(mt(t=Number(t))&&this.viewed&&!this.played&&n.rotatable){var s=o.rotate;if(bt(n.rotate)&&Rt(i,tt,n.rotate,{once:!0}),!1===Mt(i,tt,{degree:t,oldDegree:s}))return this;o.rotate=t,this.rotating=!0,this.renderImage((function(){e.rotating=!1,bt(n.rotated)&&Rt(i,et,n.rotated,{once:!0}),Mt(i,et,{degree:t,oldDegree:s},{cancelable:!1})}))}return this},scaleX:function(t){return this.scale(t,this.imageData.scaleY),this},scaleY:function(t){return this.scale(this.imageData.scaleX,t),this},scale:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,n=this.element,o=this.options,s=this.imageData;if(t=Number(t),i=Number(i),this.viewed&&!this.played&&o.scalable){var a=s.scaleX,r=s.scaleY,l=!1;if(mt(t)?l=!0:t=a,mt(i)?l=!0:i=r,l){if(bt(o.scale)&&Rt(n,it,o.scale,{once:!0}),!1===Mt(n,it,{scaleX:t,scaleY:i,oldScaleX:a,oldScaleY:r}))return this;s.scaleX=t,s.scaleY=i,this.scaling=!0,this.renderImage((function(){e.scaling=!1,bt(o.scaled)&&Rt(n,nt,o.scaled,{once:!0}),Mt(n,nt,{scaleX:t,scaleY:i,oldScaleX:a,oldScaleY:r},{cancelable:!1})}))}}return this},zoom:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=this.imageData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(o.width*t/o.naturalWidth,e,i,n),this},zoomTo:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,s=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=this.element,r=this.options,l=this.pointers,h=this.imageData,c=h.x,u=h.y,d=h.width,m=h.height,f=h.naturalWidth,g=h.naturalHeight;if(mt(t=Math.max(0,t))&&this.viewed&&!this.played&&(s||r.zoomable)){if(!s){var v=Math.max(.01,r.minZoomRatio),p=Math.min(100,r.maxZoomRatio);t=Math.min(Math.max(t,v),p)}if(o)switch(o.type){case"wheel":r.zoomRatio>=.055&&t>.95&&t<1.05&&(t=1);break;case"pointermove":case"touchmove":case"mousemove":t>.99&&t<1.01&&(t=1)}var b=f*t,w=g*t,y=b-d,x=w-m,k=h.ratio;if(bt(r.zoom)&&Rt(a,ot,r.zoom,{once:!0}),!1===Mt(a,ot,{ratio:t,oldRatio:k,originalEvent:o}))return this;if(this.zooming=!0,o){var z=function(t){var e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}(this.viewer),T=l&&Object.keys(l).length>0?function(t){var e=0,i=0,n=0;return wt(t,(function(t){var o=t.startX,s=t.startY;e+=o,i+=s,n+=1})),{pageX:e/=n,pageY:i/=n}}(l):{pageX:o.pageX,pageY:o.pageY};h.x-=y*((T.pageX-z.left-c)/d),h.y-=x*((T.pageY-z.top-u)/m)}else pt(n)&&mt(n.x)&&mt(n.y)?(h.x-=y*((n.x-c)/d),h.y-=x*((n.y-u)/m)):(h.x-=y/2,h.y-=x/2);h.left=h.x,h.top=h.y,h.width=b,h.height=w,h.oldRatio=k,h.ratio=t,this.renderImage((function(){e.zooming=!1,bt(r.zoomed)&&Rt(a,st,r.zoomed,{once:!0}),Mt(a,st,{ratio:t,oldRatio:k,originalEvent:o},{cancelable:!1})})),i&&this.tooltip()}return this},play:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.isShown||this.played)return this;var i=this.element,n=this.options;if(bt(n.play)&&Rt(i,at,n.play,{once:!0}),!1===Mt(i,at))return this;var o=this.player,s=this.loadImage.bind(this),a=[],r=0,l=0;if(this.played=!0,this.onLoadWhenPlay=s,e&&this.requestFullscreen(e),Tt(o,O),wt(this.items,(function(t,e){var i=t.querySelector("img"),h=document.createElement("img");h.src=At(i,"originalUrl"),h.alt=i.getAttribute("alt"),h.referrerPolicy=i.referrerPolicy,r+=1,Tt(h,p),Dt(h,C,n.transition),zt(t,g)&&(Tt(h,E),l=e),a.push(h),Rt(h,Y,s,{once:!0}),o.appendChild(h)})),mt(n.interval)&&n.interval>0){var h=function(){clearTimeout(t.playing.timeout),Et(a[l],E),Tt(a[l=(l-=1)>=0?l:r-1],E),t.playing.timeout=setTimeout(h,n.interval)},c=function(){clearTimeout(t.playing.timeout),Et(a[l],E),Tt(a[l=(l+=1)<r?l:0],E),t.playing.timeout=setTimeout(c,n.interval)};r>1&&(this.playing={prev:h,next:c,timeout:setTimeout(c,n.interval)})}return this},stop:function(){var t=this;if(!this.played)return this;var e=this.element,i=this.options;if(bt(i.stop)&&Rt(e,rt,i.stop,{once:!0}),!1===Mt(e,rt))return this;var n=this.player;return clearTimeout(this.playing.timeout),this.playing=!1,this.played=!1,wt(n.getElementsByTagName("img"),(function(e){Lt(e,Y,t.onLoadWhenPlay)})),Et(n,O),n.innerHTML="",this.exitFullscreen(),this},full:function(){var t=this,e=this.options,i=this.viewer,n=this.image,o=this.list;return!this.isShown||this.played||this.fulled||!e.inline||(this.fulled=!0,this.open(),Tt(this.button,y),e.transition&&(Et(o,C),this.viewed&&Et(n,C)),Tt(i,b),i.setAttribute("role","dialog"),i.setAttribute("aria-labelledby",this.title.id),i.setAttribute("aria-modal",!0),i.removeAttribute("style"),kt(i,{zIndex:e.zIndex}),e.focus&&this.enforceFocus(),this.initContainer(),this.viewerData=yt({},this.containerData),this.renderList(),this.viewed&&this.initImage((function(){t.renderImage((function(){e.transition&&setTimeout((function(){Tt(n,C),Tt(o,C)}),0)}))}))),this},exit:function(){var t=this,e=this.options,i=this.viewer,n=this.image,o=this.list;return this.isShown&&!this.played&&this.fulled&&e.inline?(this.fulled=!1,this.close(),Et(this.button,y),e.transition&&(Et(o,C),this.viewed&&Et(n,C)),e.focus&&this.clearEnforceFocus(),i.removeAttribute("role"),i.removeAttribute("aria-labelledby"),i.removeAttribute("aria-modal"),Et(i,b),kt(i,{zIndex:e.zIndexInline}),this.viewerData=yt({},this.parentData),this.renderViewer(),this.renderList(),this.viewed&&this.initImage((function(){t.renderImage((function(){e.transition&&setTimeout((function(){Tt(n,C),Tt(o,C)}),0)}))})),this):this},tooltip:function(){var t=this,e=this.options,i=this.tooltipBox,n=this.imageData;return this.viewed&&!this.played&&e.tooltip?(i.textContent="".concat(Math.round(100*n.ratio),"%"),this.tooltipping?clearTimeout(this.tooltipping):e.transition?(this.fading&&Mt(i,H),Tt(i,O),Tt(i,p),Tt(i,C),i.removeAttribute("aria-hidden"),i.initialOffsetWidth=i.offsetWidth,Tt(i,E)):(Tt(i,O),i.removeAttribute("aria-hidden")),this.tooltipping=setTimeout((function(){e.transition?(Rt(i,H,(function(){Et(i,O),Et(i,p),Et(i,C),i.setAttribute("aria-hidden",!0),t.fading=!1}),{once:!0}),Et(i,E),t.fading=!0):(Et(i,O),i.setAttribute("aria-hidden",!0)),t.tooltipping=!1}),1e3),this):this},toggle:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return 1===this.imageData.ratio?this.zoomTo(this.imageData.oldRatio,!0,null,t):this.zoomTo(1,!0,null,t),this},reset:function(){return this.viewed&&!this.played&&(this.imageData=yt({},this.initialImageData),this.renderImage()),this},update:function(){var t=this,e=this.element,i=this.options,n=this.isImg;if(n&&!e.parentNode)return this.destroy();var o=[];if(wt(n?[e]:e.querySelectorAll("img"),(function(e){bt(i.filter)?i.filter.call(t,e)&&o.push(e):t.getImageURL(e)&&o.push(e)})),!o.length)return this;if(this.images=o,this.length=o.length,this.ready){var s=[];if(wt(this.items,(function(t,e){var i=t.querySelector("img"),n=o[e];n&&i&&n.src===i.src&&n.alt===i.alt||s.push(e)})),kt(this.list,{width:"auto"}),this.initList(),this.isShown)if(this.length){if(this.viewed){var a=s.indexOf(this.index);if(a>=0)this.viewed=!1,this.view(Math.max(Math.min(this.index-a,this.length-1),0));else{var r=this.items[this.index];Tt(r,g),r.setAttribute("aria-selected",!0)}}}else this.image=null,this.viewed=!1,this.index=0,this.imageData={},this.canvas.innerHTML="",this.title.innerHTML=""}else this.build();return this},destroy:function(){var t=this.element,e=this.options;return t[u]?(this.destroyed=!0,this.ready?(this.played&&this.stop(),e.inline?(this.fulled&&this.exit(),this.unbind()):this.isShown?(this.viewing&&(this.imageRendering?this.imageRendering.abort():this.imageInitializing&&this.imageInitializing.abort()),this.hiding&&this.transitioning.abort(),this.hidden()):this.showing&&(this.transitioning.abort(),this.hidden()),this.ready=!1,this.viewer.parentNode.removeChild(this.viewer)):e.inline&&(this.delaying?this.delaying.abort():this.initializing&&this.initializing.abort()),e.inline||Lt(t,L,this.onStart),t[u]=void 0,this):this}},Bt={getImageURL:function(t){var e=this.options.url;return e=ut(e)?t.getAttribute(e):bt(e)?e.call(this,t):""},enforceFocus:function(){var t=this;this.clearEnforceFocus(),Rt(document,F,this.onFocusin=function(e){var i=t.viewer,n=e.target;if(n!==document&&n!==i&&!i.contains(n)){for(;n;){if(null!==n.getAttribute("tabindex")||"true"===n.getAttribute("aria-modal"))return;n=n.parentElement}i.focus()}})},clearEnforceFocus:function(){this.onFocusin&&(Lt(document,F,this.onFocusin),this.onFocusin=null)},open:function(){var t=this.body;Tt(t,A),this.scrollbarWidth>0&&(t.style.paddingRight="".concat(this.scrollbarWidth+(parseFloat(this.initialBodyComputedPaddingRight)||0),"px"))},close:function(){var t=this.body;Et(t,A),this.scrollbarWidth>0&&(t.style.paddingRight=this.initialBodyPaddingRight)},shown:function(){var t=this.element,e=this.options,i=this.viewer;this.fulled=!0,this.isShown=!0,this.render(),this.bind(),this.showing=!1,e.focus&&(i.focus(),this.enforceFocus()),bt(e.shown)&&Rt(t,K,e.shown,{once:!0}),!1!==Mt(t,K)&&this.ready&&this.isShown&&!this.hiding&&this.view(this.index)},hidden:function(){var t=this.element,e=this.options,i=this.viewer;e.fucus&&this.clearEnforceFocus(),this.close(),this.unbind(),Tt(i,x),i.removeAttribute("role"),i.removeAttribute("aria-labelledby"),i.removeAttribute("aria-modal"),i.setAttribute("aria-hidden",!0),this.resetList(),this.resetImage(),this.fulled=!1,this.viewed=!1,this.isShown=!1,this.hiding=!1,this.destroyed||(bt(e.hidden)&&Rt(t,$,e.hidden,{once:!0}),Mt(t,$,null,{cancelable:!1}))},requestFullscreen:function(t){var e=this.element.ownerDocument;if(this.fulled&&!(e.fullscreenElement||e.webkitFullscreenElement||e.mozFullScreenElement||e.msFullscreenElement)){var i=e.documentElement;i.requestFullscreen?pt(t)?i.requestFullscreen(t):i.requestFullscreen():i.webkitRequestFullscreen?i.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):i.mozRequestFullScreen?i.mozRequestFullScreen():i.msRequestFullscreen&&i.msRequestFullscreen()}},exitFullscreen:function(){var t=this.element.ownerDocument;this.fulled&&(t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement)&&(t.exitFullscreen?t.exitFullscreen():t.webkitExitFullscreen?t.webkitExitFullscreen():t.mozCancelFullScreen?t.mozCancelFullScreen():t.msExitFullscreen&&t.msExitFullscreen())},change:function(t){var e=this.options,i=this.pointers,o=i[Object.keys(i)[0]];if(o){var s=o.endX-o.startX,a=o.endY-o.startY;switch(this.action){case d:0===s&&0===a||(this.pointerMoved=!0,this.move(s,a,t));break;case f:this.zoom(function(t){var e=n({},t),i=[];return wt(t,(function(t,n){delete e[n],wt(e,(function(e){var n=Math.abs(t.startX-e.startX),o=Math.abs(t.startY-e.startY),s=Math.abs(t.endX-e.endX),a=Math.abs(t.endY-e.endY),r=Math.sqrt(n*n+o*o),l=(Math.sqrt(s*s+a*a)-r)/r;i.push(l)}))})),i.sort((function(t,e){return Math.abs(t)<Math.abs(e)})),i[0]}(i),!1,null,t);break;case m:this.action="switched";var r=Math.abs(s);r>1&&r>Math.abs(a)&&(this.pointers={},s>1?this.prev(e.loop):s<-1&&this.next(e.loop))}wt(i,(function(t){t.startX=t.endX,t.startY=t.endY}))}},isSwitchable:function(){var t=this.imageData,e=this.viewerData;return this.length>1&&t.x>=0&&t.y>=0&&t.width<=e.width&&t.height<=e.height}},Vt=l.Viewer,Ut=function(t){return function(){return t+=1}}(-1),Kt=function(){function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),!t||1!==t.nodeType)throw new Error("The first argument is required and must be an element.");this.element=t,this.options=yt({},a,pt(i)&&i),this.action=!1,this.fading=!1,this.fulled=!1,this.hiding=!1,this.imageClicked=!1,this.imageData={},this.index=this.options.initialViewIndex,this.isImg=!1,this.isShown=!1,this.length=0,this.moving=!1,this.played=!1,this.playing=!1,this.pointers={},this.ready=!1,this.rotating=!1,this.scaling=!1,this.showing=!1,this.timeout=!1,this.tooltipping=!1,this.viewed=!1,this.viewing=!1,this.wheeling=!1,this.zooming=!1,this.pointerMoved=!1,this.id=Ut(),this.init()}return i=e,o=[{key:"noConflict",value:function(){return window.Viewer=Vt,e}},{key:"setDefaults",value:function(t){yt(a,pt(t)&&t)}}],(n=[{key:"init",value:function(){var t=this,e=this.element,i=this.options;if(!e[u]){e[u]=this,i.focus&&!i.keyboard&&(i.focus=!1);var n="img"===e.localName,o=[];if(wt(n?[e]:e.querySelectorAll("img"),(function(e){bt(i.filter)?i.filter.call(t,e)&&o.push(e):t.getImageURL(e)&&o.push(e)})),this.isImg=n,this.length=o.length,this.images=o,this.initBody(),ft(document.createElement(u).style.transition)&&(i.transition=!1),i.inline){var s=0,a=function(){var e;(s+=1)===t.length&&(t.initializing=!1,t.delaying={abort:function(){clearTimeout(e)}},e=setTimeout((function(){t.delaying=!1,t.build()}),0))};this.initializing={abort:function(){wt(o,(function(t){t.complete||(Lt(t,Y,a),Lt(t,X,a))}))}},wt(o,(function(t){var e,i;t.complete?a():(Rt(t,Y,e=function(){Lt(t,X,i),a()},{once:!0}),Rt(t,X,i=function(){Lt(t,Y,e),a()},{once:!0}))}))}else Rt(e,L,this.onStart=function(e){var n=e.target;"img"!==n.localName||bt(i.filter)&&!i.filter.call(t,n)||t.view(t.images.indexOf(n))})}}},{key:"build",value:function(){if(!this.ready){var t=this.element,e=this.options,i=t.parentNode,n=document.createElement("div");n.innerHTML='<div class="viewer-container" tabindex="-1" touch-action="none"><div class="viewer-canvas"></div><div class="viewer-footer"><div class="viewer-title"></div><div class="viewer-toolbar"></div><div class="viewer-navbar"><ul class="viewer-list" role="navigation"></ul></div></div><div class="viewer-tooltip" role="alert" aria-hidden="true"></div><div class="viewer-button" data-viewer-action="mix" role="button"></div><div class="viewer-player"></div></div>';var o=n.querySelector(".".concat(u,"-container")),s=o.querySelector(".".concat(u,"-title")),a=o.querySelector(".".concat(u,"-toolbar")),r=o.querySelector(".".concat(u,"-navbar")),l=o.querySelector(".".concat(u,"-button")),h=o.querySelector(".".concat(u,"-canvas"));if(this.parent=i,this.viewer=o,this.title=s,this.toolbar=a,this.navbar=r,this.button=l,this.canvas=h,this.footer=o.querySelector(".".concat(u,"-footer")),this.tooltipBox=o.querySelector(".".concat(u,"-tooltip")),this.player=o.querySelector(".".concat(u,"-player")),this.list=o.querySelector(".".concat(u,"-list")),o.id="".concat(u).concat(this.id),s.id="".concat(u,"Title").concat(this.id),Tt(s,e.title?Xt(Array.isArray(e.title)?e.title[0]:e.title):x),Tt(r,e.navbar?Xt(e.navbar):x),Dt(l,x,!e.button),e.keyboard&&l.setAttribute("tabindex",0),e.backdrop&&(Tt(o,"".concat(u,"-backdrop")),e.inline||"static"===e.backdrop||Ot(h,lt,"hide")),ut(e.className)&&e.className&&e.className.split(ht).forEach((function(t){Tt(o,t)})),e.toolbar){var c=document.createElement("ul"),d=pt(e.toolbar),m=ct.slice(0,3),f=ct.slice(7,9),g=ct.slice(9);d||Tt(a,Xt(e.toolbar)),wt(d?e.toolbar:ct,(function(t,i){var n=d&&pt(t),o=d?It(i):t,s=n&&!ft(t.show)?t.show:t;if(s&&(e.zoomable||-1===m.indexOf(o))&&(e.rotatable||-1===f.indexOf(o))&&(e.scalable||-1===g.indexOf(o))){var a=n&&!ft(t.size)?t.size:t,r=n&&!ft(t.click)?t.click:t,l=document.createElement("li");e.keyboard&&l.setAttribute("tabindex",0),l.setAttribute("role","button"),Tt(l,"".concat(u,"-").concat(o)),bt(r)||Ot(l,lt,o),mt(s)&&Tt(l,Xt(s)),-1!==["small","large"].indexOf(a)?Tt(l,"".concat(u,"-").concat(a)):"play"===o&&Tt(l,"".concat(u,"-large")),bt(r)&&Rt(l,L,r),c.appendChild(l)}})),a.appendChild(c)}else Tt(a,x);if(!e.rotatable){var y=a.querySelectorAll('li[class*="rotate"]');Tt(y,D),wt(y,(function(t){a.appendChild(t)}))}if(e.inline)Tt(l,w),kt(o,{zIndex:e.zIndexInline}),"static"===window.getComputedStyle(i).position&&kt(i,{position:"relative"}),i.insertBefore(o,t.nextSibling);else{Tt(l,v),Tt(o,b),Tt(o,p),Tt(o,x),kt(o,{zIndex:e.zIndex});var k=e.container;ut(k)&&(k=t.ownerDocument.querySelector(k)),k||(k=this.body),k.appendChild(o)}e.inline&&(this.render(),this.bind(),this.isShown=!0),this.ready=!0,bt(e.ready)&&Rt(t,V,e.ready,{once:!0}),!1!==Mt(t,V)?this.ready&&e.inline&&this.view(this.index):this.ready=!1}}}])&&t(i.prototype,n),o&&t(i,o),Object.defineProperty(i,"prototype",{writable:!1}),i;var i,n,o}();yt(Kt.prototype,Pt,Wt,jt,Ht,Bt);export{Kt as V};
