import{d as e,r as t,p as r,u as n,w as o,f as a,m as i,M as c,i as l,j as u,b as s,o as f,e as d,a4 as p,E as v,c as h,ab as m,ad as g,aa as y,F as b,ag as x,x as w,am as S,ae as k,a9 as C,I as O,n as E,q as A}from"./@vue-HScy-mz9.js";import{y as N}from"./@popperjs-DxP-MrnL.js";function I(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function F(e,t){if(e){if("string"==typeof e)return I(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?I(e,t):void 0}}function _(e){return(_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function j(e,t,r,n,o,a,i){try{var c=e[a](i),l=c.value}catch(u){return void r(u)}c.done?t(l):Promise.resolve(l).then(n,o)}function L(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){j(a,n,o,i,c,"next",e)}function c(e){j(a,n,o,i,c,"throw",e)}i(void 0)}))}}var P={exports:{}},M={exports:{}};!function(e){function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(M),function(e){var t=M.exports.default;function r(){e.exports=r=function(){return o},e.exports.__esModule=!0,e.exports.default=e.exports;var n,o={},a=Object.prototype,i=a.hasOwnProperty,c=Object.defineProperty||function(e,t,r){e[t]=r.value},l="function"==typeof Symbol?Symbol:{},u=l.iterator||"@@iterator",s=l.asyncIterator||"@@asyncIterator",f=l.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(M){d=function(e,t,r){return e[t]=r}}function p(e,t,r,n){var o=t&&t.prototype instanceof x?t:x,a=Object.create(o.prototype),i=new L(n||[]);return c(a,"_invoke",{value:I(e,r,i)}),a}function v(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(n){return{type:"throw",arg:n}}}o.wrap=p;var h="suspendedStart",m="suspendedYield",g="executing",y="completed",b={};function x(){}function w(){}function S(){}var k={};d(k,u,(function(){return this}));var C=Object.getPrototypeOf,O=C&&C(C(P([])));O&&O!==a&&i.call(O,u)&&(k=O);var E=S.prototype=x.prototype=Object.create(k);function A(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,r){function n(o,a,c,l){var u=v(e[o],e,a);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==t(f)&&i.call(f,"__await")?r.resolve(f.__await).then((function(e){n("next",e,c,l)}),(function(e){n("throw",e,c,l)})):r.resolve(f).then((function(e){s.value=e,c(s)}),(function(e){return n("throw",e,c,l)}))}l(u.arg)}var o;c(this,"_invoke",{value:function(e,t){function a(){return new r((function(r,o){n(e,t,r,o)}))}return o=o?o.then(a,a):a()}})}function I(e,t,r){var o=h;return function(a,i){if(o===g)throw Error("Generator is already running");if(o===y){if("throw"===a)throw i;return{value:n,done:!0}}for(r.method=a,r.arg=i;;){var c=r.delegate;if(c){var l=F(c,r);if(l){if(l===b)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===h)throw o=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=g;var u=v(e,t,r);if("normal"===u.type){if(o=r.done?y:m,u.arg===b)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=y,r.method="throw",r.arg=u.arg)}}}function F(e,t){var r=t.method,o=e.iterator[r];if(o===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=n,F(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),b;var a=v(o,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,b;var i=a.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,b):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,b)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function j(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[u];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function t(){for(;++o<e.length;)if(i.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=n,t.done=!0,t};return a.next=a}}throw new TypeError(t(e)+" is not iterable")}return w.prototype=S,c(E,"constructor",{value:S,configurable:!0}),c(S,"constructor",{value:w,configurable:!0}),w.displayName=d(S,f,"GeneratorFunction"),o.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},o.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,S):(e.__proto__=S,d(e,f,"GeneratorFunction")),e.prototype=Object.create(E),e},o.awrap=function(e){return{__await:e}},A(N.prototype),d(N.prototype,s,(function(){return this})),o.AsyncIterator=N,o.async=function(e,t,r,n,a){void 0===a&&(a=Promise);var i=new N(p(e,t,r,n),a);return o.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},A(E),d(E,f,"Generator"),d(E,u,(function(){return this})),d(E,"toString",(function(){return"[object Generator]"})),o.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},o.values=P,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(j),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,o){return c.type="throw",c.arg=e,t.next=r,o&&(t.method="next",t.arg=n),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var l=i.call(a,"catchLoc"),u=i.call(a,"finallyLoc");if(l&&u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),j(r),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:P(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=n),b}},o}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(P);var B=P.exports(),R=B;try{regeneratorRuntime=B}catch(Be){"object"==typeof globalThis?globalThis.regeneratorRuntime=B:Function("r","regeneratorRuntime = r")(B)}function z(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,c=[],l=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(s){u=!0,o=s}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return c}}(e,t)||F(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=_(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=_(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==_(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var D=e({name:"Saturation",props:{size:{type:Number,default:160},hue:{type:Number,default:0},saturation:{type:Number,default:0},value:{type:Number,default:0}},emits:["change"],setup:function(e,t){var n=t.emit;return{saturationStyle:r((function(){return{width:"".concat(e.size,"px"),height:"".concat(e.size,"px"),background:"hsl(".concat(e.hue,", 100%, 50%)")}})),sliderStyle:r((function(){return{top:"".concat((100-e.value)/100*e.size-5,"px"),left:"".concat(e.saturation*e.size/100-5,"px"),width:"".concat(10,"px"),height:"".concat(10,"px")}})),onSelect:function(t){var r=t.target.getBoundingClientRect(),o=r.left,a=r.top,i=function(t){var r,i;t instanceof MouseEvent?(r=t.clientX,i=t.clientY):t instanceof TouchEvent&&(r=t.touches[0].clientX,i=t.touches[0].clientY);var c=r-o,l=i-a;c<0&&(c=0),l<0&&(l=0),c>e.size&&(c=e.size),l>e.size&&(l=e.size);var u=c/e.size*100,s=100-l/e.size*100;n("change",u,s)};i(t);var c=function(){document.removeEventListener("mousemove",i),document.removeEventListener("mouseup",c),document.removeEventListener("touchmove",i),document.removeEventListener("touchend",c)};i(t),t instanceof MouseEvent&&(document.addEventListener("mousemove",i),document.addEventListener("mouseup",c)),t instanceof TouchEvent&&(t.preventDefault(),document.addEventListener("touchmove",i,{passive:!1}),document.addEventListener("touchend",c))}}}});function H(e,t){void 0===t&&(t={});var r=t.insertAt;if(e&&"undefined"!=typeof document){var n=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css","top"===r&&n.firstChild?n.insertBefore(o,n.firstChild):n.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}}H(".saturation[data-v-24517fec]{position:relative}.saturation-black[data-v-24517fec],.saturation-white[data-v-24517fec]{cursor:pointer;height:100%;left:0;position:absolute;top:0;width:100%}.saturation-white[data-v-24517fec]{background:linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.saturation-black[data-v-24517fec]{background:linear-gradient(0deg,#000,transparent)}.slider[data-v-24517fec]{border:1px solid #fff;border-radius:50%;box-shadow:0 0 1px 1px rgba(0,0,0,.3);left:0;pointer-events:none;position:absolute;top:0;z-index:1}"),D.render=function(e,t,r,n,o,a){return f(),s("div",{class:"saturation",style:p(e.saturationStyle),onMousedown:t[0]||(t[0]=v((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),["prevent","stop"])),onTouchstart:t[1]||(t[1]=v((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),["prevent","stop"]))},[t[2]||(t[2]=d("div",{class:"saturation-white"},null,-1)),t[3]||(t[3]=d("div",{class:"saturation-black"},null,-1)),d("div",{class:"slider",style:p(e.sliderStyle)},null,4)],36)},D.__scopeId="data-v-24517fec",D.__file="src/picker/Saturation.vue";var G=e({name:"Hue",props:{width:{type:Number,default:15},height:{type:Number,default:160},hue:{type:Number,default:0}},emits:["change"],setup:function(e,n){var o=n.emit,i=r((function(){return{top:"".concat((1-e.hue/360)*e.height-2,"px"),height:"".concat(4,"px")}})),c=t();return a((function(){!function(){if(c.value){c.value.width=e.width,c.value.height=e.height;var t=c.value.getContext("2d");if(t){var r=t.createLinearGradient(0,0,0,e.height);r.addColorStop(0,"#FF0000"),r.addColorStop(.17,"#FF00FF"),r.addColorStop(.34,"#0000FF"),r.addColorStop(.51,"#00FFFF"),r.addColorStop(.68,"#00FF00"),r.addColorStop(.17*5,"#FFFF00"),r.addColorStop(1,"#FF0000"),t.fillStyle=r,t.fillRect(0,0,e.width,e.height)}}}()})),{canvas:c,sliderStyle:i,onSelect:function(t){var r=t.target.getBoundingClientRect().top,n=function(t){var n;t instanceof MouseEvent?n=t.clientY:t instanceof TouchEvent&&(n=t.touches[0].clientY);var a=n-r;a<0&&(a=0),a>e.height&&(a=e.height);var i=-100*a/e.height+100;o("change",360*i/100)},a=function(){document.removeEventListener("mousemove",n),document.removeEventListener("mouseup",a),document.removeEventListener("touchmove",n),document.removeEventListener("touchend",a)};n(t),t instanceof MouseEvent&&(document.addEventListener("mousemove",n),document.addEventListener("mouseup",a)),t instanceof TouchEvent&&(t.preventDefault(),document.addEventListener("touchmove",n,{passive:!1}),document.addEventListener("touchend",a))}}}}),V={ref:"canvas"};H(".hue[data-v-78b9f4f0]{position:relative;touch-action:none}.slider[data-v-78b9f4f0]{background:#fff;box-shadow:0 0 1px 0 rgba(0,0,0,.3);left:0;pointer-events:none;position:absolute;width:100%;z-index:1}"),G.render=function(e,t,r,n,o,a){return f(),s("div",{class:"hue",onMousedown:t[0]||(t[0]=v((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),["prevent","stop"])),onTouchstart:t[1]||(t[1]=v((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),["prevent","stop"]))},[d("canvas",V,null,512),d("div",{class:"slider",style:p(e.sliderStyle)},null,4)],32)},G.__scopeId="data-v-78b9f4f0",G.__file="src/picker/Hue.vue";var Y=e({name:"Alpha",props:{width:{type:Number,default:15},height:{type:Number,default:160},color:{type:String,default:"#000000"},alpha:{type:Number,default:1}},setup:function(e,n){var i=n.emit,c=r((function(){return{top:"".concat(e.alpha*e.height-2,"px"),height:"".concat(4,"px")}})),l=t(),u=function(){var t=l.value.getContext("2d");l.value.width=e.width,l.value.height=e.height;var r,n,o=(r=document.createElement("canvas"),n=r.getContext("2d"),r.width=10,r.height=10,n.fillStyle="#ffffff",n.fillRect(0,0,10,10),n.fillStyle="#ccd5db",n.fillRect(0,0,5,5),n.fillRect(5,5,5,5),r);t.fillStyle=t.createPattern(o,"repeat"),t.fillRect(0,0,e.width,e.height);var a=t.createLinearGradient(0,0,0,e.height);a.addColorStop(.01,"rgba(255,255,255,0)"),a.addColorStop(.99,e.color),t.fillStyle=a,t.fillRect(0,0,e.width,e.height)};return o((function(){return e.color}),(function(){u()})),a((function(){u()})),{canvas:l,sliderStyle:c,onSelect:function(t){var r=t.target.getBoundingClientRect().top,n=function(t){var n;t instanceof MouseEvent?n=t.clientY:t instanceof TouchEvent&&(n=t.touches[0].clientY);var o=n-r;o<0&&(o=0),o>e.height&&(o=e.height);var a=parseFloat((o/e.height).toFixed(2));i("change",a)},o=function(){document.removeEventListener("mousemove",n),document.removeEventListener("mouseup",o),document.removeEventListener("touchmove",n),document.removeEventListener("touchend",o)};n(t),t instanceof MouseEvent&&(document.addEventListener("mousemove",n),document.addEventListener("mouseup",o)),t instanceof TouchEvent&&(t.preventDefault(),document.addEventListener("touchmove",n,{passive:!1}),document.addEventListener("touchend",o))}}}}),$={ref:"canvas"};H(".alpha[data-v-24dc9656]{position:relative;touch-action:none}.slider[data-v-24dc9656]{background:#fff;box-shadow:0 0 1px 0 rgba(0,0,0,.3);left:0;pointer-events:none;position:absolute;width:100%;z-index:1}"),Y.render=function(e,t,r,n,o,a){return f(),s("div",{class:"alpha",onMousedown:t[0]||(t[0]=v((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),["prevent","stop"])),onTouchstart:t[1]||(t[1]=v((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),["prevent","stop"]))},[d("canvas",$,null,512),d("div",{class:"slider",style:p(e.sliderStyle)},null,4)],32)},Y.__scopeId="data-v-24dc9656",Y.__file="src/picker/Alpha.vue";var X={rgb:"RGBA",hex:"HEX",hsl:"HSLA",hsv:"HSVA"},q={rgb:"RGB",hex:"HEX",hsl:"HSL",hsv:"HSV"},J={RGB:"rgb",RGBA:"rgb",HEX:"hex",HSL:"hsl",HSLA:"hsl",HSV:"hsv",HSVA:"hsv"};function U(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function W(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?U(Object(r),!0).forEach((function(t){T(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var K=null,Q=function(e,r,a){var i=t({}),c=a||{},l=c.placement,s=c.defaultStyle,f={strategy:c.strategy||"absolute",placement:l||"auto",onFirstUpdate:function(){K.update()},modifiers:[{name:"offset",options:{offset:[0,5]}},{name:"computeStyles",options:{gpuAcceleration:!1,adaptive:!0}},{name:"flip",options:{allowedAutoPlacements:["top","bottom"]}},{name:"applyStyles",enabled:!1},{name:"updateState",enabled:!0,phase:"write",requires:["computeStyles"],fn:function(e){var t=e.state,r=t.styles,n=t.placement,o=r.popper;i.value=W(W(W({},o),s),{},{transformOrigin:"top"===n?"center bottom":"center top"})}}]};return o((function(){return[n(e),n(r)]}),(function(e,t){var r,n=z(e,2),o=n[0],a=n[1],i=z(t,2),c=i[0],l=i[1];if(o&&a&&(c!==o||l!==c)){null===(r=K)||void 0===r||r.destroy();var u=o.$el||o,s=a.$el||a;E((function(){K=N(u,s,f)}))}})),u((function(){var e;K&&(null===(e=K)||void 0===e||e.destroy(),K=null)})),{instance:K,style:i}},Z=e({props:{value:{type:String,default:"RGBA"},showAlpha:{type:Boolean},options:{type:[Boolean,Array]}},emits:["change"],setup:function(e,o){var c=o.emit,l=t(null),u=t(null),s=t(!1),f=Q(l,u,{strategy:"fixed",defaultStyle:{zIndex:2}}).style,d=r((function(){return Array.isArray(e.options)&&e.options.length>1})),p=r((function(){var t=e.options,r=e.showAlpha,n=e.value;return Array.isArray(t)?r?t.map((function(e){return X[e]})).filter((function(e){return!e.includes(n)})):t.map((function(e){return q[e]})).filter((function(e){return!e.includes(n)})):[]})),v=function(e){var t,r,o=e.target;(null===(t=n(l))||void 0===t||!t.isEqualNode(o))&&(null===(r=n(l))||void 0===r?void 0:r.contains(o))||(s.value=!1)};return a((function(){document.addEventListener("mouseup",v,!1)})),i((function(){document.removeEventListener("mouseup",v,!1)})),{targetRef:l,selectorRef:u,selectorStyle:f,isShowSelector:s,isNeedSelect:d,formatOptions:p,onShow:function(){d.value&&(s.value=!0)},onFormatChange:function(e){c("change",J[e])}}}}),ee={class:"format"},te={key:0,class:"arrow"},re=["onClick"];H(".format[data-v-5f6e8f5e]{position:relative}.label[data-v-5f6e8f5e]{align-items:center;background:#e7e8e9;color:#999;display:flex;float:left;font-weight:500;height:30px;justify-content:center;position:relative;width:60px}[pick-colors-theme=dark] .label[data-v-5f6e8f5e]{background:#252930;color:#999}.arrow[data-v-5f6e8f5e]{height:6px;margin-bottom:4px;margin-left:5px;transform:rotate(135deg);width:6px}.arrow[data-v-5f6e8f5e],[pick-colors-theme=dark] .arrow[data-v-5f6e8f5e]{border-right:1px solid #999;border-top:1px solid #999}.selector[data-v-5f6e8f5e]{align-items:center;background:#f7f8f9;border-radius:5px;box-shadow:0 0 16px 0 rgba(0,0,0,.16);cursor:pointer;display:flex;flex-direction:column;font-weight:400;justify-content:center;padding:4px}[pick-colors-theme=dark] .selector[data-v-5f6e8f5e]{background:#252930;color:#999}.selector-item[data-v-5f6e8f5e]{align-items:center;display:flex;height:30px;justify-content:center;width:60px}.selector-item[data-v-5f6e8f5e]:hover{background:#e1f2fe}[pick-colors-theme=dark] .selector-item[data-v-5f6e8f5e]{color:#fff}[pick-colors-theme=dark] .selector-item[data-v-5f6e8f5e]:hover{background:#0087fa}.active-selector-item[data-v-5f6e8f5e]{background:#e1f2fe}[pick-colors-theme=dark] .active-selector-item[data-v-5f6e8f5e]{background:#0087fa}.v-enter-active[data-v-5f6e8f5e],.v-leave-active[data-v-5f6e8f5e]{opacity:1;transform:scaleY(1);transform-origin:center top;transition:opacity .2s ease-in-out,transform .2s ease-in-out}.v-enter-from[data-v-5f6e8f5e],.v-leave-to[data-v-5f6e8f5e]{opacity:0;transform:scaleY(0)}"),Z.render=function(e,t,r,n,o,a){return f(),s("div",ee,[d("div",{class:"label",ref:"targetRef",onClick:t[0]||(t[0]=function(){return e.onShow&&e.onShow.apply(e,arguments)})},[d("span",null,g(e.value),1),e.isNeedSelect?(f(),s("div",te)):m("v-if",!0)],512),h(w,null,{default:y((function(){return[e.isShowSelector?(f(),s("div",{key:0,class:"selector",ref:"selectorRef",style:p(e.selectorStyle)},[(f(!0),s(b,null,x(e.formatOptions,(function(t){return f(),s("div",{class:"selector-item",key:t,onClick:function(r){return e.onFormatChange(t)}},g(t),9,re)})),128))],4)):m("v-if",!0)]})),_:1})])},Z.__scopeId="data-v-5f6e8f5e",Z.__file="src/picker/input-value/FormatValue.vue";var ne=e({name:"Input",components:{FormatValue:Z},props:{format:{type:String,default:"RGBA"},value:{type:String,default:""},width:{type:Number},showAlpha:{type:Boolean},formatOptions:{type:[Boolean,Array]}},emits:["change","focus","blur","enter","formatChange"],setup:function(e,t){var n=t.emit;return{onInput:function(e){var t;n("change",null===(t=e.target)||void 0===t?void 0:t.value)},valueStyle:r((function(){return{minWidth:"".concat(e.width,"px"),maxWidth:"".concat(e.width,"px"),width:"".concat(e.width,"px")}})),onFocus:function(){n("focus")},onBlur:function(){n("blur")},onEnter:function(){n("enter")},onFormatChange:function(e){n("formatChange",e)}}}}),oe={class:"input"},ae=[".value"];function ie(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ce(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ie(Object(r),!0).forEach((function(t){T(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ie(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}H(".input[data-v-2c454c00]{display:flex;font-size:12px}.value[data-v-2c454c00]{background:#eceef0;border:1px solid #eceef0;box-sizing:border-box;color:#666;flex:1;height:30px;text-align:center}[pick-colors-theme=dark] .value[data-v-2c454c00]{background:#2e333a;border:1px solid #2e333a;color:#fff}.value[data-v-2c454c00]:focus{border:1px solid #1890ff;outline:none}"),ne.render=function(e,t,r,n,o,a){var i=S("FormatValue");return f(),s("div",oe,[h(i,{value:e.format,showAlpha:e.showAlpha,options:e.formatOptions,onChange:e.onFormatChange},null,8,["value","showAlpha","options","onChange"]),d("input",{class:"value",style:p(e.valueStyle),".value":e.value,onFocus:t[0]||(t[0]=function(){return e.onFocus&&e.onFocus.apply(e,arguments)}),onInput:t[1]||(t[1]=function(){return e.onInput&&e.onInput.apply(e,arguments)}),onBlur:t[2]||(t[2]=function(){return e.onBlur&&e.onBlur.apply(e,arguments)}),onKeydown:t[3]||(t[3]=k((function(){return e.onEnter&&e.onEnter.apply(e,arguments)}),["enter"]))},null,44,ae)])},ne.__scopeId="data-v-2c454c00",ne.__file="src/picker/input-value/InputValue.vue";var le=function(e,t,r){return[e,t*r/((e=(2-t)*r)<1?e:2-e)||0,e/2]},ue={10:"A",11:"B",12:"C",13:"D",14:"E",15:"F"},se=function(e){e=Math.min(Math.round(e),255);var t=Math.floor(e/16),r=e%16;return"".concat(ue[t]||t).concat(ue[r]||r)},fe=function(e){var t=e.r,r=e.g,n=e.b;return isNaN(t)||isNaN(r)||isNaN(n)?"":"#".concat(se(t)).concat(se(r)).concat(se(n))},de=function(e,t){var r;"string"==typeof(r=e)&&-1!==r.indexOf(".")&&1===parseFloat(r)&&(e="100%");var n,o="string"==typeof(n=e)&&-1!==n.indexOf("%");return e=Math.min(t,Math.max(0,parseFloat("".concat(e)))),o&&(e=parseInt("".concat(e*t),10)/100),Math.abs(e-t)<1e-6?1:e%t/parseFloat(t)},pe=function(e,t,r){e=6*de(e,360),t=de(t,100),r=de(r,100);var n=Math.floor(e),o=e-n,a=r*(1-t),i=r*(1-o*t),c=r*(1-(1-o)*t),l=n%6,u=[r,i,a,a,c,r][l],s=[c,r,r,i,a,a][l],f=[a,a,c,r,r,i][l];return{r:Math.round(255*u),g:Math.round(255*s),b:Math.round(255*f)}},ve=function(e,t,r){var n,o,a=e.h,i=e.s,c=e.v,l=e.a;if(r)switch(["hsl","hsv","rga"].includes(t)&&(l=(o=(n=l).toString().match(/\.(\d{1,2})(\d*)/))&&o[2].length>0?parseFloat(n.toFixed(2)):n),t){case"hsl":var u=le(a,i/100,c/100);return"hsla(".concat(a.toFixed(0),", ").concat(Math.round(100*u[1]),"%, ").concat(Math.round(100*u[2]),"%, ").concat(l,")");case"hsv":return"hsva(".concat(a.toFixed(0),", ").concat(Math.round(i),"%, ").concat(Math.round(c),"%, ").concat(l,")");case"rgb":var s=pe(a,i,c),f=s.r,d=s.g,p=s.b;return"rgba(".concat(f,", ").concat(d,", ").concat(p,", ").concat(l,")");default:return"".concat(fe(pe(a,i,c))).concat(se(255*l))}else switch(t){case"hsl":var v=le(a,i/100,c/100);return"hsl(".concat(a.toFixed(0),", ").concat(Math.round(100*v[1]),"%, ").concat(Math.round(100*v[2]),"%)");case"hsv":return"hsv(".concat(a.toFixed(0),", ").concat(Math.round(i),"%, ").concat(Math.round(c),"%)");case"rgb":var h=pe(a,i,c),m=h.r,g=h.g,y=h.b;return"rgb(".concat(m,", ").concat(g,", ").concat(y,")");default:return fe(pe(a,i,c))}},he=function(e){var t=e.r,r=e.g,n=e.b;t=de(t,255),r=de(r,255),n=de(n,255);var o,a=Math.max(t,r,n),i=Math.min(t,r,n),c=a,l=a-i,u=0===a?0:l/a;if(a===i)o=0;else{switch(a){case t:o=(r-n)/l+(r<n?6:0);break;case r:o=(n-t)/l+2;break;case n:o=(t-r)/l+4}o/=6}return{h:360*o,s:100*u,v:100*c}},me=function(e){var t=e.h,r=e.s,n=e.l;n/=100;var o=r/=100,a=Math.max(n,.01);return r*=(n*=2)<=1?n:2-n,o*=a<=1?a:2-a,{h:+t,s:100*(0===n?2*o/(a+o):2*r/(n+r)),v:(n+r)/2*100}},ge=function(e){var t=[];if(e.match(/^#([0-9a-fA-f]{3,4})$/g))for(var r=1;r<e.length;r++)t.push(parseInt("0x"+e[r].repeat(2)));else if(e.match(/^#([0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/g))for(var n=1;n<e.length;n+=2)t.push(parseInt("0x"+e.slice(n,n+2)));return{r:t[0],g:t[1],b:t[2],a:t[3]}},ye=function(e,t,r){if("string"==typeof e&&""!==e){var n=Se(e,ke(e),r),o=Ce(n);return null==o?"":ve(o,t,r)}return""},be=function(e){var t=z(e.match(/(\d(\.\d+)?)+/g),4);return{r:t[0],g:t[1],b:t[2],a:t[3]}},xe=function(e){var t=z(e.match(/(\d(\.\d+)?)+/g),4),r=t[0],n=t[1],o=t[2],a=t[3];return{h:r,s:parseFloat(n),l:parseFloat(o),a:a}},we=function(e){var t=z(e.match(/(\d(\.\d+)?)+/g),4),r=t[0],n=t[1],o=t[2],a=t[3];return{h:parseFloat(r),s:parseFloat(n),v:parseFloat(o),a:parseFloat(a)}},Se=function(e,t){if(arguments.length>2&&void 0!==arguments[2]&&!arguments[2])switch(t){case"rgb":return ce(ce({},he(be(e))),{},{a:1});case"hsv":var r=we(e);return{h:r.h,s:r.s,v:r.v,a:1};case"hsl":return ce(ce({},me(xe(e))),{},{a:1});default:return ce(ce({},he(ge(e))),{},{a:1})}else switch(t){case"rgb":var n=be(e),o=n.r,a=n.g,i=n.b,c=n.a;return ce(ce({},he({r:o,g:a,b:i})),{},{a:+c});case"hsv":var l=we(e);return{h:l.h,s:l.s,v:l.v,a:l.a};case"hsl":var u=xe(e),s=u.h,f=u.s,d=u.l,p=u.a;return ce(ce({},me({h:s,s:f,l:d})),{},{a:+p});default:var v=ge(e),h=v.r,m=v.g,g=v.b,y=v.a;return ce(ce({},he({r:h,g:m,b:g})),{},{a:y/255})}},ke=function(e){return e.match(/^#/)?"hex":e.match(/^rgb/)?"rgb":e.match(/^hsl/)?"hsl":e.match(/^hsv/)?"hsv":"hex"},Ce=function(e){var t=e.h,r=e.s,n=e.v,o=e.a;return isNaN(t)&&isNaN(r)&&isNaN(n)?null:(isNaN(t)&&(t=0),isNaN(r)&&(r=0),isNaN(n)&&(n=0),isNaN(o)&&(o=1),{h:t,s:r,v:n,a:o})},Oe=e({name:"ColorItem",props:{size:{type:[Number,String],default:20},width:{type:[Number,String]},height:{type:[Number,String]},value:{type:String,default:""},border:{type:Boolean,default:!0},borderRadius:{type:Number,default:5},selected:{type:Boolean,default:!1},draggable:{type:Boolean,default:!1}},emits:["select"],setup:function(e){var i=t(),c=l("theme",{theme:"light"}).theme,u=r((function(){return parseFloat((e.width||e.size)+"")})),s=r((function(){return parseFloat((e.height||e.size)+"")})),f=r((function(){return{width:"".concat(n(u),"px"),height:"".concat(n(s),"px"),border:e.border?"1px solid ".concat("dark"===n(c)?"#434345":"#d9d9d9"):"",borderRadius:"".concat(e.borderRadius,"px"),boxShadow:e.selected?"0 0 3px 2px ".concat("dark"===n(c)?"#2681ff":"#1890ff"):""}})),d=function(){var t=i.value.getContext("2d");i.value.width=n(u),i.value.height=n(s);var r,o,a=(r=document.createElement("canvas"),o=r.getContext("2d"),r.width=10,r.height=10,o.fillStyle="#ffffff",o.fillRect(0,0,10,10),o.fillStyle="#ccd5db",o.fillRect(0,0,5,5),o.fillRect(5,5,5,5),r);t.fillStyle=t.createPattern(a,"repeat"),t.fillRect(0,0,n(u),n(s)),t.fillStyle=e.value,t.fillRect(0,0,n(u),n(s))};return o((function(){return e.value}),(function(){d()})),a((function(){d()})),{canvas:i,colorItemStyle:f}}}),Ee=["draggable"];H(".color-item[data-v-02da71fd]{display:inline-block;vertical-align:top}"),Oe.render=function(e,t,r,n,o,a){return f(),s("canvas",{class:"color-item",style:p(e.colorItemStyle),ref:"canvas",draggable:e.draggable},null,12,Ee)},Oe.__scopeId="data-v-02da71fd",Oe.__file="src/color-item/ColorItem.vue";var Ae=e({name:"Colors",components:{ColorItem:Oe},props:{colors:{type:Array,default:function(){return[]}},selectedIndex:{type:Number,default:-1}},emits:["change"],setup:function(e,t){var n=t.emit;return{onSelectColor:function(e,t){n("change",e,t)},useColors:r((function(){return e.colors.map((function(e){return ye(e,"hex",!0)}))}))}}}),Ne={class:"colors"};function Ie(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Fe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ie(Object(r),!0).forEach((function(t){T(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ie(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}H(".colors[data-v-0f8b52a8]{align-items:center;display:flex;flex-wrap:wrap;margin-top:5px}.color-item[data-v-0f8b52a8]{margin:5px}"),Ae.render=function(e,t,r,n,o,a){var i=S("color-item");return f(),s("div",Ne,[(f(!0),s(b,null,x(e.useColors,(function(t,r){return f(),C(i,{key:r,class:"color-item",size:16,value:t,border:!1,"border-radius":3,selected:e.selectedIndex===r,onClick:v((function(n){return e.onSelectColor(t,+r)}),["stop","prevent"])},null,8,["value","selected","onClick"])})),128))])},Ae.__scopeId="data-v-0f8b52a8",Ae.__file="src/picker/Colors.vue";var _e=e({name:"Picker",components:{Colors:Ae,Saturation:D,Hue:G,Alpha:Y,InputValue:ne},props:{format:{type:String,default:"hex"},showAlpha:{type:Boolean,default:!1},value:{type:String,default:""},colors:{type:Array,default:function(){return[]}},style:{type:Object,default:function(){return{}}},formatOptions:{type:[Boolean,Array]}},emits:["change","formatChange"],setup:function(e,a){var i=a.emit,c=r((function(){return Fe(Fe({},e.style),{},{width:e.showAlpha?"230px":"205px"})})),l=t(),u=t(),s=t(),f=function(){var t,r=null===(t=e.value)||void 0===t?void 0:t.trim();if(null!=r&&""!==r){var n=ke(r);return Ce(Se(r,n,e.showAlpha))}return null};o((function(){return e.value}),(function(){e.value!==n(s)&&(l.value=f())}),{immediate:!0}),o((function(){return[n(l),e.format]}),(function(t){var r=z(t,2),n=r[0],o=r[1],a="";null!=n?(a=ve(Fe({},n),e.format,e.showAlpha),u.value=Fe({},n)):u.value=null,s.value=a;var c=f();JSON.stringify(n)!==JSON.stringify(c)&&JSON.stringify(n)!==JSON.stringify(o)&&i("change",a)}),{immediate:!0});var d=r((function(){var e;return(null===(e=n(l))||void 0===e?void 0:e.h)||0})),p=r((function(){var e;return(null===(e=n(l))||void 0===e?void 0:e.s)||0})),v=r((function(){var e;return(null===(e=n(l))||void 0===e?void 0:e.v)||0})),h=r((function(){var e,t;return null!=(null===(e=n(l))||void 0===e?void 0:e.a)?null===(t=n(l))||void 0===t?void 0:t.a:1})),m=r((function(){return pe(n(d),n(p),n(v))})),g=r((function(){return"rgb(".concat(n(m).r,", ").concat(n(m).g,", ").concat(n(m).b,")")})),y=t(-1);return{h:d,s:p,v:v,a:h,rgbStr:g,onSelectSaturation:function(e,t){y.value=-1,null==n(l)?l.value={h:n(d),s:e,v:t,a:n(h)}:l.value=Fe(Fe({},n(l)),{},{s:e,v:t})},onSelectHue:function(e){y.value=-1,null==n(l)&&(l.value={h:e,s:n(p),v:n(v),a:n(h)}),l.value=Fe(Fe({},n(l)),{},{h:e})},onSelectAlpha:function(e){y.value=-1,null==n(l)?l.value={h:n(d),s:n(p),v:n(v),a:e}:l.value=Fe(Fe({},n(l)),{},{a:e})},handleInputFormat:function(t){return e.showAlpha?X[t]:q[t]},colorValue:s,pickerStyle:c,onInputChange:function(e){y.value=-1,s.value=e},handleChange:function(){var t,r=null===(t=n(s))||void 0===t?void 0:t.trim();if(""!==r){var o=e.showAlpha,a=ke(r),i=Ce(Se(r,a,o));(function(e){if(!e)return!1;var t=e.h,r=e.s,n=e.v,o=e.a;return!(isNaN(t)||isNaN(r)||isNaN(n)||isNaN(o))})(i)?l.value=i:null!=n(u)?l.value=n(u):s.value=""}else l.value=null},selectColorIndex:y,onSelectColor:function(e,t){if(y.value=t,""===(e=e.trim()))l.value=null;else{var r=ke(e);r?function(e,t){if(0!==e.length){var r=Se(e,t,!0),n=r.h,o=r.s,a=r.v;if(!(isNaN(n)||isNaN(o)||isNaN(a))){var i=1,c=r.a;isNaN(c)||(i=c),l.value={h:n,s:o,v:a,a:i}}}}(e,r):l.value=null}},onFormatChange:function(e){i("formatChange",e)}}}}),je={class:"picker-inner"},Le={class:"picker-header"};H(".picker[data-v-6ceadec6]{background:#f7f8f9;border-radius:4px;box-shadow:0 0 16px 0 rgba(0,0,0,.16)}.picker-inner[data-v-6ceadec6]{padding:10px}[pick-colors-theme=dark] .picker-inner[data-v-6ceadec6]{background:#1d2024;box-shadow:0 0 16px 0 rgba(0,0,0,.16)}.picker-header[data-v-6ceadec6]{display:flex;margin-bottom:5px}.alpha[data-v-6ceadec6],.hue[data-v-6ceadec6]{margin-left:10px}.colors[data-v-6ceadec6]{margin-top:5px}"),_e.render=function(e,t,r,n,o,a){var i=S("saturation"),c=S("hue"),l=S("alpha"),u=S("input-value"),v=S("Colors");return f(),s("div",{class:"picker",style:p(e.pickerStyle)},[d("div",je,[d("div",Le,[h(i,{class:"saturation",hue:e.h,saturation:e.s,value:e.v,onChange:e.onSelectSaturation},null,8,["hue","saturation","value","onChange"]),h(c,{class:"hue",hue:e.h,onChange:e.onSelectHue},null,8,["hue","onChange"]),e.showAlpha?(f(),C(l,{key:0,class:"alpha",alpha:e.a,color:e.rgbStr,onChange:e.onSelectAlpha},null,8,["alpha","color","onChange"])):m("v-if",!0)]),h(u,{format:e.handleInputFormat(e.format),value:e.colorValue,showAlpha:e.showAlpha,width:e.showAlpha?150:125,formatOptions:e.formatOptions,onChange:e.onInputChange,onBlur:e.handleChange,onEnter:e.handleChange,onFormatChange:e.onFormatChange},null,8,["format","value","showAlpha","width","formatOptions","onChange","onBlur","onEnter","onFormatChange"]),e.colors.length>0?(f(),C(v,{key:0,class:"colors",colors:e.colors,"selected-index":e.selectColorIndex,onChange:e.onSelectColor},null,8,["colors","selected-index","onChange"])):m("v-if",!0)])],4)},_e.__scopeId="data-v-6ceadec6",_e.__file="src/picker/Picker.vue";var Pe=e({name:"AddColorItem",props:{size:{type:Number,default:20},selected:{type:Boolean,default:!1}},setup:function(e){var t=l("theme",{theme:"light"}).theme;return{addColorItemStyle:r((function(){return{width:"".concat(e.size,"px"),height:"".concat(e.size,"px"),lineHeight:"".concat(e.size,"px"),boxShadow:e.selected?"0 0 3px 2px ".concat("dark"===n(t)?"#2681ff":"#1890ff"):""}}))}}});H(".add-color-item[data-v-ceb1719c]{background:#fff;border:1px solid #d9d9d9;border-radius:5px;vertical-align:top}.container[data-v-ceb1719c]{pointer-events:none;transform:scale(.9);transform-origin:center}[pick-colors-theme=dark] .add-color-item[data-v-ceb1719c]{background:#141414;border:1px solid #434343}path[data-v-ceb1719c]{fill:#000}[pick-colors-theme=dark] path[data-v-ceb1719c]{fill:#fff}"),Pe.render=function(e,t,r,n,o,a){return f(),s("svg",{class:"add-color-item",style:p(e.addColorItemStyle),viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},t[0]||(t[0]=[d("g",{class:"container"},[d("path",{d:"M544 464V160h-80v304H160v80h304v304h80V544h304v-80z"})],-1)]),4)},Pe.__scopeId="data-v-ceb1719c",Pe.__file="src/add-color-item/AddColorItem.vue";var Me=e({name:"ColorPicker",components:{ColorItem:Oe,Picker:_e,AddColorItem:Pe},props:{value:{type:[String,Array]},theme:{type:String,default:"light"},size:{type:[Number,String],default:20},width:{type:[Number,String]},height:{type:[Number,String]},format:{type:String},showPicker:{type:Boolean,default:void 0},showAlpha:{type:Boolean,default:!1},addColor:{type:Boolean,default:!1},deleteColor:{type:Boolean,default:!0},max:{type:Number,default:13},popupContainer:{type:[String,Object,Boolean],default:"body"},zIndex:{type:Number,default:1e3},colors:{type:Array,default:function(){return["#ff4500","#ff8c00","#ffd700","#90ee90","#00ced1","#1e90ff","#c71585","#ff4500","#ff7d4d","#00babd","#1f93ff","#fa64c3"]}},position:{type:String},placement:{type:String},formatOptions:{type:[Boolean,Array],default:!1}},emits:["change","update:value","update:showPicker","overflowMax","closePicker","formatChange"],setup:function(e,l){var u=l.emit,s=t([]),f=r((function(){return n(s).map((function(t){return ye(t,"hex",e.showAlpha)}))})),d=t("hex");o((function(){return e.format}),(function(){d.value=e.format}),{immediate:!0}),o((function(){return e.value}),(function(){var t=e.value||"",r=Array.isArray(t)?t:[t];s.value=r.map((function(t){return ye(t,n(d),e.showAlpha)}))}),{immediate:!0});var p=t(void 0),v=r((function(){return n(s)[n(p)]})),h=t(!1);o((function(){return e.showPicker}),(function(){h.value=e.showPicker}),{immediate:!0});var m,g,y,b=t(null),x=t(null),w=Q(b,x,{defaultStyle:{zIndex:e.zIndex},strategy:e.position,placement:e.placement}).style,S=function(){null==n(b)&&(b.value=n(T)[0]),null==n(p)&&(p.value=0),void 0===e.showPicker?h.value=!0:u("update:showPicker",!0)},k=function(){p.value=void 0,void 0===e.showPicker?h.value=!1:u("update:showPicker",!1),u("closePicker",c(Array.isArray(e.value)||e.addColor?n(s):n(s)[0]))},C=t(),O=(y=L(R.mark((function e(t){var r,o,a,i;return R.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=t.target,null!=(a=null===(r=o.dataset)||void 0===r?void 0:r.index)&&""!==a){e.next=4;break}return e.abrupt("return");case 4:if(i=+a,n(p)!==i){e.next=7;break}return e.abrupt("return");case 7:null!=n(p)&&n(p)!==i?(k(),m&&clearTimeout(m),m=setTimeout((function(){S(),clearTimeout(m)}),100)):S(),p.value=i,b.value=o;case 10:case"end":return e.stop()}}),e)}))),function(e){return y.apply(this,arguments)}),N=function(){var e=L(R.mark((function e(t){var r,o,a,i,c;return R.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=t.target,null!==(r=n(C))&&void 0!==r&&r.isEqualNode(i)||!(null===(o=n(C))||void 0===o?void 0:o.contains(i))){e.next=4;break}return e.abrupt("return");case 4:if(!(null==(c=null===(a=n(x))||void 0===a?void 0:a.$el)?void 0:c.contains(i))){e.next=8;break}return e.abrupt("return");case 8:g&&clearTimeout(g),g=setTimeout((function(){n(h)&&k()}),0);case 10:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();o(h,(function(){n(h)&&(S(),clearTimeout(g))}));var j,P=t(e.max>n(s).length),M=r((function(){return"string"==typeof e.popupContainer||"object"===_(e.popupContainer)&&null!=e.popupContainer?e.popupContainer:"body"})),B=r((function(){return"boolean"==typeof e.popupContainer&&!1===e.popupContainer})),z=r((function(){return e.theme}));o((function(){return[e.theme,n(x)]}),(function(){E((function(){var e,t;null===(e=n(C))||void 0===e||e.setAttribute("pick-colors-theme",n(z)),null===(t=n(x))||void 0===t||null===(t=t.$el)||void 0===t||t.setAttribute("pick-colors-theme",n(z))}))}),{immediate:!0}),A("theme",{theme:z});var T=t([]);return a((function(){document.addEventListener("mouseup",N,!1),e.showPicker&&S()})),i((function(){document.removeEventListener("mouseup",N,!1),m&&(clearTimeout(m),m=null),g&&(clearTimeout(g),g=null)})),{valueList:s,colorItemSelected:function(t){return(e.addColor?n(s).length>0:n(s).length>1)&&n(p)===t},selectedColor:v,selectedIndex:p,isShowPicker:h,addColorItemShow:P,onPickerChange:function(t){var r=n(p),o=n(s).slice(),a=n(s).length;if(null!=r){r>=0?o[r]=t:(p.value=a,o.push(t));var i;i=Array.isArray(e.value)||e.addColor?o:t,s.value=Array.isArray(i)?i:[i],u("update:value",i),u("change",i,t,r),e.addColor&&a>=e.max&&(P.value=!1,u("overflowMax"))}},colorPicker:C,onColorClick:O,pickerRef:x,onColorItemDragStart:function(e){e.dataTransfer.effectAllowed="move";var t=e.target;j=+t.dataset.index},onColorItemDragOver:function(e){},onColorItemDrop:function(e){var t=+e.target.dataset.index,r=function(e){return function(e){if(Array.isArray(e))return I(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||F(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(n(s)),o=r[j];r.splice(j,1);var a=r.slice(0,t),i=r.splice(t),c=a.concat([o]).concat(i);u("update:value",c),u("change",c,c[j],j)},colorItemsRef:T,pickerStyle:w,values:f,teleportDisabled:B,toPopupContainer:M,formatValue:d,onFormatChange:function(e){d.value=e,u("formatChange",e)}}}});H(".color-picker[data-v-3c43ade8]{display:inline-block}.color-item[data-v-3c43ade8]{margin:5px}.add-color-item[data-v-3c43ade8]{display:inline-block;margin:5px}.picker[data-v-3c43ade8]{overflow:hidden}.v-enter-active[data-v-3c43ade8],.v-leave-active[data-v-3c43ade8]{opacity:1;transform:scaleY(1);transform-origin:center top;transition:opacity .2s ease-in-out,transform .2s ease-in-out}.v-enter-from[data-v-3c43ade8],.v-leave-to[data-v-3c43ade8]{opacity:0;transform:scaleY(0)}"),Me.render=function(e,t,r,n,o,a){var i=S("color-item"),c=S("add-color-item"),l=S("picker");return f(),s("div",{class:"color-picker",ref:"colorPicker",onDragstart:t[0]||(t[0]=v((function(){return e.onColorItemDragStart&&e.onColorItemDragStart.apply(e,arguments)}),["stop"])),onDragover:t[1]||(t[1]=v((function(){return e.onColorItemDragOver&&e.onColorItemDragOver.apply(e,arguments)}),["prevent","stop"])),onDrop:t[2]||(t[2]=v((function(){return e.onColorItemDrop&&e.onColorItemDrop.apply(e,arguments)}),["prevent","stop"])),onClick:t[3]||(t[3]=v((function(){return e.onColorClick&&e.onColorClick.apply(e,arguments)}),["stop"]))},[(f(!0),s(b,null,x(e.values,(function(t,r){return f(),C(i,{class:"color-item",key:r,ref_for:!0,ref:function(t){return e.colorItemsRef[r]=t},size:e.size,width:e.width,height:e.height,value:t,selected:e.colorItemSelected(r),"data-index":r,draggable:e.valueList.length>1,format:e.formatValue},null,8,["size","width","height","value","selected","data-index","draggable","format"])})),128)),e.addColor&&e.addColorItemShow?(f(),C(c,{key:0,class:"add-color-item",ref:"addColorItem",selected:e.colorItemSelected(-1),"data-index":-1},null,8,["selected"])):m("v-if",!0),(f(),C(O,{to:e.toPopupContainer,disabled:e.teleportDisabled},[h(w,null,{default:y((function(){return[e.isShowPicker?(f(),C(l,{key:0,class:"picker",style:p(e.pickerStyle),ref:"pickerRef",value:e.selectedColor,format:e.formatValue,"show-alpha":e.showAlpha,colors:e.colors,formatOptions:e.formatOptions,onChange:e.onPickerChange,onFormatChange:e.onFormatChange},null,8,["style","value","format","show-alpha","colors","formatOptions","onChange","onFormatChange"])):m("v-if",!0)]})),_:1})],8,["to","disabled"]))],544)},Me.__scopeId="data-v-3c43ade8",Me.__file="src/ColorPicker.vue";export{Me as R};
