import{p as t,s as e,b as r}from"./js-binary-schema-parser-G48GG52R.js";import{b as o,ab as i,o as n}from"./@vue-HScy-mz9.js";const s=(t,e)=>{const r=t.__vccOpts||t;for(const[o,i]of e)r[o]=i;return r};function a(t){return""===t?t:"true"===t||"1"==t}function l(t){if("string"!=typeof t)throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}function h(t,e){for(var r,o="",i=0,n=-1,s=0,a=0;a<=t.length;++a){if(a<t.length)r=t.charCodeAt(a);else{if(47===r)break;r=47}if(47===r){if(n===a-1||1===s);else if(n!==a-1&&2===s){if(o.length<2||2!==i||46!==o.charCodeAt(o.length-1)||46!==o.charCodeAt(o.length-2))if(o.length>2){var l=o.lastIndexOf("/");if(l!==o.length-1){-1===l?(o="",i=0):i=(o=o.slice(0,l)).length-1-o.lastIndexOf("/"),n=a,s=0;continue}}else if(2===o.length||1===o.length){o="",i=0,n=a,s=0;continue}e&&(o.length>0?o+="/..":o="..",i=2)}else o.length>0?o+="/"+t.slice(n+1,a):o=t.slice(n+1,a),i=a-n-1;n=a,s=0}else 46===r&&-1!==s?++s:s=-1}return o}var c={resolve:function(){for(var t,e="",r=!1,o=arguments.length-1;o>=-1&&!r;o--){var i;o>=0?i=arguments[o]:(void 0===t&&(t=process.cwd()),i=t),l(i),0!==i.length&&(e=i+"/"+e,r=47===i.charCodeAt(0))}return e=h(e,!r),r?e.length>0?"/"+e:"/":e.length>0?e:"."},normalize:function(t){if(l(t),0===t.length)return".";var e=47===t.charCodeAt(0),r=47===t.charCodeAt(t.length-1);return 0!==(t=h(t,!e)).length||e||(t="."),t.length>0&&r&&(t+="/"),e?"/"+t:t},isAbsolute:function(t){return l(t),t.length>0&&47===t.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var t,e=0;e<arguments.length;++e){var r=arguments[e];l(r),r.length>0&&(void 0===t?t=r:t+="/"+r)}return void 0===t?".":c.normalize(t)},relative:function(t,e){if(l(t),l(e),t===e)return"";if((t=c.resolve(t))===(e=c.resolve(e)))return"";for(var r=1;r<t.length&&47===t.charCodeAt(r);++r);for(var o=t.length,i=o-r,n=1;n<e.length&&47===e.charCodeAt(n);++n);for(var s=e.length-n,a=i<s?i:s,h=-1,u=0;u<=a;++u){if(u===a){if(s>a){if(47===e.charCodeAt(n+u))return e.slice(n+u+1);if(0===u)return e.slice(n+u)}else i>a&&(47===t.charCodeAt(r+u)?h=u:0===u&&(h=0));break}var g=t.charCodeAt(r+u);if(g!==e.charCodeAt(n+u))break;47===g&&(h=u)}var f="";for(u=r+h+1;u<=o;++u)u!==o&&47!==t.charCodeAt(u)||(0===f.length?f+="..":f+="/..");return f.length>0?f+e.slice(n+h):(n+=h,47===e.charCodeAt(n)&&++n,e.slice(n))},_makeLong:function(t){return t},dirname:function(t){if(l(t),0===t.length)return".";for(var e=t.charCodeAt(0),r=47===e,o=-1,i=!0,n=t.length-1;n>=1;--n)if(47===(e=t.charCodeAt(n))){if(!i){o=n;break}}else i=!1;return-1===o?r?"/":".":r&&1===o?"//":t.slice(0,o)},basename:function(t,e){if(void 0!==e&&"string"!=typeof e)throw new TypeError('"ext" argument must be a string');l(t);var r,o=0,i=-1,n=!0;if(void 0!==e&&e.length>0&&e.length<=t.length){if(e.length===t.length&&e===t)return"";var s=e.length-1,a=-1;for(r=t.length-1;r>=0;--r){var h=t.charCodeAt(r);if(47===h){if(!n){o=r+1;break}}else-1===a&&(n=!1,a=r+1),s>=0&&(h===e.charCodeAt(s)?-1==--s&&(i=r):(s=-1,i=a))}return o===i?i=a:-1===i&&(i=t.length),t.slice(o,i)}for(r=t.length-1;r>=0;--r)if(47===t.charCodeAt(r)){if(!n){o=r+1;break}}else-1===i&&(n=!1,i=r+1);return-1===i?"":t.slice(o,i)},extname:function(t){l(t);for(var e=-1,r=0,o=-1,i=!0,n=0,s=t.length-1;s>=0;--s){var a=t.charCodeAt(s);if(47!==a)-1===o&&(i=!1,o=s+1),46===a?-1===e?e=s:1!==n&&(n=1):-1!==e&&(n=-1);else if(!i){r=s+1;break}}return-1===e||-1===o||0===n||1===n&&e===o-1&&e===r+1?"":t.slice(e,o)},format:function(t){if(null===t||"object"!=typeof t)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof t);return function(t,e){var r=e.dir||e.root,o=e.base||(e.name||"")+(e.ext||"");return r?r===e.root?r+o:r+t+o:o}("/",t)},parse:function(t){l(t);var e={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return e;var r,o=t.charCodeAt(0),i=47===o;i?(e.root="/",r=1):r=0;for(var n=-1,s=0,a=-1,h=!0,c=t.length-1,u=0;c>=r;--c)if(47!==(o=t.charCodeAt(c)))-1===a&&(h=!1,a=c+1),46===o?-1===n?n=c:1!==u&&(u=1):-1!==n&&(u=-1);else if(!h){s=c+1;break}return-1===n||-1===a||0===u||1===u&&n===a-1&&n===s+1?-1!==a&&(e.base=e.name=0===s&&i?t.slice(1,a):t.slice(s,a)):(0===s&&i?(e.name=t.slice(1,n),e.base=t.slice(1,a)):(e.name=t.slice(s,n),e.base=t.slice(s,a)),e.ext=t.slice(n,a)),s>0?e.dir=t.slice(0,s-1):i&&(e.dir="/"),e},sep:"/",delimiter:":",win32:null,posix:null};c.posix=c;const u=c.extname,g=c.basename;class f{constructor(){let t=(()=>"undefined"==typeof global)(),e="image/png",r="image/jpeg",o="image/jpeg",i="image/webp",n="application/pdf",s="image/svg+xml";Object.assign(this,{toMime:this.toMime.bind(this),fromMime:this.fromMime.bind(this),expected:t?'"png", "jpg", or "webp"':'"png", "jpg", "pdf", or "svg"',formats:t?{png:e,jpg:r,jpeg:o,webp:i}:{png:e,jpg:r,jpeg:o,pdf:n,svg:s},mimes:t?{[e]:"png",[r]:"jpg",[i]:"webp"}:{[e]:"png",[r]:"jpg",[n]:"pdf",[s]:"svg"}})}toMime(t){return this.formats[(t||"").replace(/^\./,"").toLowerCase()]}fromMime(t){return this.mimes[t]}}class d{static for(t){return(new d).append(t).get()}constructor(){this.crc=-1}get(){return~this.crc}append(t){for(var e=0|this.crc,r=this.table,o=0,i=0|t.length;o<i;o++)e=e>>>8^r[255&(e^t[o])];return this.crc=e,this}}function p(t){let e=new Uint8Array(t),r=new DataView(e.buffer),o={array:e,view:r,size:t,set8:(t,e)=>(r.setUint8(t,e),o),set16:(t,e)=>(r.setUint16(t,e,!0),o),set32:(t,e)=>(r.setUint32(t,e,!0),o),bytes:(t,r)=>(e.set(r,t),o)};return o}d.prototype.table=(()=>{var t,e,r,o=[];for(t=0;t<256;t++){for(r=t,e=0;e<8;e++)r=1&r?r>>>1^3988292384:r>>>1;o[t]=r}return o})();class m{constructor(t){let e=new Date;Object.assign(this,{directory:t,offset:0,files:[],time:(e.getHours()<<6|e.getMinutes())<<5|e.getSeconds()/2,date:(e.getFullYear()-1980<<4|e.getMonth()+1)<<5|e.getDate()}),this.add(t)}async add(t,e){let r=!e,o=m.encoder.encode(`${this.directory}/${r?"":t}`),i=new Uint8Array(r?0:await e.arrayBuffer()),n=30+o.length,s=n+i.length,{offset:a}=this,l=p(26).set32(0,134742036).set16(6,this.time).set16(8,this.date).set32(10,d.for(i)).set32(14,i.length).set32(18,i.length).set16(22,o.length);a+=n;let h=p(n+i.length+16).set32(0,67324752).bytes(4,l.array).bytes(30,o).bytes(n,i);a+=i.length,h.set32(s,134695760).bytes(s+4,l.array.slice(10,22)),a+=16,this.files.push({offset:a,folder:r,name:o,header:l,payload:h}),this.offset=a}toBuffer(){let t=this.files.reduce(((t,{name:e})=>46+e.length+t),0),e=p(t+22),r=0;for(var{offset:o,name:i,header:n,folder:s}of this.files)e.set32(r,33639248).set16(r+4,20).bytes(r+6,n.array).set8(r+38,s?16:0).set32(r+42,o).bytes(r+46,i),r+=46+i.length;e.set32(r,101010256).set16(r+8,this.files.length).set16(r+10,this.files.length).set32(r+12,t).set32(r+16,this.offset);let a=new Uint8Array(this.offset+e.size),l=0;for(var{payload:h}of this.files)a.set(h.array,l),l+=h.size;return a.set(e.array,l),a}get blob(){return new Blob([this.toBuffer()],{type:"application/zip"})}}m.encoder=new TextEncoder;const w=(t,e,r,o)=>{if(o){let{width:e,height:r}=t,i=Object.assign(document.createElement("canvas"),{width:e,height:r}),n=i.getContext("2d");n.fillStyle=o,n.fillRect(0,0,e,r),n.drawImage(t,0,0),t=i}return new Promise(((o,i)=>t.toBlob(o,e,r)))},y=(t,e)=>{const r=window.URL.createObjectURL(e),o=document.createElement("a");o.style.display="none",o.href=r,o.setAttribute("download",t),void 0===o.download&&o.setAttribute("target","_blank"),document.body.appendChild(o),o.click(),document.body.removeChild(o),setTimeout((()=>window.URL.revokeObjectURL(r)),100)},b={asBuffer:(...t)=>w(...t).then((t=>t.arrayBuffer())),asDownload:async(t,e,r,o,i)=>{y(i,await w(t,e,r,o))},asZipDownload:async(t,e,r,o,i,n,s)=>{let a=g(i,".zip")||"archive",l=new m(a);await Promise.all(t.map((async(t,i)=>{let a=(t=>n.replace("{}",String(t+1).padStart(s,"0")))(i);await l.add(a,await w(t,e,r,o))}))),y(`${a}.zip`,l.blob)},atScale:(t,e,r)=>t.map((t=>{if(1==e&&!r)return t.canvas;let o=document.createElement("canvas"),i=o.getContext("2d"),n=t.canvas?t.canvas:t;return o.width=n.width*e,o.height=n.height*e,r&&(i.fillStyle=r,i.fillRect(0,0,o.width,o.height)),i.scale(e,e),i.drawImage(n,0,0),o})),options:function(t,{filename:e="",extension:r="",format:o,page:i,quality:n,matte:s,density:a,outline:l,archive:h}={}){var{fromMime:c,toMime:d,expected:p}=new f,m=(h=h||"canvas",o||r.replace(/@\d+x$/i,"")||u(e)),w=(o=c(d(m)||m),d(o)),y=t.length;if(!m)throw new Error("Cannot determine image format (use a filename extension or 'format' argument)");if(!o)throw new Error(`Unsupported file format "${m}" (expected ${p})`);if(!y)throw new RangeError("Canvas has no associated contexts (try calling getContext or newPage first)");let b,v,C=e.replace(/{(\d*)}/g,((t,e)=>(v=!0,e=parseInt(e,10),b=isFinite(e)?e:isFinite(b)?b:-1,"{}"))),A=i>0?i-1:i<0?y+i:void 0;if(isFinite(A)&&A<0||A>=y)throw new RangeError(1==y?`Canvas only has a ‘page 1’ (${A} is out of bounds)`:`Canvas has pages 1–${y} (${A} is out of bounds)`);if(t=isFinite(A)?[t[A]]:v||"pdf"==o?t:t.slice(-1),void 0===n)n=.92;else if("number"!=typeof n||!isFinite(n)||n<0||n>1)throw new TypeError("The quality option must be an number in the 0.0–1.0 range");if(void 0===a){let t=(r||g(e,m)).match(/@(\d+)x$/i);a=t?parseInt(t[1],10):1}else if("number"!=typeof a||!Number.isInteger(a)||a<1)throw new TypeError("The density option must be a non-negative integer");return void 0===l?l=!0:"svg"==o&&(l=!!l),{filename:e,pattern:C,format:o,mime:w,pages:t,padding:b,quality:n,matte:s,density:a,outline:l,archive:h}}},{asBuffer:v,asDownload:C,asZipDownload:A,atScale:B,options:P}=b,T=Symbol.for("toDataURL");const D={Canvas:class{constructor(t,e){let r=document.createElement("canvas"),o=[];for(var[i,n]of(Object.defineProperty(r,"async",{value:!0,writable:!1,enumerable:!0}),Object.entries({png:()=>v(r,"image/png"),jpg:()=>v(r,"image/jpeg"),pages:()=>o.concat(r).map((t=>t.getContext("2d")))})))Object.defineProperty(r,i,{get:n});return Object.assign(r,{width:t,height:e,newPage(...t){var{width:e,height:i}=r,n=Object.assign(document.createElement("canvas"),{width:e,height:i});n.getContext("2d").drawImage(r,0,0),o.push(n);var[e,i]=t.length?t:[e,i];return Object.assign(r,{width:e,height:i}).getContext("2d")},saveAs(t,e){e="number"==typeof e?{quality:e}:e;let r=P(this.pages,{filename:t,...e}),{pattern:o,padding:i,mime:n,quality:s,matte:a,density:l,archive:h}=r,c=B(r.pages,l);return null==i?C(c[0],n,s,a,t):A(c,n,s,a,h,o,i)},toBuffer(t="png",e={}){e="number"==typeof e?{quality:e}:e;let r=P(this.pages,{extension:t,...e}),{mime:o,quality:i,matte:n,pages:s,density:a}=r,l=B(s,a,n)[0];return v(l,o,i,n)},[T]:r.toDataURL.bind(r),toDataURL(t="png",e={}){e="number"==typeof e?{quality:e}:e;let o=P(this.pages,{extension:t,...e}),{mime:i,quality:n,matte:s,pages:a,density:l}=o,h=B(a,l,s)[0],c=h[h===r?T:"toDataURL"](i,n);return Promise.resolve(c)}})}}},E=(t,e,r)=>{if(!t.image)return;const{image:o}=t,i=o.descriptor.width*o.descriptor.height;var n=((t,e,r)=>{const o=4096,i=r;var n,s,a,l,h,c,u,g,f,d;const p=new Array(r),m=new Array(o),w=new Array(o),y=new Array(4097);for(h=1+(s=1<<(d=t)),n=s+2,u=-1,a=(1<<(l=d+1))-1,g=0;g<s;g++)m[g]=0,w[g]=g;var b,v,C,A,B,P;for(b=v=C=A=B=P=0,f=0;f<i;){if(0===A){if(v<l){b+=e[P]<<v,v+=8,P++;continue}if(g=b&a,b>>=l,v-=l,g>n||g==h)break;if(g==s){a=(1<<(l=d+1))-1,n=s+2,u=-1;continue}if(-1==u){y[A++]=w[g],u=g,C=g;continue}for(c=g,g==n&&(y[A++]=C,g=u);g>s;)y[A++]=w[g],g=m[g];C=255&w[g],y[A++]=C,n<o&&(m[n]=u,w[n]=C,!(++n&a)&&n<o&&(l++,a+=n)),u=c}A--,p[B++]=y[A],f++}for(f=B;f<i;f++)p[f]=0;return p})(o.data.minCodeSize,o.data.blocks,i);o.descriptor.lct.interlaced&&(n=((t,e)=>{const r=new Array(t.length),o=t.length/e,i=function(o,i){const n=t.slice(i*e,(i+1)*e);r.splice.apply(r,[o*e,e].concat(n))},n=[0,4,2,1],s=[8,8,4,2];for(var a=0,l=0;l<4;l++)for(var h=n[l];h<o;h+=s[l])i(h,a),a++;return r})(n,o.descriptor.width));const s={pixels:n,dims:{top:t.image.descriptor.top,left:t.image.descriptor.left,width:t.image.descriptor.width,height:t.image.descriptor.height}};return o.descriptor.lct&&o.descriptor.lct.exists?s.colorTable=o.lct:s.colorTable=e,t.gce&&(s.delay=10*(t.gce.delay||10),s.disposalType=t.gce.extras.disposal,t.gce.extras.transparentColorGiven&&(s.transparentIndex=t.gce.transparentColorIndex)),s.patch=(t=>{const e=t.pixels.length,r=new Uint8ClampedArray(4*e);for(var o=0;o<e;o++){const e=4*o,i=t.pixels[o],n=t.colorTable[i];r[e]=n[0],r[e+1]=n[1],r[e+2]=n[2],r[e+3]=i!==t.transparentIndex?255:0}return r})(s),s};function x(t){var e=encodeURI(t).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return e.length+(e.length!=Number(t)?3:0)}class k{constructor(t){this.mode=S.MODE_8BIT_BYTE,this.parsedData=[],this.data=t;const e=[];for(let r=0,o=this.data.length;r<o;r++){const t=[],o=this.data.charCodeAt(r);o>65536?(t[0]=240|(1835008&o)>>>18,t[1]=128|(258048&o)>>>12,t[2]=128|(4032&o)>>>6,t[3]=128|63&o):o>2048?(t[0]=224|(61440&o)>>>12,t[1]=128|(4032&o)>>>6,t[2]=128|63&o):o>128?(t[0]=192|(1984&o)>>>6,t[1]=128|63&o):t[0]=o,e.push(t)}this.parsedData=Array.prototype.concat.apply([],e),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}getLength(){return this.parsedData.length}write(t){for(let e=0,r=this.parsedData.length;e<r;e++)t.put(this.parsedData[e],8)}}class L{constructor(t=-1,e=R.L){this.moduleCount=0,this.dataList=[],this.typeNumber=t,this.errorCorrectLevel=e,this.moduleCount=0,this.dataList=[]}addData(t){if(this.typeNumber<=0)this.typeNumber=function(t,e){for(var r=1,o=x(t),i=0,n=U.length;i<n;i++){var s=0;switch(e){case R.L:s=U[i][0];break;case R.M:s=U[i][1];break;case R.Q:s=U[i][2];break;case R.H:s=U[i][3]}if(o<=s)break;r++}if(r>U.length)throw new Error("Too long data");return r}(t,this.errorCorrectLevel);else{if(this.typeNumber>40)throw new Error(`Invalid QR version: ${this.typeNumber}`);if(!function(t,e,r){const o=x(e),i=t-1;let n=0;switch(r){case R.L:n=U[i][0];break;case R.M:n=U[i][1];break;case R.Q:n=U[i][2];break;case R.H:n=U[i][3]}return o<=n}(this.typeNumber,t,this.errorCorrectLevel))throw new Error(`Data is too long for QR version: ${this.typeNumber}`)}const e=new k(t);this.dataList.push(e),this.dataCache=void 0}isDark(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error(`${t},${e}`);return this.modules[t][e]}getModuleCount(){return this.moduleCount}make(){this.makeImpl(!1,this.getBestMaskPattern())}makeImpl(t,e){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(let r=0;r<this.moduleCount;r++){this.modules[r]=new Array(this.moduleCount);for(let t=0;t<this.moduleCount;t++)this.modules[r][t]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,e),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=L.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,e)}setupPositionProbePattern(t,e){for(let r=-1;r<=7;r++)if(!(t+r<=-1||this.moduleCount<=t+r))for(let o=-1;o<=7;o++)e+o<=-1||this.moduleCount<=e+o||(this.modules[t+r][e+o]=0<=r&&r<=6&&(0==o||6==o)||0<=o&&o<=6&&(0==r||6==r)||2<=r&&r<=4&&2<=o&&o<=4)}getBestMaskPattern(){if(Number.isInteger(this.maskPattern)&&Object.values(M).includes(this.maskPattern))return this.maskPattern;let t=0,e=0;for(let r=0;r<8;r++){this.makeImpl(!0,r);const o=_.getLostPoint(this);(0==r||t>o)&&(t=o,e=r)}return e}setupTimingPattern(){for(let t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(let t=8;t<this.moduleCount-8;t++)null==this.modules[6][t]&&(this.modules[6][t]=t%2==0)}setupPositionAdjustPattern(){const t=_.getPatternPosition(this.typeNumber);for(let e=0;e<t.length;e++)for(let r=0;r<t.length;r++){const o=t[e],i=t[r];if(null==this.modules[o][i])for(let t=-2;t<=2;t++)for(let e=-2;e<=2;e++)this.modules[o+t][i+e]=-2==t||2==t||-2==e||2==e||0==t&&0==e}}setupTypeNumber(t){const e=_.getBCHTypeNumber(this.typeNumber);for(var r=0;r<18;r++){var o=!t&&1==(e>>r&1);this.modules[Math.floor(r/3)][r%3+this.moduleCount-8-3]=o}for(r=0;r<18;r++){o=!t&&1==(e>>r&1);this.modules[r%3+this.moduleCount-8-3][Math.floor(r/3)]=o}}setupTypeInfo(t,e){const r=this.errorCorrectLevel<<3|e,o=_.getBCHTypeInfo(r);for(var i=0;i<15;i++){var n=!t&&1==(o>>i&1);i<6?this.modules[i][8]=n:i<8?this.modules[i+1][8]=n:this.modules[this.moduleCount-15+i][8]=n}for(i=0;i<15;i++){n=!t&&1==(o>>i&1);i<8?this.modules[8][this.moduleCount-i-1]=n:i<9?this.modules[8][15-i-1+1]=n:this.modules[8][15-i-1]=n}this.modules[this.moduleCount-8][8]=!t}mapData(t,e){let r=-1,o=this.moduleCount-1,i=7,n=0;for(let s=this.moduleCount-1;s>0;s-=2)for(6==s&&s--;;){for(let r=0;r<2;r++)if(null==this.modules[o][s-r]){let a=!1;n<t.length&&(a=1==(t[n]>>>i&1));_.getMask(e,o,s-r)&&(a=!a),this.modules[o][s-r]=a,i--,-1==i&&(n++,i=7)}if(o+=r,o<0||this.moduleCount<=o){o-=r,r=-r;break}}}static createData(t,e,r){const o=O.getRSBlocks(t,e),i=new j;for(var n=0;n<r.length;n++){const e=r[n];i.put(e.mode,4),i.put(e.getLength(),_.getLengthInBits(e.mode,t)),e.write(i)}let s=0;for(n=0;n<o.length;n++)s+=o[n].dataCount;if(i.getLengthInBits()>8*s)throw new Error(`code length overflow. (${i.getLengthInBits()}>${8*s})`);for(i.getLengthInBits()+4<=8*s&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(!1);for(;!(i.getLengthInBits()>=8*s||(i.put(L.PAD0,8),i.getLengthInBits()>=8*s));)i.put(L.PAD1,8);return L.createBytes(i,o)}static createBytes(t,e){let r=0,o=0,i=0;const n=new Array(e.length),s=new Array(e.length);for(var a=0;a<e.length;a++){const h=e[a].dataCount,c=e[a].totalCount-h;o=Math.max(o,h),i=Math.max(i,c),n[a]=new Array(h);for(var l=0;l<n[a].length;l++)n[a][l]=255&t.buffer[l+r];r+=h;const u=_.getErrorCorrectPolynomial(c),g=new N(n[a],u.getLength()-1).mod(u);s[a]=new Array(u.getLength()-1);for(l=0;l<s[a].length;l++){const t=l+g.getLength()-s[a].length;s[a][l]=t>=0?g.get(t):0}}let h=0;for(l=0;l<e.length;l++)h+=e[l].totalCount;const c=new Array(h);let u=0;for(l=0;l<o;l++)for(a=0;a<e.length;a++)l<n[a].length&&(c[u++]=n[a][l]);for(l=0;l<i;l++)for(a=0;a<e.length;a++)l<s[a].length&&(c[u++]=s[a][l]);return c}}L.PAD0=236,L.PAD1=17;const R={L:1,M:0,Q:3,H:2},S={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},M={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};class _{static getBCHTypeInfo(t){let e=t<<10;for(;_.getBCHDigit(e)-_.getBCHDigit(_.G15)>=0;)e^=_.G15<<_.getBCHDigit(e)-_.getBCHDigit(_.G15);return(t<<10|e)^_.G15_MASK}static getBCHTypeNumber(t){let e=t<<12;for(;_.getBCHDigit(e)-_.getBCHDigit(_.G18)>=0;)e^=_.G18<<_.getBCHDigit(e)-_.getBCHDigit(_.G18);return t<<12|e}static getBCHDigit(t){let e=0;for(;0!=t;)e++,t>>>=1;return e}static getPatternPosition(t){return _.PATTERN_POSITION_TABLE[t-1]}static getMask(t,e,r){switch(t){case M.PATTERN000:return(e+r)%2==0;case M.PATTERN001:return e%2==0;case M.PATTERN010:return r%3==0;case M.PATTERN011:return(e+r)%3==0;case M.PATTERN100:return(Math.floor(e/2)+Math.floor(r/3))%2==0;case M.PATTERN101:return e*r%2+e*r%3==0;case M.PATTERN110:return(e*r%2+e*r%3)%2==0;case M.PATTERN111:return(e*r%3+(e+r)%2)%2==0;default:throw new Error(`bad maskPattern:${t}`)}}static getErrorCorrectPolynomial(t){let e=new N([1],0);for(let r=0;r<t;r++)e=e.multiply(new N([1,I.gexp(r)],0));return e}static getLengthInBits(t,e){if(1<=e&&e<10)switch(t){case S.MODE_NUMBER:return 10;case S.MODE_ALPHA_NUM:return 9;case S.MODE_8BIT_BYTE:case S.MODE_KANJI:return 8;default:throw new Error(`mode:${t}`)}else if(e<27)switch(t){case S.MODE_NUMBER:return 12;case S.MODE_ALPHA_NUM:return 11;case S.MODE_8BIT_BYTE:return 16;case S.MODE_KANJI:return 10;default:throw new Error(`mode:${t}`)}else{if(!(e<41))throw new Error(`type:${e}`);switch(t){case S.MODE_NUMBER:return 14;case S.MODE_ALPHA_NUM:return 13;case S.MODE_8BIT_BYTE:return 16;case S.MODE_KANJI:return 12;default:throw new Error(`mode:${t}`)}}}static getLostPoint(t){const e=t.getModuleCount();let r=0;for(var o=0;o<e;o++)for(var i=0;i<e;i++){let n=0;const s=t.isDark(o,i);for(let r=-1;r<=1;r++)if(!(o+r<0||e<=o+r))for(let a=-1;a<=1;a++)i+a<0||e<=i+a||0==r&&0==a||s==t.isDark(o+r,i+a)&&n++;n>5&&(r+=3+n-5)}for(o=0;o<e-1;o++)for(i=0;i<e-1;i++){let e=0;t.isDark(o,i)&&e++,t.isDark(o+1,i)&&e++,t.isDark(o,i+1)&&e++,t.isDark(o+1,i+1)&&e++,0!=e&&4!=e||(r+=3)}for(o=0;o<e;o++)for(i=0;i<e-6;i++)t.isDark(o,i)&&!t.isDark(o,i+1)&&t.isDark(o,i+2)&&t.isDark(o,i+3)&&t.isDark(o,i+4)&&!t.isDark(o,i+5)&&t.isDark(o,i+6)&&(r+=40);for(i=0;i<e;i++)for(o=0;o<e-6;o++)t.isDark(o,i)&&!t.isDark(o+1,i)&&t.isDark(o+2,i)&&t.isDark(o+3,i)&&t.isDark(o+4,i)&&!t.isDark(o+5,i)&&t.isDark(o+6,i)&&(r+=40);let n=0;for(i=0;i<e;i++)for(o=0;o<e;o++)t.isDark(o,i)&&n++;return r+=10*(Math.abs(100*n/e/e-50)/5),r}}_.PATTERN_POSITION_TABLE=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],_.G15=1335,_.G18=7973,_.G15_MASK=21522;class I{static glog(t){if(t<1)throw new Error(`glog(${t})`);return I.LOG_TABLE[t]}static gexp(t){for(;t<0;)t+=255;for(;t>=256;)t-=255;return I.EXP_TABLE[t]}}I.EXP_TABLE=new Array(256),I.LOG_TABLE=new Array(256),I._constructor=function(){for(var t=0;t<8;t++)I.EXP_TABLE[t]=1<<t;for(t=8;t<256;t++)I.EXP_TABLE[t]=I.EXP_TABLE[t-4]^I.EXP_TABLE[t-5]^I.EXP_TABLE[t-6]^I.EXP_TABLE[t-8];for(t=0;t<255;t++)I.LOG_TABLE[I.EXP_TABLE[t]]=t}();class N{constructor(t,e){if(null==t.length)throw new Error(`${t.length}/${e}`);let r=0;for(;r<t.length&&0==t[r];)r++;this.num=new Array(t.length-r+e);for(let o=0;o<t.length-r;o++)this.num[o]=t[o+r]}get(t){return this.num[t]}getLength(){return this.num.length}multiply(t){const e=new Array(this.getLength()+t.getLength()-1);for(let r=0;r<this.getLength();r++)for(let o=0;o<t.getLength();o++)e[r+o]^=I.gexp(I.glog(this.get(r))+I.glog(t.get(o)));return new N(e,0)}mod(t){if(this.getLength()-t.getLength()<0)return this;const e=I.glog(this.get(0))-I.glog(t.get(0)),r=new Array(this.getLength());for(var o=0;o<this.getLength();o++)r[o]=this.get(o);for(o=0;o<t.getLength();o++)r[o]^=I.gexp(I.glog(t.get(o))+e);return new N(r,0).mod(t)}}class O{constructor(t,e){this.totalCount=t,this.dataCount=e}static getRSBlocks(t,e){const r=O.getRsBlockTable(t,e);if(null==r)throw new Error(`bad rs block @ typeNumber:${t}/errorCorrectLevel:${e}`);const o=r.length/3,i=[];for(let n=0;n<o;n++){const t=r[3*n+0],e=r[3*n+1],o=r[3*n+2];for(let r=0;r<t;r++)i.push(new O(e,o))}return i}static getRsBlockTable(t,e){switch(e){case R.L:return O.RS_BLOCK_TABLE[4*(t-1)+0];case R.M:return O.RS_BLOCK_TABLE[4*(t-1)+1];case R.Q:return O.RS_BLOCK_TABLE[4*(t-1)+2];case R.H:return O.RS_BLOCK_TABLE[4*(t-1)+3];default:return}}}O.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];class j{constructor(){this.buffer=[],this.length=0}get(t){const e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)}put(t,e){for(let r=0;r<e;r++)this.putBit(1==(t>>>e-r-1&1))}getLengthInBits(){return this.length}putBit(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}}const U=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];var F=256,$=1024,z=1<<18;function G(t,e){var r,o,i,n,s;function a(t,e,o,i,n){r[e][0]-=t*(r[e][0]-o)/$,r[e][1]-=t*(r[e][1]-i)/$,r[e][2]-=t*(r[e][2]-n)/$}function l(t,e,o,i,n){for(var a,l,h=Math.abs(e-t),c=Math.min(e+t,F),u=e+1,g=e-1,f=1;u<c||g>h;)l=s[f++],u<c&&((a=r[u++])[0]-=l*(a[0]-o)/z,a[1]-=l*(a[1]-i)/z,a[2]-=l*(a[2]-n)/z),g>h&&((a=r[g--])[0]-=l*(a[0]-o)/z,a[1]-=l*(a[1]-i)/z,a[2]-=l*(a[2]-n)/z)}function h(t,e,o){var s,a,l,h,c,u=2147483647,g=u,f=-1,d=f;for(s=0;s<F;s++)a=r[s],(l=Math.abs(a[0]-t)+Math.abs(a[1]-e)+Math.abs(a[2]-o))<u&&(u=l,f=s),(h=l-(i[s]>>12))<g&&(g=h,d=s),c=n[s]>>10,n[s]-=c,i[s]+=c<<10;return n[f]+=64,i[f]-=65536,d}this.buildColormap=function(){!function(){var t,e;for(r=[],o=new Int32Array(256),i=new Int32Array(F),n=new Int32Array(F),s=new Int32Array(32),t=0;t<F;t++)e=(t<<12)/F,r[t]=new Float64Array([e,e,e,0]),n[t]=256,i[t]=0}(),function(){var r,o,i,n,c,u,g=t.length,f=30+(e-1)/3,d=g/(3*e),p=~~(d/100),m=$,w=2048,y=w>>6;for(y<=1&&(y=0),r=0;r<y;r++)s[r]=m*(256*(y*y-r*r)/(y*y));g<1509?(e=1,o=3):o=g%499!=0?1497:g%491!=0?1473:g%487!=0?1461:1509;var b=0;for(r=0;r<d;)if(a(m,u=h(i=(255&t[b])<<4,n=(255&t[b+1])<<4,c=(255&t[b+2])<<4),i,n,c),0!==y&&l(y,u,i,n,c),(b+=o)>=g&&(b-=g),0===p&&(p=1),++r%p==0)for(m-=m/f,(y=(w-=w/30)>>6)<=1&&(y=0),u=0;u<y;u++)s[u]=m*(256*(y*y-u*u)/(y*y))}(),function(){for(var t=0;t<F;t++)r[t][0]>>=4,r[t][1]>>=4,r[t][2]>>=4,r[t][3]=t}(),function(){var t,e,i,n,s,a,l=0,h=0;for(t=0;t<F;t++){for(s=t,a=(i=r[t])[1],e=t+1;e<F;e++)(n=r[e])[1]<a&&(s=e,a=n[1]);if(n=r[s],t!=s&&(e=n[0],n[0]=i[0],i[0]=e,e=n[1],n[1]=i[1],i[1]=e,e=n[2],n[2]=i[2],i[2]=e,e=n[3],n[3]=i[3],i[3]=e),a!=l){for(o[l]=h+t>>1,e=l+1;e<a;e++)o[e]=t;l=a,h=t}}for(o[l]=h+255>>1,e=l+1;e<256;e++)o[e]=255}()},this.getColormap=function(){for(var t=[],e=[],o=0;o<F;o++)e[r[o][3]]=o;for(var i=0,n=0;n<F;n++){var s=e[n];t[i++]=r[s][0],t[i++]=r[s][1],t[i++]=r[s][2]}return t},this.lookupRGB=function(t,e,i){for(var n,s,a,l=1e3,h=-1,c=o[e],u=c-1;c<F||u>=0;)c<F&&((a=(s=r[c])[1]-e)>=l?c=F:(c++,a<0&&(a=-a),(n=s[0]-t)<0&&(n=-n),(a+=n)<l&&((n=s[2]-i)<0&&(n=-n),(a+=n)<l&&(l=a,h=s[3])))),u>=0&&((a=e-(s=r[u])[1])>=l?u=-1:(u--,a<0&&(a=-a),(n=s[0]-t)<0&&(n=-n),(a+=n)<l&&((n=s[2]-i)<0&&(n=-n),(a+=n)<l&&(l=a,h=s[3]))));return h}}var H=5003,q=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535];function Q(t,e,r,o){var i,n,s,a,l,h,c,u,g,f=Math.max(2,o),d=new Uint8Array(256),p=new Int32Array(H),m=new Int32Array(H),w=0,y=0,b=!1;function v(t,e){d[n++]=t,n>=254&&B(e)}function C(t){A(H),y=l+2,b=!0,D(l,t)}function A(t){for(var e=0;e<t;++e)p[e]=-1}function B(t){n>0&&(t.writeByte(n),t.writeBytes(d,0,n),n=0)}function P(t){return(1<<t)-1}function T(){return 0===c?-1:(--c,255&r[u++])}function D(t,e){for(i&=q[w],w>0?i|=t<<w:i=t,w+=g;w>=8;)v(255&i,e),i>>=8,w-=8;if((y>s||b)&&(b?(s=P(g=a),b=!1):(++g,s=12==g?4096:P(g))),t==h){for(;w>0;)v(255&i,e),i>>=8,w-=8;B(e)}}this.encode=function(r){r.writeByte(f),c=t*e,u=0,function(t,e){var r,o,i,c,u,f,d;for(b=!1,s=P(g=a=t),h=1+(l=1<<t-1),y=l+2,n=0,c=T(),d=0,r=H;r<65536;r*=2)++d;d=8-d,A(f=H),D(l,e);t:for(;-1!=(o=T());)if(r=(o<<12)+c,p[i=o<<d^c]!==r){if(p[i]>=0){u=f-i,0===i&&(u=1);do{if((i-=u)<0&&(i+=f),p[i]===r){c=m[i];continue t}}while(p[i]>=0)}D(c,e),c=o,y<4096?(m[i]=y++,p[i]=r):C(e)}else c=m[i];D(c,e),D(h,e)}(f+1,r),r.writeByte(0)}}function K(){this.page=-1,this.pages=[],this.newPage()}K.pageSize=4096,K.charMap={};for(var X=0;X<256;X++)K.charMap[X]=String.fromCharCode(X);function Y(t,e){this.width=~~t,this.height=~~e,this.transparent=null,this.transIndex=0,this.repeat=-1,this.delay=0,this.image=null,this.pixels=null,this.indexedPixels=null,this.colorDepth=null,this.colorTab=null,this.neuQuant=null,this.usedEntry=new Array,this.palSize=7,this.dispose=-1,this.firstFrame=!0,this.sample=10,this.dither=!1,this.globalPalette=!1,this.out=new K}K.prototype.newPage=function(){this.pages[++this.page]=new Uint8Array(K.pageSize),this.cursor=0},K.prototype.getData=function(){for(var t="",e=0;e<this.pages.length;e++)for(var r=0;r<K.pageSize;r++)t+=K.charMap[this.pages[e][r]];return t},K.prototype.toFlattenUint8Array=function(){const t=[];for(var e=0;e<this.pages.length;e++)if(e===this.pages.length-1){const r=Uint8Array.from(this.pages[e].slice(0,this.cursor));t.push(r)}else t.push(this.pages[e]);const r=new Uint8Array(t.reduce(((t,e)=>t+e.length),0));return t.reduce(((t,e)=>(r.set(e,t),t+e.length)),0),r},K.prototype.writeByte=function(t){this.cursor>=K.pageSize&&this.newPage(),this.pages[this.page][this.cursor++]=t},K.prototype.writeUTFBytes=function(t){for(var e=t.length,r=0;r<e;r++)this.writeByte(t.charCodeAt(r))},K.prototype.writeBytes=function(t,e,r){for(var o=r||t.length,i=e||0;i<o;i++)this.writeByte(t[i])},Y.prototype.setDelay=function(t){this.delay=Math.round(t/10)},Y.prototype.setFrameRate=function(t){this.delay=Math.round(100/t)},Y.prototype.setDispose=function(t){t>=0&&(this.dispose=t)},Y.prototype.setRepeat=function(t){this.repeat=t},Y.prototype.setTransparent=function(t){this.transparent=t},Y.prototype.addFrame=function(t){this.image=t,this.colorTab=this.globalPalette&&this.globalPalette.slice?this.globalPalette:null,this.getImagePixels(),this.analyzePixels(),!0===this.globalPalette&&(this.globalPalette=this.colorTab),this.firstFrame&&(this.writeHeader(),this.writeLSD(),this.writePalette(),this.repeat>=0&&this.writeNetscapeExt()),this.writeGraphicCtrlExt(),this.writeImageDesc(),this.firstFrame||this.globalPalette||this.writePalette(),this.writePixels(),this.firstFrame=!1},Y.prototype.finish=function(){this.out.writeByte(59)},Y.prototype.setQuality=function(t){t<1&&(t=1),this.sample=t},Y.prototype.setDither=function(t){!0===t&&(t="FloydSteinberg"),this.dither=t},Y.prototype.setGlobalPalette=function(t){this.globalPalette=t},Y.prototype.getGlobalPalette=function(){return this.globalPalette&&this.globalPalette.slice&&this.globalPalette.slice(0)||this.globalPalette},Y.prototype.writeHeader=function(){this.out.writeUTFBytes("GIF89a")},Y.prototype.analyzePixels=function(){this.colorTab||(this.neuQuant=new G(this.pixels,this.sample),this.neuQuant.buildColormap(),this.colorTab=this.neuQuant.getColormap()),this.dither?this.ditherPixels(this.dither.replace("-serpentine",""),null!==this.dither.match(/-serpentine/)):this.indexPixels(),this.pixels=null,this.colorDepth=8,this.palSize=7,null!==this.transparent&&(this.transIndex=this.findClosest(this.transparent,!0))},Y.prototype.indexPixels=function(t){var e=this.pixels.length/3;this.indexedPixels=new Uint8Array(e);for(var r=0,o=0;o<e;o++){var i=this.findClosestRGB(255&this.pixels[r++],255&this.pixels[r++],255&this.pixels[r++]);this.usedEntry[i]=!0,this.indexedPixels[o]=i}},Y.prototype.ditherPixels=function(t,e){var r={FalseFloydSteinberg:[[3/8,1,0],[3/8,0,1],[2/8,1,1]],FloydSteinberg:[[7/16,1,0],[3/16,-1,1],[5/16,0,1],[1/16,1,1]],Stucki:[[8/42,1,0],[4/42,2,0],[2/42,-2,1],[4/42,-1,1],[8/42,0,1],[4/42,1,1],[2/42,2,1],[1/42,-2,2],[2/42,-1,2],[4/42,0,2],[2/42,1,2],[1/42,2,2]],Atkinson:[[1/8,1,0],[1/8,2,0],[1/8,-1,1],[1/8,0,1],[1/8,1,1],[1/8,0,2]]};if(!t||!r[t])throw"Unknown dithering kernel: "+t;var o=r[t],i=0,n=this.height,s=this.width,a=this.pixels,l=e?-1:1;this.indexedPixels=new Uint8Array(this.pixels.length/3);for(var h=0;h<n;h++){e&&(l*=-1);for(var c=1==l?0:s-1,u=1==l?s:0;c!==u;c+=l){var g=3*(i=h*s+c),f=a[g],d=a[g+1],p=a[g+2];g=this.findClosestRGB(f,d,p),this.usedEntry[g]=!0,this.indexedPixels[i]=g,g*=3;for(var m=f-this.colorTab[g],w=d-this.colorTab[g+1],y=p-this.colorTab[g+2],b=1==l?0:o.length-1,v=1==l?o.length:0;b!==v;b+=l){var C=o[b][1],A=o[b][2];if(C+c>=0&&C+c<s&&A+h>=0&&A+h<n){var B=o[b][0];g=i+C+A*s,a[g*=3]=Math.max(0,Math.min(255,a[g]+m*B)),a[g+1]=Math.max(0,Math.min(255,a[g+1]+w*B)),a[g+2]=Math.max(0,Math.min(255,a[g+2]+y*B))}}}}},Y.prototype.findClosest=function(t,e){return this.findClosestRGB((16711680&t)>>16,(65280&t)>>8,255&t,e)},Y.prototype.findClosestRGB=function(t,e,r,o){if(null===this.colorTab)return-1;if(this.neuQuant&&!o)return this.neuQuant.lookupRGB(t,e,r);for(var i=0,n=16777216,s=this.colorTab.length,a=0,l=0;a<s;l++){var h=t-(255&this.colorTab[a++]),c=e-(255&this.colorTab[a++]),u=r-(255&this.colorTab[a++]),g=h*h+c*c+u*u;(!o||this.usedEntry[l])&&g<n&&(n=g,i=l)}return i},Y.prototype.getImagePixels=function(){var t=this.width,e=this.height;this.pixels=new Uint8Array(t*e*3);for(var r=this.image,o=0,i=0,n=0;n<e;n++)for(var s=0;s<t;s++)this.pixels[i++]=r[o++],this.pixels[i++]=r[o++],this.pixels[i++]=r[o++],o++},Y.prototype.writeGraphicCtrlExt=function(){var t,e;this.out.writeByte(33),this.out.writeByte(249),this.out.writeByte(4),null===this.transparent?(t=0,e=0):(t=1,e=2),this.dispose>=0&&(e=7&this.dispose),e<<=2,this.out.writeByte(e|t),this.writeShort(this.delay),this.out.writeByte(this.transIndex),this.out.writeByte(0)},Y.prototype.writeImageDesc=function(){this.out.writeByte(44),this.writeShort(0),this.writeShort(0),this.writeShort(this.width),this.writeShort(this.height),this.firstFrame||this.globalPalette?this.out.writeByte(0):this.out.writeByte(128|this.palSize)},Y.prototype.writeLSD=function(){this.writeShort(this.width),this.writeShort(this.height),this.out.writeByte(240|this.palSize),this.out.writeByte(0),this.out.writeByte(0)},Y.prototype.writeNetscapeExt=function(){this.out.writeByte(33),this.out.writeByte(255),this.out.writeByte(11),this.out.writeUTFBytes("NETSCAPE2.0"),this.out.writeByte(3),this.out.writeByte(1),this.writeShort(this.repeat),this.out.writeByte(0)},Y.prototype.writePalette=function(){this.out.writeBytes(this.colorTab);for(var t=768-this.colorTab.length,e=0;e<t;e++)this.out.writeByte(0)},Y.prototype.writeShort=function(t){this.out.writeByte(255&t),this.out.writeByte(t>>8&255)},Y.prototype.writePixels=function(){new Q(this.width,this.height,this.indexedPixels,this.colorDepth).encode(this.out)},Y.prototype.stream=function(){return this.out};var J=function(t,e,r,o){return new(r||(r=Promise))((function(i,n){function s(t){try{l(o.next(t))}catch(e){n(e)}}function a(t){try{l(o.throw(t))}catch(e){n(e)}}function l(t){var e;t.done?i(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(s,a)}l((o=o.apply(t,e||[])).next())}))};const{Canvas:W}=D,Z=.4;function V(t){if(t)return new Promise((function(r,o){if("data"==t.slice(0,4)){let i=new Image;return i.onload=function(){r(i),e(i)},i.onerror=function(){o("Image load error"),e(i)},void(i.src=t)}let i=new Image;i.setAttribute("crossOrigin","Anonymous"),i.onload=function(){r(i)},i.onerror=function(){o("Image load error")},i.src=t}));function e(t){t.onload=null,t.onerror=null}}class tt{constructor(t){const e=Object.assign({},t);if(Object.keys(tt.defaultOptions).forEach((t=>{t in e||Object.defineProperty(e,t,{value:tt.defaultOptions[t],enumerable:!0,writable:!0})})),e.components?"object"==typeof e.components&&Object.keys(tt.defaultComponentOptions).forEach((t=>{t in e.components?Object.defineProperty(e.components,t,{value:Object.assign(Object.assign({},tt.defaultComponentOptions[t]),e.components[t]),enumerable:!0,writable:!0}):Object.defineProperty(e.components,t,{value:tt.defaultComponentOptions[t],enumerable:!0,writable:!0})})):e.components=tt.defaultComponentOptions,null!==e.dotScale&&void 0!==e.dotScale){if(e.dotScale<=0||e.dotScale>1)throw new Error("dotScale should be in range (0, 1].");e.components.data.scale=e.dotScale,e.components.timing.scale=e.dotScale,e.components.alignment.scale=e.dotScale}this.options=e,this.canvas=new W(t.size,t.size),this.canvasContext=this.canvas.getContext("2d"),this.qrCode=new L(-1,this.options.correctLevel),Number.isInteger(this.options.maskPattern)&&(this.qrCode.maskPattern=this.options.maskPattern),Number.isInteger(this.options.version)&&(this.qrCode.typeNumber=this.options.version),this.qrCode.addData(this.options.text),this.qrCode.make()}draw(){return new Promise((t=>this._draw().then(t)))}_clear(){this.canvasContext.clearRect(0,0,this.canvas.width,this.canvas.height)}static _prepareRoundedCornerClip(t,e,r,o,i,n){t.beginPath(),t.moveTo(e,r),t.arcTo(e+o,r,e+o,r+i,n),t.arcTo(e+o,r+i,e,r+i,n),t.arcTo(e,r+i,e,r,n),t.arcTo(e,r,e+o,r,n),t.closePath()}static _getAverageRGB(t){const e={r:0,g:0,b:0};let r,o,i=-4;const n={r:0,g:0,b:0};let s=0;o=t.naturalHeight||t.height,r=t.naturalWidth||t.width;const a=new W(r,o).getContext("2d");if(!a)return e;let l;a.drawImage(t,0,0);try{l=a.getImageData(0,0,r,o)}catch(h){return e}for(;(i+=20)<l.data.length;)l.data[i]>200||l.data[i+1]>200||l.data[i+2]>200||(++s,n.r+=l.data[i],n.g+=l.data[i+1],n.b+=l.data[i+2]);return n.r=~~(n.r/s),n.g=~~(n.g/s),n.b=~~(n.b/s),n}static _drawDot(t,e,r,o,i=0,n=1){t.fillRect((e+i)*o,(r+i)*o,n*o,n*o)}static _drawAlignProtector(t,e,r,o){t.clearRect((e-2)*o,(r-2)*o,5*o,5*o),t.fillRect((e-2)*o,(r-2)*o,5*o,5*o)}static _drawAlign(t,e,r,o,i=0,n=1,s,a){const l=t.fillStyle;t.fillStyle=s,new Array(4).fill(0).map(((s,a)=>{tt._drawDot(t,e-2+a,r-2,o,i,n),tt._drawDot(t,e+2,r-2+a,o,i,n),tt._drawDot(t,e+2-a,r+2,o,i,n),tt._drawDot(t,e-2,r+2-a,o,i,n)})),tt._drawDot(t,e,r,o,i,n),a||(t.fillStyle="rgba(255, 255, 255, 0.6)",new Array(2).fill(0).map(((s,a)=>{tt._drawDot(t,e-1+a,r-1,o,i,n),tt._drawDot(t,e+1,r-1+a,o,i,n),tt._drawDot(t,e+1-a,r+1,o,i,n),tt._drawDot(t,e-1,r+1-a,o,i,n)}))),t.fillStyle=l}_draw(){var o,i,n,s,a,l,h,c,u,g,f,d,p,m,w,y,b,v,C;return J(this,void 0,void 0,(function*(){const A=null===(o=this.qrCode)||void 0===o?void 0:o.moduleCount,B=this.options.size;let P=this.options.margin;(P<0||2*P>=B)&&(P=0);const T=Math.ceil(P),D=B-2*P,x=this.options.whiteMargin,k=this.options.backgroundDimming,L=Math.ceil(D/A),R=L*A,S=R+2*T,M=new W(S,S),I=M.getContext("2d");this._clear(),I.save(),I.translate(T,T);const N=new W(S,S),O=N.getContext("2d");let j=null,U=[];if(this.options.gifBackground){const o=(o=>{const i=new Uint8Array(o);return t(r(i),e)})(this.options.gifBackground);if(j=o,U=(F=o).frames.filter((t=>t.image)).map((t=>E(t,F.gct))),this.options.autoColor){let t=0,e=0,r=0,o=0;for(let i=0;i<U[0].colorTable.length;i++){const n=U[0].colorTable[i];n[0]>200||n[1]>200||n[2]>200||(0===n[0]&&0===n[1]&&0===n[2]||(o++,t+=n[0],e+=n[1],r+=n[2]))}t=~~(t/o),e=~~(e/o),r=~~(r/o),this.options.colorDark=`rgb(${t},${e},${r})`}}else if(this.options.backgroundImage){const t=yield V(this.options.backgroundImage);if(this.options.autoColor){const e=tt._getAverageRGB(t);this.options.colorDark=`rgb(${e.r},${e.g},${e.b})`}O.drawImage(t,0,0,t.width,t.height,0,0,S,S),O.rect(0,0,S,S),O.fillStyle=k,O.fill()}else O.rect(0,0,S,S),O.fillStyle=this.options.colorLight,O.fill();var F;const $=_.getPatternPosition(this.qrCode.typeNumber),z=(null===(n=null===(i=this.options.components)||void 0===i?void 0:i.data)||void 0===n?void 0:n.scale)||Z,G=.5*(1-z);for(let t=0;t<A;t++)for(let e=0;e<A;e++){const r=this.qrCode.isDark(t,e),o=e<8&&(t<8||t>=A-8)||e>=A-8&&t<8;let i=o||(6==t&&e>=8&&e<=A-8||6==e&&t>=8&&t<=A-8);for(let a=1;a<$.length-1;a++)i=i||t>=$[a]-2&&t<=$[a]+2&&e>=$[a]-2&&e<=$[a]+2;const n=e*L+(i?0:G*L),s=t*L+(i?0:G*L);if(I.strokeStyle=r?this.options.colorDark:this.options.colorLight,I.lineWidth=.5,I.fillStyle=r?this.options.colorDark:this.options.colorLight,0===$.length)i||I.fillRect(n,s,(i?1:z)*L,(i?1:z)*L);else{i||e<A-4&&e>=A-4-5&&t<A-4&&t>=A-4-5||I.fillRect(n,s,(i?1:z)*L,(i?1:z)*L)}}const H=$[$.length-1],q=this.options.colorLight;if(I.fillStyle=q,I.fillRect(0,0,8*L,8*L),I.fillRect(0,(A-8)*L,8*L,8*L),I.fillRect((A-8)*L,0,8*L,8*L),(null===(a=null===(s=this.options.components)||void 0===s?void 0:s.timing)||void 0===a?void 0:a.protectors)&&(I.fillRect(8*L,6*L,(A-8-8)*L,L),I.fillRect(6*L,8*L,L,(A-8-8)*L)),(null===(h=null===(l=this.options.components)||void 0===l?void 0:l.cornerAlignment)||void 0===h?void 0:h.protectors)&&tt._drawAlignProtector(I,H,H,L),null===(u=null===(c=this.options.components)||void 0===c?void 0:c.alignment)||void 0===u?void 0:u.protectors)for(let t=0;t<$.length;t++)for(let e=0;e<$.length;e++){const r=$[e],o=$[t];(6!==r||6!==o&&o!==H)&&((6!==o||6!==r&&r!==H)&&(r===H&&o===H||tt._drawAlignProtector(I,r,o,L)))}I.fillStyle=this.options.colorDark,I.fillRect(0,0,7*L,L),I.fillRect((A-7)*L,0,7*L,L),I.fillRect(0,6*L,7*L,L),I.fillRect((A-7)*L,6*L,7*L,L),I.fillRect(0,(A-7)*L,7*L,L),I.fillRect(0,(A-7+6)*L,7*L,L),I.fillRect(0,0,L,7*L),I.fillRect(6*L,0,L,7*L),I.fillRect((A-7)*L,0,L,7*L),I.fillRect((A-7+6)*L,0,L,7*L),I.fillRect(0,(A-7)*L,L,7*L),I.fillRect(6*L,(A-7)*L,L,7*L),I.fillRect(2*L,2*L,3*L,3*L),I.fillRect((A-7+2)*L,2*L,3*L,3*L),I.fillRect(2*L,(A-7+2)*L,3*L,3*L);const Q=(null===(f=null===(g=this.options.components)||void 0===g?void 0:g.timing)||void 0===f?void 0:f.scale)||Z,K=.5*(1-Q);for(let t=0;t<A-8;t+=2)tt._drawDot(I,8+t,6,L,K,Q),tt._drawDot(I,6,8+t,L,K,Q);const X=(null===(p=null===(d=this.options.components)||void 0===d?void 0:d.cornerAlignment)||void 0===p?void 0:p.scale)||Z,J=.5*(1-X);tt._drawAlign(I,H,H,L,J,X,this.options.colorDark,(null===(w=null===(m=this.options.components)||void 0===m?void 0:m.cornerAlignment)||void 0===w?void 0:w.protectors)||!1);const rt=(null===(b=null===(y=this.options.components)||void 0===y?void 0:y.alignment)||void 0===b?void 0:b.scale)||Z,ot=.5*(1-rt);for(let t=0;t<$.length;t++)for(let e=0;e<$.length;e++){const r=$[e],o=$[t];(6!==r||6!==o&&o!==H)&&((6!==o||6!==r&&r!==H)&&(r===H&&o===H||tt._drawAlign(I,r,o,L,ot,rt,this.options.colorDark,(null===(C=null===(v=this.options.components)||void 0===v?void 0:v.alignment)||void 0===C?void 0:C.protectors)||!1)))}if(x&&(I.fillStyle=this.options.backgroundColor,I.fillRect(-T,-T,S,T),I.fillRect(-T,R,S,T),I.fillRect(R,-T,T,S),I.fillRect(-T,-T,T,S)),this.options.logoImage){const t=yield V(this.options.logoImage);let e=this.options.logoScale,r=this.options.logoMargin,o=this.options.logoCornerRadius;(e<=0||e>=1)&&(e=.2),r<0&&(r=0),o<0&&(o=0);const i=R*e,n=.5*(S-i),s=n;I.restore(),I.fillStyle=this.options.logoBackgroundColor,I.save(),tt._prepareRoundedCornerClip(I,n-r,s-r,i+2*r,i+2*r,o+r),I.clip();const a=I.globalCompositeOperation;I.globalCompositeOperation="destination-out",I.fill(),I.globalCompositeOperation=a,I.restore(),I.save(),tt._prepareRoundedCornerClip(I,n,s,i,i,o),I.clip(),I.drawImage(t,n,s,i,i),I.restore(),I.save(),I.translate(T,T)}if(j){let t,e,r,o,i,n;if(U.forEach((function(s){t||(t=new Y(B,B),t.setDelay(s.delay),t.setRepeat(0));const{width:a,height:l}=s.dims;e||(e=new W(a,l),r=e.getContext("2d"),r.rect(0,0,e.width,e.height),r.fillStyle="#ffffff",r.fill()),o&&n&&a===o.width&&l===o.height||(o=new W(a,l),i=o.getContext("2d"),n=i.createImageData(a,l)),n.data.set(s.patch),i.putImageData(n,0,0),r.drawImage(o.getContext("2d").canvas,s.dims.left,s.dims.top);const h=new W(S,S),c=h.getContext("2d");c.drawImage(e.getContext("2d").canvas,0,0,S,S),c.rect(0,0,S,S),c.fillStyle=k,c.fill(),c.drawImage(M.getContext("2d").canvas,0,0,S,S);const u=new W(B,B),g=u.getContext("2d");g.drawImage(h.getContext("2d").canvas,0,0,B,B),t.addFrame(g.getImageData(0,0,u.width,u.height).data)})),!t)throw new Error("No frames.");if(t.finish(),et(this.canvas)){const e=t.stream().toFlattenUint8Array().reduce(((t,e)=>t+String.fromCharCode(e)),"");return Promise.resolve(`data:image/gif;base64,${window.btoa(e)}`)}return Promise.resolve(Buffer.from(t.stream().toFlattenUint8Array()))}{O.drawImage(M.getContext("2d").canvas,0,0,S,S),I.drawImage(N.getContext("2d").canvas,-T,-T,S,S);const t=new W(B,B);t.getContext("2d").drawImage(M.getContext("2d").canvas,0,0,B,B),this.canvas=t;const e=this.options.gifBackground?"gif":"png";return et(this.canvas)?Promise.resolve(this.canvas.toDataURL(e)):Promise.resolve(this.canvas.toBuffer(e))}}))}}function et(t){try{return t instanceof HTMLElement}catch(e){return"object"==typeof t&&1===t.nodeType&&"object"==typeof t.style&&"object"==typeof t.ownerDocument}}tt.CorrectLevel=R,tt.defaultComponentOptions={data:{scale:.4},timing:{scale:.5,protectors:!1},alignment:{scale:.5,protectors:!1},cornerAlignment:{scale:.5,protectors:!0}},tt.defaultOptions={text:"",size:400,margin:20,colorDark:"#000000",colorLight:"rgba(255, 255, 255, 0.6)",correctLevel:R.M,backgroundImage:void 0,backgroundDimming:"rgba(0,0,0,0)",logoImage:void 0,logoScale:.2,logoMargin:4,logoCornerRadius:8,whiteMargin:!0,components:tt.defaultComponentOptions,autoColor:!0,logoBackgroundColor:"#ffffff",backgroundColor:"#ffffff"};const rt={props:{text:{type:String,required:!0},qid:{type:String},correctLevel:{type:Number,default:1},size:{type:Number,default:200},margin:{type:Number,default:20},colorDark:{type:String,default:"#000000"},colorLight:{type:String,default:"#FFFFFF"},bgSrc:{type:String,default:void 0},background:{type:String,default:"rgba(0,0,0,0)"},backgroundDimming:{type:String,default:"rgba(0,0,0,0)"},logoSrc:{type:String,default:void 0},logoBackgroundColor:{type:String,default:"rgba(255,255,255,1)"},gifBgSrc:{type:String,default:void 0},logoScale:{type:Number,default:.2},logoMargin:{type:Number,default:0},logoCornerRadius:{type:Number,default:8},whiteMargin:{type:[Boolean,String],default:!0},dotScale:{type:Number,default:1},autoColor:{type:[Boolean,String],default:!0},binarize:{type:[Boolean,String],default:!1},binarizeThreshold:{type:Number,default:128},callback:{type:Function,default:function(){}},bindElement:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},components:{default:function(){return{data:{scale:1},timing:{scale:1,protectors:!1},alignment:{scale:1,protectors:!1},cornerAlignment:{scale:1,protectors:!0}}}}},name:"vue-qr",data:()=>({imgUrl:""}),watch:{$props:{deep:!0,handler(){this.main()}}},mounted(){this.main()},methods:{async main(){if(this.gifBgSrc){const e=await(t=this.gifBgSrc,new Promise(((e,r)=>{var o=new XMLHttpRequest;o.responseType="blob",o.onload=function(){var t=new FileReader;t.onloadend=function(){e(t.result)},t.readAsArrayBuffer(o.response)},o.open("GET",t),o.send()}))),r=this.logoSrc;return void this.render(void 0,r,e)}var t;const e=this.bgSrc,r=this.logoSrc;this.render(e,r)},async render(t,e,r){const o=this;new tt({gifBackground:r,text:o.text,size:o.size,margin:o.margin,colorDark:o.colorDark,colorLight:o.colorLight,backgroundColor:o.backgroundColor,backgroundImage:t,backgroundDimming:o.backgroundDimming,logoImage:e,logoScale:o.logoScale,logoBackgroundColor:o.logoBackgroundColor,correctLevel:o.correctLevel,logoMargin:o.logoMargin,logoCornerRadius:o.logoCornerRadius,whiteMargin:a(o.whiteMargin),dotScale:o.dotScale,autoColor:a(o.autoColor),binarize:a(o.binarize),binarizeThreshold:o.binarizeThreshold,components:o.components}).draw().then((t=>{this.imgUrl=t,o.callback&&o.callback(t,o.qid)}))}}},ot=["src"];const it=s(rt,[["render",function(t,e,r,s,a,l){return r.bindElement?(n(),o("img",{key:0,style:{display:"inline-block"},src:a.imgUrl},null,8,ot)):i("",!0)}]]);export{s as _,it as v};
