function e(e,t,r){return r&&function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e,r),e}function t(){return(t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function r(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function n(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)t.indexOf(r=o[n])>=0||(i[r]=e[r]);return i}function i(e){return 1==(null!=(t=e)&&"object"==typeof t&&!1===Array.isArray(t))&&"[object Object]"===Object.prototype.toString.call(e);var t}var o=Object.prototype,u=o.toString,a=o.hasOwnProperty,f=/^\s*function (\w+)/;function c(e){var t,r=null!==(t=null==e?void 0:e.type)&&void 0!==t?t:e;if(r){var n=r.toString().match(f);return n?n[1]:""}return""}var l=function(e){var t,r;return!1!==i(e)&&"function"==typeof(t=e.constructor)&&!1!==i(r=t.prototype)&&!1!==r.hasOwnProperty("isPrototypeOf")},s=function(e){return e},y=function(e,t){return a.call(e,t)},v=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e},p=Array.isArray||function(e){return"[object Array]"===u.call(e)},d=function(e){return"[object Function]"===u.call(e)},h=function(e){return l(e)&&y(e,"_vueTypes_name")},b=function(e){return l(e)&&(y(e,"type")||["_vueTypes_name","validator","default","required"].some((function(t){return y(e,t)})))};function g(e,t){return Object.defineProperty(e.bind(t),"__original",{value:e})}function O(e,t,r){var n,i=!0,o="";n=l(e)?e:{type:e};var u=h(n)?n._vueTypes_name+" - ":"";if(b(n)&&null!==n.type){if(void 0===n.type||!0===n.type)return i;if(!n.required&&void 0===t)return i;p(n.type)?(i=n.type.some((function(e){return!0===O(e,t)})),o=n.type.map((function(e){return c(e)})).join(" or ")):i="Array"===(o=c(n))?p(t):"Object"===o?l(t):"String"===o||"Number"===o||"Boolean"===o||"Function"===o?function(e){if(null==e)return"";var t=e.constructor.toString().match(f);return t?t[1]:""}(t)===o:t instanceof n.type}if(!i)return u+'value "'+t+'" should be of type "'+o+'"';if(y(n,"validator")&&d(n.validator)){var a=s,v=[];if(s=function(e){v.push(e)},i=n.validator(t),s=a,!i){var g=(v.length>1?"* ":"")+v.join("\n* ");return v.length=0,g}}return i}function m(e,t){var r=Object.defineProperties(t,{_vueTypes_name:{value:e,writable:!0},isRequired:{get:function(){return this.required=!0,this}},def:{value:function(e){return void 0!==e||this.default?d(e)||!0===O(this,e)?(this.default=p(e)?function(){return[].concat(e)}:l(e)?function(){return Object.assign({},e)}:e,this):(s(this._vueTypes_name+' - invalid default value: "'+e+'"'),this):this}}}),n=r.validator;return d(n)&&(r.validator=g(n,r)),r}function j(e,t){var r=m(e,t);return Object.defineProperty(r,"validate",{value:function(e){return d(this.validator)&&s(this._vueTypes_name+" - calling .validate() will overwrite the current custom validator function. Validator info:\n"+JSON.stringify(this)),this.validator=g(e,this),this}})}function _(e,t,r){var i,o,u=(i=t,o={},Object.getOwnPropertyNames(i).forEach((function(e){o[e]=Object.getOwnPropertyDescriptor(i,e)})),Object.defineProperties({},o));if(u._vueTypes_name=e,!l(r))return u;var a,f,c=r.validator,s=n(r,["validator"]);if(d(c)){var y=u.validator;y&&(y=null!==(f=(a=y).__original)&&void 0!==f?f:a),u.validator=g(y?function(e){return y.call(this,e)&&c.call(this,e)}:c,u)}return Object.assign(u,s)}function T(e){return e.replace(/^(?!\s*$)/gm,"  ")}var w=function(){function t(){}return t.extend=function(e){var t=this;if(p(e))return e.forEach((function(e){return t.extend(e)})),this;var r=e.name,i=e.validate,o=void 0!==i&&i,u=e.getter,a=void 0!==u&&u,f=n(e,["name","validate","getter"]);if(y(this,r))throw new TypeError('[VueTypes error]: Type "'+r+'" already defined');var c,l=f.type;return h(l)?(delete f.type,Object.defineProperty(this,r,a?{get:function(){return _(r,l,f)}}:{value:function(){var e,t=_(r,l,f);return t.validator&&(t.validator=(e=t.validator).bind.apply(e,[t].concat([].slice.call(arguments)))),t}})):(c=a?{get:function(){var e=Object.assign({},f);return o?j(r,e):m(r,e)},enumerable:!0}:{value:function(){var e,t,n=Object.assign({},f);return e=o?j(r,n):m(r,n),n.validator&&(e.validator=(t=n.validator).bind.apply(t,[e].concat([].slice.call(arguments)))),e},enumerable:!0},Object.defineProperty(this,r,c))},e(t,0,[{key:"any",get:function(){return j("any",{})}},{key:"func",get:function(){return j("function",{type:Function}).def(this.defaults.func)}},{key:"bool",get:function(){return j("boolean",{type:Boolean}).def(this.defaults.bool)}},{key:"string",get:function(){return j("string",{type:String}).def(this.defaults.string)}},{key:"number",get:function(){return j("number",{type:Number}).def(this.defaults.number)}},{key:"array",get:function(){return j("array",{type:Array}).def(this.defaults.array)}},{key:"object",get:function(){return j("object",{type:Object}).def(this.defaults.object)}},{key:"integer",get:function(){return m("integer",{type:Number,validator:function(e){return v(e)}}).def(this.defaults.integer)}},{key:"symbol",get:function(){return m("symbol",{validator:function(e){return"symbol"==typeof e}})}}]),t}();function k(n){var i;return void 0===n&&(n={func:function(){},bool:!0,string:"",number:0,array:function(){return[]},object:function(){return{}},integer:0}),(i=function(i){function o(){return i.apply(this,arguments)||this}return r(o,i),e(o,0,[{key:"sensibleDefaults",get:function(){return t({},this.defaults)},set:function(e){this.defaults=!1!==e?t({},!0!==e?e:n):{}}}]),o}(w)).defaults=t({},n),i}w.defaults={},w.custom=function(e,t){if(void 0===t&&(t="custom validation failed"),"function"!=typeof e)throw new TypeError("[VueTypes error]: You must provide a function as argument");return m(e.name||"<<anonymous function>>",{validator:function(r){var n=e(r);return n||s(this._vueTypes_name+" - "+t),n}})},w.oneOf=function(e){if(!p(e))throw new TypeError("[VueTypes error]: You must provide an array as argument.");var t='oneOf - value should be one of "'+e.join('", "')+'".',r=e.reduce((function(e,t){if(null!=t){var r=t.constructor;-1===e.indexOf(r)&&e.push(r)}return e}),[]);return m("oneOf",{type:r.length>0?r:void 0,validator:function(r){var n=-1!==e.indexOf(r);return n||s(t),n}})},w.instanceOf=function(e){return m("instanceOf",{type:e})},w.oneOfType=function(e){if(!p(e))throw new TypeError("[VueTypes error]: You must provide an array as argument");for(var t=!1,r=[],n=0;n<e.length;n+=1){var i=e[n];if(b(i)){if(h(i)&&"oneOf"===i._vueTypes_name){r=r.concat(i.type);continue}if(d(i.validator)&&(t=!0),!0!==i.type&&i.type){r=r.concat(i.type);continue}}r.push(i)}return r=r.filter((function(e,t){return r.indexOf(e)===t})),m("oneOfType",t?{type:r,validator:function(t){var r=[],n=e.some((function(e){var n=O(h(e)&&"oneOf"===e._vueTypes_name?e.type||null:e,t);return"string"==typeof n&&r.push(n),!0===n}));return n||s("oneOfType - provided value does not match any of the "+r.length+" passed-in validators:\n"+T(r.join("\n"))),n}}:{type:r})},w.arrayOf=function(e){return m("arrayOf",{type:Array,validator:function(t){var r,n=t.every((function(t){return!0===(r=O(e,t))}));return n||s("arrayOf - value validation error:\n"+T(r)),n}})},w.objectOf=function(e){return m("objectOf",{type:Object,validator:function(t){var r,n=Object.keys(t).every((function(n){return!0===(r=O(e,t[n]))}));return n||s("objectOf - value validation error:\n"+T(r)),n}})},w.shape=function(e){var t=Object.keys(e),r=t.filter((function(t){var r;return!!(null===(r=e[t])||void 0===r?void 0:r.required)})),n=m("shape",{type:Object,validator:function(n){var i=this;if(!l(n))return!1;var o=Object.keys(n);if(r.length>0&&r.some((function(e){return-1===o.indexOf(e)}))){var u=r.filter((function(e){return-1===o.indexOf(e)}));return s(1===u.length?'shape - required property "'+u[0]+'" is not defined.':'shape - required properties "'+u.join('", "')+'" are not defined.'),!1}return o.every((function(r){if(-1===t.indexOf(r))return!0===i._vueTypes_isLoose||(s('shape - shape definition does not include a "'+r+'" property. Allowed keys: "'+t.join('", "')+'".'),!1);var o=O(e[r],n[r]);return"string"==typeof o&&s('shape - "'+r+'" property validation error:\n '+T(o)),!0===o}))}});return Object.defineProperty(n,"_vueTypes_isLoose",{writable:!0,value:!1}),Object.defineProperty(n,"loose",{get:function(){return this._vueTypes_isLoose=!0,this}}),n},w.utils={validate:function(e,t){return!0===O(t,e)},toType:function(e,t,r){return void 0===r&&(r=!1),r?j(e,t):m(e,t)}},function(e){function t(){return e.apply(this,arguments)||this}r(t,e)}(k());export{k as z};
