import{a as e}from"./@vue-HScy-mz9.js";var t={expireTimes:"1d",path:"; path=/",domain:"",secure:!1,sameSite:"; SameSite=Lax"},n=function(){function e(){this.current_default_config=t}return e.prototype.config=function(e){for(var n in this.current_default_config)this.current_default_config[n]=e[n]?e[n]:t[n]},e.prototype.get=function(e){var t=decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null;if(t&&"{"===t.substring(0,1)&&"}"===t.substring(t.length-1,t.length))try{t=JSON.parse(t)}catch(n){return t}return t},e.prototype.set=function(e,t,n,r,i,o,a){if(!e)throw new Error("<PERSON>ie name is not found in the first argument.");if(/^(?:expires|max-age|path|domain|secure|SameSite)$/i.test(e))throw new Error('Cookie name illegality. Cannot be set to ["expires","max-age","path","domain","secure","SameSite"]\t current key name: '+e);t&&t.constructor===Object&&(t=JSON.stringify(t));var c="";if(null==n&&(n=this.current_default_config.expireTimes?this.current_default_config.expireTimes:""),n&&0!=n)switch(n.constructor){case Number:c=Infinity===n||-1===n?"; expires=Fri, 31 Dec 9999 23:59:59 GMT":"; max-age="+n;break;case String:if(/^(?:\d+(y|m|d|h|min|s))$/i.test(n)){var s=n.replace(/^(\d+)(?:y|m|d|h|min|s)$/i,"$1");switch(n.replace(/^(?:\d+)(y|m|d|h|min|s)$/i,"$1").toLowerCase()){case"m":c="; max-age="+2592e3*+s;break;case"d":c="; max-age="+86400*+s;break;case"h":c="; max-age="+3600*+s;break;case"min":c="; max-age="+60*+s;break;case"s":c="; max-age="+s;break;case"y":c="; max-age="+31104e3*+s}}else c="; expires="+n;break;case Date:c="; expires="+n.toUTCString()}return document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+c+(i?"; domain="+i:this.current_default_config.domain?this.current_default_config.domain:"")+(r?"; path="+r:this.current_default_config.path?this.current_default_config.path:"; path=/")+(null==o?this.current_default_config.secure?"; Secure":"":o?"; Secure":"")+(null==a?this.current_default_config.sameSite?"; SameSute="+this.current_default_config.sameSite:"":a?"; SameSite="+a:""),this},e.prototype.remove=function(e,t,n){return!(!e||!this.isKey(e))&&(document.cookie=encodeURIComponent(e)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT"+(n?"; domain="+n:this.current_default_config.domain?this.current_default_config.domain:"")+(t?"; path="+t:this.current_default_config.path?this.current_default_config.path:"; path=/")+"; SameSite=Lax",!0)},e.prototype.isKey=function(e){return new RegExp("(?:^|;\\s*)"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=").test(document.cookie)},e.prototype.keys=function(){if(!document.cookie)return[];for(var e=document.cookie.replace(/((?:^|\s*;)[^\=]+)(?=;|$)|^\s*|\s*(?:\=[^;]*)?(?:\1|$)/g,"").split(/\s*(?:\=[^;]*)?;\s*/),t=0;t<e.length;t++)e[t]=decodeURIComponent(e[t]);return e},e}(),r=null;function i(e){null==r&&(r=new n),r.config(e)}function o(){return null==r&&(r=new n),{cookies:e(r)}}export{i as g,o as u};
