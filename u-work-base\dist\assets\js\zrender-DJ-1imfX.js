import{_ as t}from"./tslib-DITk-L2-.js";var e=function(){return function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}}(),r=new(function(){return function(){this.browser=new e,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window}}());"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(r.wxa=!0,r.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?r.worker=!0:!r.hasGlobalWindow||"Deno"in window?(r.node=!0,r.svgSupported=!0):function(t,e){var r=e.browser,i=t.match(/Firefox\/([\d.]+)/),n=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(r.firefox=!0,r.version=i[1]);n&&(r.ie=!0,r.version=n[1]);o&&(r.edge=!0,r.version=o[1],r.newEdge=+o[1].split(".")[0]>18);a&&(r.weChat=!0);e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!r.ie&&!r.edge,e.pointerEventsSupported="onpointerdown"in window&&(r.edge||r.ie&&+r.version>=11),e.domSupported="undefined"!=typeof document;var s=document.documentElement.style;e.transform3dSupported=(r.ie&&"transition"in s||r.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||r.ie&&+r.version>=9}(navigator.userAgent,r);var i="sans-serif",n="12px "+i;var o=function(t){var e={};if("undefined"==typeof JSON)return e;for(var r=0;r<t.length;r++){var i=String.fromCharCode(r+32),n=(t.charCodeAt(r)-20)/100;e[i]=n}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),a={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(){var t,e;return function(r,i){if(!t){var s=a.createCanvas();t=s&&s.getContext("2d")}if(t)return e!==i&&(e=t.font=i||n),t.measureText(r);r=r||"";var h=/((?:\d+)?\.?\d*)px/.exec(i=i||n),l=h&&+h[1]||12,u=0;if(i.indexOf("mono")>=0)u=l*r.length;else for(var f=0;f<r.length;f++){var c=o[r[f]];u+=null==c?l:c*l}return{width:u}}}(),loadImage:function(t,e,r){var i=new Image;return i.onload=e,i.onerror=r,i.src=t,i}};function s(t){for(var e in a)t[e]&&(a[e]=t[e])}var h=O(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],(function(t,e){return t["[object "+e+"]"]=!0,t}),{}),l=O(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],(function(t,e){return t["[object "+e+"Array]"]=!0,t}),{}),u=Object.prototype.toString,f=Array.prototype,c=f.forEach,p=f.filter,d=f.slice,v=f.map,y=function(){}.constructor,g=y?y.prototype:null,_="__proto__",m=2311;function x(){return m++}function w(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]}function b(t){if(null==t||"object"!=typeof t)return t;var e=t,r=u.call(t);if("[object Array]"===r){if(!ht(t)){e=[];for(var i=0,n=t.length;i<n;i++)e[i]=b(t[i])}}else if(l[r]){if(!ht(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,n=t.length;i<n;i++)e[i]=t[i]}}}else if(!h[r]&&!ht(t)&&!G(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==_&&(e[a]=b(t[a]));return e}function k(t,e,r){if(!Y(e)||!Y(t))return r?b(e):t;for(var i in e)if(e.hasOwnProperty(i)&&i!==_){var n=t[i],o=e[i];!Y(o)||!Y(n)||E(o)||E(n)||G(o)||G(n)||V(o)||V(n)||ht(o)||ht(n)?!r&&i in t||(t[i]=b(e[i])):k(n,o,r)}return t}function S(t,e){for(var r=t[0],i=1,n=t.length;i<n;i++)r=k(r,t[i],e);return r}function T(t,e){if(Object.assign)Object.assign(t,e);else for(var r in e)e.hasOwnProperty(r)&&r!==_&&(t[r]=e[r]);return t}function C(t,e,r){for(var i=B(e),n=0,o=i.length;n<o;n++){var a=i[n];(r?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}var P=a.createCanvas;function M(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var r=0,i=t.length;r<i;r++)if(t[r]===e)return r}return-1}function A(t,e){var r=t.prototype;function i(){}for(var n in i.prototype=e.prototype,t.prototype=new i,r)r.hasOwnProperty(n)&&(t.prototype[n]=r[n]);t.prototype.constructor=t,t.superClass=e}function L(t,e,r){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),n=0;n<i.length;n++){var o=i[n];"constructor"!==o&&(r?null!=e[o]:null==t[o])&&(t[o]=e[o])}else C(t,e,r)}function I(t){return!!t&&("string"!=typeof t&&"number"==typeof t.length)}function D(t,e,r){if(t&&e)if(t.forEach&&t.forEach===c)t.forEach(e,r);else if(t.length===+t.length)for(var i=0,n=t.length;i<n;i++)e.call(r,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(r,t[o],o,t)}function z(t,e,r){if(!t)return[];if(!e)return rt(t);if(t.map&&t.map===v)return t.map(e,r);for(var i=[],n=0,o=t.length;n<o;n++)i.push(e.call(r,t[n],n,t));return i}function O(t,e,r,i){if(t&&e){for(var n=0,o=t.length;n<o;n++)r=e.call(i,r,t[n],n,t);return r}}function R(t,e,r){if(!t)return[];if(!e)return rt(t);if(t.filter&&t.filter===p)return t.filter(e,r);for(var i=[],n=0,o=t.length;n<o;n++)e.call(r,t[n],n,t)&&i.push(t[n]);return i}function F(t,e,r){if(t&&e)for(var i=0,n=t.length;i<n;i++)if(e.call(r,t[i],i,t))return t[i]}function B(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var r in t)t.hasOwnProperty(r)&&e.push(r);return e}var N=g&&W(g.bind)?g.call.bind(g.bind):function(t,e){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return function(){return t.apply(e,r.concat(d.call(arguments)))}};function H(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return function(){return t.apply(this,e.concat(d.call(arguments)))}}function E(t){return Array.isArray?Array.isArray(t):"[object Array]"===u.call(t)}function W(t){return"function"==typeof t}function X(t){return"string"==typeof t}function j(t){return"[object String]"===u.call(t)}function q(t){return"number"==typeof t}function Y(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function V(t){return!!h[u.call(t)]}function U(t){return!!l[u.call(t)]}function G(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function Z(t){return null!=t.colorStops}function K(t){return null!=t.image}function Q(t){return"[object RegExp]"===u.call(t)}function $(t){return t!=t}function J(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0,i=t.length;r<i;r++)if(null!=t[r])return t[r]}function tt(t,e){return null!=t?t:e}function et(t,e,r){return null!=t?t:null!=e?e:r}function rt(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return d.apply(t,e)}function it(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function nt(t,e){if(!t)throw new Error(e)}function ot(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var at="__ec_primitive__";function st(t){t[at]=!0}function ht(t){return t[at]}var lt=function(){function t(){this.data={}}return t.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},t.prototype.has=function(t){return this.data.hasOwnProperty(t)},t.prototype.get=function(t){return this.data[t]},t.prototype.set=function(t,e){return this.data[t]=e,this},t.prototype.keys=function(){return B(this.data)},t.prototype.forEach=function(t){var e=this.data;for(var r in e)e.hasOwnProperty(r)&&t(e[r],r)},t}(),ut="function"==typeof Map;var ft=function(){function t(e){var r=E(e);this.data=ut?new Map:new lt;var i=this;function n(t,e){r?i.set(t,e):i.set(e,t)}e instanceof t?e.each(n):e&&D(e,n)}return t.prototype.hasKey=function(t){return this.data.has(t)},t.prototype.get=function(t){return this.data.get(t)},t.prototype.set=function(t,e){return this.data.set(t,e),e},t.prototype.each=function(t,e){this.data.forEach((function(r,i){t.call(e,r,i)}))},t.prototype.keys=function(){var t=this.data.keys();return ut?Array.from(t):t},t.prototype.removeKey=function(t){this.data.delete(t)},t}();function ct(t){return new ft(t)}function pt(t,e){for(var r=new t.constructor(t.length+e.length),i=0;i<t.length;i++)r[i]=t[i];var n=t.length;for(i=0;i<e.length;i++)r[i+n]=e[i];return r}function dt(t,e){var r;if(Object.create)r=Object.create(t);else{var i=function(){};i.prototype=t,r=new i}return e&&T(r,e),r}function vt(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function yt(t,e){return t.hasOwnProperty(e)}function gt(){}var _t=180/Math.PI;const mt=Object.freeze(Object.defineProperty({__proto__:null,HashMap:ft,RADIAN_TO_DEGREE:_t,assert:nt,bind:N,clone:b,concatArray:pt,createCanvas:P,createHashMap:ct,createObject:dt,curry:H,defaults:C,disableUserSelect:vt,each:D,eqNaN:$,extend:T,filter:R,find:F,guid:x,hasOwn:yt,indexOf:M,inherits:A,isArray:E,isArrayLike:I,isBuiltInObject:V,isDom:G,isFunction:W,isGradientObject:Z,isImagePatternObject:K,isNumber:q,isObject:Y,isPrimitive:ht,isRegExp:Q,isString:X,isStringSafe:j,isTypedArray:U,keys:B,logError:w,map:z,merge:k,mergeAll:S,mixin:L,noop:gt,normalizeCssArray:it,reduce:O,retrieve:J,retrieve2:tt,retrieve3:et,setAsPrimitive:st,slice:rt,trim:ot},Symbol.toStringTag,{value:"Module"}));function xt(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function wt(t,e){return t[0]=e[0],t[1]=e[1],t}function bt(t){return[t[0],t[1]]}function kt(t,e,r){return t[0]=e,t[1]=r,t}function St(t,e,r){return t[0]=e[0]+r[0],t[1]=e[1]+r[1],t}function Tt(t,e,r,i){return t[0]=e[0]+r[0]*i,t[1]=e[1]+r[1]*i,t}function Ct(t,e,r){return t[0]=e[0]-r[0],t[1]=e[1]-r[1],t}function Pt(t){return Math.sqrt(At(t))}var Mt=Pt;function At(t){return t[0]*t[0]+t[1]*t[1]}var Lt=At;function It(t,e,r){return t[0]=e[0]*r,t[1]=e[1]*r,t}function Dt(t,e){var r=Pt(e);return 0===r?(t[0]=0,t[1]=0):(t[0]=e[0]/r,t[1]=e[1]/r),t}function zt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Ot=zt;function Rt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Ft=Rt;function Bt(t,e,r,i){return t[0]=e[0]+i*(r[0]-e[0]),t[1]=e[1]+i*(r[1]-e[1]),t}function Nt(t,e,r){var i=e[0],n=e[1];return t[0]=r[0]*i+r[2]*n+r[4],t[1]=r[1]*i+r[3]*n+r[5],t}function Ht(t,e,r){return t[0]=Math.min(e[0],r[0]),t[1]=Math.min(e[1],r[1]),t}function Et(t,e,r){return t[0]=Math.max(e[0],r[0]),t[1]=Math.max(e[1],r[1]),t}const Wt=Object.freeze(Object.defineProperty({__proto__:null,add:St,applyTransform:Nt,clone:bt,copy:wt,create:xt,dist:Ot,distSquare:Ft,distance:zt,distanceSquare:Rt,div:function(t,e,r){return t[0]=e[0]/r[0],t[1]=e[1]/r[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:Pt,lenSquare:At,length:Mt,lengthSquare:Lt,lerp:Bt,max:Et,min:Ht,mul:function(t,e,r){return t[0]=e[0]*r[0],t[1]=e[1]*r[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:Dt,scale:It,scaleAndAdd:Tt,set:kt,sub:Ct},Symbol.toStringTag,{value:"Module"}));var Xt=function(){return function(t,e){this.target=t,this.topTarget=e&&e.topTarget}}(),jt=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Xt(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var r=t.offsetX,i=t.offsetY,n=r-this._x,o=i-this._y;this._x=r,this._y=i,e.drift(n,o,t),this.handler.dispatchToElement(new Xt(e,t),"drag",t.event);var a=this.handler.findHover(r,i,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.handler.dispatchToElement(new Xt(s,t),"dragleave",t.event),a&&a!==s&&this.handler.dispatchToElement(new Xt(a,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Xt(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Xt(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}(),qt=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,r,i){this._$handlers||(this._$handlers={});var n=this._$handlers;if("function"==typeof e&&(i=r,r=e,e=null),!r||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),n[t]||(n[t]=[]);for(var a=0;a<n[t].length;a++)if(n[t][a].h===r)return this;var s={h:r,query:e,ctx:i||this,callAtLast:r.zrEventfulCallAtLast},h=n[t].length-1,l=n[t][h];return l&&l.callAtLast?n[t].splice(h,0,s):n[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var r=this._$handlers;if(!r)return this;if(!t)return this._$handlers={},this;if(e){if(r[t]){for(var i=[],n=0,o=r[t].length;n<o;n++)r[t][n].h!==e&&i.push(r[t][n]);r[t]=i}r[t]&&0===r[t].length&&delete r[t]}else delete r[t];return this},t.prototype.trigger=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var h=i[s];if(!n||!n.filter||null==h.query||n.filter(t,h.query))switch(o){case 0:h.h.call(h.ctx);break;case 1:h.h.call(h.ctx,e[0]);break;case 2:h.h.call(h.ctx,e[0],e[1]);break;default:h.h.apply(h.ctx,e)}}return n&&n.afterTrigger&&n.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(!this._$handlers)return this;var i=this._$handlers[t],n=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,h=0;h<s;h++){var l=i[h];if(!n||!n.filter||null==l.query||n.filter(t,l.query))switch(o){case 0:l.h.call(a);break;case 1:l.h.call(a,e[0]);break;case 2:l.h.call(a,e[0],e[1]);break;default:l.h.apply(a,e.slice(1,o-1))}}return n&&n.afterTrigger&&n.afterTrigger(t),this},t}(),Yt=Math.log(2);function Vt(t,e,r,i,n,o){var a=i+"-"+n,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var h=Math.round(Math.log((1<<s)-1&~n)/Yt);return t[r][h]}for(var l=i|1<<r,u=r+1;i&1<<u;)u++;for(var f=0,c=0,p=0;c<s;c++){var d=1<<c;d&n||(f+=(p%2?-1:1)*t[r][c]*Vt(t,e-1,u,l,n|d,o),p++)}return o[a]=f,f}function Ut(t,e){var r=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},n=Vt(r,8,0,0,0,i);if(0!==n){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*Vt(r,7,0===a?1:0,1<<a,1<<s,i)/n*e[a];return function(t,e,r){var i=e*o[6]+r*o[7]+1;t[0]=(e*o[0]+r*o[1]+o[2])/i,t[1]=(e*o[3]+r*o[4]+o[5])/i}}}var Gt="___zrEVENTSAVED",Zt=[];function Kt(t,e,r,i,n){return Qt(Zt,e,i,n,!0)&&Qt(t,r,Zt[0],Zt[1])}function Qt(t,e,i,n,o){if(e.getBoundingClientRect&&r.domSupported&&!$t(e)){var a=e[Gt]||(e[Gt]={}),s=function(t,e){var r=e.markers;if(r)return r;r=e.markers=[];for(var i=["left","right"],n=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=o%2,h=(o>>1)%2;a.style.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[s]+":0",n[h]+":0",i[1-s]+":auto",n[1-h]+":auto",""].join("!important;"),t.appendChild(a),r.push(a)}return r}(e,a),h=function(t,e,r){for(var i=r?"invTrans":"trans",n=e[i],o=e.srcCoords,a=[],s=[],h=!0,l=0;l<4;l++){var u=t[l].getBoundingClientRect(),f=2*l,c=u.left,p=u.top;a.push(c,p),h=h&&o&&c===o[f]&&p===o[f+1],s.push(t[l].offsetLeft,t[l].offsetTop)}return h&&n?n:(e.srcCoords=a,e[i]=r?Ut(s,a):Ut(a,s))}(s,a,o);if(h)return h(t,i,n),!0}return!1}function $t(t){return"CANVAS"===t.nodeName.toUpperCase()}var Jt=/([&<>"'])/g,te={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function ee(t){return null==t?"":(t+"").replace(Jt,(function(t,e){return te[e]}))}var re=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ie=[],ne=r.browser.firefox&&+r.browser.version.split(".")[0]<39;function oe(t,e,r,i){return r=r||{},i?ae(t,e,r):ne&&null!=e.layerX&&e.layerX!==e.offsetX?(r.zrX=e.layerX,r.zrY=e.layerY):null!=e.offsetX?(r.zrX=e.offsetX,r.zrY=e.offsetY):ae(t,e,r),r}function ae(t,e,i){if(r.domSupported&&t.getBoundingClientRect){var n=e.clientX,o=e.clientY;if($t(t)){var a=t.getBoundingClientRect();return i.zrX=n-a.left,void(i.zrY=o-a.top)}if(Qt(ie,t,n,o))return i.zrX=ie[0],void(i.zrY=ie[1])}i.zrX=i.zrY=0}function se(t){return t||window.event}function he(t,e,r){if(null!=(e=se(e)).zrX)return e;var i=e.type;if(i&&i.indexOf("touch")>=0){var n="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];n&&oe(t,n,e,r)}else{oe(t,e,e,r);var o=function(t){var e=t.wheelDelta;if(e)return e;var r=t.deltaX,i=t.deltaY;if(null==r||null==i)return e;return 3*(0!==i?Math.abs(i):Math.abs(r))*(i>0?-1:i<0?1:r>0?-1:1)}(e);e.zrDelta=o?o/120:-(e.detail||0)/3}var a=e.button;return null==e.which&&void 0!==a&&re.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}function le(t,e,r,i){t.addEventListener(e,r,i)}var ue=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0};function fe(t){return 2===t.which||3===t.which}var ce=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,r){return this._doTrack(t,e,r),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,r){var i=t.touches;if(i){for(var n={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],h=oe(r,s,{});n.points.push([h.zrX,h.zrY]),n.touches.push(s)}this._track.push(n)}},t.prototype._recognize=function(t){for(var e in de)if(de.hasOwnProperty(e)){var r=de[e](this._track,t);if(r)return r}},t}();function pe(t){var e=t[1][0]-t[0][0],r=t[1][1]-t[0][1];return Math.sqrt(e*e+r*r)}var de={pinch:function(t,e){var r=t.length;if(r){var i,n=(t[r-1]||{}).points,o=(t[r-2]||{}).points||n;if(o&&o.length>1&&n&&n.length>1){var a=pe(n)/pe(o);!isFinite(a)&&(a=1),e.pinchScale=a;var s=[((i=n)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}};function ve(){return[1,0,0,1,0,0]}function ye(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ge(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function _e(t,e,r){var i=e[0]*r[0]+e[2]*r[1],n=e[1]*r[0]+e[3]*r[1],o=e[0]*r[2]+e[2]*r[3],a=e[1]*r[2]+e[3]*r[3],s=e[0]*r[4]+e[2]*r[5]+e[4],h=e[1]*r[4]+e[3]*r[5]+e[5];return t[0]=i,t[1]=n,t[2]=o,t[3]=a,t[4]=s,t[5]=h,t}function me(t,e,r){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+r[0],t[5]=e[5]+r[1],t}function xe(t,e,r,i){void 0===i&&(i=[0,0]);var n=e[0],o=e[2],a=e[4],s=e[1],h=e[3],l=e[5],u=Math.sin(r),f=Math.cos(r);return t[0]=n*f+s*u,t[1]=-n*u+s*f,t[2]=o*f+h*u,t[3]=-o*u+f*h,t[4]=f*(a-i[0])+u*(l-i[1])+i[0],t[5]=f*(l-i[1])-u*(a-i[0])+i[1],t}function we(t,e,r){var i=r[0],n=r[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function be(t,e){var r=e[0],i=e[2],n=e[4],o=e[1],a=e[3],s=e[5],h=r*a-o*i;return h?(h=1/h,t[0]=a*h,t[1]=-o*h,t[2]=-i*h,t[3]=r*h,t[4]=(i*s-a*n)*h,t[5]=(o*n-r*s)*h,t):null}function ke(t){var e=[1,0,0,1,0,0];return ge(e,t),e}const Se=Object.freeze(Object.defineProperty({__proto__:null,clone:ke,copy:ge,create:ve,identity:ye,invert:be,mul:_e,rotate:xe,scale:we,translate:me},Symbol.toStringTag,{value:"Module"}));var Te=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,r=this.y-t.y;return Math.sqrt(e*e+r*r)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,r=this.y-t.y;return e*e+r*r},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,r=this.y;return this.x=t[0]*e+t[2]*r+t[4],this.y=t[1]*e+t[3]*r+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,r){t.x=e,t.y=r},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,r){t.x=e.x+r.x,t.y=e.y+r.y},t.sub=function(t,e,r){t.x=e.x-r.x,t.y=e.y-r.y},t.scale=function(t,e,r){t.x=e.x*r,t.y=e.y*r},t.scaleAndAdd=function(t,e,r,i){t.x=e.x+r.x*i,t.y=e.y+r.y*i},t.lerp=function(t,e,r,i){var n=1-i;t.x=n*e.x+i*r.x,t.y=n*e.y+i*r.y},t}(),Ce=Math.min,Pe=Math.max,Me=new Te,Ae=new Te,Le=new Te,Ie=new Te,De=new Te,ze=new Te,Oe=function(){function t(t,e,r,i){r<0&&(t+=r,r=-r),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=r,this.height=i}return t.prototype.union=function(t){var e=Ce(t.x,this.x),r=Ce(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Pe(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Pe(t.y+t.height,this.y+this.height)-r:this.height=t.height,this.x=e,this.y=r},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,r=t.width/e.width,i=t.height/e.height,n=[1,0,0,1,0,0];return me(n,n,[-e.x,-e.y]),we(n,n,[r,i]),me(n,n,[t.x,t.y]),n},t.prototype.intersect=function(e,r){if(!e)return!1;e instanceof t||(e=t.create(e));var i=this,n=i.x,o=i.x+i.width,a=i.y,s=i.y+i.height,h=e.x,l=e.x+e.width,u=e.y,f=e.y+e.height,c=!(o<h||l<n||s<u||f<a);if(r){var p=Infinity,d=0,v=Math.abs(o-h),y=Math.abs(l-n),g=Math.abs(s-u),_=Math.abs(f-a),m=Math.min(v,y),x=Math.min(g,_);o<h||l<n?m>d&&(d=m,v<y?Te.set(ze,-v,0):Te.set(ze,y,0)):m<p&&(p=m,v<y?Te.set(De,v,0):Te.set(De,-y,0)),s<u||f<a?x>d&&(d=x,g<_?Te.set(ze,0,-g):Te.set(ze,0,_)):m<p&&(p=m,g<_?Te.set(De,0,g):Te.set(De,0,-_))}return r&&Te.copy(r,c?De:ze),c},t.prototype.contain=function(t,e){var r=this;return t>=r.x&&t<=r.x+r.width&&e>=r.y&&e<=r.y+r.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,r,i){if(i){if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],o=i[3],a=i[4],s=i[5];return e.x=r.x*n+a,e.y=r.y*o+s,e.width=r.width*n,e.height=r.height*o,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}Me.x=Le.x=r.x,Me.y=Ie.y=r.y,Ae.x=Ie.x=r.x+r.width,Ae.y=Le.y=r.y+r.height,Me.transform(i),Ie.transform(i),Ae.transform(i),Le.transform(i),e.x=Ce(Me.x,Ae.x,Le.x,Ie.x),e.y=Ce(Me.y,Ae.y,Le.y,Ie.y);var h=Pe(Me.x,Ae.x,Le.x,Ie.x),l=Pe(Me.y,Ae.y,Le.y,Ie.y);e.width=h-e.x,e.height=l-e.y}else e!==r&&t.copy(e,r)},t}(),Re="silent";function Fe(){ue(this.event)}var Be=function(e){function r(){var t=null!==e&&e.apply(this,arguments)||this;return t.handler=null,t}return t(r,e),r.prototype.dispose=function(){},r.prototype.setCursor=function(){},r}(qt),Ne=function(){return function(t,e){this.x=t,this.y=e}}(),He=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Ee=new Oe(0,0,0,0),We=function(e){function r(t,r,i,n,o){var a=e.call(this)||this;return a._hovered=new Ne(0,0),a.storage=t,a.painter=r,a.painterRoot=n,a._pointerSize=o,i=i||new Be,a.proxy=null,a.setHandlerProxy(i),a._draggingMgr=new jt(a),a}return t(r,e),r.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(D(He,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},r.prototype.mousemove=function(t){var e=t.zrX,r=t.zrY,i=qe(this,e,r),n=this._hovered,o=n.target;o&&!o.__zr&&(o=(n=this.findHover(n.x,n.y)).target);var a=this._hovered=i?new Ne(e,r):this.findHover(e,r),s=a.target,h=this.proxy;h.setCursor&&h.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(n,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},r.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},r.prototype.resize=function(){this._hovered=new Ne(0,0)},r.prototype.dispatch=function(t,e){var r=this[t];r&&r.call(this,e)},r.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},r.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},r.prototype.dispatchToElement=function(t,e,r){var i=(t=t||{}).target;if(!i||!i.silent){for(var n="on"+e,o=function(t,e,r){return{type:t,event:r,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:r.zrX,offsetY:r.zrY,gestureEvent:r.gestureEvent,pinchX:r.pinchX,pinchY:r.pinchY,pinchScale:r.pinchScale,wheelDelta:r.zrDelta,zrByTouch:r.zrByTouch,which:r.which,stop:Fe}}(e,t,r);i&&(i[n]&&(o.cancelBubble=!!i[n].call(i,o)),i.trigger(e,o),i=i.__hostTarget?i.__hostTarget:i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){"function"==typeof t[n]&&t[n].call(t,o),t.trigger&&t.trigger(e,o)})))}},r.prototype.findHover=function(t,e,r){var i=this.storage.getDisplayList(),n=new Ne(t,e);if(je(i,n,t,e,r),this._pointerSize&&!n.target){for(var o=[],a=this._pointerSize,s=a/2,h=new Oe(t-s,e-s,a,a),l=i.length-1;l>=0;l--){var u=i[l];u===r||u.ignore||u.ignoreCoarsePointer||u.parent&&u.parent.ignoreCoarsePointer||(Ee.copy(u.getBoundingRect()),u.transform&&Ee.applyTransform(u.transform),Ee.intersect(h)&&o.push(u))}if(o.length)for(var f=Math.PI/12,c=2*Math.PI,p=0;p<s;p+=4)for(var d=0;d<c;d+=f){if(je(o,n,t+p*Math.cos(d),e+p*Math.sin(d),r),n.target)return n}}return n},r.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new ce);var r=this._gestureMgr;"start"===e&&r.clear();var i=r.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&r.clear(),i){var n=i.type;t.gestureEvent=n;var o=new Ne;o.target=i.target,this.dispatchToElement(o,n,i.event)}},r}(qt);function Xe(t,e,r){if(t[t.rectHover?"rectContain":"contain"](e,r)){for(var i=t,n=void 0,o=!1;i;){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,r))return!1}i.silent&&(n=!0);var s=i.__hostTarget;i=s||i.parent}return!n||Re}return!1}function je(t,e,r,i,n){for(var o=t.length-1;o>=0;o--){var a=t[o],s=void 0;if(a!==n&&!a.ignore&&(s=Xe(a,r,i))&&(!e.topTarget&&(e.topTarget=a),s!==Re)){e.target=a;break}}}function qe(t,e,r){var i=t.painter;return e<0||e>i.getWidth()||r<0||r>i.getHeight()}D(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){We.prototype[t]=function(e){var r,i,n=e.zrX,o=e.zrY,a=qe(this,n,o);if("mouseup"===t&&a||(i=(r=this.findHover(n,o)).target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||Ot(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(r,t,e)}}));function Ye(t,e,r,i){var n=e+1;if(n===r)return 1;if(i(t[n++],t[e])<0){for(;n<r&&i(t[n],t[n-1])<0;)n++;!function(t,e,r){r--;for(;e<r;){var i=t[e];t[e++]=t[r],t[r--]=i}}(t,e,n)}else for(;n<r&&i(t[n],t[n-1])>=0;)n++;return n-e}function Ve(t,e,r,i,n){for(i===e&&i++;i<r;i++){for(var o,a=t[i],s=e,h=i;s<h;)n(a,t[o=s+h>>>1])<0?h=o:s=o+1;var l=i-s;switch(l){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;l>0;)t[s+l]=t[s+l-1],l--}t[s]=a}}function Ue(t,e,r,i,n,o){var a=0,s=0,h=1;if(o(t,e[r+n])>0){for(s=i-n;h<s&&o(t,e[r+n+h])>0;)a=h,(h=1+(h<<1))<=0&&(h=s);h>s&&(h=s),a+=n,h+=n}else{for(s=n+1;h<s&&o(t,e[r+n-h])<=0;)a=h,(h=1+(h<<1))<=0&&(h=s);h>s&&(h=s);var l=a;a=n-h,h=n-l}for(a++;a<h;){var u=a+(h-a>>>1);o(t,e[r+u])>0?a=u+1:h=u}return h}function Ge(t,e,r,i,n,o){var a=0,s=0,h=1;if(o(t,e[r+n])<0){for(s=n+1;h<s&&o(t,e[r+n-h])<0;)a=h,(h=1+(h<<1))<=0&&(h=s);h>s&&(h=s);var l=a;a=n-h,h=n-l}else{for(s=i-n;h<s&&o(t,e[r+n+h])>=0;)a=h,(h=1+(h<<1))<=0&&(h=s);h>s&&(h=s),a+=n,h+=n}for(a++;a<h;){var u=a+(h-a>>>1);o(t,e[r+u])<0?h=u:a=u+1}return h}function Ze(t,e){var r,i,n=7,o=0,a=[];function s(s){var h=r[s],l=i[s],u=r[s+1],f=i[s+1];i[s]=l+f,s===o-3&&(r[s+1]=r[s+2],i[s+1]=i[s+2]),o--;var c=Ge(t[u],t,h,l,0,e);h+=c,0!==(l-=c)&&0!==(f=Ue(t[h+l-1],t,u,f,f-1,e))&&(l<=f?function(r,i,o,s){var h=0;for(h=0;h<i;h++)a[h]=t[r+h];var l=0,u=o,f=r;if(t[f++]=t[u++],0==--s){for(h=0;h<i;h++)t[f+h]=a[l+h];return}if(1===i){for(h=0;h<s;h++)t[f+h]=t[u+h];return void(t[f+s]=a[l])}var c,p,d,v=n;for(;;){c=0,p=0,d=!1;do{if(e(t[u],a[l])<0){if(t[f++]=t[u++],p++,c=0,0==--s){d=!0;break}}else if(t[f++]=a[l++],c++,p=0,1==--i){d=!0;break}}while((c|p)<v);if(d)break;do{if(0!==(c=Ge(t[u],a,l,i,0,e))){for(h=0;h<c;h++)t[f+h]=a[l+h];if(f+=c,l+=c,(i-=c)<=1){d=!0;break}}if(t[f++]=t[u++],0==--s){d=!0;break}if(0!==(p=Ue(a[l],t,u,s,0,e))){for(h=0;h<p;h++)t[f+h]=t[u+h];if(f+=p,u+=p,0===(s-=p)){d=!0;break}}if(t[f++]=a[l++],1==--i){d=!0;break}v--}while(c>=7||p>=7);if(d)break;v<0&&(v=0),v+=2}if((n=v)<1&&(n=1),1===i){for(h=0;h<s;h++)t[f+h]=t[u+h];t[f+s]=a[l]}else{if(0===i)throw new Error;for(h=0;h<i;h++)t[f+h]=a[l+h]}}(h,l,u,f):function(r,i,o,s){var h=0;for(h=0;h<s;h++)a[h]=t[o+h];var l=r+i-1,u=s-1,f=o+s-1,c=0,p=0;if(t[f--]=t[l--],0==--i){for(c=f-(s-1),h=0;h<s;h++)t[c+h]=a[h];return}if(1===s){for(p=(f-=i)+1,c=(l-=i)+1,h=i-1;h>=0;h--)t[p+h]=t[c+h];return void(t[f]=a[u])}var d=n;for(;;){var v=0,y=0,g=!1;do{if(e(a[u],t[l])<0){if(t[f--]=t[l--],v++,y=0,0==--i){g=!0;break}}else if(t[f--]=a[u--],y++,v=0,1==--s){g=!0;break}}while((v|y)<d);if(g)break;do{if(0!==(v=i-Ge(a[u],t,r,i,i-1,e))){for(i-=v,p=(f-=v)+1,c=(l-=v)+1,h=v-1;h>=0;h--)t[p+h]=t[c+h];if(0===i){g=!0;break}}if(t[f--]=a[u--],1==--s){g=!0;break}if(0!==(y=s-Ue(t[l],a,0,s,s-1,e))){for(s-=y,p=(f-=y)+1,c=(u-=y)+1,h=0;h<y;h++)t[p+h]=a[c+h];if(s<=1){g=!0;break}}if(t[f--]=t[l--],0==--i){g=!0;break}d--}while(v>=7||y>=7);if(g)break;d<0&&(d=0),d+=2}(n=d)<1&&(n=1);if(1===s){for(p=(f-=i)+1,c=(l-=i)+1,h=i-1;h>=0;h--)t[p+h]=t[c+h];t[f]=a[u]}else{if(0===s)throw new Error;for(c=f-(s-1),h=0;h<s;h++)t[c+h]=a[h]}}(h,l,u,f))}return r=[],i=[],{mergeRuns:function(){for(;o>1;){var t=o-2;if(t>=1&&i[t-1]<=i[t]+i[t+1]||t>=2&&i[t-2]<=i[t]+i[t-1])i[t-1]<i[t+1]&&t--;else if(i[t]>i[t+1])break;s(t)}},forceMergeRuns:function(){for(;o>1;){var t=o-2;t>0&&i[t-1]<i[t+1]&&t--,s(t)}},pushRun:function(t,e){r[o]=t,i[o]=e,o+=1}}}function Ke(t,e,r,i){r||(r=0),i||(i=t.length);var n=i-r;if(!(n<2)){var o=0;if(n<32)Ve(t,r,i,r+(o=Ye(t,r,i,e)),e);else{var a=Ze(t,e),s=function(t){for(var e=0;t>=32;)e|=1&t,t>>=1;return t+e}(n);do{if((o=Ye(t,r,i,e))<s){var h=n;h>s&&(h=s),Ve(t,r,r+h,r+o,e),o=h}a.pushRun(r,o),a.mergeRuns(),n-=o,r+=o}while(0!==n);a.forceMergeRuns()}}}var Qe=!1;function $e(){Qe||(Qe=!0)}function Je(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var tr,er=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Je}return t.prototype.traverse=function(t,e){for(var r=0;r<this._roots.length;r++)this._roots[r].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var r=this._displayList;return!t&&r.length||this.updateDisplayList(e),r},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,r=this._displayList,i=0,n=e.length;i<n;i++)this._updateAndAddDisplayable(e[i],null,t);r.length=this._displayListLen,Ke(r,Je)},t.prototype._updateAndAddDisplayable=function(t,e,r){if(!t.ignore||r){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var n=i,o=t;n;)n.parent=o,n.updateTransform(),e.push(n),o=n,n=n.getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var h=a[s];t.__dirty&&(h.__dirty|=1),this._updateAndAddDisplayable(h,e,r)}t.__dirty=0}else{var l=t;e&&e.length?l.__clipPaths=e:l.__clipPaths&&l.__clipPaths.length>0&&(l.__clipPaths=[]),isNaN(l.z)&&($e(),l.z=0),isNaN(l.z2)&&($e(),l.z2=0),isNaN(l.zlevel)&&($e(),l.zlevel=0),this._displayList[this._displayListLen++]=l}var u=t.getDecalElement&&t.getDecalElement();u&&this._updateAndAddDisplayable(u,e,r);var f=t.getTextGuideLine();f&&this._updateAndAddDisplayable(f,e,r);var c=t.getTextContent();c&&this._updateAndAddDisplayable(c,e,r)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,r=t.length;e<r;e++)this.delRoot(t[e]);else{var i=M(this._roots,t);i>=0&&this._roots.splice(i,1)}},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}();tr=r.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)};var rr={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,r=.1;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=.4*Math.asin(1/r)/(2*Math.PI),-r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,r=.1;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=.4*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,r=.1,i=.4;return 0===t?0:1===t?1:(!r||r<1?(r=1,e=.1):e=i*Math.asin(1/r)/(2*Math.PI),(t*=2)<1?r*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:r*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-rr.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*rr.bounceIn(2*t):.5*rr.bounceOut(2*t-1)+.5}},ir=Math.pow,nr=Math.sqrt,or=1e-4,ar=nr(3),sr=1/3,hr=xt(),lr=xt(),ur=xt();function fr(t){return t>-1e-8&&t<1e-8}function cr(t){return t>1e-8||t<-1e-8}function pr(t,e,r,i,n){var o=1-n;return o*o*(o*t+3*n*e)+n*n*(n*i+3*o*r)}function dr(t,e,r,i,n){var o=1-n;return 3*(((e-t)*o+2*(r-e)*n)*o+(i-r)*n*n)}function vr(t,e,r,i,n,o){var a=i+3*(e-r)-t,s=3*(r-2*e+t),h=3*(e-t),l=t-n,u=s*s-3*a*h,f=s*h-9*a*l,c=h*h-3*s*l,p=0;if(fr(u)&&fr(f)){if(fr(s))o[0]=0;else(S=-h/s)>=0&&S<=1&&(o[p++]=S)}else{var d=f*f-4*u*c;if(fr(d)){var v=f/u,y=-v/2;(S=-s/a+v)>=0&&S<=1&&(o[p++]=S),y>=0&&y<=1&&(o[p++]=y)}else if(d>0){var g=nr(d),_=u*s+1.5*a*(-f+g),m=u*s+1.5*a*(-f-g);(S=(-s-((_=_<0?-ir(-_,sr):ir(_,sr))+(m=m<0?-ir(-m,sr):ir(m,sr))))/(3*a))>=0&&S<=1&&(o[p++]=S)}else{var x=(2*u*s-3*a*f)/(2*nr(u*u*u)),w=Math.acos(x)/3,b=nr(u),k=Math.cos(w),S=(-s-2*b*k)/(3*a),T=(y=(-s+b*(k+ar*Math.sin(w)))/(3*a),(-s+b*(k-ar*Math.sin(w)))/(3*a));S>=0&&S<=1&&(o[p++]=S),y>=0&&y<=1&&(o[p++]=y),T>=0&&T<=1&&(o[p++]=T)}}return p}function yr(t,e,r,i,n){var o=6*r-12*e+6*t,a=9*e+3*i-3*t-9*r,s=3*e-3*t,h=0;if(fr(a)){if(cr(o))(u=-s/o)>=0&&u<=1&&(n[h++]=u)}else{var l=o*o-4*a*s;if(fr(l))n[0]=-o/(2*a);else if(l>0){var u,f=nr(l),c=(-o-f)/(2*a);(u=(-o+f)/(2*a))>=0&&u<=1&&(n[h++]=u),c>=0&&c<=1&&(n[h++]=c)}}return h}function gr(t,e,r,i,n,o){var a=(e-t)*n+t,s=(r-e)*n+e,h=(i-r)*n+r,l=(s-a)*n+a,u=(h-s)*n+s,f=(u-l)*n+l;o[0]=t,o[1]=a,o[2]=l,o[3]=f,o[4]=f,o[5]=u,o[6]=h,o[7]=i}function _r(t,e,r,i,n,o,a,s,h,l,u){var f,c,p,d,v,y=.005,g=Infinity;hr[0]=h,hr[1]=l;for(var _=0;_<1;_+=.05)lr[0]=pr(t,r,n,a,_),lr[1]=pr(e,i,o,s,_),(d=Ft(hr,lr))<g&&(f=_,g=d);g=Infinity;for(var m=0;m<32&&!(y<or);m++)c=f-y,p=f+y,lr[0]=pr(t,r,n,a,c),lr[1]=pr(e,i,o,s,c),d=Ft(lr,hr),c>=0&&d<g?(f=c,g=d):(ur[0]=pr(t,r,n,a,p),ur[1]=pr(e,i,o,s,p),v=Ft(ur,hr),p<=1&&v<g?(f=p,g=v):y*=.5);return u&&(u[0]=pr(t,r,n,a,f),u[1]=pr(e,i,o,s,f)),nr(g)}function mr(t,e,r,i,n,o,a,s,h){for(var l=t,u=e,f=0,c=1/h,p=1;p<=h;p++){var d=p*c,v=pr(t,r,n,a,d),y=pr(e,i,o,s,d),g=v-l,_=y-u;f+=Math.sqrt(g*g+_*_),l=v,u=y}return f}function xr(t,e,r,i){var n=1-i;return n*(n*t+2*i*e)+i*i*r}function wr(t,e,r,i){return 2*((1-i)*(e-t)+i*(r-e))}function br(t,e,r){var i=t+r-2*e;return 0===i?.5:(t-e)/i}function kr(t,e,r,i,n){var o=(e-t)*i+t,a=(r-e)*i+e,s=(a-o)*i+o;n[0]=t,n[1]=o,n[2]=s,n[3]=s,n[4]=a,n[5]=r}function Sr(t,e,r,i,n,o,a,s,h){var l,u=.005,f=Infinity;hr[0]=a,hr[1]=s;for(var c=0;c<1;c+=.05){lr[0]=xr(t,r,n,c),lr[1]=xr(e,i,o,c),(y=Ft(hr,lr))<f&&(l=c,f=y)}f=Infinity;for(var p=0;p<32&&!(u<or);p++){var d=l-u,v=l+u;lr[0]=xr(t,r,n,d),lr[1]=xr(e,i,o,d);var y=Ft(lr,hr);if(d>=0&&y<f)l=d,f=y;else{ur[0]=xr(t,r,n,v),ur[1]=xr(e,i,o,v);var g=Ft(ur,hr);v<=1&&g<f?(l=v,f=g):u*=.5}}return h&&(h[0]=xr(t,r,n,l),h[1]=xr(e,i,o,l)),nr(f)}function Tr(t,e,r,i,n,o,a){for(var s=t,h=e,l=0,u=1/a,f=1;f<=a;f++){var c=f*u,p=xr(t,r,n,c),d=xr(e,i,o,c),v=p-s,y=d-h;l+=Math.sqrt(v*v+y*y),s=p,h=d}return l}var Cr=/cubic-bezier\(([0-9,\.e ]+)\)/;function Pr(t){var e=t&&Cr.exec(t);if(e){var r=e[1].split(","),i=+ot(r[0]),n=+ot(r[1]),o=+ot(r[2]),a=+ot(r[3]);if(isNaN(i+n+o+a))return;var s=[];return function(t){return t<=0?0:t>=1?1:vr(0,i,o,1,t,s)&&pr(0,n,a,1,s[0])}}}var Mr=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||gt,this.ondestroy=t.ondestroy||gt,this.onrestart=t.onrestart||gt,t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var r=this._life,i=t-this._startTime-this._pausedTime,n=i/r;n<0&&(n=0),n=Math.min(n,1);var o=this.easingFunc,a=o?o(n):n;if(this.onframe(a),1===n){if(!this.loop)return!0;var s=i%r;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=W(t)?t:rr[t]||Pr(t)},t}(),Ar=function(){return function(t){this.value=t}}(),Lr=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new Ar(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,r=t.next;e?e.next=r:this.head=r,r?r.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}(),Ir=function(){function t(t){this._list=new Lr,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var r=this._list,i=this._map,n=null;if(null==i[t]){var o=r.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var s=r.head;r.remove(s),delete i[s.key],n=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new Ar(e),a.key=t,r.insertEntry(a),i[t]=a}return n},t.prototype.get=function(t){var e=this._map[t],r=this._list;if(null!=e)return e!==r.tail&&(r.remove(e),r.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}(),Dr={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function zr(t){return(t=Math.round(t))<0?0:t>255?255:t}function Or(t){return t<0?0:t>1?1:t}function Rr(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?zr(parseFloat(e)/100*255):zr(parseInt(e,10))}function Fr(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Or(parseFloat(e)/100):Or(parseFloat(e))}function Br(t,e,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function Nr(t,e,r){return t+(e-t)*r}function Hr(t,e,r,i,n){return t[0]=e,t[1]=r,t[2]=i,t[3]=n,t}function Er(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Wr=new Ir(20),Xr=null;function jr(t,e){Xr&&Er(Xr,e),Xr=Wr.put(t,Xr||e.slice())}function qr(t,e){if(t){e=e||[];var r=Wr.get(t);if(r)return Er(e,r);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in Dr)return Er(e,Dr[i]),jr(t,e),e;var n,o=i.length;if("#"===i.charAt(0))return 4===o||5===o?(n=parseInt(i.slice(1,4),16))>=0&&n<=4095?(Hr(e,(3840&n)>>4|(3840&n)>>8,240&n|(240&n)>>4,15&n|(15&n)<<4,5===o?parseInt(i.slice(4),16)/15:1),jr(t,e),e):void Hr(e,0,0,0,1):7===o||9===o?(n=parseInt(i.slice(1,7),16))>=0&&n<=16777215?(Hr(e,(16711680&n)>>16,(65280&n)>>8,255&n,9===o?parseInt(i.slice(7),16)/255:1),jr(t,e),e):void Hr(e,0,0,0,1):void 0;var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var h=i.substr(0,a),l=i.substr(a+1,s-(a+1)).split(","),u=1;switch(h){case"rgba":if(4!==l.length)return 3===l.length?Hr(e,+l[0],+l[1],+l[2],1):Hr(e,0,0,0,1);u=Fr(l.pop());case"rgb":return l.length>=3?(Hr(e,Rr(l[0]),Rr(l[1]),Rr(l[2]),3===l.length?u:Fr(l[3])),jr(t,e),e):void Hr(e,0,0,0,1);case"hsla":return 4!==l.length?void Hr(e,0,0,0,1):(l[3]=Fr(l[3]),Yr(l,e),jr(t,e),e);case"hsl":return 3!==l.length?void Hr(e,0,0,0,1):(Yr(l,e),jr(t,e),e);default:return}}Hr(e,0,0,0,1)}}function Yr(t,e){var r=(parseFloat(t[0])%360+360)%360/360,i=Fr(t[1]),n=Fr(t[2]),o=n<=.5?n*(i+1):n+i-n*i,a=2*n-o;return Hr(e=e||[],zr(255*Br(a,o,r+1/3)),zr(255*Br(a,o,r)),zr(255*Br(a,o,r-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Vr(t,e){var r=qr(t);if(r){for(var i=0;i<3;i++)r[i]=e<0?r[i]*(1-e)|0:(255-r[i])*e+r[i]|0,r[i]>255?r[i]=255:r[i]<0&&(r[i]=0);return Jr(r,4===r.length?"rgba":"rgb")}}function Ur(t,e,r){if(e&&e.length&&t>=0&&t<=1){r=r||[];var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),a=e[n],s=e[o],h=i-n;return r[0]=zr(Nr(a[0],s[0],h)),r[1]=zr(Nr(a[1],s[1],h)),r[2]=zr(Nr(a[2],s[2],h)),r[3]=Or(Nr(a[3],s[3],h)),r}}var Gr=Ur;function Zr(t,e,r){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),n=Math.floor(i),o=Math.ceil(i),a=qr(e[n]),s=qr(e[o]),h=i-n,l=Jr([zr(Nr(a[0],s[0],h)),zr(Nr(a[1],s[1],h)),zr(Nr(a[2],s[2],h)),Or(Nr(a[3],s[3],h))],"rgba");return r?{color:l,leftIndex:n,rightIndex:o,value:i}:l}}var Kr=Zr;function Qr(t,e,r,i){var n,o=qr(t);if(t)return o=function(t){if(t){var e,r,i=t[0]/255,n=t[1]/255,o=t[2]/255,a=Math.min(i,n,o),s=Math.max(i,n,o),h=s-a,l=(s+a)/2;if(0===h)e=0,r=0;else{r=l<.5?h/(s+a):h/(2-s-a);var u=((s-i)/6+h/2)/h,f=((s-n)/6+h/2)/h,c=((s-o)/6+h/2)/h;i===s?e=c-f:n===s?e=1/3+u-c:o===s&&(e=2/3+f-u),e<0&&(e+=1),e>1&&(e-=1)}var p=[360*e,r,l];return null!=t[3]&&p.push(t[3]),p}}(o),null!=e&&(o[0]=(n=e,(n=Math.round(n))<0?0:n>360?360:n)),null!=r&&(o[1]=Fr(r)),null!=i&&(o[2]=Fr(i)),Jr(Yr(o),"rgba")}function $r(t,e){var r=qr(t);if(r&&null!=e)return r[3]=Or(e),Jr(r,"rgba")}function Jr(t,e){if(t&&t.length){var r=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(r+=","+t[3]),e+"("+r+")"}}function ti(t,e){var r=qr(t);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*e:0}var ei=new Ir(100);function ri(t){if(X(t)){var e=ei.get(t);return e||(e=Vr(t,-.1),ei.put(t,e)),e}if(Z(t)){var r=T({},t);return r.colorStops=z(t.colorStops,(function(t){return{offset:t.offset,color:Vr(t.color,-.1)}})),r}return t}const ii=Object.freeze(Object.defineProperty({__proto__:null,fastLerp:Ur,fastMapToColor:Gr,lerp:Zr,lift:Vr,liftColor:ri,lum:ti,mapToColor:Kr,modifyAlpha:$r,modifyHSL:Qr,parse:qr,random:function(){return Jr([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},stringify:Jr,toHex:function(t){var e=qr(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}},Symbol.toStringTag,{value:"Module"}));var ni=Math.round;function oi(t){var e;if(t&&"transparent"!==t){if("string"==typeof t&&t.indexOf("rgba")>-1){var r=qr(t);r&&(t="rgb("+r[0]+","+r[1]+","+r[2]+")",e=r[3])}}else t="none";return{color:t,opacity:null==e?1:e}}function ai(t){return t<1e-4&&t>-1e-4}function si(t){return ni(1e3*t)/1e3}function hi(t){return ni(1e4*t)/1e4}var li={left:"start",right:"end",center:"middle",middle:"middle"};function ui(t){return t&&!!t.image}function fi(t){return ui(t)||function(t){return t&&!!t.svgElement}(t)}function ci(t){return"linear"===t.type}function pi(t){return"radial"===t.type}function di(t){return t&&("linear"===t.type||"radial"===t.type)}function vi(t){return"url(#"+t+")"}function yi(t){var e=t.getGlobalScale(),r=Math.max(e[0],e[1]);return Math.max(Math.ceil(Math.log(r)/Math.log(10)),1)}function gi(t){var e=t.x||0,r=t.y||0,i=(t.rotation||0)*_t,n=tt(t.scaleX,1),o=tt(t.scaleY,1),a=t.skewX||0,s=t.skewY||0,h=[];return(e||r)&&h.push("translate("+e+"px,"+r+"px)"),i&&h.push("rotate("+i+")"),1===n&&1===o||h.push("scale("+n+","+o+")"),(a||s)&&h.push("skew("+ni(a*_t)+"deg, "+ni(s*_t)+"deg)"),h.join(" ")}var _i=r.hasGlobalWindow&&W(window.btoa)?function(t){return window.btoa(unescape(encodeURIComponent(t)))}:"undefined"!=typeof Buffer?function(t){return Buffer.from(t).toString("base64")}:function(t){return null},mi=Array.prototype.slice;function xi(t,e,r){return(e-t)*r+t}function wi(t,e,r,i){for(var n=e.length,o=0;o<n;o++)t[o]=xi(e[o],r[o],i);return t}function bi(t,e,r,i){for(var n=e.length,o=0;o<n;o++)t[o]=e[o]+r[o]*i;return t}function ki(t,e,r,i){for(var n=e.length,o=n&&e[0].length,a=0;a<n;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+r[a][s]*i}return t}function Si(t,e){for(var r=t.length,i=e.length,n=r>i?e:t,o=Math.min(r,i),a=n[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(r,i);s++)n.push({offset:a.offset,color:a.color.slice()})}function Ti(t,e,r){var i=t,n=e;if(i.push&&n.push){var o=i.length,a=n.length;if(o!==a)if(o>a)i.length=a;else for(var s=o;s<a;s++)i.push(1===r?n[s]:mi.call(n[s]));var h=i[0]&&i[0].length;for(s=0;s<i.length;s++)if(1===r)isNaN(i[s])&&(i[s]=n[s]);else for(var l=0;l<h;l++)isNaN(i[s][l])&&(i[s][l]=n[s][l])}}function Ci(t){if(I(t)){var e=t.length;if(I(t[0])){for(var r=[],i=0;i<e;i++)r.push(mi.call(t[i]));return r}return mi.call(t)}return t}function Pi(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Mi(t){return 4===t||5===t}function Ai(t){return 1===t||2===t}var Li=[0,0,0,0],Ii=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,r){this._needsSort=!0;var i=this.keyframes,n=i.length,o=!1,a=6,s=e;if(I(e)){var h=function(t){return I(t&&t[0])?2:1}(e);a=h,(1===h&&!q(e[0])||2===h&&!q(e[0][0]))&&(o=!0)}else if(q(e)&&!$(e))a=0;else if(X(e))if(isNaN(+e)){var l=qr(e);l&&(s=l,a=3)}else a=0;else if(Z(e)){var u=T({},s);u.colorStops=z(e.colorStops,(function(t){return{offset:t.offset,color:qr(t.color)}})),ci(e)?a=4:pi(e)&&(a=5),s=u}0===n?this.valType=a:a===this.valType&&6!==a||(o=!0),this.discrete=this.discrete||o;var f={time:t,value:s,rawValue:e,percent:0};return r&&(f.easing=r,f.easingFunc=W(r)?r:rr[r]||Pr(r)),i.push(f),f},t.prototype.prepare=function(t,e){var r=this.keyframes;this._needsSort&&r.sort((function(t,e){return t.time-e.time}));for(var i=this.valType,n=r.length,o=r[n-1],a=this.discrete,s=Ai(i),h=Mi(i),l=0;l<n;l++){var u=r[l],f=u.value,c=o.value;u.percent=u.time/t,a||(s&&l!==n-1?Ti(f,c,i):h&&Si(f.colorStops,c.colorStops))}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;var p=r[0].value;for(l=0;l<n;l++)0===i?r[l].additiveValue=r[l].value-p:3===i?r[l].additiveValue=bi([],r[l].value,p,-1):Ai(i)&&(r[l].additiveValue=1===i?bi([],r[l].value,p,-1):ki([],r[l].value,p,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var r,i,n,o=null!=this._additiveTrack,a=o?"additiveValue":"value",s=this.valType,h=this.keyframes,l=h.length,u=this.propName,f=3===s,c=this._lastFr,p=Math.min;if(1===l)i=n=h[0];else{if(e<0)r=0;else if(e<this._lastFrP){for(r=p(c+1,l-1);r>=0&&!(h[r].percent<=e);r--);r=p(r,l-2)}else{for(r=c;r<l&&!(h[r].percent>e);r++);r=p(r-1,l-2)}n=h[r+1],i=h[r]}if(i&&n){this._lastFr=r,this._lastFrP=e;var d=n.percent-i.percent,v=0===d?1:p((e-i.percent)/d,1);n.easingFunc&&(v=n.easingFunc(v));var y=o?this._additiveValue:f?Li:t[u];if(!Ai(s)&&!f||y||(y=this._additiveValue=[]),this.discrete)t[u]=v<1?i.rawValue:n.rawValue;else if(Ai(s))1===s?wi(y,i[a],n[a],v):function(t,e,r,i){for(var n=e.length,o=n&&e[0].length,a=0;a<n;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=xi(e[a][s],r[a][s],i)}}(y,i[a],n[a],v);else if(Mi(s)){var g=i[a],_=n[a],m=4===s;t[u]={type:m?"linear":"radial",x:xi(g.x,_.x,v),y:xi(g.y,_.y,v),colorStops:z(g.colorStops,(function(t,e){var r=_.colorStops[e];return{offset:xi(t.offset,r.offset,v),color:Pi(wi([],t.color,r.color,v))}})),global:_.global},m?(t[u].x2=xi(g.x2,_.x2,v),t[u].y2=xi(g.y2,_.y2,v)):t[u].r=xi(g.r,_.r,v)}else if(f)wi(y,i[a],n[a],v),o||(t[u]=Pi(y));else{var x=xi(i[a],n[a],v);o?this._additiveValue=x:t[u]=x}o&&this._addToTarget(t)}}},t.prototype._addToTarget=function(t){var e=this.valType,r=this.propName,i=this._additiveValue;0===e?t[r]=t[r]+i:3===e?(qr(t[r],Li),bi(Li,Li,i,1),t[r]=Pi(Li)):1===e?bi(t[r],t[r],i,1):2===e&&ki(t[r],t[r],i,1)},t}(),Di=function(){function t(t,e,r,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&i?w("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=r)}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,r){return this.whenWithKeys(t,e,B(e),r)},t.prototype.whenWithKeys=function(t,e,r,i){for(var n=this._tracks,o=0;o<r.length;o++){var a=r[o],s=n[a];if(!s){s=n[a]=new Ii(a);var h=void 0,l=this._getAdditiveTrack(a);if(l){var u=l.keyframes,f=u[u.length-1];h=f&&f.value,3===l.valType&&h&&(h=Pi(h))}else h=this._target[a];if(null==h)continue;t>0&&s.addKeyframe(0,Ci(h),i),this._trackKeys.push(a)}s.addKeyframe(t,Ci(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,r=0;r<e;r++)t[r].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var r=0;r<e.length;r++)e[r].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,r=0;r<e.length;r++)t[e[r]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,r=this._additiveAnimators;if(r)for(var i=0;i<r.length;i++){var n=r[i].getTrack(t);n&&(e=n)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,r=[],i=this._maxTime||0,n=0;n<this._trackKeys.length;n++){var o=this._trackKeys[n],a=this._tracks[o],s=this._getAdditiveTrack(o),h=a.keyframes,l=h.length;if(a.prepare(i,s),a.needsAnimate())if(!this._allowDiscrete&&a.discrete){var u=h[l-1];u&&(e._target[a.propName]=u.rawValue),a.setFinished()}else r.push(a)}if(r.length||this._force){var f=new Mr({life:i,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var i=e._additiveAnimators;if(i){for(var n=!1,o=0;o<i.length;o++)if(i[o]._clip){n=!0;break}n||(e._additiveAnimators=null)}for(o=0;o<r.length;o++)r[o].step(e._target,t);var a=e._onframeCbs;if(a)for(o=0;o<a.length;o++)a[o](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=f,this.animation&&this.animation.addClip(f),t&&f.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return z(this._trackKeys,(function(e){return t._tracks[e]}))},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var r=this._tracks,i=this._trackKeys,n=0;n<t.length;n++){var o=r[t[n]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}var a=!0;for(n=0;n<i.length;n++)if(!r[i[n]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveTo=function(t,e,r){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var n=e[i],o=this._tracks[n];if(o&&!o.isFinished()){var a=o.keyframes,s=a[r?0:a.length-1];s&&(t[n]=Ci(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||B(t);for(var r=0;r<e.length;r++){var i=e[r],n=this._tracks[i];if(n){var o=n.keyframes;if(o.length>1){var a=o.pop();n.addKeyframe(a.time,t[i]),n.prepare(this._maxTime,n.getAdditiveTrack())}}}},t}();function zi(){return(new Date).getTime()}var Oi,Ri,Fi=function(e){function r(t){var r=e.call(this)||this;return r._running=!1,r._time=0,r._pausedTime=0,r._pauseStart=0,r._paused=!1,t=t||{},r.stage=t.stage||{},r}return t(r,e),r.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},r.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},r.prototype.removeClip=function(t){if(t.animation){var e=t.prev,r=t.next;e?e.next=r:this._head=r,r?r.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},r.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},r.prototype.update=function(t){for(var e=zi()-this._pausedTime,r=e-this._time,i=this._head;i;){var n=i.next;i.step(e,r)?(i.ondestroy(),this.removeClip(i),i=n):i=n}this._time=e,t||(this.trigger("frame",r),this.stage.update&&this.stage.update())},r.prototype._startLoop=function(){var t=this;this._running=!0,tr((function e(){t._running&&(tr(e),!t._paused&&t.update())}))},r.prototype.start=function(){this._running||(this._time=zi(),this._pausedTime=0,this._startLoop())},r.prototype.stop=function(){this._running=!1},r.prototype.pause=function(){this._paused||(this._pauseStart=zi(),this._paused=!0)},r.prototype.resume=function(){this._paused&&(this._pausedTime+=zi()-this._pauseStart,this._paused=!1)},r.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},r.prototype.isFinished=function(){return null==this._head},r.prototype.animate=function(t,e){e=e||{},this.start();var r=new Di(t,e.loop);return this.addAnimator(r),r},r}(qt),Bi=r.domSupported,Ni=(Ri={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:Oi=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:z(Oi,(function(t){var e=t.replace("mouse","pointer");return Ri.hasOwnProperty(e)?e:t}))}),Hi=["mousemove","mouseup"],Ei=["pointermove","pointerup"],Wi=!1;function Xi(t){var e=t.pointerType;return"pen"===e||"touch"===e}function ji(t){t&&(t.zrByTouch=!0)}function qi(t,e){for(var r=e,i=!1;r&&9!==r.nodeType&&!(i=r.domBelongToZr||r!==e&&r===t.painterRoot);)r=r.parentNode;return i}var Yi=function(){return function(t,e){this.stopPropagation=gt,this.stopImmediatePropagation=gt,this.preventDefault=gt,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}}(),Vi={mousedown:function(t){t=he(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=he(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=he(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){qi(this,(t=he(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){Wi=!0,t=he(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){Wi||(t=he(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){ji(t=he(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Vi.mousemove.call(this,t),Vi.mousedown.call(this,t)},touchmove:function(t){ji(t=he(this.dom,t)),this.handler.processGesture(t,"change"),Vi.mousemove.call(this,t)},touchend:function(t){ji(t=he(this.dom,t)),this.handler.processGesture(t,"end"),Vi.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&Vi.click.call(this,t)},pointerdown:function(t){Vi.mousedown.call(this,t)},pointermove:function(t){Xi(t)||Vi.mousemove.call(this,t)},pointerup:function(t){Vi.mouseup.call(this,t)},pointerout:function(t){Xi(t)||Vi.mouseout.call(this,t)}};D(["click","dblclick","contextmenu"],(function(t){Vi[t]=function(e){e=he(this.dom,e),this.trigger(t,e)}}));var Ui={pointermove:function(t){Xi(t)||Ui.mousemove.call(this,t)},pointerup:function(t){Ui.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function Gi(t,e){var i=e.domHandlers;r.pointerEventsSupported?D(Ni.pointer,(function(r){Ki(e,r,(function(e){i[r].call(t,e)}))})):(r.touchEventsSupported&&D(Ni.touch,(function(r){Ki(e,r,(function(n){i[r].call(t,n),function(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}(e)}))})),D(Ni.mouse,(function(r){Ki(e,r,(function(n){n=se(n),e.touching||i[r].call(t,n)}))})))}function Zi(t,e){function i(r){Ki(e,r,(function(i){i=se(i),qi(t,i.target)||(i=function(t,e){return he(t.dom,new Yi(t,e),!0)}(t,i),e.domHandlers[r].call(t,i))}),{capture:!0})}r.pointerEventsSupported?D(Ei,i):r.touchEventsSupported||D(Hi,i)}function Ki(t,e,r,i){t.mounted[e]=r,t.listenerOpts[e]=i,le(t.domTarget,e,r,i)}function Qi(t){var e,r,i,n,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,r=a,i=o[a],n=t.listenerOpts[a],e.removeEventListener(r,i,n));t.mounted={}}var $i=function(){return function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}}(),Ji=function(e){function r(t,r){var i=e.call(this)||this;return i.__pointerCapturing=!1,i.dom=t,i.painterRoot=r,i._localHandlerScope=new $i(t,Vi),Bi&&(i._globalHandlerScope=new $i(document,Ui)),Gi(i,i._localHandlerScope),i}return t(r,e),r.prototype.dispose=function(){Qi(this._localHandlerScope),Bi&&Qi(this._globalHandlerScope)},r.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},r.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,Bi&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?Zi(this,e):Qi(e)}},r}(qt),tn=1;r.hasGlobalWindow&&(tn=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var en=tn,rn="#333",nn="#ccc",on=ye;function an(t){return t>5e-5||t<-5e-5}var sn=[],hn=[],ln=[1,0,0,1,0,0],un=Math.abs,fn=function(){function t(){}var e;return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return an(this.rotation)||an(this.x)||an(this.y)||an(this.scaleX-1)||an(this.scaleY-1)||an(this.skewX)||an(this.skewY)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),r=this.transform;e||t?(r=r||[1,0,0,1,0,0],e?this.getLocalTransform(r):on(r),t&&(e?_e(r,t,r):ge(r,t)),this.transform=r,this._resolveGlobalScaleRatio(r)):r&&(on(r),this.invTransform=null)},t.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(sn);var r=sn[0]<0?-1:1,i=sn[1]<0?-1:1,n=((sn[0]-r)*e+r)/sn[0]||0,o=((sn[1]-i)*e+i)/sn[1]||0;t[0]*=n,t[1]*=n,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||[1,0,0,1,0,0],be(this.invTransform,t)},t.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],r=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),n=Math.PI/2+i-Math.atan2(t[3],t[2]);r=Math.sqrt(r)*Math.cos(n),e=Math.sqrt(e),this.skewX=n,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=r,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||[1,0,0,1,0,0],_e(hn,t.invTransform,e),e=hn);var r=this.originX,i=this.originY;(r||i)&&(ln[4]=r,ln[5]=i,_e(hn,e,ln),hn[4]-=r,hn[5]-=i,e=hn),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var r=[t,e],i=this.invTransform;return i&&Nt(r,r,i),r},t.prototype.transformCoordToGlobal=function(t,e){var r=[t,e],i=this.transform;return i&&Nt(r,r,i),r},t.prototype.getLineScale=function(){var t=this.transform;return t&&un(t[0]-1)>1e-10&&un(t[3]-1)>1e-10?Math.sqrt(un(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){pn(this,t)},t.getLocalTransform=function(t,e){e=e||[];var r=t.originX||0,i=t.originY||0,n=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,h=t.rotation||0,l=t.x,u=t.y,f=t.skewX?Math.tan(t.skewX):0,c=t.skewY?Math.tan(-t.skewY):0;if(r||i||a||s){var p=r+a,d=i+s;e[4]=-p*n-f*d*o,e[5]=-d*o-c*p*n}else e[4]=e[5]=0;return e[0]=n,e[3]=o,e[1]=c*n,e[2]=f*o,h&&xe(e,e,h),e[4]+=r+l,e[5]+=i+u,e},t.initDefaultProps=((e=t.prototype).scaleX=e.scaleY=e.globalScaleRatio=1,void(e.x=e.y=e.originX=e.originY=e.skewX=e.skewY=e.rotation=e.anchorX=e.anchorY=0)),t}(),cn=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function pn(t,e){for(var r=0;r<cn.length;r++){var i=cn[r];t[i]=e[i]}}var dn={};function vn(t,e){var r=dn[e=e||n];r||(r=dn[e]=new Ir(500));var i=r.get(t);return null==i&&(i=a.measureText(t,e).width,r.put(t,i)),i}function yn(t,e,r,i){var n=vn(t,e),o=xn(e),a=_n(0,n,r),s=mn(0,o,i);return new Oe(a,s,n,o)}function gn(t,e,r,i){var n=((t||"")+"").split("\n");if(1===n.length)return yn(n[0],e,r,i);for(var o=new Oe(0,0,0,0),a=0;a<n.length;a++){var s=yn(n[a],e,r,i);0===a?o.copy(s):o.union(s)}return o}function _n(t,e,r){return"right"===r?t-=e:"center"===r&&(t-=e/2),t}function mn(t,e,r){return"middle"===r?t-=e/2:"bottom"===r&&(t-=e),t}function xn(t){return vn("国",t)}function wn(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function bn(t,e,r){var i=e.position||"inside",n=null!=e.distance?e.distance:5,o=r.height,a=r.width,s=o/2,h=r.x,l=r.y,u="left",f="top";if(i instanceof Array)h+=wn(i[0],r.width),l+=wn(i[1],r.height),u=null,f=null;else switch(i){case"left":h-=n,l+=s,u="right",f="middle";break;case"right":h+=n+a,l+=s,f="middle";break;case"top":h+=a/2,l-=n,u="center",f="bottom";break;case"bottom":h+=a/2,l+=o+n,u="center";break;case"inside":h+=a/2,l+=s,u="center",f="middle";break;case"insideLeft":h+=n,l+=s,f="middle";break;case"insideRight":h+=a-n,l+=s,u="right",f="middle";break;case"insideTop":h+=a/2,l+=n,u="center";break;case"insideBottom":h+=a/2,l+=o-n,u="center",f="bottom";break;case"insideTopLeft":h+=n,l+=n;break;case"insideTopRight":h+=a-n,l+=n,u="right";break;case"insideBottomLeft":h+=n,l+=o-n,f="bottom";break;case"insideBottomRight":h+=a-n,l+=o-n,u="right",f="bottom"}return(t=t||{}).x=h,t.y=l,t.align=u,t.verticalAlign=f,t}var kn="__zr_normal__",Sn=cn.concat(["ignore"]),Tn=O(cn,(function(t,e){return t[e]=!0,t}),{ignore:!1}),Cn={},Pn=new Oe(0,0,0,0),Mn=function(){function t(t){this.id=x(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,r){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var r=this.textConfig,i=r.local,n=e.innerTransformable,o=void 0,a=void 0,s=!1;n.parent=i?this:null;var h=!1;if(n.copyTransform(e),null!=r.position){var l=Pn;r.layoutRect?l.copy(r.layoutRect):l.copy(this.getBoundingRect()),i||l.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Cn,r,l):bn(Cn,r,l),n.x=Cn.x,n.y=Cn.y,o=Cn.align,a=Cn.verticalAlign;var u=r.origin;if(u&&null!=r.rotation){var f=void 0,c=void 0;"center"===u?(f=.5*l.width,c=.5*l.height):(f=wn(u[0],l.width),c=wn(u[1],l.height)),h=!0,n.originX=-n.x+f+(i?0:l.x),n.originY=-n.y+c+(i?0:l.y)}}null!=r.rotation&&(n.rotation=r.rotation);var p=r.offset;p&&(n.x+=p[0],n.y+=p[1],h||(n.originX=-p[0],n.originY=-p[1]));var d=null==r.inside?"string"==typeof r.position&&r.position.indexOf("inside")>=0:r.inside,v=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),y=void 0,g=void 0,_=void 0;d&&this.canBeInsideText()?(y=r.insideFill,g=r.insideStroke,null!=y&&"auto"!==y||(y=this.getInsideTextFill()),null!=g&&"auto"!==g||(g=this.getInsideTextStroke(y),_=!0)):(y=r.outsideFill,g=r.outsideStroke,null!=y&&"auto"!==y||(y=this.getOutsideFill()),null!=g&&"auto"!==g||(g=this.getOutsideStroke(y),_=!0)),(y=y||"#000")===v.fill&&g===v.stroke&&_===v.autoStroke&&o===v.align&&a===v.verticalAlign||(s=!0,v.fill=y,v.stroke=g,v.autoStroke=_,v.align=o,v.verticalAlign=a,e.setDefaultTextStyle(v)),e.__dirty|=1,s&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?nn:rn},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),r="string"==typeof e&&qr(e);r||(r=[255,255,255,1]);for(var i=r[3],n=this.__zr.isDarkMode(),o=0;o<3;o++)r[o]=r[o]*i+(n?0:255)*(1-i);return r[3]=1,Jr(r,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},T(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(Y(t))for(var r=B(t),i=0;i<r.length;i++){var n=r[i];this.attrKV(n,t[n])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,r=0;r<this.animators.length;r++){var i=this.animators[r],n=i.__fromStateTransition;if(!(i.getLoop()||n&&n!==kn)){var o=i.targetName,a=o?e[o]:e;i.saveTo(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Sn)},t.prototype._savePrimaryToNormal=function(t,e,r){for(var i=0;i<r.length;i++){var n=r[i];null==t[n]||n in e||(e[n]=this[n])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(kn,!1,t)},t.prototype.useState=function(t,e,r,i){var n=t===kn;if(this.hasState()||!n){var o=this.currentStates,a=this.stateTransition;if(!(M(o,t)>=0)||!e&&1!==o.length){var s;if(this.stateProxy&&!n&&(s=this.stateProxy(t)),s||(s=this.states&&this.states[t]),s||n){n||this.saveCurrentToNormalState(s);var h=!!(s&&s.hoverLayer||i);h&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,s,this._normalState,e,!r&&!this.__inHover&&a&&a.duration>0,a);var l=this._textContent,u=this._textGuide;return l&&l.useState(t,e,r,h),u&&u.useState(t,e,r,h),n?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2),s}w("State "+t+" not exists.")}}},t.prototype.useStates=function(t,e,r){if(t.length){var i=[],n=this.currentStates,o=t.length,a=o===n.length;if(a)for(var s=0;s<o;s++)if(t[s]!==n[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var h=t[s],l=void 0;this.stateProxy&&(l=this.stateProxy(h,t)),l||(l=this.states[h]),l&&i.push(l)}var u=i[o-1],f=!!(u&&u.hoverLayer||r);f&&this._toggleHoverLayerFlag(!0);var c=this._mergeStates(i),p=this.stateTransition;this.saveCurrentToNormalState(c),this._applyStateObj(t.join(","),c,this._normalState,!1,!e&&!this.__inHover&&p&&p.duration>0,p);var d=this._textContent,v=this._textGuide;d&&d.useStates(t,e,f),v&&v.useStates(t,e,f),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2)}else this.clearStates()},t.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=M(this.currentStates,t);if(e>=0){var r=this.currentStates.slice();r.splice(e,1),this.useStates(r)}},t.prototype.replaceState=function(t,e,r){var i=this.currentStates.slice(),n=M(i,t),o=M(i,e)>=0;n>=0?o?i.splice(n,1):i[n]=e:r&&!o&&i.push(e),this.useStates(i)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,r={},i=0;i<t.length;i++){var n=t[i];T(r,n),n.textConfig&&T(e=e||{},n.textConfig)}return e&&(r.textConfig=e),r},t.prototype._applyStateObj=function(t,e,r,i,n,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=T({},i?this.textConfig:r.textConfig),T(this.textConfig,e.textConfig)):a&&r.textConfig&&(this.textConfig=r.textConfig);for(var s={},h=!1,l=0;l<Sn.length;l++){var u=Sn[l],f=n&&Tn[u];e&&null!=e[u]?f?(h=!0,s[u]=e[u]):this[u]=e[u]:a&&null!=r[u]&&(f?(h=!0,s[u]=r[u]):this[u]=r[u])}if(!n)for(l=0;l<this.animators.length;l++){var c=this.animators[l],p=c.targetName;c.getLoop()||c.__changeFinalValue(p?(e||r)[p]:e||r)}h&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if((!t.__zr||t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new fn,this._attachComponent(t),this._textContent=t,this.markRedraw())},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),T(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=1;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,r=this._textGuide;e&&(e.__inHover=t),r&&(r.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.addAnimator(e[r]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var r=0;r<e.length;r++)t.animation.removeAnimator(e[r]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,r){var i=t?this[t]:this,n=new Di(i,e,r);return t&&(n.targetName=t),this.addAnimator(n,t),n},t.prototype.addAnimator=function(t,e){var r=this.__zr,i=this;t.during((function(){i.updateDuringAnimation(e)})).done((function(){var e=i.animators,r=M(e,t);r>=0&&e.splice(r,1)})),this.animators.push(t),r&&r.animation.addAnimator(t),r&&r.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var r=this.animators,i=r.length,n=[],o=0;o<i;o++){var a=r[o];t&&t!==a.scope?n.push(a):a.stop(e)}return this.animators=n,this},t.prototype.animateTo=function(t,e,r){An(this,t,e,r)},t.prototype.animateFrom=function(t,e,r){An(this,t,e,r,!0)},t.prototype._transitionState=function(t,e,r,i){for(var n=An(this,e,r,i),o=0;o<n.length;o++)n[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;function r(t,r,i,n){function o(t,e){Object.defineProperty(e,0,{get:function(){return t[i]},set:function(e){t[i]=e}}),Object.defineProperty(e,1,{get:function(){return t[n]},set:function(e){t[n]=e}})}Object.defineProperty(e,t,{get:function(){this[r]||o(this,this[r]=[]);return this[r]},set:function(t){this[i]=t[0],this[n]=t[1],this[r]=t,o(this,t)}})}e.type="element",e.name="",e.ignore=e.silent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=1,Object.defineProperty&&(r("position","_legacyPos","x","y"),r("scale","_legacyScale","scaleX","scaleY"),r("origin","_legacyOrigin","originX","originY"))}(),t}();function An(t,e,r,i,n){var o=[];Dn(t,"",t,e,r=r||{},i,o,n);var a=o.length,s=!1,h=r.done,l=r.aborted,u=function(){s=!0,--a<=0&&(s?h&&h():l&&l())},f=function(){--a<=0&&(s?h&&h():l&&l())};a||h&&h(),o.length>0&&r.during&&o[0].during((function(t,e){r.during(e)}));for(var c=0;c<o.length;c++){var p=o[c];u&&p.done(u),f&&p.aborted(f),r.force&&p.duration(r.duration),p.start(r.easing)}return o}function Ln(t,e,r){for(var i=0;i<r;i++)t[i]=e[i]}function In(t,e,r){if(I(e[r]))if(I(t[r])||(t[r]=[]),U(e[r])){var i=e[r].length;t[r].length!==i&&(t[r]=new e[r].constructor(i),Ln(t[r],e[r],i))}else{var n=e[r],o=t[r],a=n.length;if(I(n[0]))for(var s=n[0].length,h=0;h<a;h++)o[h]?Ln(o[h],n[h],s):o[h]=Array.prototype.slice.call(n[h]);else Ln(o,n,a);o.length=n.length}else t[r]=e[r]}function Dn(t,e,r,i,n,o,a,s){for(var h=B(i),l=n.duration,u=n.delay,f=n.additive,c=n.setToFinal,p=!Y(o),d=t.animators,v=[],y=0;y<h.length;y++){var g=h[y],_=i[g];if(null!=_&&null!=r[g]&&(p||o[g]))if(!Y(_)||I(_)||Z(_))v.push(g);else{if(e){s||(r[g]=_,t.updateDuringAnimation(e));continue}Dn(t,g,r[g],_,n,o&&o[g],a,s)}else s||(r[g]=_,t.updateDuringAnimation(e),v.push(g))}var m=v.length;if(!f&&m)for(var x=0;x<d.length;x++){if((b=d[x]).targetName===e)if(b.stopTracks(v)){var w=M(d,b);d.splice(w,1)}}if(n.force||(v=R(v,(function(t){return e=i[t],n=r[t],!(e===n||I(e)&&I(n)&&function(t,e){var r=t.length;if(r!==e.length)return!1;for(var i=0;i<r;i++)if(t[i]!==e[i])return!1;return!0}(e,n));var e,n})),m=v.length),m>0||n.force&&!a.length){var b,k=void 0,S=void 0,T=void 0;if(s){S={},c&&(k={});for(x=0;x<m;x++){S[g=v[x]]=r[g],c?k[g]=i[g]:r[g]=i[g]}}else if(c){T={};for(x=0;x<m;x++){T[g=v[x]]=Ci(r[g]),In(r,i,g)}}(b=new Di(r,!1,!1,f?R(d,(function(t){return t.targetName===e})):null)).targetName=e,n.scope&&(b.scope=n.scope),c&&k&&b.whenWithKeys(0,k,v),T&&b.whenWithKeys(0,T,v),b.whenWithKeys(null==l?500:l,s?S:i,v).delay(u||0),t.addAnimator(b,e),a.push(b)}}L(Mn,qt),L(Mn,fn);var zn=function(e){function r(t){var r=e.call(this)||this;return r.isGroup=!0,r._children=[],r.attr(t),r}return t(r,e),r.prototype.childrenRef=function(){return this._children},r.prototype.children=function(){return this._children.slice()},r.prototype.childAt=function(t){return this._children[t]},r.prototype.childOfName=function(t){for(var e=this._children,r=0;r<e.length;r++)if(e[r].name===t)return e[r]},r.prototype.childCount=function(){return this._children.length},r.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},r.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var r=this._children,i=r.indexOf(e);i>=0&&(r.splice(i,0,t),this._doAdd(t))}return this},r.prototype.replace=function(t,e){var r=M(this._children,t);return r>=0&&this.replaceAt(e,r),this},r.prototype.replaceAt=function(t,e){var r=this._children,i=r[e];if(t&&t!==this&&t.parent!==this&&t!==i){r[e]=t,i.parent=null;var n=this.__zr;n&&i.removeSelfFromZr(n),this._doAdd(t)}return this},r.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},r.prototype.remove=function(t){var e=this.__zr,r=this._children,i=M(r,t);return i<0||(r.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},r.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,r=0;r<t.length;r++){var i=t[r];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},r.prototype.eachChild=function(t,e){for(var r=this._children,i=0;i<r.length;i++){var n=r[i];t.call(e,n,i)}return this},r.prototype.traverse=function(t,e){for(var r=0;r<this._children.length;r++){var i=this._children[r],n=t.call(e,i);i.isGroup&&!n&&i.traverse(t,e)}return this},r.prototype.addSelfToZr=function(t){e.prototype.addSelfToZr.call(this,t);for(var r=0;r<this._children.length;r++){this._children[r].addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){e.prototype.removeSelfFromZr.call(this,t);for(var r=0;r<this._children.length;r++){this._children[r].removeSelfFromZr(t)}},r.prototype.getBoundingRect=function(t){for(var e=new Oe(0,0,0,0),r=t||this._children,i=[],n=null,o=0;o<r.length;o++){var a=r[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),h=a.getLocalTransform(i);h?(Oe.applyTransform(e,s,h),(n=n||e.clone()).union(e)):(n=n||s.clone()).union(s)}}return n||e},r}(Mn);zn.prototype.type="group";var On={},Rn={};var Fn,Bn=function(){function t(t,e,i){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=e,this.id=t;var o=new er,a=i.renderer||"canvas";On[a]||(a=B(On)[0]),i.useDirtyRect=null!=i.useDirtyRect&&i.useDirtyRect;var s=new On[a](e,o,i,t),h=i.ssr||s.ssrOnly;this.storage=o,this.painter=s;var l,u=r.node||r.worker||h?null:new Ji(s.getViewportRoot(),s.root),f=i.useCoarsePointer;(null==f||"auto"===f?r.touchEventsSupported:!!f)&&(l=tt(i.pointerSize,44)),this.handler=new We(o,s,u,s.root,l),this.animation=new Fi({stage:{update:h?null:function(){return n._flush(!0)}}}),h||this.animation.start()}return t.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},t.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return ti(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,r=0,i=e.length,n=0;n<i;n++)r+=ti(e[n].color,1);return(r/=i)<.4}return!1}(t))},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},t.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},t.prototype.flush=function(){this._disposed||this._flush(!1)},t.prototype._flush=function(t){var e,r=zi();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=zi();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-r})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},t.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},t.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},t.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},t.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},t.prototype.on=function(t,e,r){return this._disposed||this.handler.on(t,e,r),this},t.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},t.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},t.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof zn&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},t.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete Rn[t])},t}();function Nn(t,e){var r=new Bn(x(),t,e);return Rn[r.id]=r,r}function Hn(t,e){On[t]=e}function En(t){if("function"==typeof Fn)return Fn(t)}function Wn(t){Fn=t}const Xn=Object.freeze(Object.defineProperty({__proto__:null,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in Rn)Rn.hasOwnProperty(t)&&Rn[t].dispose();Rn={}},getElementSSRData:En,getInstance:function(t){return Rn[t]},init:Nn,registerPainter:Hn,registerSSRDataGetter:Wn,version:"5.6.1"},Symbol.toStringTag,{value:"Module"}));var jn=new Ir(50);function qn(t){if("string"==typeof t){var e=jn.get(t);return e&&e.image}return t}function Yn(t,e,r,i,n){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!r)return e;var o=jn.get(t),s={hostEl:r,cb:i,cbPayload:n};return o?!Un(e=o.image)&&o.pending.push(s):((e=a.loadImage(t,Vn,Vn)).__zrImageSrc=t,jn.put(t,e.__cachedImgObj={image:e,pending:[s]})),e}return t}return e}function Vn(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var r=t.pending[e],i=r.cb;i&&i(this,r.cbPayload),r.hostEl.dirty()}t.pending.length=0}function Un(t){return t&&t.width&&t.height}var Gn=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Zn(t,e,r,i,n){var o={};return Kn(o,t,e,r,i,n),o.text}function Kn(t,e,r,i,n,o){if(!r)return t.text="",void(t.isTruncated=!1);var a=(e+"").split("\n");o=Qn(r,i,n,o);for(var s=!1,h={},l=0,u=a.length;l<u;l++)$n(h,a[l],o),a[l]=h.textLine,s=s||h.isTruncated;t.text=a.join("\n"),t.isTruncated=s}function Qn(t,e,r,i){var n=T({},i=i||{});n.font=e,r=tt(r,"..."),n.maxIterations=tt(i.maxIterations,2);var o=n.minChar=tt(i.minChar,0);n.cnCharWidth=vn("国",e);var a=n.ascCharWidth=vn("a",e);n.placeholder=tt(i.placeholder,"");for(var s=t=Math.max(0,t-1),h=0;h<o&&s>=a;h++)s-=a;var l=vn(r,e);return l>s&&(r="",l=0),s=t-l,n.ellipsis=r,n.ellipsisWidth=l,n.contentWidth=s,n.containerWidth=t,n}function $n(t,e,r){var i=r.containerWidth,n=r.font,o=r.contentWidth;if(!i)return t.textLine="",void(t.isTruncated=!1);var a=vn(e,n);if(a<=i)return t.textLine=e,void(t.isTruncated=!1);for(var s=0;;s++){if(a<=o||s>=r.maxIterations){e+=r.ellipsis;break}var h=0===s?Jn(e,o,r.ascCharWidth,r.cnCharWidth):a>0?Math.floor(e.length*o/a):0;a=vn(e=e.substr(0,h),n)}""===e&&(e=r.placeholder),t.textLine=e,t.isTruncated=!0}function Jn(t,e,r,i){for(var n=0,o=0,a=t.length;o<a&&n<e;o++){var s=t.charCodeAt(o);n+=0<=s&&s<=127?r:i}return o}var to=function(){return function(){}}(),eo=function(){return function(t){this.tokens=[],t&&(this.tokens=t)}}(),ro=function(){return function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}}();function io(t,e,r,i,n){var o,a,s=""===e,h=n&&r.rich[n]||{},l=t.lines,u=h.font||r.font,f=!1;if(i){var c=h.padding,p=c?c[1]+c[3]:0;if(null!=h.width&&"auto"!==h.width){var d=wn(h.width,i.width)+p;l.length>0&&d+i.accumWidth>i.width&&(o=e.split("\n"),f=!0),i.accumWidth=d}else{var v=ao(e,u,i.width,i.breakAll,i.accumWidth);i.accumWidth=v.accumWidth+p,a=v.linesWidths,o=v.lines}}else o=e.split("\n");for(var y=0;y<o.length;y++){var g=o[y],_=new to;if(_.styleName=n,_.text=g,_.isLineHolder=!g&&!s,"number"==typeof h.width?_.width=h.width:_.width=a?a[y]:vn(g,u),y||f)l.push(new eo([_]));else{var m=(l[l.length-1]||(l[0]=new eo)).tokens,x=m.length;1===x&&m[0].isLineHolder?m[0]=_:(g||!x||s)&&m.push(_)}}}var no=O(",&?/;] ".split(""),(function(t,e){return t[e]=!0,t}),{});function oo(t){return!function(t){var e=t.charCodeAt(0);return e>=32&&e<=591||e>=880&&e<=4351||e>=4608&&e<=5119||e>=7680&&e<=8303}(t)||!!no[t]}function ao(t,e,r,i,n){for(var o=[],a=[],s="",h="",l=0,u=0,f=0;f<t.length;f++){var c=t.charAt(f);if("\n"!==c){var p=vn(c,e),d=!i&&!oo(c);(o.length?u+p>r:n+u+p>r)?u?(s||h)&&(d?(s||(s=h,h="",u=l=0),o.push(s),a.push(u-l),h+=c,s="",u=l+=p):(h&&(s+=h,h="",l=0),o.push(s),a.push(u),s=c,u=p)):d?(o.push(h),a.push(l),h=c,l=p):(o.push(c),a.push(p)):(u+=p,d?(h+=c,l+=p):(h&&(s+=h,h="",l=0),s+=c))}else h&&(s+=h,u+=l),o.push(s),a.push(u),s="",h="",l=0,u=0}return o.length||s||(s=t,h="",l=0),h&&(s+=h),s&&(o.push(s),a.push(u)),1===o.length&&(u+=n),{accumWidth:u,lines:o,linesWidths:a}}var so="__zr_style_"+Math.round(10*Math.random()),ho={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},lo={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};ho[so]=!0;var uo=["z","z2","invisible"],fo=["invisible"],co=function(e){function r(t){return e.call(this,t)||this}var i;return t(r,e),r.prototype._init=function(t){for(var r=B(t),i=0;i<r.length;i++){var n=r[i];"style"===n?this.useStyle(t[n]):e.prototype.attrKV.call(this,n,t[n])}this.style||this.useStyle({})},r.prototype.beforeBrush=function(){},r.prototype.afterBrush=function(){},r.prototype.innerBeforeBrush=function(){},r.prototype.innerAfterBrush=function(){},r.prototype.shouldBePainted=function(t,e,r,i){var n=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,r){po.copy(t.getBoundingRect()),t.transform&&po.applyTransform(t.transform);return vo.width=e,vo.height=r,!po.intersect(vo)}(this,t,e)||n&&!n[0]&&!n[3])return!1;if(r&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},r.prototype.contain=function(t,e){return this.rectContain(t,e)},r.prototype.traverse=function(t,e){t.call(e,this)},r.prototype.rectContain=function(t,e){var r=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(r[0],r[1])},r.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,r=this.getBoundingRect(),i=this.style,n=i.shadowBlur||0,o=i.shadowOffsetX||0,a=i.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new Oe(0,0,0,0)),e?Oe.applyTransform(t,r,e):t.copy(r),(n||o||a)&&(t.width+=2*n+Math.abs(o),t.height+=2*n+Math.abs(a),t.x=Math.min(t.x,t.x+o-n),t.y=Math.min(t.y,t.y+a-n));var s=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-s),t.y=Math.floor(t.y-s),t.width=Math.ceil(t.width+1+2*s),t.height=Math.ceil(t.height+1+2*s))}return t},r.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new Oe(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},r.prototype.getPrevPaintRect=function(){return this._prevPaintRect},r.prototype.animateStyle=function(t){return this.animate("style",t)},r.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},r.prototype.attrKV=function(t,r){"style"!==t?e.prototype.attrKV.call(this,t,r):this.style?this.setStyle(r):this.useStyle(r)},r.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:T(this.style,t),this.dirtyStyle(),this},r.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},r.prototype.dirty=function(){this.dirtyStyle()},r.prototype.styleChanged=function(){return!!(2&this.__dirty)},r.prototype.styleUpdated=function(){this.__dirty&=-3},r.prototype.createStyle=function(t){return dt(ho,t)},r.prototype.useStyle=function(t){t[so]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},r.prototype.isStyleObject=function(t){return t[so]},r.prototype._innerSaveToNormal=function(t){e.prototype._innerSaveToNormal.call(this,t);var r=this._normalState;t.style&&!r.style&&(r.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,r,uo)},r.prototype._applyStateObj=function(t,r,i,n,o,a){e.prototype._applyStateObj.call(this,t,r,i,n,o,a);var s,h=!(r&&n);if(r&&r.style?o?n?s=r.style:(s=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(s,r.style)):(s=this._mergeStyle(this.createStyle(),n?this.style:i.style),this._mergeStyle(s,r.style)):h&&(s=i.style),s)if(o){var l=this.style;if(this.style=this.createStyle(h?{}:l),h)for(var u=B(l),f=0;f<u.length;f++){(p=u[f])in s&&(s[p]=s[p],this.style[p]=l[p])}var c=B(s);for(f=0;f<c.length;f++){var p=c[f];this.style[p]=this.style[p]}this._transitionState(t,{style:s},a,this.getAnimationStyleProps())}else this.useStyle(s);var d=this.__inHover?fo:uo;for(f=0;f<d.length;f++){p=d[f];r&&null!=r[p]?this[p]=r[p]:h&&null!=i[p]&&(this[p]=i[p])}},r.prototype._mergeStates=function(t){for(var r,i=e.prototype._mergeStates.call(this,t),n=0;n<t.length;n++){var o=t[n];o.style&&(r=r||{},this._mergeStyle(r,o.style))}return r&&(i.style=r),i},r.prototype._mergeStyle=function(t,e){return T(t,e),t},r.prototype.getAnimationStyleProps=function(){return lo},r.initDefaultProps=((i=r.prototype).type="displayable",i.invisible=!1,i.z=0,i.z2=0,i.zlevel=0,i.culling=!1,i.cursor="pointer",i.rectHover=!1,i.incremental=!1,i._rect=null,i.dirtyRectTolerance=0,void(i.__dirty=3)),r}(Mn),po=new Oe(0,0,0,0),vo=new Oe(0,0,0,0);var yo=Math.min,go=Math.max,_o=Math.sin,mo=Math.cos,xo=2*Math.PI,wo=xt(),bo=xt(),ko=xt();function So(t,e,r){if(0!==t.length){for(var i=t[0],n=i[0],o=i[0],a=i[1],s=i[1],h=1;h<t.length;h++)i=t[h],n=yo(n,i[0]),o=go(o,i[0]),a=yo(a,i[1]),s=go(s,i[1]);e[0]=n,e[1]=a,r[0]=o,r[1]=s}}function To(t,e,r,i,n,o){n[0]=yo(t,r),n[1]=yo(e,i),o[0]=go(t,r),o[1]=go(e,i)}var Co=[],Po=[];function Mo(t,e,r,i,n,o,a,s,h,l){var u=yr,f=pr,c=u(t,r,n,a,Co);h[0]=Infinity,h[1]=Infinity,l[0]=-Infinity,l[1]=-Infinity;for(var p=0;p<c;p++){var d=f(t,r,n,a,Co[p]);h[0]=yo(d,h[0]),l[0]=go(d,l[0])}c=u(e,i,o,s,Po);for(p=0;p<c;p++){var v=f(e,i,o,s,Po[p]);h[1]=yo(v,h[1]),l[1]=go(v,l[1])}h[0]=yo(t,h[0]),l[0]=go(t,l[0]),h[0]=yo(a,h[0]),l[0]=go(a,l[0]),h[1]=yo(e,h[1]),l[1]=go(e,l[1]),h[1]=yo(s,h[1]),l[1]=go(s,l[1])}function Ao(t,e,r,i,n,o,a,s){var h=br,l=xr,u=go(yo(h(t,r,n),1),0),f=go(yo(h(e,i,o),1),0),c=l(t,r,n,u),p=l(e,i,o,f);a[0]=yo(t,n,c),a[1]=yo(e,o,p),s[0]=go(t,n,c),s[1]=go(e,o,p)}function Lo(t,e,r,i,n,o,a,s,h){var l=Ht,u=Et,f=Math.abs(n-o);if(f%xo<1e-4&&f>1e-4)return s[0]=t-r,s[1]=e-i,h[0]=t+r,void(h[1]=e+i);if(wo[0]=mo(n)*r+t,wo[1]=_o(n)*i+e,bo[0]=mo(o)*r+t,bo[1]=_o(o)*i+e,l(s,wo,bo),u(h,wo,bo),(n%=xo)<0&&(n+=xo),(o%=xo)<0&&(o+=xo),n>o&&!a?o+=xo:n<o&&a&&(n+=xo),a){var c=o;o=n,n=c}for(var p=0;p<o;p+=Math.PI/2)p>n&&(ko[0]=mo(p)*r+t,ko[1]=_o(p)*i+e,l(s,ko,s),u(h,ko,h))}var Io={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Do=[],zo=[],Oo=[],Ro=[],Fo=[],Bo=[],No=Math.min,Ho=Math.max,Eo=Math.cos,Wo=Math.sin,Xo=Math.abs,jo=Math.PI,qo=2*jo,Yo="undefined"!=typeof Float32Array,Vo=[];function Uo(t){return Math.round(t/jo*1e8)/1e8%2*jo}function Go(t,e){var r=Uo(t[0]);r<0&&(r+=qo);var i=r-t[0],n=t[1];n+=i,!e&&n-r>=qo?n=r+qo:e&&r-n>=qo?n=r-qo:!e&&r>n?n=r+(qo-Uo(r-n)):e&&r<n&&(n=r-(qo-Uo(n-r))),t[0]=r,t[1]=n}var Zo=function(){function t(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}var e;return t.prototype.increaseVersion=function(){this._version++},t.prototype.getVersion=function(){return this._version},t.prototype.setScale=function(t,e,r){(r=r||0)>0&&(this._ux=Xo(r/en/t)||0,this._uy=Xo(r/en/e)||0)},t.prototype.setDPR=function(t){this.dpr=t},t.prototype.setContext=function(t){this._ctx=t},t.prototype.getContext=function(){return this._ctx},t.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},t.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},t.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Io.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},t.prototype.lineTo=function(t,e){var r=Xo(t-this._xi),i=Xo(e-this._yi),n=r>this._ux||i>this._uy;if(this.addData(Io.L,t,e),this._ctx&&n&&this._ctx.lineTo(t,e),n)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=r*r+i*i;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},t.prototype.bezierCurveTo=function(t,e,r,i,n,o){return this._drawPendingPt(),this.addData(Io.C,t,e,r,i,n,o),this._ctx&&this._ctx.bezierCurveTo(t,e,r,i,n,o),this._xi=n,this._yi=o,this},t.prototype.quadraticCurveTo=function(t,e,r,i){return this._drawPendingPt(),this.addData(Io.Q,t,e,r,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,r,i),this._xi=r,this._yi=i,this},t.prototype.arc=function(t,e,r,i,n,o){this._drawPendingPt(),Vo[0]=i,Vo[1]=n,Go(Vo,o),i=Vo[0];var a=(n=Vo[1])-i;return this.addData(Io.A,t,e,r,r,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,r,i,n,o),this._xi=Eo(n)*r+t,this._yi=Wo(n)*r+e,this},t.prototype.arcTo=function(t,e,r,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,r,i,n),this},t.prototype.rect=function(t,e,r,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,r,i),this.addData(Io.R,t,e,r,i),this},t.prototype.closePath=function(){this._drawPendingPt(),this.addData(Io.Z);var t=this._ctx,e=this._x0,r=this._y0;return t&&t.closePath(),this._xi=e,this._yi=r,this},t.prototype.fill=function(t){t&&t.fill(),this.toStatic()},t.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},t.prototype.len=function(){return this._len},t.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!Yo||(this.data=new Float32Array(e));for(var r=0;r<e;r++)this.data[r]=t[r];this._len=e},t.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,r=0,i=this._len,n=0;n<e;n++)r+=t[n].len();Yo&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+r));for(n=0;n<e;n++)for(var o=t[n].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},t.prototype.addData=function(t,e,r,i,n,o,a,s,h){if(this._saveData){var l=this.data;this._len+arguments.length>l.length&&(this._expandData(),l=this.data);for(var u=0;u<arguments.length;u++)l[this._len++]=arguments[u]}},t.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},t.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},t.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,Yo&&this._len>11&&(this.data=new Float32Array(t)))}},t.prototype.getBoundingRect=function(){Oo[0]=Oo[1]=Fo[0]=Fo[1]=Number.MAX_VALUE,Ro[0]=Ro[1]=Bo[0]=Bo[1]=-Number.MAX_VALUE;var t,e=this.data,r=0,i=0,n=0,o=0;for(t=0;t<this._len;){var a=e[t++],s=1===t;switch(s&&(n=r=e[t],o=i=e[t+1]),a){case Io.M:r=n=e[t++],i=o=e[t++],Fo[0]=n,Fo[1]=o,Bo[0]=n,Bo[1]=o;break;case Io.L:To(r,i,e[t],e[t+1],Fo,Bo),r=e[t++],i=e[t++];break;case Io.C:Mo(r,i,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],Fo,Bo),r=e[t++],i=e[t++];break;case Io.Q:Ao(r,i,e[t++],e[t++],e[t],e[t+1],Fo,Bo),r=e[t++],i=e[t++];break;case Io.A:var h=e[t++],l=e[t++],u=e[t++],f=e[t++],c=e[t++],p=e[t++]+c;t+=1;var d=!e[t++];s&&(n=Eo(c)*u+h,o=Wo(c)*f+l),Lo(h,l,u,f,c,p,d,Fo,Bo),r=Eo(p)*u+h,i=Wo(p)*f+l;break;case Io.R:To(n=r=e[t++],o=i=e[t++],n+e[t++],o+e[t++],Fo,Bo);break;case Io.Z:r=n,i=o}Ht(Oo,Oo,Fo),Et(Ro,Ro,Bo)}return 0===t&&(Oo[0]=Oo[1]=Ro[0]=Ro[1]=0),new Oe(Oo[0],Oo[1],Ro[0]-Oo[0],Ro[1]-Oo[1])},t.prototype._calculateLength=function(){var t=this.data,e=this._len,r=this._ux,i=this._uy,n=0,o=0,a=0,s=0;this._pathSegLen||(this._pathSegLen=[]);for(var h=this._pathSegLen,l=0,u=0,f=0;f<e;){var c=t[f++],p=1===f;p&&(a=n=t[f],s=o=t[f+1]);var d=-1;switch(c){case Io.M:n=a=t[f++],o=s=t[f++];break;case Io.L:var v=t[f++],y=(m=t[f++])-o;(Xo(A=v-n)>r||Xo(y)>i||f===e-1)&&(d=Math.sqrt(A*A+y*y),n=v,o=m);break;case Io.C:var g=t[f++],_=t[f++],m=(v=t[f++],t[f++]),x=t[f++],w=t[f++];d=mr(n,o,g,_,v,m,x,w,10),n=x,o=w;break;case Io.Q:d=Tr(n,o,g=t[f++],_=t[f++],v=t[f++],m=t[f++],10),n=v,o=m;break;case Io.A:var b=t[f++],k=t[f++],S=t[f++],T=t[f++],C=t[f++],P=t[f++],M=P+C;f+=1,p&&(a=Eo(C)*S+b,s=Wo(C)*T+k),d=Ho(S,T)*No(qo,Math.abs(P)),n=Eo(M)*S+b,o=Wo(M)*T+k;break;case Io.R:a=n=t[f++],s=o=t[f++],d=2*t[f++]+2*t[f++];break;case Io.Z:var A=a-n;y=s-o;d=Math.sqrt(A*A+y*y),n=a,o=s}d>=0&&(h[u++]=d,l+=d)}return this._pathLen=l,l},t.prototype.rebuildPath=function(t,e){var r,i,n,o,a,s,h,l,u,f,c=this.data,p=this._ux,d=this._uy,v=this._len,y=e<1,g=0,_=0,m=0;if(!y||(this._pathSegLen||this._calculateLength(),h=this._pathSegLen,l=e*this._pathLen))t:for(var x=0;x<v;){var w=c[x++],b=1===x;switch(b&&(r=n=c[x],i=o=c[x+1]),w!==Io.L&&m>0&&(t.lineTo(u,f),m=0),w){case Io.M:r=n=c[x++],i=o=c[x++],t.moveTo(n,o);break;case Io.L:a=c[x++],s=c[x++];var k=Xo(a-n),S=Xo(s-o);if(k>p||S>d){if(y){if(g+(G=h[_++])>l){var T=(l-g)/G;t.lineTo(n*(1-T)+a*T,o*(1-T)+s*T);break t}g+=G}t.lineTo(a,s),n=a,o=s,m=0}else{var C=k*k+S*S;C>m&&(u=a,f=s,m=C)}break;case Io.C:var P=c[x++],M=c[x++],A=c[x++],L=c[x++],I=c[x++],D=c[x++];if(y){if(g+(G=h[_++])>l){gr(n,P,A,I,T=(l-g)/G,Do),gr(o,M,L,D,T,zo),t.bezierCurveTo(Do[1],zo[1],Do[2],zo[2],Do[3],zo[3]);break t}g+=G}t.bezierCurveTo(P,M,A,L,I,D),n=I,o=D;break;case Io.Q:P=c[x++],M=c[x++],A=c[x++],L=c[x++];if(y){if(g+(G=h[_++])>l){kr(n,P,A,T=(l-g)/G,Do),kr(o,M,L,T,zo),t.quadraticCurveTo(Do[1],zo[1],Do[2],zo[2]);break t}g+=G}t.quadraticCurveTo(P,M,A,L),n=A,o=L;break;case Io.A:var z=c[x++],O=c[x++],R=c[x++],F=c[x++],B=c[x++],N=c[x++],H=c[x++],E=!c[x++],W=R>F?R:F,X=Xo(R-F)>.001,j=B+N,q=!1;if(y)g+(G=h[_++])>l&&(j=B+N*(l-g)/G,q=!0),g+=G;if(X&&t.ellipse?t.ellipse(z,O,R,F,H,B,j,E):t.arc(z,O,W,B,j,E),q)break t;b&&(r=Eo(B)*R+z,i=Wo(B)*F+O),n=Eo(j)*R+z,o=Wo(j)*F+O;break;case Io.R:r=n=c[x],i=o=c[x+1],a=c[x++],s=c[x++];var Y=c[x++],V=c[x++];if(y){if(g+(G=h[_++])>l){var U=l-g;t.moveTo(a,s),t.lineTo(a+No(U,Y),s),(U-=Y)>0&&t.lineTo(a+Y,s+No(U,V)),(U-=V)>0&&t.lineTo(a+Ho(Y-U,0),s+V),(U-=Y)>0&&t.lineTo(a,s+Ho(V-U,0));break t}g+=G}t.rect(a,s,Y,V);break;case Io.Z:if(y){var G;if(g+(G=h[_++])>l){T=(l-g)/G;t.lineTo(n*(1-T)+r*T,o*(1-T)+i*T);break t}g+=G}t.closePath(),n=r,o=i}}},t.prototype.clone=function(){var e=new t,r=this.data;return e.data=r.slice?r.slice():Array.prototype.slice.call(r),e._len=this._len,e},t.CMD=Io,t.initDefaultProps=((e=t.prototype)._saveData=!0,e._ux=0,e._uy=0,e._pendingPtDist=0,void(e._version=0)),t}();function Ko(t,e,r,i,n,o,a){if(0===n)return!1;var s=n,h=0;if(a>e+s&&a>i+s||a<e-s&&a<i-s||o>t+s&&o>r+s||o<t-s&&o<r-s)return!1;if(t===r)return Math.abs(o-t)<=s/2;var l=(h=(e-i)/(t-r))*o-a+(t*i-r*e)/(t-r);return l*l/(h*h+1)<=s/2*s/2}function Qo(t,e,r,i,n,o,a,s,h,l,u){if(0===h)return!1;var f=h;return!(u>e+f&&u>i+f&&u>o+f&&u>s+f||u<e-f&&u<i-f&&u<o-f&&u<s-f||l>t+f&&l>r+f&&l>n+f&&l>a+f||l<t-f&&l<r-f&&l<n-f&&l<a-f)&&_r(t,e,r,i,n,o,a,s,l,u,null)<=f/2}function $o(t,e,r,i,n,o,a,s,h){if(0===a)return!1;var l=a;return!(h>e+l&&h>i+l&&h>o+l||h<e-l&&h<i-l&&h<o-l||s>t+l&&s>r+l&&s>n+l||s<t-l&&s<r-l&&s<n-l)&&Sr(t,e,r,i,n,o,s,h,null)<=l/2}var Jo=2*Math.PI;function ta(t){return(t%=Jo)<0&&(t+=Jo),t}var ea=2*Math.PI;function ra(t,e,r,i,n,o,a,s,h){if(0===a)return!1;var l=a;s-=t,h-=e;var u=Math.sqrt(s*s+h*h);if(u-l>r||u+l<r)return!1;if(Math.abs(i-n)%ea<1e-4)return!0;if(o){var f=i;i=ta(n),n=ta(f)}else i=ta(i),n=ta(n);i>n&&(n+=ea);var c=Math.atan2(h,s);return c<0&&(c+=ea),c>=i&&c<=n||c+ea>=i&&c+ea<=n}function ia(t,e,r,i,n,o){if(o>e&&o>i||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!==a&&0!==a||(s=i<e?.5:-.5);var h=a*(r-t)+t;return h===n?Infinity:h>n?s:0}var na=Zo.CMD,oa=2*Math.PI;var aa=[-1,-1,-1],sa=[-1,-1];function ha(t,e,r,i,n,o,a,s,h,l){if(l>e&&l>i&&l>o&&l>s||l<e&&l<i&&l<o&&l<s)return 0;var u,f=vr(e,i,o,s,l,aa);if(0===f)return 0;for(var c=0,p=-1,d=void 0,v=void 0,y=0;y<f;y++){var g=aa[y],_=0===g||1===g?.5:1;pr(t,r,n,a,g)<h||(p<0&&(p=yr(e,i,o,s,sa),sa[1]<sa[0]&&p>1&&(u=void 0,u=sa[0],sa[0]=sa[1],sa[1]=u),d=pr(e,i,o,s,sa[0]),p>1&&(v=pr(e,i,o,s,sa[1]))),2===p?g<sa[0]?c+=d<e?_:-_:g<sa[1]?c+=v<d?_:-_:c+=s<v?_:-_:g<sa[0]?c+=d<e?_:-_:c+=s<d?_:-_)}return c}function la(t,e,r,i,n,o,a,s){if(s>e&&s>i&&s>o||s<e&&s<i&&s<o)return 0;var h=function(t,e,r,i,n){var o=t-2*e+r,a=2*(e-t),s=t-i,h=0;if(fr(o))cr(a)&&(u=-s/a)>=0&&u<=1&&(n[h++]=u);else{var l=a*a-4*o*s;if(fr(l))(u=-a/(2*o))>=0&&u<=1&&(n[h++]=u);else if(l>0){var u,f=nr(l),c=(-a-f)/(2*o);(u=(-a+f)/(2*o))>=0&&u<=1&&(n[h++]=u),c>=0&&c<=1&&(n[h++]=c)}}return h}(e,i,o,s,aa);if(0===h)return 0;var l=br(e,i,o);if(l>=0&&l<=1){for(var u=0,f=xr(e,i,o,l),c=0;c<h;c++){var p=0===aa[c]||1===aa[c]?.5:1;xr(t,r,n,aa[c])<a||(aa[c]<l?u+=f<e?p:-p:u+=o<f?p:-p)}return u}p=0===aa[0]||1===aa[0]?.5:1;return xr(t,r,n,aa[0])<a?0:o<e?p:-p}function ua(t,e,r,i,n,o,a,s){if((s-=e)>r||s<-r)return 0;var h=Math.sqrt(r*r-s*s);aa[0]=-h,aa[1]=h;var l=Math.abs(i-n);if(l<1e-4)return 0;if(l>=oa-1e-4){i=0,n=oa;var u=o?1:-1;return a>=aa[0]+t&&a<=aa[1]+t?u:0}if(i>n){var f=i;i=n,n=f}i<0&&(i+=oa,n+=oa);for(var c=0,p=0;p<2;p++){var d=aa[p];if(d+t>a){var v=Math.atan2(s,d);u=o?1:-1;v<0&&(v=oa+v),(v>=i&&v<=n||v+oa>=i&&v+oa<=n)&&(v>Math.PI/2&&v<1.5*Math.PI&&(u=-u),c+=u)}}return c}function fa(t,e,r,i,n){for(var o,a,s,h,l=t.data,u=t.len(),f=0,c=0,p=0,d=0,v=0,y=0;y<u;){var g=l[y++],_=1===y;switch(g===na.M&&y>1&&(r||(f+=ia(c,p,d,v,i,n))),_&&(d=c=l[y],v=p=l[y+1]),g){case na.M:c=d=l[y++],p=v=l[y++];break;case na.L:if(r){if(Ko(c,p,l[y],l[y+1],e,i,n))return!0}else f+=ia(c,p,l[y],l[y+1],i,n)||0;c=l[y++],p=l[y++];break;case na.C:if(r){if(Qo(c,p,l[y++],l[y++],l[y++],l[y++],l[y],l[y+1],e,i,n))return!0}else f+=ha(c,p,l[y++],l[y++],l[y++],l[y++],l[y],l[y+1],i,n)||0;c=l[y++],p=l[y++];break;case na.Q:if(r){if($o(c,p,l[y++],l[y++],l[y],l[y+1],e,i,n))return!0}else f+=la(c,p,l[y++],l[y++],l[y],l[y+1],i,n)||0;c=l[y++],p=l[y++];break;case na.A:var m=l[y++],x=l[y++],w=l[y++],b=l[y++],k=l[y++],S=l[y++];y+=1;var T=!!(1-l[y++]);o=Math.cos(k)*w+m,a=Math.sin(k)*b+x,_?(d=o,v=a):f+=ia(c,p,o,a,i,n);var C=(i-m)*b/w+m;if(r){if(ra(m,x,b,k,k+S,T,e,C,n))return!0}else f+=ua(m,x,b,k,k+S,T,C,n);c=Math.cos(k+S)*w+m,p=Math.sin(k+S)*b+x;break;case na.R:if(d=c=l[y++],v=p=l[y++],o=d+l[y++],a=v+l[y++],r){if(Ko(d,v,o,v,e,i,n)||Ko(o,v,o,a,e,i,n)||Ko(o,a,d,a,e,i,n)||Ko(d,a,d,v,e,i,n))return!0}else f+=ia(o,v,o,a,i,n),f+=ia(d,a,d,v,i,n);break;case na.Z:if(r){if(Ko(c,p,d,v,e,i,n))return!0}else f+=ia(c,p,d,v,i,n);c=d,p=v}}return r||(s=p,h=v,Math.abs(s-h)<1e-4)||(f+=ia(c,p,d,v,i,n)||0),0!==f}var ca=C({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},ho),pa={style:C({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},lo.style)},da=cn.concat(["invisible","culling","z","z2","zlevel","parent"]),va=function(e){function r(t){return e.call(this,t)||this}var i;return t(r,e),r.prototype.update=function(){var t=this;e.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new r;n.buildPath===r.prototype.buildPath&&(n.buildPath=function(e){t.buildPath(e,t.shape)}),n.silent=!0;var o=n.style;for(var a in i)o[a]!==i[a]&&(o[a]=i[a]);o.fill=i.fill?i.decal:null,o.decal=null,o.shadowColor=null,i.strokeFirst&&(o.stroke=null);for(var s=0;s<da.length;++s)n[da[s]]=this[da[s]];n.__dirty|=1}else this._decalEl&&(this._decalEl=null)},r.prototype.getDecalElement=function(){return this._decalEl},r.prototype._init=function(t){var r=B(t);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var n=0;n<r.length;n++){var o=r[n],a=t[o];"style"===o?this.style?T(this.style,a):this.useStyle(a):"shape"===o?T(this.shape,a):e.prototype.attrKV.call(this,o,a)}this.style||this.useStyle({})},r.prototype.getDefaultStyle=function(){return null},r.prototype.getDefaultShape=function(){return{}},r.prototype.canBeInsideText=function(){return this.hasFill()},r.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if(X(t)){var e=ti(t,0);return e>.5?rn:e>.2?"#eee":nn}if(t)return nn}return rn},r.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(X(e)){var r=this.__zr;if(!(!r||!r.isDarkMode())===ti(t,0)<.4)return e}},r.prototype.buildPath=function(t,e,r){},r.prototype.pathUpdated=function(){this.__dirty&=-5},r.prototype.getUpdatedPathProxy=function(t){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},r.prototype.createPathProxy=function(){this.path=new Zo(!1)},r.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))},r.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},r.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,r=!t;if(r){var i=!1;this.path||(i=!0,this.createPathProxy());var n=this.path;(i||4&this.__dirty)&&(n.beginPath(),this.buildPath(n,this.shape,!1),this.pathUpdated()),t=n.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||r){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var h=this.strokeContainThreshold;s=Math.max(s,null==h?4:h)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},r.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),n=this.style;if(t=r[0],e=r[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=n.lineWidth,s=n.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),function(t,e,r,i){return fa(t,e,!0,r,i)}(o,a/s,t,e)))return!0}if(this.hasFill())return function(t,e,r){return fa(t,0,!1,e,r)}(o,t,e)}return!1},r.prototype.dirtyShape=function(){this.__dirty|=4,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},r.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},r.prototype.animateShape=function(t){return this.animate("shape",t)},r.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},r.prototype.attrKV=function(t,r){"shape"===t?this.setShape(r):e.prototype.attrKV.call(this,t,r)},r.prototype.setShape=function(t,e){var r=this.shape;return r||(r=this.shape={}),"string"==typeof t?r[t]=e:T(r,t),this.dirtyShape(),this},r.prototype.shapeChanged=function(){return!!(4&this.__dirty)},r.prototype.createStyle=function(t){return dt(ca,t)},r.prototype._innerSaveToNormal=function(t){e.prototype._innerSaveToNormal.call(this,t);var r=this._normalState;t.shape&&!r.shape&&(r.shape=T({},this.shape))},r.prototype._applyStateObj=function(t,r,i,n,o,a){e.prototype._applyStateObj.call(this,t,r,i,n,o,a);var s,h=!(r&&n);if(r&&r.shape?o?n?s=r.shape:(s=T({},i.shape),T(s,r.shape)):(s=T({},n?this.shape:i.shape),T(s,r.shape)):h&&(s=i.shape),s)if(o){this.shape=T({},this.shape);for(var l={},u=B(s),f=0;f<u.length;f++){var c=u[f];"object"==typeof s[c]?this.shape[c]=s[c]:l[c]=s[c]}this._transitionState(t,{shape:l},a)}else this.shape=s,this.dirtyShape()},r.prototype._mergeStates=function(t){for(var r,i=e.prototype._mergeStates.call(this,t),n=0;n<t.length;n++){var o=t[n];o.shape&&(r=r||{},this._mergeStyle(r,o.shape))}return r&&(i.shape=r),i},r.prototype.getAnimationStyleProps=function(){return pa},r.prototype.isZeroArea=function(){return!1},r.extend=function(e){var i=function(r){function i(t){var i=r.call(this,t)||this;return e.init&&e.init.call(i,t),i}return t(i,r),i.prototype.getDefaultStyle=function(){return b(e.style)},i.prototype.getDefaultShape=function(){return b(e.shape)},i}(r);for(var n in e)"function"==typeof e[n]&&(i.prototype[n]=e[n]);return i},r.initDefaultProps=((i=r.prototype).type="path",i.strokeContainThreshold=5,i.segmentIgnoreThreshold=0,i.subPixelOptimize=!1,i.autoBatch=!1,void(i.__dirty=7)),r}(co),ya=C({strokeFirst:!0,font:n,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},ca),ga=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),r.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},r.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},r.prototype.createStyle=function(t){return dt(ya,t)},r.prototype.setBoundingRect=function(t){this._rect=t},r.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+="":e="";var r=gn(e,t.font,t.textAlign,t.textBaseline);if(r.x+=t.x||0,r.y+=t.y||0,this.hasStroke()){var i=t.lineWidth;r.x-=i/2,r.y-=i/2,r.width+=i,r.height+=i}this._rect=r}return this._rect},r.initDefaultProps=void(r.prototype.dirtyRectTolerance=10),r}(co);ga.prototype.type="tspan";var _a=C({x:0,y:0},ho),ma={style:C({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},lo.style)};var xa=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),r.prototype.createStyle=function(t){return dt(_a,t)},r.prototype._getSize=function(t){var e=this.style,r=e[t];if(null!=r)return r;var i,n=(i=e.image)&&"string"!=typeof i&&i.width&&i.height?e.image:this.__image;if(!n)return 0;var o="width"===t?"height":"width",a=e[o];return null==a?n[t]:n[t]/n[o]*a},r.prototype.getWidth=function(){return this._getSize("width")},r.prototype.getHeight=function(){return this._getSize("height")},r.prototype.getAnimationStyleProps=function(){return ma},r.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new Oe(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},r}(co);xa.prototype.type="image";var wa=Math.round;function ba(t,e,r){if(e){var i=e.x1,n=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=n,t.y1=o,t.y2=a;var s=r&&r.lineWidth;return s?(wa(2*i)===wa(2*n)&&(t.x1=t.x2=Sa(i,s,!0)),wa(2*o)===wa(2*a)&&(t.y1=t.y2=Sa(o,s,!0)),t):t}}function ka(t,e,r){if(e){var i=e.x,n=e.y,o=e.width,a=e.height;t.x=i,t.y=n,t.width=o,t.height=a;var s=r&&r.lineWidth;return s?(t.x=Sa(i,s,!0),t.y=Sa(n,s,!0),t.width=Math.max(Sa(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(Sa(n+a,s,!1)-t.y,0===a?0:1),t):t}}function Sa(t,e,r){if(!e)return t;var i=wa(2*t);return(i+wa(e))%2==0?i/2:(i+(r?1:-1))/2}var Ta=function(){return function(){this.x=0,this.y=0,this.width=0,this.height=0}}(),Ca={},Pa=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new Ta},r.prototype.buildPath=function(t,e){var r,i,n,o;if(this.subPixelOptimize){var a=ka(Ca,e,this.style);r=a.x,i=a.y,n=a.width,o=a.height,a.r=e.r,e=a}else r=e.x,i=e.y,n=e.width,o=e.height;e.r?function(t,e){var r,i,n,o,a,s=e.x,h=e.y,l=e.width,u=e.height,f=e.r;l<0&&(s+=l,l=-l),u<0&&(h+=u,u=-u),"number"==typeof f?r=i=n=o=f:f instanceof Array?1===f.length?r=i=n=o=f[0]:2===f.length?(r=n=f[0],i=o=f[1]):3===f.length?(r=f[0],i=o=f[1],n=f[2]):(r=f[0],i=f[1],n=f[2],o=f[3]):r=i=n=o=0,r+i>l&&(r*=l/(a=r+i),i*=l/a),n+o>l&&(n*=l/(a=n+o),o*=l/a),i+n>u&&(i*=u/(a=i+n),n*=u/a),r+o>u&&(r*=u/(a=r+o),o*=u/a),t.moveTo(s+r,h),t.lineTo(s+l-i,h),0!==i&&t.arc(s+l-i,h+i,i,-Math.PI/2,0),t.lineTo(s+l,h+u-n),0!==n&&t.arc(s+l-n,h+u-n,n,0,Math.PI/2),t.lineTo(s+o,h+u),0!==o&&t.arc(s+o,h+u-o,o,Math.PI/2,Math.PI),t.lineTo(s,h+r),0!==r&&t.arc(s+r,h+r,r,Math.PI,1.5*Math.PI)}(t,e):t.rect(r,i,n,o)},r.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},r}(va);Pa.prototype.type="rect";var Ma={fill:"#000"},Aa={style:C({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},lo.style)},La=function(e){function r(t){var r=e.call(this)||this;return r.type="text",r._children=[],r._defaultStyle=Ma,r.attr(t),r}return t(r,e),r.prototype.childrenRef=function(){return this._children},r.prototype.update=function(){e.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var r=this._children[t];r.zlevel=this.zlevel,r.z=this.z,r.z2=this.z2,r.culling=this.culling,r.cursor=this.cursor,r.invisible=this.invisible}},r.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):e.prototype.updateTransform.call(this)},r.prototype.getLocalTransform=function(t){var r=this.innerTransformable;return r?r.getLocalTransform(t):e.prototype.getLocalTransform.call(this,t)},r.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),e.prototype.getComputedTransform.call(this)},r.prototype._updateSubTexts=function(){var t;this._childCursor=0,Ba(t=this.style),D(t.rich,Ba),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},r.prototype.addSelfToZr=function(t){e.prototype.addSelfToZr.call(this,t);for(var r=0;r<this._children.length;r++)this._children[r].__zr=t},r.prototype.removeSelfFromZr=function(t){e.prototype.removeSelfFromZr.call(this,t);for(var r=0;r<this._children.length;r++)this._children[r].__zr=null},r.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new Oe(0,0,0,0),e=this._children,r=[],i=null,n=0;n<e.length;n++){var o=e[n],a=o.getBoundingRect(),s=o.getLocalTransform(r);s?(t.copy(a),t.applyTransform(s),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},r.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Ma},r.prototype.setTextContent=function(t){},r.prototype._mergeStyle=function(t,e){if(!e)return t;var r=e.rich,i=t.rich||r&&{};return T(t,e),r&&i?(this._mergeRich(i,r),t.rich=i):i&&(t.rich=i),t},r.prototype._mergeRich=function(t,e){for(var r=B(e),i=0;i<r.length;i++){var n=r[i];t[n]=t[n]||{},T(t[n],e[n])}},r.prototype.getAnimationStyleProps=function(){return Aa},r.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},r.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||n,r=t.padding,i=function(t,e){null!=t&&(t+="");var r,i=e.overflow,n=e.padding,o=e.font,a="truncate"===i,s=xn(o),h=tt(e.lineHeight,s),l=!!e.backgroundColor,u="truncate"===e.lineOverflow,f=!1,c=e.width,p=(r=null==c||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?ao(t,e.font,c,"breakAll"===i,0).lines:[]).length*h,d=tt(e.height,p);if(p>d&&u){var v=Math.floor(d/h);f=f||r.length>v,r=r.slice(0,v)}if(t&&a&&null!=c)for(var y=Qn(c,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),g={},_=0;_<r.length;_++)$n(g,r[_],y),r[_]=g.textLine,f=f||g.isTruncated;var m=d,x=0;for(_=0;_<r.length;_++)x=Math.max(vn(r[_],o),x);null==c&&(c=x);var w=x;return n&&(m+=n[0]+n[2],w+=n[1]+n[3],c+=n[1]+n[3]),l&&(w=c),{lines:r,height:d,outerWidth:w,outerHeight:m,lineHeight:h,calculatedLineHeight:s,contentWidth:x,contentHeight:p,width:c,isTruncated:f}}(Wa(t),t),o=Xa(t),a=!!t.backgroundColor,s=i.outerHeight,h=i.outerWidth,l=i.contentWidth,u=i.lines,f=i.lineHeight,c=this._defaultStyle;this.isTruncated=!!i.isTruncated;var p=t.x||0,d=t.y||0,v=t.align||c.align||"left",y=t.verticalAlign||c.verticalAlign||"top",g=p,_=mn(d,i.contentHeight,y);if(o||r){var m=_n(p,h,v),x=mn(d,s,y);o&&this._renderBackground(t,t,m,x,h,s)}_+=f/2,r&&(g=Ea(p,v,r),"top"===y?_+=r[0]:"bottom"===y&&(_-=r[2]));for(var w=0,b=!1,k=(Ha("fill"in t?t.fill:(b=!0,c.fill))),S=(Na("stroke"in t?t.stroke:a||c.autoStroke&&!b?null:(w=2,c.stroke))),T=t.textShadowBlur>0,C=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),P=i.calculatedLineHeight,M=0;M<u.length;M++){var A=this._getOrCreateChild(ga),L=A.createStyle();A.useStyle(L),L.text=u[M],L.x=g,L.y=_,L.textAlign=v,L.textBaseline="middle",L.opacity=t.opacity,L.strokeFirst=!0,T&&(L.shadowBlur=t.textShadowBlur||0,L.shadowColor=t.textShadowColor||"transparent",L.shadowOffsetX=t.textShadowOffsetX||0,L.shadowOffsetY=t.textShadowOffsetY||0),L.stroke=S,L.fill=k,S&&(L.lineWidth=t.lineWidth||w,L.lineDash=t.lineDash,L.lineDashOffset=t.lineDashOffset||0),L.font=e,Ra(L,t),_+=f,C&&A.setBoundingRect(new Oe(_n(L.x,l,L.textAlign),mn(L.y,P,L.textBaseline),l,P))}},r.prototype._updateRichTexts=function(){var t=this.style,e=function(t,e){var r=new ro;if(null!=t&&(t+=""),!t)return r;for(var i,n=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==n?null:{width:n,accumWidth:0,breakAll:"breakAll"===a},h=Gn.lastIndex=0;null!=(i=Gn.exec(t));){var l=i.index;l>h&&io(r,t.substring(h,l),e,s),io(r,i[2],e,s,i[1]),h=Gn.lastIndex}h<t.length&&io(r,t.substring(h,t.length),e,s);var u=[],f=0,c=0,p=e.padding,d="truncate"===a,v="truncate"===e.lineOverflow,y={};function g(t,e,r){t.width=e,t.lineHeight=r,f+=r,c=Math.max(c,e)}t:for(var _=0;_<r.lines.length;_++){for(var m=r.lines[_],x=0,w=0,b=0;b<m.tokens.length;b++){var k=(O=m.tokens[b]).styleName&&e.rich[O.styleName]||{},S=O.textPadding=k.padding,T=S?S[1]+S[3]:0,C=O.font=k.font||e.font;O.contentHeight=xn(C);var P=tt(k.height,O.contentHeight);if(O.innerHeight=P,S&&(P+=S[0]+S[2]),O.height=P,O.lineHeight=et(k.lineHeight,e.lineHeight,P),O.align=k&&k.align||e.align,O.verticalAlign=k&&k.verticalAlign||"middle",v&&null!=o&&f+O.lineHeight>o){var M=r.lines.length;b>0?(m.tokens=m.tokens.slice(0,b),g(m,w,x),r.lines=r.lines.slice(0,_+1)):r.lines=r.lines.slice(0,_),r.isTruncated=r.isTruncated||r.lines.length<M;break t}var A=k.width,L=null==A||"auto"===A;if("string"==typeof A&&"%"===A.charAt(A.length-1))O.percentWidth=A,u.push(O),O.contentWidth=vn(O.text,C);else{if(L){var I=k.backgroundColor,D=I&&I.image;D&&Un(D=qn(D))&&(O.width=Math.max(O.width,D.width*P/D.height))}var z=d&&null!=n?n-w:null;null!=z&&z<O.width?!L||z<T?(O.text="",O.width=O.contentWidth=0):(Kn(y,O.text,z-T,C,e.ellipsis,{minChar:e.truncateMinChar}),O.text=y.text,r.isTruncated=r.isTruncated||y.isTruncated,O.width=O.contentWidth=vn(O.text,C)):O.contentWidth=vn(O.text,C)}O.width+=T,w+=O.width,k&&(x=Math.max(x,O.lineHeight))}g(m,w,x)}for(r.outerWidth=r.width=tt(n,c),r.outerHeight=r.height=tt(o,f),r.contentHeight=f,r.contentWidth=c,p&&(r.outerWidth+=p[1]+p[3],r.outerHeight+=p[0]+p[2]),_=0;_<u.length;_++){var O,R=(O=u[_]).percentWidth;O.width=parseInt(R,10)/100*r.width}return r}(Wa(t),t),r=e.width,i=e.outerWidth,n=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,h=this._defaultStyle,l=t.align||h.align,u=t.verticalAlign||h.verticalAlign;this.isTruncated=!!e.isTruncated;var f=_n(a,i,l),c=mn(s,n,u),p=f,d=c;o&&(p+=o[3],d+=o[0]);var v=p+r;Xa(t)&&this._renderBackground(t,t,f,c,i,n);for(var y=!!t.backgroundColor,g=0;g<e.lines.length;g++){for(var _=e.lines[g],m=_.tokens,x=m.length,w=_.lineHeight,b=_.width,k=0,S=p,T=v,C=x-1,P=void 0;k<x&&(!(P=m[k]).align||"left"===P.align);)this._placeToken(P,t,w,d,S,"left",y),b-=P.width,S+=P.width,k++;for(;C>=0&&"right"===(P=m[C]).align;)this._placeToken(P,t,w,d,T,"right",y),b-=P.width,T-=P.width,C--;for(S+=(r-(S-p)-(v-T)-b)/2;k<=C;)P=m[k],this._placeToken(P,t,w,d,S+P.width/2,"center",y),S+=P.width,k++;d+=w}},r.prototype._placeToken=function(t,e,r,i,o,a,s){var h=e.rich[t.styleName]||{};h.text=t.text;var l=t.verticalAlign,u=i+r/2;"top"===l?u=i+t.height/2:"bottom"===l&&(u=i+r-t.height/2),!t.isLineHolder&&Xa(h)&&this._renderBackground(h,e,"right"===a?o-t.width:"center"===a?o-t.width/2:o,u-t.height/2,t.width,t.height);var f=!!h.backgroundColor,c=t.textPadding;c&&(o=Ea(o,a,c),u-=t.height/2-c[0]-t.innerHeight/2);var p=this._getOrCreateChild(ga),d=p.createStyle();p.useStyle(d);var v=this._defaultStyle,y=!1,g=0,_=Ha("fill"in h?h.fill:"fill"in e?e.fill:(y=!0,v.fill)),m=Na("stroke"in h?h.stroke:"stroke"in e?e.stroke:f||s||v.autoStroke&&!y?null:(g=2,v.stroke)),x=h.textShadowBlur>0||e.textShadowBlur>0;d.text=t.text,d.x=o,d.y=u,x&&(d.shadowBlur=h.textShadowBlur||e.textShadowBlur||0,d.shadowColor=h.textShadowColor||e.textShadowColor||"transparent",d.shadowOffsetX=h.textShadowOffsetX||e.textShadowOffsetX||0,d.shadowOffsetY=h.textShadowOffsetY||e.textShadowOffsetY||0),d.textAlign=a,d.textBaseline="middle",d.font=t.font||n,d.opacity=et(h.opacity,e.opacity,1),Ra(d,h),m&&(d.lineWidth=et(h.lineWidth,e.lineWidth,g),d.lineDash=tt(h.lineDash,e.lineDash),d.lineDashOffset=e.lineDashOffset||0,d.stroke=m),_&&(d.fill=_);var w=t.contentWidth,b=t.contentHeight;p.setBoundingRect(new Oe(_n(d.x,w,d.textAlign),mn(d.y,b,d.textBaseline),w,b))},r.prototype._renderBackground=function(t,e,r,i,n,o){var a,s,h,l=t.backgroundColor,u=t.borderWidth,f=t.borderColor,c=l&&l.image,p=l&&!c,d=t.borderRadius,v=this;if(p||t.lineHeight||u&&f){(a=this._getOrCreateChild(Pa)).useStyle(a.createStyle()),a.style.fill=null;var y=a.shape;y.x=r,y.y=i,y.width=n,y.height=o,y.r=d,a.dirtyShape()}if(p)(h=a.style).fill=l||null,h.fillOpacity=tt(t.fillOpacity,1);else if(c){(s=this._getOrCreateChild(xa)).onload=function(){v.dirtyStyle()};var g=s.style;g.image=l.image,g.x=r,g.y=i,g.width=n,g.height=o}u&&f&&((h=a.style).lineWidth=u,h.stroke=f,h.strokeOpacity=tt(t.strokeOpacity,1),h.lineDash=t.borderDash,h.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill()&&a.hasStroke()&&(h.strokeFirst=!0,h.lineWidth*=2));var _=(a||s).style;_.shadowBlur=t.shadowBlur||0,_.shadowColor=t.shadowColor||"transparent",_.shadowOffsetX=t.shadowOffsetX||0,_.shadowOffsetY=t.shadowOffsetY||0,_.opacity=et(t.opacity,e.opacity,1)},r.makeFont=function(t){var e="";return Fa(t)&&(e=[t.fontStyle,t.fontWeight,Oa(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&ot(e)||t.textFont||t.font},r}(co),Ia={left:!0,right:1,center:1},Da={top:1,bottom:1,middle:1},za=["fontStyle","fontWeight","fontSize","fontFamily"];function Oa(t){return"string"!=typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?"12px":t+"px":t}function Ra(t,e){for(var r=0;r<za.length;r++){var i=za[r],n=e[i];null!=n&&(t[i]=n)}}function Fa(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}function Ba(t){if(t){t.font=La.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||Ia[e]?e:"left";var r=t.verticalAlign;"center"===r&&(r="middle"),t.verticalAlign=null==r||Da[r]?r:"top",t.padding&&(t.padding=it(t.padding))}}function Na(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ha(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ea(t,e,r){return"right"===e?t-r[1]:"center"===e?t+r[3]/2-r[1]/2:t+r[3]}function Wa(t){var e=t.text;return null!=e&&(e+=""),e}function Xa(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var ja=Zo.CMD,qa=[[],[],[]],Ya=Math.sqrt,Va=Math.atan2;function Ua(t,e){if(e){var r,i,n,o,a,s,h=t.data,l=t.len(),u=ja.M,f=ja.C,c=ja.L,p=ja.R,d=ja.A,v=ja.Q;for(n=0,o=0;n<l;){switch(r=h[n++],o=n,i=0,r){case u:case c:i=1;break;case f:i=3;break;case v:i=2;break;case d:var y=e[4],g=e[5],_=Ya(e[0]*e[0]+e[1]*e[1]),m=Ya(e[2]*e[2]+e[3]*e[3]),x=Va(-e[1]/m,e[0]/_);h[n]*=_,h[n++]+=y,h[n]*=m,h[n++]+=g,h[n++]*=_,h[n++]*=m,h[n++]+=x,h[n++]+=x,o=n+=2;break;case p:s[0]=h[n++],s[1]=h[n++],Nt(s,s,e),h[o++]=s[0],h[o++]=s[1],s[0]+=h[n++],s[1]+=h[n++],Nt(s,s,e),h[o++]=s[0],h[o++]=s[1]}for(a=0;a<i;a++){var w=qa[a];w[0]=h[n++],w[1]=h[n++],Nt(w,w,e),h[o++]=w[0],h[o++]=w[1]}}t.increaseVersion()}}var Ga=Math.sqrt,Za=Math.sin,Ka=Math.cos,Qa=Math.PI;function $a(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function Ja(t,e){return(t[0]*e[0]+t[1]*e[1])/($a(t)*$a(e))}function ts(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Ja(t,e))}function es(t,e,r,i,n,o,a,s,h,l,u){var f=h*(Qa/180),c=Ka(f)*(t-r)/2+Za(f)*(e-i)/2,p=-1*Za(f)*(t-r)/2+Ka(f)*(e-i)/2,d=c*c/(a*a)+p*p/(s*s);d>1&&(a*=Ga(d),s*=Ga(d));var v=(n===o?-1:1)*Ga((a*a*(s*s)-a*a*(p*p)-s*s*(c*c))/(a*a*(p*p)+s*s*(c*c)))||0,y=v*a*p/s,g=v*-s*c/a,_=(t+r)/2+Ka(f)*y-Za(f)*g,m=(e+i)/2+Za(f)*y+Ka(f)*g,x=ts([1,0],[(c-y)/a,(p-g)/s]),w=[(c-y)/a,(p-g)/s],b=[(-1*c-y)/a,(-1*p-g)/s],k=ts(w,b);if(Ja(w,b)<=-1&&(k=Qa),Ja(w,b)>=1&&(k=0),k<0){var S=Math.round(k/Qa*1e6)/1e6;k=2*Qa+S%2*Qa}u.addData(l,_,m,a,s,x,k,f,o)}var rs=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,is=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;var ns=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),r.prototype.applyTransform=function(t){},r}(va);function os(t){return null!=t.setData}function as(t,e){var r=function(t){var e=new Zo;if(!t)return e;var r,i=0,n=0,o=i,a=n,s=Zo.CMD,h=t.match(rs);if(!h)return e;for(var l=0;l<h.length;l++){for(var u=h[l],f=u.charAt(0),c=void 0,p=u.match(is)||[],d=p.length,v=0;v<d;v++)p[v]=parseFloat(p[v]);for(var y=0;y<d;){var g=void 0,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0,k=void 0,S=i,T=n,C=void 0,P=void 0;switch(f){case"l":i+=p[y++],n+=p[y++],c=s.L,e.addData(c,i,n);break;case"L":i=p[y++],n=p[y++],c=s.L,e.addData(c,i,n);break;case"m":i+=p[y++],n+=p[y++],c=s.M,e.addData(c,i,n),o=i,a=n,f="l";break;case"M":i=p[y++],n=p[y++],c=s.M,e.addData(c,i,n),o=i,a=n,f="L";break;case"h":i+=p[y++],c=s.L,e.addData(c,i,n);break;case"H":i=p[y++],c=s.L,e.addData(c,i,n);break;case"v":n+=p[y++],c=s.L,e.addData(c,i,n);break;case"V":n=p[y++],c=s.L,e.addData(c,i,n);break;case"C":c=s.C,e.addData(c,p[y++],p[y++],p[y++],p[y++],p[y++],p[y++]),i=p[y-2],n=p[y-1];break;case"c":c=s.C,e.addData(c,p[y++]+i,p[y++]+n,p[y++]+i,p[y++]+n,p[y++]+i,p[y++]+n),i+=p[y-2],n+=p[y-1];break;case"S":g=i,_=n,C=e.len(),P=e.data,r===s.C&&(g+=i-P[C-4],_+=n-P[C-3]),c=s.C,S=p[y++],T=p[y++],i=p[y++],n=p[y++],e.addData(c,g,_,S,T,i,n);break;case"s":g=i,_=n,C=e.len(),P=e.data,r===s.C&&(g+=i-P[C-4],_+=n-P[C-3]),c=s.C,S=i+p[y++],T=n+p[y++],i+=p[y++],n+=p[y++],e.addData(c,g,_,S,T,i,n);break;case"Q":S=p[y++],T=p[y++],i=p[y++],n=p[y++],c=s.Q,e.addData(c,S,T,i,n);break;case"q":S=p[y++]+i,T=p[y++]+n,i+=p[y++],n+=p[y++],c=s.Q,e.addData(c,S,T,i,n);break;case"T":g=i,_=n,C=e.len(),P=e.data,r===s.Q&&(g+=i-P[C-4],_+=n-P[C-3]),i=p[y++],n=p[y++],c=s.Q,e.addData(c,g,_,i,n);break;case"t":g=i,_=n,C=e.len(),P=e.data,r===s.Q&&(g+=i-P[C-4],_+=n-P[C-3]),i+=p[y++],n+=p[y++],c=s.Q,e.addData(c,g,_,i,n);break;case"A":m=p[y++],x=p[y++],w=p[y++],b=p[y++],k=p[y++],es(S=i,T=n,i=p[y++],n=p[y++],b,k,m,x,w,c=s.A,e);break;case"a":m=p[y++],x=p[y++],w=p[y++],b=p[y++],k=p[y++],es(S=i,T=n,i+=p[y++],n+=p[y++],b,k,m,x,w,c=s.A,e)}}"z"!==f&&"Z"!==f||(c=s.Z,e.addData(c),i=o,n=a),r=c}return e.toStatic(),e}(t),i=T({},e);return i.buildPath=function(t){if(os(t)){t.setData(r.data),(e=t.getContext())&&t.rebuildPath(e,1)}else{var e=t;r.rebuildPath(e,1)}},i.applyTransform=function(t){Ua(r,t),this.dirtyShape()},i}function ss(t,e){return new ns(as(t,e))}function hs(e,r){var i=as(e,r);return function(e){function r(t){var r=e.call(this,t)||this;return r.applyTransform=i.applyTransform,r.buildPath=i.buildPath,r}return t(r,e),r}(ns)}function ls(t,e){for(var r=[],i=t.length,n=0;n<i;n++){var o=t[n];r.push(o.getUpdatedPathProxy(!0))}var a=new va(e);return a.createPathProxy(),a.buildPath=function(t){if(os(t)){t.appendPath(r);var e=t.getContext();e&&t.rebuildPath(e,1)}},a}function us(t,e){e=e||{};var r=new va;return t.shape&&r.setShape(t.shape),r.setStyle(t.style),e.bakeTransform?Ua(r.path,t.getComputedTransform()):e.toLocal?r.setLocalTransform(t.getComputedTransform()):r.copyTransform(t),r.buildPath=t.buildPath,r.applyTransform=r.applyTransform,r.z=t.z,r.z2=t.z2,r.zlevel=t.zlevel,r}var fs=function(){return function(){this.cx=0,this.cy=0,this.r=0}}(),cs=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new fs},r.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},r}(va);cs.prototype.type="circle";var ps=function(){return function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}}(),ds=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new ps},r.prototype.buildPath=function(t,e){var r=.5522848,i=e.cx,n=e.cy,o=e.rx,a=e.ry,s=o*r,h=a*r;t.moveTo(i-o,n),t.bezierCurveTo(i-o,n-h,i-s,n-a,i,n-a),t.bezierCurveTo(i+s,n-a,i+o,n-h,i+o,n),t.bezierCurveTo(i+o,n+h,i+s,n+a,i,n+a),t.bezierCurveTo(i-s,n+a,i-o,n+h,i-o,n),t.closePath()},r}(va);ds.prototype.type="ellipse";var vs=Math.PI,ys=2*vs,gs=Math.sin,_s=Math.cos,ms=Math.acos,xs=Math.atan2,ws=Math.abs,bs=Math.sqrt,ks=Math.max,Ss=Math.min,Ts=1e-4;function Cs(t,e,r,i,n,o,a){var s=t-r,h=e-i,l=(a?o:-o)/bs(s*s+h*h),u=l*h,f=-l*s,c=t+u,p=e+f,d=r+u,v=i+f,y=(c+d)/2,g=(p+v)/2,_=d-c,m=v-p,x=_*_+m*m,w=n-o,b=c*v-d*p,k=(m<0?-1:1)*bs(ks(0,w*w*x-b*b)),S=(b*m-_*k)/x,T=(-b*_-m*k)/x,C=(b*m+_*k)/x,P=(-b*_+m*k)/x,M=S-y,A=T-g,L=C-y,I=P-g;return M*M+A*A>L*L+I*I&&(S=C,T=P),{cx:S,cy:T,x0:-u,y0:-f,x1:S*(n/w-1),y1:T*(n/w-1)}}function Ps(t,e){var r,i=ks(e.r,0),n=ks(e.r0||0,0),o=i>0;if(o||n>0){if(o||(i=n,n=0),n>i){var a=i;i=n,n=a}var s=e.startAngle,h=e.endAngle;if(!isNaN(s)&&!isNaN(h)){var l=e.cx,u=e.cy,f=!!e.clockwise,c=ws(h-s),p=c>ys&&c%ys;if(p>Ts&&(c=p),i>Ts)if(c>ys-Ts)t.moveTo(l+i*_s(s),u+i*gs(s)),t.arc(l,u,i,s,h,!f),n>Ts&&(t.moveTo(l+n*_s(h),u+n*gs(h)),t.arc(l,u,n,h,s,f));else{var d=void 0,v=void 0,y=void 0,g=void 0,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0,k=void 0,S=void 0,T=void 0,C=void 0,P=void 0,M=void 0,A=void 0,L=i*_s(s),I=i*gs(s),D=n*_s(h),z=n*gs(h),O=c>Ts;if(O){var R=e.cornerRadius;R&&(d=(r=function(t){var e;if(E(t)){var r=t.length;if(!r)return t;e=1===r?[t[0],t[0],0,0]:2===r?[t[0],t[0],t[1],t[1]]:3===r?t.concat(t[2]):t}else e=[t,t,t,t];return e}(R))[0],v=r[1],y=r[2],g=r[3]);var F=ws(i-n)/2;if(_=Ss(F,y),m=Ss(F,g),x=Ss(F,d),w=Ss(F,v),S=b=ks(_,m),T=k=ks(x,w),(b>Ts||k>Ts)&&(C=i*_s(h),P=i*gs(h),M=n*_s(s),A=n*gs(s),c<vs)){var B=function(t,e,r,i,n,o,a,s){var h=r-t,l=i-e,u=a-n,f=s-o,c=f*h-u*l;if(!(c*c<Ts))return[t+(c=(u*(e-o)-f*(t-n))/c)*h,e+c*l]}(L,I,M,A,C,P,D,z);if(B){var N=L-B[0],H=I-B[1],W=C-B[0],X=P-B[1],j=1/gs(ms((N*W+H*X)/(bs(N*N+H*H)*bs(W*W+X*X)))/2),q=bs(B[0]*B[0]+B[1]*B[1]);S=Ss(b,(i-q)/(j+1)),T=Ss(k,(n-q)/(j-1))}}}if(O)if(S>Ts){var Y=Ss(y,S),V=Ss(g,S),U=Cs(M,A,L,I,i,Y,f),G=Cs(C,P,D,z,i,V,f);t.moveTo(l+U.cx+U.x0,u+U.cy+U.y0),S<b&&Y===V?t.arc(l+U.cx,u+U.cy,S,xs(U.y0,U.x0),xs(G.y0,G.x0),!f):(Y>0&&t.arc(l+U.cx,u+U.cy,Y,xs(U.y0,U.x0),xs(U.y1,U.x1),!f),t.arc(l,u,i,xs(U.cy+U.y1,U.cx+U.x1),xs(G.cy+G.y1,G.cx+G.x1),!f),V>0&&t.arc(l+G.cx,u+G.cy,V,xs(G.y1,G.x1),xs(G.y0,G.x0),!f))}else t.moveTo(l+L,u+I),t.arc(l,u,i,s,h,!f);else t.moveTo(l+L,u+I);if(n>Ts&&O)if(T>Ts){Y=Ss(d,T),U=Cs(D,z,C,P,n,-(V=Ss(v,T)),f),G=Cs(L,I,M,A,n,-Y,f);t.lineTo(l+U.cx+U.x0,u+U.cy+U.y0),T<k&&Y===V?t.arc(l+U.cx,u+U.cy,T,xs(U.y0,U.x0),xs(G.y0,G.x0),!f):(V>0&&t.arc(l+U.cx,u+U.cy,V,xs(U.y0,U.x0),xs(U.y1,U.x1),!f),t.arc(l,u,n,xs(U.cy+U.y1,U.cx+U.x1),xs(G.cy+G.y1,G.cx+G.x1),f),Y>0&&t.arc(l+G.cx,u+G.cy,Y,xs(G.y1,G.x1),xs(G.y0,G.x0),!f))}else t.lineTo(l+D,u+z),t.arc(l,u,n,h,s,f);else t.lineTo(l+D,u+z)}else t.moveTo(l,u);t.closePath()}}}var Ms=function(){return function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0}}(),As=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new Ms},r.prototype.buildPath=function(t,e){Ps(t,e)},r.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},r}(va);As.prototype.type="sector";var Ls=function(){return function(){this.cx=0,this.cy=0,this.r=0,this.r0=0}}(),Is=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new Ls},r.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=2*Math.PI;t.moveTo(r+e.r,i),t.arc(r,i,e.r,0,n,!1),t.moveTo(r+e.r0,i),t.arc(r,i,e.r0,0,n,!0)},r}(va);function Ds(t,e,r){var i=e.smooth,n=e.points;if(n&&n.length>=2){if(i){var o=function(t,e,r,i){var n,o,a,s,h=[],l=[],u=[],f=[];if(i){a=[Infinity,Infinity],s=[-Infinity,-Infinity];for(var c=0,p=t.length;c<p;c++)Ht(a,a,t[c]),Et(s,s,t[c]);Ht(a,a,i[0]),Et(s,s,i[1])}for(c=0,p=t.length;c<p;c++){var d=t[c];if(r)n=t[c?c-1:p-1],o=t[(c+1)%p];else{if(0===c||c===p-1){h.push(bt(t[c]));continue}n=t[c-1],o=t[c+1]}Ct(l,o,n),It(l,l,e);var v=zt(d,n),y=zt(d,o),g=v+y;0!==g&&(v/=g,y/=g),It(u,l,-v),It(f,l,y);var _=St([],d,u),m=St([],d,f);i&&(Et(_,_,a),Ht(_,_,s),Et(m,m,a),Ht(m,m,s)),h.push(_),h.push(m)}return r&&h.push(h.shift()),h}(n,i,r,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var a=n.length,s=0;s<(r?a:a-1);s++){var h=o[2*s],l=o[2*s+1],u=n[(s+1)%a];t.bezierCurveTo(h[0],h[1],l[0],l[1],u[0],u[1])}}else{t.moveTo(n[0][0],n[0][1]);s=1;for(var f=n.length;s<f;s++)t.lineTo(n[s][0],n[s][1])}r&&t.closePath()}}Is.prototype.type="ring";var zs=function(){return function(){this.points=null,this.smooth=0,this.smoothConstraint=null}}(),Os=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultShape=function(){return new zs},r.prototype.buildPath=function(t,e){Ds(t,e,!0)},r}(va);Os.prototype.type="polygon";var Rs=function(){return function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}}(),Fs=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},r.prototype.getDefaultShape=function(){return new Rs},r.prototype.buildPath=function(t,e){Ds(t,e,!1)},r}(va);Fs.prototype.type="polyline";var Bs={},Ns=function(){return function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}}(),Hs=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},r.prototype.getDefaultShape=function(){return new Ns},r.prototype.buildPath=function(t,e){var r,i,n,o;if(this.subPixelOptimize){var a=ba(Bs,e,this.style);r=a.x1,i=a.y1,n=a.x2,o=a.y2}else r=e.x1,i=e.y1,n=e.x2,o=e.y2;var s=e.percent;0!==s&&(t.moveTo(r,i),s<1&&(n=r*(1-s)+n*s,o=i*(1-s)+o*s),t.lineTo(n,o))},r.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},r}(va);Hs.prototype.type="line";var Es=[],Ws=function(){return function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}}();function Xs(t,e,r){var i=t.cpx2,n=t.cpy2;return null!=i||null!=n?[(r?dr:pr)(t.x1,t.cpx1,t.cpx2,t.x2,e),(r?dr:pr)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(r?wr:xr)(t.x1,t.cpx1,t.x2,e),(r?wr:xr)(t.y1,t.cpy1,t.y2,e)]}var js=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},r.prototype.getDefaultShape=function(){return new Ws},r.prototype.buildPath=function(t,e){var r=e.x1,i=e.y1,n=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,h=e.cpx2,l=e.cpy2,u=e.percent;0!==u&&(t.moveTo(r,i),null==h||null==l?(u<1&&(kr(r,a,n,u,Es),a=Es[1],n=Es[2],kr(i,s,o,u,Es),s=Es[1],o=Es[2]),t.quadraticCurveTo(a,s,n,o)):(u<1&&(gr(r,a,h,n,u,Es),a=Es[1],h=Es[2],n=Es[3],gr(i,s,l,o,u,Es),s=Es[1],l=Es[2],o=Es[3]),t.bezierCurveTo(a,s,h,l,n,o)))},r.prototype.pointAt=function(t){return Xs(this.shape,t,!1)},r.prototype.tangentAt=function(t){var e=Xs(this.shape,t,!0);return Dt(e,e)},r}(va);js.prototype.type="bezier-curve";var qs=function(){return function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0}}(),Ys=function(e){function r(t){return e.call(this,t)||this}return t(r,e),r.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},r.prototype.getDefaultShape=function(){return new qs},r.prototype.buildPath=function(t,e){var r=e.cx,i=e.cy,n=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,h=Math.cos(o),l=Math.sin(o);t.moveTo(h*n+r,l*n+i),t.arc(r,i,n,o,a,!s)},r}(va);Ys.prototype.type="arc";var Vs=function(e){function r(){var t=null!==e&&e.apply(this,arguments)||this;return t.type="compound",t}return t(r,e),r.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),r=0;r<t.length;r++)e=e||t[r].shapeChanged();e&&this.dirtyShape()},r.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),r=0;r<t.length;r++)t[r].path||t[r].createPathProxy(),t[r].path.setScale(e[0],e[1],t[r].segmentIgnoreThreshold)},r.prototype.buildPath=function(t,e){for(var r=e.paths||[],i=0;i<r.length;i++)r[i].buildPath(t,r[i].shape,!0)},r.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},r.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),va.prototype.getBoundingRect.call(this)},r}(va),Us=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}(),Gs=function(e){function r(t,r,i,n,o,a){var s=e.call(this,o)||this;return s.x=null==t?0:t,s.y=null==r?0:r,s.x2=null==i?1:i,s.y2=null==n?0:n,s.type="linear",s.global=a||!1,s}return t(r,e),r}(Us),Zs=function(e){function r(t,r,i,n,o){var a=e.call(this,n)||this;return a.x=null==t?.5:t,a.y=null==r?.5:r,a.r=null==i?.5:i,a.type="radial",a.global=o||!1,a}return t(r,e),r}(Us),Ks=[0,0],Qs=[0,0],$s=new Te,Js=new Te,th=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var r=0;r<4;r++)this._corners[r]=new Te;for(r=0;r<2;r++)this._axes[r]=new Te;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var r=this._corners,i=this._axes,n=t.x,o=t.y,a=n+t.width,s=o+t.height;if(r[0].set(n,o),r[1].set(a,o),r[2].set(a,s),r[3].set(n,s),e)for(var h=0;h<4;h++)r[h].transform(e);Te.sub(i[0],r[1],r[0]),Te.sub(i[1],r[3],r[0]),i[0].normalize(),i[1].normalize();for(h=0;h<2;h++)this._origin[h]=i[h].dot(r[0])},t.prototype.intersect=function(t,e){var r=!0,i=!e;return $s.set(Infinity,Infinity),Js.set(0,0),!this._intersectCheckOneSide(this,t,$s,Js,i,1)&&(r=!1,i)||!this._intersectCheckOneSide(t,this,$s,Js,i,-1)&&(r=!1,i)||i||Te.copy(e,r?$s:Js),r},t.prototype._intersectCheckOneSide=function(t,e,r,i,n,o){for(var a=!0,s=0;s<2;s++){var h=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,Ks),this._getProjMinMaxOnAxis(s,e._corners,Qs),Ks[1]<Qs[0]||Ks[0]>Qs[1]){if(a=!1,n)return a;var l=Math.abs(Qs[0]-Ks[1]),u=Math.abs(Ks[0]-Qs[1]);Math.min(l,u)>i.len()&&(l<u?Te.scale(i,h,-l*o):Te.scale(i,h,u*o))}else if(r){l=Math.abs(Qs[0]-Ks[1]),u=Math.abs(Ks[0]-Qs[1]);Math.min(l,u)<r.len()&&(l<u?Te.scale(r,h,l*o):Te.scale(r,h,-u*o))}}return a},t.prototype._getProjMinMaxOnAxis=function(t,e,r){for(var i=this._axes[t],n=this._origin,o=e[0].dot(i)+n[t],a=o,s=o,h=1;h<e.length;h++){var l=e[h].dot(i)+n[t];a=Math.min(l,a),s=Math.max(l,s)}r[0]=a,r[1]=s},t}(),eh=[],rh=function(e){function r(){var t=null!==e&&e.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}return t(r,e),r.prototype.traverse=function(t,e){t.call(e,this)},r.prototype.useStyle=function(){this.style={}},r.prototype.getCursor=function(){return this._cursor},r.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},r.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},r.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},r.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},r.prototype.addDisplayables=function(t,e){e=e||!1;for(var r=0;r<t.length;r++)this.addDisplayable(t[r],e)},r.prototype.getDisplayables=function(){return this._displayables},r.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},r.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},r.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){var e;(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},r.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Oe(Infinity,Infinity,-Infinity,-Infinity),e=0;e<this._displayables.length;e++){var r=this._displayables[e],i=r.getBoundingRect().clone();r.needLocalTransform()&&i.applyTransform(r.getLocalTransform(eh)),t.union(i)}this._rect=t}return this._rect},r.prototype.contain=function(t,e){var r=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(r[0],r[1]))for(var i=0;i<this._displayables.length;i++){if(this._displayables[i].contain(t,e))return!0}return!1},r}(co),ih=Math.round(9*Math.random()),nh="function"==typeof Object.defineProperty,oh=function(){function t(){this._id="__ec_inner_"+ih++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var r=this._guard(t);return nh?Object.defineProperty(r,this._id,{value:e,enumerable:!1,configurable:!0}):r[this._id]=e,this},t.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}();function ah(t){return isFinite(t)}function sh(t,e,r){for(var i="radial"===e.type?function(t,e,r){var i=r.width,n=r.height,o=Math.min(i,n),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,h=null==e.r?.5:e.r;return e.global||(a=a*i+r.x,s=s*n+r.y,h*=o),a=ah(a)?a:.5,s=ah(s)?s:.5,h=h>=0&&ah(h)?h:.5,t.createRadialGradient(a,s,0,a,s,h)}(t,e,r):function(t,e,r){var i=null==e.x?0:e.x,n=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*r.width+r.x,n=n*r.width+r.x,o=o*r.height+r.y,a=a*r.height+r.y),i=ah(i)?i:0,n=ah(n)?n:1,o=ah(o)?o:0,a=ah(a)?a:0,t.createLinearGradient(i,o,n,a)}(t,e,r),n=e.colorStops,o=0;o<n.length;o++)i.addColorStop(n[o].offset,n[o].color);return i}function hh(t){return parseInt(t,10)}function lh(t,e,r){var i=["width","height"][e],n=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],a=["paddingRight","paddingBottom"][e];if(null!=r[i]&&"auto"!==r[i])return parseFloat(r[i]);var s=document.defaultView.getComputedStyle(t);return(t[n]||hh(s[i])||hh(t.style[i]))-(hh(s[o])||0)-(hh(s[a])||0)|0}function uh(t){var e,r,i=t.style,n=i.lineDash&&i.lineWidth>0&&(e=i.lineDash,r=i.lineWidth,e&&"solid"!==e&&r>0?"dashed"===e?[4*r,2*r]:"dotted"===e?[r]:q(e)?[e]:E(e)?e:null:null),o=i.lineDashOffset;if(n){var a=i.strokeNoScale&&t.getLineScale?t.getLineScale():1;a&&1!==a&&(n=z(n,(function(t){return t/a})),o/=a)}return[n,o]}var fh=new Zo(!0);function ch(t){var e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))}function ph(t){return"string"==typeof t&&"none"!==t}function dh(t){var e=t.fill;return null!=e&&"none"!==e}function vh(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var r=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=r}else t.fill()}function yh(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var r=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=r}else t.stroke()}function gh(t,e,r){var i=Yn(e.image,e.__image,r);if(Un(i)){var n=t.createPattern(i,e.repeat||"repeat");if("function"==typeof DOMMatrix&&n&&n.setTransform){var o=new DOMMatrix;o.translateSelf(e.x||0,e.y||0),o.rotateSelf(0,0,(e.rotation||0)*_t),o.scaleSelf(e.scaleX||1,e.scaleY||1),n.setTransform(o)}return n}}var _h=["shadowBlur","shadowOffsetX","shadowOffsetY"],mh=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function xh(t,e,r,i,n){var o=!1;if(!i&&e===(r=r||{}))return!1;if(i||e.opacity!==r.opacity){kh(t,n),o=!0;var a=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(a)?ho.opacity:a}(i||e.blend!==r.blend)&&(o||(kh(t,n),o=!0),t.globalCompositeOperation=e.blend||ho.blend);for(var s=0;s<_h.length;s++){var h=_h[s];(i||e[h]!==r[h])&&(o||(kh(t,n),o=!0),t[h]=t.dpr*(e[h]||0))}return(i||e.shadowColor!==r.shadowColor)&&(o||(kh(t,n),o=!0),t.shadowColor=e.shadowColor||ho.shadowColor),o}function wh(t,e,r,i,n){var o=Sh(e,n.inHover),a=i?null:r&&Sh(r,n.inHover)||{};if(o===a)return!1;var s=xh(t,o,a,i,n);if((i||o.fill!==a.fill)&&(s||(kh(t,n),s=!0),ph(o.fill)&&(t.fillStyle=o.fill)),(i||o.stroke!==a.stroke)&&(s||(kh(t,n),s=!0),ph(o.stroke)&&(t.strokeStyle=o.stroke)),(i||o.opacity!==a.opacity)&&(s||(kh(t,n),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var h=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==h&&(s||(kh(t,n),s=!0),t.lineWidth=h)}for(var l=0;l<mh.length;l++){var u=mh[l],f=u[0];(i||o[f]!==a[f])&&(s||(kh(t,n),s=!0),t[f]=o[f]||u[1])}return s}function bh(t,e){var r=e.transform,i=t.dpr||1;r?t.setTransform(i*r[0],i*r[1],i*r[2],i*r[3],i*r[4],i*r[5]):t.setTransform(i,0,0,i,0,0)}function kh(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function Sh(t,e){return e&&t.__hoverStyle||t.style}function Th(t,e){Ch(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Ch(t,e,r,i){var o=e.transform;if(!e.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1))return e.__dirty&=-2,void(e.__isRendered=!1);var a=e.__clipPaths,s=r.prevElClipPaths,h=!1,l=!1;if(s&&!function(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return!0;return!1}(a,s)||(s&&s.length&&(kh(t,r),t.restore(),l=h=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),a&&a.length&&(kh(t,r),t.save(),function(t,e,r){for(var i=!1,n=0;n<t.length;n++){var o=t[n];i=i||o.isZeroArea(),bh(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}r.allClipped=i}(a,t,r),h=!0),r.prevElClipPaths=a),r.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var u=r.prevEl;u||(l=h=!0);var f,c,p=e instanceof va&&e.autoBatch&&function(t){var e=dh(t),r=ch(t);return!(t.lineDash||!(+e^+r)||e&&"string"!=typeof t.fill||r&&"string"!=typeof t.stroke||t.strokePercent<1||t.strokeOpacity<1||t.fillOpacity<1)}(e.style);h||(f=o,c=u.transform,f&&c?f[0]!==c[0]||f[1]!==c[1]||f[2]!==c[2]||f[3]!==c[3]||f[4]!==c[4]||f[5]!==c[5]:f||c)?(kh(t,r),bh(t,e)):p||kh(t,r);var d=Sh(e,r.inHover);e instanceof va?(1!==r.lastDrawType&&(l=!0,r.lastDrawType=1),wh(t,e,u,l,r),p&&(r.batchFill||r.batchStroke)||t.beginPath(),function(t,e,r,i){var n,o=ch(r),a=dh(r),s=r.strokePercent,h=s<1,l=!e.path;e.silent&&!h||!l||e.createPathProxy();var u=e.path||fh,f=e.__dirty;if(!i){var c=r.fill,p=r.stroke,d=a&&!!c.colorStops,v=o&&!!p.colorStops,y=a&&!!c.image,g=o&&!!p.image,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0;(d||v)&&(b=e.getBoundingRect()),d&&(_=f?sh(t,c,b):e.__canvasFillGradient,e.__canvasFillGradient=_),v&&(m=f?sh(t,p,b):e.__canvasStrokeGradient,e.__canvasStrokeGradient=m),y&&(x=f||!e.__canvasFillPattern?gh(t,c,e):e.__canvasFillPattern,e.__canvasFillPattern=x),g&&(w=f||!e.__canvasStrokePattern?gh(t,p,e):e.__canvasStrokePattern,e.__canvasStrokePattern=x),d?t.fillStyle=_:y&&(x?t.fillStyle=x:a=!1),v?t.strokeStyle=m:g&&(w?t.strokeStyle=w:o=!1)}var k,S,T=e.getGlobalScale();u.setScale(T[0],T[1],e.segmentIgnoreThreshold),t.setLineDash&&r.lineDash&&(k=(n=uh(e))[0],S=n[1]);var C=!0;(l||4&f)&&(u.setDPR(t.dpr),h?u.setContext(null):(u.setContext(t),C=!1),u.reset(),e.buildPath(u,e.shape,i),u.toStatic(),e.pathUpdated()),C&&u.rebuildPath(t,h?s:1),k&&(t.setLineDash(k),t.lineDashOffset=S),i||(r.strokeFirst?(o&&yh(t,r),a&&vh(t,r)):(a&&vh(t,r),o&&yh(t,r))),k&&t.setLineDash([])}(t,e,d,p),p&&(r.batchFill=d.fill||"",r.batchStroke=d.stroke||"")):e instanceof ga?(3!==r.lastDrawType&&(l=!0,r.lastDrawType=3),wh(t,e,u,l,r),function(t,e,r){var i,o=r.text;if(null!=o&&(o+=""),o){t.font=r.font||n,t.textAlign=r.textAlign,t.textBaseline=r.textBaseline;var a=void 0,s=void 0;t.setLineDash&&r.lineDash&&(a=(i=uh(e))[0],s=i[1]),a&&(t.setLineDash(a),t.lineDashOffset=s),r.strokeFirst?(ch(r)&&t.strokeText(o,r.x,r.y),dh(r)&&t.fillText(o,r.x,r.y)):(dh(r)&&t.fillText(o,r.x,r.y),ch(r)&&t.strokeText(o,r.x,r.y)),a&&t.setLineDash([])}}(t,e,d)):e instanceof xa?(2!==r.lastDrawType&&(l=!0,r.lastDrawType=2),function(t,e,r,i,n){xh(t,Sh(e,n.inHover),r&&Sh(r,n.inHover),i,n)}(t,e,u,l,r),function(t,e,r){var i=e.__image=Yn(r.image,e.__image,e,e.onload);if(i&&Un(i)){var n=r.x||0,o=r.y||0,a=e.getWidth(),s=e.getHeight(),h=i.width/i.height;if(null==a&&null!=s?a=s*h:null==s&&null!=a?s=a/h:null==a&&null==s&&(a=i.width,s=i.height),r.sWidth&&r.sHeight){var l=r.sx||0,u=r.sy||0;t.drawImage(i,l,u,r.sWidth,r.sHeight,n,o,a,s)}else if(r.sx&&r.sy){var f=a-(l=r.sx),c=s-(u=r.sy);t.drawImage(i,l,u,f,c,n,o,a,s)}else t.drawImage(i,n,o,a,s)}}(t,e,d)):e.getTemporalDisplayables&&(4!==r.lastDrawType&&(l=!0,r.lastDrawType=4),function(t,e,r){var i=e.getDisplayables(),n=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){(u=i[o]).beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Ch(t,u,s,o===a-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}for(var h=0,l=n.length;h<l;h++){var u;(u=n[h]).beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Ch(t,u,s,h===l-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),s.prevEl=u}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,r)),p&&i&&kh(t,r),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),r.prevEl=e,e.__dirty=0,e.__isRendered=!0}}function Ph(t,e){return Math.abs(t-e)<1e-8}function Mh(t,e,r){var i=0,n=t[0];if(!n)return!1;for(var o=1;o<t.length;o++){var a=t[o];i+=ia(n[0],n[1],a[0],a[1],e,r),n=a}var s=t[0];return Ph(n[0],s[0])&&Ph(n[1],s[1])||(i+=ia(n[0],n[1],s[0],s[1],e,r)),0!==i}var Ah=Math.sin,Lh=Math.cos,Ih=Math.PI,Dh=2*Math.PI,zh=180/Ih,Oh=function(){function t(){}return t.prototype.reset=function(t){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,t||4)},t.prototype.moveTo=function(t,e){this._add("M",t,e)},t.prototype.lineTo=function(t,e){this._add("L",t,e)},t.prototype.bezierCurveTo=function(t,e,r,i,n,o){this._add("C",t,e,r,i,n,o)},t.prototype.quadraticCurveTo=function(t,e,r,i){this._add("Q",t,e,r,i)},t.prototype.arc=function(t,e,r,i,n,o){this.ellipse(t,e,r,r,0,i,n,o)},t.prototype.ellipse=function(t,e,r,i,n,o,a,s){var h=a-o,l=!s,u=Math.abs(h),f=ai(u-Dh)||(l?h>=Dh:-h>=Dh),c=h>0?h%Dh:h%Dh+Dh,p=!1;p=!!f||!ai(u)&&c>=Ih==!!l;var d=t+r*Lh(o),v=e+i*Ah(o);this._start&&this._add("M",d,v);var y=Math.round(n*zh);if(f){var g=1/this._p,_=(l?1:-1)*(Dh-g);this._add("A",r,i,y,1,+l,t+r*Lh(o+_),e+i*Ah(o+_)),g>.01&&this._add("A",r,i,y,0,+l,d,v)}else{var m=t+r*Lh(a),x=e+i*Ah(a);this._add("A",r,i,y,+p,+l,m,x)}},t.prototype.rect=function(t,e,r,i){this._add("M",t,e),this._add("l",r,0),this._add("l",0,i),this._add("l",-r,0),this._add("Z")},t.prototype.closePath=function(){this._d.length>0&&this._add("Z")},t.prototype._add=function(t,e,r,i,n,o,a,s,h){for(var l=[],u=this._p,f=1;f<arguments.length;f++){var c=arguments[f];if(isNaN(c))return void(this._invalid=!0);l.push(Math.round(c*u)/u)}this._d.push(t+l.join(" ")),this._start="Z"===t},t.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},t.prototype.getStr=function(){return this._str},t}(),Rh="none",Fh=Math.round;var Bh=["lineCap","miterLimit","lineJoin"],Nh=z(Bh,(function(t){return"stroke-"+t.toLowerCase()}));function Hh(t,e,r,i){var n=null==e.opacity?1:e.opacity;if(r instanceof xa)t("opacity",n);else{if(function(t){var e=t.fill;return null!=e&&e!==Rh}(e)){var o=oi(e.fill);t("fill",o.color);var a=null!=e.fillOpacity?e.fillOpacity*o.opacity*n:o.opacity*n;a<1&&t("fill-opacity",a)}else t("fill",Rh);if(function(t){var e=t.stroke;return null!=e&&e!==Rh}(e)){var s=oi(e.stroke);t("stroke",s.color);var h=e.strokeNoScale?r.getLineScale():1,l=h?(e.lineWidth||0)/h:0,u=null!=e.strokeOpacity?e.strokeOpacity*s.opacity*n:s.opacity*n,f=e.strokeFirst;if(1!==l&&t("stroke-width",l),f&&t("paint-order",f?"stroke":"fill"),u<1&&t("stroke-opacity",u),e.lineDash){var c=uh(r),p=c[0],d=c[1];p&&(d=Fh(d||0),t("stroke-dasharray",p.join(",")),(d||i)&&t("stroke-dashoffset",d))}for(var v=0;v<Bh.length;v++){var y=Bh[v];if(e[y]!==ca[y]){var g=e[y]||ca[y];g&&t(Nh[v],g)}}}}}var Eh="http://www.w3.org/2000/svg",Wh="http://www.w3.org/1999/xlink",Xh="ecmeta_";function jh(t){return document.createElementNS(Eh,t)}function qh(t,e,r,i,n){return{tag:t,attrs:r||{},children:i,text:n,key:e}}function Yh(t,e){var r=(e=e||{}).newline?"\n":"";return function t(e){var i=e.children,n=e.tag,o=e.attrs,a=e.text;return function(t,e){var r=[];if(e)for(var i in e){var n=e[i],o=i;!1!==n&&(!0!==n&&null!=n&&(o+='="'+n+'"'),r.push(o))}return"<"+t+" "+r.join(" ")+">"}(n,o)+("style"!==n?ee(a):a||"")+(i?""+r+z(i,(function(e){return t(e)})).join(r)+r:"")+("</"+n+">")}(t)}function Vh(t){return{zrId:t,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function Uh(t,e,r,i){return qh("svg","root",{width:t,height:e,xmlns:Eh,"xmlns:xlink":Wh,version:"1.1",baseProfile:"full",viewBox:!!i&&"0 0 "+t+" "+e},r)}var Gh=0;function Zh(){return Gh++}var Kh={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},Qh="transform-origin";function $h(t,e,r){var i=T({},t.shape);T(i,e),t.buildPath(r,i);var n=new Oh;return n.reset(yi(t)),r.rebuildPath(n,1),n.generateStr(),n.getStr()}function Jh(t,e){var r=e.originX,i=e.originY;(r||i)&&(t[Qh]=r+"px "+i+"px")}var tl={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function el(t,e){var r=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[r]=t,r}function rl(t){return X(t)?Kh[t]?"cubic-bezier("+Kh[t]+")":Pr(t)?t:"":""}function il(t,e,r,i){var n=t.animators,o=n.length,a=[];if(t instanceof Vs){var s=function(t,e,r){var i,n,o=t.shape.paths,a={};if(D(o,(function(t){var e=Vh(r.zrId);e.animation=!0,il(t,{},e,!0);var o=e.cssAnims,s=e.cssNodes,h=B(o),l=h.length;if(l){var u=o[n=h[l-1]];for(var f in u){var c=u[f];a[f]=a[f]||{d:""},a[f].d+=c.d||""}for(var p in s){var d=s[p].animation;d.indexOf(n)>=0&&(i=d)}}})),i){e.d=!1;var s=el(a,r);return i.replace(n,s)}}(t,e,r);if(s)a.push(s);else if(!o)return}else if(!o)return;for(var h={},l=0;l<o;l++){var u=n[l],f=[u.getMaxTime()/1e3+"s"],c=rl(u.getClip().easing),p=u.getDelay();c?f.push(c):f.push("linear"),p&&f.push(p/1e3+"s"),u.getLoop()&&f.push("infinite");var d=f.join(" ");h[d]=h[d]||[d,[]],h[d][1].push(u)}function v(n){var o,a=n[1],s=a.length,h={},l={},u={},f="animation-timing-function";function c(t,e,r){for(var i=t.getTracks(),n=t.getMaxTime(),o=0;o<i.length;o++){var a=i[o];if(a.needsAnimate()){var s=a.keyframes,h=a.propName;if(r&&(h=r(h)),h)for(var l=0;l<s.length;l++){var u=s[l],c=Math.round(u.time/n*100)+"%",p=rl(u.easing),d=u.rawValue;(X(d)||q(d))&&(e[c]=e[c]||{},e[c][h]=u.rawValue,p&&(e[c][f]=p))}}}}for(var p=0;p<s;p++){(k=(b=a[p]).targetName)?"shape"===k&&c(b,l):!i&&c(b,h)}for(var d in h){var v={};pn(v,t),T(v,h[d]);var y=gi(v),g=h[d][f];u[d]=y?{transform:y}:{},Jh(u[d],v),g&&(u[d][f]=g)}var _=!0;for(var d in l){u[d]=u[d]||{};var m=!o;g=l[d][f];m&&(o=new Zo);var x=o.len();o.reset(),u[d].d=$h(t,l[d],o);var w=o.len();if(!m&&x!==w){_=!1;break}g&&(u[d][f]=g)}if(!_)for(var d in u)delete u[d].d;if(!i)for(p=0;p<s;p++){var b,k;"style"===(k=(b=a[p]).targetName)&&c(b,u,(function(t){return tl[t]}))}var S,C=B(u),P=!0;for(p=1;p<C.length;p++){var M=C[p-1],A=C[p];if(u[M][Qh]!==u[A][Qh]){P=!1;break}S=u[M][Qh]}if(P&&S){for(var d in u)u[d][Qh]&&delete u[d][Qh];e[Qh]=S}if(R(C,(function(t){return B(u[t]).length>0})).length)return el(u,r)+" "+n[0]+" both"}for(var y in h){(s=v(h[y]))&&a.push(s)}if(a.length){var g=r.zrId+"-cls-"+Zh();r.cssNodes["."+g]={animation:a.join(",")},e.class=g}}function nl(t,e,r,i){var n=JSON.stringify(t),o=r.cssStyleCache[n];o||(o=r.zrId+"-cls-"+Zh(),r.cssStyleCache[n]=o,r.cssNodes["."+o+":hover"]=t),e.class=e.class?e.class+" "+o:o}var ol=Math.round;function al(t){return t&&X(t.src)}function sl(t){return t&&W(t.toDataURL)}function hl(t,e,r,i){Hh((function(n,o){var a="fill"===n||"stroke"===n;a&&di(o)?ml(e,t,n,i):a&&fi(o)?xl(r,t,n,i):t[n]=o,a&&i.ssr&&"none"===o&&(t["pointer-events"]="visible")}),e,r,!1),function(t,e,r){var i=t.style;if(function(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY)}(i)){var n=function(t){var e=t.style,r=t.getGlobalScale();return[e.shadowColor,(e.shadowBlur||0).toFixed(2),(e.shadowOffsetX||0).toFixed(2),(e.shadowOffsetY||0).toFixed(2),r[0],r[1]].join(",")}(t),o=r.shadowCache,a=o[n];if(!a){var s=t.getGlobalScale(),h=s[0],l=s[1];if(!h||!l)return;var u=i.shadowOffsetX||0,f=i.shadowOffsetY||0,c=i.shadowBlur,p=oi(i.shadowColor),d=p.opacity,v=p.color,y=c/2/h+" "+c/2/l;a=r.zrId+"-s"+r.shadowIdx++,r.defs[a]=qh("filter",a,{id:a,x:"-100%",y:"-100%",width:"300%",height:"300%"},[qh("feDropShadow","",{dx:u/h,dy:f/l,stdDeviation:y,"flood-color":v,"flood-opacity":d})]),o[n]=a}e.filter=vi(a)}}(r,t,i)}function ll(t,e){var r=En(e);r&&(r.each((function(e,r){null!=e&&(t[(Xh+r).toLowerCase()]=e+"")})),e.isSilent()&&(t[Xh+"silent"]="true"))}function ul(t){return ai(t[0]-1)&&ai(t[1])&&ai(t[2])&&ai(t[3]-1)}function fl(t,e,r){if(e&&(!function(t){return ai(t[4])&&ai(t[5])}(e)||!ul(e))){var i=1e4;t.transform=ul(e)?"translate("+ol(e[4]*i)/i+" "+ol(e[5]*i)/i+")":function(t){return"matrix("+si(t[0])+","+si(t[1])+","+si(t[2])+","+si(t[3])+","+hi(t[4])+","+hi(t[5])+")"}(e)}}function cl(t,e,r){for(var i=t.points,n=[],o=0;o<i.length;o++)n.push(ol(i[o][0]*r)/r),n.push(ol(i[o][1]*r)/r);e.points=n.join(" ")}function pl(t){return!t.smooth}var dl,vl,yl={circle:[(dl=["cx","cy","r"],vl=z(dl,(function(t){return"string"==typeof t?[t,t]:t})),function(t,e,r){for(var i=0;i<vl.length;i++){var n=vl[i],o=t[n[0]];null!=o&&(e[n[1]]=ol(o*r)/r)}})],polyline:[cl,pl],polygon:[cl,pl]};function gl(t,e){var r=t.style,i=t.shape,n=yl[t.type],o={},a=e.animation,s="path",h=t.style.strokePercent,l=e.compress&&yi(t)||4;if(!n||e.willUpdate||n[1]&&!n[1](i)||a&&function(t){for(var e=t.animators,r=0;r<e.length;r++)if("shape"===e[r].targetName)return!0;return!1}(t)||h<1){var u=!t.path||t.shapeChanged();t.path||t.createPathProxy();var f=t.path;u&&(f.beginPath(),t.buildPath(f,t.shape),t.pathUpdated());var c=f.getVersion(),p=t,d=p.__svgPathBuilder;p.__svgPathVersion===c&&d&&h===p.__svgPathStrokePercent||(d||(d=p.__svgPathBuilder=new Oh),d.reset(l),f.rebuildPath(d,h),d.generateStr(),p.__svgPathVersion=c,p.__svgPathStrokePercent=h),o.d=d.getStr()}else{s=t.type;var v=Math.pow(10,l);n[0](i,o,v)}return fl(o,t.transform),hl(o,r,t,e),ll(o,t),e.animation&&il(t,o,e),e.emphasis&&function(t,e,r){if(!t.ignore)if(t.isSilent())nl(l={"pointer-events":"none"},e,r);else{var i=t.states.emphasis&&t.states.emphasis.style?t.states.emphasis.style:{},n=i.fill;if(!n){var o=t.style&&t.style.fill,a=t.states.select&&t.states.select.style&&t.states.select.style.fill,s=t.currentStates.indexOf("select")>=0&&a||o;s&&(n=ri(s))}var h=i.lineWidth;h&&(h/=!i.strokeNoScale&&t.transform?t.transform[0]:1);var l={cursor:"pointer"};n&&(l.fill=n),i.stroke&&(l.stroke=i.stroke),h&&(l["stroke-width"]=h),nl(l,e,r)}}(t,o,e),qh(s,t.id+"",o)}function _l(t,e){return t instanceof va?gl(t,e):t instanceof xa?function(t,e){var r=t.style,i=r.image;if(i&&!X(i)&&(al(i)?i=i.src:sl(i)&&(i=i.toDataURL())),i){var n=r.x||0,o=r.y||0,a={href:i,width:r.width,height:r.height};return n&&(a.x=n),o&&(a.y=o),fl(a,t.transform),hl(a,r,t,e),ll(a,t),e.animation&&il(t,a,e),qh("image",t.id+"",a)}}(t,e):t instanceof ga?function(t,e){var r=t.style,o=r.text;if(null!=o&&(o+=""),o&&!isNaN(r.x)&&!isNaN(r.y)){var a=r.font||n,s=r.x||0,h=function(t,e,r){return"top"===r?t+=e/2:"bottom"===r&&(t-=e/2),t}(r.y||0,xn(a),r.textBaseline),l={"dominant-baseline":"central","text-anchor":li[r.textAlign]||r.textAlign};if(Fa(r)){var u="",f=r.fontStyle,c=Oa(r.fontSize);if(!parseFloat(c))return;var p=r.fontFamily||i,d=r.fontWeight;u+="font-size:"+c+";font-family:"+p+";",f&&"normal"!==f&&(u+="font-style:"+f+";"),d&&"normal"!==d&&(u+="font-weight:"+d+";"),l.style=u}else l.style="font: "+a;return o.match(/\s/)&&(l["xml:space"]="preserve"),s&&(l.x=s),h&&(l.y=h),fl(l,t.transform),hl(l,r,t,e),ll(l,t),e.animation&&il(t,l,e),qh("text",t.id+"",l,void 0,o)}}(t,e):void 0}function ml(t,e,r,i){var n,o=t[r],a={gradientUnits:o.global?"userSpaceOnUse":"objectBoundingBox"};if(ci(o))n="linearGradient",a.x1=o.x,a.y1=o.y,a.x2=o.x2,a.y2=o.y2;else{if(!pi(o))return;n="radialGradient",a.cx=tt(o.x,.5),a.cy=tt(o.y,.5),a.r=tt(o.r,.5)}for(var s=o.colorStops,h=[],l=0,u=s.length;l<u;++l){var f=100*hi(s[l].offset)+"%",c=oi(s[l].color),p=c.color,d=c.opacity,v={offset:f};v["stop-color"]=p,d<1&&(v["stop-opacity"]=d),h.push(qh("stop",l+"",v))}var y=Yh(qh(n,"",a,h)),g=i.gradientCache,_=g[y];_||(_=i.zrId+"-g"+i.gradientIdx++,g[y]=_,a.id=_,i.defs[_]=qh(n,_,a,h)),e[r]=vi(_)}function xl(t,e,r,i){var n,o=t.style[r],a=t.getBoundingRect(),s={},h=o.repeat,l="no-repeat"===h,u="repeat-x"===h,f="repeat-y"===h;if(ui(o)){var c=o.imageWidth,p=o.imageHeight,d=void 0,v=o.image;if(X(v)?d=v:al(v)?d=v.src:sl(v)&&(d=v.toDataURL()),"undefined"==typeof Image){var y="Image width/height must been given explictly in svg-ssr renderer.";nt(c,y),nt(p,y)}else if(null==c||null==p){var g=function(t,e){if(t){var r=t.elm,i=c||e.width,n=p||e.height;"pattern"===t.tag&&(u?(n=1,i/=a.width):f&&(i=1,n/=a.height)),t.attrs.width=i,t.attrs.height=n,r&&(r.setAttribute("width",i),r.setAttribute("height",n))}},_=Yn(d,null,t,(function(t){l||g(k,t),g(n,t)}));_&&_.width&&_.height&&(c=c||_.width,p=p||_.height)}n=qh("image","img",{href:d,width:c,height:p}),s.width=c,s.height=p}else o.svgElement&&(n=b(o.svgElement),s.width=o.svgWidth,s.height=o.svgHeight);if(n){var m,x;l?m=x=1:u?(x=1,m=s.width/a.width):f?(m=1,x=s.height/a.height):s.patternUnits="userSpaceOnUse",null==m||isNaN(m)||(s.width=m),null==x||isNaN(x)||(s.height=x);var w=gi(o);w&&(s.patternTransform=w);var k=qh("pattern","",s,[n]),S=Yh(k),T=i.patternCache,C=T[S];C||(C=i.zrId+"-p"+i.patternIdx++,T[S]=C,s.id=C,k=i.defs[C]=qh("pattern",C,s,[n])),e[r]=vi(C)}}function wl(t,e,r){var i=r.clipPathCache,n=r.defs,o=i[t.id];if(!o){var a={id:o=r.zrId+"-c"+r.clipPathIdx++};i[t.id]=o,n[o]=qh("clipPath",o,a,[gl(t,r)])}e["clip-path"]=vi(o)}function bl(t){return document.createTextNode(t)}function kl(t,e,r){t.insertBefore(e,r)}function Sl(t,e){t.removeChild(e)}function Tl(t,e){t.appendChild(e)}function Cl(t){return t.parentNode}function Pl(t){return t.nextSibling}function Ml(t,e){t.textContent=e}var Al=qh("","");function Ll(t){return void 0===t}function Il(t){return void 0!==t}function Dl(t,e,r){for(var i={},n=e;n<=r;++n){var o=t[n].key;void 0!==o&&(i[o]=n)}return i}function zl(t,e){var r=t.key===e.key;return t.tag===e.tag&&r}function Ol(t){var e,r=t.children,i=t.tag;if(Il(i)){var n=t.elm=jh(i);if(Bl(Al,t),E(r))for(e=0;e<r.length;++e){var o=r[e];null!=o&&Tl(n,Ol(o))}else Il(t.text)&&!Y(t.text)&&Tl(n,bl(t.text))}else t.elm=bl(t.text);return t.elm}function Rl(t,e,r,i,n){for(;i<=n;++i){var o=r[i];null!=o&&kl(t,Ol(o),e)}}function Fl(t,e,r,i){for(;r<=i;++r){var n=e[r];if(null!=n)if(Il(n.tag))Sl(Cl(n.elm),n.elm);else Sl(t,n.elm)}}function Bl(t,e){var r,i=e.elm,n=t&&t.attrs||{},o=e.attrs||{};if(n!==o){for(r in o){var a=o[r];n[r]!==a&&(!0===a?i.setAttribute(r,""):!1===a?i.removeAttribute(r):"style"===r?i.style.cssText=a:120!==r.charCodeAt(0)?i.setAttribute(r,a):"xmlns:xlink"===r||"xmlns"===r?i.setAttributeNS("http://www.w3.org/2000/xmlns/",r,a):58===r.charCodeAt(3)?i.setAttributeNS("http://www.w3.org/XML/1998/namespace",r,a):58===r.charCodeAt(5)?i.setAttributeNS(Wh,r,a):i.setAttribute(r,a))}for(r in n)r in o||i.removeAttribute(r)}}function Nl(t,e){var r=e.elm=t.elm,i=t.children,n=e.children;t!==e&&(Bl(t,e),Ll(e.text)?Il(i)&&Il(n)?i!==n&&function(t,e,r){for(var i,n,o,a=0,s=0,h=e.length-1,l=e[0],u=e[h],f=r.length-1,c=r[0],p=r[f];a<=h&&s<=f;)null==l?l=e[++a]:null==u?u=e[--h]:null==c?c=r[++s]:null==p?p=r[--f]:zl(l,c)?(Nl(l,c),l=e[++a],c=r[++s]):zl(u,p)?(Nl(u,p),u=e[--h],p=r[--f]):zl(l,p)?(Nl(l,p),kl(t,l.elm,Pl(u.elm)),l=e[++a],p=r[--f]):zl(u,c)?(Nl(u,c),kl(t,u.elm,l.elm),u=e[--h],c=r[++s]):(Ll(i)&&(i=Dl(e,a,h)),Ll(n=i[c.key])||(o=e[n]).tag!==c.tag?kl(t,Ol(c),l.elm):(Nl(o,c),e[n]=void 0,kl(t,o.elm,l.elm)),c=r[++s]);(a<=h||s<=f)&&(a>h?Rl(t,null==r[f+1]?null:r[f+1].elm,r,s,f):Fl(t,e,a,h))}(r,i,n):Il(n)?(Il(t.text)&&Ml(r,""),Rl(r,null,n,0,n.length-1)):Il(i)?Fl(r,i,0,i.length-1):Il(t.text)&&Ml(r,""):t.text!==e.text&&(Il(i)&&Fl(r,i,0,i.length-1),Ml(r,e.text)))}var Hl=0,El=function(){function t(t,e,r){if(this.type="svg",this.refreshHover=function(){},this.configLayer=function(){},this.storage=e,this._opts=r=T({},r),this.root=t,this._id="zr"+Hl++,this._oldVNode=Uh(r.width,r.height),t&&!r.ssr){var i=this._viewport=document.createElement("div");i.style.cssText="position:relative;overflow:hidden";var n=this._svgDom=this._oldVNode.elm=jh("svg");Bl(null,this._oldVNode),i.appendChild(n),t.appendChild(i)}this.resize(r.width,r.height)}return t.prototype.getType=function(){return this.type},t.prototype.getViewportRoot=function(){return this._viewport},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.getSvgDom=function(){return this._svgDom},t.prototype.refresh=function(){if(this.root){var t=this.renderToVNode({willUpdate:!0});t.attrs.style="position:absolute;left:0;top:0;user-select:none",function(t,e){if(zl(t,e))Nl(t,e);else{var r=t.elm,i=Cl(r);Ol(e),null!==i&&(kl(i,e.elm,Pl(r)),Fl(i,[t],0,0))}}(this._oldVNode,t),this._oldVNode=t}},t.prototype.renderOneToVNode=function(t){return _l(t,Vh(this._id))},t.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),r=this._width,i=this._height,n=Vh(this._id);n.animation=t.animation,n.willUpdate=t.willUpdate,n.compress=t.compress,n.emphasis=t.emphasis,n.ssr=this._opts.ssr;var o=[],a=this._bgVNode=function(t,e,r,i){var n;if(r&&"none"!==r)if(n=qh("rect","bg",{width:t,height:e,x:"0",y:"0"}),di(r))ml({fill:r},n.attrs,"fill",i);else if(fi(r))xl({style:{fill:r},dirty:gt,getBoundingRect:function(){return{width:t,height:e}}},n.attrs,"fill",i);else{var o=oi(r),a=o.color,s=o.opacity;n.attrs.fill=a,s<1&&(n.attrs["fill-opacity"]=s)}return n}(r,i,this._backgroundColor,n);a&&o.push(a);var s=t.compress?null:this._mainVNode=qh("g","main",{},[]);this._paintList(e,n,s?s.children:o),s&&o.push(s);var h=z(B(n.defs),(function(t){return n.defs[t]}));if(h.length&&o.push(qh("defs","defs",{},h)),t.animation){var l=function(t,e,r){var i=(r=r||{}).newline?"\n":"",n=" {"+i,o=i+"}",a=z(B(t),(function(e){return e+n+z(B(t[e]),(function(r){return r+":"+t[e][r]+";"})).join(i)+o})).join(i),s=z(B(e),(function(t){return"@keyframes "+t+n+z(B(e[t]),(function(r){return r+n+z(B(e[t][r]),(function(i){var n=e[t][r][i];return"d"===i&&(n='path("'+n+'")'),i+":"+n+";"})).join(i)+o})).join(i)+o})).join(i);return a||s?["<![CDATA[",a,s,"]]>"].join(i):""}(n.cssNodes,n.cssAnims,{newline:!0});if(l){var u=qh("style","stl",{},[],l);o.push(u)}}return Uh(r,i,o,t.useViewBox)},t.prototype.renderToString=function(t){return t=t||{},Yh(this.renderToVNode({animation:tt(t.cssAnimation,!0),emphasis:tt(t.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:tt(t.useViewBox,!0)}),{newline:!0})},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t},t.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},t.prototype._paintList=function(t,e,r){for(var i,n,o=t.length,a=[],s=0,h=0,l=0;l<o;l++){var u=t[l];if(!u.invisible){var f=u.__clipPaths,c=f&&f.length||0,p=n&&n.length||0,d=void 0;for(d=Math.max(c-1,p-1);d>=0&&(!f||!n||f[d]!==n[d]);d--);for(var v=p-1;v>d;v--)i=a[--s-1];for(var y=d+1;y<c;y++){var g={};wl(f[y],g,e);var _=qh("g","clip-g-"+h++,g,[]);(i?i.children:r).push(_),a[s++]=_,i=_}n=f;var m=_l(u,e);m&&(i?i.children:r).push(m)}}},t.prototype.resize=function(t,e){var r=this._opts,i=this.root,n=this._viewport;if(null!=t&&(r.width=t),null!=e&&(r.height=e),i&&n&&(n.style.display="none",t=lh(i,0,r),e=lh(i,1,r),n.style.display=""),this._width!==t||this._height!==e){if(this._width=t,this._height=e,n){var o=n.style;o.width=t+"px",o.height=e+"px"}if(fi(this._backgroundColor))this.refresh();else{var a=this._svgDom;a&&(a.setAttribute("width",t),a.setAttribute("height",e));var s=this._bgVNode&&this._bgVNode.elm;s&&(s.setAttribute("width",t),s.setAttribute("height",e))}}},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},t.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},t.prototype.toDataURL=function(t){var e=this.renderToString(),r="data:image/svg+xml;";return t?(e=_i(e))&&r+"base64,"+e:r+"charset=UTF-8,"+encodeURIComponent(e)},t}();function Wl(t,e,r){var i=a.createCanvas(),n=e.getWidth(),o=e.getHeight(),s=i.style;return s&&(s.position="absolute",s.left="0",s.top="0",s.width=n+"px",s.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=n*r,i.height=o*r,i}var Xl=function(e){function r(t,r,i){var n,o=e.call(this)||this;o.motionBlur=!1,o.lastFrameAlpha=.7,o.dpr=1,o.virtual=!1,o.config={},o.incremental=!1,o.zlevel=0,o.maxRepaintRectCount=5,o.__dirty=!0,o.__firstTimePaint=!0,o.__used=!1,o.__drawIndex=0,o.__startIndex=0,o.__endIndex=0,o.__prevStartIndex=null,o.__prevEndIndex=null,i=i||en,"string"==typeof t?n=Wl(t,r,i):Y(t)&&(t=(n=t).id),o.id=t,o.dom=n;var a=n.style;return a&&(vt(n),n.onselectstart=function(){return!1},a.padding="0",a.margin="0",a.borderWidth="0"),o.painter=r,o.dpr=i,o}return t(r,e),r.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},r.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},r.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},r.prototype.setUnpainted=function(){this.__firstTimePaint=!0},r.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=Wl("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},r.prototype.createRepaintRects=function(t,e,r,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var n,o=[],a=this.maxRepaintRectCount,s=!1,h=new Oe(0,0,0,0);function l(t){if(t.isFinite()&&!t.isZero())if(0===o.length){(e=new Oe(0,0,0,0)).copy(t),o.push(e)}else{for(var e,r=!1,i=Infinity,n=0,l=0;l<o.length;++l){var u=o[l];if(u.intersect(t)){var f=new Oe(0,0,0,0);f.copy(u),f.union(t),o[l]=f,r=!0;break}if(s){h.copy(t),h.union(u);var c=t.width*t.height,p=u.width*u.height,d=h.width*h.height-c-p;d<i&&(i=d,n=l)}}if(s&&(o[n].union(t),r=!0),!r)(e=new Oe(0,0,0,0)).copy(t),o.push(e);s||(s=o.length>=a)}}for(var u=this.__startIndex;u<this.__endIndex;++u){if(p=t[u]){var f=p.shouldBePainted(r,i,!0,!0);(d=p.__isRendered&&(1&p.__dirty||!f)?p.getPrevPaintRect():null)&&l(d);var c=f&&(1&p.__dirty||!p.__isRendered)?p.getPaintRect():null;c&&l(c)}}for(u=this.__prevStartIndex;u<this.__prevEndIndex;++u){var p,d;f=(p=e[u])&&p.shouldBePainted(r,i,!0,!0);if(p&&(!f||!p.__zr)&&p.__isRendered)(d=p.getPrevPaintRect())&&l(d)}do{n=!1;for(u=0;u<o.length;)if(o[u].isZero())o.splice(u,1);else{for(var v=u+1;v<o.length;)o[u].intersect(o[v])?(n=!0,o[u].union(o[v]),o.splice(v,1)):v++;u++}}while(n);return this._paintRects=o,o},r.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},r.prototype.resize=function(t,e){var r=this.dpr,i=this.dom,n=i.style,o=this.domBack;n&&(n.width=t+"px",n.height=e+"px"),i.width=t*r,i.height=e*r,o&&(o.width=t*r,o.height=e*r,1!==r&&this.ctxBack.scale(r,r))},r.prototype.clear=function(t,e,r){var i=this.dom,n=this.ctx,o=i.width,a=i.height;e=e||this.clearColor;var s=this.motionBlur&&!t,h=this.lastFrameAlpha,l=this.dpr,u=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,o/l,a/l));var f=this.domBack;function c(t,r,i,o){if(n.clearRect(t,r,i,o),e&&"transparent"!==e){var a=void 0;if(Z(e))a=(e.global||e.__width===i&&e.__height===o)&&e.__canvasGradient||sh(n,e,{x:0,y:0,width:i,height:o}),e.__canvasGradient=a,e.__width=i,e.__height=o;else K(e)&&(e.scaleX=e.scaleX||l,e.scaleY=e.scaleY||l,a=gh(n,e,{dirty:function(){u.setUnpainted(),u.painter.refresh()}}));n.save(),n.fillStyle=a||e,n.fillRect(t,r,i,o),n.restore()}s&&(n.save(),n.globalAlpha=h,n.drawImage(f,t,r,i,o),n.restore())}!r||s?c(0,0,o,a):r.length&&D(r,(function(t){c(t.x*l,t.y*l,t.width*l,t.height*l)}))},r}(qt),jl=1e5,ql=314159,Yl=.01;var Vl,Ul=function(){function t(t,e,r,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=r=T({},r||{}),this.dpr=r.devicePixelRatio||en,this._singleCanvas=n,this.root=t,t.style&&(vt(t),t.innerHTML=""),this.storage=e;var o=this._zlevelList;this._prevDisplayList=[];var a=this._layers;if(n){var s=t,h=s.width,l=s.height;null!=r.width&&(h=r.width),null!=r.height&&(l=r.height),this.dpr=r.devicePixelRatio||1,s.width=h*this.dpr,s.height=l*this.dpr,this._width=h,this._height=l;var u=new Xl(s,this,this.dpr);u.__builtin__=!0,u.initContext(),a[314159]=u,u.zlevel=ql,o.push(ql),this._domRoot=t}else{this._width=lh(t,0,r),this._height=lh(t,1,r);var f=this._domRoot=function(t,e){var r=document.createElement("div");return r.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",r}(this._width,this._height);t.appendChild(f)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),r=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,r,t,this._redrawId);for(var n=0;n<i.length;n++){var o=i[n],a=this._layers[o];if(!a.__builtin__&&a.refresh){var s=0===n?this._backgroundColor:null;a.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,r=this._hoverlayer;if(r&&r.clear(),e){for(var i,n={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(r||(r=this._hoverlayer=this.getLayer(jl)),i||(i=r.ctx).save(),Ch(i,a,n,o===e-1))}i&&i.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(jl)},t.prototype.paintOne=function(t,e){Th(t,e)},t.prototype._paintList=function(t,e,r,i){if(this._redrawId===i){r=r||!1,this._updateLayerStatus(t);var n=this._doPaintList(t,e,r),o=n.finished,a=n.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;tr((function(){s._paintList(t,e,r,i)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(ql).ctx,e=this._domRoot.width,r=this._domRoot.height;t.clearRect(0,0,e,r),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,r)}))},t.prototype._doPaintList=function(t,e,i){for(var n=this,o=[],a=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var h=this._zlevelList[s],l=this._layers[h];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||i)&&o.push(l)}for(var u=!0,f=!1,c=function(r){var s,h=o[r],l=h.ctx,c=a&&h.createRepaintRects(t,e,p._width,p._height),d=i?h.__startIndex:h.__drawIndex,v=!i&&h.incremental&&Date.now,y=v&&Date.now(),g=h.zlevel===p._zlevelList[0]?p._backgroundColor:null;if(h.__startIndex===h.__endIndex)h.clear(!1,g,c);else if(d===h.__startIndex){var _=t[d];_.incremental&&_.notClear&&!i||h.clear(!1,g,c)}-1===d&&(d=h.__startIndex);var m=function(e){var r={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(s=d;s<h.__endIndex;s++){var i=t[s];if(i.__inHover&&(f=!0),n._doPaintEl(i,h,a,e,r,s===h.__endIndex-1),v)if(Date.now()-y>15)break}r.prevElClipPaths&&l.restore()};if(c)if(0===c.length)s=h.__endIndex;else for(var x=p.dpr,w=0;w<c.length;++w){var b=c[w];l.save(),l.beginPath(),l.rect(b.x*x,b.y*x,b.width*x,b.height*x),l.clip(),m(b),l.restore()}else l.save(),m(),l.restore();h.__drawIndex=s,h.__drawIndex<h.__endIndex&&(u=!1)},p=this,d=0;d<o.length;d++)c(d);return r.wxa&&D(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:u,needsRefreshHover:f}},t.prototype._doPaintEl=function(t,e,r,i,n,o){var a=e.ctx;if(r){var s=t.getPaintRect();(!i||s&&s.intersect(i))&&(Ch(a,t,n,o),t.setPrevPaintRect(s))}else Ch(a,t,n,o)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=ql);var r=this._layers[t];return r||((r=new Xl("zr_"+t,this,this.dpr)).zlevel=t,r.__builtin__=!0,this._layerConfig[t]?k(r,this._layerConfig[t],!0):this._layerConfig[t-Yl]&&k(r,this._layerConfig[t-Yl],!0),e&&(r.virtual=e),this.insertLayer(t,r),r.initContext()),r},t.prototype.insertLayer=function(t,e){var r=this._layers,i=this._zlevelList,n=i.length,o=this._domRoot,a=null,s=-1;if(!r[t]&&function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(n>0&&t>i[0]){for(s=0;s<n-1&&!(i[s]<t&&i[s+1]>t);s++);a=r[i[s]]}if(i.splice(s+1,0,t),r[t]=e,!e.virtual)if(a){var h=a.dom;h.nextSibling?o.insertBefore(e.dom,h.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},t.prototype.eachLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i];t.call(e,this._layers[n],n)}},t.prototype.eachBuiltinLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__&&t.call(e,o,n)}},t.prototype.eachOtherLayer=function(t,e){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__||t.call(e,o,n)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){o&&(o.__endIndex!==t&&(o.__dirty=!0),o.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var r=1;r<t.length;r++){if((s=t[r]).zlevel!==t[r-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}}var i,n,o=null,a=0;for(n=0;n<t.length;n++){var s,h=(s=t[n]).zlevel,l=void 0;i!==h&&(i=h,a=0),s.incremental?((l=this.getLayer(h+.001,this._needsManuallyCompositing)).incremental=!0,a=1):l=this.getLayer(h+(a>0?Yl:0),this._needsManuallyCompositing),l.__builtin__||w("ZLevel "+h+" has been used by unkown layer "+l.id),l!==o&&(l.__used=!0,l.__startIndex!==n&&(l.__dirty=!0),l.__startIndex=n,l.incremental?l.__drawIndex=-1:l.__drawIndex=n,e(n),o=l),1&s.__dirty&&!s.__inHover&&(l.__dirty=!0,l.incremental&&l.__drawIndex<0&&(l.__drawIndex=n))}e(n),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,D(this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var r=this._layerConfig;r[t]?k(r[t],e,!0):r[t]=e;for(var i=0;i<this._zlevelList.length;i++){var n=this._zlevelList[i];if(n===t||n===t+Yl)k(this._layers[n],r[t],!0)}}},t.prototype.delLayer=function(t){var e=this._layers,r=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],r.splice(M(r,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var r=this._domRoot;r.style.display="none";var i=this._opts,n=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=lh(n,0,i),e=lh(n,1,i),r.style.display="",this._width!==t||e!==this._height){for(var o in r.style.width=t+"px",r.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(ql).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[314159].dom;var e=new Xl("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var r=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height;this.eachLayer((function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(r.save(),t.renderToCanvas(r),r.restore())}))}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,h=a.length;s<h;s++){var l=a[s];Ch(r,l,o,s===h-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}();function Gl(t){X(t)&&(t=(new DOMParser).parseFromString(t,"text/xml"));var e=t;for(9===e.nodeType&&(e=e.firstChild);"svg"!==e.nodeName.toLowerCase()||1!==e.nodeType;)e=e.nextSibling;return e}var Zl={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},Kl=B(Zl),Ql={"alignment-baseline":"textBaseline","stop-color":"stopColor"},$l=B(Ql),Jl=function(){function t(){this._defs={},this._root=null}return t.prototype.parse=function(t,e){e=e||{};var r=Gl(t);this._defsUsePending=[];var i=new zn;this._root=i;var n=[],o=r.getAttribute("viewBox")||"",a=parseFloat(r.getAttribute("width")||e.width),s=parseFloat(r.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(s)&&(s=null),ou(r,i,null,!0,!1);for(var h,l,u=r.firstChild;u;)this._parseNode(u,i,n,null,!1,!1),u=u.nextSibling;if(function(t,e){for(var r=0;r<e.length;r++){var i=e[r];i[0].style[i[1]]=t[i[2]]}}(this._defs,this._defsUsePending),this._defsUsePending=[],o){var f=lu(o);f.length>=4&&(h={x:parseFloat(f[0]||0),y:parseFloat(f[1]||0),width:parseFloat(f[2]),height:parseFloat(f[3])})}if(h&&null!=a&&null!=s&&(l=du(h,{x:0,y:0,width:a,height:s}),!e.ignoreViewBox)){var c=i;(i=new zn).add(c),c.scaleX=c.scaleY=l.scale,c.x=l.x,c.y=l.y}return e.ignoreRootClip||null==a||null==s||i.setClipPath(new Pa({shape:{x:0,y:0,width:a,height:s}})),{root:i,width:a,height:s,viewBoxRect:h,viewBoxTransform:l,named:n}},t.prototype._parseNode=function(t,e,r,i,n,o){var a,s=t.nodeName.toLowerCase(),h=i;if("defs"===s&&(n=!0),"text"===s&&(o=!0),"defs"===s||"switch"===s)a=e;else{if(!n){var l=Vl[s];if(l&&yt(Vl,s)){a=l.call(this,t,e);var u=t.getAttribute("name");if(u){var f={name:u,namedFrom:null,svgNodeTagLower:s,el:a};r.push(f),"g"===s&&(h=f)}else i&&r.push({name:i.name,namedFrom:i,svgNodeTagLower:s,el:a});e.add(a)}}var c=tu[s];if(c&&yt(tu,s)){var p=c.call(this,t),d=t.getAttribute("id");d&&(this._defs[d]=p)}}if(a&&a.isGroup)for(var v=t.firstChild;v;)1===v.nodeType?this._parseNode(v,a,r,h,n,o):3===v.nodeType&&o&&this._parseText(v,a),v=v.nextSibling},t.prototype._parseText=function(t,e){var r=new ga({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});iu(e,r),ou(t,r,this._defsUsePending,!1,!1),function(t,e){var r=e.__selfStyle;if(r){var i=r.textBaseline,n=i;i&&"auto"!==i?"baseline"===i?n="alphabetic":"before-edge"===i||"text-before-edge"===i?n="top":"after-edge"===i||"text-after-edge"===i?n="bottom":"central"!==i&&"mathematical"!==i||(n="middle"):n="alphabetic",t.style.textBaseline=n}var o=e.__inheritedStyle;if(o){var a=o.textAlign,s=a;a&&("middle"===a&&(s="center"),t.style.textAlign=s)}}(r,e);var i=r.style,n=i.fontSize;n&&n<9&&(i.fontSize=9,r.scaleX*=n/9,r.scaleY*=n/9);var o=(i.fontSize||i.fontFamily)&&[i.fontStyle,i.fontWeight,(i.fontSize||12)+"px",i.fontFamily||"sans-serif"].join(" ");i.font=o;var a=r.getBoundingRect();return this._textX+=a.width,e.add(r),r},t.internalField=void(Vl={g:function(t,e){var r=new zn;return iu(e,r),ou(t,r,this._defsUsePending,!1,!1),r},rect:function(t,e){var r=new Pa;return iu(e,r),ou(t,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(t,e){var r=new cs;return iu(e,r),ou(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),r.silent=!0,r},line:function(t,e){var r=new Hs;return iu(e,r),ou(t,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(t,e){var r=new ds;return iu(e,r),ou(t,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(t,e){var r,i=t.getAttribute("points");i&&(r=nu(i));var n=new Os({shape:{points:r||[]},silent:!0});return iu(e,n),ou(t,n,this._defsUsePending,!1,!1),n},polyline:function(t,e){var r,i=t.getAttribute("points");i&&(r=nu(i));var n=new Fs({shape:{points:r||[]},silent:!0});return iu(e,n),ou(t,n,this._defsUsePending,!1,!1),n},image:function(t,e){var r=new xa;return iu(e,r),ou(t,r,this._defsUsePending,!1,!1),r.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),r.silent=!0,r},text:function(t,e){var r=t.getAttribute("x")||"0",i=t.getAttribute("y")||"0",n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(n),this._textY=parseFloat(i)+parseFloat(o);var a=new zn;return iu(e,a),ou(t,a,this._defsUsePending,!1,!0),a},tspan:function(t,e){var r=t.getAttribute("x"),i=t.getAttribute("y");null!=r&&(this._textX=parseFloat(r)),null!=i&&(this._textY=parseFloat(i));var n=t.getAttribute("dx")||"0",o=t.getAttribute("dy")||"0",a=new zn;return iu(e,a),ou(t,a,this._defsUsePending,!1,!0),this._textX+=parseFloat(n),this._textY+=parseFloat(o),a},path:function(t,e){var r=ss(t.getAttribute("d")||"");return iu(e,r),ou(t,r,this._defsUsePending,!1,!1),r.silent=!0,r}}),t}(),tu={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||"0",10),r=parseInt(t.getAttribute("y1")||"0",10),i=parseInt(t.getAttribute("x2")||"10",10),n=parseInt(t.getAttribute("y2")||"0",10),o=new Gs(e,r,i,n);return eu(t,o),ru(t,o),o},radialgradient:function(t){var e=parseInt(t.getAttribute("cx")||"0",10),r=parseInt(t.getAttribute("cy")||"0",10),i=parseInt(t.getAttribute("r")||"0",10),n=new Zs(e,r,i);return eu(t,n),ru(t,n),n}};function eu(t,e){"userSpaceOnUse"===t.getAttribute("gradientUnits")&&(e.global=!0)}function ru(t,e){for(var r=t.firstChild;r;){if(1===r.nodeType&&"stop"===r.nodeName.toLocaleLowerCase()){var i=r.getAttribute("offset"),n=void 0;n=i&&i.indexOf("%")>0?parseInt(i,10)/100:i?parseFloat(i):0;var o={};pu(r,o,o);var a=o.stopColor||r.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:n,color:a})}r=r.nextSibling}}function iu(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),C(e.__inheritedStyle,t.__inheritedStyle))}function nu(t){for(var e=lu(t),r=[],i=0;i<e.length;i+=2){var n=parseFloat(e[i]),o=parseFloat(e[i+1]);r.push([n,o])}return r}function ou(t,e,r,i,n){var o=e,a=o.__inheritedStyle=o.__inheritedStyle||{},s={};1===t.nodeType&&(function(t,e){var r=t.getAttribute("transform");if(r){r=r.replace(/,/g," ");var i=[],n=null;r.replace(uu,(function(t,e,r){return i.push(e,r),""}));for(var o=i.length-1;o>0;o-=2){var a=i[o],s=i[o-1],h=lu(a);switch(n=n||[1,0,0,1,0,0],s){case"translate":me(n,n,[parseFloat(h[0]),parseFloat(h[1]||"0")]);break;case"scale":we(n,n,[parseFloat(h[0]),parseFloat(h[1]||h[0])]);break;case"rotate":xe(n,n,-parseFloat(h[0])*fu,[parseFloat(h[1]||"0"),parseFloat(h[2]||"0")]);break;case"skewX":_e(n,[1,0,Math.tan(parseFloat(h[0])*fu),1,0,0],n);break;case"skewY":_e(n,[1,Math.tan(parseFloat(h[0])*fu),0,1,0,0],n);break;case"matrix":n[0]=parseFloat(h[0]),n[1]=parseFloat(h[1]),n[2]=parseFloat(h[2]),n[3]=parseFloat(h[3]),n[4]=parseFloat(h[4]),n[5]=parseFloat(h[5])}}e.setLocalTransform(n)}}(t,e),pu(t,a,s),i||function(t,e,r){for(var i=0;i<Kl.length;i++){var n=Kl[i];null!=(o=t.getAttribute(n))&&(e[Zl[n]]=o)}for(i=0;i<$l.length;i++){var o;n=$l[i];null!=(o=t.getAttribute(n))&&(r[Ql[n]]=o)}}(t,a,s)),o.style=o.style||{},null!=a.fill&&(o.style.fill=su(o,"fill",a.fill,r)),null!=a.stroke&&(o.style.stroke=su(o,"stroke",a.stroke,r)),D(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],(function(t){null!=a[t]&&(o.style[t]=parseFloat(a[t]))})),D(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],(function(t){null!=a[t]&&(o.style[t]=a[t])})),n&&(o.__selfStyle=s),a.lineDash&&(o.style.lineDash=z(lu(a.lineDash),(function(t){return parseFloat(t)}))),"hidden"!==a.visibility&&"collapse"!==a.visibility||(o.invisible=!0),"none"===a.display&&(o.ignore=!0)}var au=/^url\(\s*#(.*?)\)/;function su(t,e,r,i){var n=r&&r.match(au);if(!n)return"none"===r&&(r=null),r;var o=ot(n[1]);i.push([t,e,o])}var hu=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function lu(t){return t.match(hu)||[]}var uu=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,fu=Math.PI/180;var cu=/([^\s:;]+)\s*:\s*([^:;]+)/g;function pu(t,e,r){var i,n=t.getAttribute("style");if(n)for(cu.lastIndex=0;null!=(i=cu.exec(n));){var o=i[1],a=yt(Zl,o)?Zl[o]:null;a&&(e[a]=i[2]);var s=yt(Ql,o)?Ql[o]:null;s&&(r[s]=i[2])}}function du(t,e){var r=e.width/t.width,i=e.height/t.height,n=Math.min(r,i);return{scale:n,x:-(t.x+t.width/2)*n+(e.x+e.width/2),y:-(t.y+t.height/2)*n+(e.y+e.height/2)}}function vu(t,e){return(new Jl).parse(t,e)}var yu=Zo.CMD;function gu(t,e){return Math.abs(t-e)<1e-5}function _u(t){var e,r,i,n,o,a,s,h,l,u,f,c,p,d,v,y,g,_,m,x,w,b,k,S,T=t.data,C=t.len(),P=[],M=0,A=0,L=0,I=0;function D(t,r){e&&e.length>2&&P.push(e),e=[t,r]}function z(t,r,i,n){gu(t,i)&&gu(r,n)||e.push(t,r,i,n,i,n)}for(var O=0;O<C;){var R=T[O++],F=1===O;switch(F&&(L=M=T[O],I=A=T[O+1],R!==yu.L&&R!==yu.C&&R!==yu.Q||(e=[L,I])),R){case yu.M:M=L=T[O++],A=I=T[O++],D(L,I);break;case yu.L:z(M,A,r=T[O++],i=T[O++]),M=r,A=i;break;case yu.C:e.push(T[O++],T[O++],T[O++],T[O++],M=T[O++],A=T[O++]);break;case yu.Q:r=T[O++],i=T[O++],n=T[O++],o=T[O++],e.push(M+2/3*(r-M),A+2/3*(i-A),n+2/3*(r-n),o+2/3*(i-o),n,o),M=n,A=o;break;case yu.A:var B=T[O++],N=T[O++],H=T[O++],E=T[O++],W=T[O++],X=T[O++]+W;O+=1;var j=!T[O++];r=Math.cos(W)*H+B,i=Math.sin(W)*E+N,F?D(L=r,I=i):z(M,A,r,i),M=Math.cos(X)*H+B,A=Math.sin(X)*E+N;for(var q=(j?-1:1)*Math.PI/2,Y=W;j?Y>X:Y<X;Y+=q){var V=j?Math.max(Y+q,X):Math.min(Y+q,X);a=Y,s=V,h=B,l=N,u=H,f=E,c=void 0,p=void 0,d=void 0,v=void 0,y=void 0,g=void 0,_=void 0,m=void 0,x=void 0,w=void 0,b=void 0,k=void 0,S=void 0,c=Math.abs(s-a),p=4*Math.tan(c/4)/3,d=s<a?-1:1,v=Math.cos(a),y=Math.sin(a),g=Math.cos(s),_=Math.sin(s),m=v*u+h,x=y*f+l,w=g*u+h,b=_*f+l,k=u*p*d,S=f*p*d,e.push(m-k*y,x+S*v,w+k*_,b-S*g,w,b)}break;case yu.R:L=M=T[O++],I=A=T[O++],r=L+T[O++],i=I+T[O++],D(r,I),z(r,I,r,i),z(r,i,L,i),z(L,i,L,I),z(L,I,r,I);break;case yu.Z:e&&z(M,A,L,I),M=L,A=I}}return e&&e.length>2&&P.push(e),P}function mu(t,e,r,i,n,o,a,s,h,l){if(gu(t,r)&&gu(e,i)&&gu(n,a)&&gu(o,s))h.push(a,s);else{var u=2/l,f=u*u,c=a-t,p=s-e,d=Math.sqrt(c*c+p*p);c/=d,p/=d;var v=r-t,y=i-e,g=n-a,_=o-s,m=v*v+y*y,x=g*g+_*_;if(m<f&&x<f)h.push(a,s);else{var w=c*v+p*y,b=-c*g-p*_;if(m-w*w<f&&w>=0&&x-b*b<f&&b>=0)h.push(a,s);else{var k=[],S=[];gr(t,r,n,a,.5,k),gr(e,i,o,s,.5,S),mu(k[0],S[0],k[1],S[1],k[2],S[2],k[3],S[3],h,l),mu(k[4],S[4],k[5],S[5],k[6],S[6],k[7],S[7],h,l)}}}}function xu(t,e,r){var i=t[e],n=t[1-e],o=Math.abs(i/n),a=Math.ceil(Math.sqrt(o*r)),s=Math.floor(r/a);0===s&&(s=1,a=r);for(var h=[],l=0;l<a;l++)h.push(s);var u=r-a*s;if(u>0)for(l=0;l<u;l++)h[l%a]+=1;return h}function wu(t,e,r){for(var i=t.r0,n=t.r,o=t.startAngle,a=t.endAngle,s=Math.abs(a-o),h=s*n,l=n-i,u=h>Math.abs(l),f=xu([h,l],u?0:1,e),c=(u?s:l)/f.length,p=0;p<f.length;p++)for(var d=(u?l:s)/f[p],v=0;v<f[p];v++){var y={};u?(y.startAngle=o+c*p,y.endAngle=o+c*(p+1),y.r0=i+d*v,y.r=i+d*(v+1)):(y.startAngle=o+d*v,y.endAngle=o+d*(v+1),y.r0=i+c*p,y.r=i+c*(p+1)),y.clockwise=t.clockwise,y.cx=t.cx,y.cy=t.cy,r.push(y)}}function bu(t,e,r,i){return t*i-r*e}function ku(t,e,r,i,n,o,a,s){var h=r-t,l=i-e,u=a-n,f=s-o,c=bu(u,f,h,l);if(Math.abs(c)<1e-6)return null;var p=bu(t-n,e-o,u,f)/c;return p<0||p>1?null:new Te(p*h+t,p*l+e)}function Su(t,e,r){var i=new Te;Te.sub(i,r,e),i.normalize();var n=new Te;return Te.sub(n,t,e),n.dot(i)}function Tu(t,e){var r=t[t.length-1];r&&r[0]===e[0]&&r[1]===e[1]||t.push(e)}function Cu(t){var e=t.points,r=[],i=[];So(e,r,i);var n=new Oe(r[0],r[1],i[0]-r[0],i[1]-r[1]),o=n.width,a=n.height,s=n.x,h=n.y,l=new Te,u=new Te;return o>a?(l.x=u.x=s+o/2,l.y=h,u.y=h+a):(l.y=u.y=h+a/2,l.x=s,u.x=s+o),function(t,e,r){for(var i=t.length,n=[],o=0;o<i;o++){var a=t[o],s=t[(o+1)%i],h=ku(a[0],a[1],s[0],s[1],e.x,e.y,r.x,r.y);h&&n.push({projPt:Su(h,e,r),pt:h,idx:o})}if(n.length<2)return[{points:t},{points:t}];n.sort((function(t,e){return t.projPt-e.projPt}));var l=n[0],u=n[n.length-1];if(u.idx<l.idx){var f=l;l=u,u=f}var c=[l.pt.x,l.pt.y],p=[u.pt.x,u.pt.y],d=[c],v=[p];for(o=l.idx+1;o<=u.idx;o++)Tu(d,t[o].slice());for(Tu(d,p),Tu(d,c),o=u.idx+1;o<=l.idx+i;o++)Tu(v,t[o%i].slice());return Tu(v,c),Tu(v,p),[{points:d},{points:v}]}(e,l,u)}function Pu(t,e,r,i){if(1===r)i.push(e);else{var n=Math.floor(r/2),o=t(e);Pu(t,o[0],n,i),Pu(t,o[1],r-n,i)}return i}function Mu(t,e){var r,i=[],n=t.shape;switch(t.type){case"rect":!function(t,e,r){for(var i=t.width,n=t.height,o=i>n,a=xu([i,n],o?0:1,e),s=o?"width":"height",h=o?"height":"width",l=o?"x":"y",u=o?"y":"x",f=t[s]/a.length,c=0;c<a.length;c++)for(var p=t[h]/a[c],d=0;d<a[c];d++){var v={};v[l]=c*f,v[u]=d*p,v[s]=f,v[h]=p,v.x+=t.x,v.y+=t.y,r.push(v)}}(n,e,i),r=Pa;break;case"sector":wu(n,e,i),r=As;break;case"circle":wu({r0:0,r:n.r,startAngle:0,endAngle:2*Math.PI,cx:n.cx,cy:n.cy},e,i),r=As;break;default:var o=t.getComputedTransform(),a=o?Math.sqrt(Math.max(o[0]*o[0]+o[1]*o[1],o[2]*o[2]+o[3]*o[3])):1,s=z(function(t,e){var r=_u(t),i=[];e=e||1;for(var n=0;n<r.length;n++){var o=r[n],a=[],s=o[0],h=o[1];a.push(s,h);for(var l=2;l<o.length;){var u=o[l++],f=o[l++],c=o[l++],p=o[l++],d=o[l++],v=o[l++];mu(s,h,u,f,c,p,d,v,a,e),s=d,h=v}i.push(a)}return i}(t.getUpdatedPathProxy(),a),(function(t){return function(t){for(var e=[],r=0;r<t.length;)e.push([t[r++],t[r++]]);return e}(t)})),h=s.length;if(0===h)Pu(Cu,{points:s[0]},e,i);else if(h===e)for(var l=0;l<h;l++)i.push({points:s[l]});else{var u=0,f=z(s,(function(t){var e=[],r=[];So(t,e,r);var i=(r[1]-e[1])*(r[0]-e[0]);return u+=i,{poly:t,area:i}}));f.sort((function(t,e){return e.area-t.area}));var c=e;for(l=0;l<h;l++){var p=f[l];if(c<=0)break;var d=l===h-1?c:Math.ceil(p.area/u*e);d<0||(Pu(Cu,{points:p.poly},d,i),c-=d)}}r=Os}if(!r)return function(t,e){for(var r=[],i=0;i<e;i++)r.push(us(t));return r}(t,e);var v,y,g=[];for(l=0;l<i.length;l++){var _=new r;_.setShape(i[l]),v=t,(y=_).setStyle(v.style),y.z=v.z,y.z2=v.z2,y.zlevel=v.zlevel,g.push(_)}return g}function Au(t,e){var r=t.length,i=e.length;if(r===i)return[t,e];for(var n=[],o=[],a=r<i?t:e,s=Math.min(r,i),h=Math.abs(i-r)/6,l=(s-2)/6,u=Math.ceil(h/l)+1,f=[a[0],a[1]],c=h,p=2;p<s;){var d=a[p-2],v=a[p-1],y=a[p++],g=a[p++],_=a[p++],m=a[p++],x=a[p++],w=a[p++];if(c<=0)f.push(y,g,_,m,x,w);else{for(var b=Math.min(c,u-1)+1,k=1;k<=b;k++){var S=k/b;gr(d,y,_,x,S,n),gr(v,g,m,w,S,o),d=n[3],v=o[3],f.push(n[1],o[1],n[2],o[2],d,v),y=n[5],g=o[5],_=n[6],m=o[6]}c-=b-1}}return a===t?[f,e]:[t,f]}function Lu(t,e){for(var r=t.length,i=t[r-2],n=t[r-1],o=[],a=0;a<e.length;)o[a++]=i,o[a++]=n;return o}function Iu(t){for(var e=0,r=0,i=0,n=t.length,o=0,a=n-2;o<n;a=o,o+=2){var s=t[a],h=t[a+1],l=t[o],u=t[o+1],f=s*u-l*h;e+=f,r+=(s+l)*f,i+=(h+u)*f}return 0===e?[t[0]||0,t[1]||0]:[r/e/3,i/e/3,e]}function Du(t,e,r,i){for(var n=(t.length-2)/6,o=Infinity,a=0,s=t.length,h=s-2,l=0;l<n;l++){for(var u=6*l,f=0,c=0;c<s;c+=2){var p=0===c?u:(u+c-2)%h+2,d=t[p]-r[0],v=t[p+1]-r[1],y=e[c]-i[0]-d,g=e[c+1]-i[1]-v;f+=y*y+g*g}f<o&&(o=f,a=l)}return a}function zu(t){for(var e=[],r=t.length,i=0;i<r;i+=2)e[i]=t[r-i-2],e[i+1]=t[r-i-1];return e}function Ou(t){return t.__isCombineMorphing}var Ru="__mOriginal_";function Fu(t,e,r){var i=Ru+e,n=t[i]||t[e];t[i]||(t[i]=t[e]);var o=r.replace,a=r.after,s=r.before;t[e]=function(){var t,e=arguments;return s&&s.apply(this,e),t=o?o.apply(this,e):n.apply(this,e),a&&a.apply(this,e),t}}function Bu(t,e){var r=Ru+e;t[r]&&(t[e]=t[r],t[r]=null)}function Nu(t,e){for(var r=0;r<t.length;r++)for(var i=t[r],n=0;n<i.length;){var o=i[n],a=i[n+1];i[n++]=e[0]*o+e[2]*a+e[4],i[n++]=e[1]*o+e[3]*a+e[5]}}function Hu(t,e){var r=t.getUpdatedPathProxy(),i=e.getUpdatedPathProxy(),n=function(t,e){for(var r,i,n,o=[],a=[],s=0;s<Math.max(t.length,e.length);s++){var h=t[s],l=e[s],u=void 0,f=void 0;h?l?(i=u=(r=Au(h,l))[0],n=f=r[1]):(f=Lu(n||h,h),u=h):(u=Lu(i||l,l),f=l),o.push(u),a.push(f)}return[o,a]}(_u(r),_u(i)),o=n[0],a=n[1],s=t.getComputedTransform(),h=e.getComputedTransform();s&&Nu(o,s),h&&Nu(a,h),Fu(e,"updateTransform",{replace:function(){this.transform=null}}),e.transform=null;var l=function(t,e,r,i){for(var n,o=[],a=0;a<t.length;a++){var s=t[a],h=e[a],l=Iu(s),u=Iu(h);null==n&&(n=l[2]<0!=u[2]<0);var f=[],c=[],p=0,d=Infinity,v=[],y=s.length;n&&(s=zu(s));for(var g=6*Du(s,h,l,u),_=y-2,m=0;m<_;m+=2){var x=(g+m)%_+2;f[m+2]=s[x]-l[0],f[m+3]=s[x+1]-l[1]}f[0]=s[g]-l[0],f[1]=s[g+1]-l[1];for(var w=i/r,b=-i/2;b<=i/2;b+=w){var k=Math.sin(b),S=Math.cos(b),T=0;for(m=0;m<s.length;m+=2){var C=f[m],P=f[m+1],M=h[m]-u[0],A=h[m+1]-u[1],L=M*S-A*k,I=M*k+A*S;v[m]=L,v[m+1]=I;var D=L-C,z=I-P;T+=D*D+z*z}if(T<d){d=T,p=b;for(var O=0;O<v.length;O++)c[O]=v[O]}}o.push({from:f,to:c,fromCp:l,toCp:u,rotation:-p})}return o}(o,a,10,Math.PI),u=[];Fu(e,"buildPath",{replace:function(t){for(var r=e.__morphT,i=1-r,n=[],o=0;o<l.length;o++){var a=l[o],s=a.from,h=a.to,f=a.rotation*r,c=a.fromCp,p=a.toCp,d=Math.sin(f),v=Math.cos(f);Bt(n,c,p,r);for(var y=0;y<s.length;y+=2){var g=s[y],_=s[y+1],m=g*i+(k=h[y])*r,x=_*i+(S=h[y+1])*r;u[y]=m*v-x*d+n[0],u[y+1]=m*d+x*v+n[1]}var w=u[0],b=u[1];t.moveTo(w,b);for(y=2;y<s.length;){var k=u[y++],S=u[y++],T=u[y++],C=u[y++],P=u[y++],M=u[y++];w===k&&b===S&&T===P&&C===M?t.lineTo(P,M):t.bezierCurveTo(k,S,T,C,P,M),w=P,b=M}}}})}function Eu(t,e,r){if(!t||!e)return e;var i=r.done,n=r.during;return Hu(t,e),e.__morphT=0,e.animateTo({__morphT:1},C({during:function(t){e.dirtyShape(),n&&n(t)},done:function(){Bu(e,"buildPath"),Bu(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape(),i&&i()}},r)),e}function Wu(t,e,r,i,n,o){t=n===r?0:Math.round(32767*(t-r)/(n-r)),e=o===i?0:Math.round(32767*(e-i)/(o-i));for(var a,s=0,h=32768;h>0;h/=2){var l=0,u=0;(t&h)>0&&(l=1),(e&h)>0&&(u=1),s+=h*h*(3*l^u),0===u&&(1===l&&(t=h-1-t,e=h-1-e),a=t,t=e,e=a)}return s}function Xu(t){var e=Infinity,r=Infinity,i=-Infinity,n=-Infinity,o=z(t,(function(t){var o=t.getBoundingRect(),a=t.getComputedTransform(),s=o.x+o.width/2+(a?a[4]:0),h=o.y+o.height/2+(a?a[5]:0);return e=Math.min(s,e),r=Math.min(h,r),i=Math.max(s,i),n=Math.max(h,n),[s,h]}));return z(o,(function(o,a){return{cp:o,z:Wu(o[0],o[1],e,r,i,n),path:t[a]}})).sort((function(t,e){return t.z-e.z})).map((function(t){return t.path}))}function ju(t){return Mu(t.path,t.count)}function qu(t,e,r){var i=[];!function t(e){for(var r=0;r<e.length;r++){var n=e[r];Ou(n)?t(n.childrenRef()):n instanceof va&&i.push(n)}}(t);var n=i.length;if(!n)return{fromIndividuals:[],toIndividuals:[],count:0};var o=(r.dividePath||ju)({path:e,count:n});if(o.length!==n)return{fromIndividuals:[],toIndividuals:[],count:0};i=Xu(i),o=Xu(o);for(var a=r.done,s=r.during,h=r.individualDelay,l=new fn,u=0;u<n;u++){var f=i[u],c=o[u];c.parent=e,c.copyTransform(l),h||Hu(f,c)}function p(t){for(var e=0;e<o.length;e++)o[e].addSelfToZr(t)}function d(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,Bu(e,"addSelfToZr"),Bu(e,"removeSelfFromZr")}e.__isCombineMorphing=!0,e.childrenRef=function(){return o},Fu(e,"addSelfToZr",{after:function(t){p(t)}}),Fu(e,"removeSelfFromZr",{after:function(t){for(var e=0;e<o.length;e++)o[e].removeSelfFromZr(t)}});var v=o.length;if(h){var y=v,g=function(){0===--y&&(d(),a&&a())};for(u=0;u<v;u++){var _=h?C({delay:(r.delay||0)+h(u,v,i[u],o[u]),done:g},r):r;Eu(i[u],o[u],_)}}else e.__morphT=0,e.animateTo({__morphT:1},C({during:function(t){for(var r=0;r<v;r++){var i=o[r];i.__morphT=e.__morphT,i.dirtyShape()}s&&s(t)},done:function(){d();for(var e=0;e<t.length;e++)Bu(t[e],"updateTransform");a&&a()}},r));return e.__zr&&p(e.__zr),{fromIndividuals:i,toIndividuals:o,count:v}}function Yu(t,e,r){var i=e.length,n=[],o=r.dividePath||ju;if(Ou(t)){!function t(e){for(var r=0;r<e.length;r++){var i=e[r];Ou(i)?t(i.childrenRef()):i instanceof va&&n.push(i)}}(t.childrenRef());var a=n.length;if(a<i)for(var s=0,h=a;h<i;h++)n.push(us(n[s++%a]));n.length=i}else{n=o({path:t,count:i});var l=t.getComputedTransform();for(h=0;h<n.length;h++)n[h].setLocalTransform(l);if(n.length!==i)return{fromIndividuals:[],toIndividuals:[],count:0}}n=Xu(n),e=Xu(e);var u=r.individualDelay;for(h=0;h<i;h++){var f=u?C({delay:(r.delay||0)+u(h,i,n[h],e[h])},r):r;Eu(n[h],e[h],f)}return{fromIndividuals:n,toIndividuals:e,count:e.length}}export{ot as $,ye as A,_e as B,hs as C,ba as D,ka as E,cs as F,ds as G,Os as H,Fs as I,Pa as J,js as K,Hs as L,Ys as M,Oe as N,Vs as O,va as P,zn as Q,Is as R,As as S,fn as T,rh as U,Gs as V,th as W,Te as X,Zs as Y,xa as Z,La as _,nt as a,$r as a$,k as a0,b as a1,L as a2,it as a3,ee as a4,H as a5,U as a6,R as a7,N as a8,st as a9,ta as aA,El as aB,Ul as aC,vr as aD,pr as aE,Zr as aF,Go as aG,et as aH,J as aI,xe as aJ,F as aK,fe as aL,ue as aM,Gl as aN,vu as aO,du as aP,co as aQ,ge as aR,ve as aS,wt as aT,S as aU,So as aV,me as aW,we as aX,qr as aY,Jr as aZ,Ur as a_,pt as aa,gt as ab,bn as ac,oh as ad,Ir as ae,a as af,Th as ag,qt as ah,Nn as ai,Ke as aj,s as ak,Wn as al,Hn as am,rt as an,wn as ao,$ as ap,Mh as aq,Ht as ar,Et as as,Zn as at,gn as au,Ot as av,Bt as aw,Zo as ax,Sr as ay,_r as az,E as b,Qr as b0,bt as b1,Dt as b2,It as b3,xt as b4,Ct as b5,Pt as b6,Tt as b7,kt as b8,kr as b9,xr as ba,Ft as bb,ke as bc,wr as bd,Ko as be,$o as bf,Vr as bg,cn as bh,Ci as bi,G as bj,le as bk,he as bl,Kt as bm,Q as bn,Ou as bo,Eu as bp,qu as bq,Yu as br,us as bs,ii as bt,Se as bu,Wt as bv,mt as bw,Xn as bx,Di as by,tr as bz,Y as c,ct as d,D as e,M as f,q as g,j as h,X as i,r as j,A as k,T as l,z as m,W as n,I as o,B as p,ri as q,O as r,tt as s,ss as t,Sa as u,ls as v,yt as w,C as x,be as y,Nt as z};
