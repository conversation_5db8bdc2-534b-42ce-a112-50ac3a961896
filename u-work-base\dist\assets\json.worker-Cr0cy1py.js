!function(){"use strict";const e=new class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout((()=>{if(e.stack){if(s.isErrorNoTelemetry(e))throw new s(e.message+"\n\n"+e.stack);throw new Error(e.message+"\n\n"+e.stack)}throw e}),0)}}emit(e){this.listeners.forEach((t=>{t(e)}))}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}};function t(t){(function(e){if(e instanceof i)return!0;return e instanceof Error&&e.name===r&&e.message===r})(t)||e.onUnexpectedError(t)}function n(e){if(e instanceof Error){const{name:t,message:n}=e;return{$isError:!0,name:t,message:n,stack:e.stacktrace||e.stack,noTelemetry:s.isErrorNoTelemetry(e)}}return e}const r="Canceled";class i extends Error{constructor(){super(r),this.name=this.message}}class s extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof s)return e;const t=new s;return t.message=e.message,t.stack=e.stack,t}static isErrorNoTelemetry(e){return"CodeExpectedError"===e.name}}class o extends Error{constructor(e){super(e||"An unexpected bug occurred."),Object.setPrototypeOf(this,o.prototype)}}function a(e,t){const n=this;let r,i=!1;return function(){return i||(i=!0,r=e.apply(n,arguments)),r}}var l;function u(e){if(l.is(e)){const n=[];for(const r of e)if(r)try{r.dispose()}catch(t){n.push(t)}if(1===n.length)throw n[0];if(n.length>1)throw new AggregateError(n,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}if(e)return e.dispose(),e}function c(e){return{dispose:a((()=>{e()}))}}!function(e){function t(e){return e&&"object"==typeof e&&"function"==typeof e[Symbol.iterator]}e.is=t;const n=Object.freeze([]);function*r(e){yield e}e.empty=function(){return n},e.single=r,e.wrap=function(e){return t(e)?e:r(e)},e.from=function(e){return e||n},e.reverse=function*(e){for(let t=e.length-1;t>=0;t--)yield e[t]},e.isEmpty=function(e){return!e||!0===e[Symbol.iterator]().next().done},e.first=function(e){return e[Symbol.iterator]().next().value},e.some=function(e,t){for(const n of e)if(t(n))return!0;return!1},e.find=function(e,t){for(const n of e)if(t(n))return n},e.filter=function*(e,t){for(const n of e)t(n)&&(yield n)},e.map=function*(e,t){let n=0;for(const r of e)yield t(r,n++)},e.concat=function*(...e){for(const t of e)yield*t},e.reduce=function(e,t,n){let r=n;for(const i of e)r=t(r,i);return r},e.slice=function*(e,t,n=e.length){for(t<0&&(t+=e.length),n<0?n+=e.length:n>e.length&&(n=e.length);t<n;t++)yield e[t]},e.consume=function(t,n=Number.POSITIVE_INFINITY){const r=[];if(0===n)return[r,t];const i=t[Symbol.iterator]();for(let s=0;s<n;s++){const t=i.next();if(t.done)return[r,e.empty()];r.push(t.value)}return[r,{[Symbol.iterator]:()=>i}]},e.asyncToArray=async function(e){const t=[];for await(const n of e)t.push(n);return Promise.resolve(t)}}(l||(l={}));class h{constructor(){this._toDispose=new Set,this._isDisposed=!1}dispose(){this._isDisposed||(this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(0!==this._toDispose.size)try{u(this._toDispose)}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return this._isDisposed?h.DISABLE_DISPOSED_WARNING:this._toDispose.add(e),e}deleteAndLeak(e){e&&this._toDispose.has(e)&&this._toDispose.delete(e)}}h.DISABLE_DISPOSED_WARNING=!1;class d{constructor(){this._store=new h,this._store}dispose(){this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}}d.None=Object.freeze({dispose(){}});class f{constructor(e){this.element=e,this.next=f.Undefined,this.prev=f.Undefined}}f.Undefined=new f(void 0);class m{constructor(){this._first=f.Undefined,this._last=f.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===f.Undefined}clear(){let e=this._first;for(;e!==f.Undefined;){const t=e.next;e.prev=f.Undefined,e.next=f.Undefined,e=t}this._first=f.Undefined,this._last=f.Undefined,this._size=0}unshift(e){return this._insert(e,!1)}push(e){return this._insert(e,!0)}_insert(e,t){const n=new f(e);if(this._first===f.Undefined)this._first=n,this._last=n;else if(t){const e=this._last;this._last=n,n.prev=e,e.next=n}else{const e=this._first;this._first=n,n.next=e,e.prev=n}this._size+=1;let r=!1;return()=>{r||(r=!0,this._remove(n))}}shift(){if(this._first!==f.Undefined){const e=this._first.element;return this._remove(this._first),e}}pop(){if(this._last!==f.Undefined){const e=this._last.element;return this._remove(this._last),e}}_remove(e){if(e.prev!==f.Undefined&&e.next!==f.Undefined){const t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===f.Undefined&&e.next===f.Undefined?(this._first=f.Undefined,this._last=f.Undefined):e.next===f.Undefined?(this._last=this._last.prev,this._last.next=f.Undefined):e.prev===f.Undefined&&(this._first=this._first.next,this._first.prev=f.Undefined);this._size-=1}*[Symbol.iterator](){let e=this._first;for(;e!==f.Undefined;)yield e.element,e=e.next}}const g=globalThis.performance&&"function"==typeof globalThis.performance.now;class p{static create(e){return new p(e)}constructor(e){this._now=g&&!1===e?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}elapsed(){return-1!==this._stopTime?this._stopTime-this._startTime:this._now()-this._startTime}}var v;!function(e){function t(e){return(t,n=null,r)=>{let i,s=!1;return i=e((e=>{if(!s)return i?i.dispose():s=!0,t.call(n,e)}),null,r),s&&i.dispose(),i}}function n(e,t,n){return i(((n,r=null,i)=>e((e=>n.call(r,t(e))),null,i)),n)}function r(e,t,n){return i(((n,r=null,i)=>e((e=>t(e)&&n.call(r,e)),null,i)),n)}function i(e,t){let n;const r=new w({onWillAddFirstListener(){n=e(r.fire,r)},onDidRemoveLastListener(){null==n||n.dispose()}});return null==t||t.add(r),r.event}function s(e,t,n=100,r=!1,i=!1,s,o){let a,l,u,c,h=0;const d=new w({leakWarningThreshold:s,onWillAddFirstListener(){a=e((e=>{h++,l=t(l,e),r&&!u&&(d.fire(l),l=void 0),c=()=>{const e=l;l=void 0,u=void 0,(!r||h>1)&&d.fire(e),h=0},"number"==typeof n?(clearTimeout(u),u=setTimeout(c,n)):void 0===u&&(u=0,queueMicrotask(c))}))},onWillRemoveListener(){i&&h>0&&(null==c||c())},onDidRemoveLastListener(){c=void 0,a.dispose()}});return null==o||o.add(d),d.event}e.None=()=>d.None,e.defer=function(e,t){return s(e,(()=>{}),0,void 0,!0,void 0,t)},e.once=t,e.map=n,e.forEach=function(e,t,n){return i(((n,r=null,i)=>e((e=>{t(e),n.call(r,e)}),null,i)),n)},e.filter=r,e.signal=function(e){return e},e.any=function(...e){return(t,n=null,r)=>{const i=function(...e){return c((()=>u(e)))}(...e.map((e=>e((e=>t.call(n,e))))));return function(e,t){t instanceof Array?t.push(e):t&&t.add(e);return e}(i,r)}},e.reduce=function(e,t,r,i){let s=r;return n(e,(e=>(s=t(s,e),s)),i)},e.debounce=s,e.accumulate=function(t,n=0,r){return e.debounce(t,((e,t)=>e?(e.push(t),e):[t]),n,void 0,!0,void 0,r)},e.latch=function(e,t=(e,t)=>e===t,n){let i,s=!0;return r(e,(e=>{const n=s||!t(e,i);return s=!1,i=e,n}),n)},e.split=function(t,n,r){return[e.filter(t,n,r),e.filter(t,(e=>!n(e)),r)]},e.buffer=function(e,t=!1,n=[],r){let i=n.slice(),s=e((e=>{i?i.push(e):a.fire(e)}));r&&r.add(s);const o=()=>{null==i||i.forEach((e=>a.fire(e))),i=null},a=new w({onWillAddFirstListener(){s||(s=e((e=>a.fire(e))),r&&r.add(s))},onDidAddFirstListener(){i&&(t?setTimeout(o):o())},onDidRemoveLastListener(){s&&s.dispose(),s=null}});return r&&r.add(a),a.event},e.chain=function(e,t){return(n,r,i)=>{const s=t(new a);return e((function(e){const t=s.evaluate(e);t!==o&&n.call(r,t)}),void 0,i)}};const o=Symbol("HaltChainable");class a{constructor(){this.steps=[]}map(e){return this.steps.push(e),this}forEach(e){return this.steps.push((t=>(e(t),t))),this}filter(e){return this.steps.push((t=>e(t)?t:o)),this}reduce(e,t){let n=t;return this.steps.push((t=>(n=e(n,t),n))),this}latch(e=(e,t)=>e===t){let t,n=!0;return this.steps.push((r=>{const i=n||!e(r,t);return n=!1,t=r,i?r:o})),this}evaluate(e){for(const t of this.steps)if((e=t(e))===o)break;return e}}e.fromNodeEventEmitter=function(e,t,n=e=>e){const r=(...e)=>i.fire(n(...e)),i=new w({onWillAddFirstListener:()=>e.on(t,r),onDidRemoveLastListener:()=>e.removeListener(t,r)});return i.event},e.fromDOMEventEmitter=function(e,t,n=e=>e){const r=(...e)=>i.fire(n(...e)),i=new w({onWillAddFirstListener:()=>e.addEventListener(t,r),onDidRemoveLastListener:()=>e.removeEventListener(t,r)});return i.event},e.toPromise=function(e){return new Promise((n=>t(e)(n)))},e.fromPromise=function(e){const t=new w;return e.then((e=>{t.fire(e)}),(()=>{t.fire(void 0)})).finally((()=>{t.dispose()})),t.event},e.runAndSubscribe=function(e,t,n){return t(n),e((e=>t(e)))};class l{constructor(e,t){this._observable=e,this._counter=0,this._hasChanged=!1;const n={onWillAddFirstListener:()=>{e.addObserver(this)},onDidRemoveLastListener:()=>{e.removeObserver(this)}};this.emitter=new w(n),t&&t.add(this.emitter)}beginUpdate(e){this._counter++}handlePossibleChange(e){}handleChange(e,t){this._hasChanged=!0}endUpdate(e){this._counter--,0===this._counter&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}e.fromObservable=function(e,t){return new l(e,t).emitter.event},e.fromObservableLight=function(e){return(t,n,r)=>{let i=0,s=!1;const o={beginUpdate(){i++},endUpdate(){i--,0===i&&(e.reportChanges(),s&&(s=!1,t.call(n)))},handlePossibleChange(){},handleChange(){s=!0}};e.addObserver(o),e.reportChanges();const a={dispose(){e.removeObserver(o)}};return r instanceof h?r.add(a):Array.isArray(r)&&r.push(a),a}}}(v||(v={}));class b{constructor(e){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${e}_${b._idPool++}`,b.all.add(this)}start(e){this._stopWatch=new p,this.listenerCount=e}stop(){if(this._stopWatch){const e=this._stopWatch.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this._stopWatch=void 0}}}b.all=new Set,b._idPool=0;class y{constructor(e,t=Math.random().toString(18).slice(2,5)){this.threshold=e,this.name=t,this._warnCountdown=0}dispose(){var e;null===(e=this._stacks)||void 0===e||e.clear()}check(e,t){const n=this.threshold;if(n<=0||t<n)return;this._stacks||(this._stacks=new Map);const r=this._stacks.get(e.value)||0;if(this._stacks.set(e.value,r+1),this._warnCountdown-=1,this._warnCountdown<=0){let e;this._warnCountdown=.5*n;let t=0;for(const[n,r]of this._stacks)(!e||t<r)&&(e=n,t=r)}return()=>{const t=this._stacks.get(e.value)||0;this._stacks.set(e.value,t-1)}}}class C{static create(){var e;return new C(null!==(e=(new Error).stack)&&void 0!==e?e:"")}constructor(e){this.value=e}print(){}}class _{constructor(e){this.value=e}}class w{constructor(e){var t,n,r,i,s;this._size=0,this._options=e,this._leakageMon=(null===(t=this._options)||void 0===t?void 0:t.leakWarningThreshold)?new y(null!==(r=null===(n=this._options)||void 0===n?void 0:n.leakWarningThreshold)&&void 0!==r?r:-1):void 0,this._perfMon=(null===(i=this._options)||void 0===i?void 0:i._profName)?new b(this._options._profName):void 0,this._deliveryQueue=null===(s=this._options)||void 0===s?void 0:s.deliveryQueue}dispose(){var e,t,n,r;this._disposed||(this._disposed=!0,(null===(e=this._deliveryQueue)||void 0===e?void 0:e.current)===this&&this._deliveryQueue.reset(),this._listeners&&(this._listeners=void 0,this._size=0),null===(n=null===(t=this._options)||void 0===t?void 0:t.onDidRemoveLastListener)||void 0===n||n.call(t),null===(r=this._leakageMon)||void 0===r||r.dispose())}get event(){var e;return null!==(e=this._event)&&void 0!==e||(this._event=(e,t,n)=>{var r,i,s,o,a;if(this._leakageMon&&this._size>3*this._leakageMon.threshold)return d.None;if(this._disposed)return d.None;t&&(e=e.bind(t));const l=new _(e);let u;this._leakageMon&&this._size>=Math.ceil(.2*this._leakageMon.threshold)&&(l.stack=C.create(),u=this._leakageMon.check(l.stack,this._size+1)),this._listeners?this._listeners instanceof _?(null!==(a=this._deliveryQueue)&&void 0!==a||(this._deliveryQueue=new S),this._listeners=[this._listeners,l]):this._listeners.push(l):(null===(i=null===(r=this._options)||void 0===r?void 0:r.onWillAddFirstListener)||void 0===i||i.call(r,this),this._listeners=l,null===(o=null===(s=this._options)||void 0===s?void 0:s.onDidAddFirstListener)||void 0===o||o.call(s,this)),this._size++;const f=c((()=>{null==u||u(),this._removeListener(l)}));return n instanceof h?n.add(f):Array.isArray(n)&&n.push(f),f}),this._event}_removeListener(e){var t,n,r,i;if(null===(n=null===(t=this._options)||void 0===t?void 0:t.onWillRemoveListener)||void 0===n||n.call(t,this),!this._listeners)return;if(1===this._size)return this._listeners=void 0,null===(i=null===(r=this._options)||void 0===r?void 0:r.onDidRemoveLastListener)||void 0===i||i.call(r,this),void(this._size=0);const s=this._listeners,o=s.indexOf(e);if(-1===o)throw new Error("Attempted to dispose unknown listener");this._size--,s[o]=void 0;const a=this._deliveryQueue.current===this;if(2*this._size<=s.length){let e=0;for(let t=0;t<s.length;t++)s[t]?s[e++]=s[t]:a&&(this._deliveryQueue.end--,e<this._deliveryQueue.i&&this._deliveryQueue.i--);s.length=e}}_deliver(e,n){var r;if(!e)return;const i=(null===(r=this._options)||void 0===r?void 0:r.onListenerError)||t;if(i)try{e.value(n)}catch(s){i(s)}else e.value(n)}_deliverQueue(e){const t=e.current._listeners;for(;e.i<e.end;)this._deliver(t[e.i++],e.value);e.reset()}fire(e){var t,n,r,i;if((null===(t=this._deliveryQueue)||void 0===t?void 0:t.current)&&(this._deliverQueue(this._deliveryQueue),null===(n=this._perfMon)||void 0===n||n.stop()),null===(r=this._perfMon)||void 0===r||r.start(this._size),this._listeners)if(this._listeners instanceof _)this._deliver(this._listeners,e);else{const t=this._deliveryQueue;t.enqueue(this,e,this._listeners.length),this._deliverQueue(t)}else;null===(i=this._perfMon)||void 0===i||i.stop()}hasListeners(){return this._size>0}}class S{constructor(){this.i=-1,this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}}function L(e){const t=[];for(const n of function(e){let t=[];for(;Object.prototype!==e;)t=t.concat(Object.getOwnPropertyNames(e)),e=Object.getPrototypeOf(e);return t}(e))"function"==typeof e[n]&&t.push(n);return t}let E="undefined"!=typeof document&&document.location&&document.location.hash.indexOf("pseudo=true")>=0;function x(e,t,...n){return function(e,t){let n;return n=0===t.length?e:e.replace(/\{(\d+)\}/g,((e,n)=>{const r=n[0],i=t[r];let s=e;return"string"==typeof i?s=i:"number"!=typeof i&&"boolean"!=typeof i&&null!=i||(s=String(i)),s})),E&&(n="［"+n.replace(/[aouei]/g,"$&$&")+"］"),n}(t,n)}var N;const A="en";let k,R,T,M=!1,O=!1,I=!1,P=A,F=A;const V=globalThis;let D;void 0!==V.vscode&&void 0!==V.vscode.process?D=V.vscode.process:"undefined"!=typeof process&&(D=process);const q="string"==typeof(null===(N=null==D?void 0:D.versions)||void 0===N?void 0:N.electron)&&"renderer"===(null==D?void 0:D.type);if("object"==typeof D){M="win32"===D.platform,O="darwin"===D.platform,I="linux"===D.platform,I&&D.env.SNAP&&D.env.SNAP_REVISION,D.env.CI||D.env.BUILD_ARTIFACTSTAGINGDIRECTORY,k=A,P=A;const e=D.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e),n=t.availableLanguages["*"];k=t.locale,F=t.osLocale,P=n||A,R=t._translationsConfigFile}catch(Bl){}}else"object"!=typeof navigator||q||(T=navigator.userAgent,M=T.indexOf("Windows")>=0,O=T.indexOf("Macintosh")>=0,(T.indexOf("Macintosh")>=0||T.indexOf("iPad")>=0||T.indexOf("iPhone")>=0)&&navigator.maxTouchPoints&&navigator.maxTouchPoints,I=T.indexOf("Linux")>=0,null==T||T.indexOf("Mobi"),x(0,"_"),k=A,P=k,F=navigator.language);const K=M,j=O,B=T,U="function"==typeof V.postMessage&&!V.importScripts;(()=>{if(U){const e=[];V.addEventListener("message",(t=>{if(t.data&&t.data.vscodeScheduleAsyncWork)for(let n=0,r=e.length;n<r;n++){const r=e[n];if(r.id===t.data.vscodeScheduleAsyncWork)return e.splice(n,1),void r.callback()}}));let t=0;return n=>{const r=++t;e.push({id:r,callback:n}),V.postMessage({vscodeScheduleAsyncWork:r},"*")}}})();const $=!!(B&&B.indexOf("Chrome")>=0);B&&B.indexOf("Firefox"),!$&&B&&B.indexOf("Safari"),B&&B.indexOf("Edg/"),B&&B.indexOf("Android");class W{constructor(e){this.executor=e,this._didRun=!1}get value(){if(!this._didRun)try{this._value=this.executor()}catch(e){this._error=e}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}}var H;function z(e){return e>=65&&e<=90}function G(e){return 55296<=e&&e<=56319}function J(e,t,n){const r=e.charCodeAt(n);if(G(r)&&n+1<t){const t=e.charCodeAt(n+1);if(function(e){return 56320<=e&&e<=57343}(t))return t-56320+(r-55296<<10)+65536}return r}const X=/^[\t\n\r\x20-\x7E]*$/;class Q{static getInstance(e){return H.cache.get(Array.from(e))}static getLocales(){return H._locales.value}constructor(e){this.confusableDictionary=e}isAmbiguous(e){return this.confusableDictionary.has(e)}getPrimaryConfusable(e){return this.confusableDictionary.get(e)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}}H=Q,Q.ambiguousCharacterData=new W((()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}'))),Q.cache=new class{constructor(e){this.fn=e,this.lastCache=void 0,this.lastArgKey=void 0}get(e){const t=JSON.stringify(e);return this.lastArgKey!==t&&(this.lastArgKey=t,this.lastCache=this.fn(e)),this.lastCache}}((e=>{function t(e){const t=new Map;for(let n=0;n<e.length;n+=2)t.set(e[n],e[n+1]);return t}function n(e,t){if(!e)return t;const n=new Map;for(const[r,i]of e)t.has(r)&&n.set(r,i);return n}const r=H.ambiguousCharacterData.value;let i,s=e.filter((e=>!e.startsWith("_")&&e in r));0===s.length&&(s=["_default"]);for(const a of s){i=n(i,t(r[a]))}const o=function(e,t){const n=new Map(e);for(const[r,i]of t)n.set(r,i);return n}(t(r._common),i);return new H(o)})),Q._locales=new W((()=>Object.keys(H.ambiguousCharacterData.value).filter((e=>!e.startsWith("_")))));class Z{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(Z.getRawData())),this._data}static isInvisibleCharacter(e){return Z.getData().has(e)}static get codePoints(){return Z.getData()}}Z._data=void 0;class Y{constructor(e,t,n,r){this.vsWorker=e,this.req=t,this.method=n,this.args=r,this.type=0}}class ee{constructor(e,t,n,r){this.vsWorker=e,this.seq=t,this.res=n,this.err=r,this.type=1}}class te{constructor(e,t,n,r){this.vsWorker=e,this.req=t,this.eventName=n,this.arg=r,this.type=2}}class ne{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}}class re{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}}class ie{constructor(e){this._workerId=-1,this._handler=e,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(e){this._workerId=e}sendMessage(e,t){const n=String(++this._lastSentReq);return new Promise(((r,i)=>{this._pendingReplies[n]={resolve:r,reject:i},this._send(new Y(this._workerId,n,e,t))}))}listen(e,t){let n=null;const r=new w({onWillAddFirstListener:()=>{n=String(++this._lastSentReq),this._pendingEmitters.set(n,r),this._send(new te(this._workerId,n,e,t))},onDidRemoveLastListener:()=>{this._pendingEmitters.delete(n),this._send(new re(this._workerId,n)),n=null}});return r.event}handleMessage(e){e&&e.vsWorker&&(-1!==this._workerId&&e.vsWorker!==this._workerId||this._handleMessage(e))}_handleMessage(e){switch(e.type){case 1:return this._handleReplyMessage(e);case 0:return this._handleRequestMessage(e);case 2:return this._handleSubscribeEventMessage(e);case 3:return this._handleEventMessage(e);case 4:return this._handleUnsubscribeEventMessage(e)}}_handleReplyMessage(e){if(!this._pendingReplies[e.seq])return;const t=this._pendingReplies[e.seq];if(delete this._pendingReplies[e.seq],e.err){let n=e.err;return e.err.$isError&&(n=new Error,n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),void t.reject(n)}t.resolve(e.res)}_handleRequestMessage(e){const t=e.req;this._handler.handleMessage(e.method,e.args).then((e=>{this._send(new ee(this._workerId,t,e,void 0))}),(e=>{e.detail instanceof Error&&(e.detail=n(e.detail)),this._send(new ee(this._workerId,t,void 0,n(e)))}))}_handleSubscribeEventMessage(e){const t=e.req,n=this._handler.handleEvent(e.eventName,e.arg)((e=>{this._send(new ne(this._workerId,t,e))}));this._pendingEvents.set(t,n)}_handleEventMessage(e){this._pendingEmitters.has(e.req)&&this._pendingEmitters.get(e.req).fire(e.event)}_handleUnsubscribeEventMessage(e){this._pendingEvents.has(e.req)&&(this._pendingEvents.get(e.req).dispose(),this._pendingEvents.delete(e.req))}_send(e){const t=[];if(0===e.type)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else 1===e.type&&e.res instanceof ArrayBuffer&&t.push(e.res);this._handler.sendMessage(e,t)}}function se(e){return"o"===e[0]&&"n"===e[1]&&z(e.charCodeAt(2))}function oe(e){return/^onDynamic/.test(e)&&z(e.charCodeAt(9))}class ae{constructor(e,t){this._requestHandlerFactory=t,this._requestHandler=null,this._protocol=new ie({sendMessage:(t,n)=>{e(t,n)},handleMessage:(e,t)=>this._handleMessage(e,t),handleEvent:(e,t)=>this._handleEvent(e,t)})}onmessage(e){this._protocol.handleMessage(e)}_handleMessage(e,t){if("$initialize"===e)return this.initialize(t[0],t[1],t[2],t[3]);if(!this._requestHandler||"function"!=typeof this._requestHandler[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._requestHandler[e].apply(this._requestHandler,t))}catch(Bl){return Promise.reject(Bl)}}_handleEvent(e,t){if(!this._requestHandler)throw new Error("Missing requestHandler");if(oe(e)){const n=this._requestHandler[e].call(this._requestHandler,t);if("function"!=typeof n)throw new Error(`Missing dynamic event ${e} on request handler.`);return n}if(se(e)){const t=this._requestHandler[e];if("function"!=typeof t)throw new Error(`Missing event ${e} on request handler.`);return t}throw new Error(`Malformed event name ${e}`)}initialize(e,t,n,r){this._protocol.setWorkerId(e);const i=function(e,t,n){const r=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)},i=e=>function(t){return n(e,t)},s={};for(const o of e)oe(o)?s[o]=i(o):se(o)?s[o]=n(o,void 0):s[o]=r(o);return s}(r,((e,t)=>this._protocol.sendMessage(e,t)),((e,t)=>this._protocol.listen(e,t)));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(i),Promise.resolve(L(this._requestHandler))):(t&&(void 0!==t.baseUrl&&delete t.baseUrl,void 0!==t.paths&&void 0!==t.paths.vs&&delete t.paths.vs,void 0!==t.trustedTypesPolicy&&delete t.trustedTypesPolicy,t.catchError=!0,globalThis.require.config(t)),new Promise(((e,t)=>{(0,globalThis.require)([n],(n=>{this._requestHandler=n.create(i),this._requestHandler?e(L(this._requestHandler)):t(new Error("No RequestHandler!"))}),t)})))}}class le{constructor(e,t,n,r){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=r}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}function ue(e,t){return(t<<5)-t+e|0}function ce(e,t){t=ue(149417,t);for(let n=0,r=e.length;n<r;n++)t=ue(e.charCodeAt(n),t);return t}class he{constructor(e){this.source=e}getElements(){const e=this.source,t=new Int32Array(e.length);for(let n=0,r=e.length;n<r;n++)t[n]=e.charCodeAt(n);return t}}function de(e,t,n){return new pe(new he(e),new he(t)).ComputeDiff(n).changes}class fe{static Assert(e,t){if(!e)throw new Error(t)}}class me{static Copy(e,t,n,r,i){for(let s=0;s<i;s++)n[r+s]=e[t+s]}static Copy2(e,t,n,r,i){for(let s=0;s<i;s++)n[r+s]=e[t+s]}}class ge{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new le(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_originalCount++}AddModifiedElement(e,t){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,t),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class pe{constructor(e,t,n=null){this.ContinueProcessingPredicate=n,this._originalSequence=e,this._modifiedSequence=t;const[r,i,s]=pe._getElements(e),[o,a,l]=pe._getElements(t);this._hasStrings=s&&l,this._originalStringElements=r,this._originalElementsOrHash=i,this._modifiedStringElements=o,this._modifiedElementsOrHash=a,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(e){return e.length>0&&"string"==typeof e[0]}static _getElements(e){const t=e.getElements();if(pe._isStringArray(t)){const e=new Int32Array(t.length);for(let n=0,r=t.length;n<r;n++)e[n]=ce(t[n],0);return[t,e,!0]}return t instanceof Int32Array?[[],t,!1]:[[],new Int32Array(t),!1]}ElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._modifiedStringElements[t])}ElementsAreStrictEqual(e,t){if(!this.ElementsAreEqual(e,t))return!1;return pe._getStrictElement(this._originalSequence,e)===pe._getStrictElement(this._modifiedSequence,t)}static _getStrictElement(e,t){return"function"==typeof e.getStrictElement?e.getStrictElement(t):null}OriginalElementsAreEqual(e,t){return this._originalElementsOrHash[e]===this._originalElementsOrHash[t]&&(!this._hasStrings||this._originalStringElements[e]===this._originalStringElements[t])}ModifiedElementsAreEqual(e,t){return this._modifiedElementsOrHash[e]===this._modifiedElementsOrHash[t]&&(!this._hasStrings||this._modifiedStringElements[e]===this._modifiedStringElements[t])}ComputeDiff(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}_ComputeDiff(e,t,n,r,i){const s=[!1];let o=this.ComputeDiffRecursive(e,t,n,r,s);return i&&(o=this.PrettifyChanges(o)),{quitEarly:s[0],changes:o}}ComputeDiffRecursive(e,t,n,r,i){for(i[0]=!1;e<=t&&n<=r&&this.ElementsAreEqual(e,n);)e++,n++;for(;t>=e&&r>=n&&this.ElementsAreEqual(t,r);)t--,r--;if(e>t||n>r){let i;return n<=r?(fe.Assert(e===t+1,"originalStart should only be one more than originalEnd"),i=[new le(e,0,n,r-n+1)]):e<=t?(fe.Assert(n===r+1,"modifiedStart should only be one more than modifiedEnd"),i=[new le(e,t-e+1,n,0)]):(fe.Assert(e===t+1,"originalStart should only be one more than originalEnd"),fe.Assert(n===r+1,"modifiedStart should only be one more than modifiedEnd"),i=[]),i}const s=[0],o=[0],a=this.ComputeRecursionPoint(e,t,n,r,s,o,i),l=s[0],u=o[0];if(null!==a)return a;if(!i[0]){const s=this.ComputeDiffRecursive(e,l,n,u,i);let o=[];return o=i[0]?[new le(l+1,t-(l+1)+1,u+1,r-(u+1)+1)]:this.ComputeDiffRecursive(l+1,t,u+1,r,i),this.ConcatenateChanges(s,o)}return[new le(e,t-e+1,n,r-n+1)]}WALKTRACE(e,t,n,r,i,s,o,a,l,u,c,h,d,f,m,g,p,v){let b=null,y=null,C=new ge,_=t,w=n,S=d[0]-g[0]-r,L=-1073741824,E=this.m_forwardHistory.length-1;do{const t=S+e;t===_||t<w&&l[t-1]<l[t+1]?(f=(c=l[t+1])-S-r,c<L&&C.MarkNextChange(),L=c,C.AddModifiedElement(c+1,f),S=t+1-e):(f=(c=l[t-1]+1)-S-r,c<L&&C.MarkNextChange(),L=c-1,C.AddOriginalElement(c,f+1),S=t-1-e),E>=0&&(e=(l=this.m_forwardHistory[E])[0],_=1,w=l.length-1)}while(--E>=-1);if(b=C.getReverseChanges(),v[0]){let e=d[0]+1,t=g[0]+1;if(null!==b&&b.length>0){const n=b[b.length-1];e=Math.max(e,n.getOriginalEnd()),t=Math.max(t,n.getModifiedEnd())}y=[new le(e,h-e+1,t,m-t+1)]}else{C=new ge,_=s,w=o,S=d[0]-g[0]-a,L=1073741824,E=p?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const e=S+i;e===_||e<w&&u[e-1]>=u[e+1]?(f=(c=u[e+1]-1)-S-a,c>L&&C.MarkNextChange(),L=c+1,C.AddOriginalElement(c+1,f+1),S=e+1-i):(f=(c=u[e-1])-S-a,c>L&&C.MarkNextChange(),L=c,C.AddModifiedElement(c+1,f+1),S=e-1-i),E>=0&&(i=(u=this.m_reverseHistory[E])[0],_=1,w=u.length-1)}while(--E>=-1);y=C.getChanges()}return this.ConcatenateChanges(b,y)}ComputeRecursionPoint(e,t,n,r,i,s,o){let a=0,l=0,u=0,c=0,h=0,d=0;e--,n--,i[0]=0,s[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const f=t-e+(r-n),m=f+1,g=new Int32Array(m),p=new Int32Array(m),v=r-n,b=t-e,y=e-n,C=t-r,_=(b-v)%2==0;g[v]=e,p[b]=t,o[0]=!1;for(let w=1;w<=f/2+1;w++){let f=0,S=0;u=this.ClipDiagonalBound(v-w,w,v,m),c=this.ClipDiagonalBound(v+w,w,v,m);for(let e=u;e<=c;e+=2){a=e===u||e<c&&g[e-1]<g[e+1]?g[e+1]:g[e-1]+1,l=a-(e-v)-y;const n=a;for(;a<t&&l<r&&this.ElementsAreEqual(a+1,l+1);)a++,l++;if(g[e]=a,a+l>f+S&&(f=a,S=l),!_&&Math.abs(e-b)<=w-1&&a>=p[e])return i[0]=a,s[0]=l,n<=p[e]&&w<=1448?this.WALKTRACE(v,u,c,y,b,h,d,C,g,p,a,t,i,l,r,s,_,o):null}const L=(f-e+(S-n)-w)/2;if(null!==this.ContinueProcessingPredicate&&!this.ContinueProcessingPredicate(f,L))return o[0]=!0,i[0]=f,s[0]=S,L>0&&w<=1448?this.WALKTRACE(v,u,c,y,b,h,d,C,g,p,a,t,i,l,r,s,_,o):(e++,n++,[new le(e,t-e+1,n,r-n+1)]);h=this.ClipDiagonalBound(b-w,w,b,m),d=this.ClipDiagonalBound(b+w,w,b,m);for(let m=h;m<=d;m+=2){a=m===h||m<d&&p[m-1]>=p[m+1]?p[m+1]-1:p[m-1],l=a-(m-b)-C;const f=a;for(;a>e&&l>n&&this.ElementsAreEqual(a,l);)a--,l--;if(p[m]=a,_&&Math.abs(m-v)<=w&&a<=g[m])return i[0]=a,s[0]=l,f>=g[m]&&w<=1448?this.WALKTRACE(v,u,c,y,b,h,d,C,g,p,a,t,i,l,r,s,_,o):null}if(w<=1447){let e=new Int32Array(c-u+2);e[0]=v-u+1,me.Copy2(g,u,e,1,c-u+1),this.m_forwardHistory.push(e),e=new Int32Array(d-h+2),e[0]=b-h+1,me.Copy2(p,h,e,1,d-h+1),this.m_reverseHistory.push(e)}}return this.WALKTRACE(v,u,c,y,b,h,d,C,g,p,a,t,i,l,r,s,_,o)}PrettifyChanges(e){for(let t=0;t<e.length;t++){const n=e[t],r=t<e.length-1?e[t+1].originalStart:this._originalElementsOrHash.length,i=t<e.length-1?e[t+1].modifiedStart:this._modifiedElementsOrHash.length,s=n.originalLength>0,o=n.modifiedLength>0;for(;n.originalStart+n.originalLength<r&&n.modifiedStart+n.modifiedLength<i&&(!s||this.OriginalElementsAreEqual(n.originalStart,n.originalStart+n.originalLength))&&(!o||this.ModifiedElementsAreEqual(n.modifiedStart,n.modifiedStart+n.modifiedLength));){const e=this.ElementsAreStrictEqual(n.originalStart,n.modifiedStart);if(this.ElementsAreStrictEqual(n.originalStart+n.originalLength,n.modifiedStart+n.modifiedLength)&&!e)break;n.originalStart++,n.modifiedStart++}const a=[null];t<e.length-1&&this.ChangesOverlap(e[t],e[t+1],a)&&(e[t]=a[0],e.splice(t+1,1),t--)}for(let t=e.length-1;t>=0;t--){const n=e[t];let r=0,i=0;if(t>0){const n=e[t-1];r=n.originalStart+n.originalLength,i=n.modifiedStart+n.modifiedLength}const s=n.originalLength>0,o=n.modifiedLength>0;let a=0,l=this._boundaryScore(n.originalStart,n.originalLength,n.modifiedStart,n.modifiedLength);for(let e=1;;e++){const t=n.originalStart-e,u=n.modifiedStart-e;if(t<r||u<i)break;if(s&&!this.OriginalElementsAreEqual(t,t+n.originalLength))break;if(o&&!this.ModifiedElementsAreEqual(u,u+n.modifiedLength))break;const c=(t===r&&u===i?5:0)+this._boundaryScore(t,n.originalLength,u,n.modifiedLength);c>l&&(l=c,a=e)}n.originalStart-=a,n.modifiedStart-=a;const u=[null];t>0&&this.ChangesOverlap(e[t-1],e[t],u)&&(e[t-1]=u[0],e.splice(t,1),t++)}if(this._hasStrings)for(let t=1,n=e.length;t<n;t++){const n=e[t-1],r=e[t],i=r.originalStart-n.originalStart-n.originalLength,s=n.originalStart,o=r.originalStart+r.originalLength,a=o-s,l=n.modifiedStart,u=r.modifiedStart+r.modifiedLength,c=u-l;if(i<5&&a<20&&c<20){const e=this._findBetterContiguousSequence(s,a,l,c,i);if(e){const[t,s]=e;t===n.originalStart+n.originalLength&&s===n.modifiedStart+n.modifiedLength||(n.originalLength=t-n.originalStart,n.modifiedLength=s-n.modifiedStart,r.originalStart=t+i,r.modifiedStart=s+i,r.originalLength=o-r.originalStart,r.modifiedLength=u-r.modifiedStart)}}}return e}_findBetterContiguousSequence(e,t,n,r,i){if(t<i||r<i)return null;const s=e+t-i+1,o=n+r-i+1;let a=0,l=0,u=0;for(let c=e;c<s;c++)for(let e=n;e<o;e++){const t=this._contiguousSequenceScore(c,e,i);t>0&&t>a&&(a=t,l=c,u=e)}return a>0?[l,u]:null}_contiguousSequenceScore(e,t,n){let r=0;for(let i=0;i<n;i++){if(!this.ElementsAreEqual(e+i,t+i))return 0;r+=this._originalStringElements[e+i].length}return r}_OriginalIsBoundary(e){return e<=0||e>=this._originalElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._originalStringElements[e])}_OriginalRegionIsBoundary(e,t){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._OriginalIsBoundary(n-1)||this._OriginalIsBoundary(n))return!0}return!1}_ModifiedIsBoundary(e){return e<=0||e>=this._modifiedElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[e])}_ModifiedRegionIsBoundary(e,t){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(t>0){const n=e+t;if(this._ModifiedIsBoundary(n-1)||this._ModifiedIsBoundary(n))return!0}return!1}_boundaryScore(e,t,n,r){return(this._OriginalRegionIsBoundary(e,t)?1:0)+(this._ModifiedRegionIsBoundary(n,r)?1:0)}ConcatenateChanges(e,t){const n=[];if(0===e.length||0===t.length)return t.length>0?t:e;if(this.ChangesOverlap(e[e.length-1],t[0],n)){const r=new Array(e.length+t.length-1);return me.Copy(e,0,r,0,e.length-1),r[e.length-1]=n[0],me.Copy(t,1,r,e.length,t.length-1),r}{const n=new Array(e.length+t.length);return me.Copy(e,0,n,0,e.length),me.Copy(t,0,n,e.length,t.length),n}}ChangesOverlap(e,t,n){if(fe.Assert(e.originalStart<=t.originalStart,"Left change is not less than or equal to right change"),fe.Assert(e.modifiedStart<=t.modifiedStart,"Left change is not less than or equal to right change"),e.originalStart+e.originalLength>=t.originalStart||e.modifiedStart+e.modifiedLength>=t.modifiedStart){const r=e.originalStart;let i=e.originalLength;const s=e.modifiedStart;let o=e.modifiedLength;return e.originalStart+e.originalLength>=t.originalStart&&(i=t.originalStart+t.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=t.modifiedStart&&(o=t.modifiedStart+t.modifiedLength-e.modifiedStart),n[0]=new le(r,i,s,o),!0}return n[0]=null,!1}ClipDiagonalBound(e,t,n,r){if(e>=0&&e<r)return e;const i=t%2==0;if(e<0){return i===(n%2==0)?0:1}return i===((r-n-1)%2==0)?r-1:r-2}}var ve={};let be;const ye=globalThis.vscode;if(void 0!==ye&&void 0!==ye.process){const e=ye.process;be={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd:()=>e.cwd()}}else be="undefined"!=typeof process?{get platform(){return process.platform},get arch(){return process.arch},get env(){return ve},cwd:()=>ve.VSCODE_CWD||process.cwd()}:{get platform(){return K?"win32":j?"darwin":"linux"},get arch(){},get env(){return{}},cwd:()=>"/"};const Ce=be.cwd,_e=be.env,we=be.platform,Se=46,Le=47,Ee=92,xe=58;class Ne extends Error{constructor(e,t,n){let r;"string"==typeof t&&0===t.indexOf("not ")?(r="must not be",t=t.replace(/^not /,"")):r="must be";const i=-1!==e.indexOf(".")?"property":"argument";let s=`The "${e}" ${i} ${r} of type ${t}`;s+=". Received type "+typeof n,super(s),this.code="ERR_INVALID_ARG_TYPE"}}function Ae(e,t){if("string"!=typeof e)throw new Ne(t,"string",e)}const ke="win32"===we;function Re(e){return e===Le||e===Ee}function Te(e){return e===Le}function Me(e){return e>=65&&e<=90||e>=97&&e<=122}function Oe(e,t,n,r){let i="",s=0,o=-1,a=0,l=0;for(let u=0;u<=e.length;++u){if(u<e.length)l=e.charCodeAt(u);else{if(r(l))break;l=Le}if(r(l)){if(o===u-1||1===a);else if(2===a){if(i.length<2||2!==s||i.charCodeAt(i.length-1)!==Se||i.charCodeAt(i.length-2)!==Se){if(i.length>2){const e=i.lastIndexOf(n);-1===e?(i="",s=0):(i=i.slice(0,e),s=i.length-1-i.lastIndexOf(n)),o=u,a=0;continue}if(0!==i.length){i="",s=0,o=u,a=0;continue}}t&&(i+=i.length>0?`${n}..`:"..",s=2)}else i.length>0?i+=`${n}${e.slice(o+1,u)}`:i=e.slice(o+1,u),s=u-o-1;o=u,a=0}else l===Se&&-1!==a?++a:a=-1}return i}function Ie(e,t){!function(e,t){if(null===e||"object"!=typeof e)throw new Ne(t,"Object",e)}(t,"pathObject");const n=t.dir||t.root,r=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}const Pe={resolve(...e){let t="",n="",r=!1;for(let i=e.length-1;i>=-1;i--){let s;if(i>=0){if(s=e[i],Ae(s,"path"),0===s.length)continue}else 0===t.length?s=Ce():(s=_e[`=${t}`]||Ce(),(void 0===s||s.slice(0,2).toLowerCase()!==t.toLowerCase()&&s.charCodeAt(2)===Ee)&&(s=`${t}\\`));const o=s.length;let a=0,l="",u=!1;const c=s.charCodeAt(0);if(1===o)Re(c)&&(a=1,u=!0);else if(Re(c))if(u=!0,Re(s.charCodeAt(1))){let e=2,t=e;for(;e<o&&!Re(s.charCodeAt(e));)e++;if(e<o&&e!==t){const n=s.slice(t,e);for(t=e;e<o&&Re(s.charCodeAt(e));)e++;if(e<o&&e!==t){for(t=e;e<o&&!Re(s.charCodeAt(e));)e++;e!==o&&e===t||(l=`\\\\${n}\\${s.slice(t,e)}`,a=e)}}}else a=1;else Me(c)&&s.charCodeAt(1)===xe&&(l=s.slice(0,2),a=2,o>2&&Re(s.charCodeAt(2))&&(u=!0,a=3));if(l.length>0)if(t.length>0){if(l.toLowerCase()!==t.toLowerCase())continue}else t=l;if(r){if(t.length>0)break}else if(n=`${s.slice(a)}\\${n}`,r=u,u&&t.length>0)break}return n=Oe(n,!r,"\\",Re),r?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){Ae(e,"path");const t=e.length;if(0===t)return".";let n,r=0,i=!1;const s=e.charCodeAt(0);if(1===t)return Te(s)?"\\":e;if(Re(s))if(i=!0,Re(e.charCodeAt(1))){let i=2,s=i;for(;i<t&&!Re(e.charCodeAt(i));)i++;if(i<t&&i!==s){const o=e.slice(s,i);for(s=i;i<t&&Re(e.charCodeAt(i));)i++;if(i<t&&i!==s){for(s=i;i<t&&!Re(e.charCodeAt(i));)i++;if(i===t)return`\\\\${o}\\${e.slice(s)}\\`;i!==s&&(n=`\\\\${o}\\${e.slice(s,i)}`,r=i)}}}else r=1;else Me(s)&&e.charCodeAt(1)===xe&&(n=e.slice(0,2),r=2,t>2&&Re(e.charCodeAt(2))&&(i=!0,r=3));let o=r<t?Oe(e.slice(r),!i,"\\",Re):"";return 0!==o.length||i||(o="."),o.length>0&&Re(e.charCodeAt(t-1))&&(o+="\\"),void 0===n?i?`\\${o}`:o:i?`${n}\\${o}`:`${n}${o}`},isAbsolute(e){Ae(e,"path");const t=e.length;if(0===t)return!1;const n=e.charCodeAt(0);return Re(n)||t>2&&Me(n)&&e.charCodeAt(1)===xe&&Re(e.charCodeAt(2))},join(...e){if(0===e.length)return".";let t,n;for(let s=0;s<e.length;++s){const r=e[s];Ae(r,"path"),r.length>0&&(void 0===t?t=n=r:t+=`\\${r}`)}if(void 0===t)return".";let r=!0,i=0;if("string"==typeof n&&Re(n.charCodeAt(0))){++i;const e=n.length;e>1&&Re(n.charCodeAt(1))&&(++i,e>2&&(Re(n.charCodeAt(2))?++i:r=!1))}if(r){for(;i<t.length&&Re(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return Pe.normalize(t)},relative(e,t){if(Ae(e,"from"),Ae(t,"to"),e===t)return"";const n=Pe.resolve(e),r=Pe.resolve(t);if(n===r)return"";if((e=n.toLowerCase())===(t=r.toLowerCase()))return"";let i=0;for(;i<e.length&&e.charCodeAt(i)===Ee;)i++;let s=e.length;for(;s-1>i&&e.charCodeAt(s-1)===Ee;)s--;const o=s-i;let a=0;for(;a<t.length&&t.charCodeAt(a)===Ee;)a++;let l=t.length;for(;l-1>a&&t.charCodeAt(l-1)===Ee;)l--;const u=l-a,c=o<u?o:u;let h=-1,d=0;for(;d<c;d++){const n=e.charCodeAt(i+d);if(n!==t.charCodeAt(a+d))break;n===Ee&&(h=d)}if(d!==c){if(-1===h)return r}else{if(u>c){if(t.charCodeAt(a+d)===Ee)return r.slice(a+d+1);if(2===d)return r.slice(a+d)}o>c&&(e.charCodeAt(i+d)===Ee?h=d:2===d&&(h=3)),-1===h&&(h=0)}let f="";for(d=i+h+1;d<=s;++d)d!==s&&e.charCodeAt(d)!==Ee||(f+=0===f.length?"..":"\\..");return a+=h,f.length>0?`${f}${r.slice(a,l)}`:(r.charCodeAt(a)===Ee&&++a,r.slice(a,l))},toNamespacedPath(e){if("string"!=typeof e||0===e.length)return e;const t=Pe.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===Ee){if(t.charCodeAt(1)===Ee){const e=t.charCodeAt(2);if(63!==e&&e!==Se)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(Me(t.charCodeAt(0))&&t.charCodeAt(1)===xe&&t.charCodeAt(2)===Ee)return`\\\\?\\${t}`;return e},dirname(e){Ae(e,"path");const t=e.length;if(0===t)return".";let n=-1,r=0;const i=e.charCodeAt(0);if(1===t)return Re(i)?e:".";if(Re(i)){if(n=r=1,Re(e.charCodeAt(1))){let i=2,s=i;for(;i<t&&!Re(e.charCodeAt(i));)i++;if(i<t&&i!==s){for(s=i;i<t&&Re(e.charCodeAt(i));)i++;if(i<t&&i!==s){for(s=i;i<t&&!Re(e.charCodeAt(i));)i++;if(i===t)return e;i!==s&&(n=r=i+1)}}}}else Me(i)&&e.charCodeAt(1)===xe&&(n=t>2&&Re(e.charCodeAt(2))?3:2,r=n);let s=-1,o=!0;for(let a=t-1;a>=r;--a)if(Re(e.charCodeAt(a))){if(!o){s=a;break}}else o=!1;if(-1===s){if(-1===n)return".";s=n}return e.slice(0,s)},basename(e,t){void 0!==t&&Ae(t,"ext"),Ae(e,"path");let n,r=0,i=-1,s=!0;if(e.length>=2&&Me(e.charCodeAt(0))&&e.charCodeAt(1)===xe&&(r=2),void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=r;--n){const l=e.charCodeAt(n);if(Re(l)){if(!s){r=n+1;break}}else-1===a&&(s=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?-1==--o&&(i=n):(o=-1,i=a))}return r===i?i=a:-1===i&&(i=e.length),e.slice(r,i)}for(n=e.length-1;n>=r;--n)if(Re(e.charCodeAt(n))){if(!s){r=n+1;break}}else-1===i&&(s=!1,i=n+1);return-1===i?"":e.slice(r,i)},extname(e){Ae(e,"path");let t=0,n=-1,r=0,i=-1,s=!0,o=0;e.length>=2&&e.charCodeAt(1)===xe&&Me(e.charCodeAt(0))&&(t=r=2);for(let a=e.length-1;a>=t;--a){const t=e.charCodeAt(a);if(Re(t)){if(!s){r=a+1;break}}else-1===i&&(s=!1,i=a+1),t===Se?-1===n?n=a:1!==o&&(o=1):-1!==n&&(o=-1)}return-1===n||-1===i||0===o||1===o&&n===i-1&&n===r+1?"":e.slice(n,i)},format:Ie.bind(null,"\\"),parse(e){Ae(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=e.length;let r=0,i=e.charCodeAt(0);if(1===n)return Re(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(Re(i)){if(r=1,Re(e.charCodeAt(1))){let t=2,i=t;for(;t<n&&!Re(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&Re(e.charCodeAt(t));)t++;if(t<n&&t!==i){for(i=t;t<n&&!Re(e.charCodeAt(t));)t++;t===n?r=t:t!==i&&(r=t+1)}}}}else if(Me(i)&&e.charCodeAt(1)===xe){if(n<=2)return t.root=t.dir=e,t;if(r=2,Re(e.charCodeAt(2))){if(3===n)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let s=-1,o=r,a=-1,l=!0,u=e.length-1,c=0;for(;u>=r;--u)if(i=e.charCodeAt(u),Re(i)){if(!l){o=u+1;break}}else-1===a&&(l=!1,a=u+1),i===Se?-1===s?s=u:1!==c&&(c=1):-1!==s&&(c=-1);return-1!==a&&(-1===s||0===c||1===c&&s===a-1&&s===o+1?t.base=t.name=e.slice(o,a):(t.name=e.slice(o,s),t.base=e.slice(o,a),t.ext=e.slice(s,a))),t.dir=o>0&&o!==r?e.slice(0,o-1):t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},Fe=(()=>{if(ke){const e=/\\/g;return()=>{const t=Ce().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>Ce()})(),Ve={resolve(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){const i=r>=0?e[r]:Fe();Ae(i,"path"),0!==i.length&&(t=`${i}/${t}`,n=i.charCodeAt(0)===Le)}return t=Oe(t,!n,"/",Te),n?`/${t}`:t.length>0?t:"."},normalize(e){if(Ae(e,"path"),0===e.length)return".";const t=e.charCodeAt(0)===Le,n=e.charCodeAt(e.length-1)===Le;return 0===(e=Oe(e,!t,"/",Te)).length?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute:e=>(Ae(e,"path"),e.length>0&&e.charCodeAt(0)===Le),join(...e){if(0===e.length)return".";let t;for(let n=0;n<e.length;++n){const r=e[n];Ae(r,"path"),r.length>0&&(void 0===t?t=r:t+=`/${r}`)}return void 0===t?".":Ve.normalize(t)},relative(e,t){if(Ae(e,"from"),Ae(t,"to"),e===t)return"";if((e=Ve.resolve(e))===(t=Ve.resolve(t)))return"";const n=e.length,r=n-1,i=t.length-1,s=r<i?r:i;let o=-1,a=0;for(;a<s;a++){const n=e.charCodeAt(1+a);if(n!==t.charCodeAt(1+a))break;n===Le&&(o=a)}if(a===s)if(i>s){if(t.charCodeAt(1+a)===Le)return t.slice(1+a+1);if(0===a)return t.slice(1+a)}else r>s&&(e.charCodeAt(1+a)===Le?o=a:0===a&&(o=0));let l="";for(a=1+o+1;a<=n;++a)a!==n&&e.charCodeAt(a)!==Le||(l+=0===l.length?"..":"/..");return`${l}${t.slice(1+o)}`},toNamespacedPath:e=>e,dirname(e){if(Ae(e,"path"),0===e.length)return".";const t=e.charCodeAt(0)===Le;let n=-1,r=!0;for(let i=e.length-1;i>=1;--i)if(e.charCodeAt(i)===Le){if(!r){n=i;break}}else r=!1;return-1===n?t?"/":".":t&&1===n?"//":e.slice(0,n)},basename(e,t){void 0!==t&&Ae(t,"ext"),Ae(e,"path");let n,r=0,i=-1,s=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=0;--n){const l=e.charCodeAt(n);if(l===Le){if(!s){r=n+1;break}}else-1===a&&(s=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?-1==--o&&(i=n):(o=-1,i=a))}return r===i?i=a:-1===i&&(i=e.length),e.slice(r,i)}for(n=e.length-1;n>=0;--n)if(e.charCodeAt(n)===Le){if(!s){r=n+1;break}}else-1===i&&(s=!1,i=n+1);return-1===i?"":e.slice(r,i)},extname(e){Ae(e,"path");let t=-1,n=0,r=-1,i=!0,s=0;for(let o=e.length-1;o>=0;--o){const a=e.charCodeAt(o);if(a!==Le)-1===r&&(i=!1,r=o+1),a===Se?-1===t?t=o:1!==s&&(s=1):-1!==t&&(s=-1);else if(!i){n=o+1;break}}return-1===t||-1===r||0===s||1===s&&t===r-1&&t===n+1?"":e.slice(t,r)},format:Ie.bind(null,"/"),parse(e){Ae(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;const n=e.charCodeAt(0)===Le;let r;n?(t.root="/",r=1):r=0;let i=-1,s=0,o=-1,a=!0,l=e.length-1,u=0;for(;l>=r;--l){const t=e.charCodeAt(l);if(t!==Le)-1===o&&(a=!1,o=l+1),t===Se?-1===i?i=l:1!==u&&(u=1):-1!==i&&(u=-1);else if(!a){s=l+1;break}}if(-1!==o){const r=0===s&&n?1:s;-1===i||0===u||1===u&&i===o-1&&i===s+1?t.base=t.name=e.slice(r,o):(t.name=e.slice(r,i),t.base=e.slice(r,o),t.ext=e.slice(i,o))}return s>0?t.dir=e.slice(0,s-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};Ve.win32=Pe.win32=Pe,Ve.posix=Pe.posix=Ve,ke?Pe.normalize:Ve.normalize,ke?Pe.resolve:Ve.resolve,ke?Pe.relative:Ve.relative,ke?Pe.dirname:Ve.dirname,ke?Pe.basename:Ve.basename,ke?Pe.extname:Ve.extname,ke?Pe.sep:Ve.sep;const De=/^\w[\w\d+.-]*$/,qe=/^\//,Ke=/^\/\//;const je="",Be="/",Ue=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;let $e=class e{static isUri(t){return t instanceof e||!!t&&("string"==typeof t.authority&&"string"==typeof t.fragment&&"string"==typeof t.path&&"string"==typeof t.query&&"string"==typeof t.scheme&&"string"==typeof t.fsPath&&"function"==typeof t.with&&"function"==typeof t.toString)}constructor(e,t,n,r,i,s=!1){"object"==typeof e?(this.scheme=e.scheme||je,this.authority=e.authority||je,this.path=e.path||je,this.query=e.query||je,this.fragment=e.fragment||je):(this.scheme=function(e,t){return e||t?e:"file"}(e,s),this.authority=t||je,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==Be&&(t=Be+t):t=Be}return t}(this.scheme,n||je),this.query=r||je,this.fragment=i||je,function(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!De.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!qe.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(Ke.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}(this,s))}get fsPath(){return Xe(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:i,fragment:s}=e;return void 0===t?t=this.scheme:null===t&&(t=je),void 0===n?n=this.authority:null===n&&(n=je),void 0===r?r=this.path:null===r&&(r=je),void 0===i?i=this.query:null===i&&(i=je),void 0===s?s=this.fragment:null===s&&(s=je),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&s===this.fragment?this:new He(t,n,r,i,s)}static parse(e,t=!1){const n=Ue.exec(e);return n?new He(n[2]||je,et(n[4]||je),et(n[5]||je),et(n[7]||je),et(n[9]||je),t):new He(je,je,je,je,je)}static file(e){let t=je;if(K&&(e=e.replace(/\\/g,Be)),e[0]===Be&&e[1]===Be){const n=e.indexOf(Be,2);-1===n?(t=e.substring(2),e=Be):(t=e.substring(2,n),e=e.substring(n)||Be)}return new He("file",t,e,je,je)}static from(e,t){return new He(e.scheme,e.authority,e.path,e.query,e.fragment,t)}static joinPath(t,...n){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let r;return r=K&&"file"===t.scheme?e.file(Pe.join(Xe(t,!0),...n)).path:Ve.join(t.path,...n),t.with({path:r})}toString(e=!1){return Qe(this,e)}toJSON(){return this}static revive(t){var n,r;if(t){if(t instanceof e)return t;{const e=new He(t);return e._formatted=null!==(n=t.external)&&void 0!==n?n:null,e._fsPath=t._sep===We&&null!==(r=t.fsPath)&&void 0!==r?r:null,e}}return t}};const We=K?1:void 0;class He extends $e{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=Xe(this,!1)),this._fsPath}toString(e=!1){return e?Qe(this,!0):(this._formatted||(this._formatted=Qe(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=We),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const ze={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function Ge(e,t,n){let r,i=-1;for(let s=0;s<e.length;s++){const o=e.charCodeAt(s);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o||n&&91===o||n&&93===o||n&&58===o)-1!==i&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),void 0!==r&&(r+=e.charAt(s));else{void 0===r&&(r=e.substr(0,s));const t=ze[o];void 0!==t?(-1!==i&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),r+=t):-1===i&&(i=s)}}return-1!==i&&(r+=encodeURIComponent(e.substring(i))),void 0!==r?r:e}function Je(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=ze[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function Xe(e,t){let n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?`//${e.authority}${e.path}`:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,K&&(n=n.replace(/\//g,"\\")),n}function Qe(e,t){const n=t?Je:Ge;let r="",{scheme:i,authority:s,path:o,query:a,fragment:l}=e;if(i&&(r+=i,r+=":"),(s||"file"===i)&&(r+=Be,r+=Be),s){let e=s.indexOf("@");if(-1!==e){const t=s.substr(0,e);s=s.substr(e+1),e=t.lastIndexOf(":"),-1===e?r+=n(t,!1,!1):(r+=n(t.substr(0,e),!1,!1),r+=":",r+=n(t.substr(e+1),!1,!0)),r+="@"}s=s.toLowerCase(),e=s.lastIndexOf(":"),-1===e?r+=n(s,!1,!0):(r+=n(s.substr(0,e),!1,!0),r+=s.substr(e))}if(o){if(o.length>=3&&47===o.charCodeAt(0)&&58===o.charCodeAt(2)){const e=o.charCodeAt(1);e>=65&&e<=90&&(o=`/${String.fromCharCode(e+32)}:${o.substr(3)}`)}else if(o.length>=2&&58===o.charCodeAt(1)){const e=o.charCodeAt(0);e>=65&&e<=90&&(o=`${String.fromCharCode(e+32)}:${o.substr(2)}`)}r+=n(o,!0,!1)}return a&&(r+="?",r+=n(a,!1,!1)),l&&(r+="#",r+=t?l:Ge(l,!1,!1)),r}function Ze(e){try{return decodeURIComponent(e)}catch(t){return e.length>3?e.substr(0,3)+Ze(e.substr(3)):e}}const Ye=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function et(e){return e.match(Ye)?e.replace(Ye,(e=>Ze(e))):e}let tt=class e{constructor(e,t){this.lineNumber=e,this.column=t}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new e(t,n)}delta(e=0,t=0){return this.with(this.lineNumber+e,this.column+t)}equals(t){return e.equals(this,t)}static equals(e,t){return!e&&!t||!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column}isBefore(t){return e.isBefore(this,t)}static isBefore(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<t.column}isBeforeOrEqual(t){return e.isBeforeOrEqual(this,t)}static isBeforeOrEqual(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<=t.column}static compare(e,t){const n=0|e.lineNumber,r=0|t.lineNumber;if(n===r){return(0|e.column)-(0|t.column)}return n-r}clone(){return new e(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new e(t.lineNumber,t.column)}static isIPosition(e){return e&&"number"==typeof e.lineNumber&&"number"==typeof e.column}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}},nt=class e{constructor(e,t,n,r){e>n||e===n&&t>r?(this.startLineNumber=n,this.startColumn=r,this.endLineNumber=e,this.endColumn=t):(this.startLineNumber=e,this.startColumn=t,this.endLineNumber=n,this.endColumn=r)}isEmpty(){return e.isEmpty(this)}static isEmpty(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn}containsPosition(t){return e.containsPosition(this,t)}static containsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber)&&(!(t.lineNumber===e.startLineNumber&&t.column<e.startColumn)&&!(t.lineNumber===e.endLineNumber&&t.column>e.endColumn))}static strictContainsPosition(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber)&&(!(t.lineNumber===e.startLineNumber&&t.column<=e.startColumn)&&!(t.lineNumber===e.endLineNumber&&t.column>=e.endColumn))}containsRange(t){return e.containsRange(this,t)}static containsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn)))}strictContainsRange(t){return e.strictContainsRange(this,t)}static strictContainsRange(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn)))}plusRange(t){return e.plusRange(this,t)}static plusRange(t,n){let r,i,s,o;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,i=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,i=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,i=t.startColumn),n.endLineNumber>t.endLineNumber?(s=n.endLineNumber,o=n.endColumn):n.endLineNumber===t.endLineNumber?(s=n.endLineNumber,o=Math.max(n.endColumn,t.endColumn)):(s=t.endLineNumber,o=t.endColumn),new e(r,i,s,o)}intersectRanges(t){return e.intersectRanges(this,t)}static intersectRanges(t,n){let r=t.startLineNumber,i=t.startColumn,s=t.endLineNumber,o=t.endColumn;const a=n.startLineNumber,l=n.startColumn,u=n.endLineNumber,c=n.endColumn;return r<a?(r=a,i=l):r===a&&(i=Math.max(i,l)),s>u?(s=u,o=c):s===u&&(o=Math.min(o,c)),r>s||r===s&&i>o?null:new e(r,i,s,o)}equalsRange(t){return e.equalsRange(this,t)}static equalsRange(e,t){return!e&&!t||!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn}getEndPosition(){return e.getEndPosition(this)}static getEndPosition(e){return new tt(e.endLineNumber,e.endColumn)}getStartPosition(){return e.getStartPosition(this)}static getStartPosition(e){return new tt(e.startLineNumber,e.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,n){return new e(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new e(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return e.collapseToStart(this)}static collapseToStart(t){return new e(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return e.collapseToEnd(this)}static collapseToEnd(t){return new e(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new e(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}static fromPositions(t,n=t){return new e(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new e(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(e){return e&&"number"==typeof e.startLineNumber&&"number"==typeof e.startColumn&&"number"==typeof e.endLineNumber&&"number"==typeof e.endColumn}static areIntersectingOrTouching(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<e.startColumn)}static areIntersecting(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<=t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<=e.startColumn)}static compareRangesUsingStarts(e,t){if(e&&t){const n=0|e.startLineNumber,r=0|t.startLineNumber;if(n===r){const n=0|e.startColumn,r=0|t.startColumn;if(n===r){const n=0|e.endLineNumber,r=0|t.endLineNumber;if(n===r){return(0|e.endColumn)-(0|t.endColumn)}return n-r}return n-r}return n-r}return(e?1:0)-(t?1:0)}static compareRangesUsingEnds(e,t){return e.endLineNumber===t.endLineNumber?e.endColumn===t.endColumn?e.startLineNumber===t.startLineNumber?e.startColumn-t.startColumn:e.startLineNumber-t.startLineNumber:e.endColumn-t.endColumn:e.endLineNumber-t.endLineNumber}static spansMultipleLines(e){return e.endLineNumber>e.startLineNumber}toJSON(){return this}};var rt,it;function st(e,t){return(n,r)=>t(e(n),e(r))}(it=rt||(rt={})).isLessThan=function(e){return e<0},it.isLessThanOrEqual=function(e){return e<=0},it.isGreaterThan=function(e){return e>0},it.isNeitherLessOrGreaterThan=function(e){return 0===e},it.greaterThan=1,it.lessThan=-1,it.neitherLessOrGreaterThan=0;const ot=(e,t)=>e-t;function at(e){return e<0?0:e>255?255:0|e}function lt(e){return e<0?0:e>4294967295?4294967295:0|e}class ut{constructor(e){this.values=e,this.prefixSum=new Uint32Array(e.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(e,t){e=lt(e);const n=this.values,r=this.prefixSum,i=t.length;return 0!==i&&(this.values=new Uint32Array(n.length+i),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e),e+i),this.values.set(t,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(e,t){return e=lt(e),t=lt(t),this.values[e]!==t&&(this.values[e]=t,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)}removeValues(e,t){e=lt(e),t=lt(t);const n=this.values,r=this.prefixSum;if(e>=n.length)return!1;const i=n.length-e;return t>=i&&(t=i),0!==t&&(this.values=new Uint32Array(n.length-t),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e+t),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return 0===this.values.length?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(e){return e<0?0:(e=lt(e),this._getPrefixSum(e))}_getPrefixSum(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];let t=this.prefixSumValidIndex[0]+1;0===t&&(this.prefixSum[0]=this.values[0],t++),e>=this.values.length&&(e=this.values.length-1);for(let n=t;n<=e;n++)this.prefixSum[n]=this.prefixSum[n-1]+this.values[n];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.values.length-1,r=0,i=0,s=0;for(;t<=n;)if(r=t+(n-t)/2|0,i=this.prefixSum[r],s=i-this.values[r],e<s)n=r-1;else{if(!(e>=i))break;t=r+1}return new ct(r,e-s)}}class ct{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}}class ht{constructor(e,t,n,r){this._uri=e,this._lines=t,this._eol=n,this._versionId=r,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return null===this._cachedTextValue&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);const t=e.changes;for(const n of t)this._acceptDeleteRange(n.range),this._acceptInsertText(new tt(n.range.startLineNumber,n.range.startColumn),n.text);this._versionId=e.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const e=this._eol.length,t=this._lines.length,n=new Uint32Array(t);for(let r=0;r<t;r++)n[r]=this._lines[r].length+e;this._lineStarts=new ut(n)}}_setLineText(e,t){this._lines[e]=t,this._lineStarts&&this._lineStarts.setValue(e,this._lines[e].length+this._eol.length)}_acceptDeleteRange(e){if(e.startLineNumber!==e.endLineNumber)this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber);else{if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1))}}_acceptInsertText(e,t){if(0===t.length)return;const n=t.split(/\r\n|\r|\n/);if(1===n.length)return void this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]+this._lines[e.lineNumber-1].substring(e.column-1));n[n.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]);const r=new Uint32Array(n.length-1);for(let i=1;i<n.length;i++)this._lines.splice(e.lineNumber+i-1,0,n[i]),r[i-1]=n[i].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,r)}}const dt=function(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of"`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?")e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}();function ft(e){let t=dt;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}const mt=new m;function gt(e,t,n,r,i){if(t=ft(t),i||(i=l.first(mt)),n.length>i.maxLen){let s=e-i.maxLen/2;return s<0?s=0:r+=s,gt(e,t,n=n.substring(s,e+i.maxLen/2),r,i)}const s=Date.now(),o=e-1-r;let a=-1,u=null;for(let l=1;!(Date.now()-s>=i.timeBudget);l++){const e=o-i.windowSize*l;t.lastIndex=Math.max(0,e);const r=pt(t,n,o,a);if(!r&&u)break;if(u=r,e<=0)break;a=e}if(u){const e={word:u[0],startColumn:r+1+u.index,endColumn:r+1+u.index+u[0].length};return t.lastIndex=0,e}return null}function pt(e,t,n,r){let i;for(;i=e.exec(t);){const t=i.index||0;if(t<=n&&e.lastIndex>=n)return i;if(r>0&&t>r)return null}return null}mt.unshift({maxLen:1e3,windowSize:15,timeBudget:150});class vt{constructor(e){const t=at(e);this._defaultValue=t,this._asciiMap=vt._createAsciiMap(t),this._map=new Map}static _createAsciiMap(e){const t=new Uint8Array(256);return t.fill(e),t}set(e,t){const n=at(t);e>=0&&e<256?this._asciiMap[e]=n:this._map.set(e,n)}get(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue}clear(){this._asciiMap.fill(this._defaultValue),this._map.clear()}}class bt{constructor(e,t,n){const r=new Uint8Array(e*t);for(let i=0,s=e*t;i<s;i++)r[i]=n;this._data=r,this.rows=e,this.cols=t}get(e,t){return this._data[e*this.cols+t]}set(e,t,n){this._data[e*this.cols+t]=n}}class yt{constructor(e){let t=0,n=0;for(let i=0,s=e.length;i<s;i++){const[r,s,o]=e[i];s>t&&(t=s),r>n&&(n=r),o>n&&(n=o)}t++,n++;const r=new bt(n,t,0);for(let i=0,s=e.length;i<s;i++){const[t,n,s]=e[i];r.set(t,n,s)}this._states=r,this._maxCharCode=t}nextState(e,t){return t<0||t>=this._maxCharCode?0:this._states.get(e,t)}}let Ct=null;let _t=null;class wt{static _createLink(e,t,n,r,i){let s=i-1;do{const n=t.charCodeAt(s);if(2!==e.get(n))break;s--}while(s>r);if(r>0){const e=t.charCodeAt(r-1),n=t.charCodeAt(s);(40===e&&41===n||91===e&&93===n||123===e&&125===n)&&s--}return{range:{startLineNumber:n,startColumn:r+1,endLineNumber:n,endColumn:s+2},url:t.substring(r,s+1)}}static computeLinks(e,t=function(){return null===Ct&&(Ct=new yt([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),Ct}()){const n=function(){if(null===_t){_t=new vt(0);const e=" \t<>'\"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…";for(let n=0;n<e.length;n++)_t.set(e.charCodeAt(n),1);const t=".,;:";for(let n=0;n<t.length;n++)_t.set(t.charCodeAt(n),2)}return _t}(),r=[];for(let i=1,s=e.getLineCount();i<=s;i++){const s=e.getLineContent(i),o=s.length;let a=0,l=0,u=0,c=1,h=!1,d=!1,f=!1,m=!1;for(;a<o;){let e=!1;const o=s.charCodeAt(a);if(13===c){let t;switch(o){case 40:h=!0,t=0;break;case 41:t=h?0:1;break;case 91:f=!0,d=!0,t=0;break;case 93:f=!1,t=d?0:1;break;case 123:m=!0,t=0;break;case 125:t=m?0:1;break;case 39:case 34:case 96:t=u===o?1:39===u||34===u||96===u?0:1;break;case 42:t=42===u?1:0;break;case 124:t=124===u?1:0;break;case 32:t=f?0:1;break;default:t=n.get(o)}1===t&&(r.push(wt._createLink(n,s,i,l,a)),e=!0)}else if(12===c){let t;91===o?(d=!0,t=0):t=n.get(o),1===t?e=!0:c=13}else c=t.nextState(c,o),0===c&&(e=!0);e&&(c=1,h=!1,d=!1,m=!1,l=a+1,u=o),a++}13===c&&r.push(wt._createLink(n,s,i,l,o))}return r}}class St{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(e,t,n,r,i){if(e&&t){const n=this.doNavigateValueSet(t,i);if(n)return{range:e,value:n}}if(n&&r){const e=this.doNavigateValueSet(r,i);if(e)return{range:n,value:e}}return null}doNavigateValueSet(e,t){const n=this.numberReplace(e,t);return null!==n?n:this.textReplace(e,t)}numberReplace(e,t){const n=Math.pow(10,e.length-(e.lastIndexOf(".")+1));let r=Number(e);const i=parseFloat(e);return isNaN(r)||isNaN(i)||r!==i?null:0!==r||t?(r=Math.floor(r*n),r+=t?n:-n,String(r/n)):null}textReplace(e,t){return this.valueSetsReplace(this._defaultValueSet,e,t)}valueSetsReplace(e,t,n){let r=null;for(let i=0,s=e.length;null===r&&i<s;i++)r=this.valueSetReplace(e[i],t,n);return r}valueSetReplace(e,t,n){let r=e.indexOf(t);return r>=0?(r+=n?1:-1,r<0?r=e.length-1:r%=e.length,e[r]):null}}St.INSTANCE=new St;const Lt=Object.freeze((function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}));var Et,xt;(xt=Et||(Et={})).isCancellationToken=function(e){return e===xt.None||e===xt.Cancelled||e instanceof Nt||!(!e||"object"!=typeof e)&&"boolean"==typeof e.isCancellationRequested&&"function"==typeof e.onCancellationRequested},xt.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:v.None}),xt.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Lt});class Nt{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Lt:(this._emitter||(this._emitter=new w),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class At{constructor(e){this._token=void 0,this._parentListener=void 0,this._parentListener=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new Nt),this._token}cancel(){this._token?this._token instanceof Nt&&this._token.cancel():this._token=Et.Cancelled}dispose(e=!1){var t;e&&this.cancel(),null===(t=this._parentListener)||void 0===t||t.dispose(),this._token?this._token instanceof Nt&&this._token.dispose():this._token=Et.None}}class kt{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}}const Rt=new kt,Tt=new kt,Mt=new kt,Ot=new Array(230),It=Object.create(null),Pt=Object.create(null);var Ft,Vt;!function(){const e="",t=[[1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[1,1,"Hyper",0,e,0,e,e,e],[1,2,"Super",0,e,0,e,e,e],[1,3,"Fn",0,e,0,e,e,e],[1,4,"FnLock",0,e,0,e,e,e],[1,5,"Suspend",0,e,0,e,e,e],[1,6,"Resume",0,e,0,e,e,e],[1,7,"Turbo",0,e,0,e,e,e],[1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[1,9,"WakeUp",0,e,0,e,e,e],[0,10,"KeyA",31,"A",65,"VK_A",e,e],[0,11,"KeyB",32,"B",66,"VK_B",e,e],[0,12,"KeyC",33,"C",67,"VK_C",e,e],[0,13,"KeyD",34,"D",68,"VK_D",e,e],[0,14,"KeyE",35,"E",69,"VK_E",e,e],[0,15,"KeyF",36,"F",70,"VK_F",e,e],[0,16,"KeyG",37,"G",71,"VK_G",e,e],[0,17,"KeyH",38,"H",72,"VK_H",e,e],[0,18,"KeyI",39,"I",73,"VK_I",e,e],[0,19,"KeyJ",40,"J",74,"VK_J",e,e],[0,20,"KeyK",41,"K",75,"VK_K",e,e],[0,21,"KeyL",42,"L",76,"VK_L",e,e],[0,22,"KeyM",43,"M",77,"VK_M",e,e],[0,23,"KeyN",44,"N",78,"VK_N",e,e],[0,24,"KeyO",45,"O",79,"VK_O",e,e],[0,25,"KeyP",46,"P",80,"VK_P",e,e],[0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[0,27,"KeyR",48,"R",82,"VK_R",e,e],[0,28,"KeyS",49,"S",83,"VK_S",e,e],[0,29,"KeyT",50,"T",84,"VK_T",e,e],[0,30,"KeyU",51,"U",85,"VK_U",e,e],[0,31,"KeyV",52,"V",86,"VK_V",e,e],[0,32,"KeyW",53,"W",87,"VK_W",e,e],[0,33,"KeyX",54,"X",88,"VK_X",e,e],[0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[0,36,"Digit1",22,"1",49,"VK_1",e,e],[0,37,"Digit2",23,"2",50,"VK_2",e,e],[0,38,"Digit3",24,"3",51,"VK_3",e,e],[0,39,"Digit4",25,"4",52,"VK_4",e,e],[0,40,"Digit5",26,"5",53,"VK_5",e,e],[0,41,"Digit6",27,"6",54,"VK_6",e,e],[0,42,"Digit7",28,"7",55,"VK_7",e,e],[0,43,"Digit8",29,"8",56,"VK_8",e,e],[0,44,"Digit9",30,"9",57,"VK_9",e,e],[0,45,"Digit0",21,"0",48,"VK_0",e,e],[1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[0,51,"Minus",88,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[0,52,"Equal",86,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[0,53,"BracketLeft",92,"[",219,"VK_OEM_4","[","OEM_4"],[0,54,"BracketRight",94,"]",221,"VK_OEM_6","]","OEM_6"],[0,55,"Backslash",93,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,56,"IntlHash",0,e,0,e,e,e],[0,57,"Semicolon",85,";",186,"VK_OEM_1",";","OEM_1"],[0,58,"Quote",95,"'",222,"VK_OEM_7","'","OEM_7"],[0,59,"Backquote",91,"`",192,"VK_OEM_3","`","OEM_3"],[0,60,"Comma",87,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[0,61,"Period",89,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[0,62,"Slash",90,"/",191,"VK_OEM_2","/","OEM_2"],[1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[1,64,"F1",59,"F1",112,"VK_F1",e,e],[1,65,"F2",60,"F2",113,"VK_F2",e,e],[1,66,"F3",61,"F3",114,"VK_F3",e,e],[1,67,"F4",62,"F4",115,"VK_F4",e,e],[1,68,"F5",63,"F5",116,"VK_F5",e,e],[1,69,"F6",64,"F6",117,"VK_F6",e,e],[1,70,"F7",65,"F7",118,"VK_F7",e,e],[1,71,"F8",66,"F8",119,"VK_F8",e,e],[1,72,"F9",67,"F9",120,"VK_F9",e,e],[1,73,"F10",68,"F10",121,"VK_F10",e,e],[1,74,"F11",69,"F11",122,"VK_F11",e,e],[1,75,"F12",70,"F12",123,"VK_F12",e,e],[1,76,"PrintScreen",0,e,0,e,e,e],[1,77,"ScrollLock",84,"ScrollLock",145,"VK_SCROLL",e,e],[1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[1,80,"Home",14,"Home",36,"VK_HOME",e,e],[1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[1,83,"End",13,"End",35,"VK_END",e,e],[1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[1,89,"NumLock",83,"NumLock",144,"VK_NUMLOCK",e,e],[1,90,"NumpadDivide",113,"NumPad_Divide",111,"VK_DIVIDE",e,e],[1,91,"NumpadMultiply",108,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[1,92,"NumpadSubtract",111,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[1,93,"NumpadAdd",109,"NumPad_Add",107,"VK_ADD",e,e],[1,94,"NumpadEnter",3,e,0,e,e,e],[1,95,"Numpad1",99,"NumPad1",97,"VK_NUMPAD1",e,e],[1,96,"Numpad2",100,"NumPad2",98,"VK_NUMPAD2",e,e],[1,97,"Numpad3",101,"NumPad3",99,"VK_NUMPAD3",e,e],[1,98,"Numpad4",102,"NumPad4",100,"VK_NUMPAD4",e,e],[1,99,"Numpad5",103,"NumPad5",101,"VK_NUMPAD5",e,e],[1,100,"Numpad6",104,"NumPad6",102,"VK_NUMPAD6",e,e],[1,101,"Numpad7",105,"NumPad7",103,"VK_NUMPAD7",e,e],[1,102,"Numpad8",106,"NumPad8",104,"VK_NUMPAD8",e,e],[1,103,"Numpad9",107,"NumPad9",105,"VK_NUMPAD9",e,e],[1,104,"Numpad0",98,"NumPad0",96,"VK_NUMPAD0",e,e],[1,105,"NumpadDecimal",112,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[0,106,"IntlBackslash",97,"OEM_102",226,"VK_OEM_102",e,e],[1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[1,108,"Power",0,e,0,e,e,e],[1,109,"NumpadEqual",0,e,0,e,e,e],[1,110,"F13",71,"F13",124,"VK_F13",e,e],[1,111,"F14",72,"F14",125,"VK_F14",e,e],[1,112,"F15",73,"F15",126,"VK_F15",e,e],[1,113,"F16",74,"F16",127,"VK_F16",e,e],[1,114,"F17",75,"F17",128,"VK_F17",e,e],[1,115,"F18",76,"F18",129,"VK_F18",e,e],[1,116,"F19",77,"F19",130,"VK_F19",e,e],[1,117,"F20",78,"F20",131,"VK_F20",e,e],[1,118,"F21",79,"F21",132,"VK_F21",e,e],[1,119,"F22",80,"F22",133,"VK_F22",e,e],[1,120,"F23",81,"F23",134,"VK_F23",e,e],[1,121,"F24",82,"F24",135,"VK_F24",e,e],[1,122,"Open",0,e,0,e,e,e],[1,123,"Help",0,e,0,e,e,e],[1,124,"Select",0,e,0,e,e,e],[1,125,"Again",0,e,0,e,e,e],[1,126,"Undo",0,e,0,e,e,e],[1,127,"Cut",0,e,0,e,e,e],[1,128,"Copy",0,e,0,e,e,e],[1,129,"Paste",0,e,0,e,e,e],[1,130,"Find",0,e,0,e,e,e],[1,131,"AudioVolumeMute",117,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[1,132,"AudioVolumeUp",118,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[1,133,"AudioVolumeDown",119,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[1,134,"NumpadComma",110,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[0,135,"IntlRo",115,"ABNT_C1",193,"VK_ABNT_C1",e,e],[1,136,"KanaMode",0,e,0,e,e,e],[0,137,"IntlYen",0,e,0,e,e,e],[1,138,"Convert",0,e,0,e,e,e],[1,139,"NonConvert",0,e,0,e,e,e],[1,140,"Lang1",0,e,0,e,e,e],[1,141,"Lang2",0,e,0,e,e,e],[1,142,"Lang3",0,e,0,e,e,e],[1,143,"Lang4",0,e,0,e,e,e],[1,144,"Lang5",0,e,0,e,e,e],[1,145,"Abort",0,e,0,e,e,e],[1,146,"Props",0,e,0,e,e,e],[1,147,"NumpadParenLeft",0,e,0,e,e,e],[1,148,"NumpadParenRight",0,e,0,e,e,e],[1,149,"NumpadBackspace",0,e,0,e,e,e],[1,150,"NumpadMemoryStore",0,e,0,e,e,e],[1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[1,152,"NumpadMemoryClear",0,e,0,e,e,e],[1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[1,155,"NumpadClear",131,"Clear",12,"VK_CLEAR",e,e],[1,156,"NumpadClearEntry",0,e,0,e,e,e],[1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[1,0,e,6,"Alt",18,"VK_MENU",e,e],[1,0,e,57,"Meta",91,"VK_COMMAND",e,e],[1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[1,165,"BrightnessUp",0,e,0,e,e,e],[1,166,"BrightnessDown",0,e,0,e,e,e],[1,167,"MediaPlay",0,e,0,e,e,e],[1,168,"MediaRecord",0,e,0,e,e,e],[1,169,"MediaFastForward",0,e,0,e,e,e],[1,170,"MediaRewind",0,e,0,e,e,e],[1,171,"MediaTrackNext",124,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[1,172,"MediaTrackPrevious",125,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[1,173,"MediaStop",126,"MediaStop",178,"VK_MEDIA_STOP",e,e],[1,174,"Eject",0,e,0,e,e,e],[1,175,"MediaPlayPause",127,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[1,176,"MediaSelect",128,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[1,177,"LaunchMail",129,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[1,178,"LaunchApp2",130,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[1,180,"SelectTask",0,e,0,e,e,e],[1,181,"LaunchScreenSaver",0,e,0,e,e,e],[1,182,"BrowserSearch",120,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[1,183,"BrowserHome",121,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[1,184,"BrowserBack",122,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[1,185,"BrowserForward",123,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[1,189,"ZoomToggle",0,e,0,e,e,e],[1,190,"MailReply",0,e,0,e,e,e],[1,191,"MailForward",0,e,0,e,e,e],[1,192,"MailSend",0,e,0,e,e,e],[1,0,e,114,"KeyInComposition",229,e,e,e],[1,0,e,116,"ABNT_C2",194,"VK_ABNT_C2",e,e],[1,0,e,96,"OEM_8",223,"VK_OEM_8",e,e],[1,0,e,0,e,0,"VK_KANA",e,e],[1,0,e,0,e,0,"VK_HANGUL",e,e],[1,0,e,0,e,0,"VK_JUNJA",e,e],[1,0,e,0,e,0,"VK_FINAL",e,e],[1,0,e,0,e,0,"VK_HANJA",e,e],[1,0,e,0,e,0,"VK_KANJI",e,e],[1,0,e,0,e,0,"VK_CONVERT",e,e],[1,0,e,0,e,0,"VK_NONCONVERT",e,e],[1,0,e,0,e,0,"VK_ACCEPT",e,e],[1,0,e,0,e,0,"VK_MODECHANGE",e,e],[1,0,e,0,e,0,"VK_SELECT",e,e],[1,0,e,0,e,0,"VK_PRINT",e,e],[1,0,e,0,e,0,"VK_EXECUTE",e,e],[1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[1,0,e,0,e,0,"VK_HELP",e,e],[1,0,e,0,e,0,"VK_APPS",e,e],[1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[1,0,e,0,e,0,"VK_PACKET",e,e],[1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[1,0,e,0,e,0,"VK_ATTN",e,e],[1,0,e,0,e,0,"VK_CRSEL",e,e],[1,0,e,0,e,0,"VK_EXSEL",e,e],[1,0,e,0,e,0,"VK_EREOF",e,e],[1,0,e,0,e,0,"VK_PLAY",e,e],[1,0,e,0,e,0,"VK_ZOOM",e,e],[1,0,e,0,e,0,"VK_NONAME",e,e],[1,0,e,0,e,0,"VK_PA1",e,e],[1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]],n=[],r=[];for(const i of t){const[e,t,s,o,a,l,u,c,h]=i;if(r[t]||(r[t]=!0,It[s]=t,Pt[s.toLowerCase()]=t),!n[o]){if(n[o]=!0,!a)throw new Error(`String representation missing for key code ${o} around scan code ${s}`);Rt.define(o,a),Tt.define(o,c||a),Mt.define(o,h||c||a)}l&&(Ot[l]=o)}}(),(Vt=Ft||(Ft={})).toString=function(e){return Rt.keyCodeToStr(e)},Vt.fromString=function(e){return Rt.strToKeyCode(e)},Vt.toUserSettingsUS=function(e){return Tt.keyCodeToStr(e)},Vt.toUserSettingsGeneral=function(e){return Mt.keyCodeToStr(e)},Vt.fromUserSettings=function(e){return Tt.strToKeyCode(e)||Mt.strToKeyCode(e)},Vt.toElectronAccelerator=function(e){if(e>=98&&e<=113)return null;switch(e){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return Rt.keyCodeToStr(e)};class Dt extends nt{constructor(e,t,n,r){super(e,t,n,r),this.selectionStartLineNumber=e,this.selectionStartColumn=t,this.positionLineNumber=n,this.positionColumn=r}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(e){return Dt.selectionsEqual(this,e)}static selectionsEqual(e,t){return e.selectionStartLineNumber===t.selectionStartLineNumber&&e.selectionStartColumn===t.selectionStartColumn&&e.positionLineNumber===t.positionLineNumber&&e.positionColumn===t.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(e,t){return 0===this.getDirection()?new Dt(this.startLineNumber,this.startColumn,e,t):new Dt(e,t,this.startLineNumber,this.startColumn)}getPosition(){return new tt(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new tt(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(e,t){return 0===this.getDirection()?new Dt(e,t,this.endLineNumber,this.endColumn):new Dt(this.endLineNumber,this.endColumn,e,t)}static fromPositions(e,t=e){return new Dt(e.lineNumber,e.column,t.lineNumber,t.column)}static fromRange(e,t){return 0===t?new Dt(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):new Dt(e.endLineNumber,e.endColumn,e.startLineNumber,e.startColumn)}static liftSelection(e){return new Dt(e.selectionStartLineNumber,e.selectionStartColumn,e.positionLineNumber,e.positionColumn)}static selectionsArrEqual(e,t){if(e&&!t||!e&&t)return!1;if(!e&&!t)return!0;if(e.length!==t.length)return!1;for(let n=0,r=e.length;n<r;n++)if(!this.selectionsEqual(e[n],t[n]))return!1;return!0}static isISelection(e){return e&&"number"==typeof e.selectionStartLineNumber&&"number"==typeof e.selectionStartColumn&&"number"==typeof e.positionLineNumber&&"number"==typeof e.positionColumn}static createWithDirection(e,t,n,r,i){return 0===i?new Dt(e,t,n,r):new Dt(n,r,e,t)}}const qt=Object.create(null);function Kt(e,t){if("string"==typeof t){const n=qt[t];if(void 0===n)throw new Error(`${e} references an unknown codicon: ${t}`);t=n}return qt[e]=t,{id:e}}const jt={add:Kt("add",6e4),plus:Kt("plus",6e4),gistNew:Kt("gist-new",6e4),repoCreate:Kt("repo-create",6e4),lightbulb:Kt("lightbulb",60001),lightBulb:Kt("light-bulb",60001),repo:Kt("repo",60002),repoDelete:Kt("repo-delete",60002),gistFork:Kt("gist-fork",60003),repoForked:Kt("repo-forked",60003),gitPullRequest:Kt("git-pull-request",60004),gitPullRequestAbandoned:Kt("git-pull-request-abandoned",60004),recordKeys:Kt("record-keys",60005),keyboard:Kt("keyboard",60005),tag:Kt("tag",60006),tagAdd:Kt("tag-add",60006),tagRemove:Kt("tag-remove",60006),gitPullRequestLabel:Kt("git-pull-request-label",60006),person:Kt("person",60007),personFollow:Kt("person-follow",60007),personOutline:Kt("person-outline",60007),personFilled:Kt("person-filled",60007),gitBranch:Kt("git-branch",60008),gitBranchCreate:Kt("git-branch-create",60008),gitBranchDelete:Kt("git-branch-delete",60008),sourceControl:Kt("source-control",60008),mirror:Kt("mirror",60009),mirrorPublic:Kt("mirror-public",60009),star:Kt("star",60010),starAdd:Kt("star-add",60010),starDelete:Kt("star-delete",60010),starEmpty:Kt("star-empty",60010),comment:Kt("comment",60011),commentAdd:Kt("comment-add",60011),alert:Kt("alert",60012),warning:Kt("warning",60012),search:Kt("search",60013),searchSave:Kt("search-save",60013),logOut:Kt("log-out",60014),signOut:Kt("sign-out",60014),logIn:Kt("log-in",60015),signIn:Kt("sign-in",60015),eye:Kt("eye",60016),eyeUnwatch:Kt("eye-unwatch",60016),eyeWatch:Kt("eye-watch",60016),circleFilled:Kt("circle-filled",60017),primitiveDot:Kt("primitive-dot",60017),closeDirty:Kt("close-dirty",60017),debugBreakpoint:Kt("debug-breakpoint",60017),debugBreakpointDisabled:Kt("debug-breakpoint-disabled",60017),debugBreakpointPending:Kt("debug-breakpoint-pending",60377),debugHint:Kt("debug-hint",60017),primitiveSquare:Kt("primitive-square",60018),edit:Kt("edit",60019),pencil:Kt("pencil",60019),info:Kt("info",60020),issueOpened:Kt("issue-opened",60020),gistPrivate:Kt("gist-private",60021),gitForkPrivate:Kt("git-fork-private",60021),lock:Kt("lock",60021),mirrorPrivate:Kt("mirror-private",60021),close:Kt("close",60022),removeClose:Kt("remove-close",60022),x:Kt("x",60022),repoSync:Kt("repo-sync",60023),sync:Kt("sync",60023),clone:Kt("clone",60024),desktopDownload:Kt("desktop-download",60024),beaker:Kt("beaker",60025),microscope:Kt("microscope",60025),vm:Kt("vm",60026),deviceDesktop:Kt("device-desktop",60026),file:Kt("file",60027),fileText:Kt("file-text",60027),more:Kt("more",60028),ellipsis:Kt("ellipsis",60028),kebabHorizontal:Kt("kebab-horizontal",60028),mailReply:Kt("mail-reply",60029),reply:Kt("reply",60029),organization:Kt("organization",60030),organizationFilled:Kt("organization-filled",60030),organizationOutline:Kt("organization-outline",60030),newFile:Kt("new-file",60031),fileAdd:Kt("file-add",60031),newFolder:Kt("new-folder",60032),fileDirectoryCreate:Kt("file-directory-create",60032),trash:Kt("trash",60033),trashcan:Kt("trashcan",60033),history:Kt("history",60034),clock:Kt("clock",60034),folder:Kt("folder",60035),fileDirectory:Kt("file-directory",60035),symbolFolder:Kt("symbol-folder",60035),logoGithub:Kt("logo-github",60036),markGithub:Kt("mark-github",60036),github:Kt("github",60036),terminal:Kt("terminal",60037),console:Kt("console",60037),repl:Kt("repl",60037),zap:Kt("zap",60038),symbolEvent:Kt("symbol-event",60038),error:Kt("error",60039),stop:Kt("stop",60039),variable:Kt("variable",60040),symbolVariable:Kt("symbol-variable",60040),array:Kt("array",60042),symbolArray:Kt("symbol-array",60042),symbolModule:Kt("symbol-module",60043),symbolPackage:Kt("symbol-package",60043),symbolNamespace:Kt("symbol-namespace",60043),symbolObject:Kt("symbol-object",60043),symbolMethod:Kt("symbol-method",60044),symbolFunction:Kt("symbol-function",60044),symbolConstructor:Kt("symbol-constructor",60044),symbolBoolean:Kt("symbol-boolean",60047),symbolNull:Kt("symbol-null",60047),symbolNumeric:Kt("symbol-numeric",60048),symbolNumber:Kt("symbol-number",60048),symbolStructure:Kt("symbol-structure",60049),symbolStruct:Kt("symbol-struct",60049),symbolParameter:Kt("symbol-parameter",60050),symbolTypeParameter:Kt("symbol-type-parameter",60050),symbolKey:Kt("symbol-key",60051),symbolText:Kt("symbol-text",60051),symbolReference:Kt("symbol-reference",60052),goToFile:Kt("go-to-file",60052),symbolEnum:Kt("symbol-enum",60053),symbolValue:Kt("symbol-value",60053),symbolRuler:Kt("symbol-ruler",60054),symbolUnit:Kt("symbol-unit",60054),activateBreakpoints:Kt("activate-breakpoints",60055),archive:Kt("archive",60056),arrowBoth:Kt("arrow-both",60057),arrowDown:Kt("arrow-down",60058),arrowLeft:Kt("arrow-left",60059),arrowRight:Kt("arrow-right",60060),arrowSmallDown:Kt("arrow-small-down",60061),arrowSmallLeft:Kt("arrow-small-left",60062),arrowSmallRight:Kt("arrow-small-right",60063),arrowSmallUp:Kt("arrow-small-up",60064),arrowUp:Kt("arrow-up",60065),bell:Kt("bell",60066),bold:Kt("bold",60067),book:Kt("book",60068),bookmark:Kt("bookmark",60069),debugBreakpointConditionalUnverified:Kt("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:Kt("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:Kt("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:Kt("debug-breakpoint-data-unverified",60072),debugBreakpointData:Kt("debug-breakpoint-data",60073),debugBreakpointDataDisabled:Kt("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:Kt("debug-breakpoint-log-unverified",60074),debugBreakpointLog:Kt("debug-breakpoint-log",60075),debugBreakpointLogDisabled:Kt("debug-breakpoint-log-disabled",60075),briefcase:Kt("briefcase",60076),broadcast:Kt("broadcast",60077),browser:Kt("browser",60078),bug:Kt("bug",60079),calendar:Kt("calendar",60080),caseSensitive:Kt("case-sensitive",60081),check:Kt("check",60082),checklist:Kt("checklist",60083),chevronDown:Kt("chevron-down",60084),dropDownButton:Kt("drop-down-button",60084),chevronLeft:Kt("chevron-left",60085),chevronRight:Kt("chevron-right",60086),chevronUp:Kt("chevron-up",60087),chromeClose:Kt("chrome-close",60088),chromeMaximize:Kt("chrome-maximize",60089),chromeMinimize:Kt("chrome-minimize",60090),chromeRestore:Kt("chrome-restore",60091),circle:Kt("circle",60092),circleOutline:Kt("circle-outline",60092),debugBreakpointUnverified:Kt("debug-breakpoint-unverified",60092),circleSlash:Kt("circle-slash",60093),circuitBoard:Kt("circuit-board",60094),clearAll:Kt("clear-all",60095),clippy:Kt("clippy",60096),closeAll:Kt("close-all",60097),cloudDownload:Kt("cloud-download",60098),cloudUpload:Kt("cloud-upload",60099),code:Kt("code",60100),collapseAll:Kt("collapse-all",60101),colorMode:Kt("color-mode",60102),commentDiscussion:Kt("comment-discussion",60103),compareChanges:Kt("compare-changes",60157),creditCard:Kt("credit-card",60105),dash:Kt("dash",60108),dashboard:Kt("dashboard",60109),database:Kt("database",60110),debugContinue:Kt("debug-continue",60111),debugDisconnect:Kt("debug-disconnect",60112),debugPause:Kt("debug-pause",60113),debugRestart:Kt("debug-restart",60114),debugStart:Kt("debug-start",60115),debugStepInto:Kt("debug-step-into",60116),debugStepOut:Kt("debug-step-out",60117),debugStepOver:Kt("debug-step-over",60118),debugStop:Kt("debug-stop",60119),debug:Kt("debug",60120),deviceCameraVideo:Kt("device-camera-video",60121),deviceCamera:Kt("device-camera",60122),deviceMobile:Kt("device-mobile",60123),diffAdded:Kt("diff-added",60124),diffIgnored:Kt("diff-ignored",60125),diffModified:Kt("diff-modified",60126),diffRemoved:Kt("diff-removed",60127),diffRenamed:Kt("diff-renamed",60128),diff:Kt("diff",60129),discard:Kt("discard",60130),editorLayout:Kt("editor-layout",60131),emptyWindow:Kt("empty-window",60132),exclude:Kt("exclude",60133),extensions:Kt("extensions",60134),eyeClosed:Kt("eye-closed",60135),fileBinary:Kt("file-binary",60136),fileCode:Kt("file-code",60137),fileMedia:Kt("file-media",60138),filePdf:Kt("file-pdf",60139),fileSubmodule:Kt("file-submodule",60140),fileSymlinkDirectory:Kt("file-symlink-directory",60141),fileSymlinkFile:Kt("file-symlink-file",60142),fileZip:Kt("file-zip",60143),files:Kt("files",60144),filter:Kt("filter",60145),flame:Kt("flame",60146),foldDown:Kt("fold-down",60147),foldUp:Kt("fold-up",60148),fold:Kt("fold",60149),folderActive:Kt("folder-active",60150),folderOpened:Kt("folder-opened",60151),gear:Kt("gear",60152),gift:Kt("gift",60153),gistSecret:Kt("gist-secret",60154),gist:Kt("gist",60155),gitCommit:Kt("git-commit",60156),gitCompare:Kt("git-compare",60157),gitMerge:Kt("git-merge",60158),githubAction:Kt("github-action",60159),githubAlt:Kt("github-alt",60160),globe:Kt("globe",60161),grabber:Kt("grabber",60162),graph:Kt("graph",60163),gripper:Kt("gripper",60164),heart:Kt("heart",60165),home:Kt("home",60166),horizontalRule:Kt("horizontal-rule",60167),hubot:Kt("hubot",60168),inbox:Kt("inbox",60169),issueClosed:Kt("issue-closed",60324),issueReopened:Kt("issue-reopened",60171),issues:Kt("issues",60172),italic:Kt("italic",60173),jersey:Kt("jersey",60174),json:Kt("json",60175),bracket:Kt("bracket",60175),kebabVertical:Kt("kebab-vertical",60176),key:Kt("key",60177),law:Kt("law",60178),lightbulbAutofix:Kt("lightbulb-autofix",60179),linkExternal:Kt("link-external",60180),link:Kt("link",60181),listOrdered:Kt("list-ordered",60182),listUnordered:Kt("list-unordered",60183),liveShare:Kt("live-share",60184),loading:Kt("loading",60185),location:Kt("location",60186),mailRead:Kt("mail-read",60187),mail:Kt("mail",60188),markdown:Kt("markdown",60189),megaphone:Kt("megaphone",60190),mention:Kt("mention",60191),milestone:Kt("milestone",60192),gitPullRequestMilestone:Kt("git-pull-request-milestone",60192),mortarBoard:Kt("mortar-board",60193),move:Kt("move",60194),multipleWindows:Kt("multiple-windows",60195),mute:Kt("mute",60196),noNewline:Kt("no-newline",60197),note:Kt("note",60198),octoface:Kt("octoface",60199),openPreview:Kt("open-preview",60200),package:Kt("package",60201),paintcan:Kt("paintcan",60202),pin:Kt("pin",60203),play:Kt("play",60204),run:Kt("run",60204),plug:Kt("plug",60205),preserveCase:Kt("preserve-case",60206),preview:Kt("preview",60207),project:Kt("project",60208),pulse:Kt("pulse",60209),question:Kt("question",60210),quote:Kt("quote",60211),radioTower:Kt("radio-tower",60212),reactions:Kt("reactions",60213),references:Kt("references",60214),refresh:Kt("refresh",60215),regex:Kt("regex",60216),remoteExplorer:Kt("remote-explorer",60217),remote:Kt("remote",60218),remove:Kt("remove",60219),replaceAll:Kt("replace-all",60220),replace:Kt("replace",60221),repoClone:Kt("repo-clone",60222),repoForcePush:Kt("repo-force-push",60223),repoPull:Kt("repo-pull",60224),repoPush:Kt("repo-push",60225),report:Kt("report",60226),requestChanges:Kt("request-changes",60227),rocket:Kt("rocket",60228),rootFolderOpened:Kt("root-folder-opened",60229),rootFolder:Kt("root-folder",60230),rss:Kt("rss",60231),ruby:Kt("ruby",60232),saveAll:Kt("save-all",60233),saveAs:Kt("save-as",60234),save:Kt("save",60235),screenFull:Kt("screen-full",60236),screenNormal:Kt("screen-normal",60237),searchStop:Kt("search-stop",60238),server:Kt("server",60240),settingsGear:Kt("settings-gear",60241),settings:Kt("settings",60242),shield:Kt("shield",60243),smiley:Kt("smiley",60244),sortPrecedence:Kt("sort-precedence",60245),splitHorizontal:Kt("split-horizontal",60246),splitVertical:Kt("split-vertical",60247),squirrel:Kt("squirrel",60248),starFull:Kt("star-full",60249),starHalf:Kt("star-half",60250),symbolClass:Kt("symbol-class",60251),symbolColor:Kt("symbol-color",60252),symbolCustomColor:Kt("symbol-customcolor",60252),symbolConstant:Kt("symbol-constant",60253),symbolEnumMember:Kt("symbol-enum-member",60254),symbolField:Kt("symbol-field",60255),symbolFile:Kt("symbol-file",60256),symbolInterface:Kt("symbol-interface",60257),symbolKeyword:Kt("symbol-keyword",60258),symbolMisc:Kt("symbol-misc",60259),symbolOperator:Kt("symbol-operator",60260),symbolProperty:Kt("symbol-property",60261),wrench:Kt("wrench",60261),wrenchSubaction:Kt("wrench-subaction",60261),symbolSnippet:Kt("symbol-snippet",60262),tasklist:Kt("tasklist",60263),telescope:Kt("telescope",60264),textSize:Kt("text-size",60265),threeBars:Kt("three-bars",60266),thumbsdown:Kt("thumbsdown",60267),thumbsup:Kt("thumbsup",60268),tools:Kt("tools",60269),triangleDown:Kt("triangle-down",60270),triangleLeft:Kt("triangle-left",60271),triangleRight:Kt("triangle-right",60272),triangleUp:Kt("triangle-up",60273),twitter:Kt("twitter",60274),unfold:Kt("unfold",60275),unlock:Kt("unlock",60276),unmute:Kt("unmute",60277),unverified:Kt("unverified",60278),verified:Kt("verified",60279),versions:Kt("versions",60280),vmActive:Kt("vm-active",60281),vmOutline:Kt("vm-outline",60282),vmRunning:Kt("vm-running",60283),watch:Kt("watch",60284),whitespace:Kt("whitespace",60285),wholeWord:Kt("whole-word",60286),window:Kt("window",60287),wordWrap:Kt("word-wrap",60288),zoomIn:Kt("zoom-in",60289),zoomOut:Kt("zoom-out",60290),listFilter:Kt("list-filter",60291),listFlat:Kt("list-flat",60292),listSelection:Kt("list-selection",60293),selection:Kt("selection",60293),listTree:Kt("list-tree",60294),debugBreakpointFunctionUnverified:Kt("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:Kt("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:Kt("debug-breakpoint-function-disabled",60296),debugStackframeActive:Kt("debug-stackframe-active",60297),circleSmallFilled:Kt("circle-small-filled",60298),debugStackframeDot:Kt("debug-stackframe-dot",60298),debugStackframe:Kt("debug-stackframe",60299),debugStackframeFocused:Kt("debug-stackframe-focused",60299),debugBreakpointUnsupported:Kt("debug-breakpoint-unsupported",60300),symbolString:Kt("symbol-string",60301),debugReverseContinue:Kt("debug-reverse-continue",60302),debugStepBack:Kt("debug-step-back",60303),debugRestartFrame:Kt("debug-restart-frame",60304),callIncoming:Kt("call-incoming",60306),callOutgoing:Kt("call-outgoing",60307),menu:Kt("menu",60308),expandAll:Kt("expand-all",60309),feedback:Kt("feedback",60310),gitPullRequestReviewer:Kt("git-pull-request-reviewer",60310),groupByRefType:Kt("group-by-ref-type",60311),ungroupByRefType:Kt("ungroup-by-ref-type",60312),account:Kt("account",60313),gitPullRequestAssignee:Kt("git-pull-request-assignee",60313),bellDot:Kt("bell-dot",60314),debugConsole:Kt("debug-console",60315),library:Kt("library",60316),output:Kt("output",60317),runAll:Kt("run-all",60318),syncIgnored:Kt("sync-ignored",60319),pinned:Kt("pinned",60320),githubInverted:Kt("github-inverted",60321),debugAlt:Kt("debug-alt",60305),serverProcess:Kt("server-process",60322),serverEnvironment:Kt("server-environment",60323),pass:Kt("pass",60324),stopCircle:Kt("stop-circle",60325),playCircle:Kt("play-circle",60326),record:Kt("record",60327),debugAltSmall:Kt("debug-alt-small",60328),vmConnect:Kt("vm-connect",60329),cloud:Kt("cloud",60330),merge:Kt("merge",60331),exportIcon:Kt("export",60332),graphLeft:Kt("graph-left",60333),magnet:Kt("magnet",60334),notebook:Kt("notebook",60335),redo:Kt("redo",60336),checkAll:Kt("check-all",60337),pinnedDirty:Kt("pinned-dirty",60338),passFilled:Kt("pass-filled",60339),circleLargeFilled:Kt("circle-large-filled",60340),circleLarge:Kt("circle-large",60341),circleLargeOutline:Kt("circle-large-outline",60341),combine:Kt("combine",60342),gather:Kt("gather",60342),table:Kt("table",60343),variableGroup:Kt("variable-group",60344),typeHierarchy:Kt("type-hierarchy",60345),typeHierarchySub:Kt("type-hierarchy-sub",60346),typeHierarchySuper:Kt("type-hierarchy-super",60347),gitPullRequestCreate:Kt("git-pull-request-create",60348),runAbove:Kt("run-above",60349),runBelow:Kt("run-below",60350),notebookTemplate:Kt("notebook-template",60351),debugRerun:Kt("debug-rerun",60352),workspaceTrusted:Kt("workspace-trusted",60353),workspaceUntrusted:Kt("workspace-untrusted",60354),workspaceUnspecified:Kt("workspace-unspecified",60355),terminalCmd:Kt("terminal-cmd",60356),terminalDebian:Kt("terminal-debian",60357),terminalLinux:Kt("terminal-linux",60358),terminalPowershell:Kt("terminal-powershell",60359),terminalTmux:Kt("terminal-tmux",60360),terminalUbuntu:Kt("terminal-ubuntu",60361),terminalBash:Kt("terminal-bash",60362),arrowSwap:Kt("arrow-swap",60363),copy:Kt("copy",60364),personAdd:Kt("person-add",60365),filterFilled:Kt("filter-filled",60366),wand:Kt("wand",60367),debugLineByLine:Kt("debug-line-by-line",60368),inspect:Kt("inspect",60369),layers:Kt("layers",60370),layersDot:Kt("layers-dot",60371),layersActive:Kt("layers-active",60372),compass:Kt("compass",60373),compassDot:Kt("compass-dot",60374),compassActive:Kt("compass-active",60375),azure:Kt("azure",60376),issueDraft:Kt("issue-draft",60377),gitPullRequestClosed:Kt("git-pull-request-closed",60378),gitPullRequestDraft:Kt("git-pull-request-draft",60379),debugAll:Kt("debug-all",60380),debugCoverage:Kt("debug-coverage",60381),runErrors:Kt("run-errors",60382),folderLibrary:Kt("folder-library",60383),debugContinueSmall:Kt("debug-continue-small",60384),beakerStop:Kt("beaker-stop",60385),graphLine:Kt("graph-line",60386),graphScatter:Kt("graph-scatter",60387),pieChart:Kt("pie-chart",60388),bracketDot:Kt("bracket-dot",60389),bracketError:Kt("bracket-error",60390),lockSmall:Kt("lock-small",60391),azureDevops:Kt("azure-devops",60392),verifiedFilled:Kt("verified-filled",60393),newLine:Kt("newline",60394),layout:Kt("layout",60395),layoutActivitybarLeft:Kt("layout-activitybar-left",60396),layoutActivitybarRight:Kt("layout-activitybar-right",60397),layoutPanelLeft:Kt("layout-panel-left",60398),layoutPanelCenter:Kt("layout-panel-center",60399),layoutPanelJustify:Kt("layout-panel-justify",60400),layoutPanelRight:Kt("layout-panel-right",60401),layoutPanel:Kt("layout-panel",60402),layoutSidebarLeft:Kt("layout-sidebar-left",60403),layoutSidebarRight:Kt("layout-sidebar-right",60404),layoutStatusbar:Kt("layout-statusbar",60405),layoutMenubar:Kt("layout-menubar",60406),layoutCentered:Kt("layout-centered",60407),layoutSidebarRightOff:Kt("layout-sidebar-right-off",60416),layoutPanelOff:Kt("layout-panel-off",60417),layoutSidebarLeftOff:Kt("layout-sidebar-left-off",60418),target:Kt("target",60408),indent:Kt("indent",60409),recordSmall:Kt("record-small",60410),errorSmall:Kt("error-small",60411),arrowCircleDown:Kt("arrow-circle-down",60412),arrowCircleLeft:Kt("arrow-circle-left",60413),arrowCircleRight:Kt("arrow-circle-right",60414),arrowCircleUp:Kt("arrow-circle-up",60415),heartFilled:Kt("heart-filled",60420),map:Kt("map",60421),mapFilled:Kt("map-filled",60422),circleSmall:Kt("circle-small",60423),bellSlash:Kt("bell-slash",60424),bellSlashDot:Kt("bell-slash-dot",60425),commentUnresolved:Kt("comment-unresolved",60426),gitPullRequestGoToChanges:Kt("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:Kt("git-pull-request-new-changes",60428),searchFuzzy:Kt("search-fuzzy",60429),commentDraft:Kt("comment-draft",60430),send:Kt("send",60431),sparkle:Kt("sparkle",60432),insert:Kt("insert",60433),mic:Kt("mic",60434),thumbsDownFilled:Kt("thumbsdown-filled",60435),thumbsUpFilled:Kt("thumbsup-filled",60436),coffee:Kt("coffee",60437),snake:Kt("snake",60438),game:Kt("game",60439),vr:Kt("vr",60440),chip:Kt("chip",60441),piano:Kt("piano",60442),music:Kt("music",60443),micFilled:Kt("mic-filled",60444),gitFetch:Kt("git-fetch",60445),copilot:Kt("copilot",60446),lightbulbSparkle:Kt("lightbulb-sparkle",60447),lightbulbSparkleAutofix:Kt("lightbulb-sparkle-autofix",60447),robot:Kt("robot",60448),sparkleFilled:Kt("sparkle-filled",60449),diffSingle:Kt("diff-single",60450),diffMultiple:Kt("diff-multiple",60451),surroundWith:Kt("surround-with",60452),gitStash:Kt("git-stash",60454),gitStashApply:Kt("git-stash-apply",60455),gitStashPop:Kt("git-stash-pop",60456),dialogError:Kt("dialog-error","error"),dialogWarning:Kt("dialog-warning","warning"),dialogInfo:Kt("dialog-info","info"),dialogClose:Kt("dialog-close","close"),treeItemExpanded:Kt("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:Kt("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:Kt("tree-filter-on-type-off","list-selection"),treeFilterClear:Kt("tree-filter-clear","close"),treeItemLoading:Kt("tree-item-loading","loading"),menuSelection:Kt("menu-selection","check"),menuSubmenu:Kt("menu-submenu","chevron-right"),menuBarMore:Kt("menubar-more","more"),scrollbarButtonLeft:Kt("scrollbar-button-left","triangle-left"),scrollbarButtonRight:Kt("scrollbar-button-right","triangle-right"),scrollbarButtonUp:Kt("scrollbar-button-up","triangle-up"),scrollbarButtonDown:Kt("scrollbar-button-down","triangle-down"),toolBarMore:Kt("toolbar-more","more"),quickInputBack:Kt("quick-input-back","arrow-left")};class Bt extends d{get isResolved(){return this._isResolved}constructor(e,t,n){super(),this._registry=e,this._languageId=t,this._factory=n,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}dispose(){this._isDisposed=!0,super.dispose()}async resolve(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise}async _create(){const e=await this._factory.tokenizationSupport;this._isResolved=!0,e&&!this._isDisposed&&this._register(this._registry.register(this._languageId,e))}}class Ut{constructor(e,t,n){this.offset=e,this.type=t,this.language=n,this._tokenBrand=void 0}toString(){return"("+this.offset+", "+this.type+")"}}var $t,Wt,Ht,zt,Gt,Jt,Xt,Qt,Zt,Yt,en,tn,nn,rn,sn,on,an,ln,un,cn,hn,dn,fn,mn,gn,pn,vn,bn,yn,Cn,_n,wn,Sn,Ln,En,xn,Nn,An,kn,Rn,Tn,Mn,On,In,Pn,Fn,Vn,Dn,qn,Kn,jn,Bn,Un,$n,Wn,Hn,zn,Gn,Jn,Xn,Qn,Zn,Yn,er,tr,nr,rr,ir,sr,or,ar,lr,ur,cr,hr,dr,fr,mr,gr,pr,vr,br,yr,Cr,_r,wr,Sr,Lr,Er,xr,Nr;!function(e){const t=new Map;t.set(0,jt.symbolMethod),t.set(1,jt.symbolFunction),t.set(2,jt.symbolConstructor),t.set(3,jt.symbolField),t.set(4,jt.symbolVariable),t.set(5,jt.symbolClass),t.set(6,jt.symbolStruct),t.set(7,jt.symbolInterface),t.set(8,jt.symbolModule),t.set(9,jt.symbolProperty),t.set(10,jt.symbolEvent),t.set(11,jt.symbolOperator),t.set(12,jt.symbolUnit),t.set(13,jt.symbolValue),t.set(15,jt.symbolEnum),t.set(14,jt.symbolConstant),t.set(15,jt.symbolEnum),t.set(16,jt.symbolEnumMember),t.set(17,jt.symbolKeyword),t.set(27,jt.symbolSnippet),t.set(18,jt.symbolText),t.set(19,jt.symbolColor),t.set(20,jt.symbolFile),t.set(21,jt.symbolReference),t.set(22,jt.symbolCustomColor),t.set(23,jt.symbolFolder),t.set(24,jt.symbolTypeParameter),t.set(25,jt.account),t.set(26,jt.issues),e.toIcon=function(e){let n=t.get(e);return n||(n=jt.symbolProperty),n};const n=new Map;n.set("method",0),n.set("function",1),n.set("constructor",2),n.set("field",3),n.set("variable",4),n.set("class",5),n.set("struct",6),n.set("interface",7),n.set("module",8),n.set("property",9),n.set("event",10),n.set("operator",11),n.set("unit",12),n.set("value",13),n.set("constant",14),n.set("enum",15),n.set("enum-member",16),n.set("enumMember",16),n.set("keyword",17),n.set("snippet",27),n.set("text",18),n.set("color",19),n.set("file",20),n.set("reference",21),n.set("customcolor",22),n.set("folder",23),n.set("type-parameter",24),n.set("typeParameter",24),n.set("account",25),n.set("issue",26),e.fromString=function(e,t){let r=n.get(e);return void 0!==r||t||(r=9),r}}($t||($t={})),(Ht=Wt||(Wt={}))[Ht.Automatic=0]="Automatic",Ht[Ht.Explicit=1]="Explicit",(Gt=zt||(zt={}))[Gt.Invoke=1]="Invoke",Gt[Gt.TriggerCharacter=2]="TriggerCharacter",Gt[Gt.ContentChange=3]="ContentChange",(Xt=Jt||(Jt={}))[Xt.Text=0]="Text",Xt[Xt.Read=1]="Read",Xt[Xt.Write=2]="Write",x(0,"array"),x(0,"boolean"),x(0,"class"),x(0,"constant"),x(0,"constructor"),x(0,"enumeration"),x(0,"enumeration member"),x(0,"event"),x(0,"field"),x(0,"file"),x(0,"function"),x(0,"interface"),x(0,"key"),x(0,"method"),x(0,"module"),x(0,"namespace"),x(0,"null"),x(0,"number"),x(0,"object"),x(0,"operator"),x(0,"package"),x(0,"property"),x(0,"string"),x(0,"struct"),x(0,"type parameter"),x(0,"variable"),function(e){const t=new Map;t.set(0,jt.symbolFile),t.set(1,jt.symbolModule),t.set(2,jt.symbolNamespace),t.set(3,jt.symbolPackage),t.set(4,jt.symbolClass),t.set(5,jt.symbolMethod),t.set(6,jt.symbolProperty),t.set(7,jt.symbolField),t.set(8,jt.symbolConstructor),t.set(9,jt.symbolEnum),t.set(10,jt.symbolInterface),t.set(11,jt.symbolFunction),t.set(12,jt.symbolVariable),t.set(13,jt.symbolConstant),t.set(14,jt.symbolString),t.set(15,jt.symbolNumber),t.set(16,jt.symbolBoolean),t.set(17,jt.symbolArray),t.set(18,jt.symbolObject),t.set(19,jt.symbolKey),t.set(20,jt.symbolNull),t.set(21,jt.symbolEnumMember),t.set(22,jt.symbolStruct),t.set(23,jt.symbolEvent),t.set(24,jt.symbolOperator),t.set(25,jt.symbolTypeParameter),e.toIcon=function(e){let n=t.get(e);return n||(n=jt.symbolProperty),n}}(Qt||(Qt={})),(Zt||(Zt={})).is=function(e){return!(!e||"object"!=typeof e)&&"string"==typeof e.id&&"string"==typeof e.title},(en=Yt||(Yt={}))[en.Type=1]="Type",en[en.Parameter=2]="Parameter",new class{constructor(){this._tokenizationSupports=new Map,this._factories=new Map,this._onDidChange=new w,this.onDidChange=this._onDidChange.event,this._colorMap=null}handleChange(e){this._onDidChange.fire({changedLanguages:e,changedColorMap:!1})}register(e,t){return this._tokenizationSupports.set(e,t),this.handleChange([e]),c((()=>{this._tokenizationSupports.get(e)===t&&(this._tokenizationSupports.delete(e),this.handleChange([e]))}))}get(e){return this._tokenizationSupports.get(e)||null}registerFactory(e,t){var n;null===(n=this._factories.get(e))||void 0===n||n.dispose();const r=new Bt(this,e,t);return this._factories.set(e,r),c((()=>{const t=this._factories.get(e);t&&t===r&&(this._factories.delete(e),t.dispose())}))}async getOrCreate(e){const t=this.get(e);if(t)return t;const n=this._factories.get(e);return!n||n.isResolved?null:(await n.resolve(),this.get(e))}isResolved(e){if(this.get(e))return!0;const t=this._factories.get(e);return!(t&&!t.isResolved)}setColorMap(e){this._colorMap=e,this._onDidChange.fire({changedLanguages:Array.from(this._tokenizationSupports.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}},(nn=tn||(tn={}))[nn.Unknown=0]="Unknown",nn[nn.Disabled=1]="Disabled",nn[nn.Enabled=2]="Enabled",(sn=rn||(rn={}))[sn.Invoke=1]="Invoke",sn[sn.Auto=2]="Auto",(an=on||(on={}))[an.None=0]="None",an[an.KeepWhitespace=1]="KeepWhitespace",an[an.InsertAsSnippet=4]="InsertAsSnippet",(un=ln||(ln={}))[un.Method=0]="Method",un[un.Function=1]="Function",un[un.Constructor=2]="Constructor",un[un.Field=3]="Field",un[un.Variable=4]="Variable",un[un.Class=5]="Class",un[un.Struct=6]="Struct",un[un.Interface=7]="Interface",un[un.Module=8]="Module",un[un.Property=9]="Property",un[un.Event=10]="Event",un[un.Operator=11]="Operator",un[un.Unit=12]="Unit",un[un.Value=13]="Value",un[un.Constant=14]="Constant",un[un.Enum=15]="Enum",un[un.EnumMember=16]="EnumMember",un[un.Keyword=17]="Keyword",un[un.Text=18]="Text",un[un.Color=19]="Color",un[un.File=20]="File",un[un.Reference=21]="Reference",un[un.Customcolor=22]="Customcolor",un[un.Folder=23]="Folder",un[un.TypeParameter=24]="TypeParameter",un[un.User=25]="User",un[un.Issue=26]="Issue",un[un.Snippet=27]="Snippet",(hn=cn||(cn={}))[hn.Deprecated=1]="Deprecated",(fn=dn||(dn={}))[fn.Invoke=0]="Invoke",fn[fn.TriggerCharacter=1]="TriggerCharacter",fn[fn.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions",(gn=mn||(mn={}))[gn.EXACT=0]="EXACT",gn[gn.ABOVE=1]="ABOVE",gn[gn.BELOW=2]="BELOW",(vn=pn||(pn={}))[vn.NotSet=0]="NotSet",vn[vn.ContentFlush=1]="ContentFlush",vn[vn.RecoverFromMarkers=2]="RecoverFromMarkers",vn[vn.Explicit=3]="Explicit",vn[vn.Paste=4]="Paste",vn[vn.Undo=5]="Undo",vn[vn.Redo=6]="Redo",(yn=bn||(bn={}))[yn.LF=1]="LF",yn[yn.CRLF=2]="CRLF",function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"}(Cn||(Cn={})),(wn=_n||(_n={}))[wn.None=0]="None",wn[wn.Keep=1]="Keep",wn[wn.Brackets=2]="Brackets",wn[wn.Advanced=3]="Advanced",wn[wn.Full=4]="Full",(Ln=Sn||(Sn={}))[Ln.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",Ln[Ln.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",Ln[Ln.accessibilitySupport=2]="accessibilitySupport",Ln[Ln.accessibilityPageSize=3]="accessibilityPageSize",Ln[Ln.ariaLabel=4]="ariaLabel",Ln[Ln.ariaRequired=5]="ariaRequired",Ln[Ln.autoClosingBrackets=6]="autoClosingBrackets",Ln[Ln.autoClosingComments=7]="autoClosingComments",Ln[Ln.screenReaderAnnounceInlineSuggestion=8]="screenReaderAnnounceInlineSuggestion",Ln[Ln.autoClosingDelete=9]="autoClosingDelete",Ln[Ln.autoClosingOvertype=10]="autoClosingOvertype",Ln[Ln.autoClosingQuotes=11]="autoClosingQuotes",Ln[Ln.autoIndent=12]="autoIndent",Ln[Ln.automaticLayout=13]="automaticLayout",Ln[Ln.autoSurround=14]="autoSurround",Ln[Ln.bracketPairColorization=15]="bracketPairColorization",Ln[Ln.guides=16]="guides",Ln[Ln.codeLens=17]="codeLens",Ln[Ln.codeLensFontFamily=18]="codeLensFontFamily",Ln[Ln.codeLensFontSize=19]="codeLensFontSize",Ln[Ln.colorDecorators=20]="colorDecorators",Ln[Ln.colorDecoratorsLimit=21]="colorDecoratorsLimit",Ln[Ln.columnSelection=22]="columnSelection",Ln[Ln.comments=23]="comments",Ln[Ln.contextmenu=24]="contextmenu",Ln[Ln.copyWithSyntaxHighlighting=25]="copyWithSyntaxHighlighting",Ln[Ln.cursorBlinking=26]="cursorBlinking",Ln[Ln.cursorSmoothCaretAnimation=27]="cursorSmoothCaretAnimation",Ln[Ln.cursorStyle=28]="cursorStyle",Ln[Ln.cursorSurroundingLines=29]="cursorSurroundingLines",Ln[Ln.cursorSurroundingLinesStyle=30]="cursorSurroundingLinesStyle",Ln[Ln.cursorWidth=31]="cursorWidth",Ln[Ln.disableLayerHinting=32]="disableLayerHinting",Ln[Ln.disableMonospaceOptimizations=33]="disableMonospaceOptimizations",Ln[Ln.domReadOnly=34]="domReadOnly",Ln[Ln.dragAndDrop=35]="dragAndDrop",Ln[Ln.dropIntoEditor=36]="dropIntoEditor",Ln[Ln.emptySelectionClipboard=37]="emptySelectionClipboard",Ln[Ln.experimentalWhitespaceRendering=38]="experimentalWhitespaceRendering",Ln[Ln.extraEditorClassName=39]="extraEditorClassName",Ln[Ln.fastScrollSensitivity=40]="fastScrollSensitivity",Ln[Ln.find=41]="find",Ln[Ln.fixedOverflowWidgets=42]="fixedOverflowWidgets",Ln[Ln.folding=43]="folding",Ln[Ln.foldingStrategy=44]="foldingStrategy",Ln[Ln.foldingHighlight=45]="foldingHighlight",Ln[Ln.foldingImportsByDefault=46]="foldingImportsByDefault",Ln[Ln.foldingMaximumRegions=47]="foldingMaximumRegions",Ln[Ln.unfoldOnClickAfterEndOfLine=48]="unfoldOnClickAfterEndOfLine",Ln[Ln.fontFamily=49]="fontFamily",Ln[Ln.fontInfo=50]="fontInfo",Ln[Ln.fontLigatures=51]="fontLigatures",Ln[Ln.fontSize=52]="fontSize",Ln[Ln.fontWeight=53]="fontWeight",Ln[Ln.fontVariations=54]="fontVariations",Ln[Ln.formatOnPaste=55]="formatOnPaste",Ln[Ln.formatOnType=56]="formatOnType",Ln[Ln.glyphMargin=57]="glyphMargin",Ln[Ln.gotoLocation=58]="gotoLocation",Ln[Ln.hideCursorInOverviewRuler=59]="hideCursorInOverviewRuler",Ln[Ln.hover=60]="hover",Ln[Ln.inDiffEditor=61]="inDiffEditor",Ln[Ln.inlineSuggest=62]="inlineSuggest",Ln[Ln.letterSpacing=63]="letterSpacing",Ln[Ln.lightbulb=64]="lightbulb",Ln[Ln.lineDecorationsWidth=65]="lineDecorationsWidth",Ln[Ln.lineHeight=66]="lineHeight",Ln[Ln.lineNumbers=67]="lineNumbers",Ln[Ln.lineNumbersMinChars=68]="lineNumbersMinChars",Ln[Ln.linkedEditing=69]="linkedEditing",Ln[Ln.links=70]="links",Ln[Ln.matchBrackets=71]="matchBrackets",Ln[Ln.minimap=72]="minimap",Ln[Ln.mouseStyle=73]="mouseStyle",Ln[Ln.mouseWheelScrollSensitivity=74]="mouseWheelScrollSensitivity",Ln[Ln.mouseWheelZoom=75]="mouseWheelZoom",Ln[Ln.multiCursorMergeOverlapping=76]="multiCursorMergeOverlapping",Ln[Ln.multiCursorModifier=77]="multiCursorModifier",Ln[Ln.multiCursorPaste=78]="multiCursorPaste",Ln[Ln.multiCursorLimit=79]="multiCursorLimit",Ln[Ln.occurrencesHighlight=80]="occurrencesHighlight",Ln[Ln.overviewRulerBorder=81]="overviewRulerBorder",Ln[Ln.overviewRulerLanes=82]="overviewRulerLanes",Ln[Ln.padding=83]="padding",Ln[Ln.pasteAs=84]="pasteAs",Ln[Ln.parameterHints=85]="parameterHints",Ln[Ln.peekWidgetDefaultFocus=86]="peekWidgetDefaultFocus",Ln[Ln.definitionLinkOpensInPeek=87]="definitionLinkOpensInPeek",Ln[Ln.quickSuggestions=88]="quickSuggestions",Ln[Ln.quickSuggestionsDelay=89]="quickSuggestionsDelay",Ln[Ln.readOnly=90]="readOnly",Ln[Ln.readOnlyMessage=91]="readOnlyMessage",Ln[Ln.renameOnType=92]="renameOnType",Ln[Ln.renderControlCharacters=93]="renderControlCharacters",Ln[Ln.renderFinalNewline=94]="renderFinalNewline",Ln[Ln.renderLineHighlight=95]="renderLineHighlight",Ln[Ln.renderLineHighlightOnlyWhenFocus=96]="renderLineHighlightOnlyWhenFocus",Ln[Ln.renderValidationDecorations=97]="renderValidationDecorations",Ln[Ln.renderWhitespace=98]="renderWhitespace",Ln[Ln.revealHorizontalRightPadding=99]="revealHorizontalRightPadding",Ln[Ln.roundedSelection=100]="roundedSelection",Ln[Ln.rulers=101]="rulers",Ln[Ln.scrollbar=102]="scrollbar",Ln[Ln.scrollBeyondLastColumn=103]="scrollBeyondLastColumn",Ln[Ln.scrollBeyondLastLine=104]="scrollBeyondLastLine",Ln[Ln.scrollPredominantAxis=105]="scrollPredominantAxis",Ln[Ln.selectionClipboard=106]="selectionClipboard",Ln[Ln.selectionHighlight=107]="selectionHighlight",Ln[Ln.selectOnLineNumbers=108]="selectOnLineNumbers",Ln[Ln.showFoldingControls=109]="showFoldingControls",Ln[Ln.showUnused=110]="showUnused",Ln[Ln.snippetSuggestions=111]="snippetSuggestions",Ln[Ln.smartSelect=112]="smartSelect",Ln[Ln.smoothScrolling=113]="smoothScrolling",Ln[Ln.stickyScroll=114]="stickyScroll",Ln[Ln.stickyTabStops=115]="stickyTabStops",Ln[Ln.stopRenderingLineAfter=116]="stopRenderingLineAfter",Ln[Ln.suggest=117]="suggest",Ln[Ln.suggestFontSize=118]="suggestFontSize",Ln[Ln.suggestLineHeight=119]="suggestLineHeight",Ln[Ln.suggestOnTriggerCharacters=120]="suggestOnTriggerCharacters",Ln[Ln.suggestSelection=121]="suggestSelection",Ln[Ln.tabCompletion=122]="tabCompletion",Ln[Ln.tabIndex=123]="tabIndex",Ln[Ln.unicodeHighlighting=124]="unicodeHighlighting",Ln[Ln.unusualLineTerminators=125]="unusualLineTerminators",Ln[Ln.useShadowDOM=126]="useShadowDOM",Ln[Ln.useTabStops=127]="useTabStops",Ln[Ln.wordBreak=128]="wordBreak",Ln[Ln.wordSeparators=129]="wordSeparators",Ln[Ln.wordWrap=130]="wordWrap",Ln[Ln.wordWrapBreakAfterCharacters=131]="wordWrapBreakAfterCharacters",Ln[Ln.wordWrapBreakBeforeCharacters=132]="wordWrapBreakBeforeCharacters",Ln[Ln.wordWrapColumn=133]="wordWrapColumn",Ln[Ln.wordWrapOverride1=134]="wordWrapOverride1",Ln[Ln.wordWrapOverride2=135]="wordWrapOverride2",Ln[Ln.wrappingIndent=136]="wrappingIndent",Ln[Ln.wrappingStrategy=137]="wrappingStrategy",Ln[Ln.showDeprecated=138]="showDeprecated",Ln[Ln.inlayHints=139]="inlayHints",Ln[Ln.editorClassName=140]="editorClassName",Ln[Ln.pixelRatio=141]="pixelRatio",Ln[Ln.tabFocusMode=142]="tabFocusMode",Ln[Ln.layoutInfo=143]="layoutInfo",Ln[Ln.wrappingInfo=144]="wrappingInfo",Ln[Ln.defaultColorDecorators=145]="defaultColorDecorators",Ln[Ln.colorDecoratorsActivatedOn=146]="colorDecoratorsActivatedOn",Ln[Ln.inlineCompletionsAccessibilityVerbose=147]="inlineCompletionsAccessibilityVerbose",(xn=En||(En={}))[xn.TextDefined=0]="TextDefined",xn[xn.LF=1]="LF",xn[xn.CRLF=2]="CRLF",(An=Nn||(Nn={}))[An.LF=0]="LF",An[An.CRLF=1]="CRLF",(Rn=kn||(kn={}))[Rn.Left=1]="Left",Rn[Rn.Center=2]="Center",Rn[Rn.Right=3]="Right",(Mn=Tn||(Tn={}))[Mn.None=0]="None",Mn[Mn.Indent=1]="Indent",Mn[Mn.IndentOutdent=2]="IndentOutdent",Mn[Mn.Outdent=3]="Outdent",(In=On||(On={}))[In.Both=0]="Both",In[In.Right=1]="Right",In[In.Left=2]="Left",In[In.None=3]="None",function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"}(Pn||(Pn={})),function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"}(Fn||(Fn={})),(Dn=Vn||(Vn={}))[Dn.DependsOnKbLayout=-1]="DependsOnKbLayout",Dn[Dn.Unknown=0]="Unknown",Dn[Dn.Backspace=1]="Backspace",Dn[Dn.Tab=2]="Tab",Dn[Dn.Enter=3]="Enter",Dn[Dn.Shift=4]="Shift",Dn[Dn.Ctrl=5]="Ctrl",Dn[Dn.Alt=6]="Alt",Dn[Dn.PauseBreak=7]="PauseBreak",Dn[Dn.CapsLock=8]="CapsLock",Dn[Dn.Escape=9]="Escape",Dn[Dn.Space=10]="Space",Dn[Dn.PageUp=11]="PageUp",Dn[Dn.PageDown=12]="PageDown",Dn[Dn.End=13]="End",Dn[Dn.Home=14]="Home",Dn[Dn.LeftArrow=15]="LeftArrow",Dn[Dn.UpArrow=16]="UpArrow",Dn[Dn.RightArrow=17]="RightArrow",Dn[Dn.DownArrow=18]="DownArrow",Dn[Dn.Insert=19]="Insert",Dn[Dn.Delete=20]="Delete",Dn[Dn.Digit0=21]="Digit0",Dn[Dn.Digit1=22]="Digit1",Dn[Dn.Digit2=23]="Digit2",Dn[Dn.Digit3=24]="Digit3",Dn[Dn.Digit4=25]="Digit4",Dn[Dn.Digit5=26]="Digit5",Dn[Dn.Digit6=27]="Digit6",Dn[Dn.Digit7=28]="Digit7",Dn[Dn.Digit8=29]="Digit8",Dn[Dn.Digit9=30]="Digit9",Dn[Dn.KeyA=31]="KeyA",Dn[Dn.KeyB=32]="KeyB",Dn[Dn.KeyC=33]="KeyC",Dn[Dn.KeyD=34]="KeyD",Dn[Dn.KeyE=35]="KeyE",Dn[Dn.KeyF=36]="KeyF",Dn[Dn.KeyG=37]="KeyG",Dn[Dn.KeyH=38]="KeyH",Dn[Dn.KeyI=39]="KeyI",Dn[Dn.KeyJ=40]="KeyJ",Dn[Dn.KeyK=41]="KeyK",Dn[Dn.KeyL=42]="KeyL",Dn[Dn.KeyM=43]="KeyM",Dn[Dn.KeyN=44]="KeyN",Dn[Dn.KeyO=45]="KeyO",Dn[Dn.KeyP=46]="KeyP",Dn[Dn.KeyQ=47]="KeyQ",Dn[Dn.KeyR=48]="KeyR",Dn[Dn.KeyS=49]="KeyS",Dn[Dn.KeyT=50]="KeyT",Dn[Dn.KeyU=51]="KeyU",Dn[Dn.KeyV=52]="KeyV",Dn[Dn.KeyW=53]="KeyW",Dn[Dn.KeyX=54]="KeyX",Dn[Dn.KeyY=55]="KeyY",Dn[Dn.KeyZ=56]="KeyZ",Dn[Dn.Meta=57]="Meta",Dn[Dn.ContextMenu=58]="ContextMenu",Dn[Dn.F1=59]="F1",Dn[Dn.F2=60]="F2",Dn[Dn.F3=61]="F3",Dn[Dn.F4=62]="F4",Dn[Dn.F5=63]="F5",Dn[Dn.F6=64]="F6",Dn[Dn.F7=65]="F7",Dn[Dn.F8=66]="F8",Dn[Dn.F9=67]="F9",Dn[Dn.F10=68]="F10",Dn[Dn.F11=69]="F11",Dn[Dn.F12=70]="F12",Dn[Dn.F13=71]="F13",Dn[Dn.F14=72]="F14",Dn[Dn.F15=73]="F15",Dn[Dn.F16=74]="F16",Dn[Dn.F17=75]="F17",Dn[Dn.F18=76]="F18",Dn[Dn.F19=77]="F19",Dn[Dn.F20=78]="F20",Dn[Dn.F21=79]="F21",Dn[Dn.F22=80]="F22",Dn[Dn.F23=81]="F23",Dn[Dn.F24=82]="F24",Dn[Dn.NumLock=83]="NumLock",Dn[Dn.ScrollLock=84]="ScrollLock",Dn[Dn.Semicolon=85]="Semicolon",Dn[Dn.Equal=86]="Equal",Dn[Dn.Comma=87]="Comma",Dn[Dn.Minus=88]="Minus",Dn[Dn.Period=89]="Period",Dn[Dn.Slash=90]="Slash",Dn[Dn.Backquote=91]="Backquote",Dn[Dn.BracketLeft=92]="BracketLeft",Dn[Dn.Backslash=93]="Backslash",Dn[Dn.BracketRight=94]="BracketRight",Dn[Dn.Quote=95]="Quote",Dn[Dn.OEM_8=96]="OEM_8",Dn[Dn.IntlBackslash=97]="IntlBackslash",Dn[Dn.Numpad0=98]="Numpad0",Dn[Dn.Numpad1=99]="Numpad1",Dn[Dn.Numpad2=100]="Numpad2",Dn[Dn.Numpad3=101]="Numpad3",Dn[Dn.Numpad4=102]="Numpad4",Dn[Dn.Numpad5=103]="Numpad5",Dn[Dn.Numpad6=104]="Numpad6",Dn[Dn.Numpad7=105]="Numpad7",Dn[Dn.Numpad8=106]="Numpad8",Dn[Dn.Numpad9=107]="Numpad9",Dn[Dn.NumpadMultiply=108]="NumpadMultiply",Dn[Dn.NumpadAdd=109]="NumpadAdd",Dn[Dn.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",Dn[Dn.NumpadSubtract=111]="NumpadSubtract",Dn[Dn.NumpadDecimal=112]="NumpadDecimal",Dn[Dn.NumpadDivide=113]="NumpadDivide",Dn[Dn.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",Dn[Dn.ABNT_C1=115]="ABNT_C1",Dn[Dn.ABNT_C2=116]="ABNT_C2",Dn[Dn.AudioVolumeMute=117]="AudioVolumeMute",Dn[Dn.AudioVolumeUp=118]="AudioVolumeUp",Dn[Dn.AudioVolumeDown=119]="AudioVolumeDown",Dn[Dn.BrowserSearch=120]="BrowserSearch",Dn[Dn.BrowserHome=121]="BrowserHome",Dn[Dn.BrowserBack=122]="BrowserBack",Dn[Dn.BrowserForward=123]="BrowserForward",Dn[Dn.MediaTrackNext=124]="MediaTrackNext",Dn[Dn.MediaTrackPrevious=125]="MediaTrackPrevious",Dn[Dn.MediaStop=126]="MediaStop",Dn[Dn.MediaPlayPause=127]="MediaPlayPause",Dn[Dn.LaunchMediaPlayer=128]="LaunchMediaPlayer",Dn[Dn.LaunchMail=129]="LaunchMail",Dn[Dn.LaunchApp2=130]="LaunchApp2",Dn[Dn.Clear=131]="Clear",Dn[Dn.MAX_VALUE=132]="MAX_VALUE",(Kn=qn||(qn={}))[Kn.Hint=1]="Hint",Kn[Kn.Info=2]="Info",Kn[Kn.Warning=4]="Warning",Kn[Kn.Error=8]="Error",(Bn=jn||(jn={}))[Bn.Unnecessary=1]="Unnecessary",Bn[Bn.Deprecated=2]="Deprecated",($n=Un||(Un={}))[$n.Inline=1]="Inline",$n[$n.Gutter=2]="Gutter",(Hn=Wn||(Wn={}))[Hn.UNKNOWN=0]="UNKNOWN",Hn[Hn.TEXTAREA=1]="TEXTAREA",Hn[Hn.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",Hn[Hn.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",Hn[Hn.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",Hn[Hn.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",Hn[Hn.CONTENT_TEXT=6]="CONTENT_TEXT",Hn[Hn.CONTENT_EMPTY=7]="CONTENT_EMPTY",Hn[Hn.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",Hn[Hn.CONTENT_WIDGET=9]="CONTENT_WIDGET",Hn[Hn.OVERVIEW_RULER=10]="OVERVIEW_RULER",Hn[Hn.SCROLLBAR=11]="SCROLLBAR",Hn[Hn.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",Hn[Hn.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR",(Gn=zn||(zn={}))[Gn.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",Gn[Gn.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",Gn[Gn.TOP_CENTER=2]="TOP_CENTER",(Xn=Jn||(Jn={}))[Xn.Left=1]="Left",Xn[Xn.Center=2]="Center",Xn[Xn.Right=4]="Right",Xn[Xn.Full=7]="Full",(Zn=Qn||(Qn={}))[Zn.Left=0]="Left",Zn[Zn.Right=1]="Right",Zn[Zn.None=2]="None",Zn[Zn.LeftOfInjectedText=3]="LeftOfInjectedText",Zn[Zn.RightOfInjectedText=4]="RightOfInjectedText",(er=Yn||(Yn={}))[er.Off=0]="Off",er[er.On=1]="On",er[er.Relative=2]="Relative",er[er.Interval=3]="Interval",er[er.Custom=4]="Custom",(nr=tr||(tr={}))[nr.None=0]="None",nr[nr.Text=1]="Text",nr[nr.Blocks=2]="Blocks",(ir=rr||(rr={}))[ir.Smooth=0]="Smooth",ir[ir.Immediate=1]="Immediate",(or=sr||(sr={}))[or.Auto=1]="Auto",or[or.Hidden=2]="Hidden",or[or.Visible=3]="Visible",(lr=ar||(ar={}))[lr.LTR=0]="LTR",lr[lr.RTL=1]="RTL",(cr=ur||(ur={})).Off="off",cr.OnCode="onCode",cr.On="on",function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"}(hr||(hr={})),(fr=dr||(dr={}))[fr.File=0]="File",fr[fr.Module=1]="Module",fr[fr.Namespace=2]="Namespace",fr[fr.Package=3]="Package",fr[fr.Class=4]="Class",fr[fr.Method=5]="Method",fr[fr.Property=6]="Property",fr[fr.Field=7]="Field",fr[fr.Constructor=8]="Constructor",fr[fr.Enum=9]="Enum",fr[fr.Interface=10]="Interface",fr[fr.Function=11]="Function",fr[fr.Variable=12]="Variable",fr[fr.Constant=13]="Constant",fr[fr.String=14]="String",fr[fr.Number=15]="Number",fr[fr.Boolean=16]="Boolean",fr[fr.Array=17]="Array",fr[fr.Object=18]="Object",fr[fr.Key=19]="Key",fr[fr.Null=20]="Null",fr[fr.EnumMember=21]="EnumMember",fr[fr.Struct=22]="Struct",fr[fr.Event=23]="Event",fr[fr.Operator=24]="Operator",fr[fr.TypeParameter=25]="TypeParameter",(gr=mr||(mr={}))[gr.Deprecated=1]="Deprecated",(vr=pr||(pr={}))[vr.Hidden=0]="Hidden",vr[vr.Blink=1]="Blink",vr[vr.Smooth=2]="Smooth",vr[vr.Phase=3]="Phase",vr[vr.Expand=4]="Expand",vr[vr.Solid=5]="Solid",(yr=br||(br={}))[yr.Line=1]="Line",yr[yr.Block=2]="Block",yr[yr.Underline=3]="Underline",yr[yr.LineThin=4]="LineThin",yr[yr.BlockOutline=5]="BlockOutline",yr[yr.UnderlineThin=6]="UnderlineThin",(_r=Cr||(Cr={}))[_r.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",_r[_r.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",_r[_r.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",_r[_r.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter",(Sr=wr||(wr={}))[Sr.None=0]="None",Sr[Sr.Same=1]="Same",Sr[Sr.Indent=2]="Indent",Sr[Sr.DeepIndent=3]="DeepIndent";class Ar{static chord(e,t){return function(e,t){return(e|(65535&t)<<16>>>0)>>>0}(e,t)}}function kr(e,t,n,r,i){return function(e,t,n,r,i){if(0===r)return!0;const s=t.charCodeAt(r-1);if(0!==e.get(s))return!0;if(13===s||10===s)return!0;if(i>0){const n=t.charCodeAt(r);if(0!==e.get(n))return!0}return!1}(e,t,0,r,i)&&function(e,t,n,r,i){if(r+i===n)return!0;const s=t.charCodeAt(r+i);if(0!==e.get(s))return!0;if(13===s||10===s)return!0;if(i>0){const n=t.charCodeAt(r+i-1);if(0!==e.get(n))return!0}return!1}(e,t,n,r,i)}Ar.CtrlCmd=2048,Ar.Shift=1024,Ar.Alt=512,Ar.WinCtrl=256,function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"}(Lr||(Lr={})),function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=3]="Right"}(Er||(Er={})),function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"}(xr||(xr={})),function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"}(Nr||(Nr={}));class Rr{constructor(e,t){this._wordSeparators=e,this._searchRegex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(e){this._searchRegex.lastIndex=e,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(e){const t=e.length;let n;do{if(this._prevMatchStartIndex+this._prevMatchLength===t)return null;if(n=this._searchRegex.exec(e),!n)return null;const r=n.index,i=n[0].length;if(r===this._prevMatchStartIndex&&i===this._prevMatchLength){if(0===i){J(e,t,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=r,this._prevMatchLength=i,!this._wordSeparators||kr(this._wordSeparators,e,t,r,i))return n}while(n);return null}}function Tr(e,t="Unreachable"){throw new Error(t)}function Mr(e){e()||(e(),t(new o("Assertion Failed")))}function Or(e,t){let n=0;for(;n<e.length-1;){if(!t(e[n],e[n+1]))return!1;n++}return!0}class Ir{static computeUnicodeHighlights(e,t,n){const r=n?n.startLineNumber:1,i=n?n.endLineNumber:e.getLineCount(),s=new Pr(t),o=s.getCandidateCodePoints();let a;var l,u;a="allNonBasicAscii"===o?new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):new RegExp(""+(l=Array.from(o),`[${u=l.map((e=>String.fromCodePoint(e))).join(""),u.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}]`),"g");const c=new Rr(null,a),h=[];let d,f=!1,m=0,g=0,p=0;e:for(let v=r,b=i;v<=b;v++){const t=e.getLineContent(v),n=t.length;c.reset(0);do{if(d=c.next(t),d){let e=d.index,r=d.index+d[0].length;if(e>0){G(t.charCodeAt(e-1))&&e--}if(r+1<n){G(t.charCodeAt(r-1))&&r++}const i=t.substring(e,r);let o=gt(e+1,dt,t,0);o&&o.endColumn<=e+1&&(o=null);const a=s.shouldHighlightNonBasicASCII(i,o?o.word:null);if(0!==a){3===a?m++:2===a?g++:1===a?p++:Tr();const t=1e3;if(h.length>=t){f=!0;break e}h.push(new nt(v,e+1,v,r+1))}}}while(d)}return{ranges:h,hasMore:f,ambiguousCharacterCount:m,invisibleCharacterCount:g,nonBasicAsciiCharacterCount:p}}static computeUnicodeHighlightReason(e,t){const n=new Pr(t);switch(n.shouldHighlightNonBasicASCII(e,null)){case 0:return null;case 2:return{kind:1};case 3:{const r=e.codePointAt(0),i=n.ambiguousCharacters.getPrimaryConfusable(r),s=Q.getLocales().filter((e=>!Q.getInstance(new Set([...t.allowedLocales,e])).isAmbiguous(r)));return{kind:0,confusableWith:String.fromCodePoint(i),notAmbiguousInLocales:s}}case 1:return{kind:2}}}}class Pr{constructor(e){this.options=e,this.allowedCodePoints=new Set(e.allowedCodePoints),this.ambiguousCharacters=Q.getInstance(new Set(e.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";const e=new Set;if(this.options.invisibleCharacters)for(const t of Z.codePoints)Fr(String.fromCodePoint(t))||e.add(t);if(this.options.ambiguousCharacters)for(const t of this.ambiguousCharacters.getConfusableCodePoints())e.add(t);for(const t of this.allowedCodePoints)e.delete(t);return e}shouldHighlightNonBasicASCII(e,t){const n=e.codePointAt(0);if(this.allowedCodePoints.has(n))return 0;if(this.options.nonBasicASCII)return 1;let r=!1,i=!1;if(t)for(const o of t){const e=o.codePointAt(0),t=(s=o,X.test(s));r=r||t,t||this.ambiguousCharacters.isAmbiguous(e)||Z.isInvisibleCharacter(e)||(i=!0)}var s;return!r&&i?0:this.options.invisibleCharacters&&!Fr(e)&&Z.isInvisibleCharacter(n)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(n)?3:0}}function Fr(e){return" "===e||"\n"===e||"\t"===e}class Vr{constructor(e,t,n){this.changes=e,this.moves=t,this.hitTimeout=n}}class Dr{constructor(e,t){this.lineRangeMapping=e,this.changes=t}}class qr{static addRange(e,t){let n=0;for(;n<t.length&&t[n].endExclusive<e.start;)n++;let r=n;for(;r<t.length&&t[r].start<=e.endExclusive;)r++;if(n===r)t.splice(n,0,e);else{const i=Math.min(e.start,t[n].start),s=Math.max(e.endExclusive,t[r-1].endExclusive);t.splice(n,r-n,new qr(i,s))}}static ofLength(e){return new qr(0,e)}static ofStartAndLength(e,t){return new qr(e,e+t)}constructor(e,t){if(this.start=e,this.endExclusive=t,e>t)throw new o(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(e){return new qr(this.start+e,this.endExclusive+e)}deltaStart(e){return new qr(this.start+e,this.endExclusive)}deltaEnd(e){return new qr(this.start,this.endExclusive+e)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}contains(e){return this.start<=e&&e<this.endExclusive}join(e){return new qr(Math.min(this.start,e.start),Math.max(this.endExclusive,e.endExclusive))}intersect(e){const t=Math.max(this.start,e.start),n=Math.min(this.endExclusive,e.endExclusive);if(t<=n)return new qr(t,n)}intersects(e){return Math.max(this.start,e.start)<Math.min(this.endExclusive,e.endExclusive)}isBefore(e){return this.endExclusive<=e.start}isAfter(e){return this.start>=e.endExclusive}slice(e){return e.slice(this.start,this.endExclusive)}clip(e){if(this.isEmpty)throw new o(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,e))}clipCyclic(e){if(this.isEmpty)throw new o(`Invalid clipping range: ${this.toString()}`);return e<this.start?this.endExclusive-(this.start-e)%this.length:e>=this.endExclusive?this.start+(e-this.start)%this.length:e}forEach(e){for(let t=this.start;t<this.endExclusive;t++)e(t)}}function Kr(e,t){const n=jr(e,t);return-1===n?void 0:e[n]}function jr(e,t,n=0,r=e.length){let i=n,s=r;for(;i<s;){const n=Math.floor((i+s)/2);t(e[n])?i=n+1:s=n}return i-1}function Br(e,t,n=0,r=e.length){let i=n,s=r;for(;i<s;){const n=Math.floor((i+s)/2);t(e[n])?s=n:i=n+1}return i}class Ur{constructor(e){this._array=e,this._findLastMonotonousLastIdx=0}findLastMonotonous(e){if(Ur.assertInvariants){if(this._prevFindLastPredicate)for(const t of this._array)if(this._prevFindLastPredicate(t)&&!e(t))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.");this._prevFindLastPredicate=e}const t=jr(this._array,e,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=t+1,-1===t?void 0:this._array[t]}}Ur.assertInvariants=!1;class $r{static fromRangeInclusive(e){return new $r(e.startLineNumber,e.endLineNumber+1)}static joinMany(e){if(0===e.length)return[];let t=new Wr(e[0].slice());for(let n=1;n<e.length;n++)t=t.getUnion(new Wr(e[n].slice()));return t.ranges}static ofLength(e,t){return new $r(e,e+t)}static deserialize(e){return new $r(e[0],e[1])}constructor(e,t){if(e>t)throw new o(`startLineNumber ${e} cannot be after endLineNumberExclusive ${t}`);this.startLineNumber=e,this.endLineNumberExclusive=t}contains(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(e){return new $r(this.startLineNumber+e,this.endLineNumberExclusive+e)}deltaLength(e){return new $r(this.startLineNumber,this.endLineNumberExclusive+e)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(e){return new $r(Math.min(this.startLineNumber,e.startLineNumber),Math.max(this.endLineNumberExclusive,e.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(e){const t=Math.max(this.startLineNumber,e.startLineNumber),n=Math.min(this.endLineNumberExclusive,e.endLineNumberExclusive);if(t<=n)return new $r(t,n)}intersectsStrict(e){return this.startLineNumber<e.endLineNumberExclusive&&e.startLineNumber<this.endLineNumberExclusive}overlapOrTouch(e){return this.startLineNumber<=e.endLineNumberExclusive&&e.startLineNumber<=this.endLineNumberExclusive}equals(e){return this.startLineNumber===e.startLineNumber&&this.endLineNumberExclusive===e.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new nt(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)}toExclusiveRange(){return new nt(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(e){const t=[];for(let n=this.startLineNumber;n<this.endLineNumberExclusive;n++)t.push(e(n));return t}forEach(e){for(let t=this.startLineNumber;t<this.endLineNumberExclusive;t++)e(t)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}includes(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}toOffsetRange(){return new qr(this.startLineNumber-1,this.endLineNumberExclusive-1)}}class Wr{constructor(e=[]){this._normalizedRanges=e}get ranges(){return this._normalizedRanges}addRange(e){if(0===e.length)return;const t=Br(this._normalizedRanges,(t=>t.endLineNumberExclusive>=e.startLineNumber)),n=jr(this._normalizedRanges,(t=>t.startLineNumber<=e.endLineNumberExclusive))+1;if(t===n)this._normalizedRanges.splice(t,0,e);else if(t===n-1){const n=this._normalizedRanges[t];this._normalizedRanges[t]=n.join(e)}else{const r=this._normalizedRanges[t].join(this._normalizedRanges[n-1]).join(e);this._normalizedRanges.splice(t,n-t,r)}}contains(e){const t=Kr(this._normalizedRanges,(t=>t.startLineNumber<=e));return!!t&&t.endLineNumberExclusive>e}intersects(e){const t=Kr(this._normalizedRanges,(t=>t.startLineNumber<e.endLineNumberExclusive));return!!t&&t.endLineNumberExclusive>e.startLineNumber}getUnion(e){if(0===this._normalizedRanges.length)return e;if(0===e._normalizedRanges.length)return this;const t=[];let n=0,r=0,i=null;for(;n<this._normalizedRanges.length||r<e._normalizedRanges.length;){let s=null;if(n<this._normalizedRanges.length&&r<e._normalizedRanges.length){const t=this._normalizedRanges[n],i=e._normalizedRanges[r];t.startLineNumber<i.startLineNumber?(s=t,n++):(s=i,r++)}else n<this._normalizedRanges.length?(s=this._normalizedRanges[n],n++):(s=e._normalizedRanges[r],r++);null===i?i=s:i.endLineNumberExclusive>=s.startLineNumber?i=new $r(i.startLineNumber,Math.max(i.endLineNumberExclusive,s.endLineNumberExclusive)):(t.push(i),i=s)}return null!==i&&t.push(i),new Wr(t)}subtractFrom(e){const t=Br(this._normalizedRanges,(t=>t.endLineNumberExclusive>=e.startLineNumber)),n=jr(this._normalizedRanges,(t=>t.startLineNumber<=e.endLineNumberExclusive))+1;if(t===n)return new Wr([e]);const r=[];let i=e.startLineNumber;for(let s=t;s<n;s++){const e=this._normalizedRanges[s];e.startLineNumber>i&&r.push(new $r(i,e.startLineNumber)),i=e.endLineNumberExclusive}return i<e.endLineNumberExclusive&&r.push(new $r(i,e.endLineNumberExclusive)),new Wr(r)}toString(){return this._normalizedRanges.map((e=>e.toString())).join(", ")}getIntersection(e){const t=[];let n=0,r=0;for(;n<this._normalizedRanges.length&&r<e._normalizedRanges.length;){const i=this._normalizedRanges[n],s=e._normalizedRanges[r],o=i.intersect(s);o&&!o.isEmpty&&t.push(o),i.endLineNumberExclusive<s.endLineNumberExclusive?n++:r++}return new Wr(t)}getWithDelta(e){return new Wr(this._normalizedRanges.map((t=>t.delta(e))))}}class Hr{static inverse(e,t,n){const r=[];let i=1,s=1;for(const a of e){const e=new Hr(new $r(i,a.original.startLineNumber),new $r(s,a.modified.startLineNumber));e.modified.isEmpty||r.push(e),i=a.original.endLineNumberExclusive,s=a.modified.endLineNumberExclusive}const o=new Hr(new $r(i,t+1),new $r(s,n+1));return o.modified.isEmpty||r.push(o),r}static clip(e,t,n){const r=[];for(const i of e){const e=i.original.intersect(t),s=i.modified.intersect(n);e&&!e.isEmpty&&s&&!s.isEmpty&&r.push(new Hr(e,s))}return r}constructor(e,t){this.original=e,this.modified=t}toString(){return`{${this.original.toString()}->${this.modified.toString()}}`}flip(){return new Hr(this.modified,this.original)}join(e){return new Hr(this.original.join(e.original),this.modified.join(e.modified))}}class zr extends Hr{constructor(e,t,n){super(e,t),this.innerChanges=n}flip(){var e;return new zr(this.modified,this.original,null===(e=this.innerChanges)||void 0===e?void 0:e.map((e=>e.flip())))}}class Gr{constructor(e,t){this.originalRange=e,this.modifiedRange=t}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}flip(){return new Gr(this.modifiedRange,this.originalRange)}}class Jr{computeDiff(e,t,n){var r;const i=new ti(e,t,{maxComputationTime:n.maxComputationTimeMs,shouldIgnoreTrimWhitespace:n.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),s=[];let o=null;for(const a of i.changes){let e,t;e=0===a.originalEndLineNumber?new $r(a.originalStartLineNumber+1,a.originalStartLineNumber+1):new $r(a.originalStartLineNumber,a.originalEndLineNumber+1),t=0===a.modifiedEndLineNumber?new $r(a.modifiedStartLineNumber+1,a.modifiedStartLineNumber+1):new $r(a.modifiedStartLineNumber,a.modifiedEndLineNumber+1);let n=new zr(e,t,null===(r=a.charChanges)||void 0===r?void 0:r.map((e=>new Gr(new nt(e.originalStartLineNumber,e.originalStartColumn,e.originalEndLineNumber,e.originalEndColumn),new nt(e.modifiedStartLineNumber,e.modifiedStartColumn,e.modifiedEndLineNumber,e.modifiedEndColumn)))));o&&(o.modified.endLineNumberExclusive!==n.modified.startLineNumber&&o.original.endLineNumberExclusive!==n.original.startLineNumber||(n=new zr(o.original.join(n.original),o.modified.join(n.modified),o.innerChanges&&n.innerChanges?o.innerChanges.concat(n.innerChanges):void 0),s.pop())),s.push(n),o=n}return Mr((()=>Or(s,((e,t)=>t.original.startLineNumber-e.original.endLineNumberExclusive==t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber)))),new Vr(s,[],i.quitEarly)}}function Xr(e,t,n,r){return new pe(e,t,n).ComputeDiff(r)}let Qr=class{constructor(e){const t=[],n=[];for(let r=0,i=e.length;r<i;r++)t[r]=ni(e[r],1),n[r]=ri(e[r],1);this.lines=e,this._startColumns=t,this._endColumns=n}getElements(){const e=[];for(let t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this._startColumns[t]-1,this._endColumns[t]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,t,n){const r=[],i=[],s=[];let o=0;for(let a=t;a<=n;a++){const t=this.lines[a],l=e?this._startColumns[a]:1,u=e?this._endColumns[a]:t.length+1;for(let e=l;e<u;e++)r[o]=t.charCodeAt(e-1),i[o]=a+1,s[o]=e,o++;!e&&a<n&&(r[o]=10,i[o]=a+1,s[o]=t.length+1,o++)}return new Zr(r,i,s)}};class Zr{constructor(e,t,n){this._charCodes=e,this._lineNumbers=t,this._columns=n}toString(){return"["+this._charCodes.map(((e,t)=>(10===e?"\\n":String.fromCharCode(e))+`-(${this._lineNumbers[t]},${this._columns[t]})`)).join(", ")+"]"}_assertIndex(e,t){if(e<0||e>=t.length)throw new Error("Illegal index")}getElements(){return this._charCodes}getStartLineNumber(e){return e>0&&e===this._lineNumbers.length?this.getEndLineNumber(e-1):(this._assertIndex(e,this._lineNumbers),this._lineNumbers[e])}getEndLineNumber(e){return-1===e?this.getStartLineNumber(e+1):(this._assertIndex(e,this._lineNumbers),10===this._charCodes[e]?this._lineNumbers[e]+1:this._lineNumbers[e])}getStartColumn(e){return e>0&&e===this._columns.length?this.getEndColumn(e-1):(this._assertIndex(e,this._columns),this._columns[e])}getEndColumn(e){return-1===e?this.getStartColumn(e+1):(this._assertIndex(e,this._columns),10===this._charCodes[e]?1:this._columns[e]+1)}}class Yr{constructor(e,t,n,r,i,s,o,a){this.originalStartLineNumber=e,this.originalStartColumn=t,this.originalEndLineNumber=n,this.originalEndColumn=r,this.modifiedStartLineNumber=i,this.modifiedStartColumn=s,this.modifiedEndLineNumber=o,this.modifiedEndColumn=a}static createFromDiffChange(e,t,n){const r=t.getStartLineNumber(e.originalStart),i=t.getStartColumn(e.originalStart),s=t.getEndLineNumber(e.originalStart+e.originalLength-1),o=t.getEndColumn(e.originalStart+e.originalLength-1),a=n.getStartLineNumber(e.modifiedStart),l=n.getStartColumn(e.modifiedStart),u=n.getEndLineNumber(e.modifiedStart+e.modifiedLength-1),c=n.getEndColumn(e.modifiedStart+e.modifiedLength-1);return new Yr(r,i,s,o,a,l,u,c)}}class ei{constructor(e,t,n,r,i){this.originalStartLineNumber=e,this.originalEndLineNumber=t,this.modifiedStartLineNumber=n,this.modifiedEndLineNumber=r,this.charChanges=i}static createFromDiffResult(e,t,n,r,i,s,o){let a,l,u,c,h;if(0===t.originalLength?(a=n.getStartLineNumber(t.originalStart)-1,l=0):(a=n.getStartLineNumber(t.originalStart),l=n.getEndLineNumber(t.originalStart+t.originalLength-1)),0===t.modifiedLength?(u=r.getStartLineNumber(t.modifiedStart)-1,c=0):(u=r.getStartLineNumber(t.modifiedStart),c=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1)),s&&t.originalLength>0&&t.originalLength<20&&t.modifiedLength>0&&t.modifiedLength<20&&i()){const s=n.createCharSequence(e,t.originalStart,t.originalStart+t.originalLength-1),a=r.createCharSequence(e,t.modifiedStart,t.modifiedStart+t.modifiedLength-1);if(s.getElements().length>0&&a.getElements().length>0){let e=Xr(s,a,i,!0).changes;o&&(e=function(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let r=1,i=e.length;r<i;r++){const i=e[r],s=i.originalStart-(n.originalStart+n.originalLength),o=i.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(s,o)<3?(n.originalLength=i.originalStart+i.originalLength-n.originalStart,n.modifiedLength=i.modifiedStart+i.modifiedLength-n.modifiedStart):(t.push(i),n=i)}return t}(e)),h=[];for(let t=0,n=e.length;t<n;t++)h.push(Yr.createFromDiffChange(e[t],s,a))}}return new ei(a,l,u,c,h)}}class ti{constructor(e,t,n){this.shouldComputeCharChanges=n.shouldComputeCharChanges,this.shouldPostProcessCharChanges=n.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=n.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=n.shouldMakePrettyDiff,this.originalLines=e,this.modifiedLines=t,this.original=new Qr(e),this.modified=new Qr(t),this.continueLineDiff=ii(n.maxComputationTime),this.continueCharDiff=ii(0===n.maxComputationTime?0:Math.min(n.maxComputationTime,5e3))}computeDiff(){if(1===this.original.lines.length&&0===this.original.lines[0].length)return 1===this.modified.lines.length&&0===this.modified.lines[0].length?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:void 0}]};if(1===this.modified.lines.length&&0===this.modified.lines[0].length)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};const e=Xr(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),t=e.changes,n=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){const e=[];for(let n=0,r=t.length;n<r;n++)e.push(ei.createFromDiffResult(this.shouldIgnoreTrimWhitespace,t[n],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:n,changes:e}}const r=[];let i=0,s=0;for(let o=-1,a=t.length;o<a;o++){const e=o+1<a?t[o+1]:null,n=e?e.originalStart:this.originalLines.length,l=e?e.modifiedStart:this.modifiedLines.length;for(;i<n&&s<l;){const e=this.originalLines[i],t=this.modifiedLines[s];if(e!==t){{let n=ni(e,1),o=ni(t,1);for(;n>1&&o>1;){if(e.charCodeAt(n-2)!==t.charCodeAt(o-2))break;n--,o--}(n>1||o>1)&&this._pushTrimWhitespaceCharChange(r,i+1,1,n,s+1,1,o)}{let n=ri(e,1),o=ri(t,1);const a=e.length+1,l=t.length+1;for(;n<a&&o<l;){if(e.charCodeAt(n-1)!==e.charCodeAt(o-1))break;n++,o++}(n<a||o<l)&&this._pushTrimWhitespaceCharChange(r,i+1,n,a,s+1,o,l)}}i++,s++}e&&(r.push(ei.createFromDiffResult(this.shouldIgnoreTrimWhitespace,e,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),i+=e.originalLength,s+=e.modifiedLength)}return{quitEarly:n,changes:r}}_pushTrimWhitespaceCharChange(e,t,n,r,i,s,o){if(this._mergeTrimWhitespaceCharChange(e,t,n,r,i,s,o))return;let a;this.shouldComputeCharChanges&&(a=[new Yr(t,n,t,r,i,s,i,o)]),e.push(new ei(t,t,i,i,a))}_mergeTrimWhitespaceCharChange(e,t,n,r,i,s,o){const a=e.length;if(0===a)return!1;const l=e[a-1];return 0!==l.originalEndLineNumber&&0!==l.modifiedEndLineNumber&&(l.originalEndLineNumber===t&&l.modifiedEndLineNumber===i?(this.shouldComputeCharChanges&&l.charChanges&&l.charChanges.push(new Yr(t,n,t,r,i,s,i,o)),!0):l.originalEndLineNumber+1===t&&l.modifiedEndLineNumber+1===i&&(l.originalEndLineNumber=t,l.modifiedEndLineNumber=i,this.shouldComputeCharChanges&&l.charChanges&&l.charChanges.push(new Yr(t,n,t,r,i,s,i,o)),!0))}}function ni(e,t){const n=function(e){for(let t=0,n=e.length;t<n;t++){const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}return-1}(e);return-1===n?t:n+1}function ri(e,t){const n=function(e,t=e.length-1){for(let n=t;n>=0;n--){const t=e.charCodeAt(n);if(32!==t&&9!==t)return n}return-1}(e);return-1===n?t:n+2}function ii(e){if(0===e)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}class si{static trivial(e,t){return new si([new oi(qr.ofLength(e.length),qr.ofLength(t.length))],!1)}static trivialTimedOut(e,t){return new si([new oi(qr.ofLength(e.length),qr.ofLength(t.length))],!0)}constructor(e,t){this.diffs=e,this.hitTimeout=t}}class oi{static invert(e,t){const n=[];return function(e,t){for(let n=0;n<=e.length;n++)t(0===n?void 0:e[n-1],n===e.length?void 0:e[n])}(e,((e,r)=>{n.push(oi.fromOffsetPairs(e?e.getEndExclusives():ai.zero,r?r.getStarts():new ai(t,(e?e.seq2Range.endExclusive-e.seq1Range.endExclusive:0)+t)))})),n}static fromOffsetPairs(e,t){return new oi(new qr(e.offset1,t.offset1),new qr(e.offset2,t.offset2))}constructor(e,t){this.seq1Range=e,this.seq2Range=t}swap(){return new oi(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(e){return new oi(this.seq1Range.join(e.seq1Range),this.seq2Range.join(e.seq2Range))}delta(e){return 0===e?this:new oi(this.seq1Range.delta(e),this.seq2Range.delta(e))}deltaStart(e){return 0===e?this:new oi(this.seq1Range.deltaStart(e),this.seq2Range.deltaStart(e))}deltaEnd(e){return 0===e?this:new oi(this.seq1Range.deltaEnd(e),this.seq2Range.deltaEnd(e))}intersect(e){const t=this.seq1Range.intersect(e.seq1Range),n=this.seq2Range.intersect(e.seq2Range);if(t&&n)return new oi(t,n)}getStarts(){return new ai(this.seq1Range.start,this.seq2Range.start)}getEndExclusives(){return new ai(this.seq1Range.endExclusive,this.seq2Range.endExclusive)}}class ai{constructor(e,t){this.offset1=e,this.offset2=t}toString(){return`${this.offset1} <-> ${this.offset2}`}delta(e){return 0===e?this:new ai(this.offset1+e,this.offset2+e)}equals(e){return this.offset1===e.offset1&&this.offset2===e.offset2}}ai.zero=new ai(0,0),ai.max=new ai(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER);class li{isValid(){return!0}}li.instance=new li;class ui{constructor(e){if(this.timeout=e,this.startTime=Date.now(),this.valid=!0,e<=0)throw new o("timeout must be positive")}isValid(){return!(Date.now()-this.startTime<this.timeout)&&this.valid&&(this.valid=!1),this.valid}}class ci{constructor(e,t){this.width=e,this.height=t,this.array=[],this.array=new Array(e*t)}get(e,t){return this.array[e+t*this.width]}set(e,t,n){this.array[e+t*this.width]=n}}function hi(e){return 32===e||9===e}class di{static getKey(e){let t=this.chrKeys.get(e);return void 0===t&&(t=this.chrKeys.size,this.chrKeys.set(e,t)),t}constructor(e,t,n){this.range=e,this.lines=t,this.source=n,this.histogram=[];let r=0;for(let i=e.startLineNumber-1;i<e.endLineNumberExclusive-1;i++){const e=t[i];for(let t=0;t<e.length;t++){r++;const n=e[t],i=di.getKey(n);this.histogram[i]=(this.histogram[i]||0)+1}r++;const n=di.getKey("\n");this.histogram[n]=(this.histogram[n]||0)+1}this.totalCount=r}computeSimilarity(e){var t,n;let r=0;const i=Math.max(this.histogram.length,e.histogram.length);for(let s=0;s<i;s++)r+=Math.abs((null!==(t=this.histogram[s])&&void 0!==t?t:0)-(null!==(n=e.histogram[s])&&void 0!==n?n:0));return 1-r/(this.totalCount+e.totalCount)}}di.chrKeys=new Map;class fi{compute(e,t,n=li.instance,r){if(0===e.length||0===t.length)return si.trivial(e,t);const i=new ci(e.length,t.length),s=new ci(e.length,t.length),o=new ci(e.length,t.length);for(let f=0;f<e.length;f++)for(let a=0;a<t.length;a++){if(!n.isValid())return si.trivialTimedOut(e,t);const l=0===f?0:i.get(f-1,a),u=0===a?0:i.get(f,a-1);let c;e.getElement(f)===t.getElement(a)?(c=0===f||0===a?0:i.get(f-1,a-1),f>0&&a>0&&3===s.get(f-1,a-1)&&(c+=o.get(f-1,a-1)),c+=r?r(f,a):1):c=-1;const h=Math.max(l,u,c);if(h===c){const e=f>0&&a>0?o.get(f-1,a-1):0;o.set(f,a,e+1),s.set(f,a,3)}else h===l?(o.set(f,a,0),s.set(f,a,1)):h===u&&(o.set(f,a,0),s.set(f,a,2));i.set(f,a,h)}const a=[];let l=e.length,u=t.length;function c(e,t){e+1===l&&t+1===u||a.push(new oi(new qr(e+1,l),new qr(t+1,u))),l=e,u=t}let h=e.length-1,d=t.length-1;for(;h>=0&&d>=0;)3===s.get(h,d)?(c(h,d),h--,d--):1===s.get(h,d)?h--:d--;return c(-1,-1),a.reverse(),new si(a,!1)}}class mi{compute(e,t,n=li.instance){if(0===e.length||0===t.length)return si.trivial(e,t);const r=e,i=t;function s(e,t){for(;e<r.length&&t<i.length&&r.getElement(e)===i.getElement(t);)e++,t++;return e}let o=0;const a=new pi;a.set(0,s(0,0));const l=new vi;l.set(0,0===a.get(0)?null:new gi(null,0,0,a.get(0)));let u=0;e:for(;;){if(o++,!n.isValid())return si.trivialTimedOut(r,i);const e=-Math.min(o,i.length+o%2),t=Math.min(o,r.length+o%2);for(u=e;u<=t;u+=2){const n=u===t?-1:a.get(u+1),o=u===e?-1:a.get(u-1)+1,c=Math.min(Math.max(n,o),r.length),h=c-u;if(c>r.length||h>i.length)continue;const d=s(c,h);a.set(u,d);const f=c===n?l.get(u+1):l.get(u-1);if(l.set(u,d!==c?new gi(f,c,h,d-c):f),a.get(u)===r.length&&a.get(u)-u===i.length)break e}}let c=l.get(u);const h=[];let d=r.length,f=i.length;for(;;){const e=c?c.x+c.length:0,t=c?c.y+c.length:0;if(e===d&&t===f||h.push(new oi(new qr(e,d),new qr(t,f))),!c)break;d=c.x,f=c.y,c=c.prev}return h.reverse(),new si(h,!1)}}class gi{constructor(e,t,n,r){this.prev=e,this.x=t,this.y=n,this.length=r}}class pi{constructor(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){if(e<0){if((e=-e-1)>=this.negativeArr.length){const e=this.negativeArr;this.negativeArr=new Int32Array(2*e.length),this.negativeArr.set(e)}this.negativeArr[e]=t}else{if(e>=this.positiveArr.length){const e=this.positiveArr;this.positiveArr=new Int32Array(2*e.length),this.positiveArr.set(e)}this.positiveArr[e]=t}}}class vi{constructor(){this.positiveArr=[],this.negativeArr=[]}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,t){e<0?(e=-e-1,this.negativeArr[e]=t):this.positiveArr[e]=t}}class bi{constructor(){this.map=new Map}add(e,t){let n=this.map.get(e);n||(n=new Set,this.map.set(e,n)),n.add(t)}delete(e,t){const n=this.map.get(e);n&&(n.delete(t),0===n.size&&this.map.delete(e))}forEach(e,t){const n=this.map.get(e);n&&n.forEach(t)}get(e){const t=this.map.get(e);return t||new Set}}class yi{constructor(e,t,n){this.lines=e,this.considerWhitespaceChanges=n,this.elements=[],this.firstCharOffsetByLine=[],this.additionalOffsetByLine=[];let r=!1;t.start>0&&t.endExclusive>=e.length&&(t=new qr(t.start-1,t.endExclusive),r=!0),this.lineRange=t,this.firstCharOffsetByLine[0]=0;for(let i=this.lineRange.start;i<this.lineRange.endExclusive;i++){let t=e[i],s=0;if(r)s=t.length,t="",r=!1;else if(!n){const e=t.trimStart();s=t.length-e.length,t=e.trimEnd()}this.additionalOffsetByLine.push(s);for(let e=0;e<t.length;e++)this.elements.push(t.charCodeAt(e));i<e.length-1&&(this.elements.push("\n".charCodeAt(0)),this.firstCharOffsetByLine[i-this.lineRange.start+1]=this.elements.length)}this.additionalOffsetByLine.push(0)}toString(){return`Slice: "${this.text}"`}get text(){return this.getText(new qr(0,this.length))}getText(e){return this.elements.slice(e.start,e.endExclusive).map((e=>String.fromCharCode(e))).join("")}getElement(e){return this.elements[e]}get length(){return this.elements.length}getBoundaryScore(e){const t=Si(e>0?this.elements[e-1]:-1),n=Si(e<this.elements.length?this.elements[e]:-1);if(7===t&&8===n)return 0;if(8===t)return 150;let r=0;return t!==n&&(r+=10,0===t&&1===n&&(r+=1)),r+=wi(t),r+=wi(n),r}translateOffset(e){if(this.lineRange.isEmpty)return new tt(this.lineRange.start+1,1);const t=jr(this.firstCharOffsetByLine,(t=>t<=e));return new tt(this.lineRange.start+t+1,e-this.firstCharOffsetByLine[t]+this.additionalOffsetByLine[t]+1)}translateRange(e){return nt.fromPositions(this.translateOffset(e.start),this.translateOffset(e.endExclusive))}findWordContaining(e){if(e<0||e>=this.elements.length)return;if(!Ci(this.elements[e]))return;let t=e;for(;t>0&&Ci(this.elements[t-1]);)t--;let n=e;for(;n<this.elements.length&&Ci(this.elements[n]);)n++;return new qr(t,n)}countLinesIn(e){return this.translateOffset(e.endExclusive).lineNumber-this.translateOffset(e.start).lineNumber}isStronglyEqual(e,t){return this.elements[e]===this.elements[t]}extendToFullLines(e){var t,n;const r=null!==(t=Kr(this.firstCharOffsetByLine,(t=>t<=e.start)))&&void 0!==t?t:0,i=null!==(n=function(e,t){const n=Br(e,t);return n===e.length?void 0:e[n]}(this.firstCharOffsetByLine,(t=>e.endExclusive<=t)))&&void 0!==n?n:this.elements.length;return new qr(r,i)}}function Ci(e){return e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57}const _i={0:0,1:0,2:0,3:10,4:2,5:30,6:3,7:10,8:10};function wi(e){return _i[e]}function Si(e){return 10===e?8:13===e?7:hi(e)?6:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:-1===e?3:44===e||59===e?5:4}function Li(e,t,n,r,i,s){let{moves:o,excludedChanges:a}=function(e,t,n,r){const i=[],s=e.filter((e=>e.modified.isEmpty&&e.original.length>=3)).map((e=>new di(e.original,t,e))),o=new Set(e.filter((e=>e.original.isEmpty&&e.modified.length>=3)).map((e=>new di(e.modified,n,e)))),a=new Set;for(const l of s){let e,t=-1;for(const n of o){const r=l.computeSimilarity(n);r>t&&(t=r,e=n)}if(t>.9&&e&&(o.delete(e),i.push(new Hr(l.range,e.range)),a.add(l.source),a.add(e.source)),!r.isValid())return{moves:i,excludedChanges:a}}return{moves:i,excludedChanges:a}}(e,t,n,s);if(!s.isValid())return[];const l=function(e,t,n,r,i,s){const o=[],a=new bi;for(const f of e)for(let e=f.original.startLineNumber;e<f.original.endLineNumberExclusive-2;e++){const n=`${t[e-1]}:${t[e+1-1]}:${t[e+2-1]}`;a.add(n,{range:new $r(e,e+3)})}const l=[];e.sort(st((e=>e.modified.startLineNumber),ot));for(const f of e){let e=[];for(let t=f.modified.startLineNumber;t<f.modified.endLineNumberExclusive-2;t++){const r=`${n[t-1]}:${n[t+1-1]}:${n[t+2-1]}`,i=new $r(t,t+3),s=[];a.forEach(r,(({range:t})=>{for(const r of e)if(r.originalLineRange.endLineNumberExclusive+1===t.endLineNumberExclusive&&r.modifiedLineRange.endLineNumberExclusive+1===i.endLineNumberExclusive)return r.originalLineRange=new $r(r.originalLineRange.startLineNumber,t.endLineNumberExclusive),r.modifiedLineRange=new $r(r.modifiedLineRange.startLineNumber,i.endLineNumberExclusive),void s.push(r);const n={modifiedLineRange:i,originalLineRange:t};l.push(n),s.push(n)})),e=s}if(!s.isValid())return[]}l.sort((u=st((e=>e.modifiedLineRange.length),ot),(e,t)=>-u(e,t)));var u;const c=new Wr,h=new Wr;for(const f of l){const e=f.modifiedLineRange.startLineNumber-f.originalLineRange.startLineNumber,t=c.subtractFrom(f.modifiedLineRange),n=h.subtractFrom(f.originalLineRange).getWithDelta(e),r=t.getIntersection(n);for(const i of r.ranges){if(i.length<3)continue;const t=i,n=i.delta(-e);o.push(new Hr(n,t)),c.addRange(t),h.addRange(n)}}o.sort(st((e=>e.original.startLineNumber),ot));const d=new Ur(e);for(let f=0;f<o.length;f++){const t=o[f],n=d.findLastMonotonous((e=>e.original.startLineNumber<=t.original.startLineNumber)),a=Kr(e,(e=>e.modified.startLineNumber<=t.modified.startLineNumber)),l=Math.max(t.original.startLineNumber-n.original.startLineNumber,t.modified.startLineNumber-a.modified.startLineNumber),u=d.findLastMonotonous((e=>e.original.startLineNumber<t.original.endLineNumberExclusive)),m=Kr(e,(e=>e.modified.startLineNumber<t.modified.endLineNumberExclusive)),g=Math.max(u.original.endLineNumberExclusive-t.original.endLineNumberExclusive,m.modified.endLineNumberExclusive-t.modified.endLineNumberExclusive);let p,v;for(p=0;p<l;p++){const e=t.original.startLineNumber-p-1,n=t.modified.startLineNumber-p-1;if(e>r.length||n>i.length)break;if(c.contains(n)||h.contains(e))break;if(!Ei(r[e-1],i[n-1],s))break}for(p>0&&(h.addRange(new $r(t.original.startLineNumber-p,t.original.startLineNumber)),c.addRange(new $r(t.modified.startLineNumber-p,t.modified.startLineNumber))),v=0;v<g;v++){const e=t.original.endLineNumberExclusive+v,n=t.modified.endLineNumberExclusive+v;if(e>r.length||n>i.length)break;if(c.contains(n)||h.contains(e))break;if(!Ei(r[e-1],i[n-1],s))break}v>0&&(h.addRange(new $r(t.original.endLineNumberExclusive,t.original.endLineNumberExclusive+v)),c.addRange(new $r(t.modified.endLineNumberExclusive,t.modified.endLineNumberExclusive+v))),(p>0||v>0)&&(o[f]=new Hr(new $r(t.original.startLineNumber-p,t.original.endLineNumberExclusive+v),new $r(t.modified.startLineNumber-p,t.modified.endLineNumberExclusive+v)))}return o}(e.filter((e=>!a.has(e))),r,i,t,n,s);return function(e,t){for(const n of t)e.push(n)}(o,l),o=function(e){if(0===e.length)return e;e.sort(st((e=>e.original.startLineNumber),ot));const t=[e[0]];for(let n=1;n<e.length;n++){const r=t[t.length-1],i=e[n],s=i.original.startLineNumber-r.original.endLineNumberExclusive,o=i.modified.startLineNumber-r.modified.endLineNumberExclusive;s>=0&&o>=0&&s+o<=2?t[t.length-1]=r.join(i):t.push(i)}return t}(o),o=o.filter((e=>{const n=e.original.toOffsetRange().slice(t).map((e=>e.trim()));return n.join("\n").length>=15&&function(e,t){let n=0;for(const r of e)t(r)&&n++;return n}(n,(e=>e.length>=2))>=2})),o=function(e,t){const n=new Ur(e);return t=t.filter((t=>(n.findLastMonotonous((e=>e.original.startLineNumber<t.original.endLineNumberExclusive))||new Hr(new $r(1,1),new $r(1,1)))!==Kr(e,(e=>e.modified.startLineNumber<t.modified.endLineNumberExclusive)))),t}(e,o),o}function Ei(e,t,n){if(e.trim()===t.trim())return!0;if(e.length>300&&t.length>300)return!1;const r=(new mi).compute(new yi([e],new qr(0,1),!1),new yi([t],new qr(0,1),!1),n);let i=0;const s=oi.invert(r.diffs,e.length);for(const a of s)a.seq1Range.forEach((t=>{hi(e.charCodeAt(t))||i++}));const o=function(t){let n=0;for(let r=0;r<e.length;r++)hi(t.charCodeAt(r))||n++;return n}(e.length>t.length?e:t);return i/o>.6&&o>10}function xi(e,t,n){let r=n;return r=Ni(e,t,r),r=Ni(e,t,r),r=function(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(let r=0;r<n.length;r++){const i=r>0?n[r-1]:void 0,s=n[r],o=r+1<n.length?n[r+1]:void 0,a=new qr(i?i.seq1Range.endExclusive+1:0,o?o.seq1Range.start-1:e.length),l=new qr(i?i.seq2Range.endExclusive+1:0,o?o.seq2Range.start-1:t.length);s.seq1Range.isEmpty?n[r]=Ai(s,e,t,a,l):s.seq2Range.isEmpty&&(n[r]=Ai(s.swap(),t,e,l,a).swap())}return n}(e,t,r),r}function Ni(e,t,n){if(0===n.length)return n;const r=[];r.push(n[0]);for(let s=1;s<n.length;s++){const i=r[r.length-1];let o=n[s];if(o.seq1Range.isEmpty||o.seq2Range.isEmpty){const n=o.seq1Range.start-i.seq1Range.endExclusive;let s;for(s=1;s<=n&&(e.getElement(o.seq1Range.start-s)===e.getElement(o.seq1Range.endExclusive-s)&&t.getElement(o.seq2Range.start-s)===t.getElement(o.seq2Range.endExclusive-s));s++);if(s--,s===n){r[r.length-1]=new oi(new qr(i.seq1Range.start,o.seq1Range.endExclusive-n),new qr(i.seq2Range.start,o.seq2Range.endExclusive-n));continue}o=o.delta(-s)}r.push(o)}const i=[];for(let s=0;s<r.length-1;s++){const n=r[s+1];let o=r[s];if(o.seq1Range.isEmpty||o.seq2Range.isEmpty){const i=n.seq1Range.start-o.seq1Range.endExclusive;let a;for(a=0;a<i&&(e.isStronglyEqual(o.seq1Range.start+a,o.seq1Range.endExclusive+a)&&t.isStronglyEqual(o.seq2Range.start+a,o.seq2Range.endExclusive+a));a++);if(a===i){r[s+1]=new oi(new qr(o.seq1Range.start+i,n.seq1Range.endExclusive),new qr(o.seq2Range.start+i,n.seq2Range.endExclusive));continue}a>0&&(o=o.delta(a))}i.push(o)}return r.length>0&&i.push(r[r.length-1]),i}function Ai(e,t,n,r,i){let s=1;for(;e.seq1Range.start-s>=r.start&&e.seq2Range.start-s>=i.start&&n.isStronglyEqual(e.seq2Range.start-s,e.seq2Range.endExclusive-s)&&s<100;)s++;s--;let o=0;for(;e.seq1Range.start+o<r.endExclusive&&e.seq2Range.endExclusive+o<i.endExclusive&&n.isStronglyEqual(e.seq2Range.start+o,e.seq2Range.endExclusive+o)&&o<100;)o++;if(0===s&&0===o)return e;let a=0,l=-1;for(let u=-s;u<=o;u++){const r=e.seq2Range.start+u,i=e.seq2Range.endExclusive+u,s=e.seq1Range.start+u,o=t.getBoundaryScore(s)+n.getBoundaryScore(r)+n.getBoundaryScore(i);o>l&&(l=o,a=u)}return e.delta(a)}class ki{constructor(e,t){this.trimmedHash=e,this.lines=t}getElement(e){return this.trimmedHash[e]}get length(){return this.trimmedHash.length}getBoundaryScore(e){return 1e3-((0===e?0:Ri(this.lines[e-1]))+(e===this.lines.length?0:Ri(this.lines[e])))}getText(e){return this.lines.slice(e.start,e.endExclusive).join("\n")}isStronglyEqual(e,t){return this.lines[e]===this.lines[t]}}function Ri(e){let t=0;for(;t<e.length&&(32===e.charCodeAt(t)||9===e.charCodeAt(t));)t++;return t}class Ti{constructor(){this.dynamicProgrammingDiffing=new fi,this.myersDiffingAlgorithm=new mi}computeDiff(e,t,n){if(e.length<=1&&function(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;if(e.length!==t.length)return!1;for(let r=0,i=e.length;r<i;r++)if(!n(e[r],t[r]))return!1;return!0}(e,t,((e,t)=>e===t)))return new Vr([],[],!1);if(1===e.length&&0===e[0].length||1===t.length&&0===t[0].length)return new Vr([new zr(new $r(1,e.length+1),new $r(1,t.length+1),[new Gr(new nt(1,1,e.length,e[0].length+1),new nt(1,1,t.length,t[0].length+1))])],[],!1);const r=0===n.maxComputationTimeMs?li.instance:new ui(n.maxComputationTimeMs),i=!n.ignoreTrimWhitespace,s=new Map;function o(e){let t=s.get(e);return void 0===t&&(t=s.size,s.set(e,t)),t}const a=e.map((e=>o(e.trim()))),l=t.map((e=>o(e.trim()))),u=new ki(a,e),c=new ki(l,t),h=(()=>u.length+c.length<1700?this.dynamicProgrammingDiffing.compute(u,c,r,((n,r)=>e[n]===t[r]?0===t[r].length?.1:1+Math.log(1+t[r].length):.99)):this.myersDiffingAlgorithm.compute(u,c))();let d=h.diffs,f=h.hitTimeout;d=xi(u,c,d),d=function(e,t,n){let r=n;if(0===r.length)return r;let i,s=0;do{i=!1;const t=[r[0]];for(let n=1;n<r.length;n++){let s=function(t,n){const r=new qr(a.seq1Range.endExclusive,o.seq1Range.start);return e.getText(r).replace(/\s/g,"").length<=4&&(t.seq1Range.length+t.seq2Range.length>5||n.seq1Range.length+n.seq2Range.length>5)};const o=r[n],a=t[t.length-1];s(a,o)?(i=!0,t[t.length-1]=t[t.length-1].join(o)):t.push(o)}r=t}while(s++<10&&i);return r}(u,0,d);const m=[],g=n=>{if(i)for(let s=0;s<n;s++){const n=p+s,o=v+s;if(e[n]!==t[o]){const s=this.refineDiff(e,t,new oi(new qr(n,n+1),new qr(o,o+1)),r,i);for(const e of s.mappings)m.push(e);s.hitTimeout&&(f=!0)}}};let p=0,v=0;for(const C of d){Mr((()=>C.seq1Range.start-p==C.seq2Range.start-v));g(C.seq1Range.start-p),p=C.seq1Range.endExclusive,v=C.seq2Range.endExclusive;const n=this.refineDiff(e,t,C,r,i);n.hitTimeout&&(f=!0);for(const e of n.mappings)m.push(e)}g(e.length-p);const b=Mi(m,e,t);let y=[];return n.computeMoves&&(y=this.computeMoves(b,e,t,a,l,r,i)),Mr((()=>{function n(e,t){if(e.lineNumber<1||e.lineNumber>t.length)return!1;const n=t[e.lineNumber-1];return!(e.column<1||e.column>n.length+1)}function r(e,t){return!(e.startLineNumber<1||e.startLineNumber>t.length+1)&&!(e.endLineNumberExclusive<1||e.endLineNumberExclusive>t.length+1)}for(const i of b){if(!i.innerChanges)return!1;for(const r of i.innerChanges){if(!(n(r.modifiedRange.getStartPosition(),t)&&n(r.modifiedRange.getEndPosition(),t)&&n(r.originalRange.getStartPosition(),e)&&n(r.originalRange.getEndPosition(),e)))return!1}if(!r(i.modified,t)||!r(i.original,e))return!1}return!0})),new Vr(b,y,f)}computeMoves(e,t,n,r,i,s,o){return Li(e,t,n,r,i,s).map((e=>{const r=Mi(this.refineDiff(t,n,new oi(e.original.toOffsetRange(),e.modified.toOffsetRange()),s,o).mappings,t,n,!0);return new Dr(e,r)}))}refineDiff(e,t,n,r,i){const s=new yi(e,n.seq1Range,i),o=new yi(t,n.seq2Range,i),a=s.length+o.length<500?this.dynamicProgrammingDiffing.compute(s,o,r):this.myersDiffingAlgorithm.compute(s,o,r);let l=a.diffs;l=xi(s,o,l),l=function(e,t,n){const r=oi.invert(n,e.length),i=[];let s=new ai(0,0);function o(n,o){if(n.offset1<s.offset1||n.offset2<s.offset2)return;const a=e.findWordContaining(n.offset1),l=t.findWordContaining(n.offset2);if(!a||!l)return;let u=new oi(a,l);const c=u.intersect(o);let h=c.seq1Range.length,d=c.seq2Range.length;for(;r.length>0;){const n=r[0];if(!n.seq1Range.intersects(a)&&!n.seq2Range.intersects(l))break;const i=e.findWordContaining(n.seq1Range.start),s=t.findWordContaining(n.seq2Range.start),o=new oi(i,s),c=o.intersect(n);if(h+=c.seq1Range.length,d+=c.seq2Range.length,u=u.join(o),!(u.seq1Range.endExclusive>=n.seq1Range.endExclusive))break;r.shift()}h+d<2*(u.seq1Range.length+u.seq2Range.length)/3&&i.push(u),s=u.getEndExclusives()}for(;r.length>0;){const e=r.shift();e.seq1Range.isEmpty||(o(e.getStarts(),e),o(e.getEndExclusives().delta(-1),e))}return function(e,t){const n=[];for(;e.length>0||t.length>0;){const r=e[0],i=t[0];let s;s=r&&(!i||r.seq1Range.start<i.seq1Range.start)?e.shift():t.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=s.seq1Range.start?n[n.length-1]=n[n.length-1].join(s):n.push(s)}return n}(n,i)}(s,o,l),l=function(e,t,n){const r=[];for(const i of n){const e=r[r.length-1];e&&(i.seq1Range.start-e.seq1Range.endExclusive<=2||i.seq2Range.start-e.seq2Range.endExclusive<=2)?r[r.length-1]=new oi(e.seq1Range.join(i.seq1Range),e.seq2Range.join(i.seq2Range)):r.push(i)}return r}(0,0,l),l=function(e,t,n){let r=n;if(0===r.length)return r;let i,s=0;do{i=!1;const n=[r[0]];for(let s=1;s<r.length;s++){let o=function(n,r){const i=new qr(l.seq1Range.endExclusive,a.seq1Range.start);if(e.countLinesIn(i)>5||i.length>500)return!1;const s=e.getText(i).trim();if(s.length>20||s.split(/\r\n|\r|\n/).length>1)return!1;const o=e.countLinesIn(n.seq1Range),u=n.seq1Range.length,c=t.countLinesIn(n.seq2Range),h=n.seq2Range.length,d=e.countLinesIn(r.seq1Range),f=r.seq1Range.length,m=t.countLinesIn(r.seq2Range),g=r.seq2Range.length;function p(e){return Math.min(e,130)}return Math.pow(Math.pow(p(40*o+u),1.5)+Math.pow(p(40*c+h),1.5),1.5)+Math.pow(Math.pow(p(40*d+f),1.5)+Math.pow(p(40*m+g),1.5),1.5)>74184.96480721243};const a=r[s],l=n[n.length-1];o(l,a)?(i=!0,n[n.length-1]=n[n.length-1].join(a)):n.push(a)}r=n}while(s++<10&&i);const o=[];return function(e,t){for(let n=0;n<e.length;n++)t(0===n?void 0:e[n-1],e[n],n+1===e.length?void 0:e[n+1])}(r,((t,n,r)=>{let i=n;function s(e){return e.length>0&&e.trim().length<=3&&n.seq1Range.length+n.seq2Range.length>100}const a=e.extendToFullLines(n.seq1Range),l=e.getText(new qr(a.start,n.seq1Range.start));s(l)&&(i=i.deltaStart(-l.length));const u=e.getText(new qr(n.seq1Range.endExclusive,a.endExclusive));s(u)&&(i=i.deltaEnd(u.length));const c=oi.fromOffsetPairs(t?t.getEndExclusives():ai.zero,r?r.getStarts():ai.max),h=i.intersect(c);o.length>0&&h.getStarts().equals(o[o.length-1].getEndExclusives())?o[o.length-1]=o[o.length-1].join(h):o.push(h)})),o}(s,o,l);return{mappings:l.map((e=>new Gr(s.translateRange(e.seq1Range),o.translateRange(e.seq2Range)))),hitTimeout:a.hitTimeout}}}function Mi(e,t,n,r=!1){const i=[];for(const s of function*(e,t){let n,r;for(const i of e)void 0!==r&&t(r,i)?n.push(i):(n&&(yield n),n=[i]),r=i;n&&(yield n)}(e.map((e=>function(e,t,n){let r=0,i=0;1===e.modifiedRange.endColumn&&1===e.originalRange.endColumn&&e.originalRange.startLineNumber+r<=e.originalRange.endLineNumber&&e.modifiedRange.startLineNumber+r<=e.modifiedRange.endLineNumber&&(i=-1);e.modifiedRange.startColumn-1>=n[e.modifiedRange.startLineNumber-1].length&&e.originalRange.startColumn-1>=t[e.originalRange.startLineNumber-1].length&&e.originalRange.startLineNumber<=e.originalRange.endLineNumber+i&&e.modifiedRange.startLineNumber<=e.modifiedRange.endLineNumber+i&&(r=1);const s=new $r(e.originalRange.startLineNumber+r,e.originalRange.endLineNumber+1+i),o=new $r(e.modifiedRange.startLineNumber+r,e.modifiedRange.endLineNumber+1+i);return new zr(s,o,[e])}(e,t,n))),((e,t)=>e.original.overlapOrTouch(t.original)||e.modified.overlapOrTouch(t.modified)))){const e=s[0],t=s[s.length-1];i.push(new zr(e.original.join(t.original),e.modified.join(t.modified),s.map((e=>e.innerChanges[0]))))}return Mr((()=>!(!r&&i.length>0&&i[0].original.startLineNumber!==i[0].modified.startLineNumber)&&Or(i,((e,t)=>t.original.startLineNumber-e.original.endLineNumberExclusive==t.modified.startLineNumber-e.modified.endLineNumberExclusive&&e.original.endLineNumberExclusive<t.original.startLineNumber&&e.modified.endLineNumberExclusive<t.modified.startLineNumber)))),i}const Oi=()=>new Jr,Ii=()=>new Ti;function Pi(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}class Fi{constructor(e,t,n,r=1){this._rgbaBrand=void 0,this.r=0|Math.min(255,Math.max(0,e)),this.g=0|Math.min(255,Math.max(0,t)),this.b=0|Math.min(255,Math.max(0,n)),this.a=Pi(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.r===t.r&&e.g===t.g&&e.b===t.b&&e.a===t.a}}class Vi{constructor(e,t,n,r){this._hslaBrand=void 0,this.h=0|Math.max(Math.min(360,e),0),this.s=Pi(Math.max(Math.min(1,t),0),3),this.l=Pi(Math.max(Math.min(1,n),0),3),this.a=Pi(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.l===t.l&&e.a===t.a}static fromRGBA(e){const t=e.r/255,n=e.g/255,r=e.b/255,i=e.a,s=Math.max(t,n,r),o=Math.min(t,n,r);let a=0,l=0;const u=(o+s)/2,c=s-o;if(c>0){switch(l=Math.min(u<=.5?c/(2*u):c/(2-2*u),1),s){case t:a=(n-r)/c+(n<r?6:0);break;case n:a=(r-t)/c+2;break;case r:a=(t-n)/c+4}a*=60,a=Math.round(a)}return new Vi(a,l,u,i)}static _hue2rgb(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}static toRGBA(e){const t=e.h/360,{s:n,l:r,a:i}=e;let s,o,a;if(0===n)s=o=a=r;else{const e=r<.5?r*(1+n):r+n-r*n,i=2*r-e;s=Vi._hue2rgb(i,e,t+1/3),o=Vi._hue2rgb(i,e,t),a=Vi._hue2rgb(i,e,t-1/3)}return new Fi(Math.round(255*s),Math.round(255*o),Math.round(255*a),i)}}class Di{constructor(e,t,n,r){this._hsvaBrand=void 0,this.h=0|Math.max(Math.min(360,e),0),this.s=Pi(Math.max(Math.min(1,t),0),3),this.v=Pi(Math.max(Math.min(1,n),0),3),this.a=Pi(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.h===t.h&&e.s===t.s&&e.v===t.v&&e.a===t.a}static fromRGBA(e){const t=e.r/255,n=e.g/255,r=e.b/255,i=Math.max(t,n,r),s=i-Math.min(t,n,r),o=0===i?0:s/i;let a;return a=0===s?0:i===t?((n-r)/s%6+6)%6:i===n?(r-t)/s+2:(t-n)/s+4,new Di(Math.round(60*a),o,i,e.a)}static toRGBA(e){const{h:t,s:n,v:r,a:i}=e,s=r*n,o=s*(1-Math.abs(t/60%2-1)),a=r-s;let[l,u,c]=[0,0,0];return t<60?(l=s,u=o):t<120?(l=o,u=s):t<180?(u=s,c=o):t<240?(u=o,c=s):t<300?(l=o,c=s):t<=360&&(l=s,c=o),l=Math.round(255*(l+a)),u=Math.round(255*(u+a)),c=Math.round(255*(c+a)),new Fi(l,u,c,i)}}let qi=class e{static fromHex(t){return e.Format.CSS.parseHex(t)||e.red}static equals(e,t){return!e&&!t||!(!e||!t)&&e.equals(t)}get hsla(){return this._hsla?this._hsla:Vi.fromRGBA(this.rgba)}get hsva(){return this._hsva?this._hsva:Di.fromRGBA(this.rgba)}constructor(e){if(!e)throw new Error("Color needs a value");if(e instanceof Fi)this.rgba=e;else if(e instanceof Vi)this._hsla=e,this.rgba=Vi.toRGBA(e);else{if(!(e instanceof Di))throw new Error("Invalid color ctor argument");this._hsva=e,this.rgba=Di.toRGBA(e)}}equals(e){return!!e&&Fi.equals(this.rgba,e.rgba)&&Vi.equals(this.hsla,e.hsla)&&Di.equals(this.hsva,e.hsva)}getRelativeLuminance(){return Pi(.2126*e._relativeLuminanceForComponent(this.rgba.r)+.7152*e._relativeLuminanceForComponent(this.rgba.g)+.0722*e._relativeLuminanceForComponent(this.rgba.b),4)}static _relativeLuminanceForComponent(e){const t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}isLighter(){return(299*this.rgba.r+587*this.rgba.g+114*this.rgba.b)/1e3>=128}isLighterThan(e){return this.getRelativeLuminance()>e.getRelativeLuminance()}isDarkerThan(e){return this.getRelativeLuminance()<e.getRelativeLuminance()}lighten(t){return new e(new Vi(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*t,this.hsla.a))}darken(t){return new e(new Vi(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*t,this.hsla.a))}transparent(t){const{r:n,g:r,b:i,a:s}=this.rgba;return new e(new Fi(n,r,i,s*t))}isTransparent(){return 0===this.rgba.a}isOpaque(){return 1===this.rgba.a}opposite(){return new e(new Fi(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}makeOpaque(t){if(this.isOpaque()||1!==t.rgba.a)return this;const{r:n,g:r,b:i,a:s}=this.rgba;return new e(new Fi(t.rgba.r-s*(t.rgba.r-n),t.rgba.g-s*(t.rgba.g-r),t.rgba.b-s*(t.rgba.b-i),1))}toString(){return this._toString||(this._toString=e.Format.CSS.format(this)),this._toString}static getLighterColor(e,t,n){if(e.isLighterThan(t))return e;n=n||.5;const r=e.getRelativeLuminance(),i=t.getRelativeLuminance();return n=n*(i-r)/i,e.lighten(n)}static getDarkerColor(e,t,n){if(e.isDarkerThan(t))return e;n=n||.5;const r=e.getRelativeLuminance();return n=n*(r-t.getRelativeLuminance())/r,e.darken(n)}};var Ki,ji;function Bi(e){const t=[];for(const n of e){const e=Number(n);(e||0===e&&""!==n.replace(/\s/g,""))&&t.push(e)}return t}function Ui(e,t,n,r){return{red:e/255,blue:n/255,green:t/255,alpha:r}}function $i(e,t){const n=t.index,r=t[0].length;if(!n)return;const i=e.positionAt(n);return{startLineNumber:i.lineNumber,startColumn:i.column,endLineNumber:i.lineNumber,endColumn:i.column+r}}function Wi(e,t){if(!e)return;const n=qi.Format.CSS.parseHex(t);return n?{range:e,color:Ui(n.rgba.r,n.rgba.g,n.rgba.b,n.rgba.a)}:void 0}function Hi(e,t,n){if(!e||1!==t.length)return;const r=Bi(t[0].values());return{range:e,color:Ui(r[0],r[1],r[2],n?r[3]:1)}}function zi(e,t,n){if(!e||1!==t.length)return;const r=Bi(t[0].values()),i=new qi(new Vi(r[0],r[1]/100,r[2]/100,n?r[3]:1));return{range:e,color:Ui(i.rgba.r,i.rgba.g,i.rgba.b,i.rgba.a)}}function Gi(e,t){return"string"==typeof e?[...e.matchAll(t)]:e.findMatches(t)}function Ji(e){return e&&"function"==typeof e.getValue&&"function"==typeof e.positionAt?function(e){const t=[],n=Gi(e,/\b(rgb|rgba|hsl|hsla)(\([0-9\s,.\%]*\))|(#)([A-Fa-f0-9]{3})\b|(#)([A-Fa-f0-9]{4})\b|(#)([A-Fa-f0-9]{6})\b|(#)([A-Fa-f0-9]{8})\b/gm);if(n.length>0)for(const r of n){const n=r.filter((e=>void 0!==e)),i=n[1],s=n[2];if(!s)continue;let o;if("rgb"===i){const t=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*\)$/gm;o=Hi($i(e,r),Gi(s,t),!1)}else if("rgba"===i){const t=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;o=Hi($i(e,r),Gi(s,t),!0)}else if("hsl"===i){const t=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*\)$/gm;o=zi($i(e,r),Gi(s,t),!1)}else if("hsla"===i){const t=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;o=zi($i(e,r),Gi(s,t),!0)}else"#"===i&&(o=Wi($i(e,r),i+s));o&&t.push(o)}return t}(e):[]}qi.white=new qi(new Fi(255,255,255,1)),qi.black=new qi(new Fi(0,0,0,1)),qi.red=new qi(new Fi(255,0,0,1)),qi.blue=new qi(new Fi(0,0,255,1)),qi.green=new qi(new Fi(0,255,0,1)),qi.cyan=new qi(new Fi(0,255,255,1)),qi.lightgrey=new qi(new Fi(211,211,211,1)),qi.transparent=new qi(new Fi(0,0,0,0)),Ki=qi||(qi={}),function(e){function t(e){const t=e.toString(16);return 2!==t.length?"0"+t:t}function n(e){switch(e){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:case 65:return 10;case 98:case 66:return 11;case 99:case 67:return 12;case 100:case 68:return 13;case 101:case 69:return 14;case 102:case 70:return 15}return 0}e.formatRGB=function(e){return 1===e.rgba.a?`rgb(${e.rgba.r}, ${e.rgba.g}, ${e.rgba.b})`:Ki.Format.CSS.formatRGBA(e)},e.formatRGBA=function(e){return`rgba(${e.rgba.r}, ${e.rgba.g}, ${e.rgba.b}, ${+e.rgba.a.toFixed(2)})`},e.formatHSL=function(e){return 1===e.hsla.a?`hsl(${e.hsla.h}, ${(100*e.hsla.s).toFixed(2)}%, ${(100*e.hsla.l).toFixed(2)}%)`:Ki.Format.CSS.formatHSLA(e)},e.formatHSLA=function(e){return`hsla(${e.hsla.h}, ${(100*e.hsla.s).toFixed(2)}%, ${(100*e.hsla.l).toFixed(2)}%, ${e.hsla.a.toFixed(2)})`},e.formatHex=function(e){return`#${t(e.rgba.r)}${t(e.rgba.g)}${t(e.rgba.b)}`},e.formatHexA=function(e,n=!1){return n&&1===e.rgba.a?Ki.Format.CSS.formatHex(e):`#${t(e.rgba.r)}${t(e.rgba.g)}${t(e.rgba.b)}${t(Math.round(255*e.rgba.a))}`},e.format=function(e){return e.isOpaque()?Ki.Format.CSS.formatHex(e):Ki.Format.CSS.formatRGBA(e)},e.parseHex=function(e){const t=e.length;if(0===t)return null;if(35!==e.charCodeAt(0))return null;if(7===t){const t=16*n(e.charCodeAt(1))+n(e.charCodeAt(2)),r=16*n(e.charCodeAt(3))+n(e.charCodeAt(4)),i=16*n(e.charCodeAt(5))+n(e.charCodeAt(6));return new Ki(new Fi(t,r,i,1))}if(9===t){const t=16*n(e.charCodeAt(1))+n(e.charCodeAt(2)),r=16*n(e.charCodeAt(3))+n(e.charCodeAt(4)),i=16*n(e.charCodeAt(5))+n(e.charCodeAt(6)),s=16*n(e.charCodeAt(7))+n(e.charCodeAt(8));return new Ki(new Fi(t,r,i,s/255))}if(4===t){const t=n(e.charCodeAt(1)),r=n(e.charCodeAt(2)),i=n(e.charCodeAt(3));return new Ki(new Fi(16*t+t,16*r+r,16*i+i))}if(5===t){const t=n(e.charCodeAt(1)),r=n(e.charCodeAt(2)),i=n(e.charCodeAt(3)),s=n(e.charCodeAt(4));return new Ki(new Fi(16*t+t,16*r+r,16*i+i,(16*s+s)/255))}return null}}((ji=Ki.Format||(Ki.Format={})).CSS||(ji.CSS={}));class Xi extends ht{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}findMatches(e){const t=[];for(let n=0;n<this._lines.length;n++){const r=this._lines[n],i=this.offsetAt(new tt(n+1,1)),s=r.matchAll(e);for(const e of s)(e.index||0===e.index)&&(e.index=e.index+i),t.push(e)}return t}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(e){return this._lines[e-1]}getWordAtPosition(e,t){const n=gt(e.column,ft(t),this._lines[e.lineNumber-1],0);return n?new nt(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}words(e){const t=this._lines,n=this._wordenize.bind(this);let r=0,i="",s=0,o=[];return{*[Symbol.iterator](){for(;;)if(s<o.length){const e=i.substring(o[s].start,o[s].end);s+=1,yield e}else{if(!(r<t.length))break;i=t[r],o=n(i,e),s=0,r+=1}}}}getLineWords(e,t){const n=this._lines[e-1],r=this._wordenize(n,t),i=[];for(const s of r)i.push({word:n.substring(s.start,s.end),startColumn:s.start+1,endColumn:s.end+1});return i}_wordenize(e,t){const n=[];let r;for(t.lastIndex=0;(r=t.exec(e))&&0!==r[0].length;)n.push({start:r.index,end:r.index+r[0].length});return n}getValueInRange(e){if((e=this._validateRange(e)).startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);const t=this._eol,n=e.startLineNumber-1,r=e.endLineNumber-1,i=[];i.push(this._lines[n].substring(e.startColumn-1));for(let s=n+1;s<r;s++)i.push(this._lines[s]);return i.push(this._lines[r].substring(0,e.endColumn-1)),i.join(t)}offsetAt(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();const t=this._lineStarts.getIndexOf(e),n=this._lines[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}_validateRange(e){const t=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),n=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}_validatePosition(e){if(!tt.isIPosition(e))throw new Error("bad position");let{lineNumber:t,column:n}=e,r=!1;if(t<1)t=1,n=1,r=!0;else if(t>this._lines.length)t=this._lines.length,n=this._lines[t-1].length+1,r=!0;else{const e=this._lines[t-1].length+1;n<1?(n=1,r=!0):n>e&&(n=e,r=!0)}return r?{lineNumber:t,column:n}:e}}class Qi{constructor(e,t){this._host=e,this._models=Object.create(null),this._foreignModuleFactory=t,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(e){return this._models[e]}_getModels(){const e=[];return Object.keys(this._models).forEach((t=>e.push(this._models[t]))),e}acceptNewModel(e){this._models[e.url]=new Xi($e.parse(e.url),e.lines,e.EOL,e.versionId)}acceptModelChanged(e,t){if(!this._models[e])return;this._models[e].onEvents(t)}acceptRemovedModel(e){this._models[e]&&delete this._models[e]}async computeUnicodeHighlights(e,t,n){const r=this._getModel(e);return r?Ir.computeUnicodeHighlights(r,t,n):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}}async computeDiff(e,t,n,r){const i=this._getModel(e),s=this._getModel(t);if(!i||!s)return null;return Qi.computeDiff(i,s,n,r)}static computeDiff(e,t,n,r){const i="advanced"===r?Ii():Oi(),s=e.getLinesContent(),o=t.getLinesContent(),a=i.computeDiff(s,o,n);function l(e){return e.map((e=>{var t;return[e.original.startLineNumber,e.original.endLineNumberExclusive,e.modified.startLineNumber,e.modified.endLineNumberExclusive,null===(t=e.innerChanges)||void 0===t?void 0:t.map((e=>[e.originalRange.startLineNumber,e.originalRange.startColumn,e.originalRange.endLineNumber,e.originalRange.endColumn,e.modifiedRange.startLineNumber,e.modifiedRange.startColumn,e.modifiedRange.endLineNumber,e.modifiedRange.endColumn]))]}))}return{identical:!(a.changes.length>0)&&this._modelsAreIdentical(e,t),quitEarly:a.hitTimeout,changes:l(a.changes),moves:a.moves.map((e=>[e.lineRangeMapping.original.startLineNumber,e.lineRangeMapping.original.endLineNumberExclusive,e.lineRangeMapping.modified.startLineNumber,e.lineRangeMapping.modified.endLineNumberExclusive,l(e.changes)]))}}static _modelsAreIdentical(e,t){const n=e.getLineCount();if(n!==t.getLineCount())return!1;for(let r=1;r<=n;r++){if(e.getLineContent(r)!==t.getLineContent(r))return!1}return!0}async computeMoreMinimalEdits(e,t,n){const r=this._getModel(e);if(!r)return t;const i=[];let s;t=t.slice(0).sort(((e,t)=>{if(e.range&&t.range)return nt.compareRangesUsingStarts(e.range,t.range);return(e.range?0:1)-(t.range?0:1)}));let o=0;for(let a=1;a<t.length;a++)nt.getEndPosition(t[o].range).equals(nt.getStartPosition(t[a].range))?(t[o].range=nt.fromPositions(nt.getStartPosition(t[o].range),nt.getEndPosition(t[a].range)),t[o].text+=t[a].text):(o++,t[o]=t[a]);t.length=o+1;for(let{range:a,text:l,eol:u}of t){if("number"==typeof u&&(s=u),nt.isEmpty(a)&&!l)continue;const e=r.getValueInRange(a);if(l=l.replace(/\r\n|\n|\r/g,r.eol),e===l)continue;if(Math.max(l.length,e.length)>Qi._diffLimit){i.push({range:a,text:l});continue}const t=de(e,l,n),o=r.offsetAt(nt.lift(a).getStartPosition());for(const n of t){const e=r.positionAt(o+n.originalStart),t=r.positionAt(o+n.originalStart+n.originalLength),s={text:l.substr(n.modifiedStart,n.modifiedLength),range:{startLineNumber:e.lineNumber,startColumn:e.column,endLineNumber:t.lineNumber,endColumn:t.column}};r.getValueInRange(s.range)!==s.text&&i.push(s)}}return"number"==typeof s&&i.push({eol:s,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i}async computeLinks(e){const t=this._getModel(e);return t?function(e){return e&&"function"==typeof e.getLineCount&&"function"==typeof e.getLineContent?wt.computeLinks(e):[]}(t):null}async computeDefaultDocumentColors(e){const t=this._getModel(e);return t?Ji(t):null}async textualSuggest(e,t,n,r){const i=new p,s=new RegExp(n,r),o=new Set;e:for(const a of e){const e=this._getModel(a);if(e)for(const n of e.words(s))if(n!==t&&isNaN(Number(n))&&(o.add(n),o.size>Qi._suggestionsLimit))break e}return{words:Array.from(o),duration:i.elapsed()}}async computeWordRanges(e,t,n,r){const i=this._getModel(e);if(!i)return Object.create(null);const s=new RegExp(n,r),o=Object.create(null);for(let a=t.startLineNumber;a<t.endLineNumber;a++){const e=i.getLineWords(a,s);for(const t of e){if(!isNaN(Number(t.word)))continue;let e=o[t.word];e||(e=[],o[t.word]=e),e.push({startLineNumber:a,startColumn:t.startColumn,endLineNumber:a,endColumn:t.endColumn})}}return o}async navigateValueSet(e,t,n,r,i){const s=this._getModel(e);if(!s)return null;const o=new RegExp(r,i);t.startColumn===t.endColumn&&(t={startLineNumber:t.startLineNumber,startColumn:t.startColumn,endLineNumber:t.endLineNumber,endColumn:t.endColumn+1});const a=s.getValueInRange(t),l=s.getWordAtPosition({lineNumber:t.startLineNumber,column:t.startColumn},o);if(!l)return null;const u=s.getValueInRange(l);return St.INSTANCE.navigateValueSet(t,a,l,u,n)}loadForeignModule(e,t,n){const r=function(e,t){const n=e=>function(){const n=Array.prototype.slice.call(arguments,0);return t(e,n)},r={};for(const i of e)r[i]=n(i);return r}(n,((e,t)=>this._host.fhr(e,t))),i={host:r,getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(i,t),Promise.resolve(L(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}fmr(e,t){if(!this._foreignModule||"function"!=typeof this._foreignModule[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,t))}catch(Bl){return Promise.reject(Bl)}}}Qi._diffLimit=1e5,Qi._suggestionsLimit=1e4,"function"==typeof importScripts&&(globalThis.monaco={editor:void 0,languages:void 0,CancellationTokenSource:At,Emitter:w,KeyCode:Vn,KeyMod:Ar,Position:tt,Range:nt,Selection:Dt,SelectionDirection:ar,MarkerSeverity:qn,MarkerTag:jn,Uri:$e,Token:Ut});let Zi=!1;function Yi(e){if(Zi)return;Zi=!0;const t=new ae((e=>{globalThis.postMessage(e)}),(t=>new Qi(t,e)));globalThis.onmessage=e=>{t.onmessage(e.data)}}function es(e,t){void 0===t&&(t=!1);var n=e.length,r=0,i="",s=0,o=16,a=0,l=0,u=0,c=0,h=0;function d(t,n){for(var i=0,s=0;i<t;){var o=e.charCodeAt(r);if(o>=48&&o<=57)s=16*s+o-48;else if(o>=65&&o<=70)s=16*s+o-65+10;else{if(!(o>=97&&o<=102))break;s=16*s+o-97+10}r++,i++}return i<t&&(s=-1),s}function f(){if(i="",h=0,s=r,l=a,c=u,r>=n)return s=n,o=17;var t=e.charCodeAt(r);if(ts(t)){do{r++,i+=String.fromCharCode(t),t=e.charCodeAt(r)}while(ts(t));return o=15}if(ns(t))return r++,i+=String.fromCharCode(t),13===t&&10===e.charCodeAt(r)&&(r++,i+="\n"),a++,u=r,o=14;switch(t){case 123:return r++,o=1;case 125:return r++,o=2;case 91:return r++,o=3;case 93:return r++,o=4;case 58:return r++,o=6;case 44:return r++,o=5;case 34:return r++,i=function(){for(var t="",i=r;;){if(r>=n){t+=e.substring(i,r),h=2;break}var s=e.charCodeAt(r);if(34===s){t+=e.substring(i,r),r++;break}if(92!==s){if(s>=0&&s<=31){if(ns(s)){t+=e.substring(i,r),h=2;break}h=6}r++}else{if(t+=e.substring(i,r),++r>=n){h=2;break}switch(e.charCodeAt(r++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:var o=d(4);o>=0?t+=String.fromCharCode(o):h=4;break;default:h=5}i=r}}return t}(),o=10;case 47:var f=r-1;if(47===e.charCodeAt(r+1)){for(r+=2;r<n&&!ns(e.charCodeAt(r));)r++;return i=e.substring(f,r),o=12}if(42===e.charCodeAt(r+1)){r+=2;for(var g=n-1,p=!1;r<g;){var v=e.charCodeAt(r);if(42===v&&47===e.charCodeAt(r+1)){r+=2,p=!0;break}r++,ns(v)&&(13===v&&10===e.charCodeAt(r)&&r++,a++,u=r)}return p||(r++,h=1),i=e.substring(f,r),o=13}return i+=String.fromCharCode(t),r++,o=16;case 45:if(i+=String.fromCharCode(t),++r===n||!rs(e.charCodeAt(r)))return o=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return i+=function(){var t=r;if(48===e.charCodeAt(r))r++;else for(r++;r<e.length&&rs(e.charCodeAt(r));)r++;if(r<e.length&&46===e.charCodeAt(r)){if(!(++r<e.length&&rs(e.charCodeAt(r))))return h=3,e.substring(t,r);for(r++;r<e.length&&rs(e.charCodeAt(r));)r++}var n=r;if(r<e.length&&(69===e.charCodeAt(r)||101===e.charCodeAt(r)))if((++r<e.length&&43===e.charCodeAt(r)||45===e.charCodeAt(r))&&r++,r<e.length&&rs(e.charCodeAt(r))){for(r++;r<e.length&&rs(e.charCodeAt(r));)r++;n=r}else h=3;return e.substring(t,n)}(),o=11;default:for(;r<n&&m(t);)r++,t=e.charCodeAt(r);if(s!==r){switch(i=e.substring(s,r)){case"true":return o=8;case"false":return o=9;case"null":return o=7}return o=16}return i+=String.fromCharCode(t),r++,o=16}}function m(e){if(ts(e)||ns(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){r=e,i="",s=0,o=16,h=0},getPosition:function(){return r},scan:t?function(){var e;do{e=f()}while(e>=12&&e<=15);return e}:f,getToken:function(){return o},getTokenValue:function(){return i},getTokenOffset:function(){return s},getTokenLength:function(){return r-s},getTokenStartLine:function(){return l},getTokenStartCharacter:function(){return s-c},getTokenError:function(){return h}}}function ts(e){return 32===e||9===e||11===e||12===e||160===e||5760===e||e>=8192&&e<=8203||8239===e||8287===e||12288===e||65279===e}function ns(e){return 10===e||13===e||8232===e||8233===e}function rs(e){return e>=48&&e<=57}function is(e,t,n){var r,i,s,o,a;if(t){for(o=t.offset,a=o+t.length,s=o;s>0&&!os(e,s-1);)s--;for(var l=a;l<e.length&&!os(e,l);)l++;i=e.substring(s,l),r=function(e,t){var n=0,r=0,i=t.tabSize||4;for(;n<e.length;){var s=e.charAt(n);if(" "===s)r++;else{if("\t"!==s)break;r+=i}n++}return Math.floor(r/i)}(i,n)}else i=e,r=0,s=0,o=0,a=e.length;var u,c=function(e,t){for(var n=0;n<t.length;n++){var r=t.charAt(n);if("\r"===r)return n+1<t.length&&"\n"===t.charAt(n+1)?"\r\n":"\r";if("\n"===r)return"\n"}return e&&e.eol||"\n"}(n,e),h=!1,d=0;u=n.insertSpaces?ss(" ",n.tabSize||4):"\t";var f=es(i,!1),m=!1;function g(){return c+ss(u,r+d)}function p(){var e=f.scan();for(h=!1;15===e||14===e;)h=h||14===e,e=f.scan();return m=16===e||0!==f.getTokenError(),e}var v=[];function b(n,r,i){m||t&&!(r<a&&i>o)||e.substring(r,i)===n||v.push({offset:r,length:i-r,content:n})}var y=p();if(17!==y){var C=f.getTokenOffset()+s;b(ss(u,r),s,C)}for(;17!==y;){for(var _=f.getTokenOffset()+f.getTokenLength()+s,w=p(),S="",L=!1;!h&&(12===w||13===w);){b(" ",_,f.getTokenOffset()+s),_=f.getTokenOffset()+f.getTokenLength()+s,S=(L=12===w)?g():"",w=p()}if(2===w)1!==y&&(d--,S=g());else if(4===w)3!==y&&(d--,S=g());else{switch(y){case 3:case 1:d++,S=g();break;case 5:case 12:S=g();break;case 13:h?S=g():L||(S=" ");break;case 6:L||(S=" ");break;case 10:if(6===w){L||(S="");break}case 7:case 8:case 9:case 11:case 2:case 4:12===w||13===w?L||(S=" "):5!==w&&17!==w&&(m=!0);break;case 16:m=!0}!h||12!==w&&13!==w||(S=g())}17===w&&(S=n.insertFinalNewline?c:""),b(S,_,f.getTokenOffset()+s),y=w}return v}function ss(e,t){for(var n="",r=0;r<t;r++)n+=e;return n}function os(e,t){return-1!=="\r\n".indexOf(e.charAt(t))}var as;globalThis.onmessage=e=>{Zi||Yi(null)},(as||(as={})).DEFAULT={allowTrailingComma:!1};var ls,us,cs,hs,ds,fs,ms,gs,ps,vs,bs,ys,Cs,_s,ws,Ss,Ls,Es,xs,Ns,As,ks,Rs,Ts,Ms,Os,Is,Ps,Fs,Vs,Ds,qs,Ks,js,Bs,Us,$s,Ws,Hs,zs,Gs,Js,Xs,Qs,Zs,Ys,eo,to=es,no=function(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=as.DEFAULT);var r=null,i=[],s=[];function o(e){Array.isArray(i)?i.push(e):null!==r&&(i[r]=e)}return function(e,t,n){void 0===n&&(n=as.DEFAULT);var r=es(e,!1);function i(e){return e?function(){return e(r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter())}:function(){return!0}}function s(e){return e?function(t){return e(t,r.getTokenOffset(),r.getTokenLength(),r.getTokenStartLine(),r.getTokenStartCharacter())}:function(){return!0}}var o=i(t.onObjectBegin),a=s(t.onObjectProperty),l=i(t.onObjectEnd),u=i(t.onArrayBegin),c=i(t.onArrayEnd),h=s(t.onLiteralValue),d=s(t.onSeparator),f=i(t.onComment),m=s(t.onError),g=n&&n.disallowComments,p=n&&n.allowTrailingComma;function v(){for(;;){var e=r.scan();switch(r.getTokenError()){case 4:b(14);break;case 5:b(15);break;case 3:b(13);break;case 1:g||b(11);break;case 2:b(12);break;case 6:b(16)}switch(e){case 12:case 13:g?b(10):f();break;case 16:b(1);break;case 15:case 14:break;default:return e}}}function b(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=[]),m(e),t.length+n.length>0)for(var i=r.getToken();17!==i;){if(-1!==t.indexOf(i)){v();break}if(-1!==n.indexOf(i))break;i=v()}}function y(e){var t=r.getTokenValue();return e?h(t):a(t),v(),!0}function C(){switch(r.getToken()){case 11:var e=r.getTokenValue(),t=Number(e);isNaN(t)&&(b(2),t=0),h(t);break;case 7:h(null);break;case 8:h(!0);break;case 9:h(!1);break;default:return!1}return v(),!0}function _(){return 10!==r.getToken()?(b(3,[],[2,5]),!1):(y(!1),6===r.getToken()?(d(":"),v(),L()||b(4,[],[2,5])):b(5,[],[2,5]),!0)}function w(){o(),v();for(var e=!1;2!==r.getToken()&&17!==r.getToken();){if(5===r.getToken()){if(e||b(4,[],[]),d(","),v(),2===r.getToken()&&p)break}else e&&b(6,[],[]);_()||b(4,[],[2,5]),e=!0}return l(),2!==r.getToken()?b(7,[2],[]):v(),!0}function S(){u(),v();for(var e=!1;4!==r.getToken()&&17!==r.getToken();){if(5===r.getToken()){if(e||b(4,[],[]),d(","),v(),4===r.getToken()&&p)break}else e&&b(6,[],[]);L()||b(4,[],[4,5]),e=!0}return c(),4!==r.getToken()?b(8,[4],[]):v(),!0}function L(){switch(r.getToken()){case 3:return S();case 1:return w();case 10:return y(!0);default:return C()}}if(v(),17===r.getToken())return!!n.allowEmptyContent||(b(4,[],[]),!1);if(!L())return b(4,[],[]),!1;17!==r.getToken()&&b(9,[],[])}(e,{onObjectBegin:function(){var e={};o(e),s.push(i),i=e,r=null},onObjectProperty:function(e){r=e},onObjectEnd:function(){i=s.pop()},onArrayBegin:function(){var e=[];o(e),s.push(i),i=e,r=null},onArrayEnd:function(){i=s.pop()},onLiteralValue:o,onError:function(e,n,r){t.push({error:e,offset:n,length:r})}},n),i[0]},ro=function e(t,n,r){if(void 0===r&&(r=!1),function(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}(t,n,r)){var i=t.children;if(Array.isArray(i))for(var s=0;s<i.length&&i[s].offset<=n;s++){var o=e(i[s],n,r);if(o)return o}return t}},io=function e(t){if(!t.parent||!t.parent.children)return[];var n=e(t.parent);if("property"===t.parent.type){var r=t.parent.children[0].value;n.push(r)}else if("array"===t.parent.type){var i=t.parent.children.indexOf(t);-1!==i&&n.push(i)}return n},so=function e(t){switch(t.type){case"array":return t.children.map(e);case"object":for(var n=Object.create(null),r=0,i=t.children;r<i.length;r++){var s=i[r],o=s.children[1];o&&(n[s.children[0].value]=e(o))}return n;case"null":case"string":case"number":case"boolean":return t.value;default:return}};function oo(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if(typeof e!=typeof t)return!1;if("object"!=typeof e)return!1;if(Array.isArray(e)!==Array.isArray(t))return!1;var n,r;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(n=0;n<e.length;n++)if(!oo(e[n],t[n]))return!1}else{var i=[];for(r in e)i.push(r);i.sort();var s=[];for(r in t)s.push(r);if(s.sort(),!oo(i,s))return!1;for(n=0;n<i.length;n++)if(!oo(e[i[n]],t[i[n]]))return!1}return!0}function ao(e){return"number"==typeof e}function lo(e){return void 0!==e}function uo(e){return"boolean"==typeof e}function co(e,t){var n=e.length-t.length;return n>0?e.lastIndexOf(t)===n:0===n&&e===t}function ho(e){var t="";(function(e,t){if(e.length<t.length)return!1;for(var n=0;n<t.length;n++)if(e[n]!==t[n])return!1;return!0})(e,"(?i)")&&(e=e.substring(4),t="i");try{return new RegExp(e,t+"u")}catch(Bl){try{return new RegExp(e,t)}catch(n){return}}}(us=ls||(ls={})).MIN_VALUE=-2147483648,us.MAX_VALUE=2147483647,(hs=cs||(cs={})).MIN_VALUE=0,hs.MAX_VALUE=2147483647,(fs=ds||(ds={})).create=function(e,t){return e===Number.MAX_VALUE&&(e=cs.MAX_VALUE),t===Number.MAX_VALUE&&(t=cs.MAX_VALUE),{line:e,character:t}},fs.is=function(e){var t=e;return ua.objectLiteral(t)&&ua.uinteger(t.line)&&ua.uinteger(t.character)},(gs=ms||(ms={})).create=function(e,t,n,r){if(ua.uinteger(e)&&ua.uinteger(t)&&ua.uinteger(n)&&ua.uinteger(r))return{start:ds.create(e,t),end:ds.create(n,r)};if(ds.is(e)&&ds.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments["+e+", "+t+", "+n+", "+r+"]")},gs.is=function(e){var t=e;return ua.objectLiteral(t)&&ds.is(t.start)&&ds.is(t.end)},(vs=ps||(ps={})).create=function(e,t){return{uri:e,range:t}},vs.is=function(e){var t=e;return ua.defined(t)&&ms.is(t.range)&&(ua.string(t.uri)||ua.undefined(t.uri))},(ys=bs||(bs={})).create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},ys.is=function(e){var t=e;return ua.defined(t)&&ms.is(t.targetRange)&&ua.string(t.targetUri)&&(ms.is(t.targetSelectionRange)||ua.undefined(t.targetSelectionRange))&&(ms.is(t.originSelectionRange)||ua.undefined(t.originSelectionRange))},function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){var t=e;return ua.numberRange(t.red,0,1)&&ua.numberRange(t.green,0,1)&&ua.numberRange(t.blue,0,1)&&ua.numberRange(t.alpha,0,1)}}(Cs||(Cs={})),(ws=_s||(_s={})).create=function(e,t){return{range:e,color:t}},ws.is=function(e){var t=e;return ms.is(t.range)&&Cs.is(t.color)},(Ls=Ss||(Ss={})).create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},Ls.is=function(e){var t=e;return ua.string(t.label)&&(ua.undefined(t.textEdit)||qs.is(t))&&(ua.undefined(t.additionalTextEdits)||ua.typedArray(t.additionalTextEdits,qs.is))},(xs=Es||(Es={})).Comment="comment",xs.Imports="imports",xs.Region="region",(As=Ns||(Ns={})).create=function(e,t,n,r,i){var s={startLine:e,endLine:t};return ua.defined(n)&&(s.startCharacter=n),ua.defined(r)&&(s.endCharacter=r),ua.defined(i)&&(s.kind=i),s},As.is=function(e){var t=e;return ua.uinteger(t.startLine)&&ua.uinteger(t.startLine)&&(ua.undefined(t.startCharacter)||ua.uinteger(t.startCharacter))&&(ua.undefined(t.endCharacter)||ua.uinteger(t.endCharacter))&&(ua.undefined(t.kind)||ua.string(t.kind))},(Rs=ks||(ks={})).create=function(e,t){return{location:e,message:t}},Rs.is=function(e){var t=e;return ua.defined(t)&&ps.is(t.location)&&ua.string(t.message)},(Ms=Ts||(Ts={})).Error=1,Ms.Warning=2,Ms.Information=3,Ms.Hint=4,(Is=Os||(Os={})).Unnecessary=1,Is.Deprecated=2,(Ps||(Ps={})).is=function(e){var t=e;return null!=t&&ua.string(t.href)},(Vs=Fs||(Fs={})).create=function(e,t,n,r,i,s){var o={range:e,message:t};return ua.defined(n)&&(o.severity=n),ua.defined(r)&&(o.code=r),ua.defined(i)&&(o.source=i),ua.defined(s)&&(o.relatedInformation=s),o},Vs.is=function(e){var t,n=e;return ua.defined(n)&&ms.is(n.range)&&ua.string(n.message)&&(ua.number(n.severity)||ua.undefined(n.severity))&&(ua.integer(n.code)||ua.string(n.code)||ua.undefined(n.code))&&(ua.undefined(n.codeDescription)||ua.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(ua.string(n.source)||ua.undefined(n.source))&&(ua.undefined(n.relatedInformation)||ua.typedArray(n.relatedInformation,ks.is))},function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={title:e,command:t};return ua.defined(n)&&n.length>0&&(i.arguments=n),i},e.is=function(e){var t=e;return ua.defined(t)&&ua.string(t.title)&&ua.string(t.command)}}(Ds||(Ds={})),(Ks=qs||(qs={})).replace=function(e,t){return{range:e,newText:t}},Ks.insert=function(e,t){return{range:{start:e,end:e},newText:t}},Ks.del=function(e){return{range:e,newText:""}},Ks.is=function(e){var t=e;return ua.objectLiteral(t)&&ua.string(t.newText)&&ms.is(t.range)},(Bs=js||(js={})).create=function(e,t,n){var r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},Bs.is=function(e){var t=e;return void 0!==t&&ua.objectLiteral(t)&&ua.string(t.label)&&(ua.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(ua.string(t.description)||void 0===t.description)},(Us||(Us={})).is=function(e){return"string"==typeof e},(Ws=$s||($s={})).replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},Ws.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},Ws.del=function(e,t){return{range:e,newText:"",annotationId:t}},Ws.is=function(e){var t=e;return qs.is(t)&&(js.is(t.annotationId)||Us.is(t.annotationId))},(zs=Hs||(Hs={})).create=function(e,t){return{textDocument:e,edits:t}},zs.is=function(e){var t=e;return ua.defined(t)&&vo.is(t.textDocument)&&Array.isArray(t.edits)},(Js=Gs||(Gs={})).create=function(e,t,n){var r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},Js.is=function(e){var t=e;return t&&"create"===t.kind&&ua.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||ua.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||ua.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||Us.is(t.annotationId))},(Qs=Xs||(Xs={})).create=function(e,t,n,r){var i={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(i.options=n),void 0!==r&&(i.annotationId=r),i},Qs.is=function(e){var t=e;return t&&"rename"===t.kind&&ua.string(t.oldUri)&&ua.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||ua.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||ua.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||Us.is(t.annotationId))},(Ys=Zs||(Zs={})).create=function(e,t,n){var r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},Ys.is=function(e){var t=e;return t&&"delete"===t.kind&&ua.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||ua.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||ua.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||Us.is(t.annotationId))},(eo||(eo={})).is=function(e){var t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(e){return ua.string(e.kind)?Gs.is(e)||Xs.is(e)||Zs.is(e):Hs.is(e)})))};var fo,mo,go,po,vo,bo,yo,Co,_o,wo,So,Lo,Eo,xo,No,Ao,ko,Ro,To,Mo,Oo,Io,Po,Fo,Vo,Do,qo,Ko,jo,Bo,Uo,$o,Wo,Ho,zo,Go,Jo,Xo,Qo,Zo,Yo,ea,ta,na,ra,ia,sa,oa,aa=function(){function e(e,t){this.edits=e,this.changeAnnotations=t}return e.prototype.insert=function(e,t,n){var r,i;if(void 0===n?r=qs.insert(e,t):Us.is(n)?(i=n,r=$s.insert(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),r=$s.insert(e,t,i)),this.edits.push(r),void 0!==i)return i},e.prototype.replace=function(e,t,n){var r,i;if(void 0===n?r=qs.replace(e,t):Us.is(n)?(i=n,r=$s.replace(e,t,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),r=$s.replace(e,t,i)),this.edits.push(r),void 0!==i)return i},e.prototype.delete=function(e,t){var n,r;if(void 0===t?n=qs.del(e):Us.is(t)?(r=t,n=$s.del(e,t)):(this.assertChangeAnnotations(this.changeAnnotations),r=this.changeAnnotations.manage(t),n=$s.del(e,r)),this.edits.push(n),void 0!==r)return r},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e.prototype.assertChangeAnnotations=function(e){if(void 0===e)throw new Error("Text edit change is not configured to manage change annotations.")},e}(),la=function(){function e(e){this._annotations=void 0===e?Object.create(null):e,this._counter=0,this._size=0}return e.prototype.all=function(){return this._annotations},Object.defineProperty(e.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.manage=function(e,t){var n;if(Us.is(e)?n=e:(n=this.nextId(),t=e),void 0!==this._annotations[n])throw new Error("Id "+n+" is already in use.");if(void 0===t)throw new Error("No annotation provided for id "+n);return this._annotations[n]=t,this._size++,n},e.prototype.nextId=function(){return this._counter++,this._counter.toString()},e}();!function(){function e(e){var t=this;this._textEditChanges=Object.create(null),void 0!==e?(this._workspaceEdit=e,e.documentChanges?(this._changeAnnotations=new la(e.changeAnnotations),e.changeAnnotations=this._changeAnnotations.all(),e.documentChanges.forEach((function(e){if(Hs.is(e)){var n=new aa(e.edits,t._changeAnnotations);t._textEditChanges[e.textDocument.uri]=n}}))):e.changes&&Object.keys(e.changes).forEach((function(n){var r=new aa(e.changes[n]);t._textEditChanges[n]=r}))):this._workspaceEdit={}}Object.defineProperty(e.prototype,"edit",{get:function(){return this.initDocumentChanges(),void 0!==this._changeAnnotations&&(0===this._changeAnnotations.size?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit},enumerable:!1,configurable:!0}),e.prototype.getTextEditChange=function(e){if(vo.is(e)){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var t={uri:e.uri,version:e.version};if(!(r=this._textEditChanges[t.uri])){var n={textDocument:t,edits:i=[]};this._workspaceEdit.documentChanges.push(n),r=new aa(i,this._changeAnnotations),this._textEditChanges[t.uri]=r}return r}if(this.initChanges(),void 0===this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");var r;if(!(r=this._textEditChanges[e])){var i=[];this._workspaceEdit.changes[e]=i,r=new aa(i),this._textEditChanges[e]=r}return r},e.prototype.initDocumentChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._changeAnnotations=new la,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())},e.prototype.initChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._workspaceEdit.changes=Object.create(null))},e.prototype.createFile=function(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r,i,s;if(js.is(t)||Us.is(t)?r=t:n=t,void 0===r?i=Gs.create(e,n):(s=Us.is(r)?r:this._changeAnnotations.manage(r),i=Gs.create(e,n,s)),this._workspaceEdit.documentChanges.push(i),void 0!==s)return s},e.prototype.renameFile=function(e,t,n,r){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var i,s,o;if(js.is(n)||Us.is(n)?i=n:r=n,void 0===i?s=Xs.create(e,t,r):(o=Us.is(i)?i:this._changeAnnotations.manage(i),s=Xs.create(e,t,r,o)),this._workspaceEdit.documentChanges.push(s),void 0!==o)return o},e.prototype.deleteFile=function(e,t,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r,i,s;if(js.is(t)||Us.is(t)?r=t:n=t,void 0===r?i=Zs.create(e,n):(s=Us.is(r)?r:this._changeAnnotations.manage(r),i=Zs.create(e,n,s)),this._workspaceEdit.documentChanges.push(i),void 0!==s)return s}}(),(mo=fo||(fo={})).create=function(e){return{uri:e}},mo.is=function(e){var t=e;return ua.defined(t)&&ua.string(t.uri)},(po=go||(go={})).create=function(e,t){return{uri:e,version:t}},po.is=function(e){var t=e;return ua.defined(t)&&ua.string(t.uri)&&ua.integer(t.version)},(bo=vo||(vo={})).create=function(e,t){return{uri:e,version:t}},bo.is=function(e){var t=e;return ua.defined(t)&&ua.string(t.uri)&&(null===t.version||ua.integer(t.version))},(Co=yo||(yo={})).create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},Co.is=function(e){var t=e;return ua.defined(t)&&ua.string(t.uri)&&ua.string(t.languageId)&&ua.integer(t.version)&&ua.string(t.text)},(wo=_o||(_o={})).PlainText="plaintext",wo.Markdown="markdown",function(e){e.is=function(t){var n=t;return n===e.PlainText||n===e.Markdown}}(_o||(_o={})),(So||(So={})).is=function(e){var t=e;return ua.objectLiteral(e)&&_o.is(t.kind)&&ua.string(t.value)},function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(Lo||(Lo={})),(xo=Eo||(Eo={})).PlainText=1,xo.Snippet=2,function(e){e.Deprecated=1}(No||(No={})),(ko=Ao||(Ao={})).create=function(e,t,n){return{newText:e,insert:t,replace:n}},ko.is=function(e){var t=e;return t&&ua.string(t.newText)&&ms.is(t.insert)&&ms.is(t.replace)},(To=Ro||(Ro={})).asIs=1,To.adjustIndentation=2,(Mo||(Mo={})).create=function(e){return{label:e}},(Oo||(Oo={})).create=function(e,t){return{items:e||[],isIncomplete:!!t}},(Po=Io||(Io={})).fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},Po.is=function(e){var t=e;return ua.string(t)||ua.objectLiteral(t)&&ua.string(t.language)&&ua.string(t.value)},(Fo||(Fo={})).is=function(e){var t=e;return!!t&&ua.objectLiteral(t)&&(So.is(t.contents)||Io.is(t.contents)||ua.typedArray(t.contents,Io.is))&&(void 0===e.range||ms.is(e.range))},(Vo||(Vo={})).create=function(e,t){return t?{label:e,documentation:t}:{label:e}},(Do||(Do={})).create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={label:e};return ua.defined(t)&&(i.documentation=t),ua.defined(n)?i.parameters=n:i.parameters=[],i},function(e){e.Text=1,e.Read=2,e.Write=3}(qo||(qo={})),(Ko||(Ko={})).create=function(e,t){var n={range:e};return ua.number(t)&&(n.kind=t),n},function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(jo||(jo={})),function(e){e.Deprecated=1}(Bo||(Bo={})),(Uo||(Uo={})).create=function(e,t,n,r,i){var s={name:e,kind:t,location:{uri:r,range:n}};return i&&(s.containerName=i),s},(Wo=$o||($o={})).create=function(e,t,n,r,i,s){var o={name:e,detail:t,kind:n,range:r,selectionRange:i};return void 0!==s&&(o.children=s),o},Wo.is=function(e){var t=e;return t&&ua.string(t.name)&&ua.number(t.kind)&&ms.is(t.range)&&ms.is(t.selectionRange)&&(void 0===t.detail||ua.string(t.detail))&&(void 0===t.deprecated||ua.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))},(zo=Ho||(Ho={})).Empty="",zo.QuickFix="quickfix",zo.Refactor="refactor",zo.RefactorExtract="refactor.extract",zo.RefactorInline="refactor.inline",zo.RefactorRewrite="refactor.rewrite",zo.Source="source",zo.SourceOrganizeImports="source.organizeImports",zo.SourceFixAll="source.fixAll",(Jo=Go||(Go={})).create=function(e,t){var n={diagnostics:e};return null!=t&&(n.only=t),n},Jo.is=function(e){var t=e;return ua.defined(t)&&ua.typedArray(t.diagnostics,Fs.is)&&(void 0===t.only||ua.typedArray(t.only,ua.string))},(Qo=Xo||(Xo={})).create=function(e,t,n){var r={title:e},i=!0;return"string"==typeof t?(i=!1,r.kind=t):Ds.is(t)?r.command=t:r.edit=t,i&&void 0!==n&&(r.kind=n),r},Qo.is=function(e){var t=e;return t&&ua.string(t.title)&&(void 0===t.diagnostics||ua.typedArray(t.diagnostics,Fs.is))&&(void 0===t.kind||ua.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||Ds.is(t.command))&&(void 0===t.isPreferred||ua.boolean(t.isPreferred))&&(void 0===t.edit||eo.is(t.edit))},(Yo=Zo||(Zo={})).create=function(e,t){var n={range:e};return ua.defined(t)&&(n.data=t),n},Yo.is=function(e){var t=e;return ua.defined(t)&&ms.is(t.range)&&(ua.undefined(t.command)||Ds.is(t.command))},(ta=ea||(ea={})).create=function(e,t){return{tabSize:e,insertSpaces:t}},ta.is=function(e){var t=e;return ua.defined(t)&&ua.uinteger(t.tabSize)&&ua.boolean(t.insertSpaces)},(ra=na||(na={})).create=function(e,t,n){return{range:e,target:t,data:n}},ra.is=function(e){var t=e;return ua.defined(t)&&ms.is(t.range)&&(ua.undefined(t.target)||ua.string(t.target))},(sa=ia||(ia={})).create=function(e,t){return{range:e,parent:t}},sa.is=function(e){var t=e;return void 0!==t&&ms.is(t.range)&&(void 0===t.parent||sa.is(t.parent))},function(e){function t(e,n){if(e.length<=1)return e;var r=e.length/2|0,i=e.slice(0,r),s=e.slice(r);t(i,n),t(s,n);for(var o=0,a=0,l=0;o<i.length&&a<s.length;){var u=n(i[o],s[a]);e[l++]=u<=0?i[o++]:s[a++]}for(;o<i.length;)e[l++]=i[o++];for(;a<s.length;)e[l++]=s[a++];return e}e.create=function(e,t,n,r){return new da(e,t,n,r)},e.is=function(e){var t=e;return!!(ua.defined(t)&&ua.string(t.uri)&&(ua.undefined(t.languageId)||ua.string(t.languageId))&&ua.uinteger(t.lineCount)&&ua.func(t.getText)&&ua.func(t.positionAt)&&ua.func(t.offsetAt))},e.applyEdits=function(e,n){for(var r=e.getText(),i=t(n,(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),s=r.length,o=i.length-1;o>=0;o--){var a=i[o],l=e.offsetAt(a.range.start),u=e.offsetAt(a.range.end);if(!(u<=s))throw new Error("Overlapping edit");r=r.substring(0,l)+a.newText+r.substring(u,r.length),s=l}return r}}(oa||(oa={}));var ua,ca,ha,da=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0},e.prototype.getLineOffsets=function(){if(void 0===this._lineOffsets){for(var e=[],t=this._content,n=!0,r=0;r<t.length;r++){n&&(e.push(r),n=!1);var i=t.charAt(r);n="\r"===i||"\n"===i,"\r"===i&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return ds.create(0,e);for(;n<r;){var i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}var s=n-1;return ds.create(s,e-t[s])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!1,configurable:!0}),e}();ca=ua||(ua={}),ha=Object.prototype.toString,ca.defined=function(e){return void 0!==e},ca.undefined=function(e){return void 0===e},ca.boolean=function(e){return!0===e||!1===e},ca.string=function(e){return"[object String]"===ha.call(e)},ca.number=function(e){return"[object Number]"===ha.call(e)},ca.numberRange=function(e,t,n){return"[object Number]"===ha.call(e)&&t<=e&&e<=n},ca.integer=function(e){return"[object Number]"===ha.call(e)&&-2147483648<=e&&e<=2147483647},ca.uinteger=function(e){return"[object Number]"===ha.call(e)&&0<=e&&e<=2147483647},ca.func=function(e){return"[object Function]"===ha.call(e)},ca.objectLiteral=function(e){return null!==e&&"object"==typeof e},ca.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)};var fa,ma,ga,pa,va,ba=class e{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(t,n){for(let r of t)if(e.isIncremental(r)){const e=_a(r.range),t=this.offsetAt(e.start),n=this.offsetAt(e.end);this._content=this._content.substring(0,t)+r.text+this._content.substring(n,this._content.length);const i=Math.max(e.start.line,0),s=Math.max(e.end.line,0);let o=this._lineOffsets;const a=Ca(r.text,!1,t);if(s-i===a.length)for(let r=0,u=a.length;r<u;r++)o[r+i+1]=a[r];else a.length<1e4?o.splice(i+1,s-i,...a):this._lineOffsets=o=o.slice(0,i+1).concat(a,o.slice(s+1));const l=r.text.length-(n-t);if(0!==l)for(let r=i+1+a.length,u=o.length;r<u;r++)o[r]=o[r]+l}else{if(!e.isFull(r))throw new Error("Unknown change event received");this._content=r.text,this._lineOffsets=void 0}this._version=n}getLineOffsets(){return void 0===this._lineOffsets&&(this._lineOffsets=Ca(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return{line:0,character:e};for(;n<r;){let i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}let i=n-1;return{line:i,character:e-t[i]}}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){let t=e;return null!=t&&"string"==typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"==typeof t.rangeLength)}static isFull(e){let t=e;return null!=t&&"string"==typeof t.text&&void 0===t.range&&void 0===t.rangeLength}};function ya(e,t){if(e.length<=1)return e;const n=e.length/2|0,r=e.slice(0,n),i=e.slice(n);ya(r,t),ya(i,t);let s=0,o=0,a=0;for(;s<r.length&&o<i.length;){let n=t(r[s],i[o]);e[a++]=n<=0?r[s++]:i[o++]}for(;s<r.length;)e[a++]=r[s++];for(;o<i.length;)e[a++]=i[o++];return e}function Ca(e,t,n=0){const r=t?[n]:[];for(let i=0;i<e.length;i++){let t=e.charCodeAt(i);13!==t&&10!==t||(13===t&&i+1<e.length&&10===e.charCodeAt(i+1)&&i++,r.push(n+i+1))}return r}function _a(e){const t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function wa(e){const t=_a(e.range);return t!==e.range?{newText:e.newText,range:t}:e}function Sa(e,t,...n){return function(e,t){let n;return n=0===t.length?e:e.replace(/\{(\d+)\}/g,((e,n)=>{let r=n[0];return void 0!==t[r]?t[r]:e})),n}(t,n)}function La(e){return Sa}(ma=fa||(fa={})).create=function(e,t,n,r){return new ba(e,t,n,r)},ma.update=function(e,t,n){if(e instanceof ba)return e.update(t,n),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},ma.applyEdits=function(e,t){let n=e.getText(),r=ya(t.map(wa),((e,t)=>{let n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),i=0;const s=[];for(const o of r){let t=e.offsetAt(o.range.start);if(t<i)throw new Error("Overlapping edit");t>i&&s.push(n.substring(i,t)),o.newText.length&&s.push(o.newText),i=e.offsetAt(o.range.end)}return s.push(n.substr(i)),s.join("")},(pa=ga||(ga={}))[pa.Undefined=0]="Undefined",pa[pa.EnumValueMismatch=1]="EnumValueMismatch",pa[pa.Deprecated=2]="Deprecated",pa[pa.UnexpectedEndOfComment=257]="UnexpectedEndOfComment",pa[pa.UnexpectedEndOfString=258]="UnexpectedEndOfString",pa[pa.UnexpectedEndOfNumber=259]="UnexpectedEndOfNumber",pa[pa.InvalidUnicode=260]="InvalidUnicode",pa[pa.InvalidEscapeCharacter=261]="InvalidEscapeCharacter",pa[pa.InvalidCharacter=262]="InvalidCharacter",pa[pa.PropertyExpected=513]="PropertyExpected",pa[pa.CommaExpected=514]="CommaExpected",pa[pa.ColonExpected=515]="ColonExpected",pa[pa.ValueExpected=516]="ValueExpected",pa[pa.CommaOrCloseBacketExpected=517]="CommaOrCloseBacketExpected",pa[pa.CommaOrCloseBraceExpected=518]="CommaOrCloseBraceExpected",pa[pa.TrailingComma=519]="TrailingComma",pa[pa.DuplicateKey=520]="DuplicateKey",pa[pa.CommentNotPermitted=521]="CommentNotPermitted",pa[pa.SchemaResolveError=768]="SchemaResolveError",(va||(va={})).LATEST={textDocument:{completion:{completionItem:{documentationFormat:[_o.Markdown,_o.PlainText],commitCharactersSupport:!0}}}};var Ea,xa,Na=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Aa=La(),ka={"color-hex":{errorMessage:Aa("colorHexFormatWarning","Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA."),pattern:/^#([0-9A-Fa-f]{3,4}|([0-9A-Fa-f]{2}){3,4})$/},"date-time":{errorMessage:Aa("dateTimeFormatWarning","String is not a RFC3339 date-time."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},date:{errorMessage:Aa("dateFormatWarning","String is not a RFC3339 date."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/i},time:{errorMessage:Aa("timeFormatWarning","String is not a RFC3339 time."),pattern:/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},email:{errorMessage:Aa("emailFormatWarning","String is not an e-mail address."),pattern:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}))$/},hostname:{errorMessage:Aa("hostnameFormatWarning","String is not a hostname."),pattern:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i},ipv4:{errorMessage:Aa("ipv4FormatWarning","String is not an IPv4 address."),pattern:/^(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/},ipv6:{errorMessage:Aa("ipv6FormatWarning","String is not an IPv6 address."),pattern:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i}},Ra=function(){function e(e,t,n){void 0===n&&(n=0),this.offset=t,this.length=n,this.parent=e}return Object.defineProperty(e.prototype,"children",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.toString=function(){return"type: "+this.type+" ("+this.offset+"/"+this.length+")"+(this.parent?" parent: {"+this.parent.toString()+"}":"")},e}(),Ta=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="null",r.value=null,r}return Na(t,e),t}(Ra),Ma=function(e){function t(t,n,r){var i=e.call(this,t,r)||this;return i.type="boolean",i.value=n,i}return Na(t,e),t}(Ra),Oa=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="array",r.items=[],r}return Na(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.items},enumerable:!1,configurable:!0}),t}(Ra),Ia=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="number",r.isInteger=!0,r.value=Number.NaN,r}return Na(t,e),t}(Ra),Pa=function(e){function t(t,n,r){var i=e.call(this,t,n,r)||this;return i.type="string",i.value="",i}return Na(t,e),t}(Ra),Fa=function(e){function t(t,n,r){var i=e.call(this,t,n)||this;return i.type="property",i.colonOffset=-1,i.keyNode=r,i}return Na(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.valueNode?[this.keyNode,this.valueNode]:[this.keyNode]},enumerable:!1,configurable:!0}),t}(Ra),Va=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="object",r.properties=[],r}return Na(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.properties},enumerable:!1,configurable:!0}),t}(Ra);function Da(e){return uo(e)?e?{}:{not:{}}:e}(xa=Ea||(Ea={}))[xa.Key=0]="Key",xa[xa.Enum=1]="Enum";var qa=function(){function e(e,t){void 0===e&&(e=-1),this.focusOffset=e,this.exclude=t,this.schemas=[]}return e.prototype.add=function(e){this.schemas.push(e)},e.prototype.merge=function(e){Array.prototype.push.apply(this.schemas,e.schemas)},e.prototype.include=function(e){return(-1===this.focusOffset||$a(e,this.focusOffset))&&e!==this.exclude},e.prototype.newSub=function(){return new e(-1,this.exclude)},e}(),Ka=function(){function e(){}return Object.defineProperty(e.prototype,"schemas",{get:function(){return[]},enumerable:!1,configurable:!0}),e.prototype.add=function(e){},e.prototype.merge=function(e){},e.prototype.include=function(e){return!0},e.prototype.newSub=function(){return this},e.instance=new e,e}(),ja=function(){function e(){this.problems=[],this.propertiesMatches=0,this.propertiesValueMatches=0,this.primaryValueMatches=0,this.enumValueMatch=!1,this.enumValues=void 0}return e.prototype.hasProblems=function(){return!!this.problems.length},e.prototype.mergeAll=function(e){for(var t=0,n=e;t<n.length;t++){var r=n[t];this.merge(r)}},e.prototype.merge=function(e){this.problems=this.problems.concat(e.problems)},e.prototype.mergeEnumValues=function(e){if(!this.enumValueMatch&&!e.enumValueMatch&&this.enumValues&&e.enumValues){this.enumValues=this.enumValues.concat(e.enumValues);for(var t=0,n=this.problems;t<n.length;t++){var r=n[t];r.code===ga.EnumValueMismatch&&(r.message=Aa("enumWarning","Value is not accepted. Valid values: {0}.",this.enumValues.map((function(e){return JSON.stringify(e)})).join(", ")))}}},e.prototype.mergePropertyMatch=function(e){this.merge(e),this.propertiesMatches++,(e.enumValueMatch||!e.hasProblems()&&e.propertiesMatches)&&this.propertiesValueMatches++,e.enumValueMatch&&e.enumValues&&1===e.enumValues.length&&this.primaryValueMatches++},e.prototype.compare=function(e){var t=this.hasProblems();return t!==e.hasProblems()?t?-1:1:this.enumValueMatch!==e.enumValueMatch?e.enumValueMatch?-1:1:this.primaryValueMatches!==e.primaryValueMatches?this.primaryValueMatches-e.primaryValueMatches:this.propertiesValueMatches!==e.propertiesValueMatches?this.propertiesValueMatches-e.propertiesValueMatches:this.propertiesMatches-e.propertiesMatches},e}();function Ba(e){return so(e)}function Ua(e){return io(e)}function $a(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}var Wa=function(){function e(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=[]),this.root=e,this.syntaxErrors=t,this.comments=n}return e.prototype.getNodeFromOffset=function(e,t){if(void 0===t&&(t=!1),this.root)return ro(this.root,e,t)},e.prototype.visit=function(e){if(this.root){var t=function(n){var r=e(n),i=n.children;if(Array.isArray(i))for(var s=0;s<i.length&&r;s++)r=t(i[s]);return r};t(this.root)}},e.prototype.validate=function(e,t,n){if(void 0===n&&(n=Ts.Warning),this.root&&t){var r=new ja;return Ha(this.root,t,r,Ka.instance),r.problems.map((function(t){var r,i=ms.create(e.positionAt(t.location.offset),e.positionAt(t.location.offset+t.location.length));return Fs.create(i,t.message,null!==(r=t.severity)&&void 0!==r?r:n,t.code)}))}},e.prototype.getMatchingSchemas=function(e,t,n){void 0===t&&(t=-1);var r=new qa(t,n);return this.root&&e&&Ha(this.root,e,new ja,r),r.schemas},e}();function Ha(e,t,n,r){if(e&&r.include(e)){var i=e;switch(i.type){case"object":!function(e,t,n,r){for(var i=Object.create(null),s=[],o=0,a=e.properties;o<a.length;o++){i[q=(p=a[o]).keyNode.value]=p.valueNode,s.push(q)}if(Array.isArray(t.required))for(var l=0,u=t.required;l<u.length;l++){if(!i[S=u[l]]){var c=e.parent&&"property"===e.parent.type&&e.parent.keyNode,h=c?{offset:c.offset,length:c.length}:{offset:e.offset,length:1};n.problems.push({location:h,message:Aa("MissingRequiredPropWarning",'Missing property "{0}".',S)})}}var d=function(e){for(var t=s.indexOf(e);t>=0;)s.splice(t,1),t=s.indexOf(e)};if(t.properties)for(var f=0,m=Object.keys(t.properties);f<m.length;f++){d(S=m[f]);var g=t.properties[S];if(k=i[S])if(uo(g))if(g)n.propertiesMatches++,n.propertiesValueMatches++;else{var p=k.parent;n.problems.push({location:{offset:p.keyNode.offset,length:p.keyNode.length},message:t.errorMessage||Aa("DisallowedExtraPropWarning","Property {0} is not allowed.",S)})}else Ha(k,g,x=new ja,r),n.mergePropertyMatch(x)}if(t.patternProperties)for(var v=0,b=Object.keys(t.patternProperties);v<b.length;v++)for(var y=b[v],C=ho(y),_=0,w=s.slice(0);_<w.length;_++){var S=w[_];if(null==C?void 0:C.test(S))if(d(S),k=i[S])if(uo(g=t.patternProperties[y]))if(g)n.propertiesMatches++,n.propertiesValueMatches++;else{p=k.parent;n.problems.push({location:{offset:p.keyNode.offset,length:p.keyNode.length},message:t.errorMessage||Aa("DisallowedExtraPropWarning","Property {0} is not allowed.",S)})}else Ha(k,g,x=new ja,r),n.mergePropertyMatch(x)}if("object"==typeof t.additionalProperties)for(var L=0,E=s;L<E.length;L++){if(k=i[S=E[L]]){var x=new ja;Ha(k,t.additionalProperties,x,r),n.mergePropertyMatch(x)}}else if(!1===t.additionalProperties&&s.length>0)for(var N=0,A=s;N<A.length;N++){var k;if(k=i[S=A[N]]){p=k.parent;n.problems.push({location:{offset:p.keyNode.offset,length:p.keyNode.length},message:t.errorMessage||Aa("DisallowedExtraPropWarning","Property {0} is not allowed.",S)})}}ao(t.maxProperties)&&e.properties.length>t.maxProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("MaxPropWarning","Object has more properties than limit of {0}.",t.maxProperties)});ao(t.minProperties)&&e.properties.length<t.minProperties&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("MinPropWarning","Object has fewer properties than the required number of {0}",t.minProperties)});if(t.dependencies)for(var R=0,T=Object.keys(t.dependencies);R<T.length;R++){if(i[q=T[R]]){var M=t.dependencies[q];if(Array.isArray(M))for(var O=0,I=M;O<I.length;O++){var P=I[O];i[P]?n.propertiesValueMatches++:n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("RequiredDependentPropWarning","Object is missing property {0} required by property {1}.",P,q)})}else if(g=Da(M))Ha(e,g,x=new ja,r),n.mergePropertyMatch(x)}}var F=Da(t.propertyNames);if(F)for(var V=0,D=e.properties;V<D.length;V++){var q;(q=D[V].keyNode)&&Ha(q,F,n,Ka.instance)}}(i,t,n,r);break;case"array":!function(e,t,n,r){if(Array.isArray(t.items)){for(var i=t.items,s=0;s<i.length;s++){var o=Da(i[s]),a=new ja;(d=e.items[s])?(Ha(d,o,a,r),n.mergePropertyMatch(a)):e.items.length>=i.length&&n.propertiesValueMatches++}if(e.items.length>i.length)if("object"==typeof t.additionalItems)for(var l=i.length;l<e.items.length;l++){a=new ja;Ha(e.items[l],t.additionalItems,a,r),n.mergePropertyMatch(a)}else!1===t.additionalItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("additionalItemsWarning","Array has too many items according to schema. Expected {0} or fewer.",i.length)})}else{var u=Da(t.items);if(u)for(var c=0,h=e.items;c<h.length;c++){var d;Ha(d=h[c],u,a=new ja,r),n.mergePropertyMatch(a)}}var f=Da(t.contains);if(f){e.items.some((function(e){var t=new ja;return Ha(e,f,t,Ka.instance),!t.hasProblems()}))||n.problems.push({location:{offset:e.offset,length:e.length},message:t.errorMessage||Aa("requiredItemMissingWarning","Array does not contain required item.")})}ao(t.minItems)&&e.items.length<t.minItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("minItemsWarning","Array has too few items. Expected {0} or more.",t.minItems)});ao(t.maxItems)&&e.items.length>t.maxItems&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("maxItemsWarning","Array has too many items. Expected {0} or fewer.",t.maxItems)});if(!0===t.uniqueItems){var m=Ba(e);m.some((function(e,t){return t!==m.lastIndexOf(e)}))&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("uniqueItemsWarning","Array has duplicate items.")})}}(i,t,n,r);break;case"string":!function(e,t,n){ao(t.minLength)&&e.value.length<t.minLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("minLengthWarning","String is shorter than the minimum length of {0}.",t.minLength)});ao(t.maxLength)&&e.value.length>t.maxLength&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("maxLengthWarning","String is longer than the maximum length of {0}.",t.maxLength)});if(i=t.pattern,"string"==typeof i){var r=ho(t.pattern);(null==r?void 0:r.test(e.value))||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||Aa("patternWarning",'String does not match the pattern of "{0}".',t.pattern)})}var i;if(t.format)switch(t.format){case"uri":case"uri-reference":var s=void 0;if(e.value){var o=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/.exec(e.value);o?o[2]||"uri"!==t.format||(s=Aa("uriSchemeMissing","URI with a scheme is expected.")):s=Aa("uriMissing","URI is expected.")}else s=Aa("uriEmpty","URI expected.");s&&n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||Aa("uriFormatWarning","String is not a URI: {0}",s)});break;case"color-hex":case"date-time":case"date":case"time":case"email":case"hostname":case"ipv4":case"ipv6":var a=ka[t.format];e.value&&a.pattern.exec(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},message:t.patternErrorMessage||t.errorMessage||a.errorMessage})}}(i,t,n);break;case"number":!function(e,t,n){var r=e.value;function i(e){var t,n=/^(-?\d+)(?:\.(\d+))?(?:e([-+]\d+))?$/.exec(e.toString());return n&&{value:Number(n[1]+(n[2]||"")),multiplier:((null===(t=n[2])||void 0===t?void 0:t.length)||0)-(parseInt(n[3])||0)}}if(ao(t.multipleOf)){var s=-1;if(Number.isInteger(t.multipleOf))s=r%t.multipleOf;else{var o=i(t.multipleOf),a=i(r);if(o&&a){var l=Math.pow(10,Math.abs(a.multiplier-o.multiplier));a.multiplier<o.multiplier?a.value*=l:o.value*=l,s=a.value%o.value}}0!==s&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("multipleOfWarning","Value is not divisible by {0}.",t.multipleOf)})}function u(e,t){return ao(t)?t:uo(t)&&t?e:void 0}function c(e,t){if(!uo(t)||!t)return e}var h=u(t.minimum,t.exclusiveMinimum);ao(h)&&r<=h&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("exclusiveMinimumWarning","Value is below the exclusive minimum of {0}.",h)});var d=u(t.maximum,t.exclusiveMaximum);ao(d)&&r>=d&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("exclusiveMaximumWarning","Value is above the exclusive maximum of {0}.",d)});var f=c(t.minimum,t.exclusiveMinimum);ao(f)&&r<f&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("minimumWarning","Value is below the minimum of {0}.",f)});var m=c(t.maximum,t.exclusiveMaximum);ao(m)&&r>m&&n.problems.push({location:{offset:e.offset,length:e.length},message:Aa("maximumWarning","Value is above the maximum of {0}.",m)})}(i,t,n);break;case"property":return Ha(i.valueNode,t,n,r)}!function(){function e(e){return i.type===e||"integer"===e&&"number"===i.type&&i.isInteger}Array.isArray(t.type)?t.type.some(e)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||Aa("typeArrayMismatchWarning","Incorrect type. Expected one of {0}.",t.type.join(", "))}):t.type&&(e(t.type)||n.problems.push({location:{offset:i.offset,length:i.length},message:t.errorMessage||Aa("typeMismatchWarning",'Incorrect type. Expected "{0}".',t.type)}));if(Array.isArray(t.allOf))for(var s=0,o=t.allOf;s<o.length;s++){var a=o[s];Ha(i,Da(a),n,r)}var l=Da(t.not);if(l){var u=new ja,c=r.newSub();Ha(i,l,u,c),u.hasProblems()||n.problems.push({location:{offset:i.offset,length:i.length},message:Aa("notSchemaWarning","Matches a schema that is not allowed.")});for(var h=0,d=c.schemas;h<d.length;h++){var f=d[h];f.inverted=!f.inverted,r.add(f)}}var m=function(e,t){for(var s=[],o=void 0,a=0,l=e;a<l.length;a++){var u=Da(l[a]),c=new ja,h=r.newSub();if(Ha(i,u,c,h),c.hasProblems()||s.push(u),o)if(t||c.hasProblems()||o.validationResult.hasProblems()){var d=c.compare(o.validationResult);d>0?o={schema:u,validationResult:c,matchingSchemas:h}:0===d&&(o.matchingSchemas.merge(h),o.validationResult.mergeEnumValues(c))}else o.matchingSchemas.merge(h),o.validationResult.propertiesMatches+=c.propertiesMatches,o.validationResult.propertiesValueMatches+=c.propertiesValueMatches;else o={schema:u,validationResult:c,matchingSchemas:h}}return s.length>1&&t&&n.problems.push({location:{offset:i.offset,length:1},message:Aa("oneOfWarning","Matches multiple schemas when only one must validate.")}),o&&(n.merge(o.validationResult),n.propertiesMatches+=o.validationResult.propertiesMatches,n.propertiesValueMatches+=o.validationResult.propertiesValueMatches,r.merge(o.matchingSchemas)),s.length};Array.isArray(t.anyOf)&&m(t.anyOf,!1);Array.isArray(t.oneOf)&&m(t.oneOf,!0);var g=function(e){var t=new ja,s=r.newSub();Ha(i,Da(e),t,s),n.merge(t),n.propertiesMatches+=t.propertiesMatches,n.propertiesValueMatches+=t.propertiesValueMatches,r.merge(s)},p=Da(t.if);p&&(v=p,b=Da(t.then),y=Da(t.else),C=Da(v),_=new ja,w=r.newSub(),Ha(i,C,_,w),r.merge(w),_.hasProblems()?y&&g(y):b&&g(b));var v,b,y,C,_,w;if(Array.isArray(t.enum)){for(var S=Ba(i),L=!1,E=0,x=t.enum;E<x.length;E++){if(oo(S,x[E])){L=!0;break}}n.enumValues=t.enum,n.enumValueMatch=L,L||n.problems.push({location:{offset:i.offset,length:i.length},code:ga.EnumValueMismatch,message:t.errorMessage||Aa("enumWarning","Value is not accepted. Valid values: {0}.",t.enum.map((function(e){return JSON.stringify(e)})).join(", "))})}if(lo(t.const)){oo(S=Ba(i),t.const)?n.enumValueMatch=!0:(n.problems.push({location:{offset:i.offset,length:i.length},code:ga.EnumValueMismatch,message:t.errorMessage||Aa("constWarning","Value must be {0}.",JSON.stringify(t.const))}),n.enumValueMatch=!1),n.enumValues=[t.const]}t.deprecationMessage&&i.parent&&n.problems.push({location:{offset:i.parent.offset,length:i.parent.length},severity:Ts.Warning,message:t.deprecationMessage,code:ga.Deprecated})}(),r.add({node:i,schema:t})}}function za(e,t){var n=[],r=-1,i=e.getText(),s=to(i,!1),o=t&&t.collectComments?[]:void 0;function a(){for(;;){var t=s.scan();switch(c(),t){case 12:case 13:Array.isArray(o)&&o.push(ms.create(e.positionAt(s.getTokenOffset()),e.positionAt(s.getTokenOffset()+s.getTokenLength())));break;case 15:case 14:break;default:return t}}}function l(t,i,s,o,a){if(void 0===a&&(a=Ts.Error),0===n.length||s!==r){var l=ms.create(e.positionAt(s),e.positionAt(o));n.push(Fs.create(l,t,a,i,e.languageId)),r=s}}function u(e,t,n,r,o){void 0===n&&(n=void 0),void 0===r&&(r=[]),void 0===o&&(o=[]);var u=s.getTokenOffset(),c=s.getTokenOffset()+s.getTokenLength();if(u===c&&u>0){for(u--;u>0&&/\s/.test(i.charAt(u));)u--;c=u+1}if(l(e,t,u,c),n&&h(n,!1),r.length+o.length>0)for(var d=s.getToken();17!==d;){if(-1!==r.indexOf(d)){a();break}if(-1!==o.indexOf(d))break;d=a()}return n}function c(){switch(s.getTokenError()){case 4:return u(Aa("InvalidUnicode","Invalid unicode sequence in string."),ga.InvalidUnicode),!0;case 5:return u(Aa("InvalidEscapeCharacter","Invalid escape character in string."),ga.InvalidEscapeCharacter),!0;case 3:return u(Aa("UnexpectedEndOfNumber","Unexpected end of number."),ga.UnexpectedEndOfNumber),!0;case 1:return u(Aa("UnexpectedEndOfComment","Unexpected end of comment."),ga.UnexpectedEndOfComment),!0;case 2:return u(Aa("UnexpectedEndOfString","Unexpected end of string."),ga.UnexpectedEndOfString),!0;case 6:return u(Aa("InvalidCharacter","Invalid characters in string. Control characters must be escaped."),ga.InvalidCharacter),!0}return!1}function h(e,t){return e.length=s.getTokenOffset()+s.getTokenLength()-e.offset,t&&a(),e}var d=new Pa(void 0,0,0);function f(t,n){var r=new Fa(t,s.getTokenOffset(),d),i=m(r);if(!i){if(16!==s.getToken())return;u(Aa("DoubleQuotesExpected","Property keys must be doublequoted"),ga.Undefined);var o=new Pa(r,s.getTokenOffset(),s.getTokenLength());o.value=s.getTokenValue(),i=o,a()}r.keyNode=i;var c=n[i.value];if(c?(l(Aa("DuplicateKeyWarning","Duplicate object key"),ga.DuplicateKey,r.keyNode.offset,r.keyNode.offset+r.keyNode.length,Ts.Warning),"object"==typeof c&&l(Aa("DuplicateKeyWarning","Duplicate object key"),ga.DuplicateKey,c.keyNode.offset,c.keyNode.offset+c.keyNode.length,Ts.Warning),n[i.value]=!0):n[i.value]=r,6===s.getToken())r.colonOffset=s.getTokenOffset(),a();else if(u(Aa("ColonExpected","Colon expected"),ga.ColonExpected),10===s.getToken()&&e.positionAt(i.offset+i.length).line<e.positionAt(s.getTokenOffset()).line)return r.length=i.length,r;var h=g(r);return h?(r.valueNode=h,r.length=h.offset+h.length-r.offset,r):u(Aa("ValueExpected","Value expected"),ga.ValueExpected,r,[],[2,5])}function m(e){if(10===s.getToken()){var t=new Pa(e,s.getTokenOffset());return t.value=s.getTokenValue(),h(t,!0)}}function g(e){return function(e){if(3===s.getToken()){var t=new Oa(e,s.getTokenOffset());a();for(var n=!1;4!==s.getToken()&&17!==s.getToken();){if(5===s.getToken()){n||u(Aa("ValueExpected","Value expected"),ga.ValueExpected);var r=s.getTokenOffset();if(a(),4===s.getToken()){n&&l(Aa("TrailingComma","Trailing comma"),ga.TrailingComma,r,r+1);continue}}else n&&u(Aa("ExpectedComma","Expected comma"),ga.CommaExpected);var i=g(t);i?t.items.push(i):u(Aa("PropertyExpected","Value expected"),ga.ValueExpected,void 0,[],[4,5]),n=!0}return 4!==s.getToken()?u(Aa("ExpectedCloseBracket","Expected comma or closing bracket"),ga.CommaOrCloseBacketExpected,t):h(t,!0)}}(e)||function(e){if(1===s.getToken()){var t=new Va(e,s.getTokenOffset()),n=Object.create(null);a();for(var r=!1;2!==s.getToken()&&17!==s.getToken();){if(5===s.getToken()){r||u(Aa("PropertyExpected","Property expected"),ga.PropertyExpected);var i=s.getTokenOffset();if(a(),2===s.getToken()){r&&l(Aa("TrailingComma","Trailing comma"),ga.TrailingComma,i,i+1);continue}}else r&&u(Aa("ExpectedComma","Expected comma"),ga.CommaExpected);var o=f(t,n);o?t.properties.push(o):u(Aa("PropertyExpected","Property expected"),ga.PropertyExpected,void 0,[],[2,5]),r=!0}return 2!==s.getToken()?u(Aa("ExpectedCloseBrace","Expected comma or closing brace"),ga.CommaOrCloseBraceExpected,t):h(t,!0)}}(e)||m(e)||function(e){if(11===s.getToken()){var t=new Ia(e,s.getTokenOffset());if(0===s.getTokenError()){var n=s.getTokenValue();try{var r=JSON.parse(n);if(!ao(r))return u(Aa("InvalidNumberFormat","Invalid number format."),ga.Undefined,t);t.value=r}catch(Bl){return u(Aa("InvalidNumberFormat","Invalid number format."),ga.Undefined,t)}t.isInteger=-1===n.indexOf(".")}return h(t,!0)}}(e)||function(e){switch(s.getToken()){case 7:return h(new Ta(e,s.getTokenOffset()),!0);case 8:return h(new Ma(e,!0,s.getTokenOffset()),!0);case 9:return h(new Ma(e,!1,s.getTokenOffset()),!0);default:return}}(e)}var p=void 0;return 17!==a()&&((p=g(p))?17!==s.getToken()&&u(Aa("End of file expected","End of file expected."),ga.Undefined):u(Aa("Invalid symbol","Expected a JSON object, array or literal."),ga.Undefined)),new Wa(p,n,o)}function Ga(e,t,n){if(null!==e&&"object"==typeof e){var r=t+"\t";if(Array.isArray(e)){if(0===e.length)return"[]";for(var i="[\n",s=0;s<e.length;s++)i+=r+Ga(e[s],r,n),s<e.length-1&&(i+=","),i+="\n";return i+=t+"]"}var o=Object.keys(e);if(0===o.length)return"{}";for(i="{\n",s=0;s<o.length;s++){var a=o[s];i+=r+JSON.stringify(a)+": "+Ga(e[a],r,n),s<o.length-1&&(i+=","),i+="\n"}return i+=t+"}"}return n(e)}var Ja=La(),Xa=function(){function e(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=Promise),void 0===r&&(r={}),this.schemaService=e,this.contributions=t,this.promiseConstructor=n,this.clientCapabilities=r}return e.prototype.doResolve=function(e){for(var t=this.contributions.length-1;t>=0;t--){var n=this.contributions[t].resolveCompletion;if(n){var r=n(e);if(r)return r}}return this.promiseConstructor.resolve(e)},e.prototype.doComplete=function(e,t,n){var r=this,i={items:[],isIncomplete:!1},s=e.getText(),o=e.offsetAt(t),a=n.getNodeFromOffset(o,!0);if(this.isInComment(e,a?a.offset:0,o))return Promise.resolve(i);if(a&&o===a.offset+a.length&&o>0){var l=s[o-1];("object"===a.type&&"}"===l||"array"===a.type&&"]"===l)&&(a=a.parent)}var u,c=this.getCurrentWord(e,o);if(!a||"string"!==a.type&&"number"!==a.type&&"boolean"!==a.type&&"null"!==a.type){var h=o-c.length;h>0&&'"'===s[h-1]&&h--,u=ms.create(e.positionAt(h),t)}else u=ms.create(e.positionAt(a.offset),e.positionAt(a.offset+a.length));var d={},f={add:function(e){var t=e.label,n=d[t];if(n)n.documentation||(n.documentation=e.documentation),n.detail||(n.detail=e.detail);else{if((t=t.replace(/[\n]/g,"↵")).length>60){var r=t.substr(0,57).trim()+"...";d[r]||(t=r)}u&&void 0!==e.insertText&&(e.textEdit=qs.replace(u,e.insertText)),e.label=t,d[t]=e,i.items.push(e)}},setAsIncomplete:function(){i.isIncomplete=!0},error:function(e){},log:function(e){},getNumberOfProposals:function(){return i.items.length}};return this.schemaService.getSchemaForResource(e.uri,n).then((function(t){var l=[],h=!0,m="",g=void 0;if(a&&"string"===a.type){var p=a.parent;p&&"property"===p.type&&p.keyNode===a&&(h=!p.valueNode,g=p,m=s.substr(a.offset+1,a.length-2),p&&(a=p.parent))}if(a&&"object"===a.type){if(a.offset===o)return i;a.properties.forEach((function(e){g&&g===e||(d[e.keyNode.value]=Mo.create("__"))}));var v="";h&&(v=r.evaluateSeparatorAfter(e,e.offsetAt(u.end))),t?r.getPropertyCompletions(t,n,a,h,v,f):r.getSchemaLessPropertyCompletions(n,a,m,f);var b=Ua(a);r.contributions.forEach((function(t){var n=t.collectPropertyCompletions(e.uri,b,c,h,""===v,f);n&&l.push(n)})),!t&&c.length>0&&'"'!==s.charAt(o-c.length-1)&&(f.add({kind:Lo.Property,label:r.getLabelForValue(c),insertText:r.getInsertTextForProperty(c,void 0,!1,v),insertTextFormat:Eo.Snippet,documentation:""}),f.setAsIncomplete())}var y={};return t?r.getValueCompletions(t,n,a,o,e,f,y):r.getSchemaLessValueCompletions(n,a,o,e,f),r.contributions.length>0&&r.getContributedValueCompletions(n,a,o,e,f,l),r.promiseConstructor.all(l).then((function(){if(0===f.getNumberOfProposals()){var t=o;!a||"string"!==a.type&&"number"!==a.type&&"boolean"!==a.type&&"null"!==a.type||(t=a.offset+a.length);var n=r.evaluateSeparatorAfter(e,t);r.addFillerValueCompletions(y,n,f)}return i}))}))},e.prototype.getPropertyCompletions=function(e,t,n,r,i,s){var o=this;t.getMatchingSchemas(e.schema,n.offset).forEach((function(e){if(e.node===n&&!e.inverted){var t=e.schema.properties;t&&Object.keys(t).forEach((function(e){var n=t[e];if("object"==typeof n&&!n.deprecationMessage&&!n.doNotSuggest){var a={kind:Lo.Property,label:e,insertText:o.getInsertTextForProperty(e,n,r,i),insertTextFormat:Eo.Snippet,filterText:o.getFilterTextForValue(e),documentation:o.fromMarkup(n.markdownDescription)||n.description||""};void 0!==n.suggestSortText&&(a.sortText=n.suggestSortText),a.insertText&&co(a.insertText,"$1".concat(i))&&(a.command={title:"Suggest",command:"editor.action.triggerSuggest"}),s.add(a)}}));var a=e.schema.propertyNames;if("object"==typeof a&&!a.deprecationMessage&&!a.doNotSuggest){var l=function(e,t){void 0===t&&(t=void 0);var n={kind:Lo.Property,label:e,insertText:o.getInsertTextForProperty(e,void 0,r,i),insertTextFormat:Eo.Snippet,filterText:o.getFilterTextForValue(e),documentation:t||o.fromMarkup(a.markdownDescription)||a.description||""};void 0!==a.suggestSortText&&(n.sortText=a.suggestSortText),n.insertText&&co(n.insertText,"$1".concat(i))&&(n.command={title:"Suggest",command:"editor.action.triggerSuggest"}),s.add(n)};if(a.enum)for(var u=0;u<a.enum.length;u++){var c=void 0;a.markdownEnumDescriptions&&u<a.markdownEnumDescriptions.length?c=o.fromMarkup(a.markdownEnumDescriptions[u]):a.enumDescriptions&&u<a.enumDescriptions.length&&(c=a.enumDescriptions[u]),l(a.enum[u],c)}a.const&&l(a.const)}}}))},e.prototype.getSchemaLessPropertyCompletions=function(e,t,n,r){var i=this,s=function(e){e.properties.forEach((function(e){var t=e.keyNode.value;r.add({kind:Lo.Property,label:t,insertText:i.getInsertTextForValue(t,""),insertTextFormat:Eo.Snippet,filterText:i.getFilterTextForValue(t),documentation:""})}))};if(t.parent)if("property"===t.parent.type){var o=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e!==t.parent&&e.keyNode.value===o&&e.valueNode&&"object"===e.valueNode.type&&s(e.valueNode),!0}))}else"array"===t.parent.type&&t.parent.items.forEach((function(e){"object"===e.type&&e!==t&&s(e)}));else"object"===t.type&&r.add({kind:Lo.Property,label:"$schema",insertText:this.getInsertTextForProperty("$schema",void 0,!0,""),insertTextFormat:Eo.Snippet,documentation:"",filterText:this.getFilterTextForValue("$schema")})},e.prototype.getSchemaLessValueCompletions=function(e,t,n,r,i){var s=this,o=n;if(!t||"string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(o=t.offset+t.length,t=t.parent),!t)return i.add({kind:this.getSuggestionKind("object"),label:"Empty object",insertText:this.getInsertTextForValue({},""),insertTextFormat:Eo.Snippet,documentation:""}),void i.add({kind:this.getSuggestionKind("array"),label:"Empty array",insertText:this.getInsertTextForValue([],""),insertTextFormat:Eo.Snippet,documentation:""});var a=this.evaluateSeparatorAfter(r,o),l=function(e){e.parent&&!$a(e.parent,n,!0)&&i.add({kind:s.getSuggestionKind(e.type),label:s.getLabelTextForMatchingNode(e,r),insertText:s.getInsertTextForMatchingNode(e,r,a),insertTextFormat:Eo.Snippet,documentation:""}),"boolean"===e.type&&s.addBooleanValueCompletion(!e.value,a,i)};if("property"===t.type&&n>(t.colonOffset||0)){var u=t.valueNode;if(u&&(n>u.offset+u.length||"object"===u.type||"array"===u.type))return;var c=t.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===c&&e.valueNode&&l(e.valueNode),!0})),"$schema"===c&&t.parent&&!t.parent.parent&&this.addDollarSchemaCompletions(a,i)}if("array"===t.type)if(t.parent&&"property"===t.parent.type){var h=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===h&&e.valueNode&&"array"===e.valueNode.type&&e.valueNode.items.forEach(l),!0}))}else t.items.forEach(l)},e.prototype.getValueCompletions=function(e,t,n,r,i,s,o){var a=r,l=void 0,u=void 0;if(!n||"string"!==n.type&&"number"!==n.type&&"boolean"!==n.type&&"null"!==n.type||(a=n.offset+n.length,u=n,n=n.parent),n){if("property"===n.type&&r>(n.colonOffset||0)){var c=n.valueNode;if(c&&r>c.offset+c.length)return;l=n.keyNode.value,n=n.parent}if(n&&(void 0!==l||"array"===n.type)){for(var h=this.evaluateSeparatorAfter(i,a),d=0,f=t.getMatchingSchemas(e.schema,n.offset,u);d<f.length;d++){var m=f[d];if(m.node===n&&!m.inverted&&m.schema){if("array"===n.type&&m.schema.items)if(Array.isArray(m.schema.items)){var g=this.findItemAtOffset(n,i,r);g<m.schema.items.length&&this.addSchemaValueCompletions(m.schema.items[g],h,s,o)}else this.addSchemaValueCompletions(m.schema.items,h,s,o);if(void 0!==l){var p=!1;if(m.schema.properties)(_=m.schema.properties[l])&&(p=!0,this.addSchemaValueCompletions(_,h,s,o));if(m.schema.patternProperties&&!p)for(var v=0,b=Object.keys(m.schema.patternProperties);v<b.length;v++){var y=b[v],C=ho(y);if(null==C?void 0:C.test(l)){p=!0;var _=m.schema.patternProperties[y];this.addSchemaValueCompletions(_,h,s,o)}}if(m.schema.additionalProperties&&!p){_=m.schema.additionalProperties;this.addSchemaValueCompletions(_,h,s,o)}}}}"$schema"!==l||n.parent||this.addDollarSchemaCompletions(h,s),o.boolean&&(this.addBooleanValueCompletion(!0,h,s),this.addBooleanValueCompletion(!1,h,s)),o.null&&this.addNullValueCompletion(h,s)}}else this.addSchemaValueCompletions(e.schema,"",s,o)},e.prototype.getContributedValueCompletions=function(e,t,n,r,i,s){if(t){if("string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(t=t.parent),t&&"property"===t.type&&n>(t.colonOffset||0)){var o=t.keyNode.value,a=t.valueNode;if((!a||n<=a.offset+a.length)&&t.parent){var l=Ua(t.parent);this.contributions.forEach((function(e){var t=e.collectValueCompletions(r.uri,l,o,i);t&&s.push(t)}))}}}else this.contributions.forEach((function(e){var t=e.collectDefaultCompletions(r.uri,i);t&&s.push(t)}))},e.prototype.addSchemaValueCompletions=function(e,t,n,r){var i=this;"object"==typeof e&&(this.addEnumValueCompletions(e,t,n),this.addDefaultValueCompletions(e,t,n),this.collectTypes(e,r),Array.isArray(e.allOf)&&e.allOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.anyOf)&&e.anyOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.oneOf)&&e.oneOf.forEach((function(e){return i.addSchemaValueCompletions(e,t,n,r)})))},e.prototype.addDefaultValueCompletions=function(e,t,n,r){var i=this;void 0===r&&(r=0);var s=!1;if(lo(e.default)){for(var o=e.type,a=e.default,l=r;l>0;l--)a=[a],o="array";n.add({kind:this.getSuggestionKind(o),label:this.getLabelForValue(a),insertText:this.getInsertTextForValue(a,t),insertTextFormat:Eo.Snippet,detail:Ja("json.suggest.default","Default value")}),s=!0}Array.isArray(e.examples)&&e.examples.forEach((function(o){for(var a=e.type,l=o,u=r;u>0;u--)l=[l],a="array";n.add({kind:i.getSuggestionKind(a),label:i.getLabelForValue(l),insertText:i.getInsertTextForValue(l,t),insertTextFormat:Eo.Snippet}),s=!0})),Array.isArray(e.defaultSnippets)&&e.defaultSnippets.forEach((function(o){var a,l,u=e.type,c=o.body,h=o.label;if(lo(c)){e.type;for(var d=r;d>0;d--)c=[c];a=i.getInsertTextForSnippetValue(c,t),l=i.getFilterTextForSnippetValue(c),h=h||i.getLabelForSnippetValue(c)}else{if("string"!=typeof o.bodyText)return;var f="",m="",g="";for(d=r;d>0;d--)f=f+g+"[\n",m=m+"\n"+g+"]",g+="\t",u="array";a=f+g+o.bodyText.split("\n").join("\n"+g)+m+t,h=h||a,l=a.replace(/[\n]/g,"")}n.add({kind:i.getSuggestionKind(u),label:h,documentation:i.fromMarkup(o.markdownDescription)||o.description,insertText:a,insertTextFormat:Eo.Snippet,filterText:l}),s=!0})),!s&&"object"==typeof e.items&&!Array.isArray(e.items)&&r<5&&this.addDefaultValueCompletions(e.items,t,n,r+1)},e.prototype.addEnumValueCompletions=function(e,t,n){if(lo(e.const)&&n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(e.const),insertText:this.getInsertTextForValue(e.const,t),insertTextFormat:Eo.Snippet,documentation:this.fromMarkup(e.markdownDescription)||e.description}),Array.isArray(e.enum))for(var r=0,i=e.enum.length;r<i;r++){var s=e.enum[r],o=this.fromMarkup(e.markdownDescription)||e.description;e.markdownEnumDescriptions&&r<e.markdownEnumDescriptions.length&&this.doesSupportMarkdown()?o=this.fromMarkup(e.markdownEnumDescriptions[r]):e.enumDescriptions&&r<e.enumDescriptions.length&&(o=e.enumDescriptions[r]),n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(s),insertText:this.getInsertTextForValue(s,t),insertTextFormat:Eo.Snippet,documentation:o})}},e.prototype.collectTypes=function(e,t){if(!Array.isArray(e.enum)&&!lo(e.const)){var n=e.type;Array.isArray(n)?n.forEach((function(e){return t[e]=!0})):n&&(t[n]=!0)}},e.prototype.addFillerValueCompletions=function(e,t,n){e.object&&n.add({kind:this.getSuggestionKind("object"),label:"{}",insertText:this.getInsertTextForGuessedValue({},t),insertTextFormat:Eo.Snippet,detail:Ja("defaults.object","New object"),documentation:""}),e.array&&n.add({kind:this.getSuggestionKind("array"),label:"[]",insertText:this.getInsertTextForGuessedValue([],t),insertTextFormat:Eo.Snippet,detail:Ja("defaults.array","New array"),documentation:""})},e.prototype.addBooleanValueCompletion=function(e,t,n){n.add({kind:this.getSuggestionKind("boolean"),label:e?"true":"false",insertText:this.getInsertTextForValue(e,t),insertTextFormat:Eo.Snippet,documentation:""})},e.prototype.addNullValueCompletion=function(e,t){t.add({kind:this.getSuggestionKind("null"),label:"null",insertText:"null"+e,insertTextFormat:Eo.Snippet,documentation:""})},e.prototype.addDollarSchemaCompletions=function(e,t){var n=this;this.schemaService.getRegisteredSchemaIds((function(e){return"http"===e||"https"===e})).forEach((function(r){return t.add({kind:Lo.Module,label:n.getLabelForValue(r),filterText:n.getFilterTextForValue(r),insertText:n.getInsertTextForValue(r,e),insertTextFormat:Eo.Snippet,documentation:""})}))},e.prototype.getLabelForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForSnippetValue=function(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getLabelForSnippetValue=function(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getInsertTextForPlainText=function(e){return e.replace(/[\\\$\}]/g,"\\$&")},e.prototype.getInsertTextForValue=function(e,t){var n=JSON.stringify(e,null,"\t");return"{}"===n?"{$1}"+t:"[]"===n?"[$1]"+t:this.getInsertTextForPlainText(n+t)},e.prototype.getInsertTextForSnippetValue=function(e,t){return Ga(e,"",(function(e){return"string"==typeof e&&"^"===e[0]?e.substr(1):JSON.stringify(e)}))+t},e.prototype.getInsertTextForGuessedValue=function(e,t){switch(typeof e){case"object":return null===e?"${1:null}"+t:this.getInsertTextForValue(e,t);case"string":var n=JSON.stringify(e);return n=n.substr(1,n.length-2),'"${1:'+(n=this.getInsertTextForPlainText(n))+'}"'+t;case"number":case"boolean":return"${1:"+JSON.stringify(e)+"}"+t}return this.getInsertTextForValue(e,t)},e.prototype.getSuggestionKind=function(e){if(Array.isArray(e)){var t=e;e=t.length>0?t[0]:void 0}if(!e)return Lo.Value;switch(e){case"string":default:return Lo.Value;case"object":return Lo.Module;case"property":return Lo.Property}},e.prototype.getLabelTextForMatchingNode=function(e,t){switch(e.type){case"array":return"[]";case"object":return"{}";default:return t.getText().substr(e.offset,e.length)}},e.prototype.getInsertTextForMatchingNode=function(e,t,n){switch(e.type){case"array":return this.getInsertTextForValue([],n);case"object":return this.getInsertTextForValue({},n);default:var r=t.getText().substr(e.offset,e.length)+n;return this.getInsertTextForPlainText(r)}},e.prototype.getInsertTextForProperty=function(e,t,n,r){var i=this.getInsertTextForValue(e,"");if(!n)return i;var s,o=i+": ",a=0;if(t){if(Array.isArray(t.defaultSnippets)){if(1===t.defaultSnippets.length){var l=t.defaultSnippets[0].body;lo(l)&&(s=this.getInsertTextForSnippetValue(l,""))}a+=t.defaultSnippets.length}if(t.enum&&(s||1!==t.enum.length||(s=this.getInsertTextForGuessedValue(t.enum[0],"")),a+=t.enum.length),lo(t.default)&&(s||(s=this.getInsertTextForGuessedValue(t.default,"")),a++),Array.isArray(t.examples)&&t.examples.length&&(s||(s=this.getInsertTextForGuessedValue(t.examples[0],"")),a+=t.examples.length),0===a){var u=Array.isArray(t.type)?t.type[0]:t.type;switch(u||(t.properties?u="object":t.items&&(u="array")),u){case"boolean":s="$1";break;case"string":s='"$1"';break;case"object":s="{$1}";break;case"array":s="[$1]";break;case"number":case"integer":s="${1:0}";break;case"null":s="${1:null}";break;default:return i}}}return(!s||a>1)&&(s="$1"),o+s+r},e.prototype.getCurrentWord=function(e,t){for(var n=t-1,r=e.getText();n>=0&&-1===' \t\n\r\v":{[,]}'.indexOf(r.charAt(n));)n--;return r.substring(n+1,t)},e.prototype.evaluateSeparatorAfter=function(e,t){var n=to(e.getText(),!0);switch(n.setPosition(t),n.scan()){case 5:case 2:case 4:case 17:return"";default:return","}},e.prototype.findItemAtOffset=function(e,t,n){for(var r=to(t.getText(),!0),i=e.items,s=i.length-1;s>=0;s--){var o=i[s];if(n>o.offset+o.length)return r.setPosition(o.offset+o.length),5===r.scan()&&n>=r.getTokenOffset()+r.getTokenLength()?s+1:s;if(n>=o.offset)return s}return 0},e.prototype.isInComment=function(e,t,n){var r=to(e.getText(),!1);r.setPosition(t);for(var i=r.scan();17!==i&&r.getTokenOffset()+r.getTokenLength()<n;)i=r.scan();return(12===i||13===i)&&r.getTokenOffset()<=n},e.prototype.fromMarkup=function(e){if(e&&this.doesSupportMarkdown())return{kind:_o.Markdown,value:e}},e.prototype.doesSupportMarkdown=function(){if(!lo(this.supportsMarkdown)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsMarkdown=e&&e.completionItem&&Array.isArray(e.completionItem.documentationFormat)&&-1!==e.completionItem.documentationFormat.indexOf(_o.Markdown)}return this.supportsMarkdown},e.prototype.doesSupportsCommitCharacters=function(){if(!lo(this.supportsCommitCharacters)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsCommitCharacters=e&&e.completionItem&&!!e.completionItem.commitCharactersSupport}return this.supportsCommitCharacters},e}(),Qa=function(){function e(e,t,n){void 0===t&&(t=[]),this.schemaService=e,this.contributions=t,this.promise=n||Promise}return e.prototype.doHover=function(e,t,n){var r=e.offsetAt(t),i=n.getNodeFromOffset(r);if(!i||("object"===i.type||"array"===i.type)&&r>i.offset+1&&r<i.offset+i.length-1)return this.promise.resolve(null);var s=i;if("string"===i.type){var o=i.parent;if(o&&"property"===o.type&&o.keyNode===i&&!(i=o.valueNode))return this.promise.resolve(null)}for(var a=ms.create(e.positionAt(s.offset),e.positionAt(s.offset+s.length)),l=function(e){return{contents:e,range:a}},u=Ua(i),c=this.contributions.length-1;c>=0;c--){var h=this.contributions[c].getInfoContribution(e.uri,u);if(h)return h.then((function(e){return l(e)}))}return this.schemaService.getSchemaForResource(e.uri,n).then((function(e){if(e&&i){var t=n.getMatchingSchemas(e.schema,i.offset),r=void 0,s=void 0,o=void 0,a=void 0;t.every((function(e){if(e.node===i&&!e.inverted&&e.schema&&(r=r||e.schema.title,s=s||e.schema.markdownDescription||Za(e.schema.description),e.schema.enum)){var t=e.schema.enum.indexOf(Ba(i));e.schema.markdownEnumDescriptions?o=e.schema.markdownEnumDescriptions[t]:e.schema.enumDescriptions&&(o=Za(e.schema.enumDescriptions[t])),o&&"string"!=typeof(a=e.schema.enum[t])&&(a=JSON.stringify(a))}return!0}));var u="";return r&&(u=Za(r)),s&&(u.length>0&&(u+="\n\n"),u+=s),o&&(u.length>0&&(u+="\n\n"),u+="`".concat(function(e){if(-1!==e.indexOf("`"))return"`` "+e+" ``";return e}(a),"`: ").concat(o)),l([u])}return null}))},e}();function Za(e){if(e)return e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,"$1\n\n$3").replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}var Ya=La(),el=function(){function e(e,t){this.jsonSchemaService=e,this.promise=t,this.validationEnabled=!0}return e.prototype.configure=function(e){e&&(this.validationEnabled=!1!==e.validate,this.commentSeverity=e.allowComments?void 0:Ts.Error)},e.prototype.doValidation=function(e,t,n,r){var i=this;if(!this.validationEnabled)return this.promise.resolve([]);var s=[],o={},a=function(e){var t=e.range.start.line+" "+e.range.start.character+" "+e.message;o[t]||(o[t]=!0,s.push(e))},l=function(r){var o=(null==n?void 0:n.trailingCommas)?il(n.trailingCommas):Ts.Error,l=(null==n?void 0:n.comments)?il(n.comments):i.commentSeverity,u=(null==n?void 0:n.schemaValidation)?il(n.schemaValidation):Ts.Warning,c=(null==n?void 0:n.schemaRequest)?il(n.schemaRequest):Ts.Warning;if(r){if(r.errors.length&&t.root&&c){var h=t.root,d="object"===h.type?h.properties[0]:void 0;if(d&&"$schema"===d.keyNode.value){var f=d.valueNode||d,m=ms.create(e.positionAt(f.offset),e.positionAt(f.offset+f.length));a(Fs.create(m,r.errors[0],c,ga.SchemaResolveError))}else{m=ms.create(e.positionAt(h.offset),e.positionAt(h.offset+1));a(Fs.create(m,r.errors[0],c,ga.SchemaResolveError))}}else if(u){var g=t.validate(e,r.schema,u);g&&g.forEach(a)}nl(r.schema)&&(l=void 0),rl(r.schema)&&(o=void 0)}for(var p=0,v=t.syntaxErrors;p<v.length;p++){var b=v[p];if(b.code===ga.TrailingComma){if("number"!=typeof o)continue;b.severity=o}a(b)}if("number"==typeof l){var y=Ya("InvalidCommentToken","Comments are not permitted in JSON.");t.comments.forEach((function(e){a(Fs.create(e,y,l,ga.CommentNotPermitted))}))}return s};if(r){var u=r.id||"schemaservice://untitled/"+tl++;return this.jsonSchemaService.registerExternalSchema(u,[],r).getResolvedSchema().then((function(e){return l(e)}))}return this.jsonSchemaService.getSchemaForResource(e.uri,t).then((function(e){return l(e)}))},e.prototype.getLanguageStatus=function(e,t){return{schemas:this.jsonSchemaService.getSchemaURIsForResource(e.uri,t)}},e}(),tl=0;function nl(e){if(e&&"object"==typeof e){if(uo(e.allowComments))return e.allowComments;if(e.allOf)for(var t=0,n=e.allOf;t<n.length;t++){var r=nl(n[t]);if(uo(r))return r}}}function rl(e){if(e&&"object"==typeof e){if(uo(e.allowTrailingCommas))return e.allowTrailingCommas;var t=e;if(uo(t.allowsTrailingCommas))return t.allowsTrailingCommas;if(e.allOf)for(var n=0,r=e.allOf;n<r.length;n++){var i=rl(r[n]);if(uo(i))return i}}}function il(e){switch(e){case"error":return Ts.Error;case"warning":return Ts.Warning;case"ignore":return}}function sl(e){return e<48?0:e<=57?e-48:(e<97&&(e+=32),e>=97&&e<=102?e-97+10:0)}function ol(e){if("#"===e[0])switch(e.length){case 4:return{red:17*sl(e.charCodeAt(1))/255,green:17*sl(e.charCodeAt(2))/255,blue:17*sl(e.charCodeAt(3))/255,alpha:1};case 5:return{red:17*sl(e.charCodeAt(1))/255,green:17*sl(e.charCodeAt(2))/255,blue:17*sl(e.charCodeAt(3))/255,alpha:17*sl(e.charCodeAt(4))/255};case 7:return{red:(16*sl(e.charCodeAt(1))+sl(e.charCodeAt(2)))/255,green:(16*sl(e.charCodeAt(3))+sl(e.charCodeAt(4)))/255,blue:(16*sl(e.charCodeAt(5))+sl(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(16*sl(e.charCodeAt(1))+sl(e.charCodeAt(2)))/255,green:(16*sl(e.charCodeAt(3))+sl(e.charCodeAt(4)))/255,blue:(16*sl(e.charCodeAt(5))+sl(e.charCodeAt(6)))/255,alpha:(16*sl(e.charCodeAt(7))+sl(e.charCodeAt(8)))/255}}}var al=function(){function e(e){this.schemaService=e}return e.prototype.findDocumentSymbols=function(e,t,n){var r=this;void 0===n&&(n={resultLimit:Number.MAX_VALUE});var i=t.root;if(!i)return[];var s=n.resultLimit||Number.MAX_VALUE,o=e.uri;if(("vscode://defaultsettings/keybindings.json"===o||co(o.toLowerCase(),"/user/keybindings.json"))&&"array"===i.type){for(var a=[],l=0,u=i.items;l<u.length;l++){var c=u[l];if("object"===c.type)for(var h=0,d=c.properties;h<d.length;h++){var f=d[h];if("key"===f.keyNode.value&&f.valueNode){var m=ps.create(e.uri,ll(e,c));if(a.push({name:Ba(f.valueNode),kind:jo.Function,location:m}),--s<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(o),a}}}return a}for(var g=[{node:i,containerName:""}],p=0,v=!1,b=[],y=function(t,n){"array"===t.type?t.items.forEach((function(e){e&&g.push({node:e,containerName:n})})):"object"===t.type&&t.properties.forEach((function(t){var i=t.valueNode;if(i)if(s>0){s--;var o=ps.create(e.uri,ll(e,t)),a=n?n+"."+t.keyNode.value:t.keyNode.value;b.push({name:r.getKeyLabel(t),kind:r.getSymbolKind(i.type),location:o,containerName:n}),g.push({node:i,containerName:a})}else v=!0}))};p<g.length;){var C=g[p++];y(C.node,C.containerName)}return v&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(o),b},e.prototype.findDocumentSymbols2=function(e,t,n){var r=this;void 0===n&&(n={resultLimit:Number.MAX_VALUE});var i=t.root;if(!i)return[];var s=n.resultLimit||Number.MAX_VALUE,o=e.uri;if(("vscode://defaultsettings/keybindings.json"===o||co(o.toLowerCase(),"/user/keybindings.json"))&&"array"===i.type){for(var a=[],l=0,u=i.items;l<u.length;l++){var c=u[l];if("object"===c.type)for(var h=0,d=c.properties;h<d.length;h++){var f=d[h];if("key"===f.keyNode.value&&f.valueNode){var m=ll(e,c),g=ll(e,f.keyNode);if(a.push({name:Ba(f.valueNode),kind:jo.Function,range:m,selectionRange:g}),--s<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(o),a}}}return a}for(var p=[],v=[{node:i,result:p}],b=0,y=!1,C=function(t,n){"array"===t.type?t.items.forEach((function(t,i){if(t)if(s>0){s--;var o=ll(e,t),a=o,l={name:String(i),kind:r.getSymbolKind(t.type),range:o,selectionRange:a,children:[]};n.push(l),v.push({result:l.children,node:t})}else y=!0})):"object"===t.type&&t.properties.forEach((function(t){var i=t.valueNode;if(i)if(s>0){s--;var o=ll(e,t),a=ll(e,t.keyNode),l=[],u={name:r.getKeyLabel(t),kind:r.getSymbolKind(i.type),range:o,selectionRange:a,children:l,detail:r.getDetail(i)};n.push(u),v.push({result:l,node:i})}else y=!0}))};b<v.length;){var _=v[b++];C(_.node,_.result)}return y&&n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(o),p},e.prototype.getSymbolKind=function(e){switch(e){case"object":return jo.Module;case"string":return jo.String;case"number":return jo.Number;case"array":return jo.Array;case"boolean":return jo.Boolean;default:return jo.Variable}},e.prototype.getKeyLabel=function(e){var t=e.keyNode.value;return t&&(t=t.replace(/[\n]/g,"↵")),t&&t.trim()?t:'"'.concat(t,'"')},e.prototype.getDetail=function(e){if(e)return"boolean"===e.type||"number"===e.type||"null"===e.type||"string"===e.type?String(e.value):"array"===e.type?e.children.length?void 0:"[]":"object"===e.type?e.children.length?void 0:"{}":void 0},e.prototype.findDocumentColors=function(e,t,n){return this.schemaService.getSchemaForResource(e.uri,t).then((function(r){var i=[];if(r)for(var s=n&&"number"==typeof n.resultLimit?n.resultLimit:Number.MAX_VALUE,o={},a=0,l=t.getMatchingSchemas(r.schema);a<l.length;a++){var u=l[a];if(!u.inverted&&u.schema&&("color"===u.schema.format||"color-hex"===u.schema.format)&&u.node&&"string"===u.node.type){var c=String(u.node.offset);if(!o[c]){var h=ol(Ba(u.node));if(h){var d=ll(e,u.node);i.push({color:h,range:d})}if(o[c]=!0,--s<=0)return n&&n.onResultLimitExceeded&&n.onResultLimitExceeded(e.uri),i}}}return i}))},e.prototype.getColorPresentations=function(e,t,n,r){var i,s=[],o=Math.round(255*n.red),a=Math.round(255*n.green),l=Math.round(255*n.blue);function u(e){var t=e.toString(16);return 2!==t.length?"0"+t:t}return i=1===n.alpha?"#".concat(u(o)).concat(u(a)).concat(u(l)):"#".concat(u(o)).concat(u(a)).concat(u(l)).concat(u(Math.round(255*n.alpha))),s.push({label:i,textEdit:qs.replace(r,JSON.stringify(i))}),s},e}();function ll(e,t){return ms.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length))}var ul,cl,hl,dl,fl,ml,gl=La(),pl={schemaAssociations:[],schemas:{"http://json-schema.org/schema#":{$ref:"http://json-schema.org/draft-07/schema#"},"http://json-schema.org/draft-04/schema#":{$schema:"http://json-schema.org/draft-04/schema#",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},positiveInteger:{type:"integer",minimum:0},positiveIntegerDefault0:{allOf:[{$ref:"#/definitions/positiveInteger"},{default:0}]},simpleTypes:{type:"string",enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},minItems:1,uniqueItems:!0}},type:"object",properties:{id:{type:"string",format:"uri"},$schema:{type:"string",format:"uri"},title:{type:"string"},description:{type:"string"},default:{},multipleOf:{type:"number",minimum:0,exclusiveMinimum:!0},maximum:{type:"number"},exclusiveMaximum:{type:"boolean",default:!1},minimum:{type:"number"},exclusiveMinimum:{type:"boolean",default:!1},maxLength:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minLength:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},pattern:{type:"string",format:"regex"},additionalItems:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minItems:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},uniqueItems:{type:"boolean",default:!1},maxProperties:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minProperties:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},required:{allOf:[{$ref:"#/definitions/stringArray"}]},additionalProperties:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{anyOf:[{type:"string",enum:["date-time","uri","email","hostname","ipv4","ipv6","regex"]},{type:"string"}]},allOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},anyOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},oneOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},not:{allOf:[{$ref:"#"}]}},dependencies:{exclusiveMaximum:["maximum"],exclusiveMinimum:["minimum"]},default:{}},"http://json-schema.org/draft-07/schema#":{definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}}},vl={id:gl("schema.json.id","A unique identifier for the schema."),$schema:gl("schema.json.$schema","The schema to verify this document against."),title:gl("schema.json.title","A descriptive title of the element."),description:gl("schema.json.description","A long description of the element. Used in hover menus and suggestions."),default:gl("schema.json.default","A default value. Used by suggestions."),multipleOf:gl("schema.json.multipleOf","A number that should cleanly divide the current value (i.e. have no remainder)."),maximum:gl("schema.json.maximum","The maximum numerical value, inclusive by default."),exclusiveMaximum:gl("schema.json.exclusiveMaximum","Makes the maximum property exclusive."),minimum:gl("schema.json.minimum","The minimum numerical value, inclusive by default."),exclusiveMinimum:gl("schema.json.exclusiveMininum","Makes the minimum property exclusive."),maxLength:gl("schema.json.maxLength","The maximum length of a string."),minLength:gl("schema.json.minLength","The minimum length of a string."),pattern:gl("schema.json.pattern","A regular expression to match the string against. It is not implicitly anchored."),additionalItems:gl("schema.json.additionalItems","For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail."),items:gl("schema.json.items","For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on."),maxItems:gl("schema.json.maxItems","The maximum number of items that can be inside an array. Inclusive."),minItems:gl("schema.json.minItems","The minimum number of items that can be inside an array. Inclusive."),uniqueItems:gl("schema.json.uniqueItems","If all of the items in the array must be unique. Defaults to false."),maxProperties:gl("schema.json.maxProperties","The maximum number of properties an object can have. Inclusive."),minProperties:gl("schema.json.minProperties","The minimum number of properties an object can have. Inclusive."),required:gl("schema.json.required","An array of strings that lists the names of all properties required on this object."),additionalProperties:gl("schema.json.additionalProperties","Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail."),definitions:gl("schema.json.definitions","Not used for validation. Place subschemas here that you wish to reference inline with $ref."),properties:gl("schema.json.properties","A map of property names to schemas for each property."),patternProperties:gl("schema.json.patternProperties","A map of regular expressions on property names to schemas for matching properties."),dependencies:gl("schema.json.dependencies","A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object."),enum:gl("schema.json.enum","The set of literal values that are valid."),type:gl("schema.json.type","Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types."),format:gl("schema.json.format","Describes the format expected for the value."),allOf:gl("schema.json.allOf","An array of schemas, all of which must match."),anyOf:gl("schema.json.anyOf","An array of schemas, where at least one must match."),oneOf:gl("schema.json.oneOf","An array of schemas, exactly one of which must match."),not:gl("schema.json.not","A schema which must not match."),$id:gl("schema.json.$id","A unique identifier for the schema."),$ref:gl("schema.json.$ref","Reference a definition hosted on any location."),$comment:gl("schema.json.$comment","Comments from schema authors to readers or maintainers of the schema."),readOnly:gl("schema.json.readOnly","Indicates that the value of the instance is managed exclusively by the owning authority."),examples:gl("schema.json.examples","Sample JSON values associated with a particular schema, for the purpose of illustrating usage."),contains:gl("schema.json.contains",'An array instance is valid against "contains" if at least one of its elements is valid against the given schema.'),propertyNames:gl("schema.json.propertyNames","If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema."),const:gl("schema.json.const","An instance validates successfully against this keyword if its value is equal to the value of the keyword."),contentMediaType:gl("schema.json.contentMediaType","Describes the media type of a string property."),contentEncoding:gl("schema.json.contentEncoding","Describes the content encoding of a string property."),if:gl("schema.json.if",'The validation outcome of the "if" subschema controls which of the "then" or "else" keywords are evaluated.'),then:gl("schema.json.then",'The "if" subschema is used for validation when the "if" subschema succeeds.'),else:gl("schema.json.else",'The "else" subschema is used for validation when the "if" subschema fails.')};for(fl in pl.schemas)for(dl in(ul=pl.schemas[fl]).properties)"boolean"==typeof(cl=ul.properties[dl])&&(cl=ul.properties[dl]={}),(hl=vl[dl])&&(cl.description=hl);ml=(()=>{var e={470:e=>{function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,r="",i=0,s=-1,o=0,a=0;a<=e.length;++a){if(a<e.length)n=e.charCodeAt(a);else{if(47===n)break;n=47}if(47===n){if(s===a-1||1===o);else if(s!==a-1&&2===o){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var l=r.lastIndexOf("/");if(l!==r.length-1){-1===l?(r="",i=0):i=(r=r.slice(0,l)).length-1-r.lastIndexOf("/"),s=a,o=0;continue}}else if(2===r.length||1===r.length){r="",i=0,s=a,o=0;continue}t&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+e.slice(s+1,a):r=e.slice(s+1,a),i=a-s-1;s=a,o=0}else 46===n&&-1!==o?++o:o=-1}return r}var r={resolve:function(){for(var e,r="",i=!1,s=arguments.length-1;s>=-1&&!i;s--){var o;s>=0?o=arguments[s]:(void 0===e&&(e=process.cwd()),o=e),t(o),0!==o.length&&(r=o+"/"+r,i=47===o.charCodeAt(0))}return r=n(r,!i),i?r.length>0?"/"+r:"/":r.length>0?r:"."},normalize:function(e){if(t(e),0===e.length)return".";var r=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!r)).length||r||(e="."),e.length>0&&i&&(e+="/"),r?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var i=arguments[n];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":r.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=r.resolve(e))===(n=r.resolve(n)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var s=e.length,o=s-i,a=1;a<n.length&&47===n.charCodeAt(a);++a);for(var l=n.length-a,u=o<l?o:l,c=-1,h=0;h<=u;++h){if(h===u){if(l>u){if(47===n.charCodeAt(a+h))return n.slice(a+h+1);if(0===h)return n.slice(a+h)}else o>u&&(47===e.charCodeAt(i+h)?c=h:0===h&&(c=0));break}var d=e.charCodeAt(i+h);if(d!==n.charCodeAt(a+h))break;47===d&&(c=h)}var f="";for(h=i+c+1;h<=s;++h)h!==s&&47!==e.charCodeAt(h)||(0===f.length?f+="..":f+="/..");return f.length>0?f+n.slice(a+c):(a+=c,47===n.charCodeAt(a)&&++a,n.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),r=47===n,i=-1,s=!0,o=e.length-1;o>=1;--o)if(47===(n=e.charCodeAt(o))){if(!s){i=o;break}}else s=!1;return-1===i?r?"/":".":r&&1===i?"//":e.slice(0,i)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var r,i=0,s=-1,o=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var a=n.length-1,l=-1;for(r=e.length-1;r>=0;--r){var u=e.charCodeAt(r);if(47===u){if(!o){i=r+1;break}}else-1===l&&(o=!1,l=r+1),a>=0&&(u===n.charCodeAt(a)?-1==--a&&(s=r):(a=-1,s=l))}return i===s?s=l:-1===s&&(s=e.length),e.slice(i,s)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!o){i=r+1;break}}else-1===s&&(o=!1,s=r+1);return-1===s?"":e.slice(i,s)},extname:function(e){t(e);for(var n=-1,r=0,i=-1,s=!0,o=0,a=e.length-1;a>=0;--a){var l=e.charCodeAt(a);if(47!==l)-1===i&&(s=!1,i=a+1),46===l?-1===n?n=a:1!==o&&(o=1):-1!==n&&(o=-1);else if(!s){r=a+1;break}}return-1===n||-1===i||0===o||1===o&&n===i-1&&n===r+1?"":e.slice(n,i)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return n=(t=e).dir||t.root,r=t.base||(t.name||"")+(t.ext||""),n?n===t.root?n+r:n+"/"+r:r;var t,n,r},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var r,i=e.charCodeAt(0),s=47===i;s?(n.root="/",r=1):r=0;for(var o=-1,a=0,l=-1,u=!0,c=e.length-1,h=0;c>=r;--c)if(47!==(i=e.charCodeAt(c)))-1===l&&(u=!1,l=c+1),46===i?-1===o?o=c:1!==h&&(h=1):-1!==o&&(h=-1);else if(!u){a=c+1;break}return-1===o||-1===l||0===h||1===h&&o===l-1&&o===a+1?-1!==l&&(n.base=n.name=0===a&&s?e.slice(1,l):e.slice(a,l)):(0===a&&s?(n.name=e.slice(1,o),n.base=e.slice(1,l)):(n.name=e.slice(a,o),n.base=e.slice(a,l)),n.ext=e.slice(o,l)),a>0?n.dir=e.slice(0,a-1):s&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r,e.exports=r},447:(e,t,n)=>{var r;if(n.r(t),n.d(t,{URI:()=>g,Utils:()=>x}),"object"==typeof process)r="win32"===process.platform;else if("object"==typeof navigator){var i=navigator.userAgent;r=i.indexOf("Windows")>=0}var s,o,a=(s=function(e,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}s(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),l=/^\w[\w\d+.-]*$/,u=/^\//,c=/^\/\//;function h(e,t){if(!e.scheme&&t)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'.concat(e.authority,'", path: "').concat(e.path,'", query: "').concat(e.query,'", fragment: "').concat(e.fragment,'"}'));if(e.scheme&&!l.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!u.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(c.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}var d="",f="/",m=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,g=function(){function e(e,t,n,r,i,s){void 0===s&&(s=!1),"object"==typeof e?(this.scheme=e.scheme||d,this.authority=e.authority||d,this.path=e.path||d,this.query=e.query||d,this.fragment=e.fragment||d):(this.scheme=function(e,t){return e||t?e:"file"}(e,s),this.authority=t||d,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==f&&(t=f+t):t=f}return t}(this.scheme,n||d),this.query=r||d,this.fragment=i||d,h(this,s))}return e.isUri=function(t){return t instanceof e||!!t&&"string"==typeof t.authority&&"string"==typeof t.fragment&&"string"==typeof t.path&&"string"==typeof t.query&&"string"==typeof t.scheme&&"string"==typeof t.fsPath&&"function"==typeof t.with&&"function"==typeof t.toString},Object.defineProperty(e.prototype,"fsPath",{get:function(){return _(this)},enumerable:!1,configurable:!0}),e.prototype.with=function(e){if(!e)return this;var t=e.scheme,n=e.authority,r=e.path,i=e.query,s=e.fragment;return void 0===t?t=this.scheme:null===t&&(t=d),void 0===n?n=this.authority:null===n&&(n=d),void 0===r?r=this.path:null===r&&(r=d),void 0===i?i=this.query:null===i&&(i=d),void 0===s?s=this.fragment:null===s&&(s=d),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&s===this.fragment?this:new v(t,n,r,i,s)},e.parse=function(e,t){void 0===t&&(t=!1);var n=m.exec(e);return n?new v(n[2]||d,E(n[4]||d),E(n[5]||d),E(n[7]||d),E(n[9]||d),t):new v(d,d,d,d,d)},e.file=function(e){var t=d;if(r&&(e=e.replace(/\\/g,f)),e[0]===f&&e[1]===f){var n=e.indexOf(f,2);-1===n?(t=e.substring(2),e=f):(t=e.substring(2,n),e=e.substring(n)||f)}return new v("file",t,e,d,d)},e.from=function(e){var t=new v(e.scheme,e.authority,e.path,e.query,e.fragment);return h(t,!0),t},e.prototype.toString=function(e){return void 0===e&&(e=!1),w(this,e)},e.prototype.toJSON=function(){return this},e.revive=function(t){if(t){if(t instanceof e)return t;var n=new v(t);return n._formatted=t.external,n._fsPath=t._sep===p?t.fsPath:null,n}return t},e}(),p=r?1:void 0,v=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._formatted=null,t._fsPath=null,t}return a(t,e),Object.defineProperty(t.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=_(this)),this._fsPath},enumerable:!1,configurable:!0}),t.prototype.toString=function(e){return void 0===e&&(e=!1),e?w(this,!0):(this._formatted||(this._formatted=w(this,!1)),this._formatted)},t.prototype.toJSON=function(){var e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=p),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e},t}(g),b=((o={})[58]="%3A",o[47]="%2F",o[63]="%3F",o[35]="%23",o[91]="%5B",o[93]="%5D",o[64]="%40",o[33]="%21",o[36]="%24",o[38]="%26",o[39]="%27",o[40]="%28",o[41]="%29",o[42]="%2A",o[43]="%2B",o[44]="%2C",o[59]="%3B",o[61]="%3D",o[32]="%20",o);function y(e,t){for(var n=void 0,r=-1,i=0;i<e.length;i++){var s=e.charCodeAt(i);if(s>=97&&s<=122||s>=65&&s<=90||s>=48&&s<=57||45===s||46===s||95===s||126===s||t&&47===s)-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),void 0!==n&&(n+=e.charAt(i));else{void 0===n&&(n=e.substr(0,i));var o=b[s];void 0!==o?(-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),n+=o):-1===r&&(r=i)}}return-1!==r&&(n+=encodeURIComponent(e.substring(r))),void 0!==n?n:e}function C(e){for(var t=void 0,n=0;n<e.length;n++){var r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=b[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function _(e,t){var n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?"//".concat(e.authority).concat(e.path):47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?e.path[1].toLowerCase()+e.path.substr(2):e.path,r&&(n=n.replace(/\//g,"\\")),n}function w(e,t){var n=t?C:y,r="",i=e.scheme,s=e.authority,o=e.path,a=e.query,l=e.fragment;if(i&&(r+=i,r+=":"),(s||"file"===i)&&(r+=f,r+=f),s){var u=s.indexOf("@");if(-1!==u){var c=s.substr(0,u);s=s.substr(u+1),-1===(u=c.indexOf(":"))?r+=n(c,!1):(r+=n(c.substr(0,u),!1),r+=":",r+=n(c.substr(u+1),!1)),r+="@"}-1===(u=(s=s.toLowerCase()).indexOf(":"))?r+=n(s,!1):(r+=n(s.substr(0,u),!1),r+=s.substr(u))}if(o){if(o.length>=3&&47===o.charCodeAt(0)&&58===o.charCodeAt(2))(h=o.charCodeAt(1))>=65&&h<=90&&(o="/".concat(String.fromCharCode(h+32),":").concat(o.substr(3)));else if(o.length>=2&&58===o.charCodeAt(1)){var h;(h=o.charCodeAt(0))>=65&&h<=90&&(o="".concat(String.fromCharCode(h+32),":").concat(o.substr(2)))}r+=n(o,!0)}return a&&(r+="?",r+=n(a,!1)),l&&(r+="#",r+=t?l:y(l,!1)),r}function S(e){try{return decodeURIComponent(e)}catch(t){return e.length>3?e.substr(0,3)+S(e.substr(3)):e}}var L=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function E(e){return e.match(L)?e.replace(L,(function(e){return S(e)})):e}var x,N,A=n(470),k=function(e,t,n){if(2===arguments.length)for(var r,i=0,s=t.length;i<s;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))},R=A.posix||A;(N=x||(x={})).joinPath=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return e.with({path:R.join.apply(R,k([e.path],t,!1))})},N.resolvePath=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=e.path||"/";return e.with({path:R.resolve.apply(R,k([r],t,!1))})},N.dirname=function(e){var t=R.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)?e:e.with({path:t})},N.basename=function(e){return R.basename(e.path)},N.extname=function(e){return R.extname(e.path)}}},t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}return n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n(447)})();var{URI:bl,Utils:yl}=ml;function Cl(e,t){if("string"!=typeof e)throw new TypeError("Expected a string");for(var n,r=String(e),i="",s=!!t,o=!!t,a=!1,l=t&&"string"==typeof t.flags?t.flags:"",u=0,c=r.length;u<c;u++)switch(n=r[u]){case"/":case"$":case"^":case"+":case".":case"(":case")":case"=":case"!":case"|":i+="\\"+n;break;case"?":if(s){i+=".";break}case"[":case"]":if(s){i+=n;break}case"{":if(s){a=!0,i+="(";break}case"}":if(s){a=!1,i+=")";break}case",":if(a){i+="|";break}i+="\\"+n;break;case"*":for(var h=r[u-1],d=1;"*"===r[u+1];)d++,u++;var f=r[u+1];if(o)d>1&&("/"===h||void 0===h||"{"===h||","===h)&&("/"===f||void 0===f||","===f||"}"===f)?("/"===f?u++:"/"===h&&i.endsWith("\\/")&&(i=i.substr(0,i.length-2)),i+="((?:[^/]*(?:/|$))*)"):i+="([^/]*)";else i+=".*";break;default:i+=n}return l&&~l.indexOf("g")||(i="^"+i+"$"),new RegExp(i,l)}var _l,wl=La(),Sl=function(){function e(e,t){this.globWrappers=[];try{for(var n=0,r=e;n<r.length;n++){var i=r[n],s="!"!==i[0];s||(i=i.substring(1)),i.length>0&&("/"===i[0]&&(i=i.substring(1)),this.globWrappers.push({regexp:Cl("**/"+i,{extended:!0,globstar:!0}),include:s}))}this.uris=t}catch(Bl){this.globWrappers.length=0,this.uris=[]}}return e.prototype.matchesPattern=function(e){for(var t=!1,n=0,r=this.globWrappers;n<r.length;n++){var i=r[n],s=i.regexp,o=i.include;s.test(e)&&(t=o)}return t},e.prototype.getURIs=function(){return this.uris},e}(),Ll=function(){function e(e,t,n){this.service=e,this.uri=t,this.dependencies=new Set,this.anchors=void 0,n&&(this.unresolvedSchema=this.service.promise.resolve(new El(n)))}return e.prototype.getUnresolvedSchema=function(){return this.unresolvedSchema||(this.unresolvedSchema=this.service.loadSchema(this.uri)),this.unresolvedSchema},e.prototype.getResolvedSchema=function(){var e=this;return this.resolvedSchema||(this.resolvedSchema=this.getUnresolvedSchema().then((function(t){return e.service.resolveSchemaContent(t,e)}))),this.resolvedSchema},e.prototype.clearSchema=function(){var e=!!this.unresolvedSchema;return this.resolvedSchema=void 0,this.unresolvedSchema=void 0,this.dependencies.clear(),this.anchors=void 0,e},e}(),El=function(){return function(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t}}(),xl=function(){function e(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t}return e.prototype.getSection=function(e){var t=this.getSectionRecursive(e,this.schema);if(t)return Da(t)},e.prototype.getSectionRecursive=function(e,t){if(!t||"boolean"==typeof t||0===e.length)return t;var n=e.shift();if(t.properties&&(t.properties[n],1))return this.getSectionRecursive(e,t.properties[n]);if(t.patternProperties)for(var r=0,i=Object.keys(t.patternProperties);r<i.length;r++){var s=i[r],o=ho(s);if(null==o?void 0:o.test(n))return this.getSectionRecursive(e,t.patternProperties[s])}else{if("object"==typeof t.additionalProperties)return this.getSectionRecursive(e,t.additionalProperties);if(n.match("[0-9]+"))if(Array.isArray(t.items)){var a=parseInt(n,10);if(!isNaN(a)&&t.items[a])return this.getSectionRecursive(e,t.items[a])}else if(t.items)return this.getSectionRecursive(e,t.items)}},e}(),Nl=function(){function e(e,t,n){this.contextService=t,this.requestService=e,this.promiseConstructor=n||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations=[],this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={}}return e.prototype.getRegisteredSchemaIds=function(e){return Object.keys(this.registeredSchemasIds).filter((function(t){var n=bl.parse(t).scheme;return"schemaservice"!==n&&(!e||e(n))}))},Object.defineProperty(e.prototype,"promise",{get:function(){return this.promiseConstructor},enumerable:!1,configurable:!0}),e.prototype.dispose=function(){for(;this.callOnDispose.length>0;)this.callOnDispose.pop()()},e.prototype.onResourceChange=function(e){var t=this;this.cachedSchemaForResource=void 0;for(var n=!1,r=[e=kl(e)],i=Object.keys(this.schemasById).map((function(e){return t.schemasById[e]}));r.length;)for(var s=r.pop(),o=0;o<i.length;o++){var a=i[o];a&&(a.uri===s||a.dependencies.has(s))&&(a.uri!==s&&r.push(a.uri),a.clearSchema()&&(n=!0),i[o]=void 0)}return n},e.prototype.setSchemaContributions=function(e){if(e.schemas){var t=e.schemas;for(var n in t){var r=kl(n);this.contributionSchemas[r]=this.addSchemaHandle(r,t[n])}}if(Array.isArray(e.schemaAssociations))for(var i=0,s=e.schemaAssociations;i<s.length;i++){var o=s[i],a=o.uris.map(kl),l=this.addFilePatternAssociation(o.pattern,a);this.contributionAssociations.push(l)}},e.prototype.addSchemaHandle=function(e,t){var n=new Ll(this,e,t);return this.schemasById[e]=n,n},e.prototype.getOrAddSchemaHandle=function(e,t){return this.schemasById[e]||this.addSchemaHandle(e,t)},e.prototype.addFilePatternAssociation=function(e,t){var n=new Sl(e,t);return this.filePatternAssociations.push(n),n},e.prototype.registerExternalSchema=function(e,t,n){var r=kl(e);return this.registeredSchemasIds[r]=!0,this.cachedSchemaForResource=void 0,t&&this.addFilePatternAssociation(t,[r]),n?this.addSchemaHandle(r,n):this.getOrAddSchemaHandle(r)},e.prototype.clearExternalSchemas=function(){for(var e in this.schemasById={},this.filePatternAssociations=[],this.registeredSchemasIds={},this.cachedSchemaForResource=void 0,this.contributionSchemas)this.schemasById[e]=this.contributionSchemas[e],this.registeredSchemasIds[e]=!0;for(var t=0,n=this.contributionAssociations;t<n.length;t++){var r=n[t];this.filePatternAssociations.push(r)}},e.prototype.getResolvedSchema=function(e){var t=kl(e),n=this.schemasById[t];return n?n.getResolvedSchema():this.promise.resolve(void 0)},e.prototype.loadSchema=function(e){if(!this.requestService){var t=wl("json.schema.norequestservice","Unable to load schema from '{0}'. No schema request service available",Rl(e));return this.promise.resolve(new El({},[t]))}return this.requestService(e).then((function(t){if(!t){var n=wl("json.schema.nocontent","Unable to load schema from '{0}': No content.",Rl(e));return new El({},[n])}var r,i=[];r=no(t,i);var s=i.length?[wl("json.schema.invalidFormat","Unable to parse content from '{0}': Parse error at offset {1}.",Rl(e),i[0].offset)]:[];return new El(r,s)}),(function(t){var n=t.toString(),r=t.toString().split("Error: ");return r.length>1&&(n=r[1]),co(n,".")&&(n=n.substr(0,n.length-1)),new El({},[wl("json.schema.nocontent","Unable to load schema from '{0}': {1}.",Rl(e),n)])}))},e.prototype.resolveSchemaContent=function(e,t){var n=this,r=e.errors.slice(0),i=e.schema;if(i.$schema){var s=kl(i.$schema);if("http://json-schema.org/draft-03/schema"===s)return this.promise.resolve(new xl({},[wl("json.schema.draft03.notsupported","Draft-03 schemas are not supported.")]));"https://json-schema.org/draft/2019-09/schema"===s?r.push(wl("json.schema.draft201909.notsupported","Draft 2019-09 schemas are not yet fully supported.")):"https://json-schema.org/draft/2020-12/schema"===s&&r.push(wl("json.schema.draft202012.notsupported","Draft 2020-12 schemas are not yet fully supported."))}var o=this.contextService,a=function(e,t,n,i){var s,o,a,l;void 0===i||0===i.length?s=t:"/"===i.charAt(0)?s=function(e,t){t=decodeURIComponent(t);var n=e;return"/"===t[0]&&(t=t.substring(1)),t.split("/").some((function(e){return e=e.replace(/~1/g,"/").replace(/~0/g,"~"),!(n=n[e])})),n}(t,i):(o=t,l=i,(a=n).anchors||(a.anchors=c(o)),s=a.anchors.get(l)),s?function(e,t){for(var n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&"id"!==n&&"$id"!==n&&(e[n]=t[n])}(e,s):r.push(wl("json.schema.invalidid","$ref '{0}' in '{1}' can not be resolved.",i,n.uri))},l=function(e,t,i,s){o&&!/^[A-Za-z][A-Za-z0-9+\-.+]*:\/\/.*/.test(t)&&(t=o.resolveRelativePath(t,s.uri)),t=kl(t);var l=n.getOrAddSchemaHandle(t);return l.getUnresolvedSchema().then((function(n){if(s.dependencies.add(t),n.errors.length){var o=i?t+"#"+i:t;r.push(wl("json.schema.problemloadingref","Problems loading reference '{0}': {1}",o,n.errors[0]))}return a(e,n.schema,l,i),u(e,n.schema,l)}))},u=function(e,t,r){var i=[];return n.traverseNodes(e,(function(e){for(var n=new Set;e.$ref;){var s=e.$ref,o=s.split("#",2);if(delete e.$ref,o[0].length>0)return void i.push(l(e,o[0],o[1],r));if(!n.has(s)){var u=o[1];a(e,t,r,u),n.add(s)}}})),n.promise.all(i)},c=function(e){var t=new Map;return n.traverseNodes(e,(function(e){var n=e.$id||e.id;if("string"==typeof n&&"#"===n.charAt(0)){var i=n.substring(1);t.has(i)?r.push(wl("json.schema.duplicateid","Duplicate id declaration: '{0}'",n)):t.set(i,e)}})),t};return u(i,i,t).then((function(e){return new xl(i,r)}))},e.prototype.traverseNodes=function(e,t){if(!e||"object"!=typeof e)return Promise.resolve(null);for(var n=new Set,r=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];"object"==typeof i&&o.push(i)}},i=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];if("object"==typeof i)for(var s in i){var a=i[s];"object"==typeof a&&o.push(a)}}},s=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var i=r[n];if(Array.isArray(i))for(var s=0,a=i;s<a.length;s++){var l=a[s];"object"==typeof l&&o.push(l)}}},o=[e],a=o.pop();a;)n.has(a)||(n.add(a),t(a),r(a.items,a.additionalItems,a.additionalProperties,a.not,a.contains,a.propertyNames,a.if,a.then,a.else),i(a.definitions,a.properties,a.patternProperties,a.dependencies),s(a.anyOf,a.allOf,a.oneOf,a.items)),a=o.pop()},e.prototype.getSchemaFromProperty=function(e,t){var n,r;if("object"===(null===(n=t.root)||void 0===n?void 0:n.type))for(var i=0,s=t.root.properties;i<s.length;i++){var o=s[i];if("$schema"===o.keyNode.value&&"string"===(null===(r=o.valueNode)||void 0===r?void 0:r.type)){var a=o.valueNode.value;return this.contextService&&!/^\w[\w\d+.-]*:/.test(a)&&(a=this.contextService.resolveRelativePath(a,e)),a}}},e.prototype.getAssociatedSchemas=function(e){for(var t=Object.create(null),n=[],r=function(e){try{return bl.parse(e).with({fragment:null,query:null}).toString(!0)}catch(Bl){return e}}(e),i=0,s=this.filePatternAssociations;i<s.length;i++){var o=s[i];if(o.matchesPattern(r))for(var a=0,l=o.getURIs();a<l.length;a++){var u=l[a];t[u]||(n.push(u),t[u]=!0)}}return n},e.prototype.getSchemaURIsForResource=function(e,t){var n=t&&this.getSchemaFromProperty(e,t);return n?[n]:this.getAssociatedSchemas(e)},e.prototype.getSchemaForResource=function(e,t){if(t){var n=this.getSchemaFromProperty(e,t);if(n){var r=kl(n);return this.getOrAddSchemaHandle(r).getResolvedSchema()}}if(this.cachedSchemaForResource&&this.cachedSchemaForResource.resource===e)return this.cachedSchemaForResource.resolvedSchema;var i=this.getAssociatedSchemas(e),s=i.length>0?this.createCombinedSchema(e,i).getResolvedSchema():this.promise.resolve(void 0);return this.cachedSchemaForResource={resource:e,resolvedSchema:s},s},e.prototype.createCombinedSchema=function(e,t){if(1===t.length)return this.getOrAddSchemaHandle(t[0]);var n="schemaservice://combinedSchema/"+encodeURIComponent(e),r={allOf:t.map((function(e){return{$ref:e}}))};return this.addSchemaHandle(n,r)},e.prototype.getMatchingSchemas=function(e,t,n){if(n){var r=n.id||"schemaservice://untitled/matchingSchemas/"+Al++;return this.addSchemaHandle(r,n).getResolvedSchema().then((function(e){return t.getMatchingSchemas(e.schema).filter((function(e){return!e.inverted}))}))}return this.getSchemaForResource(e.uri,t).then((function(e){return e?t.getMatchingSchemas(e.schema).filter((function(e){return!e.inverted})):[]}))},e}(),Al=0;function kl(e){try{return bl.parse(e).toString(!0)}catch(Bl){return e}}function Rl(e){try{var t=bl.parse(e);if("file"===t.scheme)return t.fsPath}catch(Bl){}return e}function Tl(e,t){var n=[],r=[],i=[],s=-1,o=to(e.getText(),!1),a=o.scan();function l(e){n.push(e),r.push(i.length)}for(;17!==a;){switch(a){case 1:case 3:var u={startLine:d=e.positionAt(o.getTokenOffset()).line,endLine:d,kind:1===a?"object":"array"};i.push(u);break;case 2:case 4:var c=2===a?"object":"array";if(i.length>0&&i[i.length-1].kind===c){u=i.pop();var h=e.positionAt(o.getTokenOffset()).line;u&&h>u.startLine+1&&s!==u.startLine&&(u.endLine=h-1,l(u),s=u.startLine)}break;case 13:var d=e.positionAt(o.getTokenOffset()).line,f=e.positionAt(o.getTokenOffset()+o.getTokenLength()).line;1===o.getTokenError()&&d+1<e.lineCount?o.setPosition(e.offsetAt(ds.create(d+1,0))):d<f&&(l({startLine:d,endLine:f,kind:Es.Comment}),s=d);break;case 12:var m=e.getText().substr(o.getTokenOffset(),o.getTokenLength()).match(/^\/\/\s*#(region\b)|(endregion\b)/);if(m){h=e.positionAt(o.getTokenOffset()).line;if(m[1]){u={startLine:h,endLine:h,kind:Es.Region};i.push(u)}else{for(var g=i.length-1;g>=0&&i[g].kind!==Es.Region;)g--;if(g>=0){u=i[g];i.length=g,h>u.startLine&&s!==u.startLine&&(u.endLine=h,l(u),s=u.startLine)}}}}a=o.scan()}var p=t&&t.rangeLimit;if("number"!=typeof p||n.length<=p)return n;t&&t.onRangeLimitExceeded&&t.onRangeLimitExceeded(e.uri);for(var v=[],b=0,y=r;b<y.length;b++){(L=y[b])<30&&(v[L]=(v[L]||0)+1)}var C=0,_=0;for(g=0;g<v.length;g++){var w=v[g];if(w){if(w+C>p){_=g;break}C+=w}}var S=[];for(g=0;g<n.length;g++){var L;"number"==typeof(L=r[g])&&(L<_||L===_&&C++<p)&&S.push(n[g])}return S}function Ml(e,t,n){function r(t,n){return ms.create(e.positionAt(t),e.positionAt(n))}var i=to(e.getText(),!0);function s(e,t){return i.setPosition(e),i.scan()===t?i.getTokenOffset()+i.getTokenLength():-1}return t.map((function(t){for(var i=e.offsetAt(t),o=n.getNodeFromOffset(i,!0),a=[];o;){switch(o.type){case"string":case"object":case"array":var l=o.offset+1,u=o.offset+o.length-1;l<u&&i>=l&&i<=u&&a.push(r(l,u)),a.push(r(o.offset,o.offset+o.length));break;case"number":case"boolean":case"null":case"property":a.push(r(o.offset,o.offset+o.length))}if("property"===o.type||o.parent&&"array"===o.parent.type){var c=s(o.offset+o.length,5);-1!==c&&a.push(r(o.offset,c))}o=o.parent}for(var h=void 0,d=a.length-1;d>=0;d--)h=ia.create(a[d],h);return h||(h=ia.create(ms.create(t,t))),h}))}function Ol(e,t){var n=[];return t.visit((function(r){var i;if("property"===r.type&&"$ref"===r.keyNode.value&&"string"===(null===(i=r.valueNode)||void 0===i?void 0:i.type)){var s=r.valueNode.value,o=function(e,t){var n=function(e){if("#"===e)return[];if("#"!==e[0]||"/"!==e[1])return null;return e.substring(2).split(/\//).map(Fl)}(t);if(!n)return null;return Pl(n,e.root)}(t,s);if(o){var a=e.positionAt(o.offset);n.push({target:"".concat(e.uri,"#").concat(a.line+1,",").concat(a.character+1),range:Il(e,r.valueNode)})}}return!0})),Promise.resolve(n)}function Il(e,t){return ms.create(e.positionAt(t.offset+1),e.positionAt(t.offset+t.length-1))}function Pl(e,t){if(!t)return null;if(0===e.length)return t;var n=e.shift();if(t&&"object"===t.type){var r=t.properties.find((function(e){return e.keyNode.value===n}));return r?Pl(e,r.valueNode):null}if(t&&"array"===t.type&&n.match(/^(0|[1-9][0-9]*)$/)){var i=Number.parseInt(n),s=t.items[i];return s?Pl(e,s):null}return null}function Fl(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function Vl(e){var t=e.promiseConstructor||Promise,n=new Nl(e.schemaRequestService,e.workspaceContext,t);n.setSchemaContributions(pl);var r=new Xa(n,e.contributions,t,e.clientCapabilities),i=new Qa(n,e.contributions,t),s=new al(n),o=new el(n,t);return{configure:function(e){n.clearExternalSchemas(),e.schemas&&e.schemas.forEach((function(e){n.registerExternalSchema(e.uri,e.fileMatch,e.schema)})),o.configure(e)},resetSchema:function(e){return n.onResourceChange(e)},doValidation:o.doValidation.bind(o),getLanguageStatus:o.getLanguageStatus.bind(o),parseJSONDocument:function(e){return za(e,{collectComments:!0})},newJSONDocument:function(e,t){return function(e,t){return void 0===t&&(t=[]),new Wa(e,t,[])}(e,t)},getMatchingSchemas:n.getMatchingSchemas.bind(n),doResolve:r.doResolve.bind(r),doComplete:r.doComplete.bind(r),findDocumentSymbols:s.findDocumentSymbols.bind(s),findDocumentSymbols2:s.findDocumentSymbols2.bind(s),findDocumentColors:s.findDocumentColors.bind(s),getColorPresentations:s.getColorPresentations.bind(s),doHover:i.doHover.bind(i),getFoldingRanges:Tl,getSelectionRanges:Ml,findDefinition:function(){return Promise.resolve([])},findLinks:Ol,format:function(e,t,n){var r=void 0;if(t){var i=e.offsetAt(t.start);r={offset:i,length:e.offsetAt(t.end)-i}}var s={tabSize:n?n.tabSize:4,insertSpaces:!0===(null==n?void 0:n.insertSpaces),insertFinalNewline:!0===(null==n?void 0:n.insertFinalNewline),eol:"\n"};return function(e,t,n){return is(e,t,n)}(e.getText(),r,s).map((function(t){return qs.replace(ms.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length)),t.content)}))}}}"undefined"!=typeof fetch&&(_l=function(e){return fetch(e).then((e=>e.text()))});var Dl=class{constructor(e,t){this._ctx=e,this._languageSettings=t.languageSettings,this._languageId=t.languageId,this._languageService=Vl({workspaceContext:{resolveRelativePath:(e,t)=>function(e,t){if(function(e){return e.charCodeAt(0)===ql}(t)){const n=bl.parse(e),r=t.split("/");return n.with({path:jl(r)}).toString()}return function(e,...t){const n=bl.parse(e),r=n.path.split("/");for(let i of t)r.push(...i.split("/"));return n.with({path:jl(r)}).toString()}(e,t)}(t.substr(0,t.lastIndexOf("/")+1),e)},schemaRequestService:t.enableSchemaRequest?_l:void 0,clientCapabilities:va.LATEST}),this._languageService.configure(this._languageSettings)}async doValidation(e){let t=this._getTextDocument(e);if(t){let e=this._languageService.parseJSONDocument(t);return this._languageService.doValidation(t,e,this._languageSettings)}return Promise.resolve([])}async doComplete(e,t){let n=this._getTextDocument(e);if(!n)return null;let r=this._languageService.parseJSONDocument(n);return this._languageService.doComplete(n,t,r)}async doResolve(e){return this._languageService.doResolve(e)}async doHover(e,t){let n=this._getTextDocument(e);if(!n)return null;let r=this._languageService.parseJSONDocument(n);return this._languageService.doHover(n,t,r)}async format(e,t,n){let r=this._getTextDocument(e);if(!r)return[];let i=this._languageService.format(r,t,n);return Promise.resolve(i)}async resetSchema(e){return Promise.resolve(this._languageService.resetSchema(e))}async findDocumentSymbols(e){let t=this._getTextDocument(e);if(!t)return[];let n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentSymbols2(t,n);return Promise.resolve(r)}async findDocumentColors(e){let t=this._getTextDocument(e);if(!t)return[];let n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentColors(t,n);return Promise.resolve(r)}async getColorPresentations(e,t,n){let r=this._getTextDocument(e);if(!r)return[];let i=this._languageService.parseJSONDocument(r),s=this._languageService.getColorPresentations(r,i,t,n);return Promise.resolve(s)}async getFoldingRanges(e,t){let n=this._getTextDocument(e);if(!n)return[];let r=this._languageService.getFoldingRanges(n,t);return Promise.resolve(r)}async getSelectionRanges(e,t){let n=this._getTextDocument(e);if(!n)return[];let r=this._languageService.parseJSONDocument(n),i=this._languageService.getSelectionRanges(n,t,r);return Promise.resolve(i)}async parseJSONDocument(e){let t=this._getTextDocument(e);if(!t)return null;let n=this._languageService.parseJSONDocument(t);return Promise.resolve(n)}async getMatchingSchemas(e){let t=this._getTextDocument(e);if(!t)return[];let n=this._languageService.parseJSONDocument(t);return Promise.resolve(this._languageService.getMatchingSchemas(t,n))}_getTextDocument(e){let t=this._ctx.getMirrorModels();for(let n of t)if(n.uri.toString()===e)return fa.create(e,this._languageId,n.version,n.getValue());return null}},ql="/".charCodeAt(0),Kl=".".charCodeAt(0);function jl(e){const t=[];for(const r of e)0===r.length||1===r.length&&r.charCodeAt(0)===Kl||(2===r.length&&r.charCodeAt(0)===Kl&&r.charCodeAt(1)===Kl?t.pop():t.push(r));e.length>1&&0===e[e.length-1].length&&t.push("");let n=t.join("/");return 0===e[0].length&&(n="/"+n),n}self.onmessage=()=>{Yi(((e,t)=>new Dl(e,t)))}}();
