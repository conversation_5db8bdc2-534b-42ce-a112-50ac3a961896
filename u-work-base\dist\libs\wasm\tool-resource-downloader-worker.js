var k;k||=typeof ResourceDownloaderWorkerModule != 'undefined' ? ResourceDownloaderWorkerModule : {};var aa=Object.assign({},k),ba=[],ca="./this.program",da=(a,b)=>{throw b;},t="",ea,fa;t=self.location.href;t=t.startsWith("blob:")?"":t.substr(0,t.replace(/[?#].*/,"").lastIndexOf("/")+1);fa=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.responseType="arraybuffer";b.send(null);return new Uint8Array(b.response)};ea=a=>fetch(a,{credentials:"same-origin"}).then(b=>b.ok?b.arrayBuffer():Promise.reject(Error(b.status+" : "+b.url)));
var ha=k.print||console.log.bind(console),w=k.printErr||console.error.bind(console);Object.assign(k,aa);aa=null;k.arguments&&(ba=k.arguments);k.thisProgram&&(ca=k.thisProgram);k.quit&&(da=k.quit);var ia;k.wasmBinary&&(ia=k.wasmBinary);var ja,x=!1,ka,z,C,D,la,E,F,ma,na;
function oa(){var a=ja.buffer;k.HEAP8=z=new Int8Array(a);k.HEAP16=D=new Int16Array(a);k.HEAPU8=C=new Uint8Array(a);k.HEAPU16=la=new Uint16Array(a);k.HEAP32=E=new Int32Array(a);k.HEAPU32=F=new Uint32Array(a);k.HEAPF32=ma=new Float32Array(a);k.HEAPF64=na=new Float64Array(a)}var pa=[],qa=[],ra=[],sa=[],ta=!1;function ua(){var a=k.preRun.shift();pa.unshift(a)}var G=0,va=null,wa=null;
function xa(a){k.onAbort?.(a);a="Aborted("+a+")";w(a);x=!0;ka=1;throw new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");}var ya=a=>a.startsWith("data:application/octet-stream;base64,"),za;function Aa(a){if(a==za&&ia)return new Uint8Array(ia);if(fa)return fa(a);throw"both async and sync fetching of the wasm failed";}function Ba(a){return ia?Promise.resolve().then(()=>Aa(a)):ea(a).then(b=>new Uint8Array(b),()=>Aa(a))}
function Ca(a,b,c){return Ba(a).then(d=>WebAssembly.instantiate(d,b)).then(c,d=>{w(`failed to asynchronously prepare wasm: ${d}`);xa(d)})}function Da(a,b){var c=za;ia||"function"!=typeof WebAssembly.instantiateStreaming||ya(c)||"function"!=typeof fetch?Ca(c,a,b):fetch(c,{credentials:"same-origin"}).then(d=>WebAssembly.instantiateStreaming(d,a).then(b,function(e){w(`wasm streaming compile failed: ${e}`);w("falling back to ArrayBuffer instantiation");return Ca(c,a,b)}))}
var H,Ea={1102124:(a,b)=>{a=a?I(C,a):"";b=b?I(C,b):"";window[a]&&delete window[a][b]},1102268:()=>{function a(c){b||=c;k.Ib.update(c-b);requestAnimationFrame(a);b=c}let b=0;requestAnimationFrame(a)}};function Fa(a){this.name="ExitStatus";this.message=`Program terminated with exit(${a})`;this.status=a}var Ga=a=>{for(;0<a.length;)a.shift()(k)},Ha=k.noExitRuntime||!0;class Ia{constructor(a){this.wb=a;this.Ja=a-24}}
var Ja=0,Ka=0,La="undefined"!=typeof TextDecoder?new TextDecoder:void 0,I=(a,b,c)=>{var d=b+c;for(c=b;a[c]&&!(c>=d);)++c;if(16<c-b&&a.buffer&&La)return La.decode(a.subarray(b,c));for(d="";b<c;){var e=a[b++];if(e&128){var f=a[b++]&63;if(192==(e&224))d+=String.fromCharCode((e&31)<<6|f);else{var g=a[b++]&63;e=224==(e&240)?(e&15)<<12|f<<6|g:(e&7)<<18|f<<12|g<<6|a[b++]&63;65536>e?d+=String.fromCharCode(e):(e-=65536,d+=String.fromCharCode(55296|e>>10,56320|e&1023))}}else d+=String.fromCharCode(e)}return d},
J=(a,b)=>Object.defineProperty(b,"name",{value:a}),Ma=[],K=[],L,Na=a=>{throw new L(a);},M=a=>{if(!a)throw new L("Cannot use deleted val. handle = "+a);return K[a]},N=a=>{switch(a){case void 0:return 2;case null:return 4;case !0:return 6;case !1:return 8;default:const b=Ma.pop()||K.length;K[b]=a;K[b+1]=1;return b}},Oa=a=>{var b=Error,c=J(a,function(d){this.name=a;this.message=d;d=Error(d).stack;void 0!==d&&(this.stack=this.toString()+"\n"+d.replace(/^Error(:[^\n]*)?\n/,""))});c.prototype=Object.create(b.prototype);
c.prototype.constructor=c;c.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`};return c},Pa,Qa,O=a=>{for(var b="";C[a];)b+=Qa[C[a++]];return b},Ra=[],Sa=()=>{for(;Ra.length;){var a=Ra.pop();a.za.Va=!1;a["delete"]()}},Ta,Q={},Ua=(a,b)=>{if(void 0===b)throw new L("ptr should not be undefined");for(;a.Ma;)b=a.Ya(b),a=a.Ma;return b},R={},Wa=a=>{a=Va(a);var b=O(a);S(a);return b},Xa=(a,b)=>{var c=R[a];if(void 0===c)throw a=`${b} has unknown type ${Wa(a)}`,
new L(a);return c},Ya=()=>{},Za=!1,$a=(a,b,c)=>{if(b===c)return a;if(void 0===c.Ma)return null;a=$a(a,b,c.Ma);return null===a?null:c.vb(a)},ab={},bb=(a,b)=>{b=Ua(a,b);return Q[b]},cb,eb=(a,b)=>{if(!b.La||!b.Ja)throw new cb("makeClassHandle requires ptr and ptrType");if(!!b.Ra!==!!b.Na)throw new cb("Both smartPtrType and smartPtr must be specified");b.count={value:1};return db(Object.create(a,{za:{value:b,writable:!0}}))},db=a=>{if("undefined"===typeof FinalizationRegistry)return db=b=>b,a;Za=new FinalizationRegistry(b=>
{b=b.za;--b.count.value;0===b.count.value&&(b.Na?b.Ra.Qa(b.Na):b.La.Ka.Qa(b.Ja))});db=b=>{var c=b.za;c.Na&&Za.register(b,{za:c},b);return b};Ya=b=>{Za.unregister(b)};return db(a)},fb={},gb=a=>{for(;a.length;){var b=a.pop();a.pop()(b)}};function hb(a){return this.fromWireType(F[a>>2])}
var ib={},jb={},U=(a,b,c)=>{function d(h){h=c(h);if(h.length!==a.length)throw new cb("Mismatched type converter count");for(var m=0;m<a.length;++m)T(a[m],h[m])}a.forEach(function(h){jb[h]=b});var e=Array(b.length),f=[],g=0;b.forEach((h,m)=>{R.hasOwnProperty(h)?e[m]=R[h]:(f.push(h),ib.hasOwnProperty(h)||(ib[h]=[]),ib[h].push(()=>{e[m]=R[h];++g;g===f.length&&d(e)}))});0===f.length&&d(e)},kb={};
function lb(a,b,c={}){var d=b.name;if(!a)throw new L(`type "${d}" must have a positive integer typeid pointer`);if(R.hasOwnProperty(a)){if(c.Bb)return;throw new L(`Cannot register type '${d}' twice`);}R[a]=b;delete jb[a];ib.hasOwnProperty(a)&&(b=ib[a],delete ib[a],b.forEach(e=>e()))}function T(a,b,c={}){if(!("argPackAdvance"in b))throw new TypeError("registerType registeredInstance requires argPackAdvance");return lb(a,b,c)}var mb=a=>{throw new L(a.za.La.Ka.name+" instance already deleted");};
function nb(){}
var ob=(a,b,c)=>{if(void 0===a[b].Pa){var d=a[b];a[b]=function(...e){if(!a[b].Pa.hasOwnProperty(e.length))throw new L(`Function '${c}' called with an invalid number of arguments (${e.length}) - expects one of (${a[b].Pa})!`);return a[b].Pa[e.length].apply(this,e)};a[b].Pa=[];a[b].Pa[d.Ua]=d}},pb=(a,b)=>{if(k.hasOwnProperty(a))throw new L(`Cannot register public name '${a}' twice`);k[a]=b},qb=a=>{if(void 0===a)return"_unknown";a=a.replace(/[^a-zA-Z0-9_]/g,"$");var b=a.charCodeAt(0);return 48<=b&&57>=
b?`_${a}`:a};function rb(a,b,c,d,e,f,g,h){this.name=a;this.constructor=b;this.Sa=c;this.Qa=d;this.Ma=e;this.yb=f;this.Ya=g;this.vb=h;this.rb=[]}var sb=(a,b,c)=>{for(;b!==c;){if(!b.Ya)throw new L(`Expected null or instance of ${c.name}, got an instance of ${b.name}`);a=b.Ya(a);b=b.Ma}return a};
function tb(a,b){if(null===b){if(this.ib)throw new L(`null is not a valid ${this.name}`);return 0}if(!b.za)throw new L(`Cannot pass "${ub(b)}" as a ${this.name}`);if(!b.za.Ja)throw new L(`Cannot pass deleted object as a pointer of type ${this.name}`);return sb(b.za.Ja,b.za.La.Ka,this.Ka)}
function wb(a,b){if(null===b){if(this.ib)throw new L(`null is not a valid ${this.name}`);if(this.bb){var c=this.Xa();null!==a&&a.push(this.Qa,c);return c}return 0}if(!b||!b.za)throw new L(`Cannot pass "${ub(b)}" as a ${this.name}`);if(!b.za.Ja)throw new L(`Cannot pass deleted object as a pointer of type ${this.name}`);if(!this.ab&&b.za.La.ab)throw new L(`Cannot convert argument of type ${b.za.Ra?b.za.Ra.name:b.za.La.name} to parameter type ${this.name}`);c=sb(b.za.Ja,b.za.La.Ka,this.Ka);if(this.bb){if(void 0===
b.za.Na)throw new L("Passing raw pointer to smart pointer is illegal");switch(this.Fb){case 0:if(b.za.Ra===this)c=b.za.Na;else throw new L(`Cannot convert argument of type ${b.za.Ra?b.za.Ra.name:b.za.La.name} to parameter type ${this.name}`);break;case 1:c=b.za.Na;break;case 2:if(b.za.Ra===this)c=b.za.Na;else{var d=b.clone();c=this.Eb(c,N(()=>d["delete"]()));null!==a&&a.push(this.Qa,c)}break;default:throw new L("Unsupporting sharing policy");}}return c}
function xb(a,b){if(null===b){if(this.ib)throw new L(`null is not a valid ${this.name}`);return 0}if(!b.za)throw new L(`Cannot pass "${ub(b)}" as a ${this.name}`);if(!b.za.Ja)throw new L(`Cannot pass deleted object as a pointer of type ${this.name}`);if(b.za.La.ab)throw new L(`Cannot convert argument of type ${b.za.La.name} to parameter type ${this.name}`);return sb(b.za.Ja,b.za.La.Ka,this.Ka)}
function yb(a,b,c,d,e,f,g,h,m,l,n){this.name=a;this.Ka=b;this.ib=c;this.ab=d;this.bb=e;this.Db=f;this.Fb=g;this.sb=h;this.Xa=m;this.Eb=l;this.Qa=n;e||void 0!==b.Ma?this.toWireType=wb:(this.toWireType=d?tb:xb,this.Oa=null)}
var zb=(a,b)=>{if(!k.hasOwnProperty(a))throw new cb("Replacing nonexistent public symbol");k[a]=b;k[a].Ua=void 0},Ab=[],Bb,V=a=>{var b=Ab[a];b||(a>=Ab.length&&(Ab.length=a+1),Ab[a]=b=Bb.get(a));return b},Cb=(a,b,c=[])=>{a.includes("j")?(a=a.replace(/p/g,"i"),b=(0,k["dynCall_"+a])(b,...c)):b=V(b)(...c);return b},Db=(a,b)=>(...c)=>Cb(a,b,c),W=(a,b)=>{a=O(a);var c=a.includes("j")?Db(a,b):V(b);if("function"!=typeof c)throw new L(`unknown function pointer with signature ${a}: ${b}`);return c},Eb,Fb=(a,
b)=>{function c(f){e[f]||R[f]||(jb[f]?jb[f].forEach(c):(d.push(f),e[f]=!0))}var d=[],e={};b.forEach(c);throw new Eb(`${a}: `+d.map(Wa).join([", "]));};function Gb(a){for(var b=1;b<a.length;++b)if(null!==a[b]&&void 0===a[b].Oa)return!0;return!1}
function Hb(a){var b=Function;if(!(b instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof b} which is not a function`);var c=J(b.name||"unknownFunctionName",function(){});c.prototype=b.prototype;c=new c;a=b.apply(c,a);return a instanceof Object?a:c}
function Ib(a,b,c,d,e,f){var g=b.length;if(2>g)throw new L("argTypes array size mismatch! Must at least get return value and 'this' types!");var h=null!==b[1]&&null!==c,m=Gb(b);c="void"!==b[0].name;d=[a,Na,d,e,gb,b[0],b[1]];for(e=0;e<g-2;++e)d.push(b[e+2]);if(!m)for(e=h?1:2;e<b.length;++e)null!==b[e].Oa&&d.push(b[e].Oa);m=Gb(b);e=b.length;var l="",n="";for(g=0;g<e-2;++g)l+=(0!==g?", ":"")+"arg"+g,n+=(0!==g?", ":"")+"arg"+g+"Wired";l=`\n        return function (${l}) {\n        if (arguments.length !== ${e-
2}) {\n          throwBindingError('function ' + humanName + ' called with ' + arguments.length + ' arguments, expected ${e-2}');\n        }`;m&&(l+="var destructors = [];\n");var q=m?"destructors":"null",v="humanName throwBindingError invoker fn runDestructors retType classParam".split(" ");h&&(l+="var thisWired = classParam['toWireType']("+q+", this);\n");for(g=0;g<e-2;++g)l+="var arg"+g+"Wired = argType"+g+"['toWireType']("+q+", arg"+g+");\n",v.push("argType"+g);h&&(n="thisWired"+(0<n.length?", ":
"")+n);l+=(c||f?"var rv = ":"")+"invoker(fn"+(0<n.length?", ":"")+n+");\n";if(m)l+="runDestructors(destructors);\n";else for(g=h?1:2;g<b.length;++g)f=1===g?"thisWired":"arg"+(g-2)+"Wired",null!==b[g].Oa&&(l+=`${f}_dtor(${f});\n`,v.push(`${f}_dtor`));c&&(l+="var ret = retType['fromWireType'](rv);\nreturn ret;\n");let [y,u]=[v,l+"}\n"];y.push(u);b=Hb(y)(...d);return J(a,b)}
var Jb=(a,b)=>{for(var c=[],d=0;d<a;d++)c.push(F[b+4*d>>2]);return c},Kb=a=>{a=a.trim();const b=a.indexOf("(");return-1!==b?a.substr(0,b):a},Lb=a=>{9<a&&0===--K[a+1]&&(K[a]=void 0,Ma.push(a))},Mb={name:"emscripten::val",fromWireType:a=>{var b=M(a);Lb(a);return b},toWireType:(a,b)=>N(b),argPackAdvance:8,readValueFromPointer:hb,Oa:null},Nb=(a,b,c)=>{switch(b){case 1:return c?function(d){return this.fromWireType(z[d])}:function(d){return this.fromWireType(C[d])};case 2:return c?function(d){return this.fromWireType(D[d>>
1])}:function(d){return this.fromWireType(la[d>>1])};case 4:return c?function(d){return this.fromWireType(E[d>>2])}:function(d){return this.fromWireType(F[d>>2])};default:throw new TypeError(`invalid integer width (${b}): ${a}`);}},ub=a=>{if(null===a)return"null";var b=typeof a;return"object"===b||"array"===b||"function"===b?a.toString():""+a},Ob=(a,b)=>{switch(b){case 4:return function(c){return this.fromWireType(ma[c>>2])};case 8:return function(c){return this.fromWireType(na[c>>3])};default:throw new TypeError(`invalid float width (${b}): ${a}`);
}},Pb=(a,b,c)=>{switch(b){case 1:return c?d=>z[d]:d=>C[d];case 2:return c?d=>D[d>>1]:d=>la[d>>1];case 4:return c?d=>E[d>>2]:d=>F[d>>2];default:throw new TypeError(`invalid integer width (${b}): ${a}`);}},X=(a,b,c,d)=>{if(!(0<d))return 0;var e=c;d=c+d-1;for(var f=0;f<a.length;++f){var g=a.charCodeAt(f);if(55296<=g&&57343>=g){var h=a.charCodeAt(++f);g=65536+((g&1023)<<10)|h&1023}if(127>=g){if(c>=d)break;b[c++]=g}else{if(2047>=g){if(c+1>=d)break;b[c++]=192|g>>6}else{if(65535>=g){if(c+2>=d)break;b[c++]=
224|g>>12}else{if(c+3>=d)break;b[c++]=240|g>>18;b[c++]=128|g>>12&63}b[c++]=128|g>>6&63}b[c++]=128|g&63}}b[c]=0;return c-e},Qb=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);127>=d?b++:2047>=d?b+=2:55296<=d&&57343>=d?(b+=4,++c):b+=3}return b},Rb="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,Sb=(a,b)=>{var c=a>>1;for(var d=c+b/2;!(c>=d)&&la[c];)++c;c<<=1;if(32<c-a&&Rb)return Rb.decode(C.subarray(a,c));c="";for(d=0;!(d>=b/2);++d){var e=D[a+2*d>>1];if(0==e)break;c+=String.fromCharCode(e)}return c},
Tb=(a,b,c)=>{c??=2147483647;if(2>c)return 0;c-=2;var d=b;c=c<2*a.length?c/2:a.length;for(var e=0;e<c;++e)D[b>>1]=a.charCodeAt(e),b+=2;D[b>>1]=0;return b-d},Ub=a=>2*a.length,Vb=(a,b)=>{for(var c=0,d="";!(c>=b/4);){var e=E[a+4*c>>2];if(0==e)break;++c;65536<=e?(e-=65536,d+=String.fromCharCode(55296|e>>10,56320|e&1023)):d+=String.fromCharCode(e)}return d},Wb=(a,b,c)=>{c??=2147483647;if(4>c)return 0;var d=b;c=d+c-4;for(var e=0;e<a.length;++e){var f=a.charCodeAt(e);if(55296<=f&&57343>=f){var g=a.charCodeAt(++e);
f=65536+((f&1023)<<10)|g&1023}E[b>>2]=f;b+=4;if(b+4>c)break}E[b>>2]=0;return b-d},Xb=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);55296<=d&&57343>=d&&++c;b+=4}return b},Yb=(a,b,c)=>{var d=[];a=a.toWireType(d,c);d.length&&(F[b>>2]=N(d));return a},Zb=[],$b={},ac=a=>{var b=$b[a];return void 0===b?O(a):b},bc=[],cc=()=>"object"==typeof globalThis?globalThis:Function("return this")(),dc=a=>{var b=Zb.length;Zb.push(a);return b},ec=(a,b)=>{for(var c=Array(a),d=0;d<a;++d)c[d]=Xa(F[b+4*d>>2],"parameter "+
d);return c},fc=a=>0===a%4&&(0!==a%100||0===a%400),gc=[0,31,60,91,121,152,182,213,244,274,305,335],hc=[0,31,59,90,120,151,181,212,243,273,304,334],ic=[],sc=(a,b)=>{jc=a;kc=b;if(lc)if(mc||=!0,0==a)Y=function(){var d=Math.max(0,nc+b-oc())|0;setTimeout(pc,d)};else if(1==a)Y=function(){qc(pc)};else if(2==a){if("undefined"==typeof rc)if("undefined"==typeof setImmediate){var c=[];addEventListener("message",d=>{if("setimmediate"===d.data||"setimmediate"===d.data.target)d.stopPropagation(),c.shift()()},!0);
rc=function(d){c.push(d);let e;(e=k).setImmediates??(e.setImmediates=[]);k.setImmediates.push(d);postMessage({target:"setimmediate"})}}else rc=setImmediate;Y=function(){rc(pc)}}},oc;oc=()=>performance.now();
var Ac=a=>{lc=a;var b=tc;mc=!1;pc=function(){if(!x)if(0<uc.length){var c=uc.shift();c.eb(c.cb);if(vc){var d=vc,e=0==d%1?d-1:Math.floor(d);vc=c.Jb?e:(8*d+(e+.5))/9}k.setStatus&&(c=k.statusMessage||"Please wait...",d=vc,e=xc.Lb,d?d<e?k.setStatus("{message} ({expected - remaining}/{expected})"):k.setStatus(c):k.setStatus(""));b<tc||setTimeout(pc,0)}else b<tc||(yc=yc+1|0,1==jc&&1<kc&&0!=yc%kc?Y():(0==jc&&(nc=oc()),x||k.preMainLoop&&!1===k.preMainLoop()||(zc(a),k.postMainLoop?.()),b<tc||("object"==typeof SDL&&
SDL.audio?.Pb?.(),Y())))}},Bc=a=>{a instanceof Fa||"unwind"==a||da(1,a)},Cc=a=>{ka=a;Ha||(k.onExit?.(a),x=!0);da(a,new Fa(a))},Dc=a=>{ka=a;Cc(a)},zc=a=>{if(!x)try{if(a(),!Ha)try{ka=a=ka,Cc(a)}catch(b){Bc(b)}}catch(b){Bc(b)}},Ec=a=>{Ec.mb||(Ec.mb={});Ec.mb[a]||(Ec.mb[a]=1,w(a))},mc=!1,Y=null,tc=0,lc=null,jc=0,kc=0,yc=0,uc=[],xc={},nc,pc,vc,Fc=!1,Gc=!1,Hc=[],Ic=[];
function Jc(){function a(){Gc=document.pointerLockElement===k.canvas||document.mozPointerLockElement===k.canvas||document.webkitPointerLockElement===k.canvas||document.msPointerLockElement===k.canvas}if(!Kc){Kc=!0;var b=k.canvas;b&&(b.requestPointerLock=b.requestPointerLock||b.mozRequestPointerLock||b.webkitRequestPointerLock||b.msRequestPointerLock||(()=>{}),b.exitPointerLock=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||(()=>{}),
b.exitPointerLock=b.exitPointerLock.bind(document),document.addEventListener("pointerlockchange",a,!1),document.addEventListener("mozpointerlockchange",a,!1),document.addEventListener("webkitpointerlockchange",a,!1),document.addEventListener("mspointerlockchange",a,!1),k.elementPointerLock&&b.addEventListener("click",c=>{!Gc&&k.canvas.requestPointerLock&&(k.canvas.requestPointerLock(),c.preventDefault())},!1))}}var Lc=!1,Mc=void 0,Nc=void 0;
function Oc(){if(!Fc)return!1;(document.exitFullscreen||document.cancelFullScreen||document.mozCancelFullScreen||document.msExitFullscreen||document.webkitCancelFullScreen||(()=>{})).apply(document,[]);return!0}var Pc=0;function qc(a){if("function"==typeof requestAnimationFrame)requestAnimationFrame(a);else{var b=Date.now();if(0===Pc)Pc=b+1E3/60;else for(;b+2>=Pc;)Pc+=1E3/60;setTimeout(a,Math.max(Pc-b,0))}}var Qc=[];function Rc(){var a=k.canvas;Qc.forEach(b=>b(a.width,a.height))}
function Sc(a,b,c){b&&c?(a.Gb=b,a.Ab=c):(b=a.Gb,c=a.Ab);var d=b,e=c;k.forcedAspectRatio&&0<k.forcedAspectRatio&&(d/e<k.forcedAspectRatio?d=Math.round(e*k.forcedAspectRatio):e=Math.round(d/k.forcedAspectRatio));if((document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||document.webkitCurrentFullScreenElement)===a.parentNode&&"undefined"!=typeof screen){var f=Math.min(screen.width/d,screen.height/e);d=Math.round(d*f);e=Math.round(e*
f)}Nc?(a.width!=d&&(a.width=d),a.height!=e&&(a.height=e),"undefined"!=typeof a.style&&(a.style.removeProperty("width"),a.style.removeProperty("height"))):(a.width!=b&&(a.width=b),a.height!=c&&(a.height=c),"undefined"!=typeof a.style&&(d!=b||e!=c?(a.style.setProperty("width",d+"px","important"),a.style.setProperty("height",e+"px","important")):(a.style.removeProperty("width"),a.style.removeProperty("height"))))}var rc,Kc;
function Tc(){if("undefined"!=typeof indexedDB)return indexedDB;var a=null;"object"==typeof window&&(a=window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB);a||xa("IDBStore used, but indexedDB not supported");return a}var Uc={};
function Vc(a,b){var c=Uc[a];if(c)b(null,c);else{try{var d=Tc().open(a,22)}catch(e){b(e);return}d.onupgradeneeded=e=>{var f=e.target.result;e=e.target.transaction;f.objectStoreNames.contains("FILE_DATA")?e.objectStore("FILE_DATA"):f.createObjectStore("FILE_DATA")};d.onsuccess=()=>{c=d.result;Uc[a]=c;b(null,c)};d.onerror=function(e){b(e.target.error||"unknown error");e.preventDefault()}}}
function Wc(a,b,c){Vc(a,(d,e)=>{if(d)return c(d);d=e.transaction(["FILE_DATA"],b);d.onerror=f=>{c(f.target.error||"unknown error");f.preventDefault()};d=d.objectStore("FILE_DATA");c(null,d)})}function Xc(a,b,c){Wc(a,"readonly",(d,e)=>{if(d)return c(d);d=e.get(b);d.onsuccess=f=>(f=f.target.result)?c(null,f):c(`file ${b} not found`);d.onerror=c})}function Yc(a,b,c,d){Wc(a,"readwrite",(e,f)=>{if(e)return d(e);e=f.put(c,b);e.onsuccess=()=>d();e.onerror=d})}
function Zc(a,b,c){Wc(a,"readwrite",(d,e)=>{if(d)return c(d);d=e.delete(b);d.onsuccess=()=>c();d.onerror=c})}function $c(a,b,c){Wc(a,"readonly",(d,e)=>{if(d)return c(d);d=e.count(b);d.onsuccess=f=>c(null,0<f.target.result);d.onerror=c})}function ad(a,b){Wc(a,"readwrite",(c,d)=>{if(c)return b(c);c=d.clear();c.onsuccess=()=>b();c.onerror=b})}
var bd=(a,b)=>{if(0>=a)return a;var c=32>=b?Math.abs(1<<b-1):Math.pow(2,b-1);a>=c&&(32>=b||a>c)&&(a=-2*c+a);return a},cd=(a,b)=>0<=a?a:32>=b?2*Math.abs(1<<b-1)+a:Math.pow(2,b)+a,dd=a=>{for(var b=a;C[b];)++b;return b-a};function ed(a){var b=Array(Qb(a)+1);a=X(a,b,0,b.length);b.length=a;return b}
var fd=(a,b)=>{function c(A){var P=d;("double"===A||"i64"===A)&&P&7&&(P+=4);d=P;"double"===A?(A=na[d>>3],d+=8):"i64"==A?(A=[E[d>>2],E[d+4>>2]],d+=8):(A=E[d>>2],d+=4);return A}for(var d=b,e=[],f,g;;){var h=a;f=z[a];if(0===f)break;g=z[a+1];if(37==f){var m=!1,l=b=!1,n=!1,q=!1;a:for(;;){switch(g){case 43:m=!0;break;case 45:b=!0;break;case 35:l=!0;break;case 48:if(n)break a;else{n=!0;break}case 32:q=!0;break;default:break a}a++;g=z[a+1]}var v=0;if(42==g)v=c("i32"),a++,g=z[a+1];else for(;48<=g&&57>=g;)v=
10*v+(g-48),a++,g=z[a+1];var y=!1,u=-1;if(46==g){u=0;y=!0;a++;g=z[a+1];if(42==g)u=c("i32"),a++;else for(;;){g=z[a+1];if(48>g||57<g)break;u=10*u+(g-48);a++}g=z[a+1]}0>u&&(u=6,y=!1);switch(String.fromCharCode(g)){case "h":g=z[a+2];if(104==g){a++;var r=1}else r=2;break;case "l":g=z[a+2];108==g?(a++,r=8):r=4;break;case "L":case "q":case "j":r=8;break;case "z":case "t":case "I":r=4;break;default:r=null}r&&a++;g=z[a+1];switch(String.fromCharCode(g)){case "d":case "i":case "u":case "o":case "x":case "X":case "p":h=
100==g||105==g;r=r||4;f=c("i"+8*r);8==r&&(f=117==g?(f[0]>>>0)+4294967296*(f[1]>>>0):(f[0]>>>0)+4294967296*f[1]);4>=r&&(f=(h?bd:cd)(f&Math.pow(256,r)-1,8*r));var B=Math.abs(f);h="";if(100==g||105==g)var p=bd(f,8*r).toString(10);else if(117==g)p=cd(f,8*r).toString(10),f=Math.abs(f);else if(111==g)p=(l?"0":"")+B.toString(8);else if(120==g||88==g){h=l&&0!=f?"0x":"";if(0>f){f=-f;p=(B-1).toString(16);B=[];for(l=0;l<p.length;l++)B.push((15-parseInt(p[l],16)).toString(16));for(p=B.join("");p.length<2*r;)p=
"f"+p}else p=B.toString(16);88==g&&(h=h.toUpperCase(),p=p.toUpperCase())}else 112==g&&(0===B?p="(nil)":(h="0x",p=B.toString(16)));if(y)for(;p.length<u;)p="0"+p;0<=f&&(m?h="+"+h:q&&(h=" "+h));"-"==p.charAt(0)&&(h="-"+h,p=p.substr(1));for(;h.length+p.length<v;)b?p+=" ":n?p="0"+p:h=" "+h;p=h+p;p.split("").forEach(function(A){e.push(A.charCodeAt(0))});break;case "f":case "F":case "e":case "E":case "g":case "G":f=c("double");if(isNaN(f))p="nan",n=!1;else if(isFinite(f)){y=!1;r=Math.min(u,20);if(103==g||
71==g)y=!0,u=u||1,r=parseInt(f.toExponential(r).split("e")[1],10),u>r&&-4<=r?(g=(103==g?"f":"F").charCodeAt(0),u-=r+1):(g=(103==g?"e":"E").charCodeAt(0),u--),r=Math.min(u,20);if(101==g||69==g)p=f.toExponential(r),/[eE][-+]\d$/.test(p)&&(p=p.slice(0,-1)+"0"+p.slice(-1));else if(102==g||70==g)p=f.toFixed(r),0===f&&(0>f||0===f&&-Infinity===1/f)&&(p="-"+p);h=p.split("e");if(y&&!l)for(;1<h[0].length&&h[0].includes(".")&&("0"==h[0].slice(-1)||"."==h[0].slice(-1));)h[0]=h[0].slice(0,-1);else for(l&&-1==
p.indexOf(".")&&(h[0]+=".");u>r++;)h[0]+="0";p=h[0]+(1<h.length?"e"+h[1]:"");69==g&&(p=p.toUpperCase());0<=f&&(m?p="+"+p:q&&(p=" "+p))}else p=(0>f?"-":"")+"inf",n=!1;for(;p.length<v;)p=b?p+" ":!n||"-"!=p[0]&&"+"!=p[0]?(n?"0":" ")+p:p[0]+"0"+p.slice(1);97>g&&(p=p.toUpperCase());p.split("").forEach(function(A){e.push(A.charCodeAt(0))});break;case "s":n=(m=c("i8*"))?dd(m):6;y&&(n=Math.min(n,u));if(!b)for(;n<v--;)e.push(32);if(m)for(l=0;l<n;l++)e.push(C[m++]);else e=e.concat(ed("(null)".substr(0,n)));
if(b)for(;n<v--;)e.push(32);break;case "c":for(b&&e.push(c("i8"));0<--v;)e.push(32);b||e.push(c("i8"));break;case "n":b=c("i32*");E[b>>2]=e.length;break;case "%":e.push(f);break;default:for(l=h;l<a+2;l++)e.push(z[l])}a+=2}else e.push(f),a+=1}return e},gd={},jd=()=>{if(!hd){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:ca||"./this.program"},b;for(b in gd)void 0===
gd[b]?delete a[b]:a[b]=gd[b];var c=[];for(b in a)c.push(`${b}=${a[b]}`);hd=c}return hd},hd,kd=[null,[],[]],ld=()=>{if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return a=>crypto.getRandomValues(a);xa("initRandomDevice")},md=a=>(md=ld())(a);L=k.BindingError=class extends Error{constructor(a){super(a);this.name="BindingError"}};K.push(0,1,void 0,1,null,1,!0,1,!1,1);k.count_emval_handles=()=>K.length/2-5-Ma.length;Pa=k.PureVirtualError=Oa("PureVirtualError");
for(var nd=Array(256),od=0;256>od;++od)nd[od]=String.fromCharCode(od);Qa=nd;k.getInheritedInstanceCount=()=>Object.keys(Q).length;k.getLiveInheritedInstances=()=>{var a=[],b;for(b in Q)Q.hasOwnProperty(b)&&a.push(Q[b]);return a};k.flushPendingDeletes=Sa;k.setDelayFunction=a=>{Ta=a;Ra.length&&Ta&&Ta(Sa)};cb=k.InternalError=class extends Error{constructor(a){super(a);this.name="InternalError"}};
Object.assign(nb.prototype,{isAliasOf:function(a){if(!(this instanceof nb&&a instanceof nb))return!1;var b=this.za.La.Ka,c=this.za.Ja;a.za=a.za;var d=a.za.La.Ka;for(a=a.za.Ja;b.Ma;)c=b.Ya(c),b=b.Ma;for(;d.Ma;)a=d.Ya(a),d=d.Ma;return b===d&&c===a},clone:function(){this.za.Ja||mb(this);if(this.za.Wa)return this.za.count.value+=1,this;var a=db,b=Object,c=b.create,d=Object.getPrototypeOf(this),e=this.za;a=a(c.call(b,d,{za:{value:{count:e.count,Va:e.Va,Wa:e.Wa,Ja:e.Ja,La:e.La,Na:e.Na,Ra:e.Ra}}}));a.za.count.value+=
1;a.za.Va=!1;return a},["delete"](){this.za.Ja||mb(this);if(this.za.Va&&!this.za.Wa)throw new L("Object already scheduled for deletion");Ya(this);var a=this.za;--a.count.value;0===a.count.value&&(a.Na?a.Ra.Qa(a.Na):a.La.Ka.Qa(a.Ja));this.za.Wa||(this.za.Na=void 0,this.za.Ja=void 0)},isDeleted:function(){return!this.za.Ja},deleteLater:function(){this.za.Ja||mb(this);if(this.za.Va&&!this.za.Wa)throw new L("Object already scheduled for deletion");Ra.push(this);1===Ra.length&&Ta&&Ta(Sa);this.za.Va=!0;
return this}});
Object.assign(yb.prototype,{zb(a){this.sb&&(a=this.sb(a));return a},pb(a){this.Qa?.(a)},argPackAdvance:8,readValueFromPointer:hb,fromWireType:function(a){function b(){return this.bb?eb(this.Ka.Sa,{La:this.Db,Ja:c,Ra:this,Na:a}):eb(this.Ka.Sa,{La:this,Ja:a})}var c=this.zb(a);if(!c)return this.pb(a),null;var d=bb(this.Ka,c);if(void 0!==d){if(0===d.za.count.value)return d.za.Ja=c,d.za.Na=a,d.clone();d=d.clone();this.pb(a);return d}d=this.Ka.yb(c);d=ab[d];if(!d)return b.call(this);d=this.ab?d.ub:d.pointerType;
var e=$a(c,this.Ka,d.Ka);return null===e?b.call(this):this.bb?eb(d.Ka.Sa,{La:d,Ja:e,Ra:this,Na:a}):eb(d.Ka.Sa,{La:d,Ja:e})}});Eb=k.UnboundTypeError=Oa("UnboundTypeError");
k.requestFullscreen=function(a,b){function c(){Fc=!1;var f=d.parentNode;(document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||document.webkitCurrentFullScreenElement)===f?(d.exitFullscreen=Oc,Mc&&d.requestPointerLock(),Fc=!0,Nc?("undefined"!=typeof SDL&&(E[SDL.screen>>2]=F[SDL.screen>>2]|8388608),Sc(k.canvas),Rc()):Sc(d)):(f.parentNode.insertBefore(d,f),f.parentNode.removeChild(f),Nc?("undefined"!=typeof SDL&&(E[SDL.screen>>2]=
F[SDL.screen>>2]&-8388609),Sc(k.canvas),Rc()):Sc(d));k.onFullScreen?.(Fc);k.onFullscreen?.(Fc)}Mc=a;Nc=b;"undefined"==typeof Mc&&(Mc=!0);"undefined"==typeof Nc&&(Nc=!1);var d=k.canvas;Lc||(Lc=!0,document.addEventListener("fullscreenchange",c,!1),document.addEventListener("mozfullscreenchange",c,!1),document.addEventListener("webkitfullscreenchange",c,!1),document.addEventListener("MSFullscreenChange",c,!1));var e=document.createElement("div");d.parentNode.insertBefore(e,d);e.appendChild(d);e.requestFullscreen=
e.requestFullscreen||e.mozRequestFullScreen||e.msRequestFullscreen||(e.webkitRequestFullscreen?()=>e.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):null)||(e.webkitRequestFullScreen?()=>e.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT):null);e.requestFullscreen()};k.requestAnimationFrame=qc;k.setCanvasSize=function(a,b,c){Sc(k.canvas,a,b);c||Rc()};k.pauseMainLoop=function(){Y=null;tc++};k.resumeMainLoop=function(){tc++;var a=jc,b=kc,c=lc;lc=null;Ac(c);sc(a,b);Y()};
k.getUserMedia=function(a){let b;(b=window).getUserMedia||(b.getUserMedia=navigator.getUserMedia||navigator.mozGetUserMedia);window.getUserMedia(a)};k.createContext=function(a,b,c,d){if(b&&k.ob&&a==k.canvas)return k.ob;var e;if(b){var f={antialias:!1,alpha:!1,Mb:2};if(d)for(var g in d)f[g]=d[g];if("undefined"!=typeof GL&&(e=GL.Kb(a,f)))var h=GL.getContext(e).Hb}else h=a.getContext("2d");if(!h)return null;c&&(k.ob=h,b&&GL.Nb(e),k.Qb=b,Hc.forEach(m=>m()),Jc());return h};
var ud={t:(a,b,c)=>{var d=new Ia(a);F[d.Ja+16>>2]=0;F[d.Ja+4>>2]=b;F[d.Ja+8>>2]=c;Ja=a;Ka++;throw Ja;},Y:function(){},S:()=>{},_:()=>{xa("")},ka:(a,b,c)=>{a=O(a);b=Xa(b,"wrapper");c=M(c);var d=b.Ka,e=d.Sa,f=d.Ma.Sa,g=d.Ma.constructor;a=J(a,function(...h){d.Ma.rb.forEach(function(m){if(this[m]===f[m])throw new Pa(`Pure virtual function ${m} must be implemented in JavaScript`);}.bind(this));Object.defineProperty(this,"__parent",{value:e});this.__construct(...h)});e.__construct=function(...h){if(this===
e)throw new L("Pass correct 'this' to __construct");h=g.implement(this,...h);Ya(h);var m=h.za;h.notifyOnDestruction();m.Wa=!0;Object.defineProperties(this,{za:{value:m}});db(this);h=m.Ja;h=Ua(d,h);if(Q.hasOwnProperty(h))throw new L(`Tried to register registered instance: ${h}`);Q[h]=this};e.__destruct=function(){if(this===e)throw new L("Pass correct 'this' to __destruct");Ya(this);var h=this.za.Ja;h=Ua(d,h);if(Q.hasOwnProperty(h))delete Q[h];else throw new L(`Tried to unregister unregistered instance: ${h}`);
};a.prototype=Object.create(e);Object.assign(a.prototype,c);return N(a)},x:a=>{var b=fb[a];delete fb[a];var c=b.elements,d=c.length,e=c.map(h=>h.hb).concat(c.map(h=>h.kb)),f=b.Xa,g=b.Qa;U([a],e,h=>{c.forEach((m,l)=>{var n=h[l],q=m.fb,v=m.gb,y=h[l+d],u=m.jb,r=m.lb;m.read=B=>n.fromWireType(q(v,B));m.write=(B,p)=>{var A=[];u(r,B,y.toWireType(A,p));gb(A)}});return[{name:b.name,fromWireType:m=>{for(var l=Array(d),n=0;n<d;++n)l[n]=c[n].read(m);g(m);return l},toWireType:(m,l)=>{if(d!==l.length)throw new TypeError(`Incorrect number of tuple elements for ${b.name}: expected=${d}, actual=${l.length}`);
for(var n=f(),q=0;q<d;++q)c[q].write(n,l[q]);null!==m&&m.push(g,n);return n},argPackAdvance:8,readValueFromPointer:hb,Oa:g}]})},B:a=>{var b=kb[a];delete kb[a];var c=b.Xa,d=b.Qa,e=b.qb,f=e.map(g=>g.hb).concat(e.map(g=>g.kb));U([a],f,g=>{var h={};e.forEach((m,l)=>{var n=g[l],q=m.fb,v=m.gb,y=g[l+e.length],u=m.jb,r=m.lb;h[m.xb]={read:B=>n.fromWireType(q(v,B)),write:(B,p)=>{var A=[];u(r,B,y.toWireType(A,p));gb(A)}}});return[{name:b.name,fromWireType:m=>{var l={},n;for(n in h)l[n]=h[n].read(m);d(m);return l},
toWireType:(m,l)=>{for(var n in h)if(!(n in l))throw new TypeError(`Missing field: "${n}"`);var q=c();for(n in h)h[n].write(q,l[n]);null!==m&&m.push(d,q);return q},argPackAdvance:8,readValueFromPointer:hb,Oa:d}]})},P:()=>{},ma:(a,b,c,d)=>{b=O(b);T(a,{name:b,fromWireType:function(e){return!!e},toWireType:function(e,f){return f?c:d},argPackAdvance:8,readValueFromPointer:function(e){return this.fromWireType(C[e])},Oa:null})},v:(a,b,c,d,e,f,g,h,m,l,n,q,v)=>{n=O(n);f=W(e,f);h&&=W(g,h);l&&=W(m,l);v=W(q,
v);var y=qb(n);pb(y,function(){Fb(`Cannot construct ${n} due to unbound types`,[d])});U([a,b,c],d?[d]:[],u=>{u=u[0];if(d){var r=u.Ka;var B=r.Sa}else B=nb.prototype;u=J(n,function(...vb){if(Object.getPrototypeOf(this)!==p)throw new L("Use 'new' to construct "+n);if(void 0===A.Ta)throw new L(n+" has no accessible constructor");var wc=A.Ta[vb.length];if(void 0===wc)throw new L(`Tried to invoke ctor of ${n} with invalid number of parameters (${vb.length}) - expected (${Object.keys(A.Ta).toString()}) parameters instead!`);
return wc.apply(this,vb)});var p=Object.create(B,{constructor:{value:u}});u.prototype=p;var A=new rb(n,u,p,v,r,f,h,l);if(A.Ma){var P;(P=A.Ma).Za??(P.Za=[]);A.Ma.Za.push(A)}r=new yb(n,A,!0,!1,!1);P=new yb(n+"*",A,!1,!1,!1);B=new yb(n+" const*",A,!1,!0,!1);ab[a]={pointerType:P,ub:B};zb(y,u);return[r,P,B]})},s:(a,b,c,d,e,f,g,h)=>{var m=Jb(c,d);b=O(b);b=Kb(b);f=W(e,f);U([],[a],l=>{function n(){Fb(`Cannot call ${q} due to unbound types`,m)}l=l[0];var q=`${l.name}.${b}`;b.startsWith("@@")&&(b=Symbol[b.substring(2)]);
var v=l.Ka.constructor;void 0===v[b]?(n.Ua=c-1,v[b]=n):(ob(v,b,q),v[b].Pa[c-1]=n);U([],m,y=>{y=Ib(q,[y[0],null].concat(y.slice(1)),null,f,g,h);void 0===v[b].Pa?(y.Ua=c-1,v[b]=y):v[b].Pa[c-1]=y;if(l.Ka.Za)for(const u of l.Ka.Za)u.constructor.hasOwnProperty(b)||(u.constructor[b]=y);return[]});return[]})},A:(a,b,c,d,e,f)=>{var g=Jb(b,c);e=W(d,e);U([],[a],h=>{h=h[0];var m=`constructor ${h.name}`;void 0===h.Ka.Ta&&(h.Ka.Ta=[]);if(void 0!==h.Ka.Ta[b-1])throw new L(`Cannot register multiple constructors with identical number of parameters (${b-
1}) for class '${h.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);h.Ka.Ta[b-1]=()=>{Fb(`Cannot construct ${h.name} due to unbound types`,g)};U([],g,l=>{l.splice(1,0,null);h.Ka.Ta[b-1]=Ib(m,l,null,e,f);return[]});return[]})},k:(a,b,c,d,e,f,g,h,m)=>{var l=Jb(c,d);b=O(b);b=Kb(b);f=W(e,f);U([],[a],n=>{function q(){Fb(`Cannot call ${v} due to unbound types`,l)}n=n[0];var v=`${n.name}.${b}`;b.startsWith("@@")&&(b=Symbol[b.substring(2)]);h&&n.Ka.rb.push(b);
var y=n.Ka.Sa,u=y[b];void 0===u||void 0===u.Pa&&u.className!==n.name&&u.Ua===c-2?(q.Ua=c-2,q.className=n.name,y[b]=q):(ob(y,b,v),y[b].Pa[c-2]=q);U([],l,r=>{r=Ib(v,r,n,f,g,m);void 0===y[b].Pa?(r.Ua=c-2,y[b]=r):y[b].Pa[c-2]=r;return[]});return[]})},la:a=>T(a,Mb),L:(a,b,c,d)=>{function e(){}b=O(b);e.values={};T(a,{name:b,constructor:e,fromWireType:function(f){return this.constructor.values[f]},toWireType:(f,g)=>g.value,argPackAdvance:8,readValueFromPointer:Nb(b,c,d),Oa:null});pb(b,e)},l:(a,b,c)=>{var d=
Xa(a,"enum");b=O(b);a=d.constructor;d=Object.create(d.constructor.prototype,{value:{value:c},constructor:{value:J(`${d.name}_${b}`,function(){})}});a.values[c]=d;a[b]=d},J:(a,b,c)=>{b=O(b);T(a,{name:b,fromWireType:d=>d,toWireType:(d,e)=>e,argPackAdvance:8,readValueFromPointer:Ob(b,c),Oa:null})},u:(a,b,c,d,e)=>{b=O(b);-1===e&&(e=4294967295);e=h=>h;if(0===d){var f=32-8*c;e=h=>h<<f>>>f}var g=b.includes("unsigned")?function(h,m){return m>>>0}:function(h,m){return m};T(a,{name:b,fromWireType:e,toWireType:g,
argPackAdvance:8,readValueFromPointer:Pb(b,c,0!==d),Oa:null})},p:(a,b,c)=>{function d(f){return new e(z.buffer,F[f+4>>2],F[f>>2])}var e=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][b];c=O(c);T(a,{name:c,fromWireType:d,argPackAdvance:8,readValueFromPointer:d},{Bb:!0})},I:a=>{T(a,Mb)},K:(a,b)=>{b=O(b);var c="std::string"===b;T(a,{name:b,fromWireType:function(d){var e=F[d>>2],f=d+4;if(c)for(var g=f,h=0;h<=e;++h){var m=f+h;if(h==e||0==C[m]){g=g?I(C,g,
m-g):"";if(void 0===l)var l=g;else l+=String.fromCharCode(0),l+=g;g=m+1}}else{l=Array(e);for(h=0;h<e;++h)l[h]=String.fromCharCode(C[f+h]);l=l.join("")}S(d);return l},toWireType:function(d,e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));var f="string"==typeof e;if(!(f||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array))throw new L("Cannot pass non-string to std::string");var g=c&&f?Qb(e):e.length;var h=pd(4+g+1),m=h+4;F[h>>2]=g;if(c&&f)X(e,C,m,g+1);else if(f)for(f=
0;f<g;++f){var l=e.charCodeAt(f);if(255<l)throw S(m),new L("String has UTF-16 code units that do not fit in 8 bits");C[m+f]=l}else for(f=0;f<g;++f)C[m+f]=e[f];null!==d&&d.push(S,h);return h},argPackAdvance:8,readValueFromPointer:hb,Oa(d){S(d)}})},E:(a,b,c)=>{c=O(c);if(2===b){var d=Sb;var e=Tb;var f=Ub;var g=h=>la[h>>1]}else 4===b&&(d=Vb,e=Wb,f=Xb,g=h=>F[h>>2]);T(a,{name:c,fromWireType:h=>{for(var m=F[h>>2],l,n=h+4,q=0;q<=m;++q){var v=h+4+q*b;if(q==m||0==g(v))n=d(n,v-n),void 0===l?l=n:(l+=String.fromCharCode(0),
l+=n),n=v+b}S(h);return l},toWireType:(h,m)=>{if("string"!=typeof m)throw new L(`Cannot pass non-string to C++ string type ${c}`);var l=f(m),n=pd(4+l+b);F[n>>2]=l/b;e(m,n+4,l+b);null!==h&&h.push(S,n);return n},argPackAdvance:8,readValueFromPointer:hb,Oa(h){S(h)}})},y:(a,b,c,d,e,f)=>{fb[a]={name:O(b),Xa:W(c,d),Qa:W(e,f),elements:[]}},i:(a,b,c,d,e,f,g,h,m)=>{fb[a].elements.push({hb:b,fb:W(c,d),gb:e,kb:f,jb:W(g,h),lb:m})},C:(a,b,c,d,e,f)=>{kb[a]={name:O(b),Xa:W(c,d),Qa:W(e,f),qb:[]}},r:(a,b,c,d,e,f,
g,h,m,l)=>{kb[a].qb.push({xb:O(b),hb:c,fb:W(d,e),gb:f,kb:g,jb:W(h,m),lb:l})},na:(a,b)=>{b=O(b);T(a,{Cb:!0,name:b,argPackAdvance:0,fromWireType:()=>{},toWireType:()=>{}})},Z:(a,b,c)=>C.copyWithin(a,b,b+c),j:(a,b,c)=>{a=M(a);b=Xa(b,"emval::as");return Yb(b,c,a)},m:(a,b,c,d)=>{a=Zb[a];b=M(b);return a(null,b,c,d)},g:(a,b,c,d,e)=>{a=Zb[a];b=M(b);c=ac(c);return a(b,b[c],d,e)},w:(a,b)=>N(new Promise((c,d)=>{F[a>>2]=N(c);F[b>>2]=N(()=>{try{var e=bc.pop();e||xa("no exception to throw");var f=e.wb;0==z[e.Ja+
13]&&(bc.push(e),z[e.Ja+13]=1,z[e.Ja+12]=0,Ka++);Ja=f;throw Ja;}catch(g){d(g)}})})),z:(a,b)=>{M(a).then(c=>{qd(b,N(c))})},a:Lb,q:(a,b)=>{a=M(a);b=M(b);return a==b},n:a=>{if(0===a)return N(cc());a=ac(a);return N(cc()[a])},e:(a,b,c)=>{b=ec(a,b);var d=b.shift();a--;var e="return function (obj, func, destructorsRef, args) {\n",f=0,g=[];0===c&&g.push("obj");for(var h=["retType"],m=[d],l=0;l<a;++l)g.push("arg"+l),h.push("argType"+l),m.push(b[l]),e+=`  var arg${l} = argType${l}.readValueFromPointer(args${f?
"+"+f:""});\n`,f+=b[l].argPackAdvance;e+=`  var rv = ${1===c?"new func":"func.call"}(${g.join(", ")});\n`;d.Cb||(h.push("emval_returnValue"),m.push(Yb),e+="  return emval_returnValue(retType, destructorsRef, rv);\n");h.push(e+"};\n");a=Hb(h)(...m);c=`methodCaller<(${b.map(n=>n.name).join(", ")}) => ${d.name}>`;return dc(J(c,a))},f:(a,b)=>{a=M(a);b=M(b);return N(a[b])},d:a=>{9<a&&(K[a+1]+=1)},c:a=>N(ac(a)),b:a=>{var b=M(a);gb(b);Lb(a)},o:(a,b,c)=>{a=M(a);b=M(b);c=M(c);a[b]=c},h:(a,b)=>{a=Xa(a,"_emval_take_value");
a=a.readValueFromPointer(b);return N(a)},M:function(a,b,c){a=new Date(1E3*(b+2097152>>>0<4194305-!!a?(a>>>0)+4294967296*b:NaN));E[c>>2]=a.getSeconds();E[c+4>>2]=a.getMinutes();E[c+8>>2]=a.getHours();E[c+12>>2]=a.getDate();E[c+16>>2]=a.getMonth();E[c+20>>2]=a.getFullYear()-1900;E[c+24>>2]=a.getDay();E[c+28>>2]=(fc(a.getFullYear())?gc:hc)[a.getMonth()]+a.getDate()-1|0;E[c+36>>2]=-(60*a.getTimezoneOffset());b=(new Date(a.getFullYear(),6,1)).getTimezoneOffset();var d=(new Date(a.getFullYear(),0,1)).getTimezoneOffset();
E[c+32>>2]=(b!=d&&a.getTimezoneOffset()==Math.min(d,b))|0},N:function(a){var b=new Date(E[a+20>>2]+1900,E[a+16>>2],E[a+12>>2],E[a+8>>2],E[a+4>>2],E[a>>2],0),c=E[a+32>>2],d=b.getTimezoneOffset(),e=(new Date(b.getFullYear(),6,1)).getTimezoneOffset(),f=(new Date(b.getFullYear(),0,1)).getTimezoneOffset(),g=Math.min(f,e);0>c?E[a+32>>2]=Number(e!=f&&g==d):0<c!=(g==d)&&(e=Math.max(f,e),b.setTime(b.getTime()+6E4*((0<c?g:e)-d)));E[a+24>>2]=b.getDay();E[a+28>>2]=(fc(b.getFullYear())?gc:hc)[b.getMonth()]+b.getDate()-
1|0;E[a>>2]=b.getSeconds();E[a+4>>2]=b.getMinutes();E[a+8>>2]=b.getHours();E[a+12>>2]=b.getDate();E[a+16>>2]=b.getMonth();E[a+20>>2]=b.getYear();a=b.getTime();a=isNaN(a)?-1:a/1E3;rd((H=a,1<=+Math.abs(H)?0<H?+Math.floor(H/4294967296)>>>0:~~+Math.ceil((H-+(~~H>>>0))/4294967296)>>>0:0));return a>>>0},T:(a,b,c,d)=>{var e=(new Date).getFullYear(),f=new Date(e,0,1),g=new Date(e,6,1);e=f.getTimezoneOffset();var h=g.getTimezoneOffset();F[a>>2]=60*Math.max(e,h);E[b>>2]=Number(e!=h);a=m=>m.toLocaleTimeString(void 0,
{hour12:!1,timeZoneName:"short"}).split(" ")[1];f=a(f);g=a(g);h<e?(X(f,C,c,17),X(g,C,d,17)):(X(f,C,d,17),X(g,C,c,17))},ja:(a,b,c)=>{ic.length=0;for(var d;d=C[b++];){var e=105!=d;e&=112!=d;c+=e&&c%8?4:0;ic.push(112==d?F[c>>2]:105==d?E[c>>2]:na[c>>3]);c+=e?8:4}return Ea[a](...ic)},aa:(a,b,c,d,e,f)=>{b=b?I(C,b):"";a=Ic[a];var g=-1;e&&(g=a.$a.length,a.$a.push({eb:V(e),cb:f}),a.nb++);b={funcName:b,callbackId:g,data:c?new Uint8Array(C.subarray(c,c+d)):0};c?a.worker.postMessage(b,[b.data.buffer]):a.worker.postMessage(b)},
ca:a=>{a=a?I(C,a):"";var b=Ic.length;a={worker:new Worker(a),$a:[],nb:0,buffer:0,bufferSize:0};a.worker.onmessage=function(c){if(!x){var d=Ic[b];if(d){var e=c.data.callbackId,f=d.$a[e];if(f)if(c.data.finalResponse&&(d.nb--,d.$a[e]=null),c=c.data.data){c.byteLength||(c=new Uint8Array(c));if(!d.buffer||d.bufferSize<c.length)d.buffer&&S(d.buffer),d.bufferSize=c.length,d.buffer=pd(c.length);C.set(c,d.buffer);f.eb(d.buffer,c.length,f.cb)}else f.eb(0,0,f.cb)}}};Ic.push(a);return b},F:()=>Date.now(),ba:a=>
{var b=Ic[a];b.worker.terminate();b.buffer&&S(b.buffer);Ic[a]=null},da:()=>{throw"unwind";},ea:(a,b,c,d)=>{ad(a?I(C,a):"",e=>{zc(()=>{e?d&&V(d)(b):c&&V(c)(b)})})},ga:(a,b,c,d,e)=>{Zc(a?I(C,a):"",b?I(C,b):"",f=>{zc(()=>{f?e&&V(e)(c):d&&V(d)(c)})})},fa:(a,b,c,d,e)=>{$c(a?I(C,a):"",b?I(C,b):"",(f,g)=>{zc(()=>{f?e&&V(e)(c):d&&V(d)(c,g)})})},ia:(a,b,c,d,e)=>{Xc(a?I(C,a):"",b?I(C,b):"",(f,g)=>{zc(()=>{if(f)e&&V(e)(c);else{var h=pd(g.length);C.set(g,h);V(d)(c,h,g.length);S(h)}})})},ha:(a,b,c,d,e,f,g)=>{Yc(a?
I(C,a):"",b?I(C,b):"",new Uint8Array(C.subarray(c,c+d)),h=>{zc(()=>{h?g&&V(g)(e):f&&V(f)(e)})})},H:(a,b,c)=>{b=fd(b,c);c=I(b,0);if(a&24){b=c=c.replace(/\s+$/,"");c=0<c.length?"\n":"";var d=a,e=Error().stack.toString();e=e.slice(e.indexOf("\n",Math.max(e.lastIndexOf("_emscripten_log"),e.lastIndexOf("_emscripten_get_callstack")))+1);d&8&&"undefined"==typeof emscripten_source_map&&(Ec('Source map information is not available, emscripten_log with EM_LOG_C_STACK will be ignored. Build with "--pre-js $EMSCRIPTEN/src/emscripten-source-map.min.js" linker flag to add source map loading to code.'),
d=d^8|16);var f=e.split("\n");e="";var g=RegExp("\\s*(.*?)@(.*?):([0-9]+):([0-9]+)"),h=RegExp("\\s*(.*?)@(.*):(.*)(:(.*))?"),m=RegExp("\\s*at (.*?) \\((.*):(.*):(.*)\\)"),l;for(l in f){var n=f[l],q;if((q=m.exec(n))&&5==q.length){n=q[1];var v=q[2];var y=q[3];q=q[4]}else if((q=g.exec(n))||(q=h.exec(n)),q&&4<=q.length)n=q[1],v=q[2],y=q[3],q=q[4]|0;else{e+=n+"\n";continue}var u=!1;if(d&8){var r=emscripten_source_map.Ob({line:y,tb:q});if(u=r?.source)d&64&&(r.source=r.source.substring(r.source.replace(/\\/g,
"/").lastIndexOf("/")+1)),e+=`    at ${n} (${r.source}:${r.line}:${r.tb})\n`}if(d&16||!u)d&64&&(v=v.substring(v.replace(/\\/g,"/").lastIndexOf("/")+1)),e+=(u?`     = ${n}`:`    at ${n}`)+` (${v}:${y}:${q})\n`}e=e.replace(/\s+$/,"");c=b+(c+e)}a&1?a&4?console.error(c):a&2?console.warn(c):a&512?console.info(c):a&256?console.debug(c):console.log(c):a&6?w(c):ha(c)},R:a=>{var b=C.length;a>>>=0;if(2147483648<a)return!1;for(var c=1;4>=c;c*=2){var d=b*(1+.2/c);d=Math.min(d,a+100663296);var e=Math;d=Math.max(a,
d);a:{e=(e.min.call(e,2147483648,d+(65536-d%65536)%65536)-ja.buffer.byteLength+65535)/65536;try{ja.grow(e);oa();var f=1;break a}catch(g){}f=void 0}if(f)return!0}return!1},$:(a,b)=>{if(sd)throw"already responded with final response!";sd=!0;b={callbackId:td,finalResponse:!0,data:a?new Uint8Array(C.subarray(a,a+b)):0};a?postMessage(b,[b.data.buffer]):postMessage(b)},D:(a,b)=>{if(sd)throw"already responded with final response!";b={callbackId:td,finalResponse:!1,data:a?new Uint8Array(C.subarray(a,a+b)):
0};a?postMessage(b,[b.data.buffer]):postMessage(b)},U:(a,b)=>{var c=0;jd().forEach((d,e)=>{var f=b+c;e=F[a+4*e>>2]=f;for(f=0;f<d.length;++f)z[e++]=d.charCodeAt(f);z[e]=0;c+=d.length+1});return 0},V:(a,b)=>{var c=jd();F[a>>2]=c.length;var d=0;c.forEach(e=>d+=e.length+1);F[b>>2]=d;return 0},G:()=>{Ec("To close sockets with PROXY_POSIX_SOCKETS bridge, prefer to use the function shutdown() that is proxied, instead of close()");return 0},X:()=>52,O:function(){return 70},W:(a,b,c,d)=>{for(var e=0,f=0;f<
c;f++){var g=F[b>>2],h=F[b+4>>2];b+=8;for(var m=0;m<h;m++){var l=C[g+m],n=kd[a];0===l||10===l?((1===a?ha:w)(I(n,0)),n.length=0):n.push(l)}e+=h}F[d>>2]=e;return 0},Q:(a,b)=>{md(C.subarray(a,a+b));return 0}},Z=function(){function a(c){Z=c.exports;ja=Z.oa;oa();Bb=Z.sa;qa.unshift(Z.pa);G--;k.monitorRunDependencies?.(G);0==G&&(null!==va&&(clearInterval(va),va=null),wa&&(c=wa,wa=null,c()));return Z}var b={a:ud};G++;k.monitorRunDependencies?.(G);if(k.instantiateWasm)try{return k.instantiateWasm(b,a)}catch(c){return w(`Module.instantiateWasm callback failed with error: ${c}`),
!1}za||=ya("tool-resource-downloader-worker.wasm")?"tool-resource-downloader-worker.wasm":k.locateFile?k.locateFile("tool-resource-downloader-worker.wasm",t):t+"tool-resource-downloader-worker.wasm";Da(b,function(c){a(c.instance)});return{}}(),Va=a=>(Va=Z.qa)(a),qd=(a,b)=>(qd=Z.ra)(a,b),pd=a=>(pd=Z.ta)(a),vd=k._main=(a,b)=>(vd=k._main=Z.ua)(a,b);k._sendRequest=(a,b)=>(k._sendRequest=Z.va)(a,b);var S=a=>(S=Z.wa)(a),rd=a=>(rd=Z.xa)(a),wd=a=>(wd=Z.ya)(a);
k.dynCall_viij=(a,b,c,d,e)=>(k.dynCall_viij=Z.Aa)(a,b,c,d,e);k.dynCall_jii=(a,b,c)=>(k.dynCall_jii=Z.Ba)(a,b,c);k.dynCall_ji=(a,b)=>(k.dynCall_ji=Z.Ca)(a,b);k.dynCall_iij=(a,b,c,d)=>(k.dynCall_iij=Z.Da)(a,b,c,d);k.dynCall_viijj=(a,b,c,d,e,f,g)=>(k.dynCall_viijj=Z.Ea)(a,b,c,d,e,f,g);k.dynCall_jiji=(a,b,c,d,e)=>(k.dynCall_jiji=Z.Fa)(a,b,c,d,e);k.dynCall_iiiiij=(a,b,c,d,e,f,g)=>(k.dynCall_iiiiij=Z.Ga)(a,b,c,d,e,f,g);k.dynCall_iiiiijj=(a,b,c,d,e,f,g,h,m)=>(k.dynCall_iiiiijj=Z.Ha)(a,b,c,d,e,f,g,h,m);
k.dynCall_iiiiiijj=(a,b,c,d,e,f,g,h,m,l)=>(k.dynCall_iiiiiijj=Z.Ia)(a,b,c,d,e,f,g,h,m,l);k.out=ha;var xd;wa=function yd(){xd||zd();xd||(wa=yd)};function Ad(a=[]){var b=vd;a.unshift(ca);var c=a.length,d=wd(4*(c+1)),e=d;a.forEach(g=>{var h=F,m=e>>2,l=Qb(g)+1,n=wd(l);X(g,C,n,l);h[m]=n;e+=4});F[e>>2]=0;try{var f=b(c,d);Dc(f)}catch(g){Bc(g)}}
function zd(){var a=ba;function b(){if(!xd&&(xd=!0,k.calledRun=!0,!x)){ta=!0;Ga(qa);Ga(ra);k.onRuntimeInitialized?.();Bd&&Ad(a);if(k.postRun)for("function"==typeof k.postRun&&(k.postRun=[k.postRun]);k.postRun.length;){var c=k.postRun.shift();sa.unshift(c)}Ga(sa)}}if(!(0<G)){if(k.preRun)for("function"==typeof k.preRun&&(k.preRun=[k.preRun]);k.preRun.length;)ua();Ga(pa);0<G||(k.setStatus?(k.setStatus("Running..."),setTimeout(function(){setTimeout(function(){k.setStatus("")},1);b()},1)):b())}}
if(k.preInit)for("function"==typeof k.preInit&&(k.preInit=[k.preInit]);0<k.preInit.length;)k.preInit.pop()();var Bd=!0;k.noInitialRun&&(Bd=!1);zd();var sd=!1,td=-1;
(function(){function a(){if(c&&ta){var f=c;c=null;f.forEach(function(g){onmessage(g)})}}function b(){a();c&&setTimeout(b,100)}var c=null,d=0,e=0;onmessage=f=>{if(ta){a();var g=k["_"+f.data.funcName];if(!g)throw"invalid worker function to call: "+f.data.funcName;var h=f.data.data;if(h){h.byteLength||(h=new Uint8Array(h));if(!d||e<h.length)d&&S(d),e=h.length,d=pd(h.length);C.set(h,d)}sd=!1;td=f.data.callbackId;h?g(d,h.length):g(0,0)}else c||(c=[],setTimeout(b,100)),c.push(f)}})();
