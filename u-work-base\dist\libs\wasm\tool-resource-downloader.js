
var ResourceDownloaderModule = (() => {
  var _scriptName = typeof document != 'undefined' ? document.currentScript?.src : undefined;
  
  return (
function(moduleArg = {}) {
  var moduleRtn;

var k=moduleArg,aa,ba,ca=new Promise((a,b)=>{aa=a;ba=b}),da=Object.assign({},k),ea=[],fa="./this.program",ha=(a,b)=>{throw b;},u="",ia;"undefined"!=typeof document&&document.currentScript&&(u=document.currentScript.src);_scriptName&&(u=_scriptName);u.startsWith("blob:")?u="":u=u.substr(0,u.replace(/[?#].*/,"").lastIndexOf("/")+1);ia=a=>fetch(a,{credentials:"same-origin"}).then(b=>b.ok?b.arrayBuffer():Promise.reject(Error(b.status+" : "+b.url)));
var ja=k.print||console.log.bind(console),w=k.printErr||console.error.bind(console);Object.assign(k,da);da=null;k.arguments&&(ea=k.arguments);k.thisProgram&&(fa=k.thisProgram);k.quit&&(ha=k.quit);var ka;k.wasmBinary&&(ka=k.wasmBinary);var la,x=!1,ma,z,C,D,na,E,F,oa,pa;
function qa(){var a=la.buffer;k.HEAP8=z=new Int8Array(a);k.HEAP16=D=new Int16Array(a);k.HEAPU8=C=new Uint8Array(a);k.HEAPU16=na=new Uint16Array(a);k.HEAP32=E=new Int32Array(a);k.HEAPU32=F=new Uint32Array(a);k.HEAPF32=oa=new Float32Array(a);k.HEAPF64=pa=new Float64Array(a)}var ra=[],sa=[],ta=[],ua=[];function va(){var a=k.preRun.shift();ra.unshift(a)}var G=0,wa=null,xa=null;
function ya(a){k.onAbort?.(a);a="Aborted("+a+")";w(a);x=!0;ma=1;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");ba(a);throw a;}var za=a=>a.startsWith("data:application/octet-stream;base64,"),Aa;function Ba(a){if(a==Aa&&ka)return new Uint8Array(ka);throw"both async and sync fetching of the wasm failed";}function Ca(a){return ka?Promise.resolve().then(()=>Ba(a)):ia(a).then(b=>new Uint8Array(b),()=>Ba(a))}
function Da(a,b,c){return Ca(a).then(d=>WebAssembly.instantiate(d,b)).then(c,d=>{w(`failed to asynchronously prepare wasm: ${d}`);ya(d)})}function Ea(a,b){var c=Aa;return ka||"function"!=typeof WebAssembly.instantiateStreaming||za(c)||"function"!=typeof fetch?Da(c,a,b):fetch(c,{credentials:"same-origin"}).then(d=>WebAssembly.instantiateStreaming(d,a).then(b,function(e){w(`wasm streaming compile failed: ${e}`);w("falling back to ArrayBuffer instantiation");return Da(c,a,b)}))}
var H,Fa={1101516:(a,b)=>{a=a?I(C,a):"";b=b?I(C,b):"";window[a]&&delete window[a][b]},1101660:()=>{function a(c){b||=c;k.Fb.update(c-b);requestAnimationFrame(a);b=c}let b=0;requestAnimationFrame(a)}};function Ga(a){this.name="ExitStatus";this.message=`Program terminated with exit(${a})`;this.status=a}var Ha=a=>{for(;0<a.length;)a.shift()(k)},Ia=k.noExitRuntime||!0;class Ja{constructor(a){this.tb=a;this.Ga=a-24}}
var Ka=0,La=0,Ma="undefined"!=typeof TextDecoder?new TextDecoder:void 0,I=(a,b,c)=>{var d=b+c;for(c=b;a[c]&&!(c>=d);)++c;if(16<c-b&&a.buffer&&Ma)return Ma.decode(a.subarray(b,c));for(d="";b<c;){var e=a[b++];if(e&128){var f=a[b++]&63;if(192==(e&224))d+=String.fromCharCode((e&31)<<6|f);else{var g=a[b++]&63;e=224==(e&240)?(e&15)<<12|f<<6|g:(e&7)<<18|f<<12|g<<6|a[b++]&63;65536>e?d+=String.fromCharCode(e):(e-=65536,d+=String.fromCharCode(55296|e>>10,56320|e&1023))}}else d+=String.fromCharCode(e)}return d},
J=(a,b)=>Object.defineProperty(b,"name",{value:a}),Na=[],K=[],L,Oa=a=>{throw new L(a);},M=a=>{if(!a)throw new L("Cannot use deleted val. handle = "+a);return K[a]},N=a=>{switch(a){case void 0:return 2;case null:return 4;case !0:return 6;case !1:return 8;default:const b=Na.pop()||K.length;K[b]=a;K[b+1]=1;return b}},Pa=a=>{var b=Error,c=J(a,function(d){this.name=a;this.message=d;d=Error(d).stack;void 0!==d&&(this.stack=this.toString()+"\n"+d.replace(/^Error(:[^\n]*)?\n/,""))});c.prototype=Object.create(b.prototype);
c.prototype.constructor=c;c.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`};return c},Qa,Ra,O=a=>{for(var b="";C[a];)b+=Ra[C[a++]];return b},Sa=[],Ta=()=>{for(;Sa.length;){var a=Sa.pop();a.wa.Sa=!1;a["delete"]()}},Ua,P={},Va=(a,b)=>{if(void 0===b)throw new L("ptr should not be undefined");for(;a.Ja;)b=a.Va(b),a=a.Ja;return b},R={},Xa=a=>{a=Wa(a);var b=O(a);S(a);return b},Ya=(a,b)=>{var c=R[a];if(void 0===c)throw a=`${b} has unknown type ${Xa(a)}`,
new L(a);return c},Za=()=>{},$a=!1,ab=(a,b,c)=>{if(b===c)return a;if(void 0===c.Ja)return null;a=ab(a,b,c.Ja);return null===a?null:c.sb(a)},bb={},cb=(a,b)=>{b=Va(a,b);return P[b]},db,fb=(a,b)=>{if(!b.Ia||!b.Ga)throw new db("makeClassHandle requires ptr and ptrType");if(!!b.Oa!==!!b.Ka)throw new db("Both smartPtrType and smartPtr must be specified");b.count={value:1};return eb(Object.create(a,{wa:{value:b,writable:!0}}))},eb=a=>{if("undefined"===typeof FinalizationRegistry)return eb=b=>b,a;$a=new FinalizationRegistry(b=>
{b=b.wa;--b.count.value;0===b.count.value&&(b.Ka?b.Oa.Na(b.Ka):b.Ia.Ha.Na(b.Ga))});eb=b=>{var c=b.wa;c.Ka&&$a.register(b,{wa:c},b);return b};Za=b=>{$a.unregister(b)};return eb(a)},gb={},hb=a=>{for(;a.length;){var b=a.pop();a.pop()(b)}};function ib(a){return this.fromWireType(F[a>>2])}
var jb={},kb={},U=(a,b,c)=>{function d(h){h=c(h);if(h.length!==a.length)throw new db("Mismatched type converter count");for(var m=0;m<a.length;++m)T(a[m],h[m])}a.forEach(function(h){kb[h]=b});var e=Array(b.length),f=[],g=0;b.forEach((h,m)=>{R.hasOwnProperty(h)?e[m]=R[h]:(f.push(h),jb.hasOwnProperty(h)||(jb[h]=[]),jb[h].push(()=>{e[m]=R[h];++g;g===f.length&&d(e)}))});0===f.length&&d(e)},lb={};
function mb(a,b,c={}){var d=b.name;if(!a)throw new L(`type "${d}" must have a positive integer typeid pointer`);if(R.hasOwnProperty(a)){if(c.yb)return;throw new L(`Cannot register type '${d}' twice`);}R[a]=b;delete kb[a];jb.hasOwnProperty(a)&&(b=jb[a],delete jb[a],b.forEach(e=>e()))}function T(a,b,c={}){if(!("argPackAdvance"in b))throw new TypeError("registerType registeredInstance requires argPackAdvance");return mb(a,b,c)}var nb=a=>{throw new L(a.wa.Ia.Ha.name+" instance already deleted");};
function ob(){}
var pb=(a,b,c)=>{if(void 0===a[b].Ma){var d=a[b];a[b]=function(...e){if(!a[b].Ma.hasOwnProperty(e.length))throw new L(`Function '${c}' called with an invalid number of arguments (${e.length}) - expects one of (${a[b].Ma})!`);return a[b].Ma[e.length].apply(this,e)};a[b].Ma=[];a[b].Ma[d.Ra]=d}},qb=(a,b)=>{if(k.hasOwnProperty(a))throw new L(`Cannot register public name '${a}' twice`);k[a]=b},rb=a=>{if(void 0===a)return"_unknown";a=a.replace(/[^a-zA-Z0-9_]/g,"$");var b=a.charCodeAt(0);return 48<=b&&57>=
b?`_${a}`:a};function sb(a,b,c,d,e,f,g,h){this.name=a;this.constructor=b;this.Pa=c;this.Na=d;this.Ja=e;this.vb=f;this.Va=g;this.sb=h;this.ob=[]}var ub=(a,b,c)=>{for(;b!==c;){if(!b.Va)throw new L(`Expected null or instance of ${c.name}, got an instance of ${b.name}`);a=b.Va(a);b=b.Ja}return a};
function vb(a,b){if(null===b){if(this.fb)throw new L(`null is not a valid ${this.name}`);return 0}if(!b.wa)throw new L(`Cannot pass "${wb(b)}" as a ${this.name}`);if(!b.wa.Ga)throw new L(`Cannot pass deleted object as a pointer of type ${this.name}`);return ub(b.wa.Ga,b.wa.Ia.Ha,this.Ha)}
function xb(a,b){if(null===b){if(this.fb)throw new L(`null is not a valid ${this.name}`);if(this.Za){var c=this.Ua();null!==a&&a.push(this.Na,c);return c}return 0}if(!b||!b.wa)throw new L(`Cannot pass "${wb(b)}" as a ${this.name}`);if(!b.wa.Ga)throw new L(`Cannot pass deleted object as a pointer of type ${this.name}`);if(!this.Ya&&b.wa.Ia.Ya)throw new L(`Cannot convert argument of type ${b.wa.Oa?b.wa.Oa.name:b.wa.Ia.name} to parameter type ${this.name}`);c=ub(b.wa.Ga,b.wa.Ia.Ha,this.Ha);if(this.Za){if(void 0===
b.wa.Ka)throw new L("Passing raw pointer to smart pointer is illegal");switch(this.Cb){case 0:if(b.wa.Oa===this)c=b.wa.Ka;else throw new L(`Cannot convert argument of type ${b.wa.Oa?b.wa.Oa.name:b.wa.Ia.name} to parameter type ${this.name}`);break;case 1:c=b.wa.Ka;break;case 2:if(b.wa.Oa===this)c=b.wa.Ka;else{var d=b.clone();c=this.Bb(c,N(()=>d["delete"]()));null!==a&&a.push(this.Na,c)}break;default:throw new L("Unsupporting sharing policy");}}return c}
function yb(a,b){if(null===b){if(this.fb)throw new L(`null is not a valid ${this.name}`);return 0}if(!b.wa)throw new L(`Cannot pass "${wb(b)}" as a ${this.name}`);if(!b.wa.Ga)throw new L(`Cannot pass deleted object as a pointer of type ${this.name}`);if(b.wa.Ia.Ya)throw new L(`Cannot convert argument of type ${b.wa.Ia.name} to parameter type ${this.name}`);return ub(b.wa.Ga,b.wa.Ia.Ha,this.Ha)}
function zb(a,b,c,d,e,f,g,h,m,l,n){this.name=a;this.Ha=b;this.fb=c;this.Ya=d;this.Za=e;this.Ab=f;this.Cb=g;this.pb=h;this.Ua=m;this.Bb=l;this.Na=n;e||void 0!==b.Ja?this.toWireType=xb:(this.toWireType=d?vb:yb,this.La=null)}
var Ab=(a,b)=>{if(!k.hasOwnProperty(a))throw new db("Replacing nonexistent public symbol");k[a]=b;k[a].Ra=void 0},Bb=[],Cb,V=a=>{var b=Bb[a];b||(a>=Bb.length&&(Bb.length=a+1),Bb[a]=b=Cb.get(a));return b},Db=(a,b,c=[])=>{a.includes("j")?(a=a.replace(/p/g,"i"),b=(0,k["dynCall_"+a])(b,...c)):b=V(b)(...c);return b},Eb=(a,b)=>(...c)=>Db(a,b,c),W=(a,b)=>{a=O(a);var c=a.includes("j")?Eb(a,b):V(b);if("function"!=typeof c)throw new L(`unknown function pointer with signature ${a}: ${b}`);return c},Fb,Gb=(a,
b)=>{function c(f){e[f]||R[f]||(kb[f]?kb[f].forEach(c):(d.push(f),e[f]=!0))}var d=[],e={};b.forEach(c);throw new Fb(`${a}: `+d.map(Xa).join([", "]));};function Hb(a){for(var b=1;b<a.length;++b)if(null!==a[b]&&void 0===a[b].La)return!0;return!1}
function Ib(a){var b=Function;if(!(b instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof b} which is not a function`);var c=J(b.name||"unknownFunctionName",function(){});c.prototype=b.prototype;c=new c;a=b.apply(c,a);return a instanceof Object?a:c}
function Jb(a,b,c,d,e,f){var g=b.length;if(2>g)throw new L("argTypes array size mismatch! Must at least get return value and 'this' types!");var h=null!==b[1]&&null!==c,m=Hb(b);c="void"!==b[0].name;d=[a,Oa,d,e,hb,b[0],b[1]];for(e=0;e<g-2;++e)d.push(b[e+2]);if(!m)for(e=h?1:2;e<b.length;++e)null!==b[e].La&&d.push(b[e].La);m=Hb(b);e=b.length;var l="",n="";for(g=0;g<e-2;++g)l+=(0!==g?", ":"")+"arg"+g,n+=(0!==g?", ":"")+"arg"+g+"Wired";l=`\n        return function (${l}) {\n        if (arguments.length !== ${e-
2}) {\n          throwBindingError('function ' + humanName + ' called with ' + arguments.length + ' arguments, expected ${e-2}');\n        }`;m&&(l+="var destructors = [];\n");var q=m?"destructors":"null",v="humanName throwBindingError invoker fn runDestructors retType classParam".split(" ");h&&(l+="var thisWired = classParam['toWireType']("+q+", this);\n");for(g=0;g<e-2;++g)l+="var arg"+g+"Wired = argType"+g+"['toWireType']("+q+", arg"+g+");\n",v.push("argType"+g);h&&(n="thisWired"+(0<n.length?", ":
"")+n);l+=(c||f?"var rv = ":"")+"invoker(fn"+(0<n.length?", ":"")+n+");\n";if(m)l+="runDestructors(destructors);\n";else for(g=h?1:2;g<b.length;++g)f=1===g?"thisWired":"arg"+(g-2)+"Wired",null!==b[g].La&&(l+=`${f}_dtor(${f});\n`,v.push(`${f}_dtor`));c&&(l+="var ret = retType['fromWireType'](rv);\nreturn ret;\n");let [y,t]=[v,l+"}\n"];y.push(t);b=Ib(y)(...d);return J(a,b)}
var Kb=(a,b)=>{for(var c=[],d=0;d<a;d++)c.push(F[b+4*d>>2]);return c},Lb=a=>{a=a.trim();const b=a.indexOf("(");return-1!==b?a.substr(0,b):a},Mb=a=>{9<a&&0===--K[a+1]&&(K[a]=void 0,Na.push(a))},Nb={name:"emscripten::val",fromWireType:a=>{var b=M(a);Mb(a);return b},toWireType:(a,b)=>N(b),argPackAdvance:8,readValueFromPointer:ib,La:null},Ob=(a,b,c)=>{switch(b){case 1:return c?function(d){return this.fromWireType(z[d])}:function(d){return this.fromWireType(C[d])};case 2:return c?function(d){return this.fromWireType(D[d>>
1])}:function(d){return this.fromWireType(na[d>>1])};case 4:return c?function(d){return this.fromWireType(E[d>>2])}:function(d){return this.fromWireType(F[d>>2])};default:throw new TypeError(`invalid integer width (${b}): ${a}`);}},wb=a=>{if(null===a)return"null";var b=typeof a;return"object"===b||"array"===b||"function"===b?a.toString():""+a},Pb=(a,b)=>{switch(b){case 4:return function(c){return this.fromWireType(oa[c>>2])};case 8:return function(c){return this.fromWireType(pa[c>>3])};default:throw new TypeError(`invalid float width (${b}): ${a}`);
}},Qb=(a,b,c)=>{switch(b){case 1:return c?d=>z[d]:d=>C[d];case 2:return c?d=>D[d>>1]:d=>na[d>>1];case 4:return c?d=>E[d>>2]:d=>F[d>>2];default:throw new TypeError(`invalid integer width (${b}): ${a}`);}},X=(a,b,c,d)=>{if(!(0<d))return 0;var e=c;d=c+d-1;for(var f=0;f<a.length;++f){var g=a.charCodeAt(f);if(55296<=g&&57343>=g){var h=a.charCodeAt(++f);g=65536+((g&1023)<<10)|h&1023}if(127>=g){if(c>=d)break;b[c++]=g}else{if(2047>=g){if(c+1>=d)break;b[c++]=192|g>>6}else{if(65535>=g){if(c+2>=d)break;b[c++]=
224|g>>12}else{if(c+3>=d)break;b[c++]=240|g>>18;b[c++]=128|g>>12&63}b[c++]=128|g>>6&63}b[c++]=128|g&63}}b[c]=0;return c-e},Rb=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);127>=d?b++:2047>=d?b+=2:55296<=d&&57343>=d?(b+=4,++c):b+=3}return b},Sb="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,Tb=(a,b)=>{var c=a>>1;for(var d=c+b/2;!(c>=d)&&na[c];)++c;c<<=1;if(32<c-a&&Sb)return Sb.decode(C.subarray(a,c));c="";for(d=0;!(d>=b/2);++d){var e=D[a+2*d>>1];if(0==e)break;c+=String.fromCharCode(e)}return c},
Ub=(a,b,c)=>{c??=2147483647;if(2>c)return 0;c-=2;var d=b;c=c<2*a.length?c/2:a.length;for(var e=0;e<c;++e)D[b>>1]=a.charCodeAt(e),b+=2;D[b>>1]=0;return b-d},Vb=a=>2*a.length,Wb=(a,b)=>{for(var c=0,d="";!(c>=b/4);){var e=E[a+4*c>>2];if(0==e)break;++c;65536<=e?(e-=65536,d+=String.fromCharCode(55296|e>>10,56320|e&1023)):d+=String.fromCharCode(e)}return d},Xb=(a,b,c)=>{c??=2147483647;if(4>c)return 0;var d=b;c=d+c-4;for(var e=0;e<a.length;++e){var f=a.charCodeAt(e);if(55296<=f&&57343>=f){var g=a.charCodeAt(++e);
f=65536+((f&1023)<<10)|g&1023}E[b>>2]=f;b+=4;if(b+4>c)break}E[b>>2]=0;return b-d},Yb=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);55296<=d&&57343>=d&&++c;b+=4}return b},Zb=(a,b,c)=>{var d=[];a=a.toWireType(d,c);d.length&&(F[b>>2]=N(d));return a},$b=[],ac={},bc=a=>{var b=ac[a];return void 0===b?O(a):b},cc=[],dc=()=>"object"==typeof globalThis?globalThis:Function("return this")(),ec=a=>{var b=$b.length;$b.push(a);return b},fc=(a,b)=>{for(var c=Array(a),d=0;d<a;++d)c[d]=Ya(F[b+4*d>>2],"parameter "+
d);return c},gc=a=>0===a%4&&(0!==a%100||0===a%400),hc=[0,31,60,91,121,152,182,213,244,274,305,335],ic=[0,31,59,90,120,151,181,212,243,273,304,334],jc=[],tc=(a,b)=>{kc=a;lc=b;if(mc)if(nc||=!0,0==a)Y=function(){var d=Math.max(0,oc+b-pc())|0;setTimeout(qc,d)};else if(1==a)Y=function(){rc(qc)};else if(2==a){if("undefined"==typeof sc)if("undefined"==typeof setImmediate){var c=[];addEventListener("message",d=>{if("setimmediate"===d.data||"setimmediate"===d.data.target)d.stopPropagation(),c.shift()()},!0);
sc=function(d){c.push(d);postMessage("setimmediate","*")}}else sc=setImmediate;Y=function(){sc(qc)}}},pc;pc=()=>performance.now();
var Bc=a=>{mc=a;var b=vc;nc=!1;qc=function(){if(!x)if(0<wc.length){var c=wc.shift();c.ab(c.$a);if(xc){var d=xc,e=0==d%1?d-1:Math.floor(d);xc=c.Gb?e:(8*d+(e+.5))/9}k.setStatus&&(c=k.statusMessage||"Please wait...",d=xc,e=yc.Ib,d?d<e?k.setStatus("{message} ({expected - remaining}/{expected})"):k.setStatus(c):k.setStatus(""));b<vc||setTimeout(qc,0)}else b<vc||(zc=zc+1|0,1==kc&&1<lc&&0!=zc%lc?Y():(0==kc&&(oc=pc()),x||k.preMainLoop&&!1===k.preMainLoop()||(Ac(a),k.postMainLoop?.()),b<vc||("object"==typeof SDL&&
SDL.audio?.Mb?.(),Y())))}},Cc=a=>{a instanceof Ga||"unwind"==a||ha(1,a)},Dc=a=>{ma=a;Ia||(k.onExit?.(a),x=!0);ha(a,new Ga(a))},Ec=a=>{ma=a;Dc(a)},Ac=a=>{if(!x)try{if(a(),!Ia)try{ma=a=ma,Dc(a)}catch(b){Cc(b)}}catch(b){Cc(b)}},Fc=a=>{Fc.jb||(Fc.jb={});Fc.jb[a]||(Fc.jb[a]=1,w(a))},nc=!1,Y=null,vc=0,mc=null,kc=0,lc=0,zc=0,wc=[],yc={},oc,qc,xc,Gc=!1,Hc=!1,Ic=[],Jc=[];
function Kc(){function a(){Hc=document.pointerLockElement===k.canvas||document.mozPointerLockElement===k.canvas||document.webkitPointerLockElement===k.canvas||document.msPointerLockElement===k.canvas}if(!Lc){Lc=!0;var b=k.canvas;b&&(b.requestPointerLock=b.requestPointerLock||b.mozRequestPointerLock||b.webkitRequestPointerLock||b.msRequestPointerLock||(()=>{}),b.exitPointerLock=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||(()=>{}),
b.exitPointerLock=b.exitPointerLock.bind(document),document.addEventListener("pointerlockchange",a,!1),document.addEventListener("mozpointerlockchange",a,!1),document.addEventListener("webkitpointerlockchange",a,!1),document.addEventListener("mspointerlockchange",a,!1),k.elementPointerLock&&b.addEventListener("click",c=>{!Hc&&k.canvas.requestPointerLock&&(k.canvas.requestPointerLock(),c.preventDefault())},!1))}}var Mc=!1,Nc=void 0,Oc=void 0;
function Pc(){if(!Gc)return!1;(document.exitFullscreen||document.cancelFullScreen||document.mozCancelFullScreen||document.msExitFullscreen||document.webkitCancelFullScreen||(()=>{})).apply(document,[]);return!0}var Qc=0;function rc(a){if("function"==typeof requestAnimationFrame)requestAnimationFrame(a);else{var b=Date.now();if(0===Qc)Qc=b+1E3/60;else for(;b+2>=Qc;)Qc+=1E3/60;setTimeout(a,Math.max(Qc-b,0))}}var Rc=[];function Sc(){var a=k.canvas;Rc.forEach(b=>b(a.width,a.height))}
function Tc(a,b,c){b&&c?(a.Db=b,a.xb=c):(b=a.Db,c=a.xb);var d=b,e=c;k.forcedAspectRatio&&0<k.forcedAspectRatio&&(d/e<k.forcedAspectRatio?d=Math.round(e*k.forcedAspectRatio):e=Math.round(d/k.forcedAspectRatio));if((document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||document.webkitCurrentFullScreenElement)===a.parentNode&&"undefined"!=typeof screen){var f=Math.min(screen.width/d,screen.height/e);d=Math.round(d*f);e=Math.round(e*
f)}Oc?(a.width!=d&&(a.width=d),a.height!=e&&(a.height=e),"undefined"!=typeof a.style&&(a.style.removeProperty("width"),a.style.removeProperty("height"))):(a.width!=b&&(a.width=b),a.height!=c&&(a.height=c),"undefined"!=typeof a.style&&(d!=b||e!=c?(a.style.setProperty("width",d+"px","important"),a.style.setProperty("height",e+"px","important")):(a.style.removeProperty("width"),a.style.removeProperty("height"))))}var sc,Lc;
function Uc(){if("undefined"!=typeof indexedDB)return indexedDB;var a=null;"object"==typeof window&&(a=window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB);a||ya("IDBStore used, but indexedDB not supported");return a}var Vc={};
function Wc(a,b){var c=Vc[a];if(c)b(null,c);else{try{var d=Uc().open(a,22)}catch(e){b(e);return}d.onupgradeneeded=e=>{var f=e.target.result;e=e.target.transaction;f.objectStoreNames.contains("FILE_DATA")?e.objectStore("FILE_DATA"):f.createObjectStore("FILE_DATA")};d.onsuccess=()=>{c=d.result;Vc[a]=c;b(null,c)};d.onerror=function(e){b(e.target.error||"unknown error");e.preventDefault()}}}
function Xc(a,b,c){Wc(a,(d,e)=>{if(d)return c(d);d=e.transaction(["FILE_DATA"],b);d.onerror=f=>{c(f.target.error||"unknown error");f.preventDefault()};d=d.objectStore("FILE_DATA");c(null,d)})}function Yc(a,b,c){Xc(a,"readonly",(d,e)=>{if(d)return c(d);d=e.get(b);d.onsuccess=f=>(f=f.target.result)?c(null,f):c(`file ${b} not found`);d.onerror=c})}function Zc(a,b,c,d){Xc(a,"readwrite",(e,f)=>{if(e)return d(e);e=f.put(c,b);e.onsuccess=()=>d();e.onerror=d})}
function $c(a,b,c){Xc(a,"readwrite",(d,e)=>{if(d)return c(d);d=e.delete(b);d.onsuccess=()=>c();d.onerror=c})}function ad(a,b,c){Xc(a,"readonly",(d,e)=>{if(d)return c(d);d=e.count(b);d.onsuccess=f=>c(null,0<f.target.result);d.onerror=c})}function bd(a,b){Xc(a,"readwrite",(c,d)=>{if(c)return b(c);c=d.clear();c.onsuccess=()=>b();c.onerror=b})}
var cd=(a,b)=>{if(0>=a)return a;var c=32>=b?Math.abs(1<<b-1):Math.pow(2,b-1);a>=c&&(32>=b||a>c)&&(a=-2*c+a);return a},dd=(a,b)=>0<=a?a:32>=b?2*Math.abs(1<<b-1)+a:Math.pow(2,b)+a,ed=a=>{for(var b=a;C[b];)++b;return b-a};function fd(a){var b=Array(Rb(a)+1);a=X(a,b,0,b.length);b.length=a;return b}
var gd=(a,b)=>{function c(A){var Q=d;("double"===A||"i64"===A)&&Q&7&&(Q+=4);d=Q;"double"===A?(A=pa[d>>3],d+=8):"i64"==A?(A=[E[d>>2],E[d+4>>2]],d+=8):(A=E[d>>2],d+=4);return A}for(var d=b,e=[],f,g;;){var h=a;f=z[a];if(0===f)break;g=z[a+1];if(37==f){var m=!1,l=b=!1,n=!1,q=!1;a:for(;;){switch(g){case 43:m=!0;break;case 45:b=!0;break;case 35:l=!0;break;case 48:if(n)break a;else{n=!0;break}case 32:q=!0;break;default:break a}a++;g=z[a+1]}var v=0;if(42==g)v=c("i32"),a++,g=z[a+1];else for(;48<=g&&57>=g;)v=
10*v+(g-48),a++,g=z[a+1];var y=!1,t=-1;if(46==g){t=0;y=!0;a++;g=z[a+1];if(42==g)t=c("i32"),a++;else for(;;){g=z[a+1];if(48>g||57<g)break;t=10*t+(g-48);a++}g=z[a+1]}0>t&&(t=6,y=!1);switch(String.fromCharCode(g)){case "h":g=z[a+2];if(104==g){a++;var r=1}else r=2;break;case "l":g=z[a+2];108==g?(a++,r=8):r=4;break;case "L":case "q":case "j":r=8;break;case "z":case "t":case "I":r=4;break;default:r=null}r&&a++;g=z[a+1];switch(String.fromCharCode(g)){case "d":case "i":case "u":case "o":case "x":case "X":case "p":h=
100==g||105==g;r=r||4;f=c("i"+8*r);8==r&&(f=117==g?(f[0]>>>0)+4294967296*(f[1]>>>0):(f[0]>>>0)+4294967296*f[1]);4>=r&&(f=(h?cd:dd)(f&Math.pow(256,r)-1,8*r));var B=Math.abs(f);h="";if(100==g||105==g)var p=cd(f,8*r).toString(10);else if(117==g)p=dd(f,8*r).toString(10),f=Math.abs(f);else if(111==g)p=(l?"0":"")+B.toString(8);else if(120==g||88==g){h=l&&0!=f?"0x":"";if(0>f){f=-f;p=(B-1).toString(16);B=[];for(l=0;l<p.length;l++)B.push((15-parseInt(p[l],16)).toString(16));for(p=B.join("");p.length<2*r;)p=
"f"+p}else p=B.toString(16);88==g&&(h=h.toUpperCase(),p=p.toUpperCase())}else 112==g&&(0===B?p="(nil)":(h="0x",p=B.toString(16)));if(y)for(;p.length<t;)p="0"+p;0<=f&&(m?h="+"+h:q&&(h=" "+h));"-"==p.charAt(0)&&(h="-"+h,p=p.substr(1));for(;h.length+p.length<v;)b?p+=" ":n?p="0"+p:h=" "+h;p=h+p;p.split("").forEach(function(A){e.push(A.charCodeAt(0))});break;case "f":case "F":case "e":case "E":case "g":case "G":f=c("double");if(isNaN(f))p="nan",n=!1;else if(isFinite(f)){y=!1;r=Math.min(t,20);if(103==g||
71==g)y=!0,t=t||1,r=parseInt(f.toExponential(r).split("e")[1],10),t>r&&-4<=r?(g=(103==g?"f":"F").charCodeAt(0),t-=r+1):(g=(103==g?"e":"E").charCodeAt(0),t--),r=Math.min(t,20);if(101==g||69==g)p=f.toExponential(r),/[eE][-+]\d$/.test(p)&&(p=p.slice(0,-1)+"0"+p.slice(-1));else if(102==g||70==g)p=f.toFixed(r),0===f&&(0>f||0===f&&-Infinity===1/f)&&(p="-"+p);h=p.split("e");if(y&&!l)for(;1<h[0].length&&h[0].includes(".")&&("0"==h[0].slice(-1)||"."==h[0].slice(-1));)h[0]=h[0].slice(0,-1);else for(l&&-1==
p.indexOf(".")&&(h[0]+=".");t>r++;)h[0]+="0";p=h[0]+(1<h.length?"e"+h[1]:"");69==g&&(p=p.toUpperCase());0<=f&&(m?p="+"+p:q&&(p=" "+p))}else p=(0>f?"-":"")+"inf",n=!1;for(;p.length<v;)p=b?p+" ":!n||"-"!=p[0]&&"+"!=p[0]?(n?"0":" ")+p:p[0]+"0"+p.slice(1);97>g&&(p=p.toUpperCase());p.split("").forEach(function(A){e.push(A.charCodeAt(0))});break;case "s":n=(m=c("i8*"))?ed(m):6;y&&(n=Math.min(n,t));if(!b)for(;n<v--;)e.push(32);if(m)for(l=0;l<n;l++)e.push(C[m++]);else e=e.concat(fd("(null)".substr(0,n)));
if(b)for(;n<v--;)e.push(32);break;case "c":for(b&&e.push(c("i8"));0<--v;)e.push(32);b||e.push(c("i8"));break;case "n":b=c("i32*");E[b>>2]=e.length;break;case "%":e.push(f);break;default:for(l=h;l<a+2;l++)e.push(z[l])}a+=2}else e.push(f),a+=1}return e},hd={},kd=()=>{if(!jd){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:fa||"./this.program"},b;for(b in hd)void 0===
hd[b]?delete a[b]:a[b]=hd[b];var c=[];for(b in a)c.push(`${b}=${a[b]}`);jd=c}return jd},jd,ld=[null,[],[]],md=()=>{if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return a=>crypto.getRandomValues(a);ya("initRandomDevice")},nd=a=>(nd=md())(a);L=k.BindingError=class extends Error{constructor(a){super(a);this.name="BindingError"}};K.push(0,1,void 0,1,null,1,!0,1,!1,1);k.count_emval_handles=()=>K.length/2-5-Na.length;Qa=k.PureVirtualError=Pa("PureVirtualError");
for(var od=Array(256),pd=0;256>pd;++pd)od[pd]=String.fromCharCode(pd);Ra=od;k.getInheritedInstanceCount=()=>Object.keys(P).length;k.getLiveInheritedInstances=()=>{var a=[],b;for(b in P)P.hasOwnProperty(b)&&a.push(P[b]);return a};k.flushPendingDeletes=Ta;k.setDelayFunction=a=>{Ua=a;Sa.length&&Ua&&Ua(Ta)};db=k.InternalError=class extends Error{constructor(a){super(a);this.name="InternalError"}};
Object.assign(ob.prototype,{isAliasOf:function(a){if(!(this instanceof ob&&a instanceof ob))return!1;var b=this.wa.Ia.Ha,c=this.wa.Ga;a.wa=a.wa;var d=a.wa.Ia.Ha;for(a=a.wa.Ga;b.Ja;)c=b.Va(c),b=b.Ja;for(;d.Ja;)a=d.Va(a),d=d.Ja;return b===d&&c===a},clone:function(){this.wa.Ga||nb(this);if(this.wa.Ta)return this.wa.count.value+=1,this;var a=eb,b=Object,c=b.create,d=Object.getPrototypeOf(this),e=this.wa;a=a(c.call(b,d,{wa:{value:{count:e.count,Sa:e.Sa,Ta:e.Ta,Ga:e.Ga,Ia:e.Ia,Ka:e.Ka,Oa:e.Oa}}}));a.wa.count.value+=
1;a.wa.Sa=!1;return a},["delete"](){this.wa.Ga||nb(this);if(this.wa.Sa&&!this.wa.Ta)throw new L("Object already scheduled for deletion");Za(this);var a=this.wa;--a.count.value;0===a.count.value&&(a.Ka?a.Oa.Na(a.Ka):a.Ia.Ha.Na(a.Ga));this.wa.Ta||(this.wa.Ka=void 0,this.wa.Ga=void 0)},isDeleted:function(){return!this.wa.Ga},deleteLater:function(){this.wa.Ga||nb(this);if(this.wa.Sa&&!this.wa.Ta)throw new L("Object already scheduled for deletion");Sa.push(this);1===Sa.length&&Ua&&Ua(Ta);this.wa.Sa=!0;
return this}});
Object.assign(zb.prototype,{wb(a){this.pb&&(a=this.pb(a));return a},mb(a){this.Na?.(a)},argPackAdvance:8,readValueFromPointer:ib,fromWireType:function(a){function b(){return this.Za?fb(this.Ha.Pa,{Ia:this.Ab,Ga:c,Oa:this,Ka:a}):fb(this.Ha.Pa,{Ia:this,Ga:a})}var c=this.wb(a);if(!c)return this.mb(a),null;var d=cb(this.Ha,c);if(void 0!==d){if(0===d.wa.count.value)return d.wa.Ga=c,d.wa.Ka=a,d.clone();d=d.clone();this.mb(a);return d}d=this.Ha.vb(c);d=bb[d];if(!d)return b.call(this);d=this.Ya?d.rb:d.pointerType;
var e=ab(c,this.Ha,d.Ha);return null===e?b.call(this):this.Za?fb(d.Ha.Pa,{Ia:d,Ga:e,Oa:this,Ka:a}):fb(d.Ha.Pa,{Ia:d,Ga:e})}});Fb=k.UnboundTypeError=Pa("UnboundTypeError");
k.requestFullscreen=function(a,b){function c(){Gc=!1;var f=d.parentNode;(document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||document.webkitCurrentFullScreenElement)===f?(d.exitFullscreen=Pc,Nc&&d.requestPointerLock(),Gc=!0,Oc?("undefined"!=typeof SDL&&(E[SDL.screen>>2]=F[SDL.screen>>2]|8388608),Tc(k.canvas),Sc()):Tc(d)):(f.parentNode.insertBefore(d,f),f.parentNode.removeChild(f),Oc?("undefined"!=typeof SDL&&(E[SDL.screen>>2]=
F[SDL.screen>>2]&-8388609),Tc(k.canvas),Sc()):Tc(d));k.onFullScreen?.(Gc);k.onFullscreen?.(Gc)}Nc=a;Oc=b;"undefined"==typeof Nc&&(Nc=!0);"undefined"==typeof Oc&&(Oc=!1);var d=k.canvas;Mc||(Mc=!0,document.addEventListener("fullscreenchange",c,!1),document.addEventListener("mozfullscreenchange",c,!1),document.addEventListener("webkitfullscreenchange",c,!1),document.addEventListener("MSFullscreenChange",c,!1));var e=document.createElement("div");d.parentNode.insertBefore(e,d);e.appendChild(d);e.requestFullscreen=
e.requestFullscreen||e.mozRequestFullScreen||e.msRequestFullscreen||(e.webkitRequestFullscreen?()=>e.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):null)||(e.webkitRequestFullScreen?()=>e.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT):null);e.requestFullscreen()};k.requestAnimationFrame=rc;k.setCanvasSize=function(a,b,c){Tc(k.canvas,a,b);c||Sc()};k.pauseMainLoop=function(){Y=null;vc++};k.resumeMainLoop=function(){vc++;var a=kc,b=lc,c=mc;mc=null;Bc(c);tc(a,b);Y()};
k.getUserMedia=function(a){let b;(b=window).getUserMedia||(b.getUserMedia=navigator.getUserMedia||navigator.mozGetUserMedia);window.getUserMedia(a)};k.createContext=function(a,b,c,d){if(b&&k.lb&&a==k.canvas)return k.lb;var e;if(b){var f={antialias:!1,alpha:!1,Jb:2};if(d)for(var g in d)f[g]=d[g];if("undefined"!=typeof GL&&(e=GL.Hb(a,f)))var h=GL.getContext(e).Eb}else h=a.getContext("2d");if(!h)return null;c&&(k.lb=h,b&&GL.Kb(e),k.Nb=b,Ic.forEach(m=>m()),Kc());return h};
var td={t:(a,b,c)=>{var d=new Ja(a);F[d.Ga+16>>2]=0;F[d.Ga+4>>2]=b;F[d.Ga+8>>2]=c;Ka=a;La++;throw Ka;},X:function(){},R:()=>{},Z:()=>{ya("")},ia:(a,b,c)=>{a=O(a);b=Ya(b,"wrapper");c=M(c);var d=b.Ha,e=d.Pa,f=d.Ja.Pa,g=d.Ja.constructor;a=J(a,function(...h){d.Ja.ob.forEach(function(m){if(this[m]===f[m])throw new Qa(`Pure virtual function ${m} must be implemented in JavaScript`);}.bind(this));Object.defineProperty(this,"__parent",{value:e});this.__construct(...h)});e.__construct=function(...h){if(this===
e)throw new L("Pass correct 'this' to __construct");h=g.implement(this,...h);Za(h);var m=h.wa;h.notifyOnDestruction();m.Ta=!0;Object.defineProperties(this,{wa:{value:m}});eb(this);h=m.Ga;h=Va(d,h);if(P.hasOwnProperty(h))throw new L(`Tried to register registered instance: ${h}`);P[h]=this};e.__destruct=function(){if(this===e)throw new L("Pass correct 'this' to __destruct");Za(this);var h=this.wa.Ga;h=Va(d,h);if(P.hasOwnProperty(h))delete P[h];else throw new L(`Tried to unregister unregistered instance: ${h}`);
};a.prototype=Object.create(e);Object.assign(a.prototype,c);return N(a)},x:a=>{var b=gb[a];delete gb[a];var c=b.elements,d=c.length,e=c.map(h=>h.eb).concat(c.map(h=>h.hb)),f=b.Ua,g=b.Na;U([a],e,h=>{c.forEach((m,l)=>{var n=h[l],q=m.bb,v=m.cb,y=h[l+d],t=m.gb,r=m.ib;m.read=B=>n.fromWireType(q(v,B));m.write=(B,p)=>{var A=[];t(r,B,y.toWireType(A,p));hb(A)}});return[{name:b.name,fromWireType:m=>{for(var l=Array(d),n=0;n<d;++n)l[n]=c[n].read(m);g(m);return l},toWireType:(m,l)=>{if(d!==l.length)throw new TypeError(`Incorrect number of tuple elements for ${b.name}: expected=${d}, actual=${l.length}`);
for(var n=f(),q=0;q<d;++q)c[q].write(n,l[q]);null!==m&&m.push(g,n);return n},argPackAdvance:8,readValueFromPointer:ib,La:g}]})},B:a=>{var b=lb[a];delete lb[a];var c=b.Ua,d=b.Na,e=b.nb,f=e.map(g=>g.eb).concat(e.map(g=>g.hb));U([a],f,g=>{var h={};e.forEach((m,l)=>{var n=g[l],q=m.bb,v=m.cb,y=g[l+e.length],t=m.gb,r=m.ib;h[m.ub]={read:B=>n.fromWireType(q(v,B)),write:(B,p)=>{var A=[];t(r,B,y.toWireType(A,p));hb(A)}}});return[{name:b.name,fromWireType:m=>{var l={},n;for(n in h)l[n]=h[n].read(m);d(m);return l},
toWireType:(m,l)=>{for(var n in h)if(!(n in l))throw new TypeError(`Missing field: "${n}"`);var q=c();for(n in h)h[n].write(q,l[n]);null!==m&&m.push(d,q);return q},argPackAdvance:8,readValueFromPointer:ib,La:d}]})},O:()=>{},ka:(a,b,c,d)=>{b=O(b);T(a,{name:b,fromWireType:function(e){return!!e},toWireType:function(e,f){return f?c:d},argPackAdvance:8,readValueFromPointer:function(e){return this.fromWireType(C[e])},La:null})},v:(a,b,c,d,e,f,g,h,m,l,n,q,v)=>{n=O(n);f=W(e,f);h&&=W(g,h);l&&=W(m,l);v=W(q,
v);var y=rb(n);qb(y,function(){Gb(`Cannot construct ${n} due to unbound types`,[d])});U([a,b,c],d?[d]:[],t=>{t=t[0];if(d){var r=t.Ha;var B=r.Pa}else B=ob.prototype;t=J(n,function(...tb){if(Object.getPrototypeOf(this)!==p)throw new L("Use 'new' to construct "+n);if(void 0===A.Qa)throw new L(n+" has no accessible constructor");var uc=A.Qa[tb.length];if(void 0===uc)throw new L(`Tried to invoke ctor of ${n} with invalid number of parameters (${tb.length}) - expected (${Object.keys(A.Qa).toString()}) parameters instead!`);
return uc.apply(this,tb)});var p=Object.create(B,{constructor:{value:t}});t.prototype=p;var A=new sb(n,t,p,v,r,f,h,l);if(A.Ja){var Q;(Q=A.Ja).Wa??(Q.Wa=[]);A.Ja.Wa.push(A)}r=new zb(n,A,!0,!1,!1);Q=new zb(n+"*",A,!1,!1,!1);B=new zb(n+" const*",A,!1,!0,!1);bb[a]={pointerType:Q,rb:B};Ab(y,t);return[r,Q,B]})},s:(a,b,c,d,e,f,g,h)=>{var m=Kb(c,d);b=O(b);b=Lb(b);f=W(e,f);U([],[a],l=>{function n(){Gb(`Cannot call ${q} due to unbound types`,m)}l=l[0];var q=`${l.name}.${b}`;b.startsWith("@@")&&(b=Symbol[b.substring(2)]);
var v=l.Ha.constructor;void 0===v[b]?(n.Ra=c-1,v[b]=n):(pb(v,b,q),v[b].Ma[c-1]=n);U([],m,y=>{y=Jb(q,[y[0],null].concat(y.slice(1)),null,f,g,h);void 0===v[b].Ma?(y.Ra=c-1,v[b]=y):v[b].Ma[c-1]=y;if(l.Ha.Wa)for(const t of l.Ha.Wa)t.constructor.hasOwnProperty(b)||(t.constructor[b]=y);return[]});return[]})},A:(a,b,c,d,e,f)=>{var g=Kb(b,c);e=W(d,e);U([],[a],h=>{h=h[0];var m=`constructor ${h.name}`;void 0===h.Ha.Qa&&(h.Ha.Qa=[]);if(void 0!==h.Ha.Qa[b-1])throw new L(`Cannot register multiple constructors with identical number of parameters (${b-
1}) for class '${h.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);h.Ha.Qa[b-1]=()=>{Gb(`Cannot construct ${h.name} due to unbound types`,g)};U([],g,l=>{l.splice(1,0,null);h.Ha.Qa[b-1]=Jb(m,l,null,e,f);return[]});return[]})},k:(a,b,c,d,e,f,g,h,m)=>{var l=Kb(c,d);b=O(b);b=Lb(b);f=W(e,f);U([],[a],n=>{function q(){Gb(`Cannot call ${v} due to unbound types`,l)}n=n[0];var v=`${n.name}.${b}`;b.startsWith("@@")&&(b=Symbol[b.substring(2)]);h&&n.Ha.ob.push(b);
var y=n.Ha.Pa,t=y[b];void 0===t||void 0===t.Ma&&t.className!==n.name&&t.Ra===c-2?(q.Ra=c-2,q.className=n.name,y[b]=q):(pb(y,b,v),y[b].Ma[c-2]=q);U([],l,r=>{r=Jb(v,r,n,f,g,m);void 0===y[b].Ma?(r.Ra=c-2,y[b]=r):y[b].Ma[c-2]=r;return[]});return[]})},ja:a=>T(a,Nb),K:(a,b,c,d)=>{function e(){}b=O(b);e.values={};T(a,{name:b,constructor:e,fromWireType:function(f){return this.constructor.values[f]},toWireType:(f,g)=>g.value,argPackAdvance:8,readValueFromPointer:Ob(b,c,d),La:null});qb(b,e)},l:(a,b,c)=>{var d=
Ya(a,"enum");b=O(b);a=d.constructor;d=Object.create(d.constructor.prototype,{value:{value:c},constructor:{value:J(`${d.name}_${b}`,function(){})}});a.values[c]=d;a[b]=d},I:(a,b,c)=>{b=O(b);T(a,{name:b,fromWireType:d=>d,toWireType:(d,e)=>e,argPackAdvance:8,readValueFromPointer:Pb(b,c),La:null})},u:(a,b,c,d,e)=>{b=O(b);-1===e&&(e=4294967295);e=h=>h;if(0===d){var f=32-8*c;e=h=>h<<f>>>f}var g=b.includes("unsigned")?function(h,m){return m>>>0}:function(h,m){return m};T(a,{name:b,fromWireType:e,toWireType:g,
argPackAdvance:8,readValueFromPointer:Qb(b,c,0!==d),La:null})},p:(a,b,c)=>{function d(f){return new e(z.buffer,F[f+4>>2],F[f>>2])}var e=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][b];c=O(c);T(a,{name:c,fromWireType:d,argPackAdvance:8,readValueFromPointer:d},{yb:!0})},H:a=>{T(a,Nb)},J:(a,b)=>{b=O(b);var c="std::string"===b;T(a,{name:b,fromWireType:function(d){var e=F[d>>2],f=d+4;if(c)for(var g=f,h=0;h<=e;++h){var m=f+h;if(h==e||0==C[m]){g=g?I(C,g,
m-g):"";if(void 0===l)var l=g;else l+=String.fromCharCode(0),l+=g;g=m+1}}else{l=Array(e);for(h=0;h<e;++h)l[h]=String.fromCharCode(C[f+h]);l=l.join("")}S(d);return l},toWireType:function(d,e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));var f="string"==typeof e;if(!(f||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array))throw new L("Cannot pass non-string to std::string");var g=c&&f?Rb(e):e.length;var h=qd(4+g+1),m=h+4;F[h>>2]=g;if(c&&f)X(e,C,m,g+1);else if(f)for(f=
0;f<g;++f){var l=e.charCodeAt(f);if(255<l)throw S(m),new L("String has UTF-16 code units that do not fit in 8 bits");C[m+f]=l}else for(f=0;f<g;++f)C[m+f]=e[f];null!==d&&d.push(S,h);return h},argPackAdvance:8,readValueFromPointer:ib,La(d){S(d)}})},D:(a,b,c)=>{c=O(c);if(2===b){var d=Tb;var e=Ub;var f=Vb;var g=h=>na[h>>1]}else 4===b&&(d=Wb,e=Xb,f=Yb,g=h=>F[h>>2]);T(a,{name:c,fromWireType:h=>{for(var m=F[h>>2],l,n=h+4,q=0;q<=m;++q){var v=h+4+q*b;if(q==m||0==g(v))n=d(n,v-n),void 0===l?l=n:(l+=String.fromCharCode(0),
l+=n),n=v+b}S(h);return l},toWireType:(h,m)=>{if("string"!=typeof m)throw new L(`Cannot pass non-string to C++ string type ${c}`);var l=f(m),n=qd(4+l+b);F[n>>2]=l/b;e(m,n+4,l+b);null!==h&&h.push(S,n);return n},argPackAdvance:8,readValueFromPointer:ib,La(h){S(h)}})},y:(a,b,c,d,e,f)=>{gb[a]={name:O(b),Ua:W(c,d),Na:W(e,f),elements:[]}},i:(a,b,c,d,e,f,g,h,m)=>{gb[a].elements.push({eb:b,bb:W(c,d),cb:e,hb:f,gb:W(g,h),ib:m})},C:(a,b,c,d,e,f)=>{lb[a]={name:O(b),Ua:W(c,d),Na:W(e,f),nb:[]}},r:(a,b,c,d,e,f,
g,h,m,l)=>{lb[a].nb.push({ub:O(b),eb:c,bb:W(d,e),cb:f,hb:g,gb:W(h,m),ib:l})},la:(a,b)=>{b=O(b);T(a,{zb:!0,name:b,argPackAdvance:0,fromWireType:()=>{},toWireType:()=>{}})},Y:(a,b,c)=>C.copyWithin(a,b,b+c),j:(a,b,c)=>{a=M(a);b=Ya(b,"emval::as");return Zb(b,c,a)},m:(a,b,c,d)=>{a=$b[a];b=M(b);return a(null,b,c,d)},g:(a,b,c,d,e)=>{a=$b[a];b=M(b);c=bc(c);return a(b,b[c],d,e)},w:(a,b)=>N(new Promise((c,d)=>{F[a>>2]=N(c);F[b>>2]=N(()=>{try{var e=cc.pop();e||ya("no exception to throw");var f=e.tb;0==z[e.Ga+
13]&&(cc.push(e),z[e.Ga+13]=1,z[e.Ga+12]=0,La++);Ka=f;throw Ka;}catch(g){d(g)}})})),z:(a,b)=>{M(a).then(c=>{rd(b,N(c))})},a:Mb,q:(a,b)=>{a=M(a);b=M(b);return a==b},n:a=>{if(0===a)return N(dc());a=bc(a);return N(dc()[a])},e:(a,b,c)=>{b=fc(a,b);var d=b.shift();a--;var e="return function (obj, func, destructorsRef, args) {\n",f=0,g=[];0===c&&g.push("obj");for(var h=["retType"],m=[d],l=0;l<a;++l)g.push("arg"+l),h.push("argType"+l),m.push(b[l]),e+=`  var arg${l} = argType${l}.readValueFromPointer(args${f?
"+"+f:""});\n`,f+=b[l].argPackAdvance;e+=`  var rv = ${1===c?"new func":"func.call"}(${g.join(", ")});\n`;d.zb||(h.push("emval_returnValue"),m.push(Zb),e+="  return emval_returnValue(retType, destructorsRef, rv);\n");h.push(e+"};\n");a=Ib(h)(...m);c=`methodCaller<(${b.map(n=>n.name).join(", ")}) => ${d.name}>`;return ec(J(c,a))},f:(a,b)=>{a=M(a);b=M(b);return N(a[b])},d:a=>{9<a&&(K[a+1]+=1)},c:a=>N(bc(a)),b:a=>{var b=M(a);hb(b);Mb(a)},o:(a,b,c)=>{a=M(a);b=M(b);c=M(c);a[b]=c},h:(a,b)=>{a=Ya(a,"_emval_take_value");
a=a.readValueFromPointer(b);return N(a)},L:function(a,b,c){a=new Date(1E3*(b+2097152>>>0<4194305-!!a?(a>>>0)+4294967296*b:NaN));E[c>>2]=a.getSeconds();E[c+4>>2]=a.getMinutes();E[c+8>>2]=a.getHours();E[c+12>>2]=a.getDate();E[c+16>>2]=a.getMonth();E[c+20>>2]=a.getFullYear()-1900;E[c+24>>2]=a.getDay();E[c+28>>2]=(gc(a.getFullYear())?hc:ic)[a.getMonth()]+a.getDate()-1|0;E[c+36>>2]=-(60*a.getTimezoneOffset());b=(new Date(a.getFullYear(),6,1)).getTimezoneOffset();var d=(new Date(a.getFullYear(),0,1)).getTimezoneOffset();
E[c+32>>2]=(b!=d&&a.getTimezoneOffset()==Math.min(d,b))|0},M:function(a){var b=new Date(E[a+20>>2]+1900,E[a+16>>2],E[a+12>>2],E[a+8>>2],E[a+4>>2],E[a>>2],0),c=E[a+32>>2],d=b.getTimezoneOffset(),e=(new Date(b.getFullYear(),6,1)).getTimezoneOffset(),f=(new Date(b.getFullYear(),0,1)).getTimezoneOffset(),g=Math.min(f,e);0>c?E[a+32>>2]=Number(e!=f&&g==d):0<c!=(g==d)&&(e=Math.max(f,e),b.setTime(b.getTime()+6E4*((0<c?g:e)-d)));E[a+24>>2]=b.getDay();E[a+28>>2]=(gc(b.getFullYear())?hc:ic)[b.getMonth()]+b.getDate()-
1|0;E[a>>2]=b.getSeconds();E[a+4>>2]=b.getMinutes();E[a+8>>2]=b.getHours();E[a+12>>2]=b.getDate();E[a+16>>2]=b.getMonth();E[a+20>>2]=b.getYear();a=b.getTime();a=isNaN(a)?-1:a/1E3;sd((H=a,1<=+Math.abs(H)?0<H?+Math.floor(H/4294967296)>>>0:~~+Math.ceil((H-+(~~H>>>0))/4294967296)>>>0:0));return a>>>0},S:(a,b,c,d)=>{var e=(new Date).getFullYear(),f=new Date(e,0,1),g=new Date(e,6,1);e=f.getTimezoneOffset();var h=g.getTimezoneOffset();F[a>>2]=60*Math.max(e,h);E[b>>2]=Number(e!=h);a=m=>m.toLocaleTimeString(void 0,
{hour12:!1,timeZoneName:"short"}).split(" ")[1];f=a(f);g=a(g);h<e?(X(f,C,c,17),X(g,C,d,17)):(X(f,C,d,17),X(g,C,c,17))},ha:(a,b,c)=>{jc.length=0;for(var d;d=C[b++];){var e=105!=d;e&=112!=d;c+=e&&c%8?4:0;jc.push(112==d?F[c>>2]:105==d?E[c>>2]:pa[c>>3]);c+=e?8:4}return Fa[a](...jc)},_:(a,b,c,d,e,f)=>{b=b?I(C,b):"";a=Jc[a];var g=-1;e&&(g=a.Xa.length,a.Xa.push({ab:V(e),$a:f}),a.kb++);b={funcName:b,callbackId:g,data:c?new Uint8Array(C.subarray(c,c+d)):0};c?a.worker.postMessage(b,[b.data.buffer]):a.worker.postMessage(b)},
aa:a=>{a=a?I(C,a):"";var b=Jc.length;a={worker:new Worker(a),Xa:[],kb:0,buffer:0,bufferSize:0};a.worker.onmessage=function(c){if(!x){var d=Jc[b];if(d){var e=c.data.callbackId,f=d.Xa[e];if(f)if(c.data.finalResponse&&(d.kb--,d.Xa[e]=null),c=c.data.data){c.byteLength||(c=new Uint8Array(c));if(!d.buffer||d.bufferSize<c.length)d.buffer&&S(d.buffer),d.bufferSize=c.length,d.buffer=qd(c.length);C.set(c,d.buffer);f.ab(d.buffer,c.length,f.$a)}else f.ab(0,0,f.$a)}}};Jc.push(a);return b},E:()=>Date.now(),$:a=>
{var b=Jc[a];b.worker.terminate();b.buffer&&S(b.buffer);Jc[a]=null},ba:()=>{throw"unwind";},ca:(a,b,c,d)=>{bd(a?I(C,a):"",e=>{Ac(()=>{e?d&&V(d)(b):c&&V(c)(b)})})},ea:(a,b,c,d,e)=>{$c(a?I(C,a):"",b?I(C,b):"",f=>{Ac(()=>{f?e&&V(e)(c):d&&V(d)(c)})})},da:(a,b,c,d,e)=>{ad(a?I(C,a):"",b?I(C,b):"",(f,g)=>{Ac(()=>{f?e&&V(e)(c):d&&V(d)(c,g)})})},ga:(a,b,c,d,e)=>{Yc(a?I(C,a):"",b?I(C,b):"",(f,g)=>{Ac(()=>{if(f)e&&V(e)(c);else{var h=qd(g.length);C.set(g,h);V(d)(c,h,g.length);S(h)}})})},fa:(a,b,c,d,e,f,g)=>{Zc(a?
I(C,a):"",b?I(C,b):"",new Uint8Array(C.subarray(c,c+d)),h=>{Ac(()=>{h?g&&V(g)(e):f&&V(f)(e)})})},G:(a,b,c)=>{b=gd(b,c);c=I(b,0);if(a&24){b=c=c.replace(/\s+$/,"");c=0<c.length?"\n":"";var d=a,e=Error().stack.toString();e=e.slice(e.indexOf("\n",Math.max(e.lastIndexOf("_emscripten_log"),e.lastIndexOf("_emscripten_get_callstack")))+1);d&8&&"undefined"==typeof emscripten_source_map&&(Fc('Source map information is not available, emscripten_log with EM_LOG_C_STACK will be ignored. Build with "--pre-js $EMSCRIPTEN/src/emscripten-source-map.min.js" linker flag to add source map loading to code.'),
d=d^8|16);var f=e.split("\n");e="";var g=RegExp("\\s*(.*?)@(.*?):([0-9]+):([0-9]+)"),h=RegExp("\\s*(.*?)@(.*):(.*)(:(.*))?"),m=RegExp("\\s*at (.*?) \\((.*):(.*):(.*)\\)"),l;for(l in f){var n=f[l],q;if((q=m.exec(n))&&5==q.length){n=q[1];var v=q[2];var y=q[3];q=q[4]}else if((q=g.exec(n))||(q=h.exec(n)),q&&4<=q.length)n=q[1],v=q[2],y=q[3],q=q[4]|0;else{e+=n+"\n";continue}var t=!1;if(d&8){var r=emscripten_source_map.Lb({line:y,qb:q});if(t=r?.source)d&64&&(r.source=r.source.substring(r.source.replace(/\\/g,
"/").lastIndexOf("/")+1)),e+=`    at ${n} (${r.source}:${r.line}:${r.qb})\n`}if(d&16||!t)d&64&&(v=v.substring(v.replace(/\\/g,"/").lastIndexOf("/")+1)),e+=(t?`     = ${n}`:`    at ${n}`)+` (${v}:${y}:${q})\n`}e=e.replace(/\s+$/,"");c=b+(c+e)}a&1?a&4?console.error(c):a&2?console.warn(c):a&512?console.info(c):a&256?console.debug(c):console.log(c):a&6?w(c):ja(c)},Q:a=>{var b=C.length;a>>>=0;if(2147483648<a)return!1;for(var c=1;4>=c;c*=2){var d=b*(1+.2/c);d=Math.min(d,a+100663296);var e=Math;d=Math.max(a,
d);a:{e=(e.min.call(e,2147483648,d+(65536-d%65536)%65536)-la.buffer.byteLength+65535)/65536;try{la.grow(e);qa();var f=1;break a}catch(g){}f=void 0}if(f)return!0}return!1},T:(a,b)=>{var c=0;kd().forEach((d,e)=>{var f=b+c;e=F[a+4*e>>2]=f;for(f=0;f<d.length;++f)z[e++]=d.charCodeAt(f);z[e]=0;c+=d.length+1});return 0},U:(a,b)=>{var c=kd();F[a>>2]=c.length;var d=0;c.forEach(e=>d+=e.length+1);F[b>>2]=d;return 0},F:()=>{Fc("To close sockets with PROXY_POSIX_SOCKETS bridge, prefer to use the function shutdown() that is proxied, instead of close()");
return 0},W:()=>52,N:function(){return 70},V:(a,b,c,d)=>{for(var e=0,f=0;f<c;f++){var g=F[b>>2],h=F[b+4>>2];b+=8;for(var m=0;m<h;m++){var l=C[g+m],n=ld[a];0===l||10===l?((1===a?ja:w)(I(n,0)),n.length=0):n.push(l)}e+=h}F[d>>2]=e;return 0},P:(a,b)=>{nd(C.subarray(a,a+b));return 0}},Z=function(){function a(c){Z=c.exports;la=Z.ma;qa();Cb=Z.qa;sa.unshift(Z.na);G--;k.monitorRunDependencies?.(G);0==G&&(null!==wa&&(clearInterval(wa),wa=null),xa&&(c=xa,xa=null,c()));return Z}var b={a:td};G++;k.monitorRunDependencies?.(G);
if(k.instantiateWasm)try{return k.instantiateWasm(b,a)}catch(c){w(`Module.instantiateWasm callback failed with error: ${c}`),ba(c)}Aa||=za("tool-resource-downloader.wasm")?"tool-resource-downloader.wasm":k.locateFile?k.locateFile("tool-resource-downloader.wasm",u):u+"tool-resource-downloader.wasm";Ea(b,function(c){a(c.instance)}).catch(ba);return{}}(),Wa=a=>(Wa=Z.oa)(a),rd=(a,b)=>(rd=Z.pa)(a,b),qd=k._malloc=a=>(qd=k._malloc=Z.ra)(a),ud=k._main=(a,b)=>(ud=k._main=Z.sa)(a,b),S=k._free=a=>(S=k._free=
Z.ta)(a),sd=a=>(sd=Z.ua)(a),vd=a=>(vd=Z.va)(a);k.dynCall_viij=(a,b,c,d,e)=>(k.dynCall_viij=Z.xa)(a,b,c,d,e);k.dynCall_jii=(a,b,c)=>(k.dynCall_jii=Z.ya)(a,b,c);k.dynCall_ji=(a,b)=>(k.dynCall_ji=Z.za)(a,b);k.dynCall_iij=(a,b,c,d)=>(k.dynCall_iij=Z.Aa)(a,b,c,d);k.dynCall_viijj=(a,b,c,d,e,f,g)=>(k.dynCall_viijj=Z.Ba)(a,b,c,d,e,f,g);k.dynCall_jiji=(a,b,c,d,e)=>(k.dynCall_jiji=Z.Ca)(a,b,c,d,e);k.dynCall_iiiiij=(a,b,c,d,e,f,g)=>(k.dynCall_iiiiij=Z.Da)(a,b,c,d,e,f,g);
k.dynCall_iiiiijj=(a,b,c,d,e,f,g,h,m)=>(k.dynCall_iiiiijj=Z.Ea)(a,b,c,d,e,f,g,h,m);k.dynCall_iiiiiijj=(a,b,c,d,e,f,g,h,m,l)=>(k.dynCall_iiiiiijj=Z.Fa)(a,b,c,d,e,f,g,h,m,l);k.out=ja;var wd;xa=function xd(){wd||yd();wd||(xa=xd)};function zd(a=[]){var b=ud;a.unshift(fa);var c=a.length,d=vd(4*(c+1)),e=d;a.forEach(g=>{var h=F,m=e>>2,l=Rb(g)+1,n=vd(l);X(g,C,n,l);h[m]=n;e+=4});F[e>>2]=0;try{var f=b(c,d);Ec(f)}catch(g){Cc(g)}}
function yd(){var a=ea;function b(){if(!wd&&(wd=!0,k.calledRun=!0,!x)){Ha(sa);Ha(ta);aa(k);k.onRuntimeInitialized?.();Ad&&zd(a);if(k.postRun)for("function"==typeof k.postRun&&(k.postRun=[k.postRun]);k.postRun.length;){var c=k.postRun.shift();ua.unshift(c)}Ha(ua)}}if(!(0<G)){if(k.preRun)for("function"==typeof k.preRun&&(k.preRun=[k.preRun]);k.preRun.length;)va();Ha(ra);0<G||(k.setStatus?(k.setStatus("Running..."),setTimeout(function(){setTimeout(function(){k.setStatus("")},1);b()},1)):b())}}
if(k.preInit)for("function"==typeof k.preInit&&(k.preInit=[k.preInit]);0<k.preInit.length;)k.preInit.pop()();var Ad=!0;k.noInitialRun&&(Ad=!1);yd();moduleRtn=ca;


  return moduleRtn;
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = ResourceDownloaderModule;
else if (typeof define === 'function' && define['amd'])
  define([], () => ResourceDownloaderModule);
