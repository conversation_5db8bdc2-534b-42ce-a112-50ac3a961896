<!doctype html>
<html lang="zh_CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <script src="/chartDepend/vue.min.js"></script>
    <script src="/chartDepend/thing.charts.min.js"></script>
    <title>工作台 • 大屏图表预览</title>
    <style>
      html,
      body {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 0;
        margin: 0;
      }
      #screen-preview {
        width: 100%;
        height: 100%;
        overflow: auto;
      }
      #screen-preview > div {
        pointer-events: all !important;
      }
    </style>
  </head>
  <body>
    <div id="screen-preview"></div>
    <script>
      const paramsStr = window.location.search;
      const origin = window.location.origin;
      const params = new URLSearchParams(paramsStr);
      const path = params.get('path');
      const type = params.get('type');
      if (type === 'chart') {
        const jsonUrl = `${origin}${path}/package.json`;
        fetch(jsonUrl)
          .then((res) => res.json())
          .then((json) => {
            const config = JSON.parse(json.config);
            const width = config.width;
            const height = config.height;
            const screenDom = document.getElementById('screen-preview');
            screenDom.style.position = 'absolute';
            screenDom.style.top = '50%';
            screenDom.style.left = '50%';
            screenDom.style.width = width + 'px';
            screenDom.style.height = height + 'px';
            screenDom.style.overflow = 'hidden';
            screenDom.style.backgroundColor = '#000';
            screenDom.style.transform = 'translateX(-50%) translateY(-50%)';
            if (width > window.document.documentElement.clientWidth) {
              const scale = window.document.documentElement.clientWidth / width;
              screenDom.style.transform = `scale(${scale}, ${scale}) translateX(-50%) translateY(-50%)`;
              screenDom.style.transformOrigin = '0 0';
            } else if (height > window.document.documentElement.clientHeight) {
              const scale = window.document.documentElement.clientHeight / height;
              screenDom.style.transform = `scale(${scale}, ${scale}) translateX(-50%) translateY(-50%)`;
              screenDom.style.transformOrigin = '0 0';
            }
          });
      }
      // 加载大屏
      const loadScreen = async (path) => {
        const url = `${origin}${path}`;
        const screenIns = await THING.CHARTS.Utils.loadBundle(url, {
          container: '#screen-preview', // dom id
          offline: true,
          baseUrl: url,
          hideCanvasBackground: true, // 画布背景色是否设置为透明的，默认false
          dataSource: {
            disable: true, // 是否禁用RestAPI数据，默认false
          },
        });
      };
      loadScreen(path);
    </script>
  </body>
</html>
