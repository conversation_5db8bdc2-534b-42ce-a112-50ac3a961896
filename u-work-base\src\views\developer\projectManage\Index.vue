<!--
 * @Description: 项目管理
 * @Version: 1.0
 * @Autor: lcm
 * @Date: 2023-03-22 16:48:48
 * @LastEditors: hasaiki
 * @LastEditTime: 2025-05-13 19:40:44
-->
<template>
  <div class="project-manage" v-if="permissionStore.checkedEnterprise?.status !== 1">
    <div class="tag-search">
      <div class="tag-content" v-if="tagsList?.length">
        <div v-for="group in tagsList" :key="group.id" class="tag-group">
          <div v-if="group?.tags?.length" class="tag-group-name" @click="checkGroupAll(group)">
            {{ group.groupName }}
          </div>
          <div v-if="group?.tags?.length" class="tag-item">
            <a-checkbox-group v-model:value="tagsChecked[group.id]" style="width: 100%">
              <div v-for="tag in group.tags" :key="tag.id" class="tag-item-name">
                <a-checkbox :value="tag.id">{{ tag.tagName }}</a-checkbox>
              </div>
            </a-checkbox-group>
          </div>
        </div>
      </div>
      <div class="no-tag-content" v-else>
        <a-spin class="loading-icon" v-if="tagLoading" :spinning="tagLoading" />
        <Nodata v-if="!tagLoading" title="请绑定标签" />
      </div>
    </div>
    <div class="content-wrap">
      <div class="search-wrap">
        <div class="search-content">
          <div class="search-item">
            <span class="search-label">项目名称</span>
            <a-input v-model:value.trim="searchParam.name" class="search-input" placeholder="请输入名称查询" :allow-clear="true" @keyup.enter="getData()" />
          </div>
          <div class="search-btns">
            <a-button type="primary" class="search-btn" @click="clickSearch()"> 查询 </a-button>
            <!-- <a-button class="search-btn" @click="reset()"> 重置 </a-button> -->
          </div>
        </div>
        <div class="table-handle">
          <a-button v-if="hasPerm('sys-project:add')" type="primary" class="handle-btn" @click="handel('add', null)"> 新建项目 </a-button>
        </div>
      </div>
      <div class="table-wrap">
        <div ref="table" class="table-content">
          <a-table
            class="table"
            :scroll="{ y: scrollY }"
            :pagination="false"
            size="small"
            :loading="confirmLoading"
            :row-key="(record: any) => record.id"
            :columns="columns"
            :data-source="tabData"
            @change="paginationChange"
            v-if="hasPerm('sys-project:user-project-list')"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'name'">
                <a-popover v-if="record.previewUrl">
                  <template #content>
                    <img class="pre-img" :src="getImgUrl(record)" alt="" />
                  </template>
                  <div class="name">
                    <span class="name-text">{{ record.name }}</span>
                    <a class="copy-btn" title="复制项目编码" @click="copyCode(record.code)"><CopyOutlined /></a>
                  </div>
                </a-popover>
                <div v-else class="name">
                  <span class="name-text">{{ record.name }}</span>
                  <a class="copy-btn" title="复制项目编码" @click="copyCode(record.code)"><CopyOutlined /></a>
                </div>
              </template>
              <!-- 标签 -->
              <template v-if="column.dataIndex === 'tags'">
                <div class="tag-wrapper">
                  <div class="tag-list">
                    <div v-for="(ele, index) in record.functionExampleTags" :key="index" class="tag-item" :style="{ backgroundColor: ele.color }">
                      {{ ele.tagName }}
                    </div>
                  </div>
                </div>
              </template>
              <!-- 参与团队成员 -->
              <template v-if="column.dataIndex === 'developer'">
                <div class="edit-developer" v-if="record.exampleType === '1'">
                  <a-popover title="" trigger="hover" placement="topLeft">
                    <template #content>
                      <div class="all-content">
                        {{ getDeveloperText(record) }}
                      </div>
                    </template>
                    <div class="text-truncate">
                      {{ getDeveloperText(record) }}
                    </div>
                  </a-popover>
                  <FormOutlined v-if="hasPerm('sys-project:add-edit-user')" style="margin-top: 5px; margin-left: 5px" @click.stop="editDeveloper(record)" />
                </div>
                <div v-else>-</div>
              </template>
              <!-- 场景数 -->
              <template v-if="column.dataIndex === 'sceneCount'">
                <div class="campus-used-wrap" style="width: 100%">
                  <a-progress
                    :stroke-color="getStrokeColor(((record.scene || 0) * 100) / record.sceneCount)"
                    :showInfo="false"
                    :percent="Number((((record.scene || 0) * 100) / record.sceneCount).toFixed(2))"
                    size="small"
                  />
                  {{ `${record.scene || 0}/${record.sceneCount}` }}
                </div>
              </template>
              <!-- 用户数 -->
              <template v-if="column.dataIndex === 'userCount'">
                <div class="campus-used-wrap" style="width: 100%">
                  <a-progress
                    :stroke-color="getStrokeColor(((record.user || 0) * 100) / record.userCount)"
                    :showInfo="false"
                    :percent="Number((((record.user || 0) * 100) / record.userCount).toFixed(2))"
                    size="small"
                  />
                  {{ `${record.user || 0}/${record.userCount}` }}
                </div>
              </template>
              <template v-if="column.dataIndex === 'exampleType'">
                {{ formatProjectType(record.exampleType) }}
              </template>
              <template v-if="column.dataIndex === 'createTime'">
                <div class="campus-used-wrap overflow-ellipse" :title="formatDate(record.createTime)">
                  {{ formatDate(record.createTime) }}
                </div>
              </template>
              <template v-if="column.dataIndex === 'classified'">
                <div class="campus-used-wrap">
                  {{ record.classified === 1 ? '是' : '否' }}
                </div>
              </template>
              <!-- <template v-if="column.dataIndex === 'licenseExpiredTime'">
                                <div class="campus-used-wrap overflow-ellipse" :title="formatDate(record.licenseExpiredTime)">
                                    {{ formatDate(record.licenseExpiredTime) }}
                                </div>
                            </template> -->
              <template v-if="column.dataIndex === 'actions'">
                <div class="table-actions">
                  <a v-if="hasPerm('sys-project:edit')" type="text" @click="handel('edit', record)">编辑</a>
                  <a v-if="hasPerm('sys-project:copy') && record.exampleType === '1'" type="text" @click="handel('copy', record)">复制</a>
                  <a-popover style="width: 500px" title="下载选项" trigger="hover">
                    <template #content>
                      <div>
                        <a-tooltip title="仅下载项目数据">
                          <a @click="downloadItemData(record)" v-if="hasPerm('sys-project:download-project')">仅下载数据</a>
                        </a-tooltip>
                        |
                        <a-tooltip title="下载项目文件和安装包地址">
                          <a @click="downloadWithPak(record)" v-if="hasPerm('sys-install-manage:page')">仅下载文件和安装包</a>
                        </a-tooltip>
                      </div>
                    </template>
                    <a v-if="record.exampleType === '1'">下载</a>
                  </a-popover>

                  <a v-if="hasPerm('sys-project:project-system')" type="text" @click="goPedestal(record)">进入项目</a>

                  <a-dropdown>
                    <a class="ant-dropdown-link">
                      更多
                      <DownOutlined />
                    </a>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item>
                          <a-popconfirm v-if="hasPerm('sys-project:delete')" placement="topRight" title="确认删除？" @confirm="() => clickDelete(record)">
                            <a>删除</a>
                          </a-popconfirm>
                        </a-menu-item>
                        <a-menu-item v-if="hasPerm('sys-project:edit') && record.exampleType === '1'">
                          <a v-if="!record.exampleId && hasPerm('sys-project:edit')" type="text" @click="publishPro(record)">发布案例</a>
                          <a v-if="record.exampleId" type="text" @click="editProject(record)">编辑案例</a>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </template>
            </template>
            <template #expandedRowRender="{ record }">
              <div>创建者：{{ record.createName }}</div>
              <div>项目编码：{{ record.code }}</div>
              <div>授权到期：{{ formatDate(record.licenseExpiredTime) }}</div>
              <div>项目所属地：{{ record.formattedAddress || '-' }}</div>
            </template>
          </a-table>
          <div class="pagination">
            <a-pagination v-if="tabData.length > 0" v-bind="paginationConfig" @change="paginationChange" />
          </div>
        </div>
      </div>
    </div>
    <EditProjectDeveloper ref="editProjectDeveloperRef" @ok="clickSearch" />

    <!-- 新增项目 -->
    <AddAndEditForm ref="addEditFormRef" @ok="clickSearch" :projectType="projectType" />
    <!-- 下载安装包 -->
    <DownLoadPaksList ref="downLoadPaksListRef" />
    <!-- 发布案例 -->
    <AddProject ref="addProjectRef" @ok="backOk" />
    <PublishProject ref="editProjectRef" @ok="backOk" />
  </div>
  <div class="project-manage" v-else>
    <div class="no-use">您当前团队【{{ permissionStore.checkedEnterprise?.name }}】已被禁用，请点击左下角【我的团队】进行切换，并设为默认！</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import clipboard from 'clipboard';
import Nodata from '@/views/default/Nodata.vue';
import AddProject from './AddProject.vue';
import PublishProject from './PublishProject.vue';
import AddAndEditForm from './AddAndEditForm.vue';
import { queryProjectList, queryCompanyList, projectGroup, deleteProject } from '@/api/developer/projectManage';
import { useGlobalMessage } from '@/hooks/useGlobalMessage';
import { intoProject, intoProjectSingle } from '@/api/operator/companyManage';
import { formatDate, getStrokeColor, Decrypt } from '@/utils/util';
import useTableScrollY from '@/hooks/useTableScrollY';
import { useUserStore } from '@/store/user';
import EditProjectDeveloper from './EditProjectDeveloper.vue';
import { usePermissionStore } from '@/store/permission';
import { getDropdown } from '@/api/operator/system/developConfig';
import { getSys, getPakBySys, getPakList, downloadProject, getZeroUrl } from '@/api/operator/plateform/operationAnalysis';
import DownLoadPaksList from './DownLoadPaksList.vue';
import { useCookies } from 'vue3-cookies';
const { cookies } = useCookies();

const permissionStore = usePermissionStore();

const userStore = useUserStore();
// 获取项目类型下拉列表
const projectType = ref([]);
const getTypeDropList = async () => {
  try {
    const res = await getDropdown({ code: 'PROJECT_TYPE' });
    if (res.code === 200) {
      projectType.value = res.data;
    }
  } catch (error) {
    console.error(error);
  }
};
getTypeDropList();
const formatProjectType = (val: any) => {
  const project = projectType.value.find((item) => item.code === val);
  return project ? project.value : 'XXV项目';
};
// 是否是开发环境
const dev = import.meta.env.DEV;
// 进入项目
const goPedestal = async (record: any) => {
  const projectType = record.exampleType;
  let url = '';
  // 低代码
  if (projectType === '1') {
    let token = '';
    const enPwd = cookies.get('ACCESS_P');
    const dePwd = enPwd ? Decrypt(enPwd.substring(32), '', enPwd.substring(0, 32)) : '';
    if (dePwd) {
      try {
        const resT = await intoProjectSingle({
          tenant: record.code,
          pwd: dePwd.replaceAll('"', ''),
        });
        if (resT.code === 200) {
          token = resT.data;
        }
      } catch (error) {
        console.error(error);
      }
    }
    if (dev) {
      url = `http://localhost:8083/base/login?tenant=${record.code}&token=${token}&name=${record.name}`;
    } else {
      url = `/base/login?tenant=${record.code}&token=${token}&name=${record.name}`;
    }
    console.log('url', url);
  } else if (projectType === '2') {
    // 零代码
    const res = await getZeroUrl({ projectId: record.id });
    if (res.code === 200) {
      url = res.data;
    }
  }
  window.open(url, '_blank');
};
const editProjectRef = ref();
const addProjectRef = ref();
// 发布案例
const publishPro = (item: any) => {
  addProjectRef.value.init(item);
};
const editProject = (item: any) => {
  editProjectRef.value.init(item);
};
const backOk = () => {
  getData();
};
// 点击删除
const clickDelete = (record: any) => {
  deleteProject({ id: record.id }).then((res) => {
    if (res.code === 200) {
      useGlobalMessage('success', '项目删除成功');
      getData();
    } else {
      useGlobalMessage('error', res.message);
    }
  });
};
// 编辑团队成员
const editProjectDeveloperRef = ref();
const editDeveloper = (record: any) => {
  editProjectDeveloperRef.value.init(record);
};
// 拼接团队成员展示数据
const getDeveloperText = (record: any) => {
  if (record.userList && record.userList.length) {
    return record.userList.map((user: any) => user.name).join(',');
  }
  return '';
};

// ------------------ 下载项目 -------------
const selectItem = ref('');
const showPanedId = ref('');

const setClicked = (id: string) => {
  showPanedId.value = id;
};
const hide = () => {
  showPanedId.value = '';
};
const downloadWithPak = (record: any) => {
  downLoadPaksListRef.value.init(record.id);
  hide();
};
const downloadData = ref<any>({});
const downLoadPaksListRef = ref();
const canDown = ref(true);
// TODO:仅下载数据
const downloadItemData = (record: any) => {
  downloadItem(record);
  hide();
};
// 下载项目
const downloadItem = (record: any) => {
  downloadProject({
    id: record.id, // 项目ID
    installManageId: downloadData.value?.id, // 所选系统ID
    downloadOption: 1,
  }).then((res) => {
    if (res.code === 200) {
      useGlobalMessage('success', res.message);
    }
  });
};
// 下载文件流
const downloadFile = async (record: any) => {
  try {
    const url = `${window.config.previewUrl}${record.data}`;
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${record.name}.zip`);

    const onLoad = () => {
      useGlobalMessage('success', '项目下载完成！');

      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    };

    link.addEventListener('load', onLoad);

    document.body.appendChild(link);

    // 异步等待，确保模拟点击在添加到 DOM 后执行
    // eslint-disable-next-line no-promise-executor-return
    await new Promise((resolve) => setTimeout(resolve, 0));

    link.click();
  } catch (error) {
    console.error('下载文件时出现错误：', error);
  }
};
// ------------------------------ 标签组----------------------------------
const tagsChecked = ref<any>({});
const tagsList = ref([]);
const tagLoading = ref(true);
// 获取类型分组
const getProjectGroup = () => {
  tagLoading.value = true;
  tagsList.value = [];
  projectGroup()
    .then((res) => {
      tagLoading.value = false;
      if (res.code !== 200) {
        tagsList.value = [];
        return;
      }
      if (res.data.length) {
        // 储存分组结构
        res.data.forEach((group: any) => {
          tagsChecked.value[group.id] = [];
        });
      }
      tagsList.value = res.data || [];
    })
    .catch(() => {
      tagLoading.value = false;
    });
};
getProjectGroup();
// 全选-反选当前分组
const checkGroupAll = (record: any) => {
  const currentTagIds = record.tags?.map((tag: any) => tag.id);
  if (JSON.stringify(tagsChecked.value[record.id]) === JSON.stringify(currentTagIds)) {
    tagsChecked.value[record.id] = [];
  } else {
    tagsChecked.value[record.id] = record.tags.map((tag: any) => tag.id);
  }
};
watch(
  () => tagsChecked.value,
  () => {
    clickSearch();
  },
  {
    deep: true,
  }
);
// 获取图片地址
const getImgUrl = (res: any) => {
  if (!res.previewUrl) return '';
  return `${window.config.previewUrl}${res.previewUrl}`;
};
// ---------------------------------------搜索框----------------------------------------------------
const searchParam = ref<{
  name: string | null;
  enterpriseId: string | null;
}>({
  name: null,
  enterpriseId: null,
});

// 点击搜索
const clickSearch = () => {
  if (permissionStore.checkedEnterprise?.status === 1) {
    // 禁用状态
    // Modal.error({
    //   title: '当前团队已禁用，请在我的团队切换团队！',
    //   keyboard: false,
    //   content: ' ',
    //   class: 'no-enterprise-modal',
    //   centered: true,
    //   closable: false,
    //   okText: ' ',
    // });
    return;
  }
  paginationConfig.value.current = 1;
  paginationConfig.value.pageSize = 10;
  getData();
};
// 重置
const reset = () => {
  searchParam.value.name = null;
  searchParam.value.enterpriseId = null;
  clickSearch();
};
// ---------------------------------------搜索框 end----------------------------------------------------

// ---------------------------------------表格 + 分页----------------------------------------------------
// table列
const columns = [
  {
    title: '项目名称',
    dataIndex: 'name',
    ellipsis: true,
    width: 300,
  },
  // {
  //   title: '项目类型',
  //   dataIndex: 'exampleType',
  //   ellipsis: true,
  //   width: 100,
  // },
  {
    title: '标签',
    dataIndex: 'tags',
    width: 180,
    minWidth: 100,
  },
  {
    title: '参与团队成员',
    dataIndex: 'developer',
    width: 180,
    minWidth: 120,
  },
  // {
  //     title: '创建者',
  //     dataIndex: 'createName',
  //     minWidth: 100,
  // },
  {
    title: '场景数',
    dataIndex: 'sceneCount',
    width: 140,
    minWidth: 80,
  },
  {
    title: '用户数',
    dataIndex: 'userCount',
    width: 140,
    minWidth: 80,
  },
  // {
  //     title: '项目所属地',
  //     dataIndex: 'formattedAddress',
  //     ellipsis: true,
  // },
  {
    title: '创建日期',
    dataIndex: 'createTime',
    width: 120,
    ellipsis: true,
  },
  {
    title: '是否涉密',
    dataIndex: 'classified',
    width: 80,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'actions',
    width: 300,
  },
];

const table = ref();
const { scrollY } = useTableScrollY(table);
const confirmLoading = ref(false);
const tabData: any = ref([]);

// 分页配置
const paginationConfig = ref<{
  total: number; // 总页数
  current: number; // 当前页
  pageSize: number; // 一页多少条
  showTotal: (total: number) => string; // 用于显示数据总量和当前数据顺序
  showSizeChanger: boolean; // 是否展示 pageSize 切换器
  showQuickJumper: boolean; // 是否可以快速跳转至某页
  size: any; // 分页尺寸
}>({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: (total: number) => `共有${total}条`,
  showSizeChanger: true,
  showQuickJumper: true,
  size: 'small',
});
// 分页变化
const paginationChange: any = (current: number, pageSize: number) => {
  paginationConfig.value = Object.assign(paginationConfig.value, {
    current,
    pageSize,
  });
  getData();
};
// 监听企业改变重新获取数据
watch(
  () => permissionStore.checkedEnterprise,
  () => {
    clickSearch();
  }
);
// ---------------------------------------表格 + 分页 end----------------------------------------------------

// ---------------------------------------获取数据----------------------------------------------------
const getData = () => {
  if (confirmLoading.value) return;
  tabData.value = [];
  confirmLoading.value = true;
  const tagIds = Object.values(tagsChecked.value)?.flat(Infinity) || [];

  if (!permissionStore.checkedEnterprise?.id) {
    tabData.value = [];
    confirmLoading.value = false;
    return;
  }
  queryProjectList({
    ...searchParam.value,
    userId: userStore.userInfo.id,
    pageNo: paginationConfig.value.current,
    pageSize: paginationConfig.value.pageSize,
    enterpriseId: permissionStore.checkedEnterprise.id,
    tagIds,
  })
    .then((res: any) => {
      confirmLoading.value = false;
      if (res.code === 200) {
        if (JSON.stringify(res.data) === '[]') {
          tabData.value = [];
        } else {
          const { rows, pageNo, pageSize, totalRows } = res.data;
          tabData.value = rows;
          paginationConfig.value.current = pageNo;
          paginationConfig.value.pageSize = pageSize;
          paginationConfig.value.total = totalRows;
        }
      }
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};
// ---------------------------------------获取数据 end----------------------------------------------------

// ---------------------------------------删除 end----------------------------------------------------

const addEditFormRef = ref();
// 点击新增或编辑
const handel = (type: string, record: any) => {
  addEditFormRef.value.init(type, record);
};
// ------------ 复制编码 ---------------------
const copyCode = async (code: any) => {
  console.log(code);
  try {
    await clipboard.copy(code);
    useGlobalMessage('success', '项目编码已复制到剪贴板！');
  } catch (err) {
    console.error('复制项目编码失败：', err);
  }
};
</script>
<style scoped>
.text-truncate {
  /* stylelint-disable-next-line value-no-vendor-prefix */
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
<style scoped lang="scss">
::v-deep .ant-progress-line {
  margin-bottom: 0;
}

.all-content {
  max-width: 300px;
}

.project-manage {
  box-sizing: border-box;
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: var(--primary-bg-color);
  border-left: 1px solid var(--header-border-color);
  border-radius: 4px;

  .no-use {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    font-size: 16px;
  }

  .campus-name-wrap.keep-px {
    display: flex;
    align-items: center;

    img {
      width: 60px;
      height: 40px;
      margin-right: 5px;
      object-fit: contain;
    }

    div {
      width: 100px;
      text-wrap: wrap;
    }
  }

  .edit-developer {
    cursor: pointer;
  }

  .search-wrap {
    align-items: inherit;

    .table-handle {
      padding: 0;
      border: none;
    }
  }

  .search-content {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
  }

  .tag-wrapper {
    position: relative;

    .tag-list {
      display: flex;
      flex-wrap: wrap;

      .tag-item {
        flex-shrink: 0;
        max-width: 100%;
        padding: 0 6px;
        margin-top: 2px;
        margin-right: 6px;
        font-family: Helvetica;
        font-size: 14px;
        line-height: 1.5;
        color: #fff;
        border-radius: 4px;
      }
    }

    .tag-opera {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      cursor: pointer;
      background: var(--second-bg-color);
      border-radius: 4px;
    }

    .type-list {
      position: absolute;
      right: 0;
      bottom: 30px;
      display: flex;
      flex-wrap: wrap;
      width: 200px;
      max-height: 150px;
      padding: 8px;
      overflow-y: scroll;
      background: #fff;
      border-radius: 6px;

      .type-item {
        height: 22px;
        padding: 0 6px;
        margin-right: 6px;
        margin-bottom: 6px;
        font-family: Helvetica;
        font-size: 14px;
        line-height: 22px;
        color: #fff;
        border-radius: 4px;
      }
    }
  }

  .tag-search {
    box-sizing: border-box;
    width: 187px;
    min-width: 100px;
    max-width: 187px;
    height: 100%;
    padding: 10px 0;
    border-right: 1px solid var(--header-border-color);

    .tag-content {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 10px;
      overflow-y: auto;
    }

    .no-tag-content {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      padding: 0 10px;
    }

    .tag-group {
      width: 100%;
      margin-bottom: 8px;

      .tag-group-name {
        height: 24px;
        min-height: 20px;
        margin-bottom: 4px;
        overflow: hidden;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: var(--header-text-active-color);
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }

      .tag-item-name {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 2px 2px 2px 9px;
        color: var(--header-text-color);
        border-radius: 4px;

        :deep(.ant-checkbox) {
          margin-right: 4px;
        }

        &.checked {
          background-color: var(--modal-header-bg-color);
        }

        &:hover {
          background-color: var(--modal-header-bg-color);
        }
      }

      .tag-item {
        margin-bottom: 4px;
      }
    }
  }

  .content-wrap {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 0 10px;
    overflow: hidden;
  }

  .table-wrap {
    display: flex;
    flex: 1;
    flex-direction: column;

    .table-handle {
      padding: 16px 0;

      .handle-btn {
        margin-right: 10px;
        border-radius: 4px;
      }
    }

    .table-content {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: hidden;

      .name {
        display: flex;
        align-items: center;
        width: 100%;

        .name-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .copy-btn {
        display: inline-block;
        margin-left: 5px;
        vertical-align: middle;
        cursor: pointer;
      }

      .table {
        flex: 1;
        overflow-y: auto;
      }

      .pagination {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 40px;
        min-height: 40px;
        max-height: 50px;
        text-align: right;
      }
    }
  }
}
</style>
